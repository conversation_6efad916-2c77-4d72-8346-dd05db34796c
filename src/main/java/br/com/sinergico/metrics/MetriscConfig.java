package br.com.sinergico.metrics;

import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetriscConfig {

  @Value("${spring.application.name}")
  private String applicationName;

  @Autowired(required = false)
  BuildProperties buildProperties;

  @Bean
  public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
    if (applicationName == null || applicationName.isEmpty()) {
      return registry -> registry.config().commonTags("application", buildProperties.getName());
    }
    return registry -> registry.config().commonTags("application", applicationName);
  }

  @Bean
  public InitializingBean forceMeterPostProcessor(
      BeanPostProcessor meterRegistryPostProcessor, MeterRegistry registry) {

    return () -> meterRegistryPostProcessor.postProcessAfterInitialization(registry, "");
  }
}
