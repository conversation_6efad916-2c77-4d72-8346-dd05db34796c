package br.com.json.bean.cadastral;

import br.com.entity.cadastral.CargaAutoProdutoParametro;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class FaturaB2BMin implements Serializable {

  private static final long serialVersionUID = 1706977935594369854L;

  private Long idFatura;

  private Integer idProcessadora;
  private Integer idInstituicao;
  private Integer idRegional;
  private Integer idFilial;
  private Integer idPontoDeRelacionamento;
  private Integer idProdInstituicao;
  private Integer idPedido;
  private Boolean enviadoTotvs;
  private List<CargaAutoProdutoParametro> parametrosCarga;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private LocalDateTime dataHoraInclusao;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private LocalDateTime dataVencimento;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private LocalDateTime dataPagamento;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private LocalDateTime dataCancelamento;

  private BigDecimal valorFatura;
  private Integer idLotePedido;
  private Long idCobrancaBancaria;
  private LocalDateTime dataHoraPgtoManual;
  private Integer idUsuarioManutencao;
  private String numeroNfse;
  private Integer tipoPagamento;
  private LocalDateTime dataVencimentoNova;
  private BigDecimal valorFaturaNova;
  private BigDecimal valorJuros;
  private Integer faturaAtualizada;
  private BigDecimal valorLiquido;
  private BigDecimal valorRetencoes;
  private LocalDateTime dataHoraProcessarAntesPgto;
  private Integer tipoPedido;

  private Boolean isAptoNovoVencimento;
  private BigDecimal valorMulta;

  private BigDecimal valorFaturaCorrigido;
  private BigDecimal valorCorrecao;
  private Integer diasEmAtraso;
  private BigDecimal txMultaAcordo;
  private BigDecimal txJuroAcordo;
  private BigDecimal valorJuroAcordo;
  private BigDecimal valorMultaAcordo;
  private BigDecimal txIofDiario;
  private BigDecimal valorIofDiario;
  private BigDecimal txIofAdicional;
  private BigDecimal valorIofAdicional;
  private Integer idUsuarioAcordo;
  private LocalDateTime dataHrAcordo;
  private Integer idFaturaOrigem;
  private Boolean faturaRenegociada;
  private String observacao;
  private Integer idProdInstituicaoMotivador;

  public Integer getIdProdInstituicao() {
    return idProdInstituicao;
  }

  public void setIdProdInstituicao(Integer idProdInstituicao) {
    this.idProdInstituicao = idProdInstituicao;
  }

  public Integer getIdPedido() {
    return idPedido;
  }

  public void setIdPedido(Integer idPedido) {
    this.idPedido = idPedido;
  }

  public Boolean isFaturaRenegociada() {
    return faturaRenegociada;
  }

  public void setFaturaRenegociada(Boolean faturaRenegociada) {
    this.faturaRenegociada = faturaRenegociada;
  }

  public String getObservacao() {
    return observacao;
  }

  public void setObservacao(String observacao) {
    this.observacao = observacao;
  }

  public Integer getIdUsuarioAcordo() {
    return idUsuarioAcordo;
  }

  public void setIdUsuarioAcordo(Integer idUsuarioAcordo) {
    this.idUsuarioAcordo = idUsuarioAcordo;
  }

  public LocalDateTime getDataHrAcordo() {
    return dataHrAcordo;
  }

  public void setDataHrAcordo(LocalDateTime dataHrAcordo) {
    this.dataHrAcordo = dataHrAcordo;
  }

  public Integer getIdFaturaOrigem() {
    return idFaturaOrigem;
  }

  public void setIdFaturaOrigem(Integer idFaturaOrigem) {
    this.idFaturaOrigem = idFaturaOrigem;
  }

  public BigDecimal getTxIofDiario() {
    return txIofDiario;
  }

  public void setTxIofDiario(BigDecimal txIofDiario) {
    this.txIofDiario = txIofDiario;
  }

  public BigDecimal getValorIofDiario() {
    return valorIofDiario;
  }

  public void setValorIofDiario(BigDecimal valorIofDiario) {
    this.valorIofDiario = valorIofDiario;
  }

  public BigDecimal getTxIofAdicional() {
    return txIofAdicional;
  }

  public void setTxIofAdicional(BigDecimal txIofAdicional) {
    this.txIofAdicional = txIofAdicional;
  }

  public BigDecimal getValorIofAdicional() {
    return valorIofAdicional;
  }

  public void setValorIofAdicional(BigDecimal valorIofAdicional) {
    this.valorIofAdicional = valorIofAdicional;
  }

  public BigDecimal getTxMultaAcordo() {
    return txMultaAcordo;
  }

  public void setTxMultaAcordo(BigDecimal txMultaAcordo) {
    this.txMultaAcordo = txMultaAcordo;
  }

  public BigDecimal getTxJuroAcordo() {
    return txJuroAcordo;
  }

  public void setTxJuroAcordo(BigDecimal txJuroAcordo) {
    this.txJuroAcordo = txJuroAcordo;
  }

  public BigDecimal getValorJuroAcordo() {
    return valorJuroAcordo;
  }

  public void setValorJuroAcordo(BigDecimal valorJuroAcordo) {
    this.valorJuroAcordo = valorJuroAcordo;
  }

  public BigDecimal getValorMultaAcordo() {
    return valorMultaAcordo;
  }

  public void setValorMultaAcordo(BigDecimal valorMultaAcordo) {
    this.valorMultaAcordo = valorMultaAcordo;
  }

  public Boolean getIsAptoNovoVencimento() {
    return isAptoNovoVencimento;
  }

  public void setIsAptoNovoVencimento(Boolean isAptoNovoVencimento) {
    this.isAptoNovoVencimento = isAptoNovoVencimento;
  }

  public BigDecimal getValorFaturaCorrigido() {
    return valorFaturaCorrigido;
  }

  public void setValorFaturaCorrigido(BigDecimal valorFaturaCorrigido) {
    this.valorFaturaCorrigido = valorFaturaCorrigido;
  }

  public BigDecimal getValorCorrecao() {
    return valorCorrecao;
  }

  public void setValorCorrecao(BigDecimal valorCorrecao) {
    this.valorCorrecao = valorCorrecao;
  }

  public Integer getDiasEmAtraso() {
    return diasEmAtraso;
  }

  public void setDiasEmAtraso(Integer diasEmAtraso) {
    this.diasEmAtraso = diasEmAtraso;
  }

  public Integer getTipoPedido() {
    return tipoPedido;
  }

  public void setTipoPedido(Integer tipoPedido) {
    this.tipoPedido = tipoPedido;
  }

  public LocalDateTime getDataHoraProcessarAntesPgto() {
    return dataHoraProcessarAntesPgto;
  }

  public void setDataHoraProcessarAntesPgto(LocalDateTime dataHoraProcessarAntesPgto) {
    this.dataHoraProcessarAntesPgto = dataHoraProcessarAntesPgto;
  }

  public Boolean getAptoNovoVencimento() {
    return isAptoNovoVencimento;
  }

  public void setAptoNovoVencimento(Boolean aptoNovoVencimento) {
    isAptoNovoVencimento = aptoNovoVencimento;
  }

  public BigDecimal getValorLiquido() {
    return valorLiquido;
  }

  public void setValorLiquido(BigDecimal valorLiquido) {
    this.valorLiquido = valorLiquido;
  }

  public BigDecimal getValorRetencoes() {
    return valorRetencoes;
  }

  public void setValorRetencoes(BigDecimal valorRetencoes) {
    this.valorRetencoes = valorRetencoes;
  }

  public Integer getFaturaAtualizada() {
    return faturaAtualizada;
  }

  public void setFaturaAtualizada(Integer faturaAtualizada) {
    this.faturaAtualizada = faturaAtualizada;
  }

  public LocalDateTime getDataVencimentoNova() {
    return dataVencimentoNova;
  }

  public void setDataVencimentoNova(LocalDateTime dataVencimentoNova) {
    this.dataVencimentoNova = dataVencimentoNova;
  }

  public BigDecimal getValorFaturaNova() {
    return valorFaturaNova;
  }

  public void setValorFaturaNova(BigDecimal valorFaturaNova) {
    this.valorFaturaNova = valorFaturaNova;
  }

  public BigDecimal getValorJuros() {
    return valorJuros;
  }

  public void setValorJuros(BigDecimal valorJuros) {
    this.valorJuros = valorJuros;
  }

  public BigDecimal getValorMulta() {
    return valorMulta;
  }

  public void setValorMulta(BigDecimal valorMulta) {
    this.valorMulta = valorMulta;
  }

  public Long getIdFatura() {
    return idFatura;
  }

  public void setIdFatura(Long idFatura) {
    this.idFatura = idFatura;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public Integer getIdPontoDeRelacionamento() {
    return idPontoDeRelacionamento;
  }

  public void setIdPontoDeRelacionamento(Integer idPontoDeRelacionamento) {
    this.idPontoDeRelacionamento = idPontoDeRelacionamento;
  }

  public LocalDateTime getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(LocalDateTime dataHoraInclusao) {
    this.dataHoraInclusao = dataHoraInclusao;
  }

  public LocalDateTime getDataVencimento() {
    return dataVencimento;
  }

  public void setDataVencimento(LocalDateTime dataVencimento) {
    this.dataVencimento = dataVencimento;
  }

  public LocalDateTime getDataPagamento() {
    return dataPagamento;
  }

  public void setDataPagamento(LocalDateTime dataPagamento) {
    this.dataPagamento = dataPagamento;
  }

  public LocalDateTime getDataCancelamento() {
    return dataCancelamento;
  }

  public void setDataCancelamento(LocalDateTime dataCancelamento) {
    this.dataCancelamento = dataCancelamento;
  }

  public BigDecimal getValorFatura() {
    return valorFatura;
  }

  public void setValorFatura(BigDecimal valorFatura) {
    this.valorFatura = valorFatura;
  }

  public Integer getIdLotePedido() {
    return idLotePedido;
  }

  public void setIdLotePedido(Integer idLotePedido) {
    this.idLotePedido = idLotePedido;
  }

  public Long getIdCobrancaBancaria() {
    return idCobrancaBancaria;
  }

  public void setIdCobrancaBancaria(Long idCobrancaBancaria) {
    this.idCobrancaBancaria = idCobrancaBancaria;
  }

  public Integer getIdProdInstituicaoMotivador() {
    return idProdInstituicaoMotivador;
  }

  public void setIdProdInstituicaoMotivador(Integer idProdInstituicaoMotivador) {
    this.idProdInstituicaoMotivador = idProdInstituicaoMotivador;
  }

  public List<CargaAutoProdutoParametro> getParametrosCarga() {
    return parametrosCarga;
  }

  public void setParametrosCarga(List<CargaAutoProdutoParametro> parametrosCarga) {
    this.parametrosCarga = parametrosCarga;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((idFatura == null) ? 0 : idFatura.hashCode());
    result = prime * result + ((idFilial == null) ? 0 : idFilial.hashCode());
    result = prime * result + ((idInstituicao == null) ? 0 : idInstituicao.hashCode());
    result =
        prime * result
            + ((idPontoDeRelacionamento == null) ? 0 : idPontoDeRelacionamento.hashCode());
    result = prime * result + ((idProcessadora == null) ? 0 : idProcessadora.hashCode());
    result = prime * result + ((idRegional == null) ? 0 : idRegional.hashCode());
    result = prime * result + ((valorFatura == null) ? 0 : valorFatura.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    FaturaB2BMin other = (FaturaB2BMin) obj;
    if (idFatura == null) {
      if (other.idFatura != null) return false;
    } else if (!idFatura.equals(other.idFatura)) return false;
    if (idFilial == null) {
      if (other.idFilial != null) return false;
    } else if (!idFilial.equals(other.idFilial)) return false;
    if (idInstituicao == null) {
      if (other.idInstituicao != null) return false;
    } else if (!idInstituicao.equals(other.idInstituicao)) return false;
    if (idPontoDeRelacionamento == null) {
      if (other.idPontoDeRelacionamento != null) return false;
    } else if (!idPontoDeRelacionamento.equals(other.idPontoDeRelacionamento)) return false;
    if (idProcessadora == null) {
      if (other.idProcessadora != null) return false;
    } else if (!idProcessadora.equals(other.idProcessadora)) return false;
    if (idRegional == null) {
      if (other.idRegional != null) return false;
    } else if (!idRegional.equals(other.idRegional)) return false;
    if (valorFatura == null) {
      if (other.valorFatura != null) return false;
    } else if (!valorFatura.equals(other.valorFatura)) return false;
    return true;
  }

  public LocalDateTime getDataHoraPgtoManual() {
    return dataHoraPgtoManual;
  }

  public void setDataHoraPgtoManual(LocalDateTime dataHoraPgtoManual) {
    this.dataHoraPgtoManual = dataHoraPgtoManual;
  }

  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  public String getNumeroNfse() {
    return numeroNfse;
  }

  public void setNumeroNfse(String numeroNfse) {
    this.numeroNfse = numeroNfse;
  }

  public Integer getTipoPagamento() {
    return tipoPagamento;
  }

  public void setTipoPagamento(Integer tipoPagamento) {
    this.tipoPagamento = tipoPagamento;
  }

  public Boolean getEnviadoTotvs() {
    return enviadoTotvs;
  }

  public void setEnviadoTotvs(Boolean enviadoTotvs) {
    this.enviadoTotvs = enviadoTotvs;
  }
}
