package br.com.sinergico.service.jcard;

import br.com.client.rest.jcard.json.bean.CardProduct;
import br.com.client.rest.jcard.json.bean.CreateCardProduct;
import br.com.client.rest.jcard.json.bean.CreateCardProductResponse;
import br.com.client.rest.jcard.json.bean.GetCardProductResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class CardProductService extends JcardService {

  private static final String URL = "/jcard/api/cardproduct/";

  /**
   * Método responsável por criar um produto no jcard
   *
   * @param createCardProduct
   * @return CreateCardProductResponse
   */
  public CreateCardProductResponse createCardProduct(CreateCardProduct createCardProduct) {
    return doPost(createCardProduct, new CreateCardProductResponse(), getUrlBaseJcard() + URL);
  }

  /**
   * @param id
   * @return GetIssuerResponse
   */
  public GetCardProductResponse getCardProduct(String id) {
    String uri = getUrlBaseJcard() + URL + id;
    return doGet(new GetCardProductResponse(), uri);
  }

  /**
   * @param cardProduct
   * @return JcardResponse
   */
  public JcardResponse updateCardProduct(CardProduct cardProduct) {
    return doPut(cardProduct, new JcardResponse(), getUrlBaseJcard() + URL);
  }

  public static CreateCardProduct prepareProdInstToCreateCardProduct(
      ProdutoInstituicaoConfiguracao prodConfig, String descProdInstituicao) {

    CreateCardProduct response = new CreateCardProduct();

    response.setActive(true);
    response.setAnonymous(true);
    response.setAtm(true);
    response.setBin(prodConfig.getBin().toString());
    response.setBinLength(prodConfig.getBinLength());
    response.setBinExtended(prodConfig.getBinEstendido().toString());

    if (prodConfig.getBinVirtual() != null && prodConfig.getBinVirtual() != 0) {
      response.setBinExtendedVirtual(prodConfig.getBinEstendidoVirtual().toString());
      response.setBinVirtual(prodConfig.getBinVirtual().toString());
    }

    response.setCardNumberLength(prodConfig.getPadrao().getTamanhoPadrao());
    response.setEcommerce(true);
    response.setEndDate("2099-07-01");
    response.setExternalAccount("0000001");
    response.setInstitutionId(
        IssuerService.getValidInstituicionId(
            prodConfig.getIdProcessadora(), prodConfig.getIdInstituicao()));
    response.setMoto(true);
    response.setName(descProdInstituicao);
    response.setPaymentScheme(
        prodConfig.getArranjoPagamento().getArranjoInstituidor().getMarcaArranjo());
    response.setPinLength(prodConfig.getTamanhoPin());
    response.setPos(true);
    response.setRandomCardNumber(true);
    response.setServiceCode(prodConfig.getServiceCode());
    response.setSmart(prodConfig.getChip());

    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

    response.setStartDate(sdf.format(new Date()));
    response.setTips(true);

    response.setAdditionalCardLimited(
        prodConfig.getDiferenciacaoLimiteAdicional() == null
            ? false
            : prodConfig.getDiferenciacaoLimiteAdicional());
    response.setAdditionalCardLimitPercentage(
        prodConfig.getPercentualLimiteAdicional() == null
            ? null
            : prodConfig.getPercentualLimiteAdicional().doubleValue());

    return response;
  }

  public static CardProduct prepareProdInstToCardProduct(
      ProdutoInstituicaoConfiguracao prodConfig,
      String descProdInstituicao,
      BigDecimal percentLimiteSaque) {

    CardProduct response = new CardProduct();

    response.setActive(true);
    ;
    response.setAnonymous(true);
    response.setAtm(true);
    response.setBin(prodConfig.getBin().toString());
    response.setBinLength(prodConfig.getBinLength());
    response.setBinExtended(prodConfig.getBinEstendido().toString());

    if (prodConfig.getBinVirtual() != null && prodConfig.getBinVirtual() != 0) {
      response.setBinExtendedVirtual(prodConfig.getBinEstendidoVirtual().toString());
      response.setBinVirtual(prodConfig.getBinVirtual().toString());
    }

    response.setCardNumberLength(prodConfig.getPadrao().getTamanhoPadrao());
    response.setEcommerce(true);
    response.setEndDate("2099-07-01");
    response.setExternalAccount("0000001");
    response.setId(prodConfig.getIdCardProduct());
    response.setMoto(true);
    response.setName(descProdInstituicao);
    response.setPinLength(prodConfig.getTamanhoPin());
    response.setPaymentScheme(
        prodConfig.getArranjoPagamento().getArranjoInstituidor().getMarcaArranjo());
    response.setPos(false);
    response.setRandomCardNumber(true);
    response.setServiceCode(prodConfig.getServiceCode());
    response.setSmart(prodConfig.getChip());

    response.setPercentualLimiteSaque(new BigDecimal(percentLimiteSaque.floatValue() / 100));

    if (prodConfig.getDtHrInclusao() != null) {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      response.setStartDate(sdf.format(prodConfig.getDtHrInclusao()));
    } else {
      response.setStartDate("2016-07-01");
    }

    response.setTips(true);

    response.setAdditionalCardLimited(
        prodConfig.getDiferenciacaoLimiteAdicional() == null
            ? false
            : prodConfig.getDiferenciacaoLimiteAdicional());
    response.setAdditionalCardLimitPercentage(
        prodConfig.getPercentualLimiteAdicional() == null
            ? null
            : prodConfig.getPercentualLimiteAdicional().doubleValue());

    response.setProductType(prodConfig.getTipoProduto());

    return response;
  }

  public static CardProduct prepareProdInstConfigToCardProduct(
      ProdutoInstituicaoConfiguracao prodConfig) {

    CardProduct response = new CardProduct();

    response.setActive(true);
    ;
    response.setAnonymous(true);
    response.setAtm(true);
    response.setBin(prodConfig.getBin().toString());
    response.setBinLength(prodConfig.getBinLength());
    response.setBinExtended(prodConfig.getBinEstendido().toString());

    if (prodConfig.getBinVirtual() != null && prodConfig.getBinVirtual() != 0) {
      response.setBinExtendedVirtual(prodConfig.getBinEstendidoVirtual().toString());
      response.setBinVirtual(prodConfig.getBinVirtual().toString());
    }

    response.setCardNumberLength(prodConfig.getPadrao().getTamanhoPadrao());
    response.setEcommerce(true);
    response.setEndDate("2099-07-01");
    response.setExternalAccount("0000001");
    response.setId(prodConfig.getIdCardProduct());
    response.setMoto(true);
    response.setPinLength(prodConfig.getTamanhoPin());
    response.setPos(false);
    response.setRandomCardNumber(true);
    response.setServiceCode(prodConfig.getServiceCode().toString());
    response.setSmart(prodConfig.getChip());

    response.setPercentualLimiteSaque(new BigDecimal("0.0"));

    if (prodConfig.getDtHrInclusao() != null) {
      SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
      response.setStartDate(sdf.format(prodConfig.getDtHrInclusao()));
    } else {
      response.setStartDate("2016-07-01");
    }

    response.setTips(true);

    response.setAdditionalCardLimited(
        prodConfig.getDiferenciacaoLimiteAdicional() == null
            ? false
            : prodConfig.getDiferenciacaoLimiteAdicional());
    response.setAdditionalCardLimitPercentage(
        prodConfig.getPercentualLimiteAdicional() == null
            ? null
            : prodConfig.getPercentualLimiteAdicional().doubleValue());

    response.setProductType(prodConfig.getTipoProduto());

    return response;
  }
}
