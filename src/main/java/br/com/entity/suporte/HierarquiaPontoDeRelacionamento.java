package br.com.entity.suporte;

// Generated 17/11/2015 23:54:02 by Hibernate Tools 4.3.1.Final

import br.com.entity.cadastral.ProdutoContratado;
import br.com.json.bean.suporte.CadastrarPontoRelacionamento;
import br.com.json.bean.suporte.StatusB2B;
import br.com.sinergico.enums.GrauDeRiscoEnum;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.hibernate.validator.constraints.NotEmpty;

@Entity
@DynamicUpdate(true)
@Table(name = "hierarquia_ponto_de_relacionamento", schema = "suporte")
public class HierarquiaPontoDeRelacionamento
    implements java.io.Serializable, Comparable<HierarquiaPontoDeRelacionamento> {

  private static final long serialVersionUID = 1L;

  @JsonIgnore private HierarquiaPontoDeRelacionamentoId id;
  private HierarquiaFilial hierarquiaFilial;
  private HierarquiaRegional hierarquiaRegional;
  private HierarquiaInstituicao hierarquiaInstituicao;
  private HierarquiaProcessadora hierarquiaProcessadora;
  private Integer idProcessadora;
  private Integer idInstituicao;
  private Integer idRegional;
  private Integer idPontoDeRelacionamento;
  private Integer idFilial;

  private Boolean habilitaFaturar;

  @Column(name = "identificador_externo", nullable = true, length = 10)
  private String identificadorExterno;

  @Column(name = "id_company")
  private Long idCompany;

  private String descricao;

  // novos campos para cadastrar empresa cliente
  private String documento;

  private LocalDateTime dataHoraInclusao;
  private LocalDateTime dataHoraUltimaAtualizacao;
  private Integer dddTelefoneComercial;
  private Integer telefoneComercial;
  private Integer dddTelefoneComercial2;
  private Integer telefoneComercial2;

  private String inscricaoEstadual;
  private String inscricaoMunicipal;
  private LocalDateTime dataFundacao;
  private String atividadePrincipal;
  private String formaDeConstituicao;
  private Integer idUsuarioInclusao;
  private Integer idUsuarioManutencao;

  // campo  que determina se o pr eh uma empresa cliente
  private Boolean b2b;
  // campo para PF. Ex: trabalhador rural que emite vale refeicao/alimentacao
  private String cei;

  /*endereco sede*/
  private String cepSede;
  private String logradouroSede;
  private String numeroSede;
  private String complementoSede;
  private String bairroSede;
  private String cidadeSede;
  private String ufSede;

  /*endereco faturamento*/
  private String cepFaturamento;
  private String logradouroFaturamento;
  private String numeroFaturamento;
  private String complementoFaturamento;
  private String bairroFaturamento;
  private String cidadeFaturamento;
  private String ufFaturamento;

  private Integer tipoDocumento;

  // nome impresso no cartao
  private String nomeImpresso;

  private String nomeFantasia;

  // status (1-ativo,5-inadimplente,9-canc)
  private Integer status;
  private LocalDateTime dataStatus;

  // (1-correios-carta registrada,2-correios-sedex,3-courrier)
  private Integer tipoPostagem;

  private List<Contato> contatos;

  private List<ProdutoContratado> produtosContratados;

  private BigDecimal retencaoIss;

  private Integer codigoMunicipio;

  private String email;

  private Long idLayout;

  private BigDecimal limiteMaxCredito;

  private GrauDeRiscoEnum grauDeRisco = GrauDeRiscoEnum.NAO_ATRIBUIDO;

  private Long idGrupoEmpresarial;

  @Transient private BigDecimal limiteConcedidoCredito;

  @Transient private BigDecimal disponivelCredito;

  @Transient private Boolean temConvenio;

  private Integer idProcessadoraCorresp;
  private Integer idInstituicaoCorresp;
  private Integer idRegionalCorresp;
  private Integer idFilialCorresp;
  private Integer idPontoDeRelacionamentoCorresp;

  /*
   * hierarquia repl bank10
   */
  private Integer idProcessadoraCorresp2;
  private Integer idInstituicaoCorresp2;
  private Integer idRegionalCorresp2;
  private Integer idFilialCorresp2;
  private Integer idPontoDeRelacionamentoCorresp2;

  private Date dataHoraReplicacao;
  private Date dataHoraReplicacao2;

  @Transient private String descStatus;

  @Column(name = "id_processadora_corresp")
  public Integer getIdProcessadoraCorresp() {
    return idProcessadoraCorresp;
  }

  public void setIdProcessadoraCorresp(Integer idProcessadoraCorresp) {
    this.idProcessadoraCorresp = idProcessadoraCorresp;
  }

  @Column(name = "id_instituicao_corresp")
  public Integer getIdInstituicaoCorresp() {
    return idInstituicaoCorresp;
  }

  public void setIdInstituicaoCorresp(Integer idInstituicaoCorresp) {
    this.idInstituicaoCorresp = idInstituicaoCorresp;
  }

  @Column(name = "id_regional_corresp")
  public Integer getIdRegionalCorresp() {
    return idRegionalCorresp;
  }

  public void setIdRegionalCorresp(Integer idRegionalCorresp) {
    this.idRegionalCorresp = idRegionalCorresp;
  }

  @Column(name = "id_filial_corresp")
  public Integer getIdFilialCorresp() {
    return idFilialCorresp;
  }

  public void setIdFilialCorresp(Integer idFilialCorresp) {
    this.idFilialCorresp = idFilialCorresp;
  }

  @Column(name = "id_ponto_de_relacionamento_corresp")
  public Integer getIdPontoDeRelacionamentoCorresp() {
    return idPontoDeRelacionamentoCorresp;
  }

  public void setIdPontoDeRelacionamentoCorresp(Integer idPontoDeRelacionamentoCorresp) {
    this.idPontoDeRelacionamentoCorresp = idPontoDeRelacionamentoCorresp;
  }

  @Column(name = "habilita_faturar")
  @Type(type = "numeric_boolean")
  public Boolean getHabilitaFaturar() {
    return habilitaFaturar;
  }

  public void setHabilitaFaturar(Boolean habilitaFaturar) {
    this.habilitaFaturar = habilitaFaturar;
  }

  public HierarquiaPontoDeRelacionamento() {
    super();
  }

  public HierarquiaPontoDeRelacionamento(HierarquiaPontoDeRelacionamentoId id, String descricao) {
    this();
    this.id = id;
    this.descricao = descricao;
  }

  public HierarquiaPontoDeRelacionamento(CadastrarPontoRelacionamento cadPontoRelacionamento) {

    this();
    if (this.getId() == null) {
      this.id = new HierarquiaPontoDeRelacionamentoId();
    }
    this.getId().setIdPontoDeRelacionamento(cadPontoRelacionamento.getCodPontoRelacionamento());
    this.descricao = cadPontoRelacionamento.getDescricao();
    this.getId().setIdFilial(cadPontoRelacionamento.getIdFilial());
    this.getId().setIdInstituicao(cadPontoRelacionamento.getIdInstituicao());
    this.getId().setIdRegional(cadPontoRelacionamento.getIdRegional());
    this.getId().setIdProcessadora(cadPontoRelacionamento.getIdProcessadora());

    setIdProcessadora(cadPontoRelacionamento.getIdProcessadora());
    setIdInstituicao(cadPontoRelacionamento.getIdInstituicao());
    setIdProcessadora(cadPontoRelacionamento.getIdProcessadora());
    setIdRegional(cadPontoRelacionamento.getIdProcessadora());
    setIdFilial(cadPontoRelacionamento.getIdFilial());
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    HierarquiaPontoDeRelacionamento that = (HierarquiaPontoDeRelacionamento) o;
    return Objects.equals(id, that.id);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(id);
  }

  @EmbeddedId
  @AttributeOverrides({
    @AttributeOverride(
        name = "idProcessadora",
        column = @Column(name = "id_processadora", nullable = false, precision = 2, scale = 0)),
    @AttributeOverride(
        name = "idInstituicao",
        column = @Column(name = "id_instituicao", nullable = false, precision = 4, scale = 0)),
    @AttributeOverride(
        name = "idRegional",
        column = @Column(name = "id_regional", nullable = false, precision = 4, scale = 0)),
    @AttributeOverride(
        name = "idFilial",
        column = @Column(name = "id_filial", nullable = false, precision = 4, scale = 0)),
    @AttributeOverride(
        name = "idPontoDeRelacionamento",
        column =
            @Column(
                name = "id_ponto_de_relacionamento",
                nullable = false,
                precision = 6,
                scale = 0))
  })
  public HierarquiaPontoDeRelacionamentoId getId() {
    return this.id;
  }

  public void setId(HierarquiaPontoDeRelacionamentoId id) {
    this.id = id;
  }

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        name = "id_processadora",
        referencedColumnName = "id_processadora",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_instituicao",
        referencedColumnName = "id_instituicao",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_regional",
        referencedColumnName = "id_regional",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_filial",
        referencedColumnName = "id_filial",
        nullable = false,
        insertable = false,
        updatable = false)
  })
  @JsonIgnore
  public HierarquiaFilial getHierarquiaFilial() {
    return this.hierarquiaFilial;
  }

  public void setHierarquiaFilial(HierarquiaFilial hierarquiaFilial) {
    this.hierarquiaFilial = hierarquiaFilial;
  }

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        name = "id_processadora",
        referencedColumnName = "id_processadora",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_instituicao",
        referencedColumnName = "id_instituicao",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_regional",
        referencedColumnName = "id_regional",
        nullable = false,
        insertable = false,
        updatable = false)
  })
  @JsonIgnore
  public HierarquiaRegional getHierarquiaRegional() {
    return hierarquiaRegional;
  }

  public void setHierarquiaRegional(HierarquiaRegional hierarquiaRegional) {
    this.hierarquiaRegional = hierarquiaRegional;
  }

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        name = "id_processadora",
        referencedColumnName = "id_processadora",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_instituicao",
        referencedColumnName = "id_instituicao",
        nullable = false,
        insertable = false,
        updatable = false)
  })
  @JsonIgnore
  public HierarquiaInstituicao getHierarquiaInstituicao() {
    return hierarquiaInstituicao;
  }

  public void setHierarquiaInstituicao(HierarquiaInstituicao hierarquiaInstituicao) {
    this.hierarquiaInstituicao = hierarquiaInstituicao;
  }

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumns({
    @JoinColumn(
        name = "id_processadora",
        referencedColumnName = "id_processadora",
        nullable = false,
        insertable = false,
        updatable = false)
  })
  @JsonIgnore
  public HierarquiaProcessadora getHierarquiaProcessadora() {
    return hierarquiaProcessadora;
  }

  public void setHierarquiaProcessadora(HierarquiaProcessadora hierarquiaProcessadora) {
    this.hierarquiaProcessadora = hierarquiaProcessadora;
  }

  public String getNomeFantasia() {
    return nomeFantasia;
  }

  public void setNomeFantasia(String nomeFantasia) {
    this.nomeFantasia = nomeFantasia;
  }

  @Column(name = "desc_ponto_de_relacionamento", nullable = false, length = 60)
  @NotNull
  @NotEmpty
  public String getDescricao() {
    return this.descricao;
  }

  public void setDescricao(String descricao) {
    this.descricao = descricao;
  }

  @Column(name = "id_processadora", insertable = false, updatable = false)
  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  @Column(name = "id_instituicao", insertable = false, updatable = false)
  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  @Column(name = "id_regional", insertable = false, updatable = false)
  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  @Column(name = "id_ponto_de_relacionamento", insertable = false, updatable = false)
  public Integer getIdPontoDeRelacionamento() {
    return idPontoDeRelacionamento;
  }

  public void setIdPontoDeRelacionamento(Integer idPontoRelacionamento) {
    this.idPontoDeRelacionamento = idPontoRelacionamento;
  }

  @Column(name = "id_filial", insertable = false, updatable = false)
  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  @Column(name = "dt_hr_inclusao")
  public LocalDateTime getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(LocalDateTime dataHoraInclusao) {
    this.dataHoraInclusao = dataHoraInclusao;
  }

  @Column(name = "dt_hr_ultima_atualizacao")
  public LocalDateTime getDataHoraUltimaAtualizacao() {
    return dataHoraUltimaAtualizacao;
  }

  public void setDataHoraUltimaAtualizacao(LocalDateTime dataHoraUltimaAtualizacao) {
    this.dataHoraUltimaAtualizacao = dataHoraUltimaAtualizacao;
  }

  @Column(name = "ddd_tel_comercial")
  public Integer getDddTelefoneComercial() {
    return dddTelefoneComercial;
  }

  public void setDddTelefoneComercial(Integer dddTelefoneComercial) {
    this.dddTelefoneComercial = dddTelefoneComercial;
  }

  @Column(name = "tel_comercial")
  public Integer getTelefoneComercial() {
    return telefoneComercial;
  }

  public void setTelefoneComercial(Integer telefoneComercial) {
    this.telefoneComercial = telefoneComercial;
  }

  @Column(name = "inscricao_estadual")
  public String getInscricaoEstadual() {
    return inscricaoEstadual;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    this.inscricaoEstadual = inscricaoEstadual;
  }

  @Column(name = "inscricao_municipal")
  public String getInscricaoMunicipal() {
    return inscricaoMunicipal;
  }

  public void setInscricaoMunicipal(String inscricaoMunicipal) {
    this.inscricaoMunicipal = inscricaoMunicipal;
  }

  @Column(name = "data_fundacao")
  public LocalDateTime getDataFundacao() {
    return dataFundacao;
  }

  public void setDataFundacao(LocalDateTime dataFundacao) {
    this.dataFundacao = dataFundacao;
  }

  @Column(name = "atividade_principal")
  public String getAtividadePrincipal() {
    return atividadePrincipal;
  }

  public void setAtividadePrincipal(String atividadePrincipal) {
    this.atividadePrincipal = atividadePrincipal;
  }

  @Column(name = "forma_de_constituicao")
  public String getFormaDeConstituicao() {
    return formaDeConstituicao;
  }

  public void setFormaDeConstituicao(String formaDeConstituicao) {
    this.formaDeConstituicao = formaDeConstituicao;
  }

  @Column(name = "id_usuario_inclusao")
  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  @Column(name = "id_usuario_manutencao")
  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  @Column(name = "b2b")
  @Type(type = "numeric_boolean")
  public Boolean getB2b() {
    return b2b;
  }

  public void setB2b(Boolean b2b) {
    this.b2b = b2b;
  }

  @Column(name = "cei")
  public String getCei() {
    return cei;
  }

  public void setCei(String cei) {
    this.cei = cei;
  }

  @Column(name = "cep_sede")
  public String getCepSede() {
    return cepSede;
  }

  public void setCepSede(String cepSede) {
    this.cepSede = cepSede;
  }

  @Column(name = "logradouro_sede")
  public String getLogradouroSede() {
    return logradouroSede;
  }

  public void setLogradouroSede(String logradouroSede) {
    this.logradouroSede = logradouroSede;
  }

  @Column(name = "numero_sede")
  public String getNumeroSede() {
    return numeroSede;
  }

  public void setNumeroSede(String numeroSede) {
    this.numeroSede = numeroSede;
  }

  @Column(name = "complemento_sede")
  public String getComplementoSede() {
    return complementoSede;
  }

  public void setComplementoSede(String complementoSede) {
    this.complementoSede = complementoSede;
  }

  @Column(name = "bairro_sede")
  public String getBairroSede() {
    return bairroSede;
  }

  public void setBairroSede(String bairroSede) {
    this.bairroSede = bairroSede;
  }

  @Column(name = "cidade_sede")
  public String getCidadeSede() {
    return cidadeSede;
  }

  public void setCidadeSede(String cidadeSede) {
    this.cidadeSede = cidadeSede;
  }

  @Column(name = "uf_sede")
  public String getUfSede() {
    return ufSede;
  }

  public void setUfSede(String ufSede) {
    this.ufSede = ufSede;
  }

  @Column(name = "cep_faturamento")
  public String getCepFaturamento() {
    return cepFaturamento;
  }

  public void setCepFaturamento(String cepFaturamento) {
    this.cepFaturamento = cepFaturamento;
  }

  @Column(name = "logradouro_faturamento")
  public String getLogradouroFaturamento() {
    return logradouroFaturamento;
  }

  public void setLogradouroFaturamento(String logradouroFaturamento) {
    this.logradouroFaturamento = logradouroFaturamento;
  }

  @Column(name = "numero_faturamento")
  public String getNumeroFaturamento() {
    return numeroFaturamento;
  }

  public void setNumeroFaturamento(String numeroFaturamento) {
    this.numeroFaturamento = numeroFaturamento;
  }

  @Column(name = "complemento_faturamento")
  public String getComplementoFaturamento() {
    return complementoFaturamento;
  }

  public void setComplementoFaturamento(String complementoFaturamento) {
    this.complementoFaturamento = complementoFaturamento;
  }

  @Column(name = "bairro_faturamento")
  public String getBairroFaturamento() {
    return bairroFaturamento;
  }

  public void setBairroFaturamento(String bairroFaturamento) {
    this.bairroFaturamento = bairroFaturamento;
  }

  @Column(name = "cidade_faturamento")
  public String getCidadeFaturamento() {
    return cidadeFaturamento;
  }

  public void setCidadeFaturamento(String cidadeFaturamento) {
    this.cidadeFaturamento = cidadeFaturamento;
  }

  @Column(name = "uf_faturamento")
  public String getUfFaturamento() {
    return ufFaturamento;
  }

  public void setUfFaturamento(String ufFaturamento) {
    this.ufFaturamento = ufFaturamento;
  }

  @OneToMany(mappedBy = "pontoRelacionamento")
  public List<ProdutoContratado> getProdutosContratados() {
    return produtosContratados;
  }

  public void setProdutosContratados(List<ProdutoContratado> produtosContratados) {
    this.produtosContratados = produtosContratados;
  }

  @Column(name = "tipo_documento")
  public Integer getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(Integer tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  @Column(name = "ddd_telefone_comercial_2")
  public Integer getDddTelefoneComercial2() {
    return dddTelefoneComercial2;
  }

  public void setDddTelefoneComercial2(Integer dddTelefoneComercial2) {
    this.dddTelefoneComercial2 = dddTelefoneComercial2;
  }

  @Column(name = "telefone_comercial_2")
  public Integer getTelefoneComercial2() {
    return telefoneComercial2;
  }

  public void setTelefoneComercial2(Integer telefoneComercial2) {
    this.telefoneComercial2 = telefoneComercial2;
  }

  @Column(name = "nome_impresso")
  public String getNomeImpresso() {
    return nomeImpresso;
  }

  public void setNomeImpresso(String nomeImpresso) {
    this.nomeImpresso = nomeImpresso;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  @Column(name = "dt_status")
  public LocalDateTime getDataStatus() {
    return dataStatus;
  }

  public void setDataStatus(LocalDateTime dataStatus) {
    this.dataStatus = dataStatus;
  }

  @Column(name = "tipo_postagem")
  public Integer getTipoPostagem() {
    return tipoPostagem;
  }

  public void setTipoPostagem(Integer tipoPostagem) {
    this.tipoPostagem = tipoPostagem;
  }

  @Column(name = "limite_max_credito")
  public BigDecimal getLimiteMaxCredito() {
    return limiteMaxCredito;
  }

  public void setLimiteMaxCredito(BigDecimal limiteMaxCredito) {
    this.limiteMaxCredito = limiteMaxCredito;
  }

  @OneToMany(mappedBy = "pontoRelacionamento")
  public List<Contato> getContatos() {
    return contatos;
  }

  public void setContatos(List<Contato> contatos) {
    this.contatos = contatos;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
    this.setDataHoraUltimaAtualizacao(LocalDateTime.now());
  }

  @Override
  public int compareTo(HierarquiaPontoDeRelacionamento o) {
    if (this.idPontoDeRelacionamento < o.idPontoDeRelacionamento) {
      return -1;
    }
    if (this.idPontoDeRelacionamento > o.idPontoDeRelacionamento) {
      return 1;
    }
    return 0;
  }

  @Column(name = "retencao_iss")
  public BigDecimal getRetencaoIss() {
    return retencaoIss;
  }

  public void setRetencaoIss(BigDecimal retencaoIss) {
    this.retencaoIss = retencaoIss;
  }

  public Integer getCodigoMunicipio() {
    return codigoMunicipio;
  }

  public void setCodigoMunicipio(Integer codigoMunicipio) {
    this.codigoMunicipio = codigoMunicipio;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getIdentificadorExterno() {
    return identificadorExterno;
  }

  public void setIdentificadorExterno(String identificadorExterno) {
    this.identificadorExterno = identificadorExterno;
  }

  @Column(name = "id_layout")
  public Long getIdLayout() {
    return idLayout;
  }

  public void setIdLayout(Long idLayout) {
    this.idLayout = idLayout;
  }

  @Transient
  public BigDecimal getLimiteConcedidoCredito() {
    return limiteConcedidoCredito;
  }

  public void setLimiteConcedidoCredito(BigDecimal limiteConcedidoCredito) {
    this.limiteConcedidoCredito = limiteConcedidoCredito;
  }

  @Transient
  public BigDecimal getDisponivelCredito() {
    return disponivelCredito;
  }

  public void setDisponivelCredito(BigDecimal disponivelCredito) {
    this.disponivelCredito = disponivelCredito;
  }

  @Transient
  public Boolean getTemConvenio() {
    return temConvenio;
  }

  public void setTemConvenio(Boolean temConvenio) {
    this.temConvenio = temConvenio;
  }

  @Column(name = "dt_hr_replicacao")
  public Date getDataHoraReplicacao() {
    return dataHoraReplicacao;
  }

  public void setDataHoraReplicacao(Date dataHoraReplicacao) {
    this.dataHoraReplicacao = dataHoraReplicacao;
  }

  @Transient
  public String getDescStatus() {
    if (this.descStatus == null) {
      this.descStatus = new StatusB2B(this.status).getDescricao();
    }
    return descStatus;
  }

  public void setDescStatus(String descStatus) {
    this.descStatus = descStatus;
  }

  @Column(name = "id_processadora_corresp_2")
  public Integer getIdProcessadoraCorresp2() {
    return idProcessadoraCorresp2;
  }

  public void setIdProcessadoraCorresp2(Integer idProcessadoraCorresp) {
    this.idProcessadoraCorresp2 = idProcessadoraCorresp;
  }

  @Column(name = "id_instituicao_corresp_2")
  public Integer getIdInstituicaoCorresp2() {
    return idInstituicaoCorresp2;
  }

  public void setIdInstituicaoCorresp2(Integer idInstituicaoCorresp) {
    this.idInstituicaoCorresp2 = idInstituicaoCorresp;
  }

  @Column(name = "id_regional_corresp_2")
  public Integer getIdRegionalCorresp2() {
    return idRegionalCorresp2;
  }

  public void setIdRegionalCorresp2(Integer idRegionalCorresp) {
    this.idRegionalCorresp2 = idRegionalCorresp;
  }

  @Column(name = "id_filial_corresp_2")
  public Integer getIdFilialCorresp2() {
    return idFilialCorresp2;
  }

  public void setIdFilialCorresp2(Integer idFilialCorresp) {
    this.idFilialCorresp2 = idFilialCorresp;
  }

  @Column(name = "id_ponto_de_relacionamento_corresp_2")
  public Integer getIdPontoDeRelacionamentoCorresp2() {
    return idPontoDeRelacionamentoCorresp2;
  }

  public void setIdPontoDeRelacionamentoCorresp2(Integer idPontoDeRelacionamentoCorresp) {
    this.idPontoDeRelacionamentoCorresp2 = idPontoDeRelacionamentoCorresp;
  }

  @Column(name = "dt_hr_replicacao_2")
  public Date getDataHoraReplicacao2() {
    return dataHoraReplicacao2;
  }

  public void setDataHoraReplicacao2(Date dataHoraReplicacao2) {
    this.dataHoraReplicacao2 = dataHoraReplicacao2;
  }

  public Long getIdCompany() {
    return idCompany;
  }

  public void setIdCompany(Long idCompany) {
    this.idCompany = idCompany;
  }

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "grau_de_risco")
  public GrauDeRiscoEnum getGrauDeRisco() {
    return grauDeRisco;
  }

  public void setGrauDeRisco(GrauDeRiscoEnum grauDeRisco) {
    this.grauDeRisco = grauDeRisco;
  }

  @Column(name = "id_grupo_empresarial")
  public Long getIdGrupoEmpresarial() {
    return idGrupoEmpresarial;
  }

  public void setIdGrupoEmpresarial(Long idGrupoEmpresarial) {
    this.idGrupoEmpresarial = idGrupoEmpresarial;
  }
}
