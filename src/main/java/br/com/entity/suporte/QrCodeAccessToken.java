package br.com.entity.suporte;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(schema = "suporte", name = "qrcode_access_token")
public class QrCodeAccessToken implements Serializable {

  private static final long serialVersionUID = -8036313174253352378L;

  @Id
  @Column(name = "id", nullable = false, updatable = false)
  private Long id;

  @Column(name = "access_token")
  private String accessToken;

  @Column(name = "dt_hr_expiracao")
  private Date dtHrExpiracao;

  @Column(name = "id_processadora", nullable = false, updatable = false)
  private Integer idProcessadora;

  @Column(name = "id_instituicao", nullable = false, updatable = false)
  private Integer idInstituicao;

  @Column(name = "client_id", nullable = false, updatable = false)
  private String clientId;

  @Column(name = "auth_token", nullable = false, updatable = false)
  private String authToken;

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    QrCodeAccessToken that = (QrCodeAccessToken) o;
    return id.equals(that.id)
        && clientId.equals(that.clientId)
        && authToken.equals(that.authToken)
        && idProcessadora.equals(that.idProcessadora)
        && idInstituicao.equals(that.idInstituicao);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, clientId, authToken, idProcessadora, idInstituicao);
  }
}
