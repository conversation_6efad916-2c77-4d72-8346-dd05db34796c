package br.com.sinergico.events.dispositivo;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class TrocaDispositivoDTO {

  private Long idDispositivo;
  private Long idLogin;
  private String username;
  private String deviceId;
  private String architectureInfo;
  private String model;
  private String platformName;
  private String plataformVersion;
  private String versaoAplicativo;
  private LocalDateTime timestamp;
  private String tipo; // PORTADOR - CORPORATIVO
}
