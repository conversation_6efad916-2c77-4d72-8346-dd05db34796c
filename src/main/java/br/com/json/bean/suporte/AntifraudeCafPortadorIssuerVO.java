package br.com.json.bean.suporte;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AntifraudeCafPortadorIssuerVO implements Serializable {

  private static final long serialVersionUID = 1L;

  private Long idAcp;
  private String idTransaction;
  private String templateId;
  private Long idCafInstituicaoConfig;
  private String idRequest;
  private LocalDateTime dataCriacao;
  private LocalDateTime dataUltimaModificacao;
  private String status;
  private String tipoDoc;
  private String confianca;
  private String identico;
  private String nomeCompleto;
  private String nomePai;
  private String nomeMae;
  private String dataNascimento;
  private String fotoFrente;
  private String fotoVerso;
  private String selfie;
  private String cpf;
  private String dataEmissaoCpf;
  private String rg;
  private String orgaoExpeditor;
  private String ufRg;
  private String cnh;
  private String validadeCnh;
  private String categoriaCnh;
  private String documentoIdentificado;
  private String documentoLegivel;
  private String cpfPresente;
  private String selfiePresente;
  private String facematch;
  private String consultaDeCpf;
  private String cpfRegular;
  private String nomesEquivalentes;
  private String obito;
  private String presencaEmSancoes;
  private String numeroCpfValido;
  private String docNaoECarteiraDeTrabalho;
  private String docNaoEPassaporte;
  private String temSancoesEncontradasOFAC;
  private String solicitacaoRevisaoSelfieManual;
  private String statusRevisaoManual;
  private String revisaoManualDescricao;
  private LocalDateTime revisaoManualData;
  private String txtRegrasDeCompliance;
  private Long idConta;
  private Integer tipoPessoa;
  private String dadosTipoConta;
  private String nomeEmpresa;
}
