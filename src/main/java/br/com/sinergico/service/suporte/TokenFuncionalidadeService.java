package br.com.sinergico.service.suporte;

import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioUnico;
import br.com.entity.suporte.GatewaySMS;
import br.com.entity.suporte.LogEventoConta;
import br.com.entity.suporte.LogSMS;
import br.com.entity.suporte.LogWhatsapp;
import br.com.entity.suporte.TokenFuncionalidade;
import br.com.enumVO.TipoTokenFuncionalidadeEnum;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.enums.MecanismosAutenticacao;
import br.com.json.bean.suporte.ComunicadoConta;
import br.com.json.bean.suporte.ComunicadoContaViaSMS;
import br.com.json.bean.suporte.ComunicadoContaViaWhatsapp;
import br.com.json.bean.suporte.GerarTokenFuncionalidade;
import br.com.json.bean.suporte.UtilizarTokenGenericoRequest;
import br.com.sinergico.enums.RegraTipoPortadorEnum;
import br.com.sinergico.repository.cadastral.ProdutoPlataformaRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.repository.suporte.TokenFuncionalidadeRepository;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.cadastral.RepresentanteLegalService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.DocumentoUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.validator.UtilValidator;
import br.com.sinergico.vo.InlineDTO;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TokenFuncionalidadeService extends GenericService<TokenFuncionalidade, Long> {

  private static final int TEMPO_EXPIRACAO_TOKEN_FUNCIONALIDADE = 5;

  private static final Integer TAMANHO_TOKEN_FUNCIONALIDADE = 6;

  private static final Integer TIPO_EVENTO_TOKEN_CADASTRO_LOGIN = 7;
  private static final Integer TIPO_EVENTO_TOKEN_LOGIN_ISSUER = 8;

  private static final Integer TIPO_EVENTO_TOKEN_LOGIN_ISSUER_WHATSAPP = 13;

  private static final int MAX_CEL = 999999999;

  private static final int MIN_CEL = 910000000;
  private static final Integer MIN_DDD = 11;

  private static final Integer MAX_DDD = 99;
  public static final String DETALHE_ERRO_LOGIN_INEXISTENTE =
      "Login não encontrado para as informações enviadas.";
  public static final int STATUS_ATIVO = 1;

  private TokenFuncionalidadeRepository tokenRepository;

  @Autowired private AgenteComunicadorPortadorService agenteComunicadorService;

  @Autowired private LogEventoContaService logEventoContaService;

  @Autowired private GatewaySMSService gatewaySMSService;

  @Autowired private LogSMSService logSMSService;

  @Autowired private LogWhatsappService logWhatsappService;

  @Autowired private HierarquiaInstituicaoRepository instituicaoRepository;

  @Autowired private ProdutoPlataformaRepository prodPlatRepository;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired PessoaService pessoaService;

  @Autowired private EmailService emailService;

  private static Logger log = LoggerFactory.getLogger(TokenAcessoService.class);
  @Autowired private UtilService utilService;

  @Autowired private RepresentanteLegalService representanteLegalService;

  @Autowired
  public TokenFuncionalidadeService(TokenFuncionalidadeRepository repo) {
    super(repo);
    tokenRepository = repo;
  }

  @Transactional
  public String gerarTokenCorporativo(
      GerarTokenFuncionalidade gerarTokenFuncionalidade, SecurityUserCorporativo userCorporativo) {
    permiteUsoCampoChaveSomenteParaPix(gerarTokenFuncionalidade);
    String numeroCelularComDdd =
        userCorporativo
            .getResponsavel()
            .getDdd()
            .toString()
            .concat(userCorporativo.getResponsavel().getCelular().toString());
    String telefoneParaChaveExterna =
        gerarTokenFuncionalidade.getChave() != null
            ? gerarTokenFuncionalidade.getChave()
            : numeroCelularComDdd;
    String chaveExterna = telefoneParaChaveExterna.concat(LocalDate.now().toString());
    String tokenGerado = gerarToken(gerarTokenFuncionalidade, chaveExterna);
    montarEEnviarSMS(
        tokenGerado,
        gerarTokenFuncionalidade,
        numeroCelularComDdd,
        TIPO_EVENTO_TOKEN_CADASTRO_LOGIN);
    return tokenGerado;
  }

  @Transactional
  public String gerarToken(GerarTokenFuncionalidade model, SecurityUserPortador userPortador) {

    permiteUsoCampoChaveSomenteParaPix(model);

    PortadorLogin portadorLogin =
        portadorLoginService.buscarLoginEGarantirExistencia(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getDocumento(),
            model.getDocumentoAcesso(),
            model.getGrupoAcesso(),
            model.getTipoLogin(),
            "Nenhum Login encontrado para os dados enviados.");
    if (!portadorLogin.getIdLogin().equals(userPortador.getIdLogin())) {
      throw new GenericServiceException("Permissão negada.", HttpStatus.FORBIDDEN);
    }

    String numeroCelularComDdd = null;
    if (portadorLogin.getDocumentoAcesso() != null
        && userPortador.getTipoLoginEnum() != null
        && userPortador.getTipoLoginEnum().getRegraTipoPortadorLoginEnum() != null
        && userPortador
            .getTipoLoginEnum()
            .getRegraTipoPortadorLoginEnum()
            .equals(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL)) {
      List<Long> listContasPortador = userPortador.getContasPortador();
      List<RepresentanteLegal> listRepresentante =
          representanteLegalService.findByIdContaInAndStatus(listContasPortador, STATUS_ATIVO);
      Optional<RepresentanteLegal> representanteLegalPortadorLogin =
          listRepresentante.stream()
              .filter(
                  representanteLegal ->
                      representanteLegal.getCpf().equals(portadorLogin.getDocumentoAcesso()))
              .findFirst();
      if (representanteLegalPortadorLogin.isPresent()) {
        numeroCelularComDdd =
            representanteLegalPortadorLogin
                .get()
                .getDddCelular()
                .toString()
                .concat(representanteLegalPortadorLogin.get().getTelefoneCelular().toString());
      }
    } else {
      Pessoa pessoa =
          validaPessoa(portadorLogin, model.getIdProcessadora(), model.getIdInstituicao());
      numeroCelularComDdd =
          pessoa.getDddTelefoneCelular().toString().concat(pessoa.getTelefoneCelular().toString());
    }

    String telefoneParaChaveExterna =
        model.getChave() != null ? model.getChave() : numeroCelularComDdd;
    String chaveExterna = telefoneParaChaveExterna.concat(LocalDate.now().toString());
    String tokenGerado = gerarToken(model, chaveExterna);
    montarEEnviarSMS(tokenGerado, model, numeroCelularComDdd, TIPO_EVENTO_TOKEN_CADASTRO_LOGIN);

    return tokenGerado;
  }

  @Transactional
  public void gerarTokenLoginIssuer(GerarTokenFuncionalidade model, AcessoUsuario acessoUsuario)
      throws GenericServiceException {
    String chaveExterna =
        acessoUsuario.getIdUsuario().toString().concat(LocalDate.now().toString());

    if (MecanismosAutenticacao.SMS.getCodigo().equals(acessoUsuario.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_SMS);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorSms(
          model, acessoUsuario.getDddNroCelular(), acessoUsuario.getNroCelular(), tokenGerado);
    } else if (MecanismosAutenticacao.EMAIL
        .getCodigo()
        .equals(acessoUsuario.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_EMAIL);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorEmail(model, acessoUsuario, tokenGerado);
    } else if (MecanismosAutenticacao.WHATSAPP
        .getCodigo()
        .equals(acessoUsuario.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_WHATSAPP);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorWhatsapp(
          model,
          acessoUsuario.getDddNroCelular(),
          acessoUsuario.getNroCelular(),
          tokenGerado,
          acessoUsuario.getIdInstituicao(),
          acessoUsuario.getIdProcessadora());
    } else if (MecanismosAutenticacao.TODOS
        .getCodigo()
        .equals(acessoUsuario.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_TODOS);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorSms(
          model, acessoUsuario.getDddNroCelular(), acessoUsuario.getNroCelular(), tokenGerado);
      enviarTokenPorEmail(model, acessoUsuario, tokenGerado);
      enviarTokenPorWhatsapp(
          model,
          acessoUsuario.getDddNroCelular(),
          acessoUsuario.getNroCelular(),
          tokenGerado,
          acessoUsuario.getIdInstituicao(),
          acessoUsuario.getIdProcessadora());
    }
  }

  private void enviarTokenPorSms(
      GerarTokenFuncionalidade model,
      Integer dddNroCelular,
      Integer nroCelular,
      String tokenGerado) {
    permiteUsoCampoChaveSomenteParaPix(model);
    if (!isCelularValido(dddNroCelular, nroCelular)) {
      throw new GenericServiceException(
          "Celular Cadastrado Para o Usuário Inválido para o Envio do Token de Autenticação");
    }

    String numeroCelularComDdd = dddNroCelular.toString().concat(nroCelular.toString());

    if (utilService.isAmbienteProducao()) {
      montarEEnviarSMS(tokenGerado, model, numeroCelularComDdd, TIPO_EVENTO_TOKEN_LOGIN_ISSUER);
    }
  }

  private void enviarTokenPorWhatsapp(
      GerarTokenFuncionalidade model,
      Integer dddNroCelular,
      Integer nroCelular,
      String tokenGerado,
      Integer idInstituicao,
      Integer idProcessadora) {
    permiteUsoCampoChaveSomenteParaPix(model);
    if (!isCelularValido(dddNroCelular, nroCelular)) {
      throw new GenericServiceException(
          "Celular Cadastrado Para o Usuário Inválido para o Envio do Token de Autenticação");
    }

    String numeroCelularComDdd = dddNroCelular.toString().concat(nroCelular.toString());

    if (utilService.isAmbienteProducao()) {
      montarEEnviarWhatsapp(
          tokenGerado,
          model,
          numeroCelularComDdd,
          TIPO_EVENTO_TOKEN_LOGIN_ISSUER_WHATSAPP,
          idInstituicao,
          idProcessadora);
    }
  }

  private void enviarTokenPorEmail(
      GerarTokenFuncionalidade model, AcessoUsuario acessoUsuario, String tokenGerado) {
    permiteUsoCampoChaveSomenteParaPix(model);
    if (!UtilValidator.isEmailValido(acessoUsuario.getEmail())) {
      throw new GenericServiceException(
          "Email Cadastrado Para o Usuário Inválido para o Envio do Token de Autenticação");
    }
    if (utilService.isAmbienteProducao()) {
      emailService.enviarEmailTokenAutenticacao(tokenGerado, acessoUsuario);
    }
  }

  private void enviarTokenPorEmail(
      GerarTokenFuncionalidade model, AcessoUsuarioUnico acessoUsuarioUnico, String tokenGerado) {
    permiteUsoCampoChaveSomenteParaPix(model);
    if (!UtilValidator.isEmailValido(acessoUsuarioUnico.getEmail())) {
      throw new GenericServiceException(
          "Email Cadastrado Para o Usuário Inválido para o Envio do Token de Autenticação");
    }
    if (utilService.isAmbienteProducao()) {
      emailService.enviarEmailTokenAutenticacao(tokenGerado, acessoUsuarioUnico);
    }
  }

  @Transactional
  public String gerarTokenOnly(GerarTokenFuncionalidade model, SecurityUserPortador userPortador) {

    permiteUsoCampoChaveSomenteParaPix(model);

    PortadorLogin portadorLogin =
        portadorLoginService.buscarLoginEGarantirExistencia(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getDocumento(),
            model.getDocumentoAcesso(),
            model.getGrupoAcesso(),
            model.getTipoLogin(),
            "Nenhum Login encontrado para os dados enviados.");
    if (!portadorLogin.getIdLogin().equals(userPortador.getIdLogin())) {
      throw new GenericServiceException("Permissão negada.", HttpStatus.FORBIDDEN);
    }

    Pessoa pessoa =
        validaPessoa(portadorLogin, model.getIdProcessadora(), model.getIdInstituicao());

    String numeroCelularComDdd =
        pessoa.getDddTelefoneCelular().toString().concat(pessoa.getTelefoneCelular().toString());
    String telefoneParaChaveExterna =
        model.getChave() != null ? model.getChave() : numeroCelularComDdd;
    String chaveExterna = telefoneParaChaveExterna.concat(LocalDate.now().toString());
    return gerarToken(model, chaveExterna);
  }

  @Transactional
  public void gerarTokenEmail(GerarTokenFuncionalidade model, SecurityUserPortador userPortador) {

    permiteUsoCampoChaveSomenteParaPix(model);

    PortadorLogin portadorLogin =
        portadorLoginService.buscarLoginEGarantirExistencia(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getDocumento(),
            model.getDocumentoAcesso(),
            model.getGrupoAcesso(),
            model.getTipoLogin(),
            "Nenhum Login encontrado para os dados enviados.");
    if (!portadorLogin.getIdLogin().equals(userPortador.getIdLogin())) {
      throw new GenericServiceException("Permissão negada.", HttpStatus.FORBIDDEN);
    }

    Pessoa pessoa =
        validaPessoa(portadorLogin, model.getIdProcessadora(), model.getIdInstituicao());
    String email = (Objects.nonNull(model.getChave()) ? model.getChave() : pessoa.getEmail());
    String chaveExterna = email.concat(LocalDate.now().toString());
    String tokenGerado = gerarToken(model, chaveExterna);
    String emailBody = model.getIdFuncionalidade().getMensagemEmail(tokenGerado);
    String subject = "Envio de Token";
    String emailRemetente =
        portadorLogin.getHierarquiaInstituicao().getEmailReply() != null
                && !portadorLogin.getHierarquiaInstituicao().getEmailReply().isEmpty()
            ? portadorLogin.getHierarquiaInstituicao().getEmailReply()
            : "<EMAIL>";
    if (StringUtils.isBlank(email)) {
      throw new GenericServiceException("Portador não possui e-mail cadastrado.");
    }
    sendEmail(email, emailRemetente, emailBody, null, subject);
  }

  private static void permiteUsoCampoChaveSomenteParaPix(GerarTokenFuncionalidade model) {
    if (!TipoTokenFuncionalidadeEnum.TOKEN_CADASTRO_CHAVE_PIX.equals(model.getIdFuncionalidade())
        && StringUtils.isNotBlank(model.getChave())) {
      throw new GenericServiceException(
          "Campo chave não deve ser enviado",
          "Não é permitido envio de chave para esse tipo de funcionalidade.",
          HttpStatus.BAD_REQUEST);
    }
  }

  public Boolean utilizarToken(UtilizarTokenGenericoRequest model) {
    List<TokenFuncionalidade> tokenAcessoList =
        findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            model.getChaveExterna(), model.getToken());

    // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
    // nao exista mesmo
    if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
      throw new GenericServiceException(
          "Código inválido.",
          "Verifique o código digitado ou solicite um novo",
          HttpStatus.UNAUTHORIZED);
    }

    validarDataValidade(tokenAcessoList.get(0));

    tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());
    tokenAcessoList.get(0).setInTokenExpirado(false);
    tokenAcessoList.get(0).setInTokenValidado(true);

    return save(tokenAcessoList.get(0)) != null;
  }

  private void validarDataValidade(TokenFuncionalidade tokenAcesso) {
    if (LocalDateTime.now().isAfter(tokenAcesso.getDataHoraExpiracaoToken())) {
      throw new GenericServiceException(
          "Código expirado. Solicite um novo", "Código expirado solicite um novo");
    }
  }

  private Pessoa validaPessoa(
      PortadorLogin portadorLogin, Integer idProcessadora, Integer idInsitituicao) {
    Pessoa pessoa =
        pessoaService
            .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataNascimentoNotNullOrderByIdPessoaDesc(
                idProcessadora,
                idInsitituicao,
                portadorLogin.getCpf(),
                DocumentoUtil.getTipoPessoa(
                    portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));

    if (Objects.isNull(pessoa)) {
      pessoa =
          pessoaService
              .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataFundacaoNotNullOrderByIdPessoaDesc(
                  idProcessadora,
                  idInsitituicao,
                  portadorLogin.getCpf(),
                  DocumentoUtil.getTipoPessoa(
                      portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));
    }

    if (Objects.nonNull(pessoa)
        && Objects.nonNull(pessoa.getTipoPessoa())
        && Constantes.PESSOA_FISICA.equals(pessoa.getTipoPessoa().getId())) {
      if (Objects.isNull(pessoa.getDataNascimento())) {
        log.warn("Não foi encontrado Pessoa com data de nascimento não nula.");
        pessoa =
            pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
                idProcessadora,
                idInsitituicao,
                portadorLogin.getCpf(),
                DocumentoUtil.getTipoPessoa(
                    portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));

        if (pessoa == null) {
          throw new GenericServiceException(
              "Login de acesso não identificado. Pessoa não encontrada. ",
              "Pessoa não encontrada. ");
        }
      }
    } else {
      if (Objects.isNull(pessoa.getDataFundacao())) {
        log.warn("Não foi encontrado Pessoa com data de nascimento não nula.");
        pessoa =
            pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
                idProcessadora,
                idInsitituicao,
                portadorLogin.getCpf(),
                DocumentoUtil.getTipoPessoa(
                    portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));

        if (pessoa == null) {
          throw new GenericServiceException(
              "Login de acesso não identificado. Pessoa não encontrada. ",
              "Pessoa não encontrada. ");
        }
      }
    }

    return pessoa;
  }

  private void montarEEnviarSMS(
      String token, GerarTokenFuncionalidade model, String celularComDdd, Integer tipoEventoConta) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    if (model.getIdConta() != null) {
      comunicado.setIdConta(model.getIdConta());
    }

    GatewaySMS gateway = null;
    if (model.getIdInstituicao() != null) {
      gateway = getGatewaySMS(model.getIdProcessadora(), model.getIdInstituicao());
    }

    comunicado.setIdCredencial(model.getIdCredencial());
    comunicado.setIdGatewaySMS(gateway == null ? null : gateway.getIdGatewaySMS());
    String mensagem =
        gateway == null ? getMensagem(model, token) : getMensagem(gateway, model, token);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(tipoEventoConta);

    Long celularNumero = null;
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = new Long(celularComDdd);
      ddd = new Integer(celularComDdd.substring(0, 2));
      restante = new Integer(celularComDdd.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celularComDdd + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celularComDdd + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        gateway == null
            ? getLogEventoConta(comunicado)
            : getLogEventoConta(
                comunicado, gateway.getIdProcessadora(), gateway.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogSMS logSms = new LogSMS();
    BeanUtils.copyProperties(comunicado, logSms);
    logSms.setNumeroCelular(new Long(celularComDdd));

    logSms.setDataHoraRegistro(new Date());
    logSms.setDataHoraAgendamento(new Date());
    logSms.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logSms.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    logSms = logSMSService.save(logSms);

    comunicado.setIdLogEventoConta(logSms.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logSms.getIdLogEventoConta());

    enviarSms(comunicado);
  }

  private void montarEEnviarWhatsapp(
      String token,
      GerarTokenFuncionalidade model,
      String celularComDdd,
      Integer tipoEventoConta,
      Integer idInstituicao,
      Integer idProcessadora) {

    ComunicadoContaViaWhatsapp comunicado = new ComunicadoContaViaWhatsapp();
    if (model.getIdConta() != null) {
      comunicado.setIdConta(model.getIdConta());
    }
    comunicado.setIdCredencial(model.getIdCredencial());
    String mensagem = getMensagem(model, token);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(tipoEventoConta);

    Long celularNumero = null;
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = new Long(celularComDdd);
      ddd = new Integer(celularComDdd.substring(0, 2));
      restante = new Integer(celularComDdd.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celularComDdd + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celularComDdd + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        getLogEventoConta(comunicado, model.getIdProcessadora(), model.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogWhatsapp logWhatsapp = new LogWhatsapp();
    BeanUtils.copyProperties(comunicado, logWhatsapp);
    logWhatsapp.setNumeroCelular(new Long(celularComDdd));

    logWhatsapp.setDataHoraRegistro(new Date());
    logWhatsapp.setDataHoraAgendamento(new Date());
    logWhatsapp.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logWhatsapp.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    if (idInstituicao != null) {
      logWhatsapp.setIdInstituicao(idInstituicao);
    }
    if (idProcessadora != null) {
      logWhatsapp.setIdProcessadora(idProcessadora);
    }
    logWhatsapp = logWhatsappService.save(logWhatsapp);

    comunicado.setIdLogEventoConta(logWhatsapp.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logWhatsapp.getIdLogEventoConta());

    enviarWhatsapp(comunicado);
  }

  private void enviarWhatsapp(ComunicadoContaViaWhatsapp comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  private String getMensagem(GatewaySMS gateway, GerarTokenFuncionalidade model, String token) {
    String descInstituicao =
        instituicaoRepository.getDescInstituicao(
            gateway.getIdProcessadora(), gateway.getIdInstituicao());
    String descProdPlat = null;
    if (model.getIdConta() != null
        && (model.getIdInstituicao() == null
            || !Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model.getIdInstituicao()))) {
      descProdPlat = prodPlatRepository.getDescProdutoPlataformaByIdConta(model.getIdConta());
    }
    return model
        .getIdFuncionalidade()
        .getMensagemSms(gateway, model, token, descInstituicao, descProdPlat);
  }

  private String getMensagem(GerarTokenFuncionalidade model, String token) {
    String descInstituicao = null;
    String descProdPlat = null;

    if (model.getIdInstituicao() != null) {
      descInstituicao =
          instituicaoRepository.getDescInstituicao(
              model.getIdProcessadora(), model.getIdInstituicao());
    }

    if (model.getIdConta() != null
        && (model.getIdInstituicao() == null
            || !Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model.getIdInstituicao()))) {
      descProdPlat = prodPlatRepository.getDescProdutoPlataformaByIdConta(model.getIdConta());
    }

    return model
        .getIdFuncionalidade()
        .getMensagemWhatsapp(model, token, descInstituicao, descProdPlat);
  }

  private boolean isCelularValido(Integer dddTelefoneCelular, Integer telefoneCelular) {
    if (dddTelefoneCelular == null || telefoneCelular == null) {
      return false;
    }
    return (dddTelefoneCelular >= MIN_DDD && dddTelefoneCelular <= MAX_DDD)
        && (telefoneCelular > MIN_CEL && telefoneCelular < MAX_CEL);
  }

  private LogEventoConta getLogEventoConta(
      ComunicadoContaViaSMS comunicado, Integer idProcessadora, Integer idInstituicao) {
    LogEventoConta evento = getLogEventoConta(comunicado);
    evento.setIdProcessadora(idProcessadora);
    evento.setIdInstituicao(idInstituicao);
    return evento;
  }

  private LogEventoConta getLogEventoConta(
      ComunicadoContaViaWhatsapp comunicado, Integer idProcessadora, Integer idInstituicao) {
    LogEventoConta evento = getLogEventoConta(comunicado);
    evento.setIdProcessadora(idProcessadora);
    evento.setIdInstituicao(idInstituicao);
    return evento;
  }

  public GatewaySMS getGatewaySMS(Integer idProcessadora, Integer idInstituicao) {
    if (idInstituicao == null) {
      idInstituicao = Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL;
    }
    if (idProcessadora == null) {
      idProcessadora = Constantes.ID_PROCESSADORA_ITS_PAY;
    }
    GatewaySMS gatewaySMS =
        gatewaySMSService.findFirstByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (gatewaySMS == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar gatewaySMS. IdProcessadora: "
              + idProcessadora
              + " , idInstituicao: "
              + idInstituicao);
    }
    return gatewaySMS;
  }

  private <Req extends ComunicadoConta> LogEventoConta getLogEventoConta(Req comunicado) {
    LogEventoConta logEvento = new LogEventoConta();
    logEvento.setIdConta(comunicado.getIdConta());
    logEvento.setIdCredencial(comunicado.getIdCredencial());
    logEvento.setTipoEvento(comunicado.getTipoEventoConta());
    return logEvento;
  }

  private void enviarSms(ComunicadoContaViaSMS comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  /**
   * Método que gera o Token Genérico Faz uma busca pelos Tokens da mesma funcionalidade que ainda
   * estão ativos e os cancela Gera um Token com o tamanho padrão (6)
   *
   * @param model
   * @param chaveExterna
   * @return
   */
  public synchronized String gerarToken(GerarTokenFuncionalidade model, String chaveExterna) {

    String tokenGerado = null;

    List<TokenFuncionalidade> tokenFuncionalidadeList =
        findByIdFuncionalidadeAndChaveExternaAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
            model.getIdFuncionalidade().getIdFuncionalidade(), chaveExterna);

    if (tokenFuncionalidadeList != null && !tokenFuncionalidadeList.isEmpty()) {
      cancelarTokensAntigos(tokenFuncionalidadeList);
    }

    tokenGerado = getTokenGerado();
    saveTokenGerado(model, tokenGerado, chaveExterna);
    return tokenGerado;
  }

  /**
   * Método utilizado para cancelar os tokens antigos relativos à uma funcionalidade
   *
   * @param tokenFuncionalidadeList
   */
  public void cancelarTokensAntigos(List<TokenFuncionalidade> tokenFuncionalidadeList) {
    for (TokenFuncionalidade tokenFuncionalidade : tokenFuncionalidadeList) {
      tokenFuncionalidade.setDataHoraCancelamentoChave(LocalDateTime.now());
    }
    saveAll(tokenFuncionalidadeList);
  }

  /**
   * Método que cria um TokenFuncionalidade através de um Builder Salva o TokenFuncionalidade no
   * Banco
   *
   * @param model
   * @param tokenGerado
   * @param chaveExterna
   */
  public void saveTokenGerado(
      GerarTokenFuncionalidade model, String tokenGerado, String chaveExterna) {
    LocalDateTime agora = LocalDateTime.now();
    Date agoraDate = DateUtil.localDateTimeToDate(agora);
    Date tempoAcrescentado =
        DateUtil.aumentar(
            agoraDate,
            model.getIdFuncionalidade() == null
                ? TEMPO_EXPIRACAO_TOKEN_FUNCIONALIDADE
                : model.getIdFuncionalidade().getTempoExpiracao(),
            DateUtil.MINUTOS);
    TokenFuncionalidade tokenFuncionalidade =
        new TokenFuncionalidade.Builder()
            .comDataHoraGeracao(agora)
            .comToken(tokenGerado)
            .comInTokenExpirado(false)
            .comInTokenValidado(false)
            .comDocumento(model.getDocumento())
            .comIdProcessadora(model.getIdProcessadora())
            .comIdInstituicao(model.getIdInstituicao())
            .comIdFuncionalidade(model.getIdFuncionalidade().getIdFuncionalidade())
            .comChaveExterna(chaveExterna)
            .comDataHoraExpiracaoToken(DateUtil.dateToLocalDateTime(tempoAcrescentado))
            .comIdUsuarioIssuer(model.getIdUsuarioIssuer())
            .build();

    save(tokenFuncionalidade);
  }

  /**
   * Método que gera a String do Token
   *
   * @return
   */
  private String getTokenGerado() {
    return Util.generateRandomNumeros(TAMANHO_TOKEN_FUNCIONALIDADE);
  }

  private void sendEmail(
      String email,
      String emailOrigem,
      String emailcontent,
      List<InlineDTO> inlines,
      String assunto) {
    // montar email
    emailService.mandarEmailProvedor(
        email, emailOrigem, assunto, emailcontent, inlines, null, null, null, null);
    log.info("SUCESSO! Email enviado com sucesso para o endereco " + email);
  }

  public List<TokenFuncionalidade>
      findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
          String chaveExterna, String token) {
    return tokenRepository
        .findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            chaveExterna, token);
  }

  public List<TokenFuncionalidade>
      findByIdFuncionalidadeAndChaveExternaAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
          Integer idFuncionalidade, String chaveExterna) {
    return tokenRepository
        .findByIdFuncionalidadeAndChaveExternaAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
            idFuncionalidade, chaveExterna);
  }

  public List<TokenFuncionalidade>
      findByIdFuncionalidadeAndChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
          Integer idFuncionalidade, String chaveExterna, String token) {
    return tokenRepository
        .findByIdFuncionalidadeAndChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
            idFuncionalidade, chaveExterna, token);
  }

  private List<TokenFuncionalidade> findAllTokenByFuncionalidade(
      Integer idFuncionalidade, String token) {
    return tokenRepository
        .findByIdFuncionalidadeAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
            idFuncionalidade, token);
  }
  ;

  public TokenFuncionalidade utilizarTokenByFuncionalidade(
      String token, TipoTokenFuncionalidadeEnum tipoTokenFuncionalidadeEnum) {
    List<TokenFuncionalidade> tokenAcessoList = null;
    try {
      tokenAcessoList =
          findAllTokenByFuncionalidade(tipoTokenFuncionalidadeEnum.getIdFuncionalidade(), token);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Erro ao recuperar o token inválido.", HttpStatus.UNAUTHORIZED);
    }
    // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
    // nao exista mesmo
    if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
      throw new GenericServiceException("Token inválido ou expirado.", HttpStatus.UNAUTHORIZED);
    }
    validarDataValidade(tokenAcessoList.get(0));

    tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());
    tokenAcessoList.get(0).setInTokenExpirado(false);
    tokenAcessoList.get(0).setInTokenValidado(true);

    save(tokenAcessoList.get(0));

    return tokenAcessoList.get(0);
  }

  public TokenFuncionalidade utilizarByFuncionalidadeAndChaveExterna(
      String token, String chaveExterna, TipoTokenFuncionalidadeEnum tipoTokenFuncionalidadeEnum) {
    List<TokenFuncionalidade> tokenAcessoList = null;
    try {
      tokenAcessoList =
          findByIdFuncionalidadeAndChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
              tipoTokenFuncionalidadeEnum.getIdFuncionalidade(), chaveExterna, token);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Erro ao recuperar o token inválido.", HttpStatus.UNAUTHORIZED);
    }
    // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
    // nao exista mesmo
    if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
      throw new GenericServiceException("Token inválido ou expirado.", HttpStatus.UNAUTHORIZED);
    }
    validarDataValidade(tokenAcessoList.get(0));

    tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());
    tokenAcessoList.get(0).setInTokenExpirado(false);
    tokenAcessoList.get(0).setInTokenValidado(true);

    save(tokenAcessoList.get(0));

    return tokenAcessoList.get(0);
  }

  public void utilizarTodosTokenUsuarioByFuncionalidade(Integer idUsuarioIssuer) {
    List<TokenFuncionalidade> tokenAcessoList = new ArrayList<>();
    try {
      tokenAcessoList =
          tokenRepository.findTokenGeradosNaoUtilizadosPorUsuarioIssuerFuncionalidadesLogin(
              idUsuarioIssuer);
    } catch (Exception e) {
      e.printStackTrace();
    }

    if (tokenAcessoList != null && !tokenAcessoList.isEmpty()) {
      tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());
      tokenAcessoList.get(0).setInTokenExpirado(false);
      tokenAcessoList.get(0).setInTokenValidado(true);
    }
  }

  public TokenFuncionalidade generateTokenFromInformacoesUsuario(AcessoUsuario usuario) {
    TokenFuncionalidade tokenFuncionalidade = new TokenFuncionalidade();
    tokenFuncionalidade.setToken(usuario.getCpf().substring(0, 6));
    tokenFuncionalidade.setIdUsuarioIssuer(usuario.getIdUsuario());
    return tokenFuncionalidade;
  }

  public TokenFuncionalidade generateTokenFromInformacoesSuperUsuario(
      AcessoUsuarioUnico acessoUsuarioUnico) {
    TokenFuncionalidade tokenFuncionalidade = new TokenFuncionalidade();
    tokenFuncionalidade.setToken(acessoUsuarioUnico.getCpf().substring(0, 6));
    tokenFuncionalidade.setDocumento(acessoUsuarioUnico.getCpf());
    return tokenFuncionalidade;
  }

  public List<TokenFuncionalidade>
      findTokenGeradosPeriodoTempoNaoUtilizadosPorUsuarioIssuerFuncionalidadesLogin(
          Integer idUsuario) {
    return tokenRepository
        .findTokenGeradosPeriodoTempoNaoUtilizadosPorUsuarioIssuerFuncionalidadesLogin(idUsuario);
  }

  public List<TokenFuncionalidade>
      findTokenGeradosPeriodoTempoNaoUtilizadosPorCpfSuperUsuarioFuncionalidadesLogin(String cpf) {
    return tokenRepository
        .findTokenGeradosPeriodoTempoNaoUtilizadosPorCpfSuperUsuarioFuncionalidadesLogin(cpf);
  }

  public String gerarTokenFuncionalidadeLoginUnico(String cpf, String loginAcessoUnico) {
    GerarTokenFuncionalidade gerarTokenFuncionalidade = new GerarTokenFuncionalidade();
    gerarTokenFuncionalidade.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_UNICO);
    gerarTokenFuncionalidade.setChave(loginAcessoUnico);
    gerarTokenFuncionalidade.setDocumento(cpf);
    return gerarToken(gerarTokenFuncionalidade, loginAcessoUnico);
  }

  @Transactional
  public void gerarTokenLoginUsuarioUnico(
      GerarTokenFuncionalidade model, AcessoUsuarioUnico acessoUsuarioUnico)
      throws GenericServiceException {
    String chaveExterna = acessoUsuarioUnico.getLogin();

    if (MecanismosAutenticacao.SMS
        .getCodigo()
        .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_SMS);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorSms(
          model,
          acessoUsuarioUnico.getDddNroCelular(),
          acessoUsuarioUnico.getNroCelular(),
          tokenGerado);
    } else if (MecanismosAutenticacao.EMAIL
        .getCodigo()
        .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_EMAIL);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorEmail(model, acessoUsuarioUnico, tokenGerado);
    } else if (MecanismosAutenticacao.WHATSAPP
        .getCodigo()
        .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_WHATSAPP);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorWhatsapp(
          model,
          acessoUsuarioUnico.getDddNroCelular(),
          acessoUsuarioUnico.getNroCelular(),
          tokenGerado,
          null,
          null);
    } else if (MecanismosAutenticacao.TODOS
        .getCodigo()
        .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
      model.setIdFuncionalidade(TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_TODOS);
      String tokenGerado = gerarToken(model, chaveExterna);
      enviarTokenPorSms(
          model,
          acessoUsuarioUnico.getDddNroCelular(),
          acessoUsuarioUnico.getNroCelular(),
          tokenGerado);
      enviarTokenPorEmail(model, acessoUsuarioUnico, tokenGerado);
      enviarTokenPorWhatsapp(
          model,
          acessoUsuarioUnico.getDddNroCelular(),
          acessoUsuarioUnico.getNroCelular(),
          tokenGerado,
          null,
          null);
    }
  }

  public TokenFuncionalidade encontraTokenValidadoParaTransacaoNaoUtilizado(
      String documento, Integer idInstituicao) {
    return tokenRepository
        .findByDocumentoAndIdInstituicaoAndInTokenValidadoAndDataHoraExpiracaoTokenIsAfterAndDataHoraUtilizacaoIsNotNullAndDtHrTokenEfetivadoIsNull(
            documento, idInstituicao, Boolean.TRUE, LocalDateTime.now());
  }

  public Boolean permitirTransacaoChecandoTokenValidado(String documento, Integer idInstituicao) {
    TokenFuncionalidade token =
        encontraTokenValidadoParaTransacaoNaoUtilizado(documento, idInstituicao);
    if (token != null) {
      token.setDtHrTokenEfetivado(LocalDateTime.now());
      save(token);
      return Boolean.TRUE;
    }
    return Boolean.FALSE;
  }
}
