package br.com.entity.suporte;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "aplicativo_frontend", schema = "suporte")
@ApiModel(value = "AplicativoFrontend")
public class AplicativoFrontend implements Serializable {

  private static final long serialVersionUID = 7326569134519614593L;

  @Id
  @Column(name = "id", unique = true, nullable = false)
  private Integer id;

  @Column(name = "id_processadora")
  private Long idProcessadora;

  @Column(name = "id_instituicao")
  private Integer idInstituicao;

  @Column(name = "nm_aplicativo")
  private String nomeAplicativo;

  @Column(name = "nm_aplicativo_apresentavel")
  private String nomeAplicativoApresentavel;

  @Column(name = "id_status")
  private Integer idStatus;

  @Column(name = "dt_hr_status")
  private LocalDateTime dtHoraStatus;

  @Column(name = "id_usuario_inclusao")
  private Long idUsuarioInclusao;

  @Column(name = "dt_hr_inclusao")
  private LocalDateTime dtHoraInclusao;

  @Column(name = "validacao_caf")
  private Boolean validacaoCaf;

  public Integer getId() {
    return id;
  }

  public void setId(Integer id) {
    this.id = id;
  }

  public Long getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Long idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public String getNomeAplicativo() {
    return nomeAplicativo;
  }

  public void setNomeAplicativo(String nomeAplicativo) {
    this.nomeAplicativo = nomeAplicativo;
  }

  public String getNomeAplicativoApresentavel() {
    return nomeAplicativoApresentavel;
  }

  public void setNomeAplicativoApresentavel(String nomeAplicativoApresentavel) {
    this.nomeAplicativoApresentavel = nomeAplicativoApresentavel;
  }

  public Integer getIdStatus() {
    return idStatus;
  }

  public void setIdStatus(Integer idStatus) {
    this.idStatus = idStatus;
  }

  public LocalDateTime getDtHoraStatus() {
    return dtHoraStatus;
  }

  public void setDtHoraStatus(LocalDateTime dtHoraStatus) {
    this.dtHoraStatus = dtHoraStatus;
  }

  public Long getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Long idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public LocalDateTime getDtHoraInclusao() {
    return dtHoraInclusao;
  }

  public void setDtHoraInclusao(LocalDateTime dtHoraInclusao) {
    this.dtHoraInclusao = dtHoraInclusao;
  }

  public Boolean getValidacaoCaf() {
    return validacaoCaf;
  }

  public void setValidacaoCaf(Boolean validacaoCaf) {
    this.validacaoCaf = validacaoCaf;
  }
}
