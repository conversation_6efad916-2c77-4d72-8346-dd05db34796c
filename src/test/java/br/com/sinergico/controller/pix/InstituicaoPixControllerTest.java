package br.com.sinergico.controller.pix;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.mockito.Mockito.when;

import br.com.BaseControllerTest;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.pix.InstituicaoPix;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.util.Constantes;
import javax.servlet.http.HttpServletRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class InstituicaoPixControllerTest extends BaseControllerTest {

  @Mock private InstituicaoPixService instituicaoPixService;
  @Mock private ContaPagamentoService contaPagamentoService;
  @Mock private HttpServletRequest request;
  @InjectMocks private InstituicaoPixController instituicaoPixController;

  @Test
  void deveVerificarPixHabilitadoQuandoInstituicaoNaoPermitida() throws Exception {
    Integer idInstituicaoNaoPermitida = 9999;
    Long idConta = 123L;

    InstituicaoPix instituicaoMock = new InstituicaoPix();
    instituicaoMock.setHabilitado(0);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idInstituicaoNaoPermitida))
        .thenReturn(instituicaoMock);

    ContaPagamento contaMock = new ContaPagamento();
    contaMock.setPixHabilitado(false);
    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta)).thenReturn(contaMock);
    ResponseEntity<Boolean> response =
        instituicaoPixController.buscarInstituicao(request, idInstituicaoNaoPermitida, idConta);
    assertNotEquals(
        Boolean.TRUE,
        response.getBody(),
        "PIX deveria estar desabilitado pois a conta não tem PIX ativo");
  }

  @Test
  void deveVerificarPixHabilitadoQuandoInstituicaoForPermitida() throws Exception {
    Integer idInstituicaoNaoPermitida = 8401;
    Long idConta = 123L;

    InstituicaoPix instituicaoMock = new InstituicaoPix();
    instituicaoMock.setHabilitado(1);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idInstituicaoNaoPermitida))
        .thenReturn(instituicaoMock);

    ContaPagamento contaMock = new ContaPagamento();
    contaMock.setPixHabilitado(true);
    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta)).thenReturn(contaMock);
    ResponseEntity<Boolean> response =
        instituicaoPixController.buscarInstituicao(request, idInstituicaoNaoPermitida, idConta);
    assertEquals(
        Boolean.TRUE,
        response.getBody(),
        "PIX deveria estar desabilitado pois a conta não tem PIX ativo");
  }

  @Test
  void deveIgnorarPixHabilitadoDaContaQuandoInstituicaoPermitida() throws Exception {
    InstituicaoPix instituicaoMock = new InstituicaoPix();
    ContaPagamento contaMock = new ContaPagamento();
    contaMock.setPixHabilitado(false);
    instituicaoMock.setHabilitado(1);
    Long idConta = 123L;

    Integer idProducaoInstituicaoVallooDigital = Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL;
    Integer idProducaoInstituicaoPaxpay = Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY;
    Integer idProducaoInstituicaoVallooPagamentos =
        Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS;
    Integer idProducaoInstituicaoAgroCash = Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH;
    Integer idProducaoInstituicaoElo = Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO;
    Integer idProducaoInstituicaoRp3Bank = Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK;
    Integer idProducaoInstituicaoEntrepay = Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY;
    Integer idProducaoInstituicaoDisnuvii = Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII;
    Integer idProducaoInstituicaoCvsCestas = Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS;
    Integer idProducaoInstituicaoWiz = Constantes.ID_PRODUCAO_INSTITUICAO_WIZ;
    Integer idProducaoInstituicaoQista = Constantes.ID_PRODUCAO_QISTA;

    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(
            idProducaoInstituicaoVallooDigital))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoPaxpay))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(
            idProducaoInstituicaoVallooPagamentos))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoAgroCash))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoElo))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoRp3Bank))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoEntrepay))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoDisnuvii))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoCvsCestas))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoWiz))
        .thenReturn(instituicaoMock);
    when(instituicaoPixService.findFirstByIdInstituicaoOrderById(idProducaoInstituicaoQista))
        .thenReturn(instituicaoMock);
    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta)).thenReturn(contaMock);

    ResponseEntity<Boolean> responseValloo =
        instituicaoPixController.buscarInstituicao(
            request, idProducaoInstituicaoVallooDigital, idConta);
    ResponseEntity<Boolean> responsePaxPay =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoPaxpay, idConta);
    ResponseEntity<Boolean> responseVallooPagamentos =
        instituicaoPixController.buscarInstituicao(
            request, idProducaoInstituicaoVallooPagamentos, idConta);
    ResponseEntity<Boolean> responseAgroCash =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoAgroCash, idConta);
    ResponseEntity<Boolean> responseElo =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoElo, idConta);
    ResponseEntity<Boolean> responseRp3 =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoRp3Bank, idConta);
    ResponseEntity<Boolean> responseEntrePay =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoEntrepay, idConta);
    ResponseEntity<Boolean> responseDisnuvii =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoDisnuvii, idConta);
    ResponseEntity<Boolean> responseCvsCestas =
        instituicaoPixController.buscarInstituicao(
            request, idProducaoInstituicaoCvsCestas, idConta);
    ResponseEntity<Boolean> responseWiz =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoWiz, idConta);
    ResponseEntity<Boolean> responseQista =
        instituicaoPixController.buscarInstituicao(request, idProducaoInstituicaoQista, idConta);

    assertEquals(
        Boolean.TRUE,
        responseValloo.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responsePaxPay.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseVallooPagamentos.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseAgroCash.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseElo.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseRp3.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseEntrePay.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseDisnuvii.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseCvsCestas.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseWiz.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
    assertEquals(
        Boolean.TRUE,
        responseQista.getBody(),
        "PIX deveria estar habilitado pois a instituição é permitida");
  }
}
