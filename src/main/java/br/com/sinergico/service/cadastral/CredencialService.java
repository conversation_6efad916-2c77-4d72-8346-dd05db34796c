package br.com.sinergico.service.cadastral;

import static br.com.sinergico.util.Constantes.ADICIONAL;
import static java.lang.Boolean.FALSE;

import br.com.client.rest.jcard.json.bean.AccountStatementResponse;
import br.com.client.rest.jcard.json.bean.CardResponse;
import br.com.client.rest.jcard.json.bean.ChangePassCardReplacement;
import br.com.client.rest.jcard.json.bean.CheckPinRequest;
import br.com.client.rest.jcard.json.bean.ErrorJcard;
import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.client.rest.jcard.json.bean.GetPinCriptoRequest;
import br.com.client.rest.jcard.json.bean.JcardRequestEmbossing;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponseEmbossing;
import br.com.client.rest.jcard.json.bean.UpdatePinRequest;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPagamentoFatura;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.CorporativoLogin;
import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.entity.cadastral.CorporativoResponsavelCredencial;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.CredencialConta;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.PerfilTarifarioTransacao;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracaoCredito;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.suporte.AcessoLogPan;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.CotacaoPontos;
import br.com.entity.suporte.CredencialPreEmitidoExterna;
import br.com.entity.suporte.EnvioSMSInstituicao;
import br.com.entity.suporte.GatewaySMS;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.HistoricoStatusCredencial;
import br.com.entity.suporte.LogAlteracaoPinCredencial;
import br.com.entity.suporte.LogRegistroAlterarStatus;
import br.com.entity.suporte.LogStatusCredencialJcard;
import br.com.entity.suporte.MapaStatus;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.suporte.PerguntaVerificacaoCredencial;
import br.com.entity.suporte.Plastico;
import br.com.entity.suporte.TipoGrupoStatus;
import br.com.entity.suporte.TipoStatus;
import br.com.entity.transacional.LancamentoAuto;
import br.com.entity.transacional.PagamentoCredencial;
import br.com.exceptions.BusinessException;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.JcardServiceException;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.adq.CredencialPortadorLancamentoAcquirerVO;
import br.com.json.bean.cadastral.CredenciaisDisponiveisDTO;
import br.com.json.bean.cadastral.CredencialExternaTO;
import br.com.json.bean.cadastral.CredencialJcard;
import br.com.json.bean.cadastral.CredencialLoteResponse;
import br.com.json.bean.cadastral.CredencialParaRecadastramentoPin;
import br.com.json.bean.cadastral.CredencialStatus;
import br.com.json.bean.cadastral.CredencialTransferencia;
import br.com.json.bean.cadastral.DetalheCredencialLoteVo;
import br.com.json.bean.cadastral.GerarCredencialRequest;
import br.com.json.bean.cadastral.GetContadorSenhaCard;
import br.com.json.bean.cadastral.GetCredenciaisResponse;
import br.com.json.bean.cadastral.GetCredencial;
import br.com.json.bean.cadastral.PessoaAdicionalPDAFRequest;
import br.com.json.bean.cadastral.PortadorCorporativo;
import br.com.json.bean.cadastral.PortadorCredencial;
import br.com.json.bean.cadastral.SegundaViaCredencialRequest;
import br.com.json.bean.cadastral.SenhaCartaoAppRequest;
import br.com.json.bean.cadastral.TrocarEstadoCredencialJcard;
import br.com.json.bean.cadastral.TrocarEstadoCredencialRequest;
import br.com.json.bean.cadastral.TrocarEstadoNFCCredencialRequest;
import br.com.json.bean.enums.NFCStateJcardEnum;
import br.com.json.bean.enums.StatusJcardEnum;
import br.com.json.bean.suporte.ComunicadoContaViaSMS;
import br.com.json.bean.suporte.ComunicadoContaViaWhatsapp;
import br.com.json.bean.suporte.GetExtratoCredencial;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.json.bean.suporte.NovoAcessoLogPan;
import br.com.json.bean.suporte.UtilizarTokenAcessoRequest;
import br.com.json.bean.transacional.CadastroLancamentoManual;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.AntifraudeCafFacialObjetivosEnum;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.enums.RegraTipoPortadorEnum;
import br.com.sinergico.enums.SegundaViaBoletoEnum;
import br.com.sinergico.enums.StatusRegistroAlterarEnum;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.repository.cadastral.ContaPagamentoFaturaRepository;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.cadastral.PerfilTarifarioTransacaoRepository;
import br.com.sinergico.repository.cadastral.ProdutoContratadoRepository;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoConfiguracaoCreditoRepository;
import br.com.sinergico.repository.cadastral.RepresentanteLegalRepository;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.repository.suporte.HistoricoStatusCredencialRepository;
import br.com.sinergico.repository.suporte.PerguntaVerificacaoCredencialRepository;
import br.com.sinergico.repository.suporte.PlasticoRepository;
import br.com.sinergico.repository.transacional.PagamentoCredencialRepository;
import br.com.sinergico.security.CriptoUtil;
import br.com.sinergico.security.LegacyPasswordEncoder;
import br.com.sinergico.security.SSM;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.GeradorCredencialService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.corporativo.CorporativoService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.jcard.JcardService;
import br.com.sinergico.service.suporte.AcessoLogPanService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.BoletoSegundaViaService;
import br.com.sinergico.service.suporte.ComunicadorPortadorSMSService;
import br.com.sinergico.service.suporte.ComunicadorPortadorWhatsappService;
import br.com.sinergico.service.suporte.ConfigReposicaoCredProdService;
import br.com.sinergico.service.suporte.CredencialPreEmitidoExternaService;
import br.com.sinergico.service.suporte.EnvioSMSInstituicaoService;
import br.com.sinergico.service.suporte.GatewaySMSService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.LogAlteracaoPinCredencialService;
import br.com.sinergico.service.suporte.LogSolicitacaoReposicaoCredencialService;
import br.com.sinergico.service.suporte.LogStatusCredencialJcardService;
import br.com.sinergico.service.suporte.MapaStatusService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.suporte.PerguntaVerificacaoTentativaCredencialService;
import br.com.sinergico.service.suporte.RegistroValidacaoFacialCafService;
import br.com.sinergico.service.suporte.RegistroValidacaoSenhaService;
import br.com.sinergico.service.suporte.TipoGrupoStatusService;
import br.com.sinergico.service.suporte.TipoStatusService;
import br.com.sinergico.service.suporte.TokenAcessoService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.B2bTarifaService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.CredencialUtil;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.ObjectUtil;
import br.com.sinergico.util.StatusCredencialComparator;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.ContaPagamentoVO;
import br.com.sinergico.vo.CredenciaisProdutoVirtualVO;
import br.com.sinergico.vo.CredencialContaResponse;
import br.com.sinergico.vo.SegundaViaCredencialRequestVO;
import br.com.sinergico.vo.sistema.InformacoesCredencialContaVO;
import br.com.sinergico.vo.vcn.DadosSensiveisCredencial;
import com.google.common.base.Strings;
import com.google.gson.Gson;
import java.io.InputStream;
import java.math.BigDecimal;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.persistence.EntityManager;
import javax.persistence.LockModeType;
import javax.persistence.PersistenceContext;
import javax.servlet.http.HttpServletRequest;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.jpos.iso.ISOUtil;
import org.jpos.security.SMException;
import org.jpos.security.SecureDESKey;
import org.jpos.security.SecureKeyStore;
import org.jpos.security.SecureKeyStore.SecureKeyStoreException;
import org.jpos.security.SimpleKeyFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;

@Service
public class CredencialService extends GenericService<Credencial, Long> {

  private static final int ID_PROD_PLAT_PRE_PAGO = 1;
  private static final int JURIDICA = 2;
  private static final int FISICA = 1;
  private static final int TAMANHO_CPF = 11;
  private static final int MOTIVO_ROUBO = 4;
  private static final int MOTIVO_PERDA = 3;
  private static final int MOTIVO_2A_VIA_COMUM = 1;
  private static final int MOTIVO_2A_VIA_EXTRAVIO = 2;
  private static final int DIAS_UTEIS_2VIA = 9;
  private static final int CANCELADO_EXTRAVIO = 58;

  private static final int ENVIO_SENHA_CREDENCIAL_ADICIONAL_CRIACAO_CONTA = 2;
  private static final String SENHA_CARTAO = "#SENHA_CARTAO#";
  private static final String ULTIMOS_4_DIGITOS = "#ULTIMOS_4_DIGITOS#";
  private static final int ENVIO_SENHA_CREDENCIAL_CRIACAO_CONTA = 1;
  private static final String SHA_256 = "SHA-256";
  private static final int CREDITO = 1;
  private static final int QTD_RESERVADA_TAMANHO_PIN = 2;
  private static final int POSICAO_INICIAL = 0;
  private static final String ID_CONSUMER = "001";
  private static final int ATIVO = 1;
  public static final String ISSUER_DIR_SECURITY_CFG = "issuer.dir.security.cfg";
  public static final String ISSUER_DIR_SECURITY_LMK = "issuer.dir.security.lmk";
  private static final String FMT_MM_YY = "MM/yy";
  private static final int HOJE = 1;
  private static final int MIN_SEGUNDOS = 0;
  private static final int MIN_MINUTO = 0;
  private static final int MIN_HORA = 0;
  public static final String MASK_2_PARTES = "$1-$2";
  public static final String MASK_2_PARTES_COMPLETA = "$1-XXXX-XXXX-$2";

  public static final String PADRAO_CREDENCIAL = "([0-9]{4})[0-9]{0,9}([0-9]{4})";

  public static final char CARACTER_ZERO = '0';

  public static final int QUATRO_DIGITOS = 4;
  private static final Boolean NAO_VIRTUAL = false;
  private static final Boolean VIRTUAL = true;
  private static final Boolean HABILITADO = true;
  private static final Boolean DESABILITADO = false;
  public static final int STATUS_CREDENCIAL_ATIVA = 1;
  public static final int STATUS_CREDENCIAL_BLOQUEIO_ORIGEM = 0;
  private static final Integer STATUS_PERDA = 32;
  private static final Integer STATUS_ROUBO = 33;
  private static final BigDecimal SALDO_PADRAO = new BigDecimal(0);
  private static final int TROCAR_STATUS_EXTERIOR = 1;
  private static final int TROCAR_STATUS_ECOMMERCE = 2;
  private static final int TROCAR_STATUS_SAQUE = 3;
  private static final int TROCAR_STATUS_USO_PESSOA = 4;
  private static final int TROCAR_STATUS_NOTIFICACAO_TRANSACAO = 5;
  private static final int CANCELADO = 9;
  private static final int BLOQUEIO = 2;
  private static final int PRIMEIRA_POSICAO = 0;
  private static final Integer APLICABILIDADE_CREDENCIAL = 1;
  private static final Integer AUTOR_URA = 4;
  private static final Integer AUTOR_AUTOMATICO = 3;
  private static final Integer AUTOR_PORTADOR = 2;
  private static final Integer AUTOR_SISTEMA = 1;
  private static final int USUARIO_PORTADOR = 999999;
  private static final int USUARIO_AUTOMATICO = 999900;
  private static final int USUARIO_URA_BAHAMAS = 42;
  private static final int USUARIO_URA_ITSPAY = 43;
  private static final int USUARIO_URA_BRB = 4967;
  public static final Integer DESBLOQUEADO = 1;
  public static final Integer BLOQUEIO_DE_ORIGEM = 0;
  public static final Integer BLOQUEIO_PREVENTIVO = 5;
  public static final Integer TIPO_GRUPO_STATUS_TEMPORARIO = 2;

  public static final List<Integer> GRUPO_STATUS_DESBLOQUEIO = Arrays.asList(0, 1, 2);

  public static final List<Integer> ESTADOS_NFC_DISPONIVEIS = Arrays.asList(0, 1);

  public static final Integer ID_RELACIONAMENTO_PRE = 1;
  public static final Integer ID_RELACIONAMENTO_POS = 2;

  // existe uma tabela com o tipo de evento
  private static final Integer TIPO_EVENTO_ENVIO_SENHA_SMS = 1;

  private static final int CANCELADO_USUARIO = 30;
  private static final int CANCELADO_INSTITUICAO = 31;

  private static final int REPOSTO = 10;

  private static final String CONFIGURACAO_INVALIDA =
      "Configuração de credencial invalida, Verifique a configuração do produto ";

  // Segunda via
  private static final Integer COD_TRANSACAO_TARIFA_2_VIA = 562;
  private static final Integer COD_EVENTO_TARIFA_2_VIA = 6;
  private static final String ERRO_SALDO_INSUFICIENTE = "not.sufficient.funds";
  private static final String ERRO_FUNCTION_CODE = "invalid.functioncode";
  private static final Integer COBRAR_EMPRESA = 0;

  private static final Integer ENDERECO_PESSOA_ATIVO = 1;

  private static final Logger log = LoggerFactory.getLogger(CredencialService.class);

  private static final String URL = "/jcard/api/cards/getdataforprinting";

  @Autowired private CredencialRepository credencialRepository;

  @Autowired
  private LogSolicitacaoReposicaoCredencialService logSolicitacaoReposicaoCredencialService;

  @Autowired private ConfigReposicaoCredProdService configReposicaoCredProdService;

  @Lazy @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfigService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private EnderecoPessoaService enderecoPessoaService;

  @Autowired private PlasticoRepository plasticoRepository;

  @Autowired private RegistroValidacaoFacialCafService registroValidacaoFacialCafService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired
  private ProdutoInstituicaoConfiguracaoCreditoRepository
      produtoInstituicaoConfiguracaoCreditoRepository;

  @Autowired private ContaPagamentoFaturaRepository contaPagamentoFaturaRepository;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private GeradorCredencialService geradorCredencialService;

  @Autowired private TipoStatusService tipoStatusService;

  @Autowired private MapaStatusService mapaStatusService;

  @Autowired private CardService cardService;

  @Autowired private AcessoUsuarioService usuarioService;

  @Lazy @Autowired private HierarquiaPontoRelacionamentoService service;

  @Autowired private AcessoLogPanService acessoLogPanService;

  @Autowired private ComunicadorPortadorSMSService agenteComunicadorService;

  @Autowired private ComunicadorPortadorWhatsappService agenteComunicadorWhatsappService;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired private EnvioSMSInstituicaoService envioSMSInstituicaoService;

  @Autowired private CredencialPreEmitidoExternaService credencialPreEmitidoExternaService;

  @Autowired private DateUtil dateUtil;

  @PersistenceContext private EntityManager em;

  @Autowired private GatewaySMSService gatewaySMSService;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private PerfilTarifarioTransacaoRepository perfilTarifarioTransacaoRepository;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private ProdutoContratadoRepository produtoContratadoRepository;

  @Autowired private B2bTarifaService b2bTarifasService;

  @Autowired private LogStatusCredencialJcardService logStatusCredencialJcardService;

  @Autowired private LogAlteracaoPinCredencialService logAlteracaoPinCredencialService;

  @Autowired private PerguntaVerificacaoCredencialRepository perguntaVerificacaoUsuarioRepository;

  @Autowired
  private PerguntaVerificacaoTentativaCredencialService
      perguntaVerificacaoTentativaCredencialService;

  @Lazy @Autowired private EmailService emailService;

  @Autowired private RepresentanteLegalRepository representanteLegalRepository;

  @Autowired private CotacaoPontosRepository cotacaoPontosRepository;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private RegistroValidacaoSenhaService registroValidacaoSenhaService;

  @Autowired private CredencialContaService credencialContaService;

  @Autowired private BoletoSegundaViaService boletoSegundaViaService;

  @Autowired private PagamentoCredencialRepository pagamentoCredencialRepository;

  @Autowired private HistoricoStatusCredencialRepository historicoStatusCredencialRepository;

  @Autowired private CredencialFacade credencialFacade;

  @Autowired private UtilService utilService;

  @Value("${jcard.url}")
  private String jcardUrl;

  @Value("${issuer.dir.security.cfg}")
  private String issuerDirSecurityCfg;

  @Value("${issuer.dir.security.lmk}")
  private String issuerDirSecurityLmk;

  @Autowired private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

  @Autowired private JcardService jcardService;

  @Autowired private TipoGrupoStatusService tipoGrupoStatusService;

  @Lazy @Autowired private CorporativoService corporativoService;

  @Autowired private CredencialUtil credencialUtil;

  @Autowired private TokenAcessoService tokenAcessoService;

  @Autowired
  public CredencialService(CredencialRepository repo) {
    super(repo);
    credencialRepository = repo;
  }

  /**
   * Método responsável por buscar uma lista de credenciais a partir dos parâmetros informados
   *
   * @param idContaPagamento
   * @param idPessoa
   * @return
   */
  public List<Credencial> findByIdContaPagamentoAndIdPessoa(
      String idContaPagamento, Long idPessoa) {
    return credencialRepository.findByIdContaPagamentoAndIdPessoa(idContaPagamento, idPessoa);
  }

  /**
   * Método responsável por buscar uma lista de credenciais ( virtuais e físicas)a partir dos
   * parâmetros informados
   *
   * @param idConta
   * @param idPessoa
   * @param titularidade
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndTitularidade(
      Long idConta, Long idPessoa, Integer titularidade) {
    return credencialRepository.findByIdContaAndIdPessoaAndTitularidade(
        idConta, idPessoa, titularidade);
  }

  /**
   * Método responsável por buscar uma lista de credenciais ( virtuais e físicas)a partir dos
   * parâmetros informados
   *
   * @param idConta
   * @param idPessoa
   * @param titularidade
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndTitularidadeOrderByCsnDesc(
      Long idConta, Long idPessoa, Integer titularidade) {
    return credencialRepository.findByIdContaAndIdPessoaAndTitularidadeOrderByCsnDesc(
        idConta, idPessoa, titularidade);
  }

  /**
   * Método responsável por buscar credenciais do portador de acordo com os parametros passados
   *
   * @param documento cpf ou cnpj do portador
   * @param idProcessadora processadora do cartao
   * @param idInstituicao instituicao que esta atrelada a credencial
   * @param tipoPessoa tipoPessoa = 1 para cpf e tipoPessoa = 2 para cnpj
   * @return lista de credenciais ordenadas de forma decrescente
   */
  public List<Credencial> findCredenciaisByDocumentoAndInstituicaoAndProcessadorAndTipoPessoa(
      Integer idProcessadora, Integer idInstituicao, String documento, Integer tipoPessoa) {
    return credencialRepository.findCredenciaisByDocumentoAndInstituicaoAndProcessadorAndTipoPessoa(
        idProcessadora, idInstituicao, documento, tipoPessoa);
  }

  /**
   * Método responsável por buscar credenciais do portador de acordo com os parametros passados
   *
   * @param documento cpf ou cnpj do portador
   * @param idProcessadora processadora do cartao
   * @param idInstituicao instituicao que esta atrelada a credencial
   * @param tipoPessoa tipoPessoa = 1 para cpf e tipoPessoa = 2 para cnpj
   * @return lista de credenciais ordenadas de forma decrescente
   */
  public List<Credencial>
      findCredenciaisByDocumentoAndInstituicaoAndIdProcessadoraAndTipoPessoaAndStatus(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idProdInstituicao,
          String documento,
          Integer tipoPessoa,
          List<Integer> status,
          Integer ultimos4Digitos) {
    return credencialRepository
        .findCredenciaisByDocumentoAndInstituicaoAndIdProcessadoraAndTipoPessoaAndStatus(
            idProcessadora,
            idInstituicao,
            idProdInstituicao,
            documento,
            tipoPessoa,
            status,
            ultimos4Digitos);
  }

  public List<Credencial>
      findCredenciaisAdicionaisByDocumentoAndInstituicaoAndProcessadorAndTipoPessoa(
          Integer idProcessadora, Integer idInstituicao, String documento, Integer tipoPessoa) {
    return credencialRepository
        .findCredenciaisAdicionaisByDocumentoAndInstituicaoAndProcessadorAndTipoPessoa(
            idProcessadora, idInstituicao, documento, tipoPessoa);
  }

  public List<Credencial> findByIdContaAndTitularidadeOrderByCsnDesc(
      Long idConta, Integer titularidade) {
    return credencialRepository.findByIdContaAndTitularidadeOrderByCsnDesc(idConta, titularidade);
  }

  public Credencial
      findFirstByIdContaAndTitularidadeAndVirtualAndTipoStatusIdGrupoStatusNotInOrderByCsnDesc(
          Long idConta, Integer titularidade, Boolean virtual, Collection<Integer> idGrupoStatus) {
    return credencialRepository
        .findFirstByIdContaAndTitularidadeAndVirtualAndTipoStatusIdGrupoStatusNotInOrderByCsnDesc(
            idConta, titularidade, virtual, idGrupoStatus);
  }

  /**
   * Método responsável por buscar uma lista de credenciais ( virtuais e físicas)a partir dos
   * parâmetros informados
   *
   * @param idConta
   * @param idPessoa
   * @param titularidade
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndTitularidadeAndVirtualOrderByCsnDesc(
      Long idConta, Long idPessoa, Boolean virtual, Integer titularidade) {
    return credencialRepository.findByIdContaAndIdPessoaAndTitularidadeAndVirtualOrderByCsnDesc(
        idConta, idPessoa, virtual, titularidade);
  }

  /**
   * Método responsável por buscar uma lista de credenciais ( virtuais e físicas)a partir dos
   * parâmetros informados
   *
   * @param idConta
   * @param idPessoa
   * @param titularidade
   * @param status
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndTitularidadeAndStatusOrderByCsnDesc(
      Long idConta, Long idPessoa, Integer titularidade, Integer status) {
    return credencialRepository.findByIdContaAndIdPessoaAndTitularidadeAndStatusOrderByCsnDesc(
        idConta, idPessoa, titularidade, status);
  }

  /**
   * Método responsável por buscar uma lista de credenciais ( virtuais e físicas)a partir dos
   * parâmetros informados
   *
   * @param idConta
   * @param idPessoa
   * @param titularidade
   * @return
   */
  public Credencial buscarCredencialMaisRecente(
      Long idConta, Long idPessoa, Boolean virtual, Integer titularidade) {
    List<Credencial> credenciais =
        findByIdContaAndIdPessoaAndTitularidadeAndVirtualOrderByCsnDesc(
            idConta, idPessoa, virtual, titularidade);

    if (credenciais != null && !credenciais.isEmpty()) {
      return credenciais.get(PRIMEIRA_POSICAO);
    }

    return null;
  }

  public Credencial buscarCredencialMaisRecente(Long idConta, Long idPessoa, Integer titularidade) {
    List<Credencial> credenciais =
        findByIdContaAndIdPessoaAndTitularidadeOrderByCsnDesc(idConta, idPessoa, titularidade);

    if (credenciais != null && !credenciais.isEmpty()) {
      return credenciais.get(PRIMEIRA_POSICAO);
    }

    return null;
  }

  /**
   * Método responsável por buscar credenciais do portador e retornar a mais recente
   *
   * @param documento cpf ou cnpj do portador
   * @param idProcessadora processadora do cartao
   * @param idInstituicao instituicao que esta atrelada a credencial
   * @param tipoPessoa tipoPessoa = 1 para cpf e tipoPessoa = 2 para cnpj
   * @return credencial mais recente ou null caso nao encontre nenhuma credencial
   */
  public Credencial buscarCredencialNaoBloqueadaMaisRecente(
      String documento, Integer idProcessadora, Integer idInstituicao, Integer tipoPessoa) {
    List<Credencial> credenciais =
        findCredenciaisByDocumentoAndInstituicaoAndProcessadorAndTipoPessoa(
            idProcessadora, idInstituicao, documento, tipoPessoa);

    if (credenciais != null && !credenciais.isEmpty()) {
      return credenciais.get(PRIMEIRA_POSICAO);
    } else {
      throw new GenericServiceException(ConstantesErro.TIN_CARTAO_NAO_ENCONTRADO.getMensagem());
    }
  }

  public Credencial buscarCredencialAdicionalMaisRecente(
      String documento, Integer idProcessadora, Integer idInstituicao, Integer tipoPessoa) {
    List<Credencial> credenciais =
        findCredenciaisAdicionaisByDocumentoAndInstituicaoAndProcessadorAndTipoPessoa(
            idProcessadora, idInstituicao, documento, tipoPessoa);

    if (credenciais != null && !credenciais.isEmpty()) {
      return credenciais.get(PRIMEIRA_POSICAO);
    }

    return null;
  }

  public Credencial buscarCredencialFisicaMaisRecenteContaTitular(
      Long idConta, Integer titularidade, Boolean virtual, Collection<Integer> idGrupoStatus) {
    return findFirstByIdContaAndTitularidadeAndVirtualAndTipoStatusIdGrupoStatusNotInOrderByCsnDesc(
        idConta, titularidade, virtual, idGrupoStatus);
  }

  public Credencial getCredencialFisicaMaisRecente(Long idConta) {
    Collection<Integer> gruposNaoVemNaPesquisa = new ArrayList<>();
    gruposNaoVemNaPesquisa.add(Constantes.GRUPO_STATUS_CANCELADO);

    Credencial credencialFisicaMaisRecente =
        buscarCredencialFisicaMaisRecenteContaTitular(
            idConta,
            Constantes.TITULARIDADE_CREDENCIAL,
            Constantes.CREDENCIAL_NAO_VIRTUAL,
            gruposNaoVemNaPesquisa);
    if (Objects.isNull(credencialFisicaMaisRecente)) {
      return null;
    }
    return credencialFisicaMaisRecente;
  }

  /**
   * Método responsável por buscar uma lista de credenciais virtuais ou não a partir dos parâmetros
   * informados
   *
   * @param idConta
   * @param idPessoa
   * @param virtual true para buscar somente os virtuais e false para buscar somente os não virtuais
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndVirtual(
      Long idConta, Long idPessoa, Boolean virtual) {
    return credencialRepository.findByIdContaAndIdPessoaAndVirtual(idConta, idPessoa, virtual);
  }

  /**
   * @param idConta
   * @param idPessoa
   * @param virtual
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndVirtualAndTipoStatusIdGrupoStatusNotIn(
      Long idConta, Long idPessoa, Boolean virtual, Collection<Integer> idsGruposStatus) {
    return credencialRepository.findByIdContaAndIdPessoaAndVirtualAndTipoStatusIdGrupoStatusNotIn(
        idConta, idPessoa, virtual, idsGruposStatus);
  }

  /**
   * Metodo responsavel por buscar a versao mais recente de uma credencial(baseado na csn)
   *
   * @param idConta
   * @param idPessoa
   * @param virtual
   * @param idsGruposStatus
   * @return
   */
  public Credencial
      findFirstByIdContaAndIdPessoaAndVirtualAndTipoStatusIdGrupoStatusNotInOrderByCsnDesc(
          Long idConta, Long idPessoa, Boolean virtual, Collection<Integer> idsGruposStatus) {
    return credencialRepository
        .findFirstByIdContaAndIdPessoaAndVirtualAndTipoStatusIdGrupoStatusNotInOrderByCsnDesc(
            idConta, idPessoa, virtual, idsGruposStatus);
  }

  /**
   * @param idConta
   * @param idPessoa
   * @param idStatus
   * @return
   */
  public List<Credencial> findByIdContaAndIdPessoaAndStatusIn(
      Long idConta, Long idPessoa, Collection<Integer> idStatus) {
    return credencialRepository.findByIdContaAndIdPessoaAndStatusIn(idConta, idPessoa, idStatus);
  }

  public List<CredenciaisProdutoVirtualVO> findByIdPessoaAndIdContaAndStatusIn(
      Long idPessoa, Long idConta, Collection<Integer> idStatus) {
    return credencialRepository.findByIdPessoaAndIdContaAndStatusIn(idPessoa, idConta, idStatus);
  }

  public List<Credencial> findByIdContaAndVirtualAndTipoStatusTipoGrupoStatusIdGrupoStatusNotIn(
      Long idConta, Boolean virtual, Collection<Integer> idsGruposStatus) {
    return credencialRepository.findByIdContaAndVirtualAndTipoStatusIdGrupoStatusNotIn(
        idConta, virtual, idsGruposStatus);
  }

  /**
   * Método responsável por buscar uma lista de credenciais virtuais ou não a partir dos parâmetros
   * informados
   *
   * @param idConta
   * @param virtual true para buscar somente os virtuais e false para buscar somente os não virtuais
   * @return
   */
  public List<Credencial> findByIdContaAndVirtual(Long idConta, Boolean virtual) {
    return credencialRepository.findByIdContaAndVirtual(idConta, virtual);
  }

  /**
   * Metodo responsavel por buscar credenciais da conta
   *
   * @param idConta
   * @return
   */
  public List<Credencial> findByIdContaInCredencialConta(Long idConta) {
    List<Credencial> find = credencialRepository.findByIdContaInCredencialConta(idConta);
    return find;
  }

  public List<Credencial> findByIdContaNaoVirtual(Long idConta) {
    return credencialRepository.findByIdContaNaoVirtual(idConta);
  }

  /**
   * Metodo responsavel por buscar uma credencial de uma conta
   *
   * @param idConta
   * @return
   */
  public List<Credencial> findByIdContaAndUltimos4Digitos(Long idConta, Long ultimosQuatro) {
    return credencialRepository.findByIdContaAndUltimos4Digitos(idConta, ultimosQuatro.intValue());
  }

  /**
   * Metodo responsavel por buscar as credenciais não virtuais de uma conta
   *
   * @param idConta
   * @return
   */
  public List<Credencial> findByIdContaAndUltimos4DigitosNaoVirtual(
      Long idConta, Long ultimosQuatro) {
    return credencialRepository.findByIdContaAndUltimos4DigitosNaoVirtual(
        idConta, ultimosQuatro.intValue());
  }

  /**
   * Metodo responsavel por buscar última versão da credencial da conta
   *
   * @param idConta
   * @param titularidade
   * @return
   */
  public List<Credencial> findByIdContaAndTitularidadde(Long idConta, Integer titularidade) {
    return credencialRepository.findByIdContaAndTitularidadeOrderByCsnDesc(idConta, titularidade);
  }

  /**
   * Método responsável por buscar uma credencial a partir dos parâmetros informados
   *
   * @param tokenInterno
   * @param idPessoa
   * @return
   */
  public Credencial findOneByTokenInternoAndIdPessoa(String tokenInterno, Long idPessoa) {
    return credencialRepository.findOneByTokenInternoAndIdPessoa(tokenInterno, idPessoa);
  }

  /**
   * étodo responsável por buscar uma credencial a partir do token interno informado
   *
   * @param tokenInterno
   * @return
   */
  public Credencial findOneByTokenInterno(String tokenInterno) {
    return credencialRepository.findOneByTokenInterno(tokenInterno);
  }

  /**
   * Método responsável por mascarar uma credencial conforme os parâmetros enviados
   *
   * @param credencialSemMascara
   * @param regex
   * @param mascara
   * @return
   */
  public static String getCredencialMascarada(
      String credencialSemMascara, String regex, String mascara) {
    // final String MASKCARD = "$1-$2";
    // regex = "([0-9]{4})[0-9]{0,9}([0-9]{4})";
    final Pattern PATTERNCARD = Pattern.compile(regex);
    Matcher matcher = PATTERNCARD.matcher(credencialSemMascara);
    if (matcher.find()) {
      return matcher.replaceAll(mascara);
    }
    return credencialSemMascara;
  }

  private List<Pessoa> findPessoas(
      String documento, Integer idProc, Integer idInst, Integer tipoPessoa) {
    return pessoaService.findByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
        idProc, idInst, documento, tipoPessoa);
  }

  private List<Pessoa> findPessoas(String documento, Integer idProc, Integer idInst) {
    return pessoaService.findByIdProcessadoraAndIdInstituicaoAndDocumento(
        idProc, idInst, documento);
  }

  private Pessoa findPessoa(String documento, Integer idProc, Integer idInst, Integer tipoPessoa) {
    return pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
        idProc, idInst, documento, tipoPessoa);
  }

  /**
   * Método responsável por buscar as credenciais virtuais de um portador em uma instituicao
   *
   * @return {@link GetCredenciaisResponse}
   */
  public GetCredenciaisResponse getCredenciaisVituaisConta(Long idConta) {

    List<GetCredencial> credenciaisRetorno = new ArrayList<>();

    ContaPagamento contaPagamento = getContaPagamentoByIdNotNull(idConta);

    List<TipoGrupoStatus> tipoGrupoStatusNaoVisiveisNoApp =
        tipoGrupoStatusService.buscarPorComportamentosBloqueados(
            TipoGrupoStatus::getVisivelAplicativo);

    Collection<Integer> idsGruposStatus =
        tipoGrupoStatusNaoVisiveisNoApp.stream()
            .map(TipoGrupoStatus::getIdGrupoStatus)
            .collect(Collectors.toList());

    List<Credencial> credenciais =
        findByIdContaAndVirtualAndTipoStatusTipoGrupoStatusIdGrupoStatusNotIn(
            idConta, VIRTUAL, idsGruposStatus);

    if (credenciais != null && !credenciais.isEmpty()) {

      for (Credencial credencial : credenciais) {

        Integer idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
        ProdutoInstituicao produtoInstituicao =
            getProdutoInstituicaoNotNull(contaPagamento, idProdutoInstituicao);
        List<Plastico> plastico = getPlasticoNotNull(idProdutoInstituicao, produtoInstituicao);

        Pessoa p = getPessoaByIdNotNull(credencial);

        GetCredencial c =
            prepareGetCredencial(
                contaPagamento,
                credencial,
                produtoInstituicao,
                plastico.get(0),
                p.getNomeCompleto(),
                p.getEmail());
        c.setApelidoVirtual(credencial.getApelidoVirtual());

        GetCardResponse response = getCard(credencial);

        c.setCredencialVirtual(response.getCard().getPan());
        c.setCodigoSeguranca(response.getCard().getCvv2());

        credenciaisRetorno.add(c);
      }
    }
    return new GetCredenciaisResponse(credenciaisRetorno);
  }

  private GetCardResponse getCard(Credencial credencial) {

    GetCardResponse response = cardService.getCard(credencial.getTokenInterno());

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Credencial virtual não encontrada. Token: " + credencial.getTokenInterno());
    }
    return response;
  }

  private ContaPagamento getContaPagamentoByIdNotNull(Long idConta) {
    ContaPagamento contaPagamento = contaPagamentoService.findById(idConta);

    if (contaPagamento == null) {
      throw new GenericServiceException("ContaPagamento não encontrada: idConta: " + idConta);
    }
    return contaPagamento;
  }

  public Pessoa getPessoaByIdNotNull(Credencial credencial) {
    Pessoa p = pessoaService.findById(credencial.getIdPessoa());
    if (p == null) {
      throw new GenericServiceException(
          "Pessoa não encontrada para informações enviadas.idPessoa: " + credencial.getIdPessoa());
    }
    return p;
  }

  private List<Plastico> getPlasticoNotNull(
      Integer idProdutoInstituicao, ProdutoInstituicao produtoInstituicao) {
    List<Plastico> plastico =
        plasticoRepository
            .findByIdProcessadoraAndIdInstituicaoAndIdProdutoInstituicaoAndDataCancelamentoEmissaoIsNull(
                produtoInstituicao.getIdProcessadora(),
                produtoInstituicao.getIdInstituicao(),
                idProdutoInstituicao);

    if (plastico.size() == 0) {
      throw new GenericServiceException(
          "Plastico não encontrado para informações enviadas. idProcessadora="
              + produtoInstituicao.getIdProcessadora()
              + " , idInstituicao = "
              + produtoInstituicao.getIdInstituicao()
              + " ,idProdutoInstituicao = "
              + idProdutoInstituicao);
    }
    return plastico;
  }

  /**
   * Método responsável por buscar as credenciais fisicas do portador
   *
   * @param documento
   * @param idProc
   * @param idInst
   * @param tipoPessoa
   * @return {@link GetCredenciaisResponse}
   */
  public GetCredenciaisResponse getCredenciais(
      String documento, Integer idProc, Integer idInst, Integer tipoPessoa) {

    List<Pessoa> pessoas = findPessoasNotNull(documento, idProc, idInst, tipoPessoa);

    List<GetCredencial> credenciaisRetorno = new ArrayList<>();
    for (Pessoa p : pessoas) {
      List<ContaPessoa> contasPessoa = getContasPessoaNotNull(documento, idProc, idInst, p);

      for (ContaPessoa cp : contasPessoa) {

        ContaPagamento contaPagamento = cp.getContaPagamento();
        Collection<Integer> gruposStatusNaoVemNaPesquisa = new ArrayList<>();
        gruposStatusNaoVemNaPesquisa.add(CANCELADO);

        Credencial credencial =
            findFirstByIdContaAndIdPessoaAndVirtualAndTipoStatusIdGrupoStatusNotInOrderByCsnDesc(
                contaPagamento.getIdConta(),
                p.getIdPessoa(),
                NAO_VIRTUAL,
                gruposStatusNaoVemNaPesquisa);

        if (credencial != null) {
          credenciaisRetorno.add(loadPlastico(credencial, contaPagamento, p, idProc, idInst));
        }
      }
    }

    GetCredenciaisResponse response = new GetCredenciaisResponse(credenciaisRetorno);
    return response;
  }

  public GetCredenciaisResponse getCredenciaisGrupo(
      String documento,
      Integer idProc,
      Integer idInst,
      Integer tipoPessoa,
      SecurityUserPortador userPortador) {
    List<Pessoa> pessoas = findPessoasNotNull(documento, idProc, idInst, tipoPessoa);

    List<GetCredencial> credenciaisRetorno = new ArrayList<>();
    for (Pessoa p : pessoas) {
      List<Long> idsContasPessoa = contaPagamentoService.obterIdContasDoPortador(userPortador);
      Set<Credencial> credenciaisContas =
          credencialRepository.findCredenciaisByIdsContas(idsContasPessoa);

      for (Credencial c : credenciaisContas) {
        ContaPagamento contaPagamento = contaPagamentoService.buscarPorContaId(c.getIdConta());
        credenciaisRetorno.add(loadPlastico(c, contaPagamento, p, idProc, idInst));
      }
    }

    HashSet<Object> seen = new HashSet<>();
    credenciaisRetorno.removeIf(e -> !seen.add(e.getIdCredencial()));

    return new GetCredenciaisResponse(credenciaisRetorno);
  }

  public List<CredencialContaResponse> getCredenciaisContaGrupo(
      SecurityUserPortador userPortador, Optional<Boolean> virtualOuFisicaOptional) {
    List<Pessoa> pessoas =
        findPessoasNotNull(
            userPortador.getCpf(),
            userPortador.getIdProcessadora(),
            userPortador.getIdInstituicao(),
            userPortador.getIdTipoPessoa());
    HashMap<Long, CredencialContaResponse> mapCredenciaisConta = new HashMap<>();

    List<Long> idsContasPessoa = contaPagamentoService.obterIdContasDoPortador(userPortador);
    if (idsContasPessoa.isEmpty()) {
      throw new GenericServiceException("Não foram encontradas contas para este portador");
    }
    List<CredencialConta> credenciaisConta =
        credencialContaService.findCredencialContaByListaContas(idsContasPessoa);

    // Para tipo login corporativo retornar somente a credencial igual ao documentoAcesso.
    if (userPortador != null
        && userPortador.getTipoLoginEnum() != null
        && userPortador
            .getTipoLoginEnum()
            .getRegraTipoPortadorLoginEnum()
            .equals(RegraTipoPortadorEnum.CORPORATIVO)) {
      credenciaisConta =
          credenciaisConta.stream()
              .filter(
                  credencial -> {
                    String numeroCompleto =
                        getNumeroCredencialEmClaro(credencial.getCredencial().getIdCredencial());
                    String documentoAcesso = userPortador.getDocumentoAcesso();

                    // Extrai os 4 primeiros e os 4 últimos dígitos do número completo
                    String quatroPrimeiros = numeroCompleto.substring(0, 4);
                    String quatroUltimos = numeroCompleto.substring(numeroCompleto.length() - 4);

                    // Compara com o documento de acesso
                    return (quatroPrimeiros + quatroUltimos).equals(documentoAcesso);
                  })
              .collect(Collectors.toList());
    }

    int[] statusIncludeList = new int[] {0, 1, 5};
    if (virtualOuFisicaOptional.isPresent()) {
      Boolean virtualOuFisica = virtualOuFisicaOptional.get();
      credenciaisConta =
          credenciaisConta.stream()
              .filter(
                  cc ->
                      ArrayUtils.contains(statusIncludeList, cc.getCredencial().getIdStatusV2())
                          && cc.getCredencial().getVirtual() == virtualOuFisica)
              .collect(Collectors.toList());
    } else {
      credenciaisConta =
          credenciaisConta.stream()
              .filter(
                  cc -> ArrayUtils.contains(statusIncludeList, cc.getCredencial().getIdStatusV2()))
              .collect(Collectors.toList());
    }

    for (CredencialConta cc : credenciaisConta) {

      if (Constantes.GRUPO_STATUS_CANCELADO.equals(
              cc.getContaPagamento().getTipoStatusV2().getIdGrupoStatus())
          || Constantes.GRUPO_STATUS_CANCELADO_PERMITINDO_AJUSTES.equals(
              cc.getContaPagamento().getTipoStatusV2().getIdGrupoStatus())) {
        continue;
      }

      Pessoa p =
          contaPessoaService.findByIdConta(cc.getContaPagamento().getIdConta()).get(0).getPessoa();
      if (!mapCredenciaisConta.containsKey(cc.getCredencial().getIdCredencial())) {
        mapCredenciaisConta.put(
            cc.getCredencial().getIdCredencial(),
            new CredencialContaResponse(
                loadPlastico(
                    cc.getCredencial(),
                    cc.getContaPagamento(),
                    p,
                    userPortador.getIdProcessadora(),
                    userPortador.getIdInstituicao()),
                new ArrayList<>()));
      }

      ContaPagamentoVO contaReduzida = new ContaPagamentoVO(cc.getContaPagamento());
      contaReduzida.setSaldoConta(
          contaPagamentoService.getSaldoConta(cc.getContaPagamento().getIdConta()));
      mapCredenciaisConta
          .get(cc.getCredencial().getIdCredencial())
          .getListaContaPagamentos()
          .add(contaReduzida);

      Integer perfilTarifario =
          perfilTarifarioService.buscarIdPerfilTarifarioMultiContas(
              cc.getContaPagamento().getIdConta());

      if (perfilTarifario != null) {
        List<PerfilTarifarioTransacao> perfilTarifarioTransacoes =
            perfilTarifarioTransacaoRepository.buscarPerfilTarifarioTransacaoPorIdPerfil(
                perfilTarifario);
        for (PerfilTarifarioTransacao perfil : perfilTarifarioTransacoes) {
          if (perfil.getCodigoTransacao().getCodTransacao() == Constantes.TARIFA_PRIMEIRA_VIA) {
            contaReduzida.setTarifaPrimeiraVia(perfil.getValorTarifa());
          } else if (perfil.getCodigoTransacao().getCodTransacao() == Constantes.TARIFA_REPOSICAO) {
            contaReduzida.setTarifaSegundaVia(perfil.getValorTarifa());
          }
        }
      }

      List<LogAlteracaoPinCredencial> credenciaisAlteracoes =
          logAlteracaoPinCredencialService.buscarAlteracaoPinPelaCredencial(
              cc.getCredencial().getIdCredencial());
      mapCredenciaisConta
          .get(cc.getCredencial().getIdCredencial())
          .getCredencial()
          .setSenhaAlterada(!credenciaisAlteracoes.isEmpty());
    }

    mapCredenciaisConta
        .values()
        .forEach(
            credResponse -> {
              credResponse
                  .getListaContaPagamentos()
                  .sort(
                      Comparator.comparing(
                          ContaPagamentoVO::getTipoProduto,
                          Comparator.comparing(TipoProdutoEnum::ordenarPorPrioridade)));
            });

    return mapCredenciaisConta.entrySet().stream()
        .sorted(Map.Entry.<Long, CredencialContaResponse>comparingByKey().reversed())
        .map(Map.Entry::getValue)
        .collect(Collectors.toList());
  }

  /**
   * Método responsável por buscar as credenciais físicas desblqueadas do portador
   *
   * @param documento
   * @param idProc
   * @param idInst
   * @param tipoPessoa
   * @return {@link GetCredenciaisResponse}
   */
  public GetCredenciaisResponse getCredenciaisDesbloqueadas(
      String documento,
      Integer idProc,
      Integer idInst,
      Integer tipoPessoa,
      SecurityUserPortador userPortador) {
    Collection<Integer> statusQueViraoNaPesquisa = new ArrayList<>();
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_DESBLOQUEADO);
    return getCredenciaisViaStatus(
        documento, idProc, idInst, tipoPessoa, statusQueViraoNaPesquisa, userPortador);
  }

  public GetCredenciaisResponse getCredenciaisDisponiveis(
      CredenciaisDisponiveisDTO credenciaisDisponiveisDTO, SecurityUserPortador userPortador) {
    Collection<Integer> statusQueViraoNaPesquisa = new ArrayList<>();
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_DESBLOQUEADO);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO);
    return getCredenciaisViaStatus(
        credenciaisDisponiveisDTO.getDocumento(),
        credenciaisDisponiveisDTO.getIdProc(),
        credenciaisDisponiveisDTO.getIdInst(),
        credenciaisDisponiveisDTO.getTipoPessoa(),
        statusQueViraoNaPesquisa,
        userPortador);
  }

  public GetCredenciaisResponse getCredenciaisDisponiveisEPerdaRoubo(
      String documento,
      Integer idProc,
      Integer idInst,
      Integer tipoPessoa,
      SecurityUserPortador userPortador) {
    Collection<Integer> statusQueViraoNaPesquisa = new ArrayList<>();
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_DESBLOQUEADO);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO);
    return getCredenciaisViaStatus(
        documento, idProc, idInst, tipoPessoa, statusQueViraoNaPesquisa, userPortador);
  }

  public GetCredenciaisResponse getCredenciaisDisponiveisParametrizado(
      String documento, Integer idProc, Integer idInst, Integer tipoPessoa, Integer acessoSite) {

    Collection<Integer> statusQueViraoNaPesquisa = new ArrayList<>();

    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_DESBLOQUEADO);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);
    statusQueViraoNaPesquisa.add(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO);

    return getCredenciaisViaStatusParametrizado(
        documento, idProc, idInst, tipoPessoa, acessoSite, statusQueViraoNaPesquisa);
  }

  private GetCredenciaisResponse getCredenciaisViaStatusParametrizado(
      String documento,
      Integer idProc,
      Integer idInst,
      Integer tipoPessoa,
      Integer acessoSite,
      Collection<Integer> statusQueViraoNaPesquisa) {

    List<Pessoa> pessoas = findPessoasNotNull(documento, idProc, idInst, tipoPessoa);

    List<GetCredencial> credenciaisRetorno = new ArrayList<>();
    for (Pessoa pessoa : pessoas) {

      List<ContaPessoa> contasPessoa = getContasPessoaNotNull(documento, idProc, idInst, pessoa);

      for (ContaPessoa cp : contasPessoa) {

        ContaPagamento contaPagamento = cp.getContaPagamento();

        List<Credencial> credenciais =
            credencialRepository.getCredenciaisDisponiveisParametrizadoPeloAcessoSiteOuNao(
                contaPagamento.getIdConta(),
                pessoa.getIdPessoa(),
                acessoSite,
                statusQueViraoNaPesquisa);

        credenciaisRetorno =
            loadPlasticos(credenciais, contaPagamento, pessoa, idProc, idInst, credenciaisRetorno);
      }
    }
    GetCredenciaisResponse response = new GetCredenciaisResponse(credenciaisRetorno);
    return response;
  }

  public GetCredenciaisResponse getCredenciaisViaStatus(
      String documento,
      Integer idProc,
      Integer idInst,
      Integer tipoPessoa,
      Collection<Integer> statusQueViraoNaPesquisa,
      SecurityUserPortador userPortador) {

    List<Pessoa> pessoas = findPessoasNotNull(documento, idProc, idInst, tipoPessoa);
    List<Credencial> credenciais = null;

    List<GetCredencial> credenciaisRetorno = new ArrayList<>();
    for (Pessoa p : pessoas) {
      List<ContaPessoa> contasPessoa = getContasPessoaNotNull(documento, idProc, idInst, p);

      for (ContaPessoa cp : contasPessoa) {

        ContaPagamento contaPagamento = cp.getContaPagamento();

        credenciais =
            findByIdContaAndIdPessoaAndVirtualAndStatusIn(
                contaPagamento.getIdConta(),
                p.getIdPessoa(),
                NAO_VIRTUAL,
                statusQueViraoNaPesquisa);

        if (credenciais.size() == 0) {
          credenciais =
              findByIdContaAndIdPessoaAndVirtualAndStatusIn(
                  contaPagamento.getIdConta(), p.getIdPessoa(), VIRTUAL, statusQueViraoNaPesquisa);
        }

        // Para tipo login corporativo retornar somente a credencial igual ao documentoAcesso.
        if (userPortador != null
            && userPortador.getTipoLoginEnum() != null
            && userPortador
                .getTipoLoginEnum()
                .getRegraTipoPortadorLoginEnum()
                .equals(RegraTipoPortadorEnum.CORPORATIVO)) {
          credenciais =
              credenciais.stream()
                  .filter(
                      credencial -> {
                        String numeroCompleto =
                            getNumeroCredencialEmClaro(credencial.getIdCredencial());
                        String documentoAcesso = userPortador.getDocumentoAcesso();

                        // Extrai os 4 primeiros e os 4 últimos dígitos do número completo
                        String quatroPrimeiros = numeroCompleto.substring(0, 4);
                        String quatroUltimos =
                            numeroCompleto.substring(numeroCompleto.length() - 4);

                        // Compara com o documento de acesso
                        return (quatroPrimeiros + quatroUltimos).equals(documentoAcesso);
                      })
                  .collect(Collectors.toList());
        }

        if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(idInst)
            && (!contaPagamento.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)
                || !contaPagamento.getIdStatusV2().equals(Constantes.TIPO_STATUS_DESBLOQUEADO))) {
          List<Credencial> crendenciasASair = new ArrayList<Credencial>();
          credenciais.forEach(
              credencial -> {
                if ((credencial.getMotivoEmissao().equals(1)
                        && credencial.getIdStatusV2().equals(0)
                        && !credencial.getCsn().equals(1))
                    || (contaPagamento
                            .getIdStatusV2()
                            .equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)
                        || !contaPagamento
                            .getIdStatusV2()
                            .equals(Constantes.TIPO_STATUS_DESBLOQUEADO))) {
                  crendenciasASair.add(credencial);
                }
              });
          crendenciasASair.forEach(credenciais::remove);
        }

        credenciaisRetorno =
            loadPlasticos(credenciais, contaPagamento, p, idProc, idInst, credenciaisRetorno);
      }
    }

    GetCredenciaisResponse response = new GetCredenciaisResponse(credenciaisRetorno);
    this.credencialUtil.setSaldoLimiteNasCredenciais(response);
    return response;
  }

  public List<GetCredencial> loadPlasticos(
      List<Credencial> credenciais,
      ContaPagamento contaPagamento,
      Pessoa p,
      Integer idProc,
      Integer idInst,
      List<GetCredencial> credenciaisRetorno) {
    for (Credencial credencial : credenciais) {
      credenciaisRetorno.add(loadPlastico(credencial, contaPagamento, p, idProc, idInst));
    }
    return credenciaisRetorno;
  }

  public GetCredencial loadPlastico(
      Credencial credencial,
      ContaPagamento contaPagamento,
      Pessoa p,
      Integer idProc,
      Integer idInst) {
    Integer idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
    ProdutoInstituicao produtoInstituicao =
        getProdutoInstituicaoNotNull(contaPagamento, idProdutoInstituicao);
    List<Plastico> plastico = getPlasticoNotNull(idProc, idInst, idProdutoInstituicao);
    return prepareGetCredencial(
        contaPagamento,
        credencial,
        produtoInstituicao,
        plastico.get(0),
        p.getNomeCompleto(),
        p.getEmail());
  }

  public List<Plastico> getPlasticoNotNull(
      Integer idProc, Integer idInst, Integer idProdutoInstituicao) {
    List<Plastico> plastico =
        plasticoRepository
            .findByIdProcessadoraAndIdInstituicaoAndIdProdutoInstituicaoAndDataCancelamentoEmissaoIsNull(
                idProc, idInst, idProdutoInstituicao);

    if (plastico.size() == 0) {
      throw new GenericServiceException(
          "Plastico não encontrado para informações enviadas. idProcessadora="
              + idProc
              + " , idInstituicao = "
              + idInst
              + " ,idProdutoInstituicao = "
              + idProdutoInstituicao);
    }
    return plastico;
  }

  public ProdutoInstituicao getProdutoInstituicaoNotNull(
      ContaPagamento contaPagamento, Integer idProdutoInstituicao) {
    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdInstituicao(),
            idProdutoInstituicao);
    if (produtoInstituicao == null) {
      throw new GenericServiceException(
          "ProdutoInstituicao não encontrado para informações enviadas. idProcessadora="
              + contaPagamento.getIdProcessadora()
              + " , idInstituicao = "
              + contaPagamento.getIdInstituicao()
              + " ,idProdutoInstituicao = "
              + idProdutoInstituicao);
    }
    return produtoInstituicao;
  }

  public List<ContaPessoa> getContasPessoaNotNull(
      String documento, Integer idProc, Integer idInst, Pessoa p) {
    List<ContaPessoa> contasPessoa = contaPessoaService.findByIdPessoa(p.getIdPessoa());

    if (contasPessoa == null) {
      throw new GenericServiceException(
          "Não existem contas para o portador. documento="
              + documento
              + " , processadora = "
              + idProc
              + " ,instituicao = "
              + idInst);
    }
    return contasPessoa;
  }

  public List<Pessoa> findPessoasNotNull(
      String documento, Integer idProc, Integer idInst, Integer tipoPessoa) {
    List<Pessoa> pessoas = findPessoas(documento, idProc, idInst, tipoPessoa);

    if (pessoas == null || pessoas.isEmpty()) {
      throw new GenericServiceException(
          "Portador não encontrado para informações enviadas. documento="
              + documento
              + " , processadora = "
              + idProc
              + " ,instituicao = "
              + idInst);
    }
    return pessoas;
  }

  public List<Pessoa> findPessoasNotNull(String documento, Integer idProc, Integer idInst) {
    List<Pessoa> pessoas = findPessoas(documento, idProc, idInst);

    if (pessoas == null || pessoas.isEmpty()) {
      throw new GenericServiceException(
          "Portador não encontrado para informações enviadas. documento="
              + documento
              + " , processadora = "
              + idProc
              + " ,instituicao = "
              + idInst);
    }
    return pessoas;
  }

  public Pessoa findPessoaNotNull(
      String documento, Integer idProc, Integer idInst, Integer tipoPessoa) {
    Pessoa p = findPessoa(documento, idProc, idInst, tipoPessoa);

    if (p == null) {
      throw new GenericServiceException(
          "Portador não encontrado para informações enviadas. documento="
              + documento
              + " , processadora = "
              + idProc
              + " ,instituicao = "
              + idInst);
    }
    return p;
  }

  private List<Credencial> findByIdContaAndIdPessoaAndVirtualAndStatusIn(
      Long idConta, Long idPessoa, Boolean virtual, Collection<Integer> statusNaPesquisa) {
    return credencialRepository.findByIdContaAndIdPessoaAndVirtualAndStatusIn(
        idConta, idPessoa, virtual, statusNaPesquisa);
  }

  public PortadorCredencial getInfoPortadorCredencialPortador(
      String panHash, SecurityUserPortador userPortador) {

    GetCardResponse card = cardService.getToken(panHash.toUpperCase());
    if (card == null || !card.getSuccess()) {
      throw new GenericServiceException("Credencial não encontrada.");
    }

    Credencial credencialBuscado = findOneByTokenInterno(card.getCard().getToken());
    if (credencialBuscado == null) {
      throw new GenericServiceException("Credencial não encontrada para informações enviadas.");
    }

    contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
        credencialBuscado.getIdConta(), userPortador);

    return getInfoPortadorCredencial(credencialBuscado);
  }

  /**
   * Método responsável por buscar informações de uma credencial de um portador
   *
   * @param credencial
   * @return {@link PortadorCredencial}
   */
  public PortadorCredencial getInfoPortadorCredencial(Credencial credencial) {

    Pessoa p = getPessoaByIdNotNull(credencial);

    PortadorCredencial pc = new PortadorCredencial();
    pc.setNome(credencial.getNomeImpresso());
    pc.setDocumento(p.getDocumento());

    BigDecimal saldo;

    GetSaldoConta getSaldo = contaPagamentoService.getSaldoConta(credencial.getIdConta());

    saldo = getSaldo.getSaldoDisponivel();

    saldo = saldo == null ? SALDO_PADRAO : saldo;
    pc.setSaldoDisponivel(saldo.doubleValue());
    pc.setEmail(p.getEmail());
    return pc;
  }

  public List<Credencial> findByDataHoraInclusaoGreaterThan(LocalDateTime dataInicial) {
    return credencialRepository.findByDataHoraInclusaoGreaterThan(dataInicial);
  }

  /**
   * Método responsável por buscar o extrato por periodo de uma credencial
   *
   * @param idCredencial
   * @param periodo
   * @return
   */
  public List<GetExtratoCredencial> getExtrato(Long idCredencial, Integer periodo) {

    Credencial credencial = findById(idCredencial);

    if (credencial == null) {
      throw new GenericServiceException(
          "Credencial não encontrada para informações enviadas. idCredencial=" + idCredencial);
    }
    LocalDateTime dataInicialDT = getDataInicial(periodo);

    String dataInicio =
        DateUtil.dateFormat("yyyy-MM-dd", DateUtil.localDateTimeToDate(dataInicialDT));
    String dataFim = DateUtil.dateFormat("yyyy-MM-dd", new Date());

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(credencial.getIdConta());

    // busca o valor de ponto para a instituição, para posteriormente fazer o calculo para obter o
    // valor er reais atraves do valor em pontos
    CotacaoPontos cotacaoPontos =
        cotacaoPontosRepository.findByIdInstituicao(conta.getIdInstituicao());

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    String accountCode =
        contaPagamentoService.getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();

    StringBuilder sb = new StringBuilder();
    sb.append(moeda + ",1" + moeda);
    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != null
        && produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == CREDITO) {}
    moeda = sb.toString();

    List<GetExtratoCredencial> extrato = new ArrayList<>();

    if (verificaBRB(conta, accountCode)) {
      Date date = DateUtil.parseDate("yyyy-MM-dd", "2018-06-01");
      Date dataInicial = DateUtil.parseDate("yyyy-MM-dd", dataInicio);
      Date dataFinal = DateUtil.parseDate("yyyy-MM-dd", dataFim);

      if (dataInicial.before(date)) {

        extrato =
            contaPagamentoService.buscarExtratoMigracaoLancamentoBRB(
                conta.getIdContaExterna(), dataInicial, dataFinal);
      }
    }

    AccountStatementResponse asr =
        contaPagamentoService.getAccountStatement(
            conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moeda,
            dataInicio,
            dataFim);

    extrato.addAll(
        contaPagamentoService.prepareGetExtratoCredencial(
            asr.getEntries(), conta.getIdInstituicao(), conta.getIdConta()));

    // para obter valor em reais a partir do valor da transação em pontos RF20210607
    if (cotacaoPontos != null) {
      if (extrato.size() > 0) {
        for (GetExtratoCredencial getExtratoCredencial : extrato) {
          if (getExtratoCredencial.getValorTransacao() == null) {
            getExtratoCredencial.setValorTransacao(new BigDecimal(0));
            getExtratoCredencial.setValorPontosEmReais(new BigDecimal(0));
          } else {
            getExtratoCredencial.setValorPontosEmReais(
                getExtratoCredencial.getValorTransacao().divide(cotacaoPontos.getValorConversao()));
          }
        }
      }
    }

    return extrato;
  }

  private boolean verificaBRB(ContaPagamento conta, String accountCode) {
    return accountCode != null
        && conta.getIdInstituicao() != null
        && Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(conta.getIdInstituicao());
  }

  private LocalDateTime getDataInicial(Integer periodo) {
    LocalDateTime dataInicialDT = LocalDateTime.now();
    dataInicialDT = dataInicialDT.withHour(MIN_HORA);
    dataInicialDT = dataInicialDT.withMinute(MIN_MINUTO);
    dataInicialDT = dataInicialDT.withSecond(MIN_SEGUNDOS);
    dataInicialDT = dataInicialDT.withNano(MIN_SEGUNDOS);

    dataInicialDT = dataInicialDT.minusDays(new Long(periodo) - HOJE);
    return dataInicialDT;
  }

  /**
   * Método responsável por buscar extrato de uma credencial pela data inicial e final
   *
   * @param idCredencial
   * @param dataInicial
   * @param dataFinal
   * @return {@link List<GetExtratoCredencial>}
   */
  public List<GetExtratoCredencial> getExtrato(
      Long idCredencial, Date dataInicial, Date dataFinal, Long idConta) {

    Credencial credencial = getCredencialNotNull(idCredencial);

    String dataInicio = DateUtil.dateFormat("yyyy-MM-dd", dataInicial);
    String dataFim = DateUtil.dateFormat("yyyy-MM-dd", dataFinal);

    ContaPagamento conta = null;
    if (idConta != null) {
      conta = contaPagamentoService.findByIdNotNull(idConta);
    } else {
      conta = contaPagamentoService.findByIdNotNull(credencial.getIdConta());
    }

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    String accountCode =
        contaPagamentoService.getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();

    StringBuilder sb = new StringBuilder();
    sb.append(moeda + ",1" + moeda);
    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != null
        && produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == CREDITO) {}
    moeda = sb.toString();

    List<GetExtratoCredencial> extrato = new ArrayList<>();

    if (verificaBRB(conta, accountCode)) {
      Date date = DateUtil.parseDate("yyyy-MM-dd", "2018-06-01");

      if (dataInicial.before(date)) {

        extrato =
            contaPagamentoService.buscarExtratoMigracaoLancamentoBRB(
                conta.getIdContaExterna(), dataInicial, dataFinal);
      }
    }

    AccountStatementResponse asr =
        contaPagamentoService.getAccountStatement(
            conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moeda,
            dataInicio,
            dataFim);

    extrato.addAll(
        contaPagamentoService.prepareGetExtratoCredencial(
            asr.getEntries(), conta.getIdInstituicao(), conta.getIdConta()));

    // busca o valor de ponto para a instituição, para posteriormente fazer o calculo para obter o
    // valor er reais atraves do valor em pontos
    CotacaoPontos cotacaoPontos =
        cotacaoPontosRepository.findByIdInstituicao(conta.getIdInstituicao());

    if (cotacaoPontos != null) {
      // para obter valor em reais a partir do valor da transação em pontos RF20210607
      if (extrato.size() > 0) {
        for (GetExtratoCredencial getExtratoCredencial : extrato) {
          getExtratoCredencial.setValordoPonto(cotacaoPontos.getValorPonto());
          getExtratoCredencial.setValorConversao(cotacaoPontos.getValorConversao());
          if (getExtratoCredencial.getValorTransacao() == null) {
            getExtratoCredencial.setValorTransacao(new BigDecimal(0));
            getExtratoCredencial.setValorPontosEmReais(new BigDecimal(0));
          } else {
            getExtratoCredencial.setValorPontosEmReais(
                getExtratoCredencial.getValorTransacao().divide(cotacaoPontos.getValorConversao()));
          }
        }
      }
    }

    return extrato;
  }

  /**
   * Método responsável por detalhar uma credencial
   *
   * @param idCredencial
   * @return {@link GetCredencial}
   */
  public GetCredencial detalhe(Long idCredencial) {

    Credencial credencial = getCredencialNotNull(idCredencial);
    ContaPagamento contaPagamento = getContaPagamentoByIdNotNull(credencial.getIdConta());

    Integer idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
    ProdutoInstituicao produtoInstituicao =
        getProdutoInstituicaoNotNull(contaPagamento, idProdutoInstituicao);

    List<Plastico> plastico =
        plasticoRepository
            .findByIdProcessadoraAndIdInstituicaoAndIdProdutoInstituicaoAndDataCancelamentoEmissaoIsNull(
                contaPagamento.getIdProcessadora(),
                contaPagamento.getIdInstituicao(),
                idProdutoInstituicao);

    if (plastico == null) {
      throw new GenericServiceException(
          "Plastico não encontrado para informações enviadas. idProcessadora = "
              + contaPagamento.getIdProcessadora()
              + " , idInstituicao = "
              + contaPagamento.getIdInstituicao()
              + " ,idProdutoInstituicao = "
              + idProdutoInstituicao);
    }

    Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());

    if (pessoa == null) {
      throw new GenericServiceException(
          "Pessoa não encontrada para informações enviadas. idPesoa = " + credencial.getIdPessoa());
    }

    return prepareGetCredencial(
        contaPagamento,
        credencial,
        produtoInstituicao,
        plastico.get(0),
        pessoa.getNomeCompleto(),
        pessoa.getEmail());
  }

  private GetCredencial prepareGetCredencial(
      ContaPagamento contaPagamento,
      Credencial credencial,
      ProdutoInstituicao produtoInstituicao,
      Plastico plastico,
      String nome,
      String email) {
    GetCredencial c = new GetCredencial();

    String credencialSemMascara =
        credencial.getBin6()
            + Strings.padStart(
                credencial.getUltimos4Digitos().toString(),
                CredencialService.QUATRO_DIGITOS,
                CredencialService.CARACTER_ZERO);

    ProdutoContratado produtoContratado =
        produtoContratadoRepository.buscarProdutoCadastradoPorCredencial(
            credencial.getIdCredencial());

    c.setCredencialMascarada(
        CredencialService.getCredencialMascarada(
            credencialSemMascara,
            CredencialService.PADRAO_CREDENCIAL,
            CredencialService.MASK_2_PARTES_COMPLETA));
    c.setCredencialMascaradaReduzida(
        CredencialService.getCredencialMascarada(
            credencialSemMascara,
            CredencialService.PADRAO_CREDENCIAL,
            CredencialService.MASK_2_PARTES));
    c.setCredencialUltimosDigitos(
        Strings.padStart(
            credencial.getUltimos4Digitos().toString(),
            CredencialService.QUATRO_DIGITOS,
            CredencialService.CARACTER_ZERO));

    c.setIdCredencial(credencial.getIdCredencial());
    c.setDataValidade(credencial.getDataValidade());
    c.setIdProduto(contaPagamento.getIdProdutoInstituicao());
    c.setDataHoraInclusao(credencial.getDataHoraInclusao());
    c.setNomeProduto(produtoInstituicao.getDescProdInstituicao());
    c.setPreparaDataSaldo(new Date());
    c.setDataSaldo(c.getDataSaldo());
    c.setIdProdutoInstituicao(produtoInstituicao.getIdProdInstituicao());

    c.setNomeCompleto(nome);

    // 1-pre-pago;2-pos-pago
    c.setTipoConta(contaPagamento.getIdRelacionamento());
    c.setUrlImagemProduto(plastico.getUrlImagemProduto());
    c.setIdConta(credencial.getIdConta());
    c.setIdPessoa(credencial.getIdPessoa());
    c.setContaPagamento(contaPagamento.getIdContaPagamento());
    String dataValidadeFmt =
        DateUtil.dateFormat(FMT_MM_YY, DateUtil.localDateTimeToDate(credencial.getDataValidade()));
    c.setDataValidadeFmt(dataValidadeFmt);
    c.setNomeImpresso(credencial.getNomeImpresso());
    c.setStatusNfc(credencial.getStatusNfc());
    c.setDtHrStatusNfc(credencial.getDtHrStatusNfc());

    c.setStatusConta(contaPagamento.getIdStatusV2());
    TipoStatus tipoStatusConta =
        contaPagamento.getTipoStatusV2() == null
            ? tipoStatusService.findById(contaPagamento.getIdStatusV2())
            : contaPagamento.getTipoStatusV2();
    c.setDescStatusConta(tipoStatusConta.getDescStatus());
    c.setMetodoSegurancaTransacao(
        contaPagamentoService.buscaMetodoSegurancaTransacaoConta(contaPagamento));

    if (produtoContratado != null) {
      c.setPermiteCartaoFisico(produtoContratado.getPermiteCartaoFisico());
    }

    c.setStatus(credencial.getIdStatusV2());
    TipoStatus tipoStatus =
        credencial.getTipoStatusV2() == null
            ? tipoStatusService.findById(credencial.getIdStatusV2())
            : credencial.getTipoStatusV2();
    c.setGrupoStatus(tipoStatus.getIdGrupoStatus());
    TipoGrupoStatus tipoGrupoStatus = tipoStatus.getTipoGrupoStatus();
    c.setDescGrupoStatus(tipoGrupoStatus.getDescGrupoStatus());
    c.setDescStatus(tipoStatus.getDescStatus());
    c.setIdCredencialExterna(credencial.getIdCredencialExterna());
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            produtoInstituicao.getIdProcessadora(),
            produtoInstituicao.getIdProdInstituicao(),
            produtoInstituicao.getIdInstituicao());

    if (produtoInstituicaoConfiguracao != null) {
      c.setIdProdutoPlataforma(produtoInstituicaoConfiguracao.getIdProdutoPlataforma());

      if (produtoInstituicaoConfiguracao.getIdProdutoPlataforma().equals(ID_PROD_PLAT_PRE_PAGO)) {
        c.setValorMaxCarga(produtoInstituicaoConfiguracao.getValorCargaMax());
        c.setValorMinCarga(produtoInstituicaoConfiguracao.getValorCargaMin());
      }
      c.setMesesValidadeCartaoVitual(produtoInstituicaoConfiguracao.getMesesValidadeMaxVirtual());
    }

    c.setVirtual(credencial.getVirtual());
    if (credencial.getVirtual()) {

      GetCardResponse response = cardService.getCard(credencial.getTokenInterno());

      if (!response.getSuccess()) {
        throw new GenericServiceException(
            "Credencial não encontrada. Token: " + credencial.getTokenInterno());
      }

      c.setCredencialVirtual(response.getCard().getPan());
      c.setCodigoSeguranca(response.getCard().getCvv2());
    }

    c.setIdPlastico(plastico.getIdPlastico());

    c.setEmail(email);

    ContaPagamentoFatura contaPagamentoFat =
        contaPagamentoFaturaRepository.findByIdConta(contaPagamento.getIdConta());

    ProdutoInstituicaoConfiguracaoCredito prodInstituicaoConfiguracaoCredito =
        produtoInstituicaoConfiguracaoCreditoRepository
            .findByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
                contaPagamento.getIdProcessadora(),
                contaPagamento.getIdInstituicao(),
                contaPagamento.getIdProdutoInstituicao());

    if (contaPagamentoFat != null && contaPagamentoFat.getEnvioSuspensoFaturaImpressa() != null) {
      c.setFaturaInibida(contaPagamentoFat.getEnvioSuspensoFaturaImpressa() == 1);
    } else {
      c.setFaturaInibida(false);
    }

    if (prodInstituicaoConfiguracaoCredito != null
        && prodInstituicaoConfiguracaoCredito.getFaturaImpressa() != null
        && prodInstituicaoConfiguracaoCredito.getPortadorSuspendeFaturaImpressa() != null) {
      c.setPermiteInibirFatura(
          prodInstituicaoConfiguracaoCredito.getFaturaImpressa() == 1
              && prodInstituicaoConfiguracaoCredito.getPortadorSuspendeFaturaImpressa() == 1);
    } else {
      c.setPermiteInibirFatura(false);
    }

    c.setB2b(contaPagamento.getHierarquiaPontoDeRelacionamento().getB2b());

    HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento =
        hierarquiaPontoRelacionamentoService.getPontoRelacPorChave(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdInstituicao(),
            contaPagamento.getIdRegional(),
            contaPagamento.getIdFilial(),
            contaPagamento.getIdPontoDeRelacionamento());
    if (hierarquiaPontoDeRelacionamento != null) {
      c.setDescPontoRelacionamento(hierarquiaPontoDeRelacionamento.getDescricao());
    }
    c.setPontoRelacionamento(contaPagamento.getIdPontoDeRelacionamento());
    c.setDataHoraEmitido(credencial.getDataHoraEmitido());
    c.setBin(credencial.getBinEstendido());

    return c;
  }

  /**
   * Método responsável por validar pin de uma Credencial
   *
   * @param pin
   * @param idCredencial
   * @return true se for senha valida e false caso seja senha inválida
   */
  public boolean validarPin(String pin, Long idCredencial, String tokenJWT) {
    Credencial credencial = getCredencialNotNull(idCredencial);
    return validarPin(pin, credencial, tokenJWT);
  }

  public Map<String, Object> validarSenha(String pin, Long idCredencial, String tokenJWT) {
    HashMap<String, Object> map = new HashMap<>();

    Credencial credencial = getCredencialNotNull(idCredencial);

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encontrada para informações enviadas.");
    }

    CheckPinRequest request = new CheckPinRequest();
    request.setHashpin(pin);
    request.setSalt(tokenJWT);
    request.setToken(credencial.getTokenInterno());

    JcardResponse response = cardService.checkPin(request);

    map.put("sucesso", response.getSuccess());
    if (!response.getSuccess()) {
      String msg = "";
      Gson g = new Gson();
      ErrorJcard e = g.fromJson(response.getErrors(), ErrorJcard.class);
      if (e.getErrors().equals("Wrong Pin")) {
        msg = "Senha do cartão inválida.";
      } else if (e.getErrors().contains("limit.incorrect.pin Invalid PINBLOCK")) {
        msg = "Senha bloqueada. Contate instituição.";
      } else {
        msg = e.getErrors();
      }
      map.put("erro", msg);
    } else {
      registroValidacaoSenhaService.registraValidacaoBemSucedida(idCredencial);
    }
    return map;
  }

  public Boolean validarPin(String pin, Credencial credencial, String tokenJWT) {

    if (credencial == null) {
      throw new GenericServiceException(
          "Credencial não encontrada para informações enviadas.", HttpStatus.NOT_FOUND);
    }

    CheckPinRequest request = new CheckPinRequest();
    request.setHashpin(pin);
    request.setSalt(tokenJWT);
    request.setToken(credencial.getTokenInterno());

    JcardResponse response = cardService.checkPin(request);

    if (!response.getSuccess()) {
      log.error(response.getErrors());
    } else {
      registroValidacaoSenhaService.registraValidacaoBemSucedida(credencial.getIdCredencial());
    }

    return response.getSuccess();
  }

  private void atualizaSenhaJcard(String novaSenha, String tokenJWT, Credencial credencial) {

    UpdatePinRequest request = new UpdatePinRequest();
    request.setNewhashpin(novaSenha);
    request.setSalt(tokenJWT);
    request.setToken(credencial.getTokenInterno());

    JcardResponse response = cardService.updatePin(request);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível trocar senha da Credencial.", response.getErrors());
    }
  }

  private Credencial atualizaSenhaPlataforma(
      String novaSenha,
      Integer idUsuario,
      Credencial credencial,
      Boolean atualizaDataHoraSenhaResgate) {
    logAlteracaoPinCredencialService.registraAlteracaoDeSenha(credencial.getIdCredencial());
    /* Removido setPin(), pois o PIN da plataforma não é utilizado
     * Sempre que é necessário checar ou atualizar um PIN de credencial, esta ação é responsabilidade do JCard,
     * o que define uma falha de segurança manter as informações no banco de dados da Plataforma.
     */
    //		credencial.setPin(UtilController.encodeSenhaSHA256(novaSenha));
    credencial.setIdUsuarioManutencao(idUsuario);
    if (atualizaDataHoraSenhaResgate) {
      credencial.setDtHrAlteracaoSenhaResgate(LocalDateTime.now());
    }
    credencial = save(credencial);
    return credencial;
  }

  /**
   * Método responsável por validar pin de uma Credencial
   *
   * @param pin
   * @param idCredencial
   * @return true se for senha valida e false caso seja senha inválida
   */
  /*
   * public Boolean validarPin(String pin, Long idCredencial) { Credencial
   * credencial = getCredencialNotNull(idCredencial);
   *
   * if (!credencial.getPin().equals(pin)) { throw new
   * GenericServiceException(
   * "Senha não confere com a senha atual da credencial", " idCredencial: " +
   * idCredencial);
   *
   * }
   *
   * return true; }
   */

  /**
   * Método responśavel por trocar um pin de uma credencial
   *
   * @param senha
   * @param novaSenha
   * @param idCredencial
   * @return se conseguir trocar pin retorna true
   */
  public Boolean trocarPin(
      String senha, String novaSenha, Long idCredencial, Integer idUsuario, String tokenJWT) {
    Credencial credencial = null;

    if (!validarPin(senha, idCredencial, tokenJWT)) {
      throw new GenericServiceException(
          "Não foi possível trocar senha da Credencial. Senha inválida.", "Senha Inválida");
    }

    credencial = findById(idCredencial);

    // Integer tamanhoPin =
    // prodInstConfigService.findByIdConta(credencial.getIdConta()).getTamanhoPin();
    ContaPagamento conta = contaPagamentoService.findByIdNotNull(credencial.getIdConta());
    if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      Pessoa pessoa = pessoaService.findPessoaByIdConta(conta.getIdConta());
      Long idValidacao =
          registroValidacaoFacialCafService.checaValidacao(
              pessoa.getDocumento(),
              pessoa.getIdInstituicao(),
              AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_CARTAO);
      if (idValidacao == null) {
        throw new GenericServiceException(
            "Não foi possível reconhecer a validação facial. Por favor, tente novamente!",
            HttpStatus.FORBIDDEN);
      }
      List<Credencial> credenciais = findByIdContaInCredencialConta(conta.getIdConta());
      for (Credencial cred : credenciais) {
        atualizaSenhaJcard(novaSenha, tokenJWT, cred);
        cred = atualizaSenhaPlataforma(novaSenha, idUsuario, cred, false);
      }
      registroValidacaoFacialCafService.efetivaValidacao(idValidacao);
    } else {
      atualizaSenhaJcard(novaSenha, tokenJWT, credencial);
      credencial = atualizaSenhaPlataforma(novaSenha, idUsuario, credencial, false);
    }

    return credencial != null;
  }

  /**
   * Método responśavel por atualizar um pin de uma credencial
   *
   * @param novaSenha
   * @param credencial
   * @return se conseguir trocar pin retorna true
   */
  public Boolean atualizarPin(
      String novaSenha, Credencial credencial, String tokenJWT, String documento) {
    ContaPagamento conta = contaPagamentoService.findByIdNotNull(credencial.getIdConta());
    if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      Long idValidacao =
          registroValidacaoFacialCafService.checaValidacao(
              documento,
              conta.getIdInstituicao(),
              AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_CARTAO,
              AntifraudeCafFacialObjetivosEnum.DESBLOQUEAR_CARTAO_VIRTUAL);
      if (idValidacao == null) {
        throw new GenericServiceException(
            "Não foi possível reconhecer a validação facial. Por favor, tente novamente!",
            HttpStatus.FORBIDDEN);
      }
      List<Credencial> credenciais = findByIdContaInCredencialConta(conta.getIdConta());
      for (Credencial cred : credenciais) {
        atualizaSenhaJcard(novaSenha, tokenJWT, cred);
        atualizaSenhaPlataforma(novaSenha, Constantes.ID_USUARIO_INCLUSAO_PORTADOR, cred, true);
      }
      registroValidacaoFacialCafService.efetivaValidacao(idValidacao);
    } else {
      atualizaSenhaJcard(novaSenha, tokenJWT, credencial);
      credencial =
          atualizaSenhaPlataforma(
              novaSenha, Constantes.ID_USUARIO_INCLUSAO_PORTADOR, credencial, true);
    }

    return credencial != null;
  }

  @Transactional
  public Boolean habilitarUsoCredencialUser(TrocarEstadoCredencialRequest req, SecurityUser user) {
    Credencial credencial = getCredencialNotNull(req.getIdCredencial());
    ContaPagamento conta = getContaPagamentoByIdNotNull(credencial.getIdConta());

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, conta);

    return habilitarUsoCredencial(credencial, user.getIdUsuario());
  }

  @Transactional
  public Boolean habilitarUsoCredencialPortador(
      TrocarEstadoCredencialRequest req, SecurityUserPortador userPortador) {
    Credencial credencial = getCredencialNotNull(req.getIdCredencial());

    contaPagamentoService.validaIdContaPeloRequestEPortador(credencial.getIdConta(), userPortador);

    return habilitarUsoCredencial(credencial, USUARIO_PORTADOR);
  }

  /**
   * Método responsável por habilitar o uso de uma credencial(caso seja possível)
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir habilitar credencial retorna true
   */
  @Transactional(rollbackFor = GenericServiceException.class)
  public Boolean habilitarUsoCredencial(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    return habilitarUsoCredencial(credencial, idUsuario);
  }

  /**
   * Método responsável por habilitar o uso de uma credencial(caso seja possível)
   *
   * @param idUsuario
   * @return se conseguir habilitar credencial retorna true
   */
  @Transactional(rollbackFor = GenericServiceException.class)
  public Boolean habilitarUsoCredencial(Credencial credencial, Integer idUsuario) {

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encontrada");
    }

    credencial.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);
    credencial.setDataHoraStatus(LocalDateTime.now());
    credencial.setHabilitaUsoPessoa(HABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    unblockCard(credencial);

    return credencial.getHabilitaUsoPessoa().equals(HABILITADO);
  }

  @Transactional(rollbackFor = GenericServiceException.class)
  public void unblockCardByIdLoteEmissaoB2b(Integer idLoteEmissao, SecurityUser user) {
    List<Credencial> credenciais = findByIdLoteEmissao(idLoteEmissao);
    for (Credencial credencial : credenciais) {
      if (credencial.getTipoStatusV2().getTipoGrupoStatus().getPermiteDesbloquear()) {
        if (user.getTipoB2B() != null
            && Constantes.TIPO_B2B_GENERICO.equals(user.getTipoB2B())
            && verificaEmissaoCredenciaisDesbloqueio(credencial)) {
          unblockCard(credencial);
          alterarStatusCredencial(
              credencial.getIdCredencial(),
              Constantes.TIPO_STATUS_DESBLOQUEADO,
              user.getIdUsuario(),
              false);
        } else {
          throw new GenericServiceException(
              "Não é permitido desbloquear lotes por usuários do portal gestor. Execute esta ação a partir do portal gestor. Se este não for seu caso, entre em contato com seu atendente");
        }
      } else {
        throw new GenericServiceException(
            "Não é permitido desbloquear lotes por usuários do portal gestor. Execute esta ação a partir do portal gestor. Se este não for seu caso, entre em contato com seu atendente");
      }
    }
  }

  @Transactional(rollbackFor = GenericServiceException.class)
  public void unblockCardByIdLoteEmissao(Integer idLoteEmissao, SecurityUser user) {
    List<Credencial> credenciais = findByIdLoteEmissao(idLoteEmissao);
    for (Credencial credencial : credenciais) {
      if (credencial.getTipoStatusV2().getTipoGrupoStatus().getPermiteDesbloquear()) {
        unblockCard(credencial);
        alterarStatusCredencial(
            credencial.getIdCredencial(),
            Constantes.TIPO_STATUS_DESBLOQUEADO,
            user.getIdUsuario(),
            false);
      }
    }
  }

  private boolean verificaEmissaoCredenciaisDesbloqueio(Credencial credencial) {
    if (credencial.getDataHoraEmitido() != null) {
      Date hoje = new Date();
      Date emissao = DateUtil.localDateTimeToDate(credencial.getDataHoraEmitido());
      Integer diferenca = DateUtil.differenceDates(hoje, emissao);
      if (diferenca <= 5) {
        throw new GenericServiceException(
            "Não é permitido o desbloqueio, pois o mesmo encontra-se em processo de emissão");
      } else {
        return true;
      }
    } else {
      throw new GenericServiceException(
          "Não é permitido o desbloqueio, pois o mesmo encontra-se em processo de emissão");
    }
  }

  /**
   * método responsável por desbloquear uma credencial no jcard
   *
   * @param credencial
   */
  @Transactional
  public void unblockCard(Credencial credencial) {
    JcardResponse unblockCard = cardService.unblockCard(credencial.getTokenInterno());

    if (!unblockCard.getSuccess()) {
      throw new GenericServiceException("Não foi possível realizar Unblock.");
    }
    ContaPagamento c = contaPagamentoService.buscarPorContaId(credencial.getIdConta());
    // ContaPagamento contaPagamento =
    // contaPagamentoService.findByIdNotNull(Long.parseLong(credencial.getIdContaPagamento()));
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(c.getIdInstituicao())
        && credencial.getCsn() > 1) {
      Pessoa p = pessoaService.findPessoaByIdConta(credencial.getIdConta());
      emailService.sendEmailUnBlockCard(
          p.getEmail(), p.getNomeCompleto(), credencial.getUltimos4Digitos());
    }
  }

  /**
   * Método responsável por buscar as credenciais de um lote
   *
   * @param idLoteEmissao
   * @return
   */
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public List<Credencial> findByIdLoteEmissao(Integer idLoteEmissao) {
    return credencialRepository.findByIdLoteEmissao(idLoteEmissao);
  }

  /**
   * Método responsável por buscar as credencias de um lote e populando uma classe wrapper
   *
   * @param idLoteEmissao
   * @return
   */
  @Transactional(propagation = Propagation.NOT_SUPPORTED)
  public List<CredencialLoteResponse> findCredencialLoteResponseByIdLoteEmissao(
      Integer idLoteEmissao) {
    List<CredencialLoteResponse> credenciais = new ArrayList<>();

    for (Credencial credencial : findByIdLoteEmissao(idLoteEmissao)) {
      credenciais.add(new CredencialLoteResponse(credencial));
    }

    return credenciais;
  }

  /**
   * Método responsável por bloquear uma credencial temporariamente
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir bloquear a credencial temporariamente retorna true
   */
  @Transactional(rollbackFor = GenericServiceException.class)
  public Boolean bloquearTemporariamente(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setIdStatusV2(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO);
    credencial.setDataHoraStatus(LocalDateTime.now());
    credencial.setHabilitaUsoPessoa(DESABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);

    credencial = save(credencial);
    blockCard(credencial);

    return credencial.getHabilitaUsoPessoa().equals(DESABILITADO);
  }

  /**
   * Método responsável por bloquear uma credencial temporariamente
   *
   * @param idCredencial
   * @return se conseguir bloquear a credencial temporariamente retorna true
   */
  @Transactional(rollbackFor = GenericServiceException.class)
  public Boolean bloquearSenhaResgatePontos(Long idCredencial) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setDtBloqueioResgatePontos(LocalDateTime.now());

    credencial = save(credencial);

    return credencial.getDtBloqueioResgatePontos() != null;
  }

  /**
   * Método responsável por verificar se uma credencial esta bloqueada
   *
   * @param credencial
   * @return se bloqueada retorna true, se não false
   */
  @Transactional(rollbackFor = GenericServiceException.class)
  public Boolean isCredencialBloqueada(Credencial credencial) {
    return credencial.getDtBloqueioResgatePontos() != null;
  }

  /**
   * Método responsável por bloquear uma credencial temporariamente
   *
   * @param idCredencial
   * @return se conseguir bloquear a credencial temporariamente retorna true
   */
  @Transactional(rollbackFor = GenericServiceException.class)
  public Boolean desbloquearSenhaResgatePontos(Long idCredencial) {
    Credencial credencial = getCredencialNotNull(idCredencial);
    credencial.setDtBloqueioResgatePontos(null);
    credencial.setQtdTentativasResgateSenha(0);
    credencial.setPin("03ac674216f3e15c761ee1a5e255f067953623c8b388b4459e13f978d7c846f4");

    credencial = save(credencial);

    List<PerguntaVerificacaoCredencial> perguntas =
        perguntaVerificacaoUsuarioRepository.findByIdIdCredencialAndStatus(
            credencial.getIdCredencial(), 1);
    for (PerguntaVerificacaoCredencial perguntaVerificacaoCredencial : perguntas) {
      perguntaVerificacaoCredencial.setStatus(0);
      perguntaVerificacaoUsuarioRepository.save(perguntaVerificacaoCredencial);
    }

    perguntaVerificacaoTentativaCredencialService.removeTentativasInvalidas(idCredencial);

    return credencial.getDtBloqueioResgatePontos() == null;
  }

  @Transactional
  public void blockCard(Credencial credencial) {
    JcardResponse response = cardService.blockCard(credencial.getTokenInterno());

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possivel realizar BlockCard. " + response.getErrors());
    }

    ContaPagamento c = contaPagamentoService.buscarPorContaId(credencial.getIdConta());

    // ContaPagamento contaPagamento =
    // contaPagamentoService.findByIdNotNull(Long.parseLong(credencial.getIdContaPagamento()));
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(c.getIdInstituicao())
        && credencial.getCsn() > 1) {
      sendEmailblockCard(credencial);
    }
  }

  @Transactional
  public void sendEmailblockCard(Credencial credencial) {
    Pessoa p = pessoaService.findPessoaByIdConta(credencial.getIdConta());
    emailService.sendEmailBlockCard(
        p.getEmail(), p.getNomeCompleto(), credencial.getUltimos4Digitos());
  }

  /**
   * Método responsável por desabilitar credencial para uso no exterior
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir desabilitar credencial para uso no exterior retorna true
   */
  public Boolean desabilitarUsoExterior(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaExterior(DESABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaExterior().equals(DESABILITADO);
  }

  /**
   * avisarPerdaOuRoubo Método responsável por habilitar credencial para uso no exterior
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir habilitar credencial para uso no exterior retorna true
   */
  public Boolean habilitarUsoExterior(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaExterior(HABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaExterior().equals(HABILITADO);
  }

  /**
   * Método responsável por desabilitar credencial para uso em E-commerce
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir desabilitar credencial para uso em E-commerce retorna true
   */
  public Boolean desabilitarEcommerce(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaEcommerce(DESABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaEcommerce().equals(DESABILITADO);
  }

  /**
   * Método responsável por habilitar credencial para uso em E-commerce
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir habilitar credencial para uso em E-commerce retorna true
   */
  public Boolean habilitarEcommerce(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaEcommerce(HABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaEcommerce().equals(HABILITADO);
  }

  /**
   * Método responsável por desabilitar saque para credencial
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir desabilitar saque para credencial retorna true
   */
  public Boolean desabilitarSaque(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaSaque(DESABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaSaque().equals(DESABILITADO);
  }

  /**
   * Método responsável por habilitar saque para credencial
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir habilitar saque para credencial retorna true
   */
  public Boolean habilitarSaque(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaSaque(HABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaSaque().equals(HABILITADO);
  }

  /**
   * Método responsável por desabilitar notificação de transação para credencial
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir desabilitar notificação de transação para credencial retorna true
   */
  public Boolean desabilitarNotificacaoTransacao(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaNotificacaoTransacao(DESABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaNotificacaoTransacao().equals(DESABILITADO);
  }

  /**
   * Método responsável por habilitar notificação de Transação para credencial
   *
   * @param idCredencial
   * @param idUsuario
   * @return se conseguir habilitar notificação de Transação para credencial retorna true
   */
  public Boolean habilitarNotificacaoTransacao(Long idCredencial, Integer idUsuario) {
    Credencial credencial = getCredencialNotNull(idCredencial);

    credencial.setHabilitaNotificacaoTransacao(HABILITADO);
    credencial.setIdUsuarioManutencao(idUsuario);
    credencial = save(credencial);

    return credencial.getHabilitaNotificacaoTransacao().equals(HABILITADO);
  }

  /**
   * Método responsável buscar status de habilitacao de uma credencial
   *
   * @param idCredencial
   * @return {@link CredencialStatus}
   */
  public CredencialStatus getStatusHabilitacao(Long idCredencial) {

    Credencial credencial = getCredencialNotNull(idCredencial);

    CredencialStatus credencialStatus = new CredencialStatus();
    BeanUtils.copyProperties(credencial, credencialStatus);

    ContaPagamentoFatura contaPagamentoFat =
        contaPagamentoFaturaRepository.findByIdConta(credencial.getIdConta());

    if (contaPagamentoFat != null && (contaPagamentoFat.getEnvioSuspensoFaturaImpressa() == null)) {
      credencialStatus.setFaturaInibida(true);
    } else if (contaPagamentoFat != null
        && contaPagamentoFat.getEnvioSuspensoFaturaImpressa() == 1) {
      credencialStatus.setFaturaInibida(
          false); // TODO regra invertida a pedido da Itspay, por prob de interpretação no mobile
    } else {
      credencialStatus.setFaturaInibida(true);
    }

    return credencialStatus;
  }

  @Transactional
  public Boolean avisarPerdaOuRoubo(
      Long idCredencial, Integer status, Integer idUsuarioManutencao, Boolean isAutoAtendimento) {
    Credencial credencial = getCredencialNotNull(idCredencial);
    return avisarPerdaOuRoubo(credencial, status, idUsuarioManutencao, isAutoAtendimento);
  }

  @Transactional
  public HashMap<String, Object> historicoCredencial(Long idCredencial) {
    Credencial credencial = findByIdCredencial(idCredencial);

    if (credencial == null || credencial.getIdCredencial() == null) {
      throw new GenericServiceException("Credencial informada não encontrada.");
    }

    List<HistoricoStatusCredencial> HistoricoCredenciais =
        historicoStatusCredencialRepository.findAllByIdCredencial(idCredencial);

    HashMap<String, Object> historicoCredencialPlataforma = new HashMap<>();

    historicoCredencialPlataforma.put("plataforma", HistoricoCredenciais);

    return historicoCredencialPlataforma;
  }

  @Transactional
  public HashMap<String, Object> regerarBoletoPorConfig(
      Long idConta, Integer idUsuarioManutencao, Long idCredencial) {

    Credencial credencial = findByIdCredencial(idCredencial);
    ContaPagamento contaPagamento = contaPagamentoService.findById(idConta);
    ProdutoInstituicaoConfiguracao prodInstConfig =
        this.prodInstConfigService.findByIdConta(idConta);

    PagamentoCredencial pagamentoCredencial =
        pagamentoCredencialRepository.findFirstByIdCredencialAnteriorOrderByIdDesc(idCredencial);
    HashMap<String, Object> result = new HashMap<>();
    if (pagamentoCredencial != null
        && pagamentoCredencial.getStatus() != 2) { // se status for diferente de expirado
      result.put("success", false);
      result.put(
          "message",
          "Não foi possível regerar boleto para segunda via de cartão pois o boleto atual ainda está válido.");
      return result;
    }

    switch (prodInstConfig.getSegundaViaBoleto()) {
      case PAGAMENTO_LIBERADO:
        if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(prodInstConfig.getIdInstituicao())) {
          // gerar boleto
          BigDecimal valor =
              procurarTarifaPerfilTarifarioConta(contaPagamento, Constantes.TARIFA_REPOSICAO);
          boletoSegundaViaService.gerarBoletoSegundaVia(
              contaPagamento, valor, idUsuarioManutencao, credencial, false);
        }
        result.put("success", true);
        result.put("message", "Novo boleto gerado.");
        PagamentoCredencial pagamentoCredencialNovo =
            pagamentoCredencialRepository.findFirstByIdCredencialAnteriorOrderByIdDesc(
                idCredencial);
        result.put("boleto", pagamentoCredencialNovo);
        return result;
      case PAGAMENTO_DESATIVADO:
        // não gerar boleto
        result.put("success", false);
        result.put(
            "message",
            "Não foi possível gerar novo boleto. Por favor, entre em contato com a agência.");
        return result;
      case DESABILITADO:
        // criar segunda via
        gerarSegundaViaPosPagamentoBoleto(credencial, idUsuarioManutencao);
        result.put("success", false);
        result.put("message", "Segunda via do cartão criada corretamente.");
        return result;
      default:
        result.put("success", false);
        result.put(
            "message",
            "Não foi possível gerar novo boleto. Por favor, entre em contato com a agência.");
        return result;
    }
  }

  public Credencial gerarSegundaViaPosPagamentoBoleto(
      Credencial credencial, Integer idUsuarioManutencao) {

    GerarCredencialRequest request = new GerarCredencialRequest();

    Integer status = credencial.getIdStatusV2();

    request.setDtHrLiberacaoEmissao(LocalDateTime.now());
    request.setIdConta(credencial.getIdConta());
    request.setIdPessoa(credencial.getIdPessoa());
    request.setIdUsuario(idUsuarioManutencao);
    request.setVirtual(credencial.getVirtual());
    request.setVirtualApelido(credencial.getApelidoVirtual());

    CredencialGerada gerarCredencial = geradorCredencialService.gerarCredencial(request);

    // Salvar log de solicitacao de reposicao
    logSolicitacaoReposicaoCredencialService.salvarSolicitacaoReposicao(
        idUsuarioManutencao,
        credencial.getIdCredencial(),
        gerarCredencial.getCredencial().getIdCredencial(),
        false,
        status.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA) ? MOTIVO_PERDA : MOTIVO_ROUBO);

    // MUDAR SENHA DO CARTÃO NOVO PARA A SENHA DO CARTÃO ANTERIOR.
    ChangePassCardReplacement changePassCardReplacement =
        new ChangePassCardReplacement(
            credencial.getTokenInterno(), gerarCredencial.getCredencial().getTokenInterno());
    JcardResponse response = cardService.changePasswordCardReplacement(changePassCardReplacement);

    if (!response.getSuccess()) {
      System.out.println(
          "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
              + credencial.getIdConta()
              + ", Motivo: "
              + response.getErrors());
      ;
    }

    credencialContaService.vincularCredencialContaSegundaVia(
        credencial.getIdCredencial(), gerarCredencial.getCredencial());

    return gerarCredencial.getCredencial();
  }

  @Transactional
  public Boolean avisarPerdaOuRoubo(
      Credencial credencial,
      Integer status,
      Integer idUsuarioManutencao,
      Boolean isAutoAtendimento) {

    if (status == null
        || (!status.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA)
            && !status.equals(Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO))) {
      throw new GenericServiceException("Status diferente de perda ou roubo. Status:" + status);
    }

    if (credencial.getVirtual() != null && credencial.getVirtual()) {
      throw new GenericServiceException(
          "Não é possível realizar Aviso de perda ou roubo para uma credencial virtual.");
    }

    TipoStatus tipoStatus =
        credencial.getTipoStatusV2() == null
            ? tipoStatusService.findById(credencial.getIdStatusV2())
            : credencial.getTipoStatusV2();

    if (tipoStatus == null) {
      throw new GenericServiceException(
          "Não é possível realizar Aviso de perda ou roubo .tipoStatus Não encontrado.");
    }

    if (!tipoStatus.getTipoGrupoStatus().getPermiteDesbloquear()) {
      throw new GenericServiceException(
          "Não é possível realizar Aviso de perda ou roubo para uma credencial cancelada.idGrupoStatus="
              + tipoStatus.getTipoGrupoStatus().getIdGrupoStatus());
    }

    if (isAutoAtendimento) {
      ContaPagamento contaPagamento =
          contaPagamentoService.buscarPorContaId(credencial.getIdConta());

      ProdutoInstituicao produtoInstituicao =
          produtoInstituicaoService.findByIdProdInstituicao(
              contaPagamento.getIdProdutoInstituicao());

      // Verificação quanto ao comportamento definido para o produto - gerar boleto antes de alterar
      // status, não
      // permitir alteração direta de status, ou comportamento padrão: alterar status sem
      // confirmação

      switch (produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getSegundaViaBoleto()) {
        case PAGAMENTO_LIBERADO:
          if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(
              contaPagamento.getIdInstituicao())) {
            PagamentoCredencial pagamentoCredencial =
                pagamentoCredencialRepository.findFirstByIdCredencialAnteriorOrderByIdDesc(
                    credencial.getIdCredencial());

            if (pagamentoCredencial != null
                && pagamentoCredencial.getStatus() != 2) { // se status for diferente de expirado
              return true;
            }
            blockCard(credencial);
            // gerar boleto
            BigDecimal valor =
                procurarTarifaPerfilTarifarioConta(contaPagamento, Constantes.TARIFA_REPOSICAO);
            boletoSegundaViaService.gerarBoletoSegundaVia(
                contaPagamento, valor, idUsuarioManutencao, credencial, false);
            return true;
          } else {
            break;
          }
        case PAGAMENTO_DESATIVADO:
          // não gerar boleto
          blockCard(credencial);
          return true;
        case DESABILITADO:
          break;
        default:
          break;
      }

      Boolean isCobrado =
          configReposicaoCredProdService.verifyMotivoIsCobradoByConta(
              credencial.getIdConta(),
              status.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA)
                  ? MOTIVO_PERDA
                  : MOTIVO_ROUBO);

      Boolean deveForcarCobranca = null;

      if (isCobrado) {
        try {
          criarTarifaDeSegundaVia(
              contaPagamento,
              credencial,
              produtoInstituicao.getB2b(),
              idUsuarioManutencao,
              deveForcarCobranca,
              "",
              isAutoAtendimento,
              FALSE);
        } catch (Exception e) {
          if (produtoInstituicao.getB2b()) {
            deveForcarCobranca = false;
            criarTarifaDeSegundaVia(
                contaPagamento,
                credencial,
                produtoInstituicao.getB2b(),
                idUsuarioManutencao,
                deveForcarCobranca,
                "",
                isAutoAtendimento,
                FALSE);
          } else {
            deveForcarCobranca = true;
            criarTarifaDeSegundaVia(
                contaPagamento,
                credencial,
                produtoInstituicao.getB2b(),
                idUsuarioManutencao,
                deveForcarCobranca,
                "",
                isAutoAtendimento,
                FALSE);
          }
        }
      }

      GerarCredencialRequest request = new GerarCredencialRequest();

      Boolean deveLiberarEmissao = false;

      // Verificar se credencial B2B pode ser liberada para emissão
      if (produtoInstituicao.getB2b()) {
        deveLiberarEmissao =
            isLiberarCredencialReposicaoB2b(credencial, deveForcarCobranca, isCobrado);
      }

      request.setDtHrLiberacaoEmissao(deveLiberarEmissao ? LocalDateTime.now() : null);
      request.setIdConta(credencial.getIdConta());
      request.setIdPessoa(credencial.getIdPessoa());
      request.setIdUsuario(idUsuarioManutencao);
      request.setVirtual(credencial.getVirtual());
      request.setVirtualApelido(credencial.getApelidoVirtual());

      CredencialGerada gerarCredencial = geradorCredencialService.gerarCredencial(request);

      // Salvar log de solicitacao de reposicao
      logSolicitacaoReposicaoCredencialService.salvarSolicitacaoReposicao(
          idUsuarioManutencao,
          credencial.getIdCredencial(),
          gerarCredencial.getCredencial().getIdCredencial(),
          isCobrado,
          status.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA) ? MOTIVO_PERDA : MOTIVO_ROUBO);

      // MUDAR SENHA DO CARTÃO NOVO PARA A SENHA DO CARTÃO ANTERIOR.
      ChangePassCardReplacement changePassCardReplacement =
          new ChangePassCardReplacement(
              credencial.getTokenInterno(), gerarCredencial.getCredencial().getTokenInterno());
      JcardResponse response = cardService.changePasswordCardReplacement(changePassCardReplacement);

      if (!response.getSuccess()) {
        log.warn(
            "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
                + credencial.getIdConta()
                + ", Motivo: "
                + response.getErrors());
      }

      blockCard(credencial);

      credencialContaService.vincularCredencialContaSegundaVia(
          credencial.getIdCredencial(), gerarCredencial.getCredencial());

      return gerarCredencial != null;
    }

    blockCard(credencial);

    return true;
  }

  /**
   * Infinanças,JoyPoints,Inmais não precisam de emissão e qm tem LocalEmissao=1
   *
   * @param credencial
   * @return boolean
   */
  private boolean isValidacaoTempoEmissaoNecessaria(Credencial credencial) {
    ProdutoInstituicaoConfiguracao configuracao =
        this.prodInstConfigService.findByIdConta(credencial.getIdConta());
    if (configuracao == null || configuracao.getIdProdInstituicao() == null) {
      throw new GenericServiceException(CONFIGURACAO_INVALIDA);
    }
    ProdutoInstituicao produto =
        this.produtoInstituicaoService.findByIdProdInstituicao(configuracao.getIdProdInstituicao());

    if (produto == null) {
      throw new GenericServiceException(
          CONFIGURACAO_INVALIDA,
          "Produto não encontrado na configuração(" + configuracao.getId() + ") .");
    }

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(produto.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_JOY_POINTS.equals(produto.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
            produto.getIdInstituicao())) {
      return false;
    } else if (Util.isNotNull(configuracao.getLocalEmissao()) && configuracao.getLocalEmissao()) {
      return false;
    } else if (!Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM.equals(credencial.getIdStatusV2())) {
      return false;
    } else {
      return true;
    }
  }

  private void validaTempoEmissaoCredencial(Credencial credencial) {
    if (this.isValidacaoTempoEmissaoNecessaria(credencial)) {
      LocalDateTime agora = LocalDateTime.now();
      String msg =
          "Não é possível emitir uma nova via deste cartão, pois ele foi emitido há menos de 8 dias completos.";
      if (Util.isNotNull(credencial.getDataHoraEmitido())) {
        long days = DateUtil.daysBetween(credencial.getDataHoraEmitido(), agora);
        if (days < DIAS_UTEIS_2VIA) {
          throw new GenericServiceException(msg);
        }
      } else {
        throw new GenericServiceException(msg);
      }
    }
  }

  private Integer getAutorAlteracao(Integer idUsuario) {
    if (idUsuario.equals(USUARIO_PORTADOR)) {
      return AUTOR_PORTADOR;
    } else if (idUsuario.equals(USUARIO_AUTOMATICO)
        || idUsuario.equals(Constantes.ID_USUARIO_PROC_7010)) {
      return AUTOR_AUTOMATICO;
    } else if (idUsuario.equals(USUARIO_URA_BAHAMAS)
        || idUsuario.equals(USUARIO_URA_ITSPAY)
        || idUsuario.equals(USUARIO_URA_BRB)) {
      return AUTOR_URA;
    } else {
      return AUTOR_SISTEMA;
    }
  }

  public Boolean avisarPerda(
      Long idCredencial, Integer idUsuarioManutencao, Boolean isAutoAtendimento) {
    return alterarStatusCredencial(
        idCredencial,
        Constantes.TIPO_STATUS_CANCELADO_POR_PERDA,
        idUsuarioManutencao,
        isAutoAtendimento);
  }

  public Boolean avisarRoubo(
      Long idCredencial, Integer idUsuarioManutencao, Boolean isAutoAtendimento) {
    return alterarStatusCredencial(
        idCredencial,
        Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO,
        idUsuarioManutencao,
        isAutoAtendimento);
  }

  public Credencial getCredencialNotNull(Long idCredencial) {
    Credencial credencial = findById(idCredencial);

    if (credencial == null) {
      throw new GenericServiceException(
          "Credencial não encontrada para informações enviadas. idCredencial: " + idCredencial);
    }
    return credencial;
  }

  /**
   * Metodo responsavel por buscar todos os mapasStatus disponíveis para a credencial
   *
   * @param idCredencial
   * @param idUsuario
   * @return
   */
  public List<MapaStatus> getMapasStatusDisponiveis(Long idCredencial, Integer idUsuario) {
    Credencial cred = getCredencialNotNull(idCredencial);
    ContaPagamento conta = getContaPagamentoByIdNotNull(cred.getIdConta());

    if (cred.getVirtual()) {
      Collection<Integer> statusDestino = new ArrayList<>();
      statusDestino.add(Constantes.TIPO_STATUS_CANCELADO_PELO_USUARIO);
      statusDestino.add(Constantes.TIPO_STATUS_CANCELADO_PELA_INSTITUICAO);
      return mapaStatusService.findMapaStatusDisponiveisVirtual(
          conta.getIdProcessadora(),
          conta.getIdInstituicao(),
          conta.getIdProdutoInstituicao(),
          APLICABILIDADE_CREDENCIAL,
          getAutorAlteracao(idUsuario),
          cred.getStatus(),
          statusDestino);
    } else {
      return mapaStatusService.findMapaStatusDisponiveis(
          conta.getIdProcessadora(),
          conta.getIdInstituicao(),
          conta.getIdProdutoInstituicao(),
          APLICABILIDADE_CREDENCIAL,
          getAutorAlteracao(idUsuario),
          cred.getStatus());
    }
  }

  /**
   * Metodo responsavel por buscar os tipos status disponives para uma credencial
   *
   * @param idCredencial
   * @param user
   * @return
   */
  public List<TipoStatus> getTipoStatusDisponiveis(Long idCredencial, SecurityUser user) {

    List<MapaStatus> mapasDisponiveis =
        getMapasStatusDisponiveis(idCredencial, user.getIdUsuario());
    List<TipoStatus> tipos = new ArrayList<>();
    List<TipoStatus> temp2 = new ArrayList<>();

    mapasDisponiveis.forEach(
        mapa -> {
          TipoStatus tipoStatusDestino = mapa.getTipoStatusDestino();
          temp2.add(tipoStatusDestino);
        });

    StatusCredencialComparator comparator = new StatusCredencialComparator();
    Collections.sort(temp2, comparator);

    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());
    HierarquiaPontoDeRelacionamento pr = service.findById(id);

    if (pr != null) {

      if (pr.getB2b()) {
        List<TipoStatus> temp = new ArrayList<>();

        for (TipoStatus tipo : temp2) {
          if (!tipo.getIdStatus().equals(Constantes.TIPO_STATUS_CANCELADO_PELA_INSTITUICAO)
              && !tipo.getIdStatus().equals(Constantes.TIPO_STATUS_CANCELADO_PELO_USUARIO)
              && !tipo.getIdStatus().equals(Constantes.TIPO_STATUS_CANCELADO_POR_COMPROMETIMENTO)
              && !tipo.getIdStatus()
                  .equals(Constantes.TIPO_STATUS_AUTORIZADO_TRANSACIONAR_PRESENCIAL)
              && !tipo.getIdStatus().equals(Constantes.TIPO_STATUS_CANCELADO_POR_FRAUDE)) {
            temp.add(tipo);
          }
        }

        StatusCredencialComparator comparator2 = new StatusCredencialComparator();
        Collections.sort(temp, comparator2);
        return temp;
      }
    }
    tipos.addAll(temp2);
    return tipos;
  }

  public void efetivaReconhecimentoFacialCaf(Long idValidacao) {
    registroValidacaoFacialCafService.efetivaValidacao(idValidacao);
  }

  public Long checaReconhecimentoFacialCaf(Credencial credencial) {
    Pessoa pessoa = pessoaService.findPessoaByIdConta(credencial.getIdConta());
    return checaReconhecimentoFacialCaf(pessoa, credencial);
  }

  public Long checaReconhecimentoFacialCaf(Pessoa pessoa, Credencial credencial) {
    ContaPagamento conta = contaPagamentoService.findByIdNotNull(credencial.getIdConta());
    return checaReconhecimentoFacialCaf(pessoa, conta);
  }

  public Long checaReconhecimentoFacialCaf(Pessoa pessoa, ContaPagamento conta) {
    if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      Long idValidacao =
          registroValidacaoFacialCafService.checaValidacao(
              pessoa.getDocumento(),
              pessoa.getIdInstituicao(),
              AntifraudeCafFacialObjetivosEnum.DESBLOQUEAR_CARTAO_FISICO);
      if (idValidacao == null) {
        throw new GenericServiceException(
            "Não foi possível reconhecer a validação facial. Por favor, tente novamente!",
            HttpStatus.FORBIDDEN);
      }
      return idValidacao;
    }
    return null;
  }

  @Transactional
  public void desbloquearCredencial(Long idCredencial) {
    desbloquearCredencial(idCredencial, null);
  }

  @Transactional
  public CredencialGerada desbloquearCredencial(Long idCredencial, SecurityUser user) {
    Credencial credencial = getCredencialLock(idCredencial);
    alterarStatusCredencial(
        credencial,
        Constantes.TIPO_STATUS_DESBLOQUEADO,
        Objects.isNull(user) ? USUARIO_PORTADOR : user.getIdUsuario(),
        FALSE);
    return new CredencialGerada(credencial);
  }

  @Transactional
  public Boolean alterarStatusCredencial(
      Long idCredencial, Integer statusDestino, Integer idUsuario, Boolean isAutoAtendimento) {
    Credencial credencial = getCredencialLock(idCredencial);
    return alterarStatusCredencial(credencial, statusDestino, idUsuario, isAutoAtendimento);
  }

  /**
   * Metodo resposavel por alterar status da credencial
   *
   * @param statusDestino
   * @param idUsuario
   * @return true caso sucesso e false caso não sucesso
   */
  private Boolean alterarStatusCredencial(
      Credencial credencial, Integer statusDestino, Integer idUsuario, Boolean isAutoAtendimento) {

    boolean sucesso = true;

    ContaPagamento conta = getContaPagamentoByIdNotNull(credencial.getIdConta());

    if (!Constantes.CONTA_ATIVA.equals(conta.getIdStatusV2())
        && Constantes.TIPO_STATUS_CANCELADO_POR_PERDA.equals(statusDestino)) {
      throw new GenericServiceException(
          "A Conta precisa estar Desbloqueada para realizar o Aviso de Perda.");
    }

    if (!Constantes.CONTA_ATIVA.equals(conta.getIdStatusV2())
        && Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO.equals(statusDestino)) {
      throw new GenericServiceException(
          "A Conta precisa estar Desbloqueada para realizar o Aviso de Roubo.");
    }

    Boolean alteracaoStatusPermitida = null;
    if (!Constantes.TIPO_STATUS_CANCELADO_PELO_USUARIO.equals(credencial.getStatus())) {
      // verifica se e possivel fazer essa alteracao, ou seja, se está mapeada
      alteracaoStatusPermitida =
          mapaStatusService.isAlteracaoStatusPermitida(
              conta.getIdProcessadora(),
              conta.getIdInstituicao(),
              conta.getIdProdutoInstituicao(),
              APLICABILIDADE_CREDENCIAL,
              getAutorAlteracao(idUsuario),
              credencial.getIdStatusV2(),
              statusDestino);

      if (statusDestino.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA)
          || statusDestino.equals(Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO)) {
        if (!alteracaoStatusPermitida) {
          throw new GenericServiceException(
              "Alteração de status da credencial não permitida. "
                  + "Status Origem: "
                  + credencial.getIdStatusV2()
                  + ", Status Destino: "
                  + statusDestino);
        }
        sucesso = avisarPerdaOuRoubo(credencial, statusDestino, idUsuario, isAutoAtendimento);
      }
    }

    if (credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)
        && statusDestino.equals(Constantes.TIPO_STATUS_DESBLOQUEADO)
        && credencial.getCsn() > 1) {
      cancelaCartoesAnteriores(
          credencial.getIdConta(), credencial.getIdPessoa(), credencial.getCsn(), idUsuario);
    } else if (credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)
        && statusDestino.equals(Constantes.TIPO_STATUS_DESBLOQUEADO)
        && Constantes.PROD_INSTITUICAO_BASICO_IN_MAIS.equals(conta.getIdProdutoInstituicao())) {
      Pessoa pessoa = pessoaService.findPessoaByIdConta(conta.getIdConta());
      emailService.sendDesbloqueioUseMais(pessoa);
    } else if (credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_DESBLOQUEADO)
        && statusDestino.equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)
        && Constantes.PROD_INSTITUICAO_BASICO_IN_MAIS.equals(conta.getIdProdutoInstituicao())) {
      Pessoa pessoa = pessoaService.findPessoaByIdConta(conta.getIdConta());
      emailService.sendBloqueioUseMais(pessoa);
    }

    boolean deveEnviarSenhaPorSMS =
        conta.getProdutoInstituicao().getProdutoInstituicaoConfiguracao().get(0).getIdGrupoProduto()
            == null;

    if (sucesso) {

      if (statusDestino.equals(Constantes.TIPO_STATUS_DESBLOQUEADO)
          && !credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO)) {
        // desbloqueia sem verificar o período de emissão das credenciais
        // todos os estados - o estado 5 -> desbloqueado
        Integer autor = getAutorAlteracao(idUsuario);
        Credencial retorno =
            contaPagamentoService.desbloquearCredencial(
                credencial.getIdCredencial(), autor, idUsuario);
        if (utilService.getProdutoInstituicaoBrbPay().equals(conta.getIdProdutoInstituicao())
            || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
                    conta.getIdInstituicao())
                && deveEnviarSenhaPorSMS)
            || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                    conta.getIdInstituicao())
                && deveEnviarSenhaPorSMS)) {
          enviarSenhaPorSms(retorno);
        }
        sucesso = retorno != null;
      } else {

        if (Boolean.FALSE.equals(alteracaoStatusPermitida)) {
          throw new GenericServiceException(
              "Alteração de status da credencial não permitida. "
                  + "Status Origem: "
                  + credencial.getIdStatusV2()
                  + ", Status Destino: "
                  + statusDestino);
        }
        TipoStatus tipoStatus = tipoStatusService.findById(statusDestino);
        if (TIPO_GRUPO_STATUS_TEMPORARIO.equals(credencial.getTipoStatusV2().getIdGrupoStatus())
            && tipoStatus.getTipoGrupoStatus().getAceitarTransacoes()) {
          // 5 -> desbloqueado
          sucesso = alterarStatus(credencial, statusDestino, idUsuario);
          if (sucesso) {
            // Desbloqueia no jcard
            unblockCard(credencial);
          }
        } else {
          // todos os estados -> todos os estados - desbloqueado
          sucesso = alterarStatus(credencial, statusDestino, idUsuario);
          if (sucesso) {
            // Bloquear credencial no JCARD se o id grupo status do tipo status destino for 9 =
            // CANCELADO
            blockCardStatusCancelado(credencial, statusDestino);
          }
        }
        if ((utilService.getProdutoInstituicaoBrbPay().equals(conta.getIdProdutoInstituicao())
                || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
                        conta.getIdInstituicao())
                    && deveEnviarSenhaPorSMS)
                || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                        conta.getIdInstituicao())
                    && deveEnviarSenhaPorSMS))
            && Constantes.TIPO_STATUS_DESBLOQUEADO.equals(statusDestino)) {
          enviarSenhaPorSmsFindCredencialById(credencial.getIdCredencial());
        }
      }
    } else {
      if ((utilService.getProdutoInstituicaoBrbPay().equals(conta.getIdProdutoInstituicao())
              || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
                      conta.getIdInstituicao())
                  && deveEnviarSenhaPorSMS)
              || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                      conta.getIdInstituicao())
                  && deveEnviarSenhaPorSMS))
          && Constantes.TIPO_STATUS_DESBLOQUEADO.equals(statusDestino)) {
        enviarSenhaPorSms(credencial);
      }
    }
    return sucesso;
  }

  public PagamentoCredencial findPagamentoCredencial(Long idCredencial) {
    try {
      PagamentoCredencial resp =
          pagamentoCredencialRepository.findFirstByIdCredencialAnteriorOrderByIdDesc(idCredencial);
      if (resp == null) {
        throw new GenericServiceException(
            "Não foi encontrada solicitação de pagamento para essa credencial");
      }
      return resp;
    } catch (GenericServiceException e) {
      return null;
    }
  }

  public SegundaViaBoletoEnum findConfigSegundaViaBoleto(Long idConta) {
    ProdutoInstituicaoConfiguracao configuracao = this.prodInstConfigService.findByIdConta(idConta);
    return configuracao.getSegundaViaBoleto();
  }

  /**
   * Metodo resposavel por alterar o status da credencial *
   *
   * @param credencial
   * @param statusDestino
   * @param idUsuario
   * @return true caso sucesso e false caso não sucesso
   */
  private Boolean alterarStatus(Credencial credencial, Integer statusDestino, Integer idUsuario) {
    credencial.setIdStatusV2(statusDestino);
    credencial.setDataHoraStatus(LocalDateTime.now());
    credencial.setIdUsuarioManutencao(idUsuario);
    return saveAndFlush(credencial) != null;
  }

  /**
   * Metodo resposavel por cancelar os cartões anteriores, altera o status da credencial *
   *
   * @param idConta
   * @param idPessoa
   * @param csn
   * @param idUsuario
   * @return true caso sucesso e false caso não sucesso
   */
  public void cancelaCartoesAnteriores(
      Long idConta, Long idPessoa, Integer csn, Integer idUsuario) {
    List<Credencial> credenciaisAnteriores =
        credencialRepository.findCartoesAnteriores(idConta, idPessoa, csn);

    credenciaisAnteriores.forEach(
        credencial -> {
          if (isCredencialGrupoStatusDesbloqueio(credencial)) {
            if (credencial.getCsn() == (csn - 1)) { // Credencial anterior
              credencial.setIdStatusV2(Constantes.TIPO_STATUS_CANCELADO_PELO_USUARIO);
            } else {
              credencial.setIdStatusV2(Constantes.TIPO_STATUS_REPOSTO);
            }

            credencial.setIdUsuarioManutencao(idUsuario);
            credencial.setDataHoraStatus(LocalDateTime.now());
            saveAndFlush(credencial);

            blockCard(credencial);
          }
        });
  }

  public Credencial getCredencialLock(Long idCredencial) {
    Credencial credencial = em.find(Credencial.class, idCredencial, LockModeType.PESSIMISTIC_WRITE);

    if (credencial == null) {
      throw new GenericServiceException(
          "Credencial não encontrada para informações enviadas. idCredencial: " + idCredencial);
    }
    return credencial;
  }

  public Credencial findUltimaCredencialVirtualOuFisica(
      String documento,
      Long idGrupo,
      Integer idInstituicao,
      Integer idProcessadora,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento,
      Integer virtual) {
    return credencialRepository.findUltimaCredencialVirtualOuFisica(
        documento,
        idGrupo,
        idInstituicao,
        idProcessadora,
        idFilial,
        idRegional,
        idPontoRelacionamento,
        virtual);
  }

  public boolean isCredencialGrupoStatusDesbloqueio(Credencial credencial) {
    TipoStatus tipo = this.tipoStatusService.findById(credencial.getIdStatusV2());
    for (Integer i : GRUPO_STATUS_DESBLOQUEIO) {
      if (tipo.getTipoGrupoStatus().getIdGrupoStatus().equals(i)) {
        return true;
      }
    }
    return false;
  }

  @Transactional
  public Boolean alterarStatusCredencialB2b(
      Long idCredencial, Integer statusDestino, Integer idUsuario) {

    Credencial credencial = getCredencialLock(idCredencial);

    boolean sucesso = true;

    ContaPagamento conta = getContaPagamentoByIdNotNull(credencial.getIdConta());

    // verifica se e possivel fazer essa alteracao, ou seja, se está mapeada
    Boolean alteracaoStatusPermitida =
        mapaStatusService.isAlteracaoStatusPermitida(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            conta.getIdProdutoInstituicao(),
            APLICABILIDADE_CREDENCIAL,
            getAutorAlteracao(idUsuario),
            credencial.getIdStatusV2(),
            statusDestino);

    if (statusDestino.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA)
        || statusDestino.equals(Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO)) {
      if (!alteracaoStatusPermitida) {
        throw new GenericServiceException(
            "Alteração de status da credencial não permitida. "
                + "Status Origem: "
                + credencial.getIdStatusV2()
                + " ,Status Destino: "
                + statusDestino);
      }
      sucesso = avisarPerdaOuRoubo(credencial, statusDestino, idUsuario, false);
    }

    if (sucesso) {
      if (statusDestino.equals(Constantes.TIPO_STATUS_DESBLOQUEADO)
          && !credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO)) {
        if (verificaEmissaoCredenciaisDesbloqueio(credencial)) {
          Integer autor = getAutorAlteracao(idUsuario);
          Credencial retorno =
              contaPagamentoService.desbloquearCredencial(
                  credencial.getIdCredencial(), autor, idUsuario);
          if (utilService.getProdutoInstituicaoBrbPay().equals(conta.getIdProdutoInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
                  conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                  conta.getIdInstituicao())) {
            enviarSenhaPorSms(retorno);
          }
          sucesso = retorno != null;
        }
      } else {

        if (!alteracaoStatusPermitida) {
          throw new GenericServiceException(
              "Alteração de status da credencial não permitida. "
                  + "Status Origem: "
                  + credencial.getIdStatusV2()
                  + " ,Status Destino: "
                  + statusDestino);
        }
        TipoStatus tipoStatus = tipoStatusService.findById(statusDestino);
        if (TIPO_GRUPO_STATUS_TEMPORARIO.equals(credencial.getTipoStatusV2().getIdGrupoStatus())
            && tipoStatus.getTipoGrupoStatus().getAceitarTransacoes()) {
          sucesso = alterarStatus(credencial, statusDestino, idUsuario);
          if (sucesso) {
            // Desbloqueia no jcard
            unblockCard(credencial);
          }
        } else {
          sucesso = alterarStatus(credencial, statusDestino, idUsuario);
          if (sucesso) {
            // Bloquear credencial no JCARD se o id grupo status do tipo status destino for 9 =
            // CANCELADO
            blockCardStatusCancelado(credencial, statusDestino);
          }
        }

        if ((utilService.getProdutoInstituicaoBrbPay().equals(conta.getIdProdutoInstituicao())
                || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
                    conta.getIdInstituicao())
                || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                    conta.getIdInstituicao()))
            && Constantes.TIPO_STATUS_DESBLOQUEADO.equals(statusDestino)) {
          enviarSenhaPorSmsFindCredencialById(credencial.getIdCredencial());
        }
      }
    }
    return sucesso;
  }

  public void blockCardStatusCancelado(Credencial credencial, Integer statusDestino) {
    if (!statusDestino.equals(Constantes.TIPO_STATUS_CANCELADO_POR_PERDA)
        && !statusDestino.equals(Constantes.TIPO_STATUS_CANCELADO_POR_ROUBO)) {
      blockCard(credencial);
    }
  }

  public Boolean trocarEstadoExterior(Long idCredencial, Integer idUsuarioManutencao) {
    Credencial c = getCredencialNotNull(idCredencial);
    Boolean resultado = false;

    if (c.getHabilitaExterior() != null && c.getHabilitaExterior()) {
      resultado = desabilitarUsoExterior(idCredencial, idUsuarioManutencao);
    } else {
      resultado = habilitarUsoExterior(idCredencial, idUsuarioManutencao);
    }
    return resultado;
  }

  public Boolean trocarEstadoEcommerce(Long idCredencial, Integer idUsuarioManutencao) {
    Credencial c = getCredencialNotNull(idCredencial);
    Boolean resultado = false;

    if (c.getHabilitaEcommerce() != null && c.getHabilitaEcommerce()) {
      resultado = desabilitarEcommerce(idCredencial, idUsuarioManutencao);
    } else {
      resultado = habilitarEcommerce(idCredencial, idUsuarioManutencao);
    }
    return resultado;
  }

  public Boolean trocarEstadoSaque(Long idCredencial, Integer idUsuarioManutencao) {
    Credencial c = getCredencialNotNull(idCredencial);
    Boolean resultado = false;

    if (c.getHabilitaSaque() != null && c.getHabilitaSaque()) {
      resultado = desabilitarSaque(idCredencial, idUsuarioManutencao);
    } else {
      resultado = habilitarSaque(idCredencial, idUsuarioManutencao);
    }
    return resultado;
  }

  public Boolean trocarEstadoUsoPessoa(Long idCredencial, Integer idUsuarioManutencao) {
    Credencial c = getCredencialNotNull(idCredencial);
    Boolean resultado = false;

    if (c.getHabilitaUsoPessoa() != null && c.getHabilitaUsoPessoa()) {
      resultado = bloquearTemporariamente(idCredencial, idUsuarioManutencao);
    } else {
      resultado = habilitarUsoCredencial(idCredencial, idUsuarioManutencao);
    }
    return resultado;
  }

  public Boolean trocarEstadoNotificacaoTransacao(Long idCredencial, Integer idUsuarioManutencao) {
    Credencial c = getCredencialNotNull(idCredencial);
    Boolean resultado = false;

    if (c.getHabilitaNotificacaoTransacao() != null && c.getHabilitaNotificacaoTransacao()) {
      resultado = desabilitarNotificacaoTransacao(idCredencial, idUsuarioManutencao);
    } else {
      resultado = habilitarNotificacaoTransacao(idCredencial, idUsuarioManutencao);
    }
    return resultado;
  }

  @Transactional
  public Boolean trocarEstado(Long idCredencial, Integer idUsuarioManutencao, Integer tipoEstado) {

    if (tipoEstado == null) {
      throw new GenericServiceException("Tipo Estado de troca não pode ser nulo.");
    }
    switch (tipoEstado) {
      case TROCAR_STATUS_EXTERIOR:
        return trocarEstadoExterior(idCredencial, idUsuarioManutencao);
      case TROCAR_STATUS_ECOMMERCE:
        return trocarEstadoEcommerce(idCredencial, idUsuarioManutencao);
      case TROCAR_STATUS_SAQUE:
        return trocarEstadoSaque(idCredencial, idUsuarioManutencao);
      case TROCAR_STATUS_USO_PESSOA:
        return trocarEstadoUsoPessoa(idCredencial, idUsuarioManutencao);
      case TROCAR_STATUS_NOTIFICACAO_TRANSACAO:
        return trocarEstadoNotificacaoTransacao(idCredencial, idUsuarioManutencao);
      default:
        throw new GenericServiceException(
            "TipoEstado diferente dos disponíveis.tipoEstado =" + tipoEstado);
    }
  }

  public List<Credencial> findByIdContaAndIdPessoa(Long idConta, Long idPessoa) {
    return credencialRepository.findByIdContaAndIdPessoa(idConta, idPessoa);
  }

  public Boolean recadastraPin(
      String novaSenha,
      String confirmacaoSenha,
      Long idCredencial,
      String senhaUsuario,
      Integer idUsuario,
      String tokenJWT) {
    return recadastraPin(
            novaSenha, confirmacaoSenha, idCredencial, senhaUsuario, idUsuario, tokenJWT, false)
        != null;
  }

  /**
   * <AUTHOR> - SPEEDWAY TI
   * @param novaSenha
   * @param idCredencial
   * @param idUsuario
   * @param tokenJWT
   * @return
   */
  public boolean recadastraPinParaLote(
      String novaSenha, Long idCredencial, Integer idUsuario, String tokenJWT) {

    if (novaSenha == null) return false;

    Credencial credencial = null;
    credencial = findById(idCredencial);

    if (credencial == null) {
      return false;
    }

    // recadastrar senha pin(jcard)
    ContaPagamento conta = contaPagamentoService.findByIdNotNull(credencial.getIdConta());
    if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      Pessoa pessoa = pessoaService.findPessoaByIdConta(conta.getIdConta());
      Long idValidacao =
          registroValidacaoFacialCafService.checaValidacao(
              pessoa.getDocumento(),
              pessoa.getIdInstituicao(),
              AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_CARTAO);
      if (idValidacao == null) {
        throw new GenericServiceException(
            "Não foi possível reconhecer a validação facial. Por favor, tente novamente!",
            HttpStatus.FORBIDDEN);
      }
      List<Credencial> credenciais = findByIdContaInCredencialConta(conta.getIdConta());
      for (Credencial cred : credenciais) {
        try {
          atualizaSenhaJcard(novaSenha, tokenJWT, cred);
        } catch (GenericServiceException e) {
          return false;
        }
        cred = atualizaSenhaPlataforma(novaSenha, idUsuario, cred, false);
        try {
          resetarSenha(cred);
        } catch (Exception ignored) {
        }
      }
      registroValidacaoFacialCafService.efetivaValidacao(idValidacao);
    } else {
      try {
        atualizaSenhaJcard(novaSenha, tokenJWT, credencial);
      } catch (GenericServiceException e) {
        return false;
      }
      credencial = atualizaSenhaPlataforma(novaSenha, idUsuario, credencial, false);
      try {
        resetarSenha(credencial);
      } catch (Exception ignored) {
      }
    }

    return true;
  }

  public Credencial recadastraPin(
      String novaSenha,
      String confirmacaoSenha,
      Long idCredencial,
      String senhaUsuario,
      Integer idUsuario,
      String tokenJWT,
      Boolean origemPortador) {
    return recadastraPin(
        novaSenha,
        confirmacaoSenha,
        idCredencial,
        senhaUsuario,
        idUsuario,
        tokenJWT,
        origemPortador,
        false);
  }

  public Credencial recadastraPin(
      String novaSenha,
      String confirmacaoSenha,
      Long idCredencial,
      String senha,
      Integer idUsuario,
      String tokenJWT,
      Boolean origemPortador,
      Boolean isCardPin) {
    return recadastraPin(
        novaSenha,
        confirmacaoSenha,
        idCredencial,
        senha,
        idUsuario,
        tokenJWT,
        origemPortador,
        isCardPin,
        null,
        null);
  }

  public Credencial recadastraPin(
      String novaSenha,
      String confirmacaoSenha,
      Long idCredencial,
      String senha,
      Integer idUsuario,
      String tokenJWT,
      Boolean origemPortador,
      Boolean isCardPin,
      String chaveExterna,
      String token) {

    Credencial credencial = null;

    if (!novaSenha.equals(confirmacaoSenha)) {
      throw new GenericServiceException("A confirmação de senha não confere.");
    }

    boolean autenticacaoValida = false;

    // Determinar tipo de validação baseado nos parâmetros fornecidos
    if (chaveExterna != null && token != null) {
      // Validação por token de acesso
      autenticacaoValida =
          tokenAcessoService.utilizarToken(
              new UtilizarTokenAcessoRequest() {
                {
                  setChaveExterna(chaveExterna);
                  setToken(token);
                }
              });
    } else {
      // Validação por PIN (lógica original)
      autenticacaoValida =
          isCardPin
              ? validarPin(senha, idCredencial, tokenJWT)
              : validarPinUsuario(senha, idUsuario, origemPortador);
    }

    if (autenticacaoValida) {
      credencial = findById(idCredencial);

      if (credencial == null) {
        throw new GenericServiceException(
            "Não foi possível recadastrar Senha. Credencial não encontrada.");
      }

      ContaPagamento contaPagamento =
          contaPagamentoService.findByIdNotNull(credencial.getIdConta());

      if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(contaPagamento.getIdInstituicao())
          && credencial.getDtHrAlteracaoSenhaResgate() != null
          && origemPortador) {
        throw new GenericServiceException("Senha já cadastrada; Contate instituição.");
      }

      // recadastrar senha pin(jcard)
      if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(contaPagamento)) {
        List<Credencial> credenciais = findByIdContaInCredencialConta(contaPagamento.getIdConta());
        Long idValidacao = null;

        if (origemPortador) {
          Pessoa pessoa = pessoaService.findPessoaByIdConta(contaPagamento.getIdConta());
          idValidacao =
              registroValidacaoFacialCafService.checaValidacao(
                  pessoa.getDocumento(),
                  pessoa.getIdInstituicao(),
                  AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_CARTAO);

          if (idValidacao == null) {
            throw new GenericServiceException(
                "Não foi possível reconhecer a validação facial. Por favor, tente novamente!",
                HttpStatus.FORBIDDEN);
          }
        }

        Boolean isKreditOrigemPortador =
            Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(contaPagamento.getIdInstituicao())
                && origemPortador;
        for (Credencial cred : credenciais) {
          atualizaSenhaJcard(novaSenha, tokenJWT, cred);
          cred =
              atualizaSenhaPlataforma(
                  novaSenha, Constantes.ID_USUARIO_INCLUSAO_PORTADOR, cred, isKreditOrigemPortador);
          resetarSenha(cred);
        }

        if (origemPortador) {
          registroValidacaoFacialCafService.efetivaValidacao(idValidacao);
        }
      } else {
        atualizaSenhaJcard(novaSenha, tokenJWT, credencial);
        credencial =
            atualizaSenhaPlataforma(
                novaSenha,
                Constantes.ID_USUARIO_INCLUSAO_PORTADOR,
                credencial,
                Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(contaPagamento.getIdInstituicao())
                    && origemPortador);
        resetarSenha(credencial);
      }

    } else {
      if (chaveExterna != null && token != null) {
        throw new GenericServiceException("Token inválido ou expirado!");
      } else {
        throw new GenericServiceException("Usuário não encontrado!");
      }
    }

    return credencial;
  }

  public Credencial recadastraPinComToken(
      String novaSenha,
      String confirmacaoSenha,
      Long idCredencial,
      String chaveExterna,
      String token,
      Integer idUsuario,
      String tokenJWT) {
    // Redirecionar para o método unificado
    return recadastraPin(
        novaSenha,
        confirmacaoSenha,
        idCredencial,
        null, // senha não é necessária para validação por token
        idUsuario,
        tokenJWT,
        true, // origemPortador = true para validação por token
        false, // isCardPin = false para validação por token
        chaveExterna,
        token);
  }

  public Credencial recadastraPinCredencialCorporativo(
      Credencial credencial, String novaSenha, String tokenJWT, SecurityUser user) {

    try {
      novaSenha = getSha(novaSenha + tokenJWT);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível gerar o hash da senha!");
    }

    if (credencial == null) {
      throw new GenericServiceException(
          "Não foi possível recadastrar Senha. Credencial não encontrada.");
    }

    atualizaSenhaJcard(novaSenha, tokenJWT, credencial);
    credencial = atualizaSenhaPlataforma(novaSenha, user.getIdUsuario(), credencial, false);
    resetarSenha(credencial);

    return credencial;
  }

  public Boolean validarPinUsuario(String senhaUsuario, Integer idUsuario, Boolean origemPortador) {
    if (origemPortador) {
      PortadorLogin usuario = portadorLoginService.findById(Long.valueOf(idUsuario));
      if (usuario != null) {
        if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(usuario.getIdInstituicao())) {
          throw new GenericServiceException("Funcionalidade temporariamente desabilitada.");
        }
        if (!isPassMatch(senhaUsuario, usuario)) {
          throw new GenericServiceException(
              "Senha de Login incorreta. Tente novamente."); // Mensagem de Erro Anterior: throw new
          // GenericServiceException("Senha do
          // usuário logado não confere.
          // idUsuario: " + usuario.getCpf());
        }
        return true;
      }

    } else {
      AcessoUsuario usuario = usuarioService.findById(idUsuario);

      if (usuario != null) {
        if (usuario.getIdInstituicao() != null
            && Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(usuario.getIdInstituicao())) {
          throw new GenericServiceException("Funcionalidade temporariamente desabilitada.");
        }
        if (!isPassMatch(senhaUsuario + usuario.getCpf(), usuario)) {
          throw new GenericServiceException(
              "Senha do usuário logado não confere. idUsuario: " + usuario.getNome());
        }
        return true;
      }
    }
    return false;
  }

  public List<Credencial> findByIdPessoaAndUltimos4Digitos(Long idPessoa, Integer ultimos4Digitos) {
    return credencialRepository.findByIdPessoaAndUltimos4Digitos(idPessoa, ultimos4Digitos);
  }

  public List<Credencial> findByIdPessoa(Long idPessoa) {
    return credencialRepository.findByIdPessoa(idPessoa);
  }

  private boolean isPassMatch(String senha, PortadorLogin usuario) {
    return encodeSenhaSHA256(senha).equals(usuario.getSenha());
  }

  public static String encodeSenhaSHA256(String senha) {
    LegacyPasswordEncoder shaPasswordEncoder = new LegacyPasswordEncoder(SHA_256);
    return shaPasswordEncoder.encodePassword(senha, null);
  }

  private boolean isPassMatch(String senha, AcessoUsuario usuario) {
    return BCrypt.checkpw(senha, usuario.getSenha());
  }

  public Boolean solicitarSegundaViaUserEstabelecimento(
      SegundaViaCredencialRequestVO segundaViaCredencialRequestVO,
      SecurityUserEstabelecimento userEstabelecimento)
      throws BusinessException {

    if (!userEstabelecimento
        .getAuthoritiesFront()
        .contains(new SimpleGrantedAuthority("EMITIR_SEGUNDA_VIA_CREDENCIAL"))) {
      throw new GenericServiceException(
          "O usuário logado não possuí permissão para emitir 2ª via de credencial.");
    }

    Credencial credencialRecebida = findById(segundaViaCredencialRequestVO.getIdCredencial());

    if (credencialRecebida == null) {
      throw new GenericServiceException(
          "Não foi Possível pedir segunda via da Credencial.",
          "Credencial Não encontrada. IdCredencial: "
              + segundaViaCredencialRequestVO.getIdCredencial());
    }

    if (!credencialRecebida.getStatus().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)
        && !segundaViaCredencialRequestVO.getCobrarTarifa()
        && !userEstabelecimento
            .getAuthoritiesFront()
            .contains(new SimpleGrantedAuthority("ISENTAR_COBRANCA_2A_VIA"))) {
      throw new GenericServiceException(
          "O usuário logado não possuí permissão para emitir 2ª via de credencial sem realizar cobrança de tarifa.");
    }

    ContaPagamento contaPagamento =
        contaPagamentoService.buscarPorContaId(credencialRecebida.getIdConta());

    UtilController.checkHierarquiaUsuarioEstabelecimentoLogado(userEstabelecimento, contaPagamento);

    return solicitarSegundaVia(
        segundaViaCredencialRequestVO,
        credencialRecebida,
        contaPagamento,
        userEstabelecimento.getIdUsuario());
  }

  public Boolean solicitarSegundaViaUser(
      SegundaViaCredencialRequestVO segundaViaCredencialRequestVO, SecurityUser user)
      throws BusinessException {

    if (!user.getAuthoritiesFront()
        .contains(new SimpleGrantedAuthority("EMITIR_SEGUNDA_VIA_CREDENCIAL"))) {
      throw new GenericServiceException(
          "O usuário logado não possuí permissão para emitir 2ª via de credencial.");
    }

    Credencial credencialRecebida = findById(segundaViaCredencialRequestVO.getIdCredencial());

    if (credencialRecebida == null) {
      throw new GenericServiceException(
          "Não foi Possível pedir segunda via da Credencial.",
          "Credencial Não encontrada. IdCredencial: "
              + segundaViaCredencialRequestVO.getIdCredencial());
    }

    if (!Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM.equals(credencialRecebida.getIdStatusV2())
        && !segundaViaCredencialRequestVO.getCobrarTarifa()
        && !user.getAuthoritiesFront()
            .contains(new SimpleGrantedAuthority("ISENTAR_COBRANCA_2A_VIA"))) {
      throw new GenericServiceException(
          "O usuário logado não possuí permissão para emitir 2ª via de credencial sem realizar cobrança de tarifa.");
    }

    ContaPagamento contaPagamento =
        contaPagamentoService.buscarPorContaId(credencialRecebida.getIdConta());

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, contaPagamento);

    return solicitarSegundaVia(
        segundaViaCredencialRequestVO, credencialRecebida, contaPagamento, user.getIdUsuario());
  }

  @Transactional
  public boolean solicitarSegundaVia(
      SegundaViaCredencialRequestVO segundaViaCredencialRequestVO,
      Credencial credencialRecebida,
      ContaPagamento contaPagamento,
      Integer idUsuario) {

    validaTempoEmissaoCredencial(credencialRecebida);

    GerarCredencialRequest request = new GerarCredencialRequest();

    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(contaPagamento.getIdProdutoInstituicao());

    // EM CASO DE EXTRAVIO (NÃO COBRA TARIFA)
    if (credencialRecebida.getIdStatusV2().equals(BLOQUEIO_DE_ORIGEM)) {
      credencialRecebida.setIdStatusV2(Constantes.TIPO_STATUS_CANCELADO_EXTRAVIADO);
      credencialRecebida.setDataHoraStatus(LocalDateTime.now());
      save(credencialRecebida);

      // Já que não cobra reposição, liberar para emissão.
      request.setDtHrLiberacaoEmissao(LocalDateTime.now());

      // EM CASO DE COBRAR TARIFA
    } else if (segundaViaCredencialRequestVO.getCobrarTarifa()) {
      criarTarifaDeSegundaVia(
          contaPagamento,
          credencialRecebida,
          produtoInstituicao.getB2b(),
          idUsuario,
          segundaViaCredencialRequestVO.getDeveForcarCobranca(),
          segundaViaCredencialRequestVO.getIpOrigem(),
          FALSE,
          FALSE);

      // EM CASO DE NÃO COBRAR TARIFA O USUÁRIO LOGADO PRECISA TER A PERMISSÃO DE ISENTAR
    }

    Boolean deveLiberarEmissao = false;

    // Verificar se credencial B2B pode ser liberada para emissão
    if (produtoInstituicao.getB2b()) {
      deveLiberarEmissao =
          isLiberarCredencialReposicaoB2b(
              credencialRecebida,
              segundaViaCredencialRequestVO.getDeveForcarCobranca(),
              segundaViaCredencialRequestVO.getCobrarTarifa());
    }

    request.setDtHrLiberacaoEmissao(deveLiberarEmissao ? LocalDateTime.now() : null);
    request.setIdConta(credencialRecebida.getIdConta());
    request.setIdPessoa(credencialRecebida.getIdPessoa());
    request.setIdUsuario(idUsuario);
    request.setVirtual(credencialRecebida.getVirtual());
    request.setVirtualApelido(credencialRecebida.getApelidoVirtual());

    CredencialGerada credencialGerada = geradorCredencialService.gerarCredencial(request);

    // Salvar log de solicitacao de reposicao
    logSolicitacaoReposicaoCredencialService.salvarSolicitacaoReposicao(
        idUsuario,
        segundaViaCredencialRequestVO.getIdCredencial(),
        credencialGerada.getCredencial().getIdCredencial(),
        segundaViaCredencialRequestVO.getCobrarTarifa(),
        Constantes.TIPO_STATUS_CANCELADO_EXTRAVIADO.equals(credencialRecebida.getIdStatusV2())
            ? MOTIVO_2A_VIA_EXTRAVIO
            : MOTIVO_2A_VIA_COMUM);

    // Verificar parâmetro do produto para saber se mantêm senha do cartão anterior.
    if (isProdutoPermiteManterSenhaAnteriorByConta(credencialRecebida.getIdConta())) {

      // MUDAR SENHA DO CARTÃO NOVO PARA A SENHA DO CARTÃO ANTERIOR.
      ChangePassCardReplacement changePassCardReplacement =
          new ChangePassCardReplacement(
              credencialRecebida.getTokenInterno(),
              credencialGerada.getCredencial().getTokenInterno());
      JcardResponse response = cardService.changePasswordCardReplacement(changePassCardReplacement);

      if (!response.getSuccess()) {
        log.warn(
            "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
                + credencialRecebida.getIdConta()
                + ", Motivo: "
                + response.getErrors());
        ;
      }
    }

    credencialContaService.vincularCredencialContaSegundaVia(
        credencialRecebida.getIdCredencial(), credencialGerada.getCredencial());

    return credencialGerada != null;
  }

  @Transactional
  public Boolean solicitarCredencial(
      SegundaViaCredencialRequest segundaViaCredencialRequest,
      SecurityUserPortador userPortador,
      String ipOrigem,
      HttpServletRequest httpServletRequest)
      throws BusinessException {

    Long idCredencial = segundaViaCredencialRequest.getIdCredencial();
    Boolean deveForcarCobranca = segundaViaCredencialRequest.getDeveForcarCobranca();
    Boolean cobrarTarifa = segundaViaCredencialRequest.getCobrarTarifa();
    Boolean primeiraViaFisica = segundaViaCredencialRequest.getPrimeiraViaFisica();

    Credencial credencialRecebida = findById(idCredencial);

    Boolean solicitaFisica = primeiraViaFisica || !credencialRecebida.getVirtual();

    if (credencialRecebida == null) {
      throw new GenericServiceException(
          "Não foi Possível pedir segunda via da Credencial.",
          "Credencial Não encontrada. IdCredencial: " + idCredencial);
    }

    validaTempoEmissaoCredencial(credencialRecebida);

    GerarCredencialRequest request = new GerarCredencialRequest();

    ContaPagamento contaPagamento = null;

    if (segundaViaCredencialRequest.getIdConta() != null) {
      contaPagamento =
          contaPagamentoService.buscarPorContaId(segundaViaCredencialRequest.getIdConta());
    } else {
      contaPagamento = contaPagamentoService.buscarPorContaId(credencialRecebida.getIdConta());
    }

    Pessoa pessoa = pessoaService.findOneByIdPessoa(credencialRecebida.getIdPessoa());
    List<EnderecoPessoa> enderecosPessoa =
        enderecoPessoaService.findByIdPessoaAndStatus(pessoa.getIdPessoa(), ENDERECO_PESSOA_ATIVO);

    if (enderecosPessoa == null || enderecosPessoa.isEmpty())
      throw new GenericServiceException("O usuário não possui endereco ativo cadastrado.");

    if (enderecosPessoa.size() > 1)
      throw new GenericServiceException("O usuário possui mais de um endereço ativo.");

    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(contaPagamento.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdProdutoInstituicao(),
            contaPagamento.getIdInstituicao());

    if (enderecosPessoa.get(0).getDtHrInclusao() == null)
      throw new GenericServiceException("A data de inclusão do endereço não foi inserida.");

    Integer diasEmissaoSegundaVia;
    diasEmissaoSegundaVia =
        produtoInstituicaoConfiguracao.getDiasEmissaoSegundaVia() == null
            ? 0
            : produtoInstituicaoConfiguracao.getDiasEmissaoSegundaVia();
    // EM CASO DE MUDANCA RECENTE DE ENDERECO
    Date currentDate = new Date();
    if (DateUtil.differenceDates(currentDate, enderecosPessoa.get(0).getDtHrInclusao())
        < diasEmissaoSegundaVia) {
      if (!Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(
          produtoInstituicao.getIdInstituicao())) {
        throw new GenericServiceException(
            "O usuário mudou de endereço em menos de " + diasEmissaoSegundaVia + " dias.");
      } else if (credencialRecebida.getCsn()
          != 1) // Excessao para a emissao do cartao sensibilizado a partir do primeiro cartao
      // INMAIS
      {
        throw new GenericServiceException(
            "O usuário mudou de endereço em menos de " + diasEmissaoSegundaVia + " dias.");
      }
    }

    if (solicitaFisica) {
      limitaMaximoCartoesEmitidos(userPortador);
    }

    // EM CASO DE EXTRAVIO (NÃO COBRA TARIFA)
    if (credencialRecebida.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)) {
      credencialRecebida.setIdStatusV2(CANCELADO_EXTRAVIO);
      credencialRecebida.setDataHoraStatus(LocalDateTime.now());
      save(credencialRecebida);

      // Já que não cobra reposição, liberar para emissão.
      request.setDtHrLiberacaoEmissao(LocalDateTime.now());

      // EM CASO DE COBRAR TARIFA
    } else if (cobrarTarifa) {
      criarTarifaDeSegundaVia(
          contaPagamento,
          credencialRecebida,
          produtoInstituicao.getB2b(),
          USUARIO_PORTADOR,
          deveForcarCobranca,
          ipOrigem,
          FALSE,
          primeiraViaFisica);

      // EM CASO DE NÃO COBRAR TARIFA O USUÁRIO LOGADO PRECISA TER A PERMISSÃO DE ISENTAR
    }
    //		else if(!user.getAuthorities().contains(new
    // SimpleGrantedAuthority("ISENTAR_COBRANCA_2A_VIA"))) {
    //			throw new GenericServiceException("O usuário logado não possuí permissão para emitir 2ª via
    // de credencial sem realizar cobrança de tarifa.");
    //		}

    Boolean deveLiberarEmissao = false;

    // Verificar se credencial B2B pode ser liberada para emissão
    if (produtoInstituicao.getB2b()) {
      deveLiberarEmissao =
          isLiberarCredencialReposicaoB2b(credencialRecebida, deveForcarCobranca, cobrarTarifa);
    }

    request.setDtHrLiberacaoEmissao(deveLiberarEmissao ? LocalDateTime.now() : null);
    request.setIdConta(credencialRecebida.getIdConta());
    request.setIdPessoa(credencialRecebida.getIdPessoa());
    request.setIdUsuario(USUARIO_PORTADOR);
    request.setVirtual(primeiraViaFisica ? FALSE : credencialRecebida.getVirtual());
    request.setVirtualApelido(credencialRecebida.getApelidoVirtual());

    CredencialGerada credencialGerada = geradorCredencialService.gerarCredencial(request);

    credencialContaService.vincularCredencialContaSegundaVia(
        credencialRecebida.getIdCredencial(), credencialGerada.getCredencial());

    // Verificar parâmetro do produto para saber se mantêm senha do cartão anterior.
    if (isProdutoPermiteManterSenhaAnteriorByConta(credencialRecebida.getIdConta())) {

      // MUDAR SENHA DO CARTÃO NOVO PARA A SENHA DO CARTÃO ANTERIOR.
      ChangePassCardReplacement changePassCardReplacement =
          new ChangePassCardReplacement(
              credencialRecebida.getTokenInterno(),
              credencialGerada.getCredencial().getTokenInterno());
      JcardResponse response = cardService.changePasswordCardReplacement(changePassCardReplacement);

      if (!response.getSuccess()) {
        System.out.println(
            "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
                + credencialRecebida.getIdConta()
                + ", Motivo: "
                + response.getErrors());
        ;
      }
    }

    if (Constantes.PROD_INSTITUICAO_BASICO_IN_MAIS.equals(contaPagamento.getIdProdutoInstituicao()))
      emailService.sendSolicitarUseMais(pessoa);

    if (credencialGerada != null) {
      List<Long> credenciaisPortador = userPortador.getCredenciaisPortador();
      credenciaisPortador.add(credencialGerada.getCredencial().getIdCredencial());
      userPortador.setCredenciaisPortador(credenciaisPortador);
      portadorLoginService.refreshAuthentication(httpServletRequest, userPortador);
    }

    return credencialGerada != null;
  }

  private void limitaMaximoCartoesEmitidos(SecurityUserPortador userPortador) {
    List<ParametroValor> parametroValoresMaximo =
        parametroValorService.getParametrosByDescricaoChaveParametro(
            Constantes.PARAMETRO_MAXIMO_CARTOES_EMITIDOS);
    ParametroValor parametroValorInstituicaoMax = null;
    List<ParametroValor> parametroValoresCount =
        parametroValorService.getParametrosByDescricaoChaveParametro(
            Constantes.PARAMETRO_COUNTER_CARTOES_EMITIDOS);
    ParametroValor parametroValorInstituicaoCount = null;
    for (ParametroValor parametroValor : parametroValoresMaximo) {
      if (userPortador.getIdInstituicao().equals(parametroValor.getIdInstituicao())) {
        parametroValorInstituicaoMax = parametroValor;
      }
    }
    for (ParametroValor parametroValor : parametroValoresCount) {
      if (userPortador.getIdInstituicao().equals(parametroValor.getIdInstituicao())) {
        parametroValorInstituicaoCount = parametroValor;
      }
    }
    if (parametroValorInstituicaoMax != null && parametroValorInstituicaoCount != null) {
      if ("N".equalsIgnoreCase(parametroValorInstituicaoMax.getParametroDefinicao().getTipo())
          && "N"
              .equalsIgnoreCase(parametroValorInstituicaoCount.getParametroDefinicao().getTipo())) {
        Integer quantidadeAtual =
            Integer.valueOf(parametroValorInstituicaoCount.getValorParametro());
        Integer quantidadeMaxima =
            Integer.valueOf(parametroValorInstituicaoMax.getValorParametro());
        if (quantidadeMaxima > 0 && quantidadeAtual >= quantidadeMaxima) {
          throw new GenericServiceException(
              "Máximo de cartões disponíveis já solicitados. Tente novamente em alguns dias.");
        }
        quantidadeAtual++; // Acrescenta em 1 o cartão que será solicitado.
        parametroValorInstituicaoCount.setValorParametro(quantidadeAtual.toString());
        parametroValorService.save(parametroValorInstituicaoCount);
      }
    }
  }

  public Boolean solicitarSegundaViaPortador(
      SegundaViaCredencialRequestVO segundaViaCredencialRequestVO,
      SecurityUserPortador userPortador)
      throws BusinessException {

    Credencial credencialRecebida = findById(segundaViaCredencialRequestVO.getIdCredencial());

    if (credencialRecebida == null) {
      throw new GenericServiceException(
          "Não foi Possível pedir segunda via da Credencial.",
          "Credencial Não encontrada. IdCredencial: "
              + segundaViaCredencialRequestVO.getIdCredencial());
    }

    validaTempoEmissaoCredencial(credencialRecebida);

    GerarCredencialRequest request = new GerarCredencialRequest();
    ContaPagamento contaPagamento = null;

    if (segundaViaCredencialRequestVO.getIdConta() != null) {
      contaPagamento =
          contaPagamentoService.buscarPorContaId(segundaViaCredencialRequestVO.getIdConta());
    } else {
      contaPagamento = contaPagamentoService.buscarPorContaId(credencialRecebida.getIdConta());
    }

    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(contaPagamento.getIdProdutoInstituicao());

    // EM CASO DE EXTRAVIO (NÃO COBRA TARIFA)
    if (credencialRecebida.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)) {
      credencialRecebida.setIdStatusV2(CANCELADO_EXTRAVIO);
      credencialRecebida.setDataHoraStatus(LocalDateTime.now());
      save(credencialRecebida);

      // Já que não cobra reposição, liberar para emissão.
      request.setDtHrLiberacaoEmissao(LocalDateTime.now());

      // EM CASO DE COBRAR TARIFA
    } else if (segundaViaCredencialRequestVO.getCobrarTarifa()) {
      criarTarifaDeSegundaVia(
          contaPagamento,
          credencialRecebida,
          produtoInstituicao.getB2b(),
          USUARIO_PORTADOR,
          segundaViaCredencialRequestVO.getDeveForcarCobranca(),
          segundaViaCredencialRequestVO.getIpOrigem(),
          FALSE,
          FALSE);

      // EM CASO DE NÃO COBRAR TARIFA O USUÁRIO LOGADO PRECISA TER A PERMISSÃO DE ISENTAR
    }

    Boolean deveLiberarEmissao = false;

    // Verificar se credencial B2B pode ser liberada para emissão
    if (produtoInstituicao.getB2b()) {
      deveLiberarEmissao =
          isLiberarCredencialReposicaoB2b(
              credencialRecebida,
              segundaViaCredencialRequestVO.getDeveForcarCobranca(),
              segundaViaCredencialRequestVO.getCobrarTarifa());
    }

    request.setDtHrLiberacaoEmissao(deveLiberarEmissao ? LocalDateTime.now() : null);
    request.setIdConta(credencialRecebida.getIdConta());
    request.setIdPessoa(credencialRecebida.getIdPessoa());
    request.setIdUsuario(USUARIO_PORTADOR);
    request.setVirtual(credencialRecebida.getVirtual());
    request.setVirtualApelido(credencialRecebida.getApelidoVirtual());

    CredencialGerada credencialGerada = geradorCredencialService.gerarCredencial(request);

    // Salvar log de solicitacao de reposicao
    logSolicitacaoReposicaoCredencialService.salvarSolicitacaoReposicao(
        USUARIO_PORTADOR,
        segundaViaCredencialRequestVO.getIdCredencial(),
        credencialGerada.getCredencial().getIdCredencial(),
        segundaViaCredencialRequestVO.getCobrarTarifa(),
        Constantes.TIPO_STATUS_CANCELADO_EXTRAVIADO.equals(credencialRecebida.getIdStatusV2())
            ? MOTIVO_2A_VIA_EXTRAVIO
            : MOTIVO_2A_VIA_COMUM);

    // Verificar parâmetro do produto para saber se mantêm senha do cartão anterior.
    if (isProdutoPermiteManterSenhaAnteriorByConta(credencialRecebida.getIdConta())) {

      // MUDAR SENHA DO CARTÃO NOVO PARA A SENHA DO CARTÃO ANTERIOR.
      ChangePassCardReplacement changePassCardReplacement =
          new ChangePassCardReplacement(
              credencialRecebida.getTokenInterno(),
              credencialGerada.getCredencial().getTokenInterno());
      JcardResponse response = cardService.changePasswordCardReplacement(changePassCardReplacement);

      if (!response.getSuccess()) {

        log.warn(
            "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
                + credencialRecebida.getIdConta()
                + ", Motivo: "
                + response.getErrors());

        throw new BusinessException(
            500,
            "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
                + credencialRecebida.getIdConta()
                + ", Motivo: "
                + response.getErrors());
      }
    }

    credencialContaService.vincularCredencialContaSegundaVia(
        credencialRecebida.getIdCredencial(), credencialGerada.getCredencial());

    // Cancelar credencial anterior.
    credencialFacade.cancelarCredencial(
        credencialRecebida.getIdCredencial(), USUARIO_PORTADOR, true);

    return credencialGerada != null;
  }

  private void vincularCredencialContaEmGrupos(
      Credencial credencialAnterior, Credencial credencialNova) {
    credencialContaService.vincularCredencialContaSegundaVia(
        credencialAnterior.getIdCredencial(), credencialNova);
  }

  private Boolean isLiberarCredencialReposicaoB2b(
      Credencial credencial, Boolean forcouCobranca, Boolean cobrarTarifa) {

    ProdutoContratado produtoContratado =
        produtoContratadoRepository.buscarProdutoCadastradoPorCredencial(
            credencial.getIdCredencial());

    if (!Objects.equals(produtoContratado.getTipoCobrancaReposicao(), COBRAR_EMPRESA)
        && (!cobrarTarifa
            || (produtoContratado.getPrazoPagamento() > 0 && forcouCobranca == null)
            || (produtoContratado.getTarifaReposicao() != null
                && produtoContratado.getTarifaReposicao().compareTo(BigDecimal.ZERO) == 0)
            || (produtoContratado.getTarifaReposicao() != null
                && produtoContratado.getTarifaReposicao().compareTo(BigDecimal.ZERO) > 0
                && forcouCobranca == null))) {
      return true;
    } else if (Objects.equals(produtoContratado.getTipoCobrancaReposicao(), COBRAR_EMPRESA)
        && (!cobrarTarifa
            || (produtoContratado.getTarifaReposicao() != null
                && produtoContratado.getTarifaReposicao().compareTo(BigDecimal.ZERO) == 0)
            || produtoContratado.getPrazoPagamento() > 0)) {
      return true;
    }

    return false;
  }

  private Boolean isProdutoPermiteManterSenhaAnteriorByConta(Long idConta) {
    return prodInstConfigService.isProdutoPermiteManterSenhaAnteriorByConta(idConta);
  }

  private void criarTarifaDeSegundaVia(
      ContaPagamento contaPagamento,
      Credencial credencial,
      Boolean b2b,
      Integer idUsuario,
      Boolean deveForcarCobranca,
      String ipOrigem,
      Boolean isAutoAtendimento,
      Boolean primeiraViaFisica)
      throws BusinessException {
    BigDecimal valorTarifa = BigDecimal.ZERO;
    int codigoTransacaoTarifa =
        primeiraViaFisica ? Constantes.TARIFA_PRIMEIRA_VIA : Constantes.TARIFA_REPOSICAO;

    if (b2b) {
      ProdutoContratado produtoContratado =
          produtoContratadoRepository.buscarProdutoCadastradoPorCredencial(
              credencial.getIdCredencial());

      if (produtoContratado != null) {
        if (Objects.equals(produtoContratado.getTipoCobrancaReposicao(), COBRAR_EMPRESA)) {
          log.debug("Reposição deve ser cobrada da empresa (fluxo via batch)");
          return;
        }

        if (produtoContratado.getTarifaReposicao() != null
            && produtoContratado.getTarifaReposicao().compareTo(BigDecimal.ZERO) > 0) {
          valorTarifa = produtoContratado.getTarifaReposicao();
        } else {
          return;
        }
      }
    }

    BigDecimal tarifaPerfilTarifario = null;

    // Cobrar do portador sem forçar
    if (deveForcarCobranca == null) {
      tarifaPerfilTarifario =
          procurarTarifaPerfilTarifarioConta(contaPagamento, codigoTransacaoTarifa);

      if (tarifaPerfilTarifario.compareTo(BigDecimal.ZERO) > 0) {
        GetSaldoConta getSaldo = contaPagamentoService.getSaldoConta(credencial.getIdConta());

        if (isAutoAtendimento) {
          // Verificar Saldo

          if (getSaldo.getSaldoDisponivel() != null
              && getSaldo.getSaldoDisponivel().compareTo(tarifaPerfilTarifario) > 0) {
            lancarTarifaJCard(
                credencial, BigDecimal.ZERO, idUsuario, ipOrigem, codigoTransacaoTarifa);
          } else {
            throw new GenericServiceException(
                "A conta não possui saldo disponível para lançamento da tarifa.");
          }
        } else {
          if (Constantes.PROD_INSTITUICAO_BASICO_IN_MAIS.equals(
              contaPagamento.getProdutoInstituicao().getIdProdInstituicao())) {
            lancarTarifaInmais(credencial, ipOrigem, getSaldo, tarifaPerfilTarifario);
          } else {
            lancarTarifaJCard(
                credencial, BigDecimal.ZERO, idUsuario, ipOrigem, codigoTransacaoTarifa);
          }
        }
      }

      // Forçar cobrança do portador.
    } else if (deveForcarCobranca) {
      tarifaPerfilTarifario =
          procurarTarifaPerfilTarifarioConta(contaPagamento, codigoTransacaoTarifa);

      if (tarifaPerfilTarifario.compareTo(BigDecimal.ZERO) > 0) {
        // Removendo o lançamento auto e utilizando lançamento Manual, para a cobrança de tarifa se
        // dar de forma imediata.
        //	    		lancarManualmenteTarifaSegundaViaPortador(credencial, contaPagamento,
        // tarifaPerfilTarifario, idUsuario, codigoTransacaoTarifa);
        lancarTarifaJCard(credencial, BigDecimal.ZERO, idUsuario, ipOrigem, codigoTransacaoTarifa);
      }

      // Cobrar da empresa (se era pra cobrar do portador, mas este não tinha saldo e foi pra cobrar
      // da empresa).
    } else {
      b2bTarifasService.lancarTarifaEmpresa(
          contaPagamento, valorTarifa, COD_EVENTO_TARIFA_2_VIA.longValue(), idUsuario, credencial);
    }
  }

  private void lancarTarifaInmais(
      Credencial credencial,
      String ipOrigem,
      GetSaldoConta getSaldo,
      BigDecimal tarifaPerfilTarifario) {
    if (getSaldo.getSaldoDisponivel() == null
        || getSaldo.getSaldoDisponivel().compareTo(tarifaPerfilTarifario) < 0) {
      throw new GenericServiceException(
          "A conta não possui saldo disponível para lançamento da tarifa.");
    }
    if (credencial.getCsn() == 1) {
      lancarTarifaJCard(
          credencial, BigDecimal.ZERO, null, ipOrigem, Constantes.TARIFA_PRIMEIRA_VIA);
    } else if (credencial.getCsn() >= 2) {
      lancarTarifaJCard(credencial, BigDecimal.ZERO, null, ipOrigem, Constantes.TARIFA_REPOSICAO);
    }
  }

  private BigDecimal procurarTarifaPerfilTarifarioConta(
      ContaPagamento contaPagamento, Integer functionCode) {
    Integer idPerfil =
        perfilTarifarioService.buscarIdPerfilTarifarioByConta(contaPagamento.getIdConta());

    if (idPerfil == null) {
      throw new GenericServiceException(
          "Não foi possível cobrar tarifa de segunda via. O produto desta conta não possui perfil tarifário configurado.");
    }

    BigDecimal tarifa = perfilTarifarioService.buscarTarifaPorId(idPerfil, functionCode);

    if (tarifa == null) {
      throw new GenericServiceException(
          "Não foi possível cobrar tarifa de segunda via. O perfil tarifário "
              + idPerfil
              + " não possui o código de tarifa "
              + functionCode
              + " associado à ele");
    }

    return tarifa;
  }

  @Transactional(noRollbackFor = JcardServiceException.class)
  public boolean lancarTarifaJCard(
      Credencial credencial,
      BigDecimal valorDebitadoAlemDaTarifa,
      Integer idUsuario,
      String ipOrigem,
      Integer codigoTransacao)
      throws BusinessException {

    CadastroLancamentoManual lancamento = new CadastroLancamentoManual();

    lancamento.setIdConta(credencial.getIdConta());
    if (codigoTransacao.equals(Constantes.TARIFA_PRIMEIRA_VIA)) {
      lancamento.setTextoExtrato("TARIFA DE PEDIDO DE EMISSÃO");
    } else if (codigoTransacao.equals(Constantes.TARIFA_REPOSICAO)) {
      lancamento.setTextoExtrato("TARIFA DE PEDIDO DE SEGUNDA VIA");
    }
    lancamento.setValor(valorDebitadoAlemDaTarifa);
    lancamento.setSinal(Constantes.DEBITADO);
    idUsuario = idUsuario == null ? 999999 : idUsuario;
    lancamento.setCodTransacao(codigoTransacao);
    try {
      JcardResponse jcard =
          getContaPagamentoFacade().salvarLancamento(lancamento, idUsuario, ipOrigem, false);

      if (!jcard.getSuccess()) {
        throw new GenericServiceException(
            "Erro ao realizar a cobrança de segunda via de cartão. Detalhe: " + jcard.getErrors());
      }
      return true;
    } catch (JcardServiceException e) {
      // Caso o saldo não tenha sido suficiente, devo saber se preciso cobrar
      if (e.getMensagem().contains(ERRO_SALDO_INSUFICIENTE)) { // not.sufficient.funds
        throw new BusinessException(1, "Portador sem saldo suficiente.");
      }

      // Caso o produto nao possa receber essa cobrança
      if (e.getMensagem().contains(ERRO_FUNCTION_CODE)) { // invalid.functioncode
        throw new GenericServiceException(
            "Este produto não possui o function code de tarifa de reposição (562) associado.");
      }
      throw new GenericServiceException(
          "Erro ao realizar a cobrança de tarifa de segunda via de cartão.");
    }
  }

  private void lancarManualmenteTarifaSegundaViaPortador(
      Credencial credencial,
      ContaPagamento contaPagamento,
      BigDecimal valorTarifa,
      Integer idUsuario,
      Integer codigoTransacaoTarifa) {

    // Gravar na tabela lancamento_auto
    LancamentoAuto lancamentoAuto = new LancamentoAuto();
    lancamentoAuto.setCodigoTransacao(codigoTransacaoTarifa);
    lancamentoAuto.setDataHoraInclusao(Calendar.getInstance().getTime());
    lancamentoAuto.setIdConta(credencial.getIdConta());
    lancamentoAuto.setIdCredencial(credencial.getIdCredencial());
    lancamentoAuto.setDataTransacao(Calendar.getInstance().getTime());
    lancamentoAuto.setIdUsuario(idUsuario);
    lancamentoAuto.setValor(valorTarifa);
    lancamentoAuto.setSinal("D");
    lancamentoAuto.setPreProcessada(0);
    lancamentoAuto.setIdProcessadora(contaPagamento.getIdProcessadora());
    lancamentoAuto.setIdInstituicao(contaPagamento.getIdInstituicao());
    lancamentoService.cadastrarNovoLancamentoAutomatico(lancamentoAuto);
  }

  @Transactional
  public String showNumeroCompleto(NovoAcessoLogPan model, SecurityUser user) {

    AcessoLogPan acessoLogPan = new AcessoLogPan();
    BeanUtils.copyProperties(model, acessoLogPan);
    acessoLogPan.setIdUsuario(user.getIdUsuario());
    acessoLogPan.setDtHrJustificativa(new Date());

    acessoLogPan = acessoLogPanService.save(acessoLogPan);
    // Implementar retorno do número completo da credencial;

    return getNumeroCredencialEmClaro(model.getIdCredencial());
  }

  public String getNumeroCredencialEmClaro(Long idCredencial) {
    Credencial credencial = getCredencialNotNull(idCredencial);
    return getNumeroCredencialEmClaro(credencial);
  }

  private String getAliasKey(Integer idProcessadora, Integer idInstituicao) {
    String chaveAlias =
        "zpk."
            + Strings.padStart(
                idProcessadora.toString(),
                Constantes.MIN_LENGHT_ID_PROCESSADORA,
                Constantes.CHAR_ZERO)
            + Strings.padStart(
                idInstituicao.toString(),
                Constantes.MIN_LENGHT_ID_INSTITUICAO,
                Constantes.CHAR_ZERO);

    return chaveAlias;
  }

  public String getNumeroCredencialCriptografado(Long idCredencial) {
    Credencial credencial = getCredencialNotNull(idCredencial);
    Pessoa pessoa = getPessoaByIdNotNull(credencial);
    String numeroCredEmClaro = getNumeroCredencialEmClaro(idCredencial);

    String numeroCredCripto =
        CriptoUtil.criptografarConteudo(
            getAliasKey(pessoa.getIdProcessadora(), pessoa.getIdInstituicao()), numeroCredEmClaro);
    return numeroCredCripto;
  }

  public String getNumeroCredencialEmClaro(Credencial credencial) {
    GetCardResponse card = new GetCardResponse();

    if (credencial.getIdCredencialExterna() != null) {
      return descriptografarCredencialExterna(
          credencial.getIdCredencialExterna(), Constantes.ZPK_001);
    } else {
      card = cardService.getPan(credencial.getTokenInterno());
      return card.getCard().getPan();
    }
  }

  public String descriptografarCredencialExterna(String idCredExterna, String aliasKey) {
    try {
      return CriptoUtil.getConteudoEmClaro(idCredExterna, aliasKey);
    } catch (SMException e) {
      e.printStackTrace();
      throw new GenericServiceException(
          "Não foi possível descriptografar credencial. Tente novamente.");
    } catch (SecureKeyStoreException e) {
      e.printStackTrace();
      throw new GenericServiceException(
          "Não foi possível descriptografar credencial. Tente novamente.");
    }
  }

  public Credencial findByIdCredencial(Long idCredencial) {
    return credencialRepository.findOneByIdCredencial(idCredencial);
  }

  public Boolean enviarSenhaPorSmsFindCredencialById(Long idCredencial) {
    Credencial credencial = findByIdCredencial(idCredencial);
    return enviarSenhaPorSms(credencial);
  }

  public Boolean enviarSenhaPorWhatsappFindCredencialById(Long idCredencial) {
    Credencial credencial = findByIdCredencial(idCredencial);
    return enviarSenhaPorWhatsapp(credencial);
  }

  public Boolean enviarURLRedefinirSenhaPorSmsFindCredencialById(Long idCredencial) {
    Credencial credencial = findByIdCredencial(idCredencial);
    return enviarURLRedefinirSenhaPorSms(credencial);
  }

  /** Método responsável por enviar senha via sms */
  public Boolean enviarSenhaPorSms(Credencial credencial) {

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encontrada.");
    }

    // Busca a pessoa da credencial.
    Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());

    // Procura o titular da conta se a pessoa for um adicional e não possuir telefone celular
    if (ADICIONAL.equals(credencial.getTitularidade()) && pessoa.getTelefoneCelular() == null) {
      pessoa = pessoaService.findPessoaTitularConta(credencial.getIdConta());
    }

    if (credencial.getTipoStatusV2() != null
        && credencial.getTipoStatusV2().getIdGrupoStatus().equals(CANCELADO)) {
      throw new GenericServiceException(
          "Não é possível enviar a senha via SMS. Motivo: A Credencial está cancelada. Nome impresso = "
              + credencial.getNomeImpresso());
    }

    if (validaNumeroCelular(pessoa)) {

      String senha = getSenhaDescriptografadaDoJcardSemPadding(credencial);

      if (senha == null) {
        throw new GenericServiceException("Não foi Possível encontrar a Senha.");
      }

      montarEEnviarSMS(credencial, senha, pessoa);

    } else {
      throw new GenericServiceException("Este número telefônico não é válido");
    }

    resetarSenha(credencial);

    return true;
  }

  /** Método responsável por enviar senha via sms */
  public Boolean enviarSenhaPorWhatsapp(Credencial credencial) {

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encontrada.");
    }

    // Busca a pessoa da credencial.
    Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());

    // Procura o titular da conta se a pessoa for um adicional e não possuir telefone celular
    if (ADICIONAL.equals(credencial.getTitularidade()) && pessoa.getTelefoneCelular() == null) {
      pessoa = pessoaService.findPessoaTitularConta(credencial.getIdConta());
    }

    if (!credencial.getTipoStatusV2().getTipoGrupoStatus().getPermiteDesbloquear()) {
      throw new GenericServiceException(
          "Não é possível enviar a senha via SMS. Motivo: A Credencial está cancelada. Nome impresso = "
              + credencial.getNomeImpresso());
    }

    if (validaNumeroCelular(pessoa)) {

      String senha = getSenhaDescriptografadaDoJcardSemPadding(credencial);

      if (senha == null) {
        throw new GenericServiceException("Não foi Possível encontrar a Senha.");
      }

      montarEEnviarWhatsapp(credencial, senha, pessoa);

    } else {
      throw new GenericServiceException("Este número telefônico não é válido");
    }

    resetarSenha(credencial);

    return true;
  }

  public Boolean enviarURLRedefinirSenhaPorSms(Credencial credencial) {

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encontrada.");
    }

    // Busca a pessoa da credencial.
    Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());

    // Procura o titular da conta se a pessoa for um adicional e não possuir telefone celular
    if (ADICIONAL.equals(credencial.getTitularidade()) && pessoa.getTelefoneCelular() == null) {
      pessoa = pessoaService.findPessoaTitularConta(credencial.getIdConta());
    }

    TipoStatus tipoStatus = tipoStatusService.findById(credencial.getIdStatusV2());

    if (tipoStatus != null && tipoStatus.getIdGrupoStatus().equals(CANCELADO)) {
      throw new GenericServiceException(
          "Não é possível enviar a senha via SMS. Motivo: A Credencial está cancelada. Nome impresso = "
              + credencial.getNomeImpresso());
    }

    if (validaNumeroCelular(pessoa)) {

      String urlRedefinirSenha = getUrlRedefinicaoSenhaInstituicao(pessoa.getIdInstituicao());

      if (urlRedefinirSenha == null) {
        throw new GenericServiceException(
            "Não foi Possível encontrar a URL para redefinição de senha.");
      }

      montarEEnviarURLPorSMS(credencial, urlRedefinirSenha, pessoa);

    } else {
      throw new GenericServiceException("Este número telefônico não é válido");
    }

    resetarSenha(credencial);

    return true;
  }

  public String getSenhaDecriptografadaById(SenhaCartaoAppRequest req) {
    String senhaDecripto = null;
    Credencial credencial = findById(req.getIdCredencial());
    if (credencial != null) {
      // valida ufRg, dtNascimento e naturalidade
      validaDadosRecuperarSenha(credencial.getIdPessoa(), req);

      resetarSenha(credencial);
      senhaDecripto = getSenhaDescriptografadaDoJcardSemPadding(credencial);
    }
    return senhaDecripto;
  }

  private void validaDadosRecuperarSenha(Long idPessoa, SenhaCartaoAppRequest req) {
    Pessoa pessoa = pessoaService.findById(idPessoa);
    boolean cpf;
    boolean cvv;
    boolean dtNasc;
    if (pessoa == null || (pessoa.getDocumento() == null || pessoa.getDataNascimento() == null)) {
      throw new GenericServiceException("Não foi possivel validar os dados informados!");
    } else {

      cpf = formataDados(req.getDocumento()).equalsIgnoreCase(formataDados(pessoa.getDocumento()));
      String dataParse =
          dateUtil.dateFormat(
              dateUtil.DD_MM_YYYY, dateUtil.localDateTimeToDate(pessoa.getDataNascimento()));
      dtNasc = dataParse.trim().equalsIgnoreCase(req.getDataNascimento().trim());

      Credencial credencial = credencialRepository.findOneByIdCredencial(req.getIdCredencial());

      // chamada servico JCARD
      JcardRequestEmbossing jcardRequest = new JcardRequestEmbossing();
      jcardRequest.setToken(credencial.getTokenInterno());
      jcardRequest.setIdconsumer("001");

      JcardResponseEmbossing responseJcard = new JcardResponseEmbossing();

      responseJcard = jcardService.doPost(jcardRequest, responseJcard, jcardUrl + URL);

      if (!responseJcard.getSuccess()) {
        throw new GenericServiceException("Dados inválidos, Credencial não encontrada!");
      }

      cvv =
          CriptoUtil.descriptografarDados(responseJcard.getCard().getCvv2cripto())
              .substring(0, 3)
              .equals(req.getCvv().trim());

      if (!cpf || !cvv || !dtNasc) {
        throw new GenericServiceException("Não foi possivel validar os dados informados!");
      }
    }
  }

  public String formataDados(String dado) {
    return dado.replaceAll("[^0-9]+", "");
  }

  public String getSenhaDescriptografadaDoJcardSemPadding(Credencial credencial) {
    return descriptografarSenhaCartao(getSenhaCriptografada(credencial));
  }

  private String getSenhaDescriptografadaDoJcardComPadding(Credencial credencial) {
    String senha = null;
    String pinCripto = getSenhaCriptografada(credencial);

    try {
      senha = getSenhaEmClaroComPadding(pinCripto);
    } catch (SMException | SecureKeyStoreException e) {
      throw new GenericServiceException("Não foi possível descriptografar senha da credencial.", e);
    }

    return senha;
  }

  public String getSenhaCriptografada(Credencial credencial) {
    GetPinCriptoRequest request = new GetPinCriptoRequest();
    request.setIdconsumer(ID_CONSUMER);
    request.setToken(credencial.getTokenInterno());

    GetCardResponse pinCripto = cardService.getPinCripto(request);

    if (!pinCripto.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível obter senha criptografada.", pinCripto.getErrors());
    }
    return pinCripto.getCard().getPincripto();
  }

  public String getSenhaCredencialCriptografada(Long idCredencial) {
    Credencial credencial = getCredencialNotNull(idCredencial);
    String senhaEmClaro = getSenhaDescriptografadaDoJcardComPadding(credencial);

    Pessoa pessoa = getPessoaByIdNotNull(credencial);
    String senhaCripto =
        CriptoUtil.criptografarConteudo(
            getAliasKey(pessoa.getIdProcessadora(), pessoa.getIdInstituicao()), senhaEmClaro);
    return senhaCripto;
  }

  private String descriptografarSenhaCartao(String senhaCripto) {
    String senha = null;

    try {

      String clearPinStr = getSenhaEmClaroComPadding(senhaCripto);

      Integer tamanho =
          new Integer(clearPinStr.substring(POSICAO_INICIAL, QTD_RESERVADA_TAMANHO_PIN));
      senha = clearPinStr.substring(QTD_RESERVADA_TAMANHO_PIN, QTD_RESERVADA_TAMANHO_PIN + tamanho);

    } catch (Exception e) {
      e.printStackTrace();
    }

    return senha;
  }

  public String getSenhaEmClaroComPadding(String senhaCripto)
      throws SMException, SecureKeyStoreException {
    SSM ssm = new SSM(issuerDirSecurityLmk); // aqui necessita do caminho completo do arquivo

    SecureKeyStore ks = new SimpleKeyFile(issuerDirSecurityCfg);
    SecureDESKey zpk =
        (SecureDESKey) ks.getKey(Constantes.ZPK_001); // esse parâmetro enviar a fogo “zpk.001”

    byte[] criptoPin =
        ISOUtil.hex2byte(senhaCripto); // esse parâmetro é a string que o serviço REST retorna.
    byte[] clearPin = ssm.customDecrypt(zpk, criptoPin);

    String clearPinStr = new String(clearPin);
    return clearPinStr;
  }

  private String getUrlRedefinicaoSenhaInstituicao(Integer idInstituicao) {
    List<ParametroValor> parametroValores =
        parametroValorService.getParametrosByDescricaoChaveParametro(
            Constantes.PARAMETRO_URL_REDEFINIR_SENHA);
    ParametroValor parametroValorInstituicao = null;
    for (ParametroValor parametroValor : parametroValores) {
      if (idInstituicao.equals(parametroValor.getIdInstituicao())) {
        parametroValorInstituicao = parametroValor;
      }
    }
    if (parametroValorInstituicao != null) {
      if ("A".equalsIgnoreCase(parametroValorInstituicao.getParametroDefinicao().getTipo())) {
        return String.valueOf(parametroValorInstituicao.getValorParametro());
      }
    }
    return null;
  }

  private void montarEEnviarSMS(Credencial credencial, String senha, Pessoa pessoa) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    comunicado.setIdConta(credencial.getIdConta());
    comunicado.setIdCredencial(credencial.getIdCredencial());

    GatewaySMS gatewaySMS = getGatewaySMS(pessoa.getIdProcessadora(), pessoa.getIdInstituicao());
    comunicado.setIdGatewaySMS(gatewaySMS.getIdGatewaySMS());
    comunicado.setMensagem(
        "A Senha do Cartão XXXX-" + credencial.getUltimos4Digitos() + " é: " + senha);
    comunicado.setTipoEventoConta(TIPO_EVENTO_ENVIO_SENHA_SMS);
    String tel = String.valueOf(pessoa.getDddTelefoneCelular()) + pessoa.getTelefoneCelular();

    comunicado.setNumeroCelular(new Long(tel));
    agenteComunicadorService.prepararComunicacao(comunicado);

    enviarSms(comunicado);
  }

  private void montarEEnviarWhatsapp(Credencial credencial, String senha, Pessoa pessoa) {
    ComunicadoContaViaWhatsapp comunicado = new ComunicadoContaViaWhatsapp();
    comunicado.setIdConta(credencial.getIdConta());
    comunicado.setIdCredencial(credencial.getIdCredencial());

    comunicado.setMensagem(
        "A Senha do Cartão XXXX-" + credencial.getUltimos4Digitos() + " é: " + senha);
    comunicado.setTipoEventoConta(TIPO_EVENTO_ENVIO_SENHA_SMS);
    String tel = String.valueOf(pessoa.getDddTelefoneCelular()) + pessoa.getTelefoneCelular();

    comunicado.setNumeroCelular(new Long(tel));
    agenteComunicadorWhatsappService.prepararComunicacao(comunicado);

    enviarWhatsapp(comunicado);
  }

  private void montarEEnviarURLPorSMS(Credencial credencial, String url, Pessoa pessoa) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    comunicado.setIdConta(credencial.getIdConta());
    comunicado.setIdCredencial(credencial.getIdCredencial());

    GatewaySMS gatewaySMS = getGatewaySMS(pessoa.getIdProcessadora(), pessoa.getIdInstituicao());
    comunicado.setIdGatewaySMS(gatewaySMS.getIdGatewaySMS());
    comunicado.setMensagem("Para redefinir sua senha, acesse o link para o APP: " + url);
    comunicado.setTipoEventoConta(TIPO_EVENTO_ENVIO_SENHA_SMS);
    String tel = String.valueOf(pessoa.getDddTelefoneCelular()) + pessoa.getTelefoneCelular();

    comunicado.setNumeroCelular(new Long(tel));
    agenteComunicadorService.prepararComunicacao(comunicado);

    enviarSms(comunicado);
  }

  @Async
  public void enviarSms(ComunicadoContaViaSMS comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  @Async
  public void enviarWhatsapp(ComunicadoContaViaWhatsapp comunicado) {
    agenteComunicadorWhatsappService.comunicar(comunicado.getIdLogEventoConta());
  }

  public static boolean validaNumeroCelular(Pessoa pessoa) {
    if (pessoa.getTelefoneCelular() != null && pessoa.getDddTelefoneCelular() != null) {
      if (pessoa.getDdiTelefoneCelular().equals(55)) {
        String telefone =
            pessoa.getDddTelefoneCelular().toString() + pessoa.getTelefoneCelular().toString();
        return telefone.matches(Constantes.REGEX_VALIDA_CELULAR);
      } else {
        throw new GenericServiceException(
            "Este número telefônico não pertence ao território Brasileiro");
      }
    } else {
      throw new GenericServiceException(
          "A Pessoa não possui número de celular! Pessoa: " + pessoa.getNomeCompleto());
    }
  }

  public boolean isSenhaPadraoResgatePontos(Credencial credencial) {
    return credencial.getPin() == null
        || credencial
            .getPin()
            .equals("03ac674216f3e15c761ee1a5e255f067953623c8b388b4459e13f978d7c846f4");
  }

  public void removerDtBloqueioResgatePontos(Credencial credencial) {
    credencial.setDtBloqueioResgatePontos(null);
    credencialRepository.save(credencial);
  }

  public void atualizarLoteNasCredenciais(
      List<Long> idsCredenciais, Integer idLote, Integer idUsuario) {
    List<Credencial> credencials = new ArrayList<>();

    Credencial credencial;
    for (Long idCredencial : idsCredenciais) {

      credencial = credencialRepository.findById(idCredencial).orElse(null);
      if (credencial != null) {
        credencial.setIdCredencial(idCredencial);
        credencial.setIdLoteEmissao(idLote);
        credencial.setIdUsuarioManutencao(idUsuario);
        credencial.setDtHrLiberacaoEmissao(LocalDateTime.now());

        credencials.add(credencial);
      }
    }

    credencialRepository.saveAll(credencials);
  }

  @Transactional
  public boolean integrarCredencialExterna(CredencialExternaTO cred, SecurityUser user) {
    String credEncript = null;
    CredencialPreEmitidoExterna credExiste = null;
    Credencial credencial = findByIdCredencial(cred.getIdCredencial());
    if (credencial == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar a credencial para associar número externo.");
    }

    if (cred.getIdCredencialExterna().length() != 16) {
      throw new GenericServiceException(
          "Número de credencial externa não contém ou extrapola os 16 dígitos.");
    }

    if (cred.getIdCredencialExterna().startsWith("0000")) {
      credencial.setIdCredencialExterna(cred.getIdCredencialExterna());
      LocalDateTime today = LocalDateTime.now();
      credencial.setDataHoraEmitido(today);
    } else {
      String aliasKey = Constantes.ZPK_001;
      credEncript = CriptoUtil.criptografarConteudo(aliasKey, cred.getIdCredencialExterna());
      credExiste =
          credencialPreEmitidoExternaService.verificarExistenciaCredencial(
              credEncript,
              user.getIdProcessadora(),
              cred.getIdInstituicao(),
              cred.getIdProdInstituicao());
      if (credExiste == null) {
        throw new GenericServiceException(
            "Este número de conta/cartão pertence a outro produto ou não existe na base de dados. Verifique o número digitado e tente novamente!");
      }
      credencial.setIdCredencialExterna(credEncript);
    }
    credencial.setCredencialExternaReduzida(
        reduzirIdCredencialExterna(cred.getIdCredencialExterna()));
    credencial.setIdUsuarioManutencao(user.getIdUsuario());
    if (credencialRepository.save(credencial) != null) {
      credencialPreEmitidoExternaService.marcarContaCartaoUtilizadoESalvar(credExiste);
      return true;
    }
    return false;
  }

  public String reduzirIdCredencialExterna(String numeroCredExterna) {
    String bin6 = numeroCredExterna.substring(0, 6);
    String ultimosQuatro = numeroCredExterna.substring(12);
    return bin6 + ultimosQuatro;
  }

  public List<GetCredencial> getCredenciaisPjB2b(SecurityUser user) {
    List<GetCredencial> creds =
        credencialRepository.getCredenciaisPjB2b(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());
    if (creds == null) {
      throw new GenericServiceException("Nenhuma credencial de pessoa jurídica disponível.");
    }
    return creds;
  }

  public List<GetCredencial> findByDocumentoAndIdentificadorExterno(
      String documento,
      String idExternoEmpresa,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    return credencialRepository.findByDocumentoAndIdentificadorExterno(
        documento,
        idExternoEmpresa,
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoRelacionamento);
  }

  public List<Credencial> findByIdContaOrderByCsn(Long idConta) {
    return credencialRepository.findByIdContaOrderByCsnDesc(idConta);
  }

  public void enviarSMSSenhaCredenciaisCriadasByProposta(
      Long idConta, Integer idProcessadora, Integer idInstituicao) {

    // Buscar as credenciais da conta
    List<Credencial> credenciais = findByIdContaInCredencialConta(idConta);

    // Buscar pessoa titular da conta
    Pessoa pessoa = pessoaService.findPessoaTitularConta(idConta);

    if (validaNumeroCelular(pessoa)) {
      // Busca se a instituicao possui uma msg personalizada para o titular
      EnvioSMSInstituicao envioSMSInstituicaoTitular =
          envioSMSInstituicaoService.findByIdProcessadoraAndIdInstituicaoAndIdTipoEnvioSMS(
              idProcessadora, idInstituicao, ENVIO_SENHA_CREDENCIAL_CRIACAO_CONTA);

      // Busca se a instituicao possui uma msg personalizada para o adicional
      EnvioSMSInstituicao envioSMSInstituicaoAdicional =
          envioSMSInstituicaoService.findByIdProcessadoraAndIdInstituicaoAndIdTipoEnvioSMS(
              idProcessadora, idInstituicao, ENVIO_SENHA_CREDENCIAL_ADICIONAL_CRIACAO_CONTA);

      for (Credencial c : credenciais) {
        String senha = getSenhaDescriptografadaDoJcardSemPadding(c);
        if (senha == null) {
          log.warn("Não foi Possível encontrar a senha da credencial: " + c.getIdCredencial());
        } else {
          if (c.getTitularidade() == Constantes.TITULAR) {
            if (envioSMSInstituicaoTitular != null) {
              montarEEnviarSMSSenhaCredencialCriada(
                  c, senha, pessoa, envioSMSInstituicaoTitular.getTextoPadrao());
            }
          } else {
            if (envioSMSInstituicaoAdicional != null) {
              montarEEnviarSMSSenhaCredencialCriada(
                  c, senha, pessoa, envioSMSInstituicaoAdicional.getTextoPadrao());
            }
          }
        }
      }
    } else {
      log.warn("O número de telefone da pessoa: " + pessoa.getIdPessoa() + " não é válido!");
    }
  }

  private void montarEEnviarSMSSenhaCredencialCriada(
      Credencial credencial, String senha, Pessoa pessoa, String textoPadrao) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    comunicado.setIdConta(credencial.getIdConta());
    comunicado.setIdCredencial(credencial.getIdCredencial());
    GatewaySMS gatewaySMS = getGatewaySMS(pessoa.getIdProcessadora(), pessoa.getIdInstituicao());
    comunicado.setIdGatewaySMS(gatewaySMS.getIdGatewaySMS());

    textoPadrao =
        textoPadrao.replace(
            ULTIMOS_4_DIGITOS,
            StringUtils.leftPad(credencial.getUltimos4Digitos().toString(), 4, "0"));
    textoPadrao = textoPadrao.replace(SENHA_CARTAO, senha);

    comunicado.setMensagem(textoPadrao);
    comunicado.setTipoEventoConta(TIPO_EVENTO_ENVIO_SENHA_SMS);

    String tel = String.valueOf(pessoa.getDddTelefoneCelular()) + pessoa.getTelefoneCelular();

    comunicado.setNumeroCelular(new Long(tel));

    agenteComunicadorService.prepararComunicacao(comunicado);

    resetarSenha(credencial);

    enviarSms(comunicado);
  }

  private void resetarSenha(Credencial credencial) {
    try {
      resetContador(credencial);
    } catch (Exception e) {
      log.error(
          "Erro ao resetar contador da credencial: "
              + credencial.getIdCredencial()
              + " erro "
              + e.getMessage());
    }
  }

  public Credencial findUltimaCredencialDesbloqueadaTitularConta(Long idConta) {
    return credencialRepository.findUltimaCredencialDesbloqueadaTitularConta(idConta);
  }

  public Credencial findUltimaCredencialNaoVirtualTitularContaByGrupoStatus(
      Long idConta, List<Integer> idGrupoStatus) {
    return credencialRepository.findUltimaCredencialNaoVirtualTitularContaByGrupoStatus(
        idConta, idGrupoStatus);
  }

  public Credencial findUltimaCredencial(Long idConta, Long idPessoa) {
    return credencialRepository.findUltimaCredencial(idConta, idPessoa);
  }

  public List<Credencial> findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
      String documentoEmClaro, Integer ultimosDigitos) {
    return credencialRepository.findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
        documentoEmClaro, ultimosDigitos);
  }

  private GatewaySMS getGatewaySMS(Integer idProcessadora, Integer idInstituicao) {
    GatewaySMS gatewaySMS =
        gatewaySMSService.findFirstByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (gatewaySMS == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar gatewaySMS. IdProcessadora: "
              + idProcessadora
              + " , idInstituicao: "
              + idInstituicao);
    }
    return gatewaySMS;
  }

  public GetContadorSenhaCard getContadorSenha(Long idCredencial) {

    GetContadorSenhaCard contador = new GetContadorSenhaCard();

    Credencial credencial = findById(idCredencial);

    GetCardResponse card = new GetCardResponse();

    if (credencial != null) {
      card = getCard(credencial);
    } else {
      throw new GenericServiceException("Credencial: " + idCredencial + " não encontrada");
    }

    contador.setContadorSenhaInvalida(card.getCard().getContadorSenhaInvalida());
    contador.setBloqueioSenhaInvalida(card.getCard().getBloqueioSenhaInvalida());
    contador.setIdCredencial(idCredencial);

    return contador;
  }

  @Transactional
  public void resetContador(Credencial credencial) {
    JcardResponse resetContador = cardService.resetContador(credencial.getTokenInterno());

    if (!resetContador.getSuccess()) {
      throw new GenericServiceException("Não foi possível realizar o reset.");
    }
  }

  public Boolean resetContadorSenha(Long idCredencial) {

    Credencial credencial = findById(idCredencial);

    if (credencial != null) {
      resetContador(credencial);
    } else {
      throw new GenericServiceException("Credencial: " + idCredencial + " não encontrada");
    }

    return true;
  }

  public String getSha(String valor) throws Exception {
    MessageDigest md = MessageDigest.getInstance("SHA-512");
    md.update(valor.getBytes());

    byte byteData[] = md.digest();

    // convert the byte to hex format method 1
    StringBuffer sb = new StringBuffer();
    for (int i = 0; i < byteData.length; i++) {
      sb.append(Integer.toString((byteData[i] & 0xff) + 0x100, 16).substring(1));
    }

    return sb.toString();
  }

  /**
   * Para recadastramento do pin em lote
   *
   * @param loteEmissao
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  public Long getCountParaDefinicaoSenha(
      Integer loteEmissao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    return credencialRepository.getCountParaDefinicaoSenha(
        loteEmissao, idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
  }

  /**
   * Para recadastramento do pin em lote
   *
   * @param loteEmissao
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  public List<CredencialParaRecadastramentoPin> getCredenciaisParaRedefinicaoSenha(
      Integer loteEmissao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    return credencialRepository.getCredenciaisParaRedefinicaoSenha(
        loteEmissao, idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
  }

  /**
   * Para ativar uma credencial pre cadastrada loyalty
   *
   * @param idConta
   * @param idInstituicao
   * @param idUsuario
   * @return
   */
  public Boolean ativarCredencialPreCadastradaLoyalty(
      Long idConta, Integer idInstituicao, Integer idUsuario) {
    Long idCredencial = getIdCredencialByIdContaAndIdInstituicaoLoyalty(idConta, idInstituicao);
    return alterarStatusCredencial(
        idCredencial, Constantes.TIPO_STATUS_DESBLOQUEADO, idUsuario, false);
  }

  public CredencialPortadorLancamentoAcquirerVO checkAndGetCredencialToDoLancamentoPortadorAcquirer(
      Long idCredencial, String cpf, Integer idProcessadora, Integer idInstituicao) {
    CredencialPortadorLancamentoAcquirerVO response =
        credencialRepository.checkAndGetCredencialToDoLancamentoPortadorAcquirer(
            idCredencial, cpf, idProcessadora, idInstituicao);

    if (response == null) {
      throw new GenericServiceException(
          "Não existe uma credencial com ID: "
              + idCredencial
              + " para o documento: "
              + cpf
              + ", Processadora: "
              + idProcessadora
              + " e Instituição: "
              + idInstituicao);
    }

    return response;
  }

  public CredencialPortadorLancamentoAcquirerVO checkAndGetCredencialToDoLancamentoUsuarioAcquirer(
      Long idCredencial, Integer idProcessadora, Integer idInstituicao) {
    CredencialPortadorLancamentoAcquirerVO response =
        credencialRepository.checkAndGetCredencialToDoLancamentoUsuarioAcquirer(
            idCredencial, idProcessadora, idInstituicao);

    if (response == null) {
      throw new GenericServiceException(
          "Não existe uma credencial com ID: "
              + idCredencial
              + " para o documento: "
              + ", Processadora: "
              + idProcessadora
              + " e Instituição: "
              + idInstituicao);
    }

    return response;
  }

  private Long getIdCredencialByIdContaAndIdInstituicaoLoyalty(
      Long idConta, Integer idInstituicao) {
    return credencialRepository.getIdCredencialByIdContaAndIdInstituicaoLoyalty(
        idConta, idInstituicao);
  }

  public CredencialJcard getStatusJcard(Long idCredencial, SecurityUser user) {
    Credencial credencial = credencialRepository.findOneByIdCredencial(idCredencial);

    CredencialJcard credencialResumida = new CredencialJcard();

    GetCardResponse card = new GetCardResponse();

    if (credencial != null) {
      card = getCard(credencial);
    } else {
      throw new GenericServiceException("Credencial: " + idCredencial + " não encontrada");
    }

    credencialResumida.setBin6(Integer.valueOf(card.getCard().getBin()));
    credencialResumida.setUltimos4Digitos(Integer.valueOf(card.getCard().getLastfour()));
    credencialResumida.setIdCredencial(credencial.getIdCredencial());
    credencialResumida.setStatus(StatusJcardEnum.getCodigo(card.getCard().getState()));
    credencialResumida.setDescStatus(
        StatusJcardEnum.getDescricaoPorCodigoPlataforma(credencialResumida.getStatus()));

    return credencialResumida;
  }

  public Boolean alteraStatusJcard(
      TrocarEstadoCredencialJcard trocarEstadoCredencialJcard, SecurityUser user) {

    Credencial credencial =
        credencialRepository.findOneByIdCredencial(trocarEstadoCredencialJcard.getIdCredencial());

    JcardResponse response = new JcardResponse();

    if (credencial != null) {
      response =
          cardService.changeState(
              credencial.getTokenInterno(),
              StatusJcardEnum.getDescricaoPorCodigo(trocarEstadoCredencialJcard.getStatusNovo()));
    } else {
      throw new GenericServiceException(
          "Credencial: " + credencial.getIdCredencial() + " não encontrada");
    }

    if (!response.getSuccess()) {
      log.error(
          ConstantesErro.JCARD_ERRO_TROCA_STATUS.format("Token: " + credencial.getTokenInterno()));
      throw new GenericServiceException(ConstantesErro.JCARD_ERRO_TROCA_STATUS.getMensagem());
    }

    LogStatusCredencialJcard credencialJcard = new LogStatusCredencialJcard();

    credencialJcard.setStatusAntigo(
        StatusJcardEnum.getDescricaoPorCodigoPlataforma(
            trocarEstadoCredencialJcard.getStatusAntigo()));
    credencialJcard.setStatusNovo(
        StatusJcardEnum.getDescricaoPorCodigoPlataforma(
            trocarEstadoCredencialJcard.getStatusNovo()));
    credencialJcard.setIdUsuario(user.getIdUsuario());
    credencialJcard.setDataAlteracao(new Date());

    logStatusCredencialJcardService.save(credencialJcard);

    return response.getSuccess();
  }

  public DetalheCredencialLoteVo consultaDetalheLoteCartao(Long idCredencial) {
    return credencialRepository.consultaDetalheLoteCartao(idCredencial);
  }

  public Credencial buscarCredencialParaLancamentoManual(Long idConta) {
    Credencial credencial =
        credencialRepository.findUltimaCredencialDesbloqueadaTitularConta(idConta);

    if (credencial == null) {
      credencial = credencialRepository.findUltimaCredencialTitularConta(idConta);
      if (credencial == null) {
        credencial =
            credencialRepository.findUltimaCredencialDesbloqueadaTitularContaInCredencialConta(
                idConta);
      }
    }
    return credencial;
  }

  public ResponseEntity<HashMap<String, Object>> liberarEmissaoManualmente(
      Long idCredencial, Integer idUsuario) {

    HashMap<String, Object> map = new HashMap<>();

    Credencial credencial = findByIdCredencial(idCredencial);

    if (credencial == null) {
      throw new GenericServiceException(
          "Impossível marcar data/hora de liberação emissão. A credencial não foi encontrada. Id Credencial: "
              + idCredencial);
    }

    if (credencial.getDtHrLiberacaoEmissao() != null) {
      map.put("msg", "A data hora liberação deste cartão já está preenchida.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    if (!credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)) {
      map.put(
          "msg",
          "A credencial deve estar em bloqueio de origem para marcar a data/hora liberação emissão.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    if (credencial.getIdLoteEmissao() != null || credencial.getDataHoraEmitido() != null) {
      map.put(
          "msg",
          "Não é possível marcar a data/hora liberação emissão. Esta credencial já foi emitida.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    credencial.setDtHrLiberacaoEmissao(LocalDateTime.now());
    credencial.setIdUsuarioManutencao(idUsuario);
    save(credencial);

    map.put("msg", "Data/Hora de liberação emissão marcada com sucesso.");
    map.put("ok", credencial != null);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  public List<Long> recuperarCredenciaisPortador(Long idLogin) {
    return credencialRepository.recuperarCredenciaisPortador(idLogin);
  }

  public List<Credencial> findCredenciaisByDocumentoAndInstituicao(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return credencialRepository.findCredenciaisByDocumentoAndInstituicao(
        idProcessadora, idInstituicao, documento);
  }

  public void validaAlgumaCredencialFisicaAtivaOuTemp(List<Credencial> credenciais) {

    credenciais.stream()
        .filter(
            c ->
                (Constantes.GRUPO_STATUS_ATIVO.equals(c.getStatus())
                        || Constantes.GRUPO_STATUS_BLOQUEIO_ORIGEM.equals(c.getStatus())
                        || Constantes.GRUPO_STATUS_BLOQUEIO_TEMP.equals(c.getStatus()))
                    && !c.getVirtual())
        .findAny()
        .orElseThrow(
            () -> new GenericServiceException(ConstantesErro.CAR_STATUS_BLOQUEADO.getMensagem()));
  }

  public GetCredenciaisResponse getCredenciaisByDocumentoAndProduto(
      String documento, Integer idProduto) {

    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(idProduto);

    if (produtoInstituicao == null) {
      throw new GenericServiceException("Produto do cartão não encontrado");
    }

    findPessoasNotNull(
        documento, produtoInstituicao.getIdProcessadora(), produtoInstituicao.getIdInstituicao());

    findContaPagamentoNotNull(
        documento, produtoInstituicao.getIdProcessadora(), produtoInstituicao.getIdInstituicao());

    List<Credencial> credencias =
        credencialRepository.getCredenciasPorDocumentoEProdutoInstituicao(documento, idProduto);

    List<GetCredencial> credenciaisRetorno = new ArrayList<>();

    for (Credencial credencial : credencias) {

      Pessoa pessoa = pessoaService.findOneByIdPessoa(credencial.getIdPessoa());

      ContaPagamento conta = contaPagamentoService.findById(credencial.getIdConta());

      credenciaisRetorno.add(
          loadPlastico(
              credencial,
              conta,
              pessoa,
              produtoInstituicao.getIdProcessadora(),
              produtoInstituicao.getIdInstituicao()));
    }

    GetCredenciaisResponse response = new GetCredenciaisResponse(credenciaisRetorno);

    return response;
  }

  public List<ContaPagamento> findContaPagamentoNotNull(
      String documento, Integer idProc, Integer idInst) {
    List<ContaPagamento> contaPagamento = findContasPagamento(documento, idProc, idInst);

    if (contaPagamento == null || contaPagamento.size() == 0) {
      throw new GenericServiceException("Conta não encontrada para este portador");
    }
    return contaPagamento;
  }

  private List<ContaPagamento> findContasPagamento(
      String documento, Integer idProc, Integer idInst) {
    return contaPagamentoService.findByCpf(documento, idProc, idInst);
  }

  public List<CredencialTransferencia> buscarCredenciaisPorDocumentoInstituicaoETipoPlataforma(
      String documento, Integer idInst, Integer idProdPlataforma, Long idContaOrigem) {

    return credencialRepository
        .buscarCredenciasPorDocumentoInstituicaoETipoProdPlataforma(
            idInst, idProdPlataforma, documento)
        .stream()
        .map(
            credencial -> {
              Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());
              ContaPagamento contaPagamento =
                  contaPagamentoService.findByIdNotNull(credencial.getIdConta());
              ProdutoInstituicao produtoInstituicao =
                  getProdutoInstituicaoNotNull(
                      contaPagamento, contaPagamento.getIdProdutoInstituicao());
              return CredencialTransferencia.builder()
                  .idConta(credencial.getIdConta())
                  .idCredencial(credencial.getIdCredencial())
                  .ultimos4digitos(credencial.getUltimos4Digitos())
                  .idProduto(Long.valueOf(produtoInstituicao.getId()))
                  .nomeProduto(produtoInstituicao.getDescProdInstituicao())
                  .nome(
                      pessoa == null ? credencial.getNomeImpresso() : pessoa.getNomePorTipoPessoa())
                  .build();
            })
        .collect(Collectors.toList());
  }

  public List<Credencial> findByIdCredencialIn(Collection<Long> ids) {
    return credencialRepository.findByIdCredencialIn(ids);
  }

  public List<Credencial> obterCredenciaisPortador(PortadorLogin userPortador) {
    List<Long> idCredenciaisPortador =
        ObjectUtil.numberListToLong(userPortador.getCredenciaisPortador());
    List<Credencial> credenciaisPortador;
    if (idCredenciaisPortador != null && !idCredenciaisPortador.isEmpty()) {
      credenciaisPortador = findByIdCredencialIn(idCredenciaisPortador);
    } else {
      credenciaisPortador =
          findCredenciaisByDocumentoAndInstituicao(
              userPortador.getIdProcessadora(),
              userPortador.getIdInstituicao(),
              userPortador.getCpf());
    }
    return credenciaisPortador;
  }

  public List<Credencial> obterCredenciaisCorporativo(CorporativoLogin corporativoLogin) {
    CorporativoResponsavel responsavel =
        this.corporativoService.findResponsavelAtivoByDocumento(
            corporativoLogin.getResponsavel().getDocumento());
    List<CorporativoResponsavelCredencial> responsavelCredenciais =
        responsavel.getCorporativoResponsavelCredencial();
    List<CorporativoResponsavelCredencial> responsavelCredenciaisAtivo =
        responsavelCredenciais.stream()
            .filter(x -> x.getDtHrFim() == null)
            .collect(Collectors.toList());
    List<Credencial> credencials = new ArrayList<>();
    if (responsavelCredenciaisAtivo.isEmpty()) {
      return credencials;
    }
    for (CorporativoResponsavelCredencial responsavelCredencial : responsavelCredenciaisAtivo) {
      Credencial credencial = this.findByIdCredencial(responsavelCredencial.getIdCredencial());
      credencials.add(credencial);
    }
    return credencials;
  }

  public PortadorCorporativo buscarInformacoesCorporativasPorCredencialESenha(
      String numeroCredencial, String pin) {
    Credencial credencial = null;
    GetCardResponse card = cardService.getToken(numeroCredencial.toUpperCase());

    if (card.getSuccess()) {
      credencial = findOneByTokenInterno(card.getCard().getToken());
    }

    if (credencial == null) {
      throw new GenericServiceException(
          "Credencial não encontrada para informações enviadas.", HttpStatus.UNAUTHORIZED);
    }

    ContaPagamento conta = contaPagamentoService.buscarPorContaId(credencial.getIdConta());

    if (conta == null) {
      throw new GenericServiceException(
          "Conta não encontrada para informações enviadas.", HttpStatus.UNAUTHORIZED);
    }

    travaServicosService.travaServicos(conta.getIdInstituicao(), Servicos.LOGIN_PORTADOR);

    String senha = getSenhaDescriptografadaDoJcardSemPadding(credencial);
    if (senha == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar a Senha.", HttpStatus.UNAUTHORIZED);
    }

    if (!senha.equalsIgnoreCase(pin)) {
      throw new GenericServiceException("Senha do cartão não confere.", HttpStatus.UNAUTHORIZED);
    }

    Pessoa pessoa = getPessoaByIdNotNull(credencial);

    GetCredencial getCredencial =
        loadPlastico(
            credencial, conta, pessoa, pessoa.getIdProcessadora(), pessoa.getIdInstituicao());

    GetSaldoConta saldoConta = contaPagamentoService.getSaldoConta(conta.getIdConta());
    BigDecimal saldo =
        saldoConta == null || saldoConta.getSaldoDisponivel() == null
            ? SALDO_PADRAO
            : saldoConta.getSaldoDisponivel();

    List<GetExtratoCredencial> extrato = getExtrato(credencial.getIdCredencial(), 30);

    List<RepresentanteLegal> representantes =
        representanteLegalRepository.findByIdConta(conta.getIdConta());

    return PortadorCorporativo.builder()
        .credencial(getCredencial)
        .saldo(saldo)
        .extrato(extrato)
        .representantes(representantes)
        .build();
  }

  public DadosSensiveisCredencial obterDadosSensiveisCredencialBitfy(Long idConta) {

    ContaPagamento conta = contaPagamentoService.findById(idConta);
    if (conta == null) {
      throw new GenericServiceException("Conta de idConta " + idConta + " não encontrada.");
    }

    if (!Constantes.ID_PRODUCAO_INSTITUICAO_BITFY.equals(conta.getIdInstituicao())) {
      throw new GenericServiceException("Conta não pertence a instituição Bitfy.");
    }

    return getDadosSensiveisCred(idConta);
  }

  public DadosSensiveisCredencial obterDadosSensiveisCredencialPelaConta(
      Long idConta, SecurityUser user) {

    if (!user.isNivelHierarquiaAllowed(HierarquiaType.INSTITUICAO.value())) {
      throw new GenericServiceException("Usuário não é do nivel instituição");
    }

    ContaPagamento conta = contaPagamentoService.findById(idConta);
    if (conta == null) {
      throw new GenericServiceException("Conta de idConta " + idConta + " não encontrada.");
    }

    if (user.getIdInstituicao() != null
        && !user.getIdInstituicao().equals(conta.getIdInstituicao())) {
      throw new GenericServiceException(
          "Usuário não tem acesso a conta da instituição:" + conta.getIdInstituicao());
    }

    return getDadosSensiveisCred(idConta);
  }

  public List<DadosSensiveisCredencial> obterDadosSensiveisCredenciaisPelaConta(
      Long idConta, SecurityUser user) {
    if (!user.isNivelHierarquiaAllowed(HierarquiaType.INSTITUICAO.value())) {
      throw new GenericServiceException("Usuário não é do nivel instituição");
    }
    ContaPagamento conta = contaPagamentoService.findById(idConta);
    if (user.getIdInstituicao() != null
        && !user.getIdInstituicao().equals(conta.getIdInstituicao())) {
      throw new GenericServiceException(
          "Usuário não tem acesso a conta da instituição:" + conta.getIdInstituicao());
    }

    return getDadosSensiveisCreds(idConta);
  }

  private DadosSensiveisCredencial getDadosSensiveisCred(Long idConta) {
    List<Credencial> credenciaisDaConta = credencialRepository.findByIdConta(idConta);
    if (credenciaisDaConta == null || credenciaisDaConta.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais.");
    }

    List<Credencial> credenciaisAtivasOuBloqueioOrigem =
        credenciaisDaConta.stream()
            .filter(
                x ->
                    x.getIdStatusV2().equals(Constantes.TIPO_STATUS_DESBLOQUEADO)
                        || x.getIdStatusV2().equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM))
            .collect(Collectors.toList());
    if (credenciaisAtivasOuBloqueioOrigem == null || credenciaisAtivasOuBloqueioOrigem.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais ativas");
    }

    CardResponse cardResponse =
        callGetDataFromPrintingJcard(credenciaisAtivasOuBloqueioOrigem.get(0));
    DadosSensiveisCredencial dadosSensiveisCredencial;
    try {
      dadosSensiveisCredencial =
          new DadosSensiveisCredencial(
              CriptoUtil.descriptografarDados(cardResponse.getPancripto()),
              CriptoUtil.descriptografarDados(cardResponse.getCvv2cripto()).substring(0, 3),
              credenciaisAtivasOuBloqueioOrigem
                  .get(0)
                  .getDataValidade()
                  .format(DateTimeFormatter.ofPattern("yyMM")),
              credenciaisAtivasOuBloqueioOrigem.get(0).getNomeImpresso(),
              credenciaisAtivasOuBloqueioOrigem.get(0).getIdCredencial(),
              (credenciaisAtivasOuBloqueioOrigem
                  .get(0)
                  .getIdStatusV2()
                  .equals(Constantes.TIPO_STATUS_DESBLOQUEADO)));
    } catch (Exception e) {
      log.error("Falha ao extrair dados sensiveis do cartao.");
      throw new GenericServiceException("Falha ao extrair dados sensiveis do cartao.", e);
    }

    return dadosSensiveisCredencial;
  }

  public DadosSensiveisCredencial getDadosSensiveisCred(Credencial credencial) {
    CardResponse cardResponse = callGetDataFromPrintingJcard(credencial);
    DadosSensiveisCredencial dadosSensiveisCredencial;
    try {
      dadosSensiveisCredencial =
          new DadosSensiveisCredencial(
              CriptoUtil.descriptografarDados(cardResponse.getPancripto()),
              CriptoUtil.descriptografarDados(cardResponse.getCvv2cripto()).substring(0, 3),
              credencial.getDataValidade().format(DateTimeFormatter.ofPattern("yyMM")),
              credencial.getNomeImpresso(),
              credencial.getIdCredencial(),
              (credencial.getStatus().equals(1) ? true : false));
    } catch (Exception e) {
      log.error("Falha ao extrair dados sensiveis do cartao.");
      throw new GenericServiceException("Falha ao extrair dados sensiveis do cartao.", e);
    }

    return dadosSensiveisCredencial;
  }

  private List<DadosSensiveisCredencial> getDadosSensiveisCreds(Long idConta) {
    List<Credencial> credenciaisDaConta = credencialRepository.findByIdConta(idConta);
    if (credenciaisDaConta == null || credenciaisDaConta.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais.");
    }

    List<DadosSensiveisCredencial> dadosSensiveisCredenciais = new ArrayList<>();

    for (Credencial credencial : credenciaisDaConta) {

      CardResponse cardResponse = callGetDataFromPrintingJcard(credencial);
      DadosSensiveisCredencial dadosSensiveisCredencial;
      try {
        dadosSensiveisCredencial =
            new DadosSensiveisCredencial(
                CriptoUtil.descriptografarDados(cardResponse.getPancripto()),
                CriptoUtil.descriptografarDados(cardResponse.getCvv2cripto()).substring(0, 3),
                credencial.getDataValidade().format(DateTimeFormatter.ofPattern("yyMM")),
                credencial.getNomeImpresso(),
                credencial.getIdCredencial(),
                (credencial.getIdStatusV2().equals(Constantes.TIPO_STATUS_DESBLOQUEADO)));
      } catch (Exception e) {
        log.error("Falha ao extrair dados sensiveis do cartao.");
        throw new GenericServiceException("Falha ao extrair dados sensiveis do cartao.", e);
      }
      dadosSensiveisCredenciais.add(dadosSensiveisCredencial);
    }

    return dadosSensiveisCredenciais;
  }

  public CardResponse callGetDataFromPrintingJcard(Credencial credencial) {
    JcardRequestEmbossing jcardRequest =
        new JcardRequestEmbossing(credencial.getTokenInterno(), "001");
    JcardResponseEmbossing responseJcard = new JcardResponseEmbossing();
    try {
      responseJcard = jcardService.doPost(jcardRequest, responseJcard, jcardUrl + URL);
    } catch (HttpClientErrorException | HttpServerErrorException e) {
      log.error(
          "Erro na chamada da API do Jcard "
              + jcardUrl
              + URL
              + ". Request: "
              + jcardRequest.toString()
              + " Response Message: "
              + e.getResponseBodyAsString());
      throw new GenericServiceException(
          "Erro na chamada da API do Jcard para obtencao dos dados sensiveis do cartao.",
          e,
          e.getStatusCode());
    }

    if (responseJcard.getSuccess()) {
      return responseJcard.getCard();
    } else {
      throw new JcardServiceException(
          "Erro na chamada da API do Jcard para obtencao dos dados sensiveis do cartao:"
              + responseJcard.getErrors());
    }
  }

  public Credencial getQuatroUltimosByTokenInternoTransacao(String token) {
    return credencialRepository.getCredencialPorToken(token);
  }

  public List<Credencial> recuperarCredenciaisPorIdConta(Long idConta) {
    return credencialRepository.recuperarCredenciaisPorIdConta(idConta);
  }

  @Transactional
  public Boolean alterarEstadoNFCCredencial(
      TrocarEstadoNFCCredencialRequest request, SecurityUserPortador userPortador) {
    Credencial credencial = getCredencialNotNull(request.getIdCredencial());
    contaPagamentoService.validaContaAtiva(userPortador);
    List<Credencial> credenciais = obterCredenciaisPortador(userPortador);
    validaAlgumaCredencialFisicaAtivaOuTemp(credenciais);

    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            credencial.getIdConta(), userPortador);
    return alterarEstadoNFCCredencial(credencial, request.getEstado(), conta);
  }

  @Transactional
  public Boolean alterarEstadoNFCCredencial(
      TrocarEstadoNFCCredencialRequest request, SecurityUserCorporativo userCorporativo) {
    Credencial credencial = getCredencialNotNull(request.getIdCredencial());
    contaPagamentoService.validaContaAtivaCorporativo(userCorporativo);
    List<Credencial> credenciais = obterCredenciaisCorporativo(userCorporativo);
    validaAlgumaCredencialFisicaAtivaOuTemp(credenciais);

    ContaPagamento conta =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
            credencial.getIdConta(), userCorporativo);
    return alterarEstadoNFCCredencial(credencial, request.getEstado(), conta);
  }

  @Transactional
  public Boolean alterarEstadoNFCCredencialIssuer(
      TrocarEstadoNFCCredencialRequest request, SecurityUser user) {
    Credencial credencial = getCredencialNotNull(request.getIdCredencial());
    return alterarEstadoNFCCredencialIssuer(credencial, request.getEstado(), user);
  }

  private Boolean alterarEstadoNFCCredencial(
      Credencial credencial, Integer estadoDestino, ContaPagamento conta) {

    boolean sucesso = true;

    if (!Constantes.CONTA_ATIVA.equals(conta.getIdStatusV2())) {
      throw new GenericServiceException(
          ConstantesErro.CTP_CONTA_PRECISA_ESTAR_DESBLOQUEADA.format("alterar esta configuração"));
    }

    if (!(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM.equals(credencial.getIdStatusV2())
        || Constantes.TIPO_STATUS_DESBLOQUEADO.equals(credencial.getIdStatusV2()))) {
      throw new GenericServiceException(
          ConstantesErro.CAR_STATUS_NAO_PERMITE_ALTERACAO.getMensagem());
    }

    if (!ESTADOS_NFC_DISPONIVEIS.contains(estadoDestino)) {
      throw new GenericServiceException(ConstantesErro.CAR_ESTADO_NFC_INEXISTENTE.getMensagem());
    }

    if (estadoDestino.equals(credencial.getStatusNfc())) {
      throw new GenericServiceException(ConstantesErro.CAR_ESTADO_NFC_JA_CONFIGURADA.getMensagem());
    }

    JcardResponse response = null;
    sucesso = alterarEstadoNFC(credencial, estadoDestino);
    if (sucesso) {
      response =
          cardService.changeNFCState(
              credencial.getTokenInterno(), NFCStateJcardEnum.getDescricaoPorCodigo(estadoDestino));
    }
    if (!sucesso || !response.getSuccess()) {
      throw new GenericServiceException(ConstantesErro.ERRO_AO_REALIZAR_OPERACAO.getMensagem());
    }

    return sucesso;
  }

  public Boolean alterarEstadoNFCCredencialIssuer(
      Credencial credencial, Integer estadoDestino, SecurityUser user) {

    ContaPagamento contaPagamento = contaPagamentoService.findByIdConta(credencial.getIdConta());
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, contaPagamento);

    boolean sucesso = true;

    JcardResponse response = null;
    sucesso = alterarEstadoNFC(credencial, estadoDestino);
    if (sucesso) {
      response =
          cardService.changeNFCState(
              credencial.getTokenInterno(), NFCStateJcardEnum.getDescricaoPorCodigo(estadoDestino));
    }
    if (!sucesso || !response.getSuccess()) {
      throw new GenericServiceException(ConstantesErro.ERRO_AO_REALIZAR_OPERACAO.getMensagem());
    }

    return sucesso;
  }

  private Boolean alterarEstadoNFC(Credencial credencial, Integer statusDestino) {
    credencial.setStatusNfc(statusDestino);
    credencial.setDtHrStatusNfc(LocalDateTime.now());
    credencial.setIdUsuarioManutencao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);
    return saveAndFlush(credencial) != null;
  }

  @Transactional
  public List<LogRegistroAlterarStatus> alterarStatusMultiplasCredenciais(
      List<LogRegistroAlterarStatus> logs, Integer idUsuario) {
    List<LogRegistroAlterarStatus> listResponse = new ArrayList<>();

    for (LogRegistroAlterarStatus log : logs) {
      listResponse.add(alterarStatusPorLogRegistro(log, idUsuario));
    }

    return listResponse;
  }

  @Transactional
  public LogRegistroAlterarStatus alterarStatusPorLogRegistro(
      LogRegistroAlterarStatus log, Integer idUsuario) {
    Credencial credencial = credencialRepository.findOneByIdCredencial(log.getIdCredencial());

    try {
      if (credencial == null) {
        throw new GenericServiceException("Não foi encontrada credencial para este ID.");
      } else {
        log.setStatusOriginal(credencial.getIdStatusV2());
      }
      Boolean sucesso =
          this.alterarStatusCredencial(
              log.getIdCredencial(), log.getStatusSolicitado(), idUsuario, false);
      if (sucesso) {
        log.setDataHoraProcessamento(LocalDateTime.now());
        log.setStatusRegistro(StatusRegistroAlterarEnum.APROVADO);
      }
    } catch (GenericServiceException e) {
      log.setDataHoraProcessamento(LocalDateTime.now());
      log.setStatusRegistro(StatusRegistroAlterarEnum.REJEITADO);
      log.setMsgErro(e.getMensagem());
    }
    return log;
  }

  public InputStream downloadBoletoCobrancaBancaria(Long idCobrancaBancaria) {
    return boletoSegundaViaService.downloadBoletoCobrancaBancaria(idCobrancaBancaria);
  }

  public void salvarCredencialAdicionalPDAF(
      PessoaAdicionalPDAFRequest adicional,
      ContaPagamento conta,
      Pessoa pessoa,
      SecurityUser user) {

    ContaPessoa contaPessoaRequest = new ContaPessoa();
    contaPessoaRequest.setIdPessoa(pessoa.getIdPessoa());
    contaPessoaRequest.setIdConta(conta.getIdConta());
    contaPessoaRequest.setIdTitularidade(ADICIONAL);

    contaPessoaService.vincularConta(contaPessoaRequest);

    GerarCredencialRequest credencialRequest = new GerarCredencialRequest();

    credencialRequest.setAdicional(true);
    credencialRequest.setIdPessoa(pessoa.getIdPessoa());
    credencialRequest.setIdConta(conta.getIdConta());
    credencialRequest.setIntegracao(false);
    credencialRequest.setVirtual(false);
    credencialRequest.setIdUsuario(user.getIdUsuario());

    geradorCredencialService.gerarCredencialAdicional(credencialRequest, BigDecimal.ZERO);
  }

  public List<InformacoesCredencialContaVO>
      findContasByDocumentoAndUltimos4DigitosNaoVirtualMulticontas(
          String documentoEmClaro,
          Integer idInstituicao,
          Integer idProcessadora,
          Integer ultimosDigitos) {
    return credencialRepository.findContasByDocumentoAndUltimos4DigitosNaoVirtualMulticontas(
        documentoEmClaro, idInstituicao, idProcessadora, ultimosDigitos);
  }

  public String buscarSenhaCredencialURA(Credencial credencial) {
    return this.getSenhaCredencialCriptografada(credencial.getIdCredencial());
  }

  /**
   * Atualiza o status das credenciais virtuais para o status fornecido.
   *
   * @param idsCredenciaisVirtuais Lista de IDs das credenciais virtuais a serem atualizadas.
   * @param status Novo status a ser aplicado às credenciais virtuais.
   */
  @Transactional
  public void atualizarStatusCredenciaisVirtuais(
      List<Long> idsCredenciaisVirtuais, Integer status) {
    if (idsCredenciaisVirtuais == null || idsCredenciaisVirtuais.isEmpty()) {
      throw new GenericServiceException("Nenhuma credencial virtual fornecida para atualização.");
    }
    credencialRepository.atualizarStatusCredenciaisVirtuais(idsCredenciaisVirtuais, status);
  }

  public Credencial alterarCredencialCorporativo(
      ContaPessoa conta, SecurityUser user, String token, String senhaUsuarioLogado) {

    if (!isPassMatch(senhaUsuarioLogado + user.getCpf(), user)) {
      throw new GenericServiceException("Senha do usuário logado não confere.");
    }

    List<Long> idsCredenciaisVirtuais =
        conta.getContaPagamento().getCredenciais().stream()
            .filter(credencial -> credencial.getVirtual())
            .map(Credencial::getIdCredencial)
            .collect(Collectors.toList());

    List<Credencial> credenciaisFisicas =
        conta.getContaPagamento().getCredenciais().stream()
            .filter(credencial -> !credencial.getVirtual())
            .collect(Collectors.toList());

    if (credenciaisFisicas == null || credenciaisFisicas.isEmpty()) {
      throw new GenericServiceException("Nenhuma credencial física encontrada.");
    }
    Credencial credencialFisica = credenciaisFisicas.get(0);

    if (idsCredenciaisVirtuais != null && !idsCredenciaisVirtuais.isEmpty()) {
      atualizarStatusCredenciaisVirtuais(idsCredenciaisVirtuais, CANCELADO_INSTITUICAO);
    }

    // Gera uma senha aleatória de 4 dígitos sem números repetidos e sem sequência numérica
    Random random = new Random();
    String senhaAleatoria;
    while (true) {
      int senha = 1000 + random.nextInt(9000);
      senhaAleatoria = String.valueOf(senha);
      if (senhaAleatoria.chars().distinct().count() == 4
          && !senhaAleatoria.matches(
              "0123|1234|2345|3456|4567|5678|6789|7890|0987|9876|8765|7654|6543|5432|4321|3210")) {
        break;
      }
    }

    credencialFisica =
        recadastraPinCredencialCorporativo(credencialFisica, senhaAleatoria, token, user);

    enviarSenhaPorSms(credencialFisica);

    return credencialFisica;
  }

  public GetCredenciaisResponse getCorporativoCredenciaisDisponiveis(
      CredenciaisDisponiveisDTO credenciaisDisponiveisDTO,
      SecurityUserCorporativo userCorporativo) {
    return getCorporativoCredenciaisViaStatus(credenciaisDisponiveisDTO, userCorporativo);
  }

  public GetCredenciaisResponse getCorporativoCredenciaisViaStatus(
      CredenciaisDisponiveisDTO credenciaisDisponiveisDTO,
      SecurityUserCorporativo userCorporativo) {

    CorporativoResponsavel responsavel =
        this.corporativoService.findResponsavelAtivoByDocumento(userCorporativo.getUsername());

    List<CorporativoResponsavelCredencial> responsavelCredenciais =
        responsavel.getCorporativoResponsavelCredencial();
    List<CorporativoResponsavelCredencial> responsavelCredenciaisAtivas =
        responsavelCredenciais.stream()
            .filter(x -> x.getDtHrFim() == null)
            .collect(Collectors.toList());

    if (responsavelCredenciaisAtivas.isEmpty()) {
      throw new GenericServiceException("Nenhuma credencial encontrada.");
    }

    List<GetCredencial> credencials = new ArrayList<>();
    for (CorporativoResponsavelCredencial responsavelCredencialAtiva :
        responsavelCredenciaisAtivas) {
      Credencial credencial = this.findByIdCredencial(responsavelCredencialAtiva.getIdCredencial());
      ContaPagamento contaPagamento =
          this.contaPagamentoService.findByIdConta(credencial.getIdConta());
      ProdutoInstituicao produtoInstituicao =
          getProdutoInstituicaoNotNull(contaPagamento, contaPagamento.getIdProdutoInstituicao());
      List<Plastico> plastico =
          getPlasticoNotNull(
              credenciaisDisponiveisDTO.getIdProc(),
              credenciaisDisponiveisDTO.getIdInst(),
              contaPagamento.getIdProdutoInstituicao());
      GetCredencial getCredencial =
          prepareGetCredencial(
              contaPagamento,
              credencial,
              produtoInstituicao,
              plastico.get(0),
              responsavel.getNome(),
              responsavel.getEmail());
      credencials.add(getCredencial);
    }
    GetCredenciaisResponse getCredenciaisResponse = new GetCredenciaisResponse(credencials);
    this.credencialUtil.setSaldoLimiteNasCredenciais(getCredenciaisResponse);
    return getCredenciaisResponse;
  }
}
