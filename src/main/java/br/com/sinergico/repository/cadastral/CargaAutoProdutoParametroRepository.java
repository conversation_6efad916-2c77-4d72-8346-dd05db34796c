package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.CargaAutoProdutoParametro;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CargaAutoProdutoParametroRepository
    extends JpaRepository<CargaAutoProdutoParametro, Long> {

  CargaAutoProdutoParametro findByIdProdInstituicaoAndIdParametroCargaAndIdStatus(
      Integer idProdInstituicao, Integer idParametro, Integer status);

  @Query(
      "select capp "
          + "from CargaAutoProdutoParametro capp "
          + "where capp.idProdInstituicao = :idProdInstituicao "
          + "order by capp.id asc ")
  List<CargaAutoProdutoParametro> findByIdProdInstituicao(
      @Param("idProdInstituicao") Integer idProdInstituicao);

  @Query(
      value =
          " select exists \n "
              + " ( select 1 \n "
              + " from cadastral.carga_auto_produto_parametro capp2 "
              + " where capp2.id_prod_instituicao = :idProdInstituicao ) \n ",
      nativeQuery = true)
  Boolean existsByProduto(@Param("idProdInstituicao") Integer idProdInstituicao);

  @Query(
      value =
          " select exists ( \n "
              + "  select 1 \n "
              + "  from cadastral.carga_auto_produto_parametro capp \n "
              + "  where true \n "
              + "    and capp.id_prod_instituicao = :idProdInstituicao \n "
              + "    and capp.id_parametro_carga = 4 \n "
              + "    and capp.vl_parametro = 'true' \n "
              + ") \n ",
      nativeQuery = true)
  Boolean existsLiberacaoManualByProduto(@Param("idProdInstituicao") Integer idProdInstituicao);

  Optional<CargaAutoProdutoParametro> findById(Long id);

  @Query(
      "select capp.vlParametro "
          + "from CargaAutoProdutoParametro as capp "
          + "inner join capp.cargaAutoParametros as cap "
          + "where cap.nmParametro = 'ID_CONTA' "
          + "and capp.idProdInstituicao = ( "
          + "select pll.idProdutoInstituicao "
          + "from PreLancamentoLote as pll "
          + "inner join pll.faturaB2B as bbf "
          + "where pll.idFatura = :idFatura) ")
  String buscarFaturasIntegracao(@Param("idFatura") Long idFatura);
}
