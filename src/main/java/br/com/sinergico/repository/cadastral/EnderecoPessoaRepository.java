package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.EnderecoPessoa;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EnderecoPessoaRepository extends JpaRepository<EnderecoPessoa, Long> {

  EnderecoPessoa findOneByIdPessoaAndIdTipoEnderecoAndStatus(
      Long idPessoa, Integer idTipoEndereco, Integer status);

  List<EnderecoPessoa> findByIdPessoaAndIdTipoEnderecoAndStatus(
      Long idPessoa, Integer idTipoEndereco, Integer status);

  List<EnderecoPessoa> findByIdPessoaAndStatus(Long idPessoa, Integer status);

  List<EnderecoPessoa> findByIdPessoa(Long idPessoa);

  List<EnderecoPessoa> findByIdPessoaIn(List<Long> idsPessoas);

  @Query(
      "select ep "
          + "from EnderecoPessoa as ep "
          + "inner join ep.pessoa as p "
          + "inner join p.contasPessoa as cp "
          + "inner join cp.contaPagamento as cpag "
          + "where cpag.idConta = :idConta and ep.idTipoEndereco = :idTipoEndereco and cp.idTitularidade = 1 "
          + "and ep.status = 1 order by ep.dtHrInclusao desc ")
  EnderecoPessoa findEnderecoPessoaByIdContaAndTipoEndereco(
      @Param("idConta") Long idConta, @Param("idTipoEndereco") Integer idTipoEndereco);

  List<EnderecoPessoa> findByIdPessoaInAndStatus(List<Long> idPessoa, Integer status);
}
