package br.com.sinergico.facade.cadastral;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.RecadastrarPinRequest;
import br.com.json.bean.cadastral.RecadastrarPinTokenRequest;
import br.com.json.bean.cadastral.TrocarEstadoNFCCredencialRequest;
import br.com.json.bean.transacional.GetExtratoCredencialInmais;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialContaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.transacional.MigracaoTransacaoInmaisService;
import br.com.sinergico.util.AnoMesUtil;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/** Created by denniscremasco on 27/04/17. */
@Service
public class CredencialFacade extends UtilController {

  private static final String INMAIS_STATUS = "680";

  private static final String INMAIS_STATUS_ACUMULO = "542";

  private static final String INMAIS_STATUS_MIGRACAO = "730";

  private static final String ACUMULO = "ACUMULADO";
  private static final String RESGATE = "RESGATE";

  private static final Integer STATUS_CANCELADO = 30;
  private static final Integer STATUS_PERDA = 32;
  private static final Integer STATUS_ROUBO = 33;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private MigracaoTransacaoInmaisService migracaoTransacaoInmaisService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;
  @Autowired private CredencialContaService credencialContaService;
  @Autowired @Lazy private CredencialService credencialService;

  @Transactional
  public Boolean recadastraPin(
      RecadastrarPinRequest req, SecurityUserPortador user, HttpServletRequest request) {

    Credencial credencial;

    // Detectar se é validação por token ou PIN
    if (req.getChaveExterna() != null && req.getToken() != null) {
      // Validação por token - usar método unificado
      credencial =
          getCredencialService()
              .recadastraPin(
                  req.getNovaSenha(),
                  req.getConfirmacaoSenha(),
                  req.getIdCredencial(),
                  null, // senha não é necessária para validação por token
                  user.getIdLogin().intValue(),
                  getTokenJWT(request),
                  true, // origemPortador = true
                  false, // isCardPin = false
                  req.getChaveExterna(),
                  req.getToken());
    } else {
      // Validação por PIN - lógica original
      credencial =
          getCredencialService()
              .recadastraPin(
                  req.getNovaSenha(),
                  req.getConfirmacaoSenha(),
                  req.getIdCredencial(),
                  req.getSenhaUsuario(),
                  user.getIdLogin().intValue(),
                  getTokenJWT(request),
                  true);
    }

    if (credencial != null
        && CredencialService.BLOQUEIO_DE_ORIGEM.equals(credencial.getStatus())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(user.getIdInstituicao())) {
      contaPagamentoService.desbloquearCredencial(
          req.getIdCredencial(), Constantes.ID_USUARIO_INCLUSAO_PORTADOR);
    }
    return credencial != null;
  }

  @Transactional
  public Boolean recadastraPinComPinCredencial(
      RecadastrarPinRequest req, SecurityUserPortador user, HttpServletRequest request) {
    Credencial credencial =
        getCredencialService()
            .recadastraPin(
                req.getNovaSenha(),
                req.getConfirmacaoSenha(),
                req.getIdCredencial(),
                req.getSenhaCartao(),
                user.getIdLogin().intValue(),
                getTokenJWT(request),
                true,
                true);
    if (credencial.getStatus().equals(CredencialService.BLOQUEIO_DE_ORIGEM)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(user.getIdInstituicao())) {
      contaPagamentoService.desbloquearCredencial(
          req.getIdCredencial(), Constantes.ID_USUARIO_INCLUSAO_PORTADOR);
    }
    return credencial != null;
  }

  public List<GetExtratoCredencialInmais> getExtratoInmaisByAnoMes(
      SecurityUserPortador user, Long idConta, Integer anoMes) {
    return getExtratoInmaisByAnoMes(user, idConta, anoMes, false);
  }

  public List<GetExtratoCredencialInmais> getExtratoInmaisByAnoMes(
      SecurityUserPortador user, Long idConta, Integer anoMes, boolean maisRecente) {
    List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList = new ArrayList<>();
    if (anoMes == AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {

      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpf(
                  user.getCpf(),
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                  DateUtil.getCalendarUltimaMigracaoINMAIS_DATA().getTime())));
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idConta,
                      DateUtil.getCalendarMigracaoTerminadaINMAIS_DATA().getTime(),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                      null)));
    } else if (anoMes > AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {

      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idConta,
                      AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                      null)));
    } else {
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpf(
                  user.getCpf(),
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                  AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes))));
    }

    getExtratoCredencialInmaisList =
        removeStatusFromExtrato(getExtratoCredencialInmaisList, INMAIS_STATUS_MIGRACAO);
    if (maisRecente) {
      Collections.sort(
          getExtratoCredencialInmaisList,
          new Comparator<GetExtratoCredencialInmais>() {
            @Override
            public int compare(
                GetExtratoCredencialInmais extrato1, GetExtratoCredencialInmais extrato2) {

              return extrato1.getDataTransacao().isBefore(extrato2.getDataTransacao()) ? 1 : -1;
            }
          });
    }

    return getExtratoCredencialInmaisList;
  }

  public List<GetExtratoCredencialInmais> getExtratoInmaisByAnoMes(
      SecurityUserPortador user,
      Long idConta,
      Integer anoMes,
      String tipoPontos,
      boolean maisRecente) {
    List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList = new ArrayList<>();
    if (anoMes == AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {

      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpfAndTipoPontos(
                  user.getCpf(),
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                  DateUtil.getCalendarUltimaMigracaoINMAIS_DATA().getTime(),
                  tipoPontos)));
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idConta,
                      DateUtil.getCalendarMigracaoTerminadaINMAIS_DATA().getTime(),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                      null)));
    } else if (anoMes > AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {
      getExtratoCredencialInmaisList =
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idConta,
                      AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                      null));
    } else {
      getExtratoCredencialInmaisList =
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpfAndTipoPontos(
                  user.getCpf(),
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                  AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                  tipoPontos));
    }
    getExtratoCredencialInmaisList =
        removeStatusFromExtrato(getExtratoCredencialInmaisList, INMAIS_STATUS_MIGRACAO);

    if (Boolean.TRUE.equals(maisRecente)) {
      getExtratoCredencialInmaisList.sort(
          Comparator.comparing(GetExtratoCredencialInmais::getDataTransacao).reversed());
    } else {
      getExtratoCredencialInmaisList.sort(
          Comparator.comparing(GetExtratoCredencialInmais::getDataTransacao));
    }
    return getExtratoCredencialInmaisList;
  }

  public List<GetExtratoCredencialInmais> getExtratoInmaisByAnoMesAndStatusFixo(
      SecurityUserPortador user, Long idConta, Integer anoMes, boolean maisRecente) {
    return filterExtratoByStatus(
        getExtratoInmaisByAnoMes(user, idConta, anoMes, RESGATE, maisRecente), INMAIS_STATUS);
  }

  public List<GetExtratoCredencialInmais> getExtratoInmaisAcumuloByAnoMesAndStatusFixo(
      SecurityUserPortador user, Long idConta, Integer anoMes, boolean maisRecente) {
    return filterExtratoByStatus(
        getExtratoInmaisByAnoMes(user, idConta, anoMes, ACUMULO, maisRecente),
        INMAIS_STATUS_ACUMULO);
  }

  private List<GetExtratoCredencialInmais> removeStatusFromExtrato(
      List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList, String STATUS) {
    List<GetExtratoCredencialInmais> retorno = new ArrayList<>();
    for (GetExtratoCredencialInmais item : getExtratoCredencialInmaisList) {
      if (item.getStatusTransacao() == null || !item.getStatusTransacao().equals(STATUS)) {
        retorno.add(item);
      }
    }
    return retorno;
  }

  private List<GetExtratoCredencialInmais> filterExtratoByStatus(
      List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList, String STATUS) {
    List<GetExtratoCredencialInmais> retorno = new ArrayList<>();
    for (GetExtratoCredencialInmais item : getExtratoCredencialInmaisList) {
      if (item.getStatusTransacao() == null || item.getStatusTransacao().equals(STATUS)) {
        retorno.add(item);
      }
    }
    return retorno;
  }

  public List<GetExtratoCredencialInmais> getExtratoInmaisByAnoMesADM(
      String documento, Long idConta, Integer anoMes, SecurityUser user, Boolean maisRecente) {

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(idConta);

    if (conta == null) {
      throw new GenericServiceException("Não foi possível encontrar a conta: " + idConta);
    }

    if (user != null) {
      consultaRestritaService.checaPrivilegio(conta, user);
    }

    List<Credencial> credenciais =
        credencialService.recuperarCredenciaisPorIdConta(conta.getIdConta());

    Long idCredencial =
        credenciais.stream()
            .sorted(
                new Comparator<Credencial>() {

                  @Override
                  public int compare(Credencial o1, Credencial o2) {
                    return o1.getCsn().compareTo(o2.getCsn());
                  }
                })
            .findFirst()
            .get()
            .getIdCredencial();

    List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList = new ArrayList<>();
    if (anoMes == AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {

      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpf(
                  documento,
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                  DateUtil.getCalendarUltimaMigracaoINMAIS_DATA().getTime())));
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idCredencial,
                      DateUtil.getCalendarMigracaoTerminadaINMAIS_DATA().getTime(),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                      null)));
    } else if (anoMes > AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idCredencial,
                      AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes),
                      idConta)));
    } else {
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpf(
                  documento,
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(anoMes),
                  AnoMesUtil.getUltimoDiaDateFromAnoMes(anoMes))));
    }
    getExtratoCredencialInmaisList =
        removeStatusFromExtrato(getExtratoCredencialInmaisList, INMAIS_STATUS_MIGRACAO);

    if (Boolean.TRUE.equals(maisRecente)) {
      getExtratoCredencialInmaisList.sort(
          Comparator.comparing(GetExtratoCredencialInmais::getDataTransacao).reversed());
    } else {
      getExtratoCredencialInmaisList.sort(
          Comparator.comparing(GetExtratoCredencialInmais::getDataTransacao));
    }
    return getExtratoCredencialInmaisList;
  }

  @Async
  public void enviarSMSSenhaCredenciaisCriadasByProposta(
      Long idConta, Integer idProcessadora, Integer idInstituicao) {

    try {
      Thread.sleep(5000);
      // Verifica se a instituição permite envio de SMS
      if (hierarquiaInstituicaoService.instituicaoPermiteEnvioSMS(idProcessadora, idInstituicao)) {
        getCredencialService()
            .enviarSMSSenhaCredenciaisCriadasByProposta(idConta, idProcessadora, idInstituicao);
      } else {
        System.out.println("A instituição: " + idInstituicao + " não permite envio de SMS!");
      }
    } catch (InterruptedException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    }
  }

  public Credencial buscarCredencialMaisRecente(
      Long idConta, Long idPessoa, Integer idTitularidade) {
    return getCredencialService().buscarCredencialMaisRecente(idConta, idPessoa, idTitularidade);
  }

  public Credencial save(Credencial c) {
    return getCredencialService().save(c);
  }

  public Credencial findById(Long idCredencial) {
    return getCredencialService().findById(idCredencial);
  }

  public Credencial findOneByTokenInternoAndIdPessoa(String tokenInterno, Long idPessoa) {
    return getCredencialService().findOneByTokenInternoAndIdPessoa(tokenInterno, idPessoa);
  }

  public Credencial findOneByTokenInterno(String tokenInterno) {
    return getCredencialService().findOneByTokenInterno(tokenInterno);
  }

  public List<Credencial> findByIdContaPagamentoAndIdPessoa(
      String idContaPagamento, Long idPessoa) {
    return getCredencialService().findByIdContaPagamentoAndIdPessoa(idContaPagamento, idPessoa);
  }

  public List<Credencial> findByDataHoraInclusaoGreaterThan(LocalDateTime dataInicial) {
    return getCredencialService().findByDataHoraInclusaoGreaterThan(dataInicial);
  }

  public Boolean validarPin(String senhaCredencial, Long idCredencial, String tokenJWT) {
    return getCredencialService().validarPin(senhaCredencial, idCredencial, tokenJWT);
  }

  public List<Credencial> findByIdPessoa(Long idPessoa) {
    return getCredencialService().findByIdPessoa(idPessoa);
  }

  public String descriptografarCredencialExterna(String idCredencialExterna, String aliasKey) {
    return getCredencialService().descriptografarCredencialExterna(idCredencialExterna, aliasKey);
  }

  public List<Credencial> findByIdConta(Long idConta) {
    return getCredencialService().findByIdContaInCredencialConta(idConta);
  }

  /**
   * Metodo resposavel por alterar status da credencial
   *
   * @param idCredencial
   * @param statusDestino
   * @param idUsuario
   * @return true caso sucesso e false caso não sucesso
   */
  public Boolean alterarStatusCredencial(
      Long idCredencial, Integer statusDestino, Integer idUsuario, Boolean isAutoAtendimento) {
    return getCredencialService()
        .alterarStatusCredencial(idCredencial, statusDestino, idUsuario, isAutoAtendimento);
  }

  public Boolean avisarPerda(
      Long idCredencial, Integer idUsuarioManutencao, Boolean isAutoAtendimento) {
    return getCredencialService()
        .alterarStatusCredencial(
            idCredencial, STATUS_PERDA, idUsuarioManutencao, isAutoAtendimento);
  }

  public Boolean avisarRoubo(
      Long idCredencial, Integer idUsuarioManutencao, Boolean isAutoAtendimento) {
    return getCredencialService()
        .alterarStatusCredencial(
            idCredencial, STATUS_ROUBO, idUsuarioManutencao, isAutoAtendimento);
  }

  public Boolean cancelarCredencial(
      Long idCredencial, Integer idUsuarioManutencao, Boolean isAutoAtendimento) {
    return getCredencialService()
        .alterarStatusCredencial(
            idCredencial, STATUS_CANCELADO, idUsuarioManutencao, isAutoAtendimento);
  }

  /**
   * Metodo resposavel por alterar status da credencial via B2B
   *
   * @param idCredencial
   * @param statusDestino
   * @param idUsuario
   * @return true caso sucesso e false caso não sucesso
   */
  public Boolean alterarStatusCredencialB2b(
      Long idCredencial, Integer statusDestino, Integer idUsuario) {
    return getCredencialService()
        .alterarStatusCredencialB2b(idCredencial, statusDestino, idUsuario);
  }

  public Credencial buscarCredencialParaLancamentoManual(Long idConta) {
    return getCredencialService().buscarCredencialParaLancamentoManual(idConta);
  }

  public Boolean alterarEstadoNFCCredencial(
      TrocarEstadoNFCCredencialRequest request, SecurityUserPortador userPortador) {
    return getCredencialService().alterarEstadoNFCCredencial(request, userPortador);
  }

  public Boolean alterarEstadoNFCCredencial(
      TrocarEstadoNFCCredencialRequest request, SecurityUserCorporativo userCorporativo) {
    return getCredencialService().alterarEstadoNFCCredencial(request, userCorporativo);
  }

  public Boolean alterarEstadoNFCCredencialIssuer(
      TrocarEstadoNFCCredencialRequest request, SecurityUser user) {
    return getCredencialService().alterarEstadoNFCCredencialIssuer(request, user);
  }

  public Long checaValidacaoFacialCaf(Credencial credencial) {
    return getCredencialService().checaReconhecimentoFacialCaf(credencial);
  }

  public void efetivaValidacaoFacialCaf(Long idValidacao) {
    getCredencialService().efetivaReconhecimentoFacialCaf(idValidacao);
  }
}
