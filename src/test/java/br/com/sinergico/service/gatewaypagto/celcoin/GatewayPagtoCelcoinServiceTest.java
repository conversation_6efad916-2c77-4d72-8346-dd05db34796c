package br.com.sinergico.service.gatewaypagto.celcoin;

import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.when;

import br.com.BaseControllerTest;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarLinhaDigitavelTitulo;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarPagamentoLinhaDigitavelResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.transacional.ConsultaPagamentoDTO;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.transacional.PagamentoConsultaStatusRendimentoResponse;
import br.com.sinergico.celcoin.CelcoinService;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.service.gatewaypagto.GatewayPagtoExternoService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.PagamentoVO;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
class GatewayPagtoCelcoinServiceTest extends BaseControllerTest {

  @Mock private InstituicaoPixService mockInstituicaoPixService;

  @Mock private BancoRendimentoClient mockBancoRendimentoClient;

  @Mock private GatewayPagtoExternoService mockGatewayPagtoExternoService;

  @Mock private LogPagtoTituloValidacaoRepository mockLogPagtoValidacaoRepository;

  @Mock private RestTemplate mockRestTemplate;

  @Mock private CelcoinService mockCelcoinService;

  @InjectMocks private GatewayPagtoCelcoinService gatewayPagtoCelcoinService;

  @Captor private ArgumentCaptor<String> urlCaptor;

  @BeforeEach
  void setUp() {
    ReflectionTestUtils.setField(
        gatewayPagtoCelcoinService, "urlBancoRendimento", "http://localhost:8080");
  }

  @Test
  void deveConsultarStatusPagamentoUsandoUrlV2()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    Method metodoPrivado =
        GatewayPagtoCelcoinService.class.getDeclaredMethod(
            "consultarStatusPagamento", PagamentoVO.class);
    metodoPrivado.setAccessible(true);

    InstituicaoPix chaveInstituicao = new InstituicaoPix();
    chaveInstituicao.setAgencia("1234");
    chaveInstituicao.setConta("4321");
    chaveInstituicao.setIdInstituicao(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);

    PagamentoVO pagamentoVO = new PagamentoVO();
    pagamentoVO.setProtocoloIdRendimento("PROTOCOLO_TESTE");

    given(
            mockInstituicaoPixService.findFirstByIdInstituicaoOrderById(
                Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS))
        .willReturn(chaveInstituicao);

    given(mockBancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao()))
        .willReturn("TOKEN_FAKE");

    given(mockGatewayPagtoExternoService.getHttpHeaders(any(), any()))
        .willReturn(new HttpHeaders());

    PagamentoConsultaStatusRendimentoResponse bodyMock =
        new PagamentoConsultaStatusRendimentoResponse();
    bodyMock.setIsSuccess(true);
    bodyMock.setIsFailure(false);
    ResponseEntity<PagamentoConsultaStatusRendimentoResponse> responseEntityMock =
        new ResponseEntity<>(bodyMock, HttpStatus.OK);

    given(
            mockRestTemplate.exchange(
                urlCaptor.capture(),
                eq(HttpMethod.GET),
                any(HttpEntity.class),
                eq(PagamentoConsultaStatusRendimentoResponse.class)))
        .willReturn(responseEntityMock);

    Object retorno = metodoPrivado.invoke(gatewayPagtoCelcoinService, pagamentoVO);
    PagamentoConsultaStatusRendimentoResponse resposta =
        (PagamentoConsultaStatusRendimentoResponse) retorno;

    assertNotNull(resposta, "A resposta não deveria ser nula");
    assertTrue(resposta.getIsSuccess(), "Resposta simulada deveria ter isSuccess=true");

    String urlUsada = urlCaptor.getValue();
    assertTrue(urlUsada.contains("/v2/"), () -> "A URL não contém '/v2/'. URL atual: " + urlUsada);
  }

  @Test
  void consultarLinhaDigitavelCelcoinV5() throws Exception {
    ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo =
        new ConsultarLinhaDigitavelTitulo();
    consultarLinhaDigitavelTitulo.setLinhaDigitavel("9000000000");
    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdConta(999999L);
    LogPagtoTituloValidacao logPagtoTituloValidacao = new LogPagtoTituloValidacao();
    logPagtoTituloValidacao.setIdConta(999999L);
    logPagtoTituloValidacao.setIdLogPagtoTitulo(999999L);

    when(mockLogPagtoValidacaoRepository.save(logPagtoTituloValidacao))
        .thenReturn(logPagtoTituloValidacao);

    ConsultarPagamentoLinhaDigitavelResponse consultarPagamentoLinhaDigitavelResponse =
        new ConsultarPagamentoLinhaDigitavelResponse();
    consultarPagamentoLinhaDigitavelResponse.setErrorCode("000");
    consultarPagamentoLinhaDigitavelResponse.setStatus("0");
    consultarPagamentoLinhaDigitavelResponse.setType(1);
    consultarPagamentoLinhaDigitavelResponse.setValue(BigDecimal.ONE);
    when(mockCelcoinService.authorizeBillPayments(any(ConsultaPagamentoDTO.class)))
        .thenReturn(consultarPagamentoLinhaDigitavelResponse);

    gatewayPagtoCelcoinService.consultarLinhaDigitavelCelcoinV5(
        consultarLinhaDigitavelTitulo, contaPagamento, 999999L);
  }

  @Test
  void consultarLinhaDigitavelCelcoinV5WithResultNull() throws Exception {
    ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo =
        new ConsultarLinhaDigitavelTitulo();
    consultarLinhaDigitavelTitulo.setLinhaDigitavel("9000000000");
    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdConta(999999L);
    LogPagtoTituloValidacao logPagtoTituloValidacao = new LogPagtoTituloValidacao();
    logPagtoTituloValidacao.setIdConta(999999L);
    logPagtoTituloValidacao.setIdLogPagtoTitulo(999999L);

    when(mockLogPagtoValidacaoRepository.save(logPagtoTituloValidacao))
        .thenReturn(logPagtoTituloValidacao);

    when(mockCelcoinService.authorizeBillPayments(any(ConsultaPagamentoDTO.class)))
        .thenReturn(null);

    gatewayPagtoCelcoinService.consultarLinhaDigitavelCelcoinV5(
        consultarLinhaDigitavelTitulo, contaPagamento, 999999L);
  }

  @Test
  void consultarLinhaDigitavelCelcoinV5WithGenericExpection() throws Exception {
    ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo =
        new ConsultarLinhaDigitavelTitulo();
    consultarLinhaDigitavelTitulo.setLinhaDigitavel("9000000000");
    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdConta(999999L);
    LogPagtoTituloValidacao logPagtoTituloValidacao = new LogPagtoTituloValidacao();
    logPagtoTituloValidacao.setIdConta(999999L);
    logPagtoTituloValidacao.setIdLogPagtoTitulo(999999L);

    when(mockLogPagtoValidacaoRepository.save(logPagtoTituloValidacao))
        .thenReturn(logPagtoTituloValidacao);

    ConsultarPagamentoLinhaDigitavelResponse consultarPagamentoLinhaDigitavelResponse =
        new ConsultarPagamentoLinhaDigitavelResponse();
    consultarPagamentoLinhaDigitavelResponse.setErrorCode("1");
    consultarPagamentoLinhaDigitavelResponse.setStatus("1");
    consultarPagamentoLinhaDigitavelResponse.setType(1);
    consultarPagamentoLinhaDigitavelResponse.setValue(BigDecimal.ONE);
    when(mockCelcoinService.authorizeBillPayments(any(ConsultaPagamentoDTO.class)))
        .thenReturn(consultarPagamentoLinhaDigitavelResponse);

    // Run the test
    assertThatThrownBy(
            () ->
                gatewayPagtoCelcoinService.consultarLinhaDigitavelCelcoinV5(
                    consultarLinhaDigitavelTitulo, contaPagamento, 999999L))
        .isInstanceOf(GenericServiceException.class);
  }
}
