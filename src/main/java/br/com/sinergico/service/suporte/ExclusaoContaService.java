package br.com.sinergico.service.suporte;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.suporte.ExclusaoConta;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.facade.cadastral.ContaPagamentoFacade;
import br.com.sinergico.repository.suporte.ExclusaoContaRepository;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.AlterarStatusExclusaoVO;
import br.com.sinergico.vo.ExclusaoContaVO;
import br.com.sinergico.vo.SolicitacaoCancelamentoVO;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Comparator;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class ExclusaoContaService {

  // Status da Exclusão
  private static final int SOLICITACAO_EXCLUSAO_ABERTA = 1;
  private static final int SOLICITACAO_EXCLUSAO_CONFIRMADA = 2;
  private static final int SOLICITACAO_EXCLUSAO_RECUSADA = 3;

  // Status da Solicitação
  private static final int AGUARDANDO_APROVACAO = 1;
  private static final int SOLICITACAO_APROVADA = 30;

  // Status da Conta
  private static final int STATUS_DESTINO_JCARD_BLOQUEADO = 9;
  private static final int STATUS_CONTA_BLOQUEADA = 30;

  @Autowired ExclusaoContaRepository exclusaoContaRepository;

  @Autowired ContaPagamentoService contaPagamentoService;

  @Autowired ContaPagamentoFacade contaPagamentoFacade;

  @Autowired PessoaService pessoaService;

  @Autowired TravaServicosService travaServicosService;

  public ExclusaoConta validarExclusaoConta(
      ExclusaoContaVO exclusaoContaVO, SecurityUserPortador userPortador) {
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.EXCLUIR_CONTA);
    contaPagamentoService.validaIdContaPeloRequestEPortador(
        exclusaoContaVO.getIdConta(), userPortador);
    return this.exclusaoConta(exclusaoContaVO);
  }

  public ExclusaoConta validarExclusaoConta(
      ExclusaoContaVO exclusaoContaVO, SecurityUserCorporativo userCorporativo) {
    travaServicosService.travaServicos(userCorporativo.getIdInstituicao(), Servicos.EXCLUIR_CONTA);
    contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
        exclusaoContaVO.getIdConta(), userCorporativo);
    return this.exclusaoConta(exclusaoContaVO);
  }

  private ExclusaoConta exclusaoConta(ExclusaoContaVO exclusaoContaVO) {

    if (!pessoaService.isNomeCompletoValido(exclusaoContaVO.getNomeCompleto())) {
      throw new GenericServiceException(
          "Nome informado é inválido.", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    // Calcula Saldo da credencial
    GetSaldoConta getSaldo = contaPagamentoService.getSaldoConta(exclusaoContaVO.getIdConta());

    if (!getSaldo
        .getSaldoDisponivel()
        .setScale(0, RoundingMode.DOWN)
        .equals(exclusaoContaVO.getSaldoExclusao().setScale(0, RoundingMode.DOWN))) {
      throw new GenericServiceException(
          "Saldo informado na exclusão não confere com o saldo disponível em conta.",
          HttpStatus.UNPROCESSABLE_ENTITY);
    }

    ExclusaoConta request =
        exclusaoContaRepository.findSolicitacaoJaFeita(exclusaoContaVO.getIdConta());
    if (request != null) {
      throw new GenericServiceException(
          "Esta solicitação já foi feita e está pendente. Aguarde a resolução de seu pedido!",
          HttpStatus.UNPROCESSABLE_ENTITY);
    }

    // Salvar no banco
    ExclusaoConta exclusaoConta = new ExclusaoConta();
    exclusaoConta.setIdConta(exclusaoContaVO.getIdConta());
    exclusaoConta.setNomeCompleto(exclusaoContaVO.getNomeCompleto());
    exclusaoConta.setSaldoExclusao(exclusaoContaVO.getSaldoExclusao());
    exclusaoConta.setIdProdInstituicao(exclusaoContaVO.getIdProdInstituicao());
    exclusaoConta.setNomeProduto(exclusaoContaVO.getNomeProduto());
    exclusaoConta.setDtHrAlteracao(null);
    exclusaoConta.setStatusSolicitacao(AGUARDANDO_APROVACAO);
    exclusaoConta.setStatusSolicitacaoExclusao(SOLICITACAO_EXCLUSAO_ABERTA);
    exclusaoConta.setDtHrSolicitacao(LocalDateTime.now());
    exclusaoConta.setIdUsuarioManutencao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);
    exclusaoConta.setCienteRecusaExclusao(null);
    return exclusaoContaRepository.save(exclusaoConta);
  }

  public List<SolicitacaoCancelamentoVO> buscarSolicitacoesExclusao() {
    return exclusaoContaRepository.getExclusaoConta();
  }

  public SolicitacaoCancelamentoVO buscarExclusaoById(Long id) {
    List<SolicitacaoCancelamentoVO> solicitacaoCancelamentoVOS =
        exclusaoContaRepository.getExclusaoContaById(id);
    return solicitacaoCancelamentoVOS.stream()
        .min(
            Comparator.comparing(
                SolicitacaoCancelamentoVO::getDtHrSolicitacao,
                Comparator.nullsLast((Comparator.reverseOrder()))))
        .orElse(null);
  }

  public void atualizarStatusExclusao(Long idConta, AlterarStatusExclusaoVO status) {
    ExclusaoConta solicitacaoAtualizada =
        exclusaoContaRepository.encontraUltimaSolicitacaoPorIdConta(idConta);
    ContaPagamento contaPagamento = contaPagamentoService.findById(idConta);

    solicitacaoAtualizada.setStatusSolicitacao(status.getStatusSolicitacao());
    solicitacaoAtualizada.setDtHrAlteracao(LocalDateTime.now());

    // ENTRA AQUI QUANDO ADM ACEITA BLOQUEIO PELO ISSUER
    if (status.getStatusSolicitacao() == SOLICITACAO_APROVADA) { // 30
      // Informar ao jcard
      contaPagamentoFacade.alterarStatusConta(contaPagamento, STATUS_DESTINO_JCARD_BLOQUEADO);
      solicitacaoAtualizada.setStatusSolicitacaoExclusao(SOLICITACAO_EXCLUSAO_CONFIRMADA); // 2
      solicitacaoAtualizada.setCienteRecusaExclusao(true);
      contaPagamento.setIdStatusV2(Constantes.TIPO_STATUS_CANCELADO_PELO_USUARIO); // 30
    }
    // ENTRA AQUI QUANDO ADM RECUSA BLOQUEIO PELO ISSUER
    else if (status.getStatusSolicitacao() == AGUARDANDO_APROVACAO) { // 1
      solicitacaoAtualizada.setStatusSolicitacaoExclusao(SOLICITACAO_EXCLUSAO_RECUSADA); // 3
    }

    exclusaoContaRepository.save(solicitacaoAtualizada);
    contaPagamentoService.save(contaPagamento);
  }

  public void visualizarModalExclusao(Long idConta) {
    ExclusaoConta solicitacaoRecusada = exclusaoContaRepository.getSolicitacaoRecusada(idConta);

    solicitacaoRecusada.setCienteRecusaExclusao(true);
    solicitacaoRecusada.setDtHrAlteracao(LocalDateTime.now());

    exclusaoContaRepository.save(solicitacaoRecusada);
  }
}
