package br.com.entity.cadastral;

import br.com.entity.suporte.EstadoCivil;
import br.com.entity.suporte.Sexo;
import br.com.entity.suporte.TipoPessoa;
import br.com.json.bean.cadastral.ConsultaRestritaEnum;
import br.com.sinergico.annotation.NotUpperCase;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;

@Entity
@DynamicUpdate(value = true)
@Table(name = "pessoa", schema = "cadastral")
@SequenceGenerator(
    name = "seq_id_pessoa_generator",
    sequenceName = "cadastral.seq_id_pessoa",
    initialValue = 1,
    allocationSize = 1)
@NamedEntityGraph(name = "Pessoa.contas", attributeNodes = @NamedAttributeNode("contasPessoa"))
@ApiModel(value = "Pessoa")
public class Pessoa implements Serializable {

  private static final int DDI_PADRAO = 55;

  private static final long serialVersionUID = 7482499539515793952L;

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_id_pessoa_generator")
  @Column(name = "id_pessoa", nullable = false)
  private Long idPessoa;

  @Column(name = "id_processadora", nullable = false)
  private Integer idProcessadora;

  @Column(name = "id_instituicao", nullable = false)
  private Integer idInstituicao;

  @ManyToOne
  @JoinColumn(name = "tipo_pessoa", nullable = false, insertable = false, updatable = false)
  private TipoPessoa tipoPessoa;

  @Column(name = "tipo_pessoa")
  private Integer idTipoPessoa;

  @ManyToOne
  @JoinColumn(name = "id_sexo", insertable = false, updatable = false)
  private Sexo sexo;

  @Column(name = "id_sexo")
  private Integer idSexo;

  @JsonIgnore
  @OneToMany(mappedBy = "pessoa", fetch = FetchType.LAZY)
  private List<Credencial> credenciais;

  @Column(nullable = false)
  private String documento;

  @Column(name = "nome_completo")
  private String nomeCompleto;

  private String naturalidade;

  private String nacionalidade;

  private String rg;

  @Column(name = "rg_orgao_emissor", nullable = false)
  private String rgOrgaoEmissor;

  @Column(name = "rg_data_emissao", nullable = false)
  private LocalDateTime rgDataEmissao;

  @Column(nullable = false, name = "estrangeiro")
  @Type(type = "numeric_boolean")
  private Boolean estrangeiro;

  private String passaporte;

  @Column(name = "data_nascimento")
  private LocalDateTime dataNascimento;

  @Column(name = "nome_pai")
  private String nomePai;

  @Column(name = "nome_mae")
  private String nomeMae;

  @Column(name = "dt_hr_inclusao", nullable = false)
  private LocalDateTime dataHoraInclusao;

  @Column(name = "dt_hr_ultima_atualizacao")
  private LocalDateTime dataHoraUltimaAtualizacao;

  @Column(name = "email")
  private String email;

  @Column(name = "email_profissional")
  private String emailProfissional;

  @Column(name = "dt_hr_conf_email")
  private LocalDateTime dataHoraConfirmacaoEmail;

  @Column(name = "ddd_tel_residencial")
  private Integer dddTelefoneResidencial;

  @Column(name = "tel_residencial")
  private Integer telefoneResidencial;

  @Column(name = "ddd_tel_comercial")
  private Integer dddTelefoneComercial;

  @Column(name = "tel_comercial")
  private Integer telefoneComercial;

  @Column(name = "ddd_tel_celular")
  private Integer dddTelefoneCelular;

  @Column(name = "ddi_tel_celular")
  private Integer ddiTelefoneCelular;

  @Column(name = "tel_celular")
  private Integer telefoneCelular;

  @Column(name = "dt_inicio_relacionamento", nullable = false)
  private LocalDateTime dataInicioRelacionamento;

  @Column(name = "dt_fim_relacionamento")
  private LocalDateTime dataFimRelacionamento;

  @Column(name = "razao_social")
  private String razaoSocial;

  @Column(name = "nome_fantasia")
  private String nomeFantasia;

  @Column(name = "inscricao_estadual")
  private String inscricaoEstadual;

  @Column(name = "inscricao_municipal")
  private String inscricaoMunicipal;

  @Column(name = "data_fundacao")
  private LocalDateTime dataFundacao;

  @Column(name = "atividade_principal")
  private String atividadePrincipal;

  @Column(name = "forma_de_constituicao")
  private String formaDeConstituicao;

  @Column(name = "id_usuario_inclusao", nullable = false)
  private Integer idUsuarioInclusao;

  @Column(name = "id_usuario_manutencao")
  private Integer idUsuarioManutencao;

  @Column(name = "id_setor_filial")
  private Integer idSetorFilial;

  @Column(name = "matricula")
  private String matricula;

  @Column(name = "nome_embossado")
  private String nomeEmbossado;

  @Column(name = "id_estado_civil")
  private Integer idEstadoCivil;

  @ManyToOne
  @JoinColumn(name = "id_estado_civil", insertable = false, updatable = false)
  private EstadoCivil estadoCivil;

  @ManyToOne
  @JoinColumn(name = "id_setor_filial", insertable = false, updatable = false)
  private SetorFilial setorFilial;

  @OneToMany(mappedBy = "contaPessoaId.pessoa")
  @JsonIgnore
  private List<ContaPessoa> contasPessoa;

  @OneToMany(mappedBy = "idPessoa")
  private List<EnderecoPessoa> enderecosPessoa;

  @Column(name = "id_banco")
  private Integer idBanco;

  @Column(name = "id_agencia")
  private Integer idAgencia;

  @Column(name = "conta_bancaria")
  private Integer contaBancaria;

  @Column(name = "conta_bancaria_x")
  private String contaBancariaX;

  @Column(name = "tipo_conta_bancaria")
  private Integer tipoContaBancaria;

  @Column(name = "rg_uf_orgao_emissor")
  private String rgUfOrgaoEmissor;

  @Column(name = "cadastro_origem")
  private Integer cadastroOrigem;

  @Column(name = "nome_representante_legal")
  private String nomeRepresentanteLegal;

  @Column(name = "cpf_representante_legal")
  private String cpfRepresentanteLegal;

  @Column(name = "rg_representante_legal")
  private String rgRepresentanteLegal;

  @Column(name = "logradouro_representante_legal")
  private String logradouroRepresentanteLegal;

  @Column(name = "numero_representante_legal")
  private String numeroRepresentanteLegal;

  @Column(name = "bairro_representante_legal")
  private String bairroRepresentanteLegal;

  @Column(name = "cidade_representante_legal")
  private String cidadeRepresentanteLegal;

  @Column(name = "uf_representante_legal")
  private String ufRepresentanteLegal;

  @Column(name = "cep_representante_legal")
  private String cepRepresentanteLegal;

  @Column(name = "ddd_celular_representante_legal")
  private Integer dddCelularRepresentanteLegal;

  @Column(name = "telefone_celular_representante_legal")
  private Integer telefonteCelularRepresentanteLegal;

  @Column(name = "ddd_fixo_representante_legal")
  private Integer dddFixoRepresentanteLegal;

  @Column(name = "telefone_fixo_representante_legal")
  private Integer telefoneFixoRepresentanteLegal;

  @Column(name = "complemento_representante_legal")
  private String complementoRepresentanteLegal;

  @Column(name = "id_parceiro_acumulo")
  private Integer idParceiroAcumulo;

  @Column(name = "nome_mae_representante_legal")
  private String nomeMaeRepresentanteLegal;

  @Column(name = "dt_nascimento_representante_legal")
  private Date dtNascimentoRepresentanteLegal;

  @Column(name = "email_representante_legal")
  private String emailRepresentanteLegal;

  @Column(name = "cnpj_vinculante")
  private String cnpjVinculante;

  @Column(nullable = true, name = "deficiente_visual")
  @Type(type = "numeric_boolean")
  private Boolean deficienteVisual;

  @Column(name = "cpf_responsavel")
  private String cpfResponsavel;

  @Column(name = "nome_responsavel")
  private String nomeResponsavel;

  @Column(name = "tipo_chave_pix")
  private Integer tipoChavePix;

  @NotUpperCase
  @Column(name = "chave_pix")
  private String chavePix;

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "consulta_restrita")
  private ConsultaRestritaEnum consultaRestrita = ConsultaRestritaEnum.SEM_RESTRICAO;

  @Transient private String nomeCartaoImpresso;

  public Pessoa() {}

  public Pessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }

  public String getCnpjVinculante() {
    return cnpjVinculante;
  }

  public void setCnpjVinculante(String cnpjVinculante) {
    this.cnpjVinculante = cnpjVinculante;
  }

  public Integer getIdParceiroAcumulo() {
    return idParceiroAcumulo;
  }

  public void setIdParceiroAcumulo(Integer idParceiroAcumulo) {
    this.idParceiroAcumulo = idParceiroAcumulo;
  }

  public Integer getCadastroOrigem() {
    return cadastroOrigem;
  }

  public void setCadastroOrigem(Integer cadastroOrigem) {
    this.cadastroOrigem = cadastroOrigem;
  }

  public String getNomeEmbossado() {
    return nomeEmbossado;
  }

  public void setNomeEmbossado(String nomeEmbossado) {
    this.nomeEmbossado = nomeEmbossado;
  }

  public List<ContaPessoa> getContasPessoa() {
    return contasPessoa;
  }

  public void setContasPessoa(List<ContaPessoa> contasPessoa) {
    this.contasPessoa = contasPessoa;
  }

  public Long getIdPessoa() {
    return idPessoa;
  }

  public void setIdPessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public TipoPessoa getTipoPessoa() {
    return tipoPessoa;
  }

  public void setTipoPessoa(TipoPessoa tipoPessoa) {
    this.tipoPessoa = tipoPessoa;
  }

  public Integer getIdTipoPessoa() {
    return idTipoPessoa;
  }

  public void setIdTipoPessoa(Integer idTipoPessoa) {
    this.idTipoPessoa = idTipoPessoa;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public String getNomeCompleto() {
    return nomeCompleto;
  }

  public void setNomeCompleto(String nomeCompleto) {
    this.nomeCompleto = nomeCompleto;
  }

  public String getNaturalidade() {
    return naturalidade;
  }

  public void setNaturalidade(String naturalidade) {
    this.naturalidade = naturalidade;
  }

  public String getNacionalidade() {
    return nacionalidade;
  }

  public void setNacionalidade(String nacionalidade) {
    this.nacionalidade = nacionalidade;
  }

  public String getRg() {
    return rg;
  }

  public void setRg(String rg) {
    this.rg = rg;
  }

  public String getRgOrgaoEmissor() {
    return rgOrgaoEmissor;
  }

  public void setRgOrgaoEmissor(String rgOrgaoEmissor) {
    this.rgOrgaoEmissor = rgOrgaoEmissor;
  }

  public LocalDateTime getRgDataEmissao() {
    return rgDataEmissao;
  }

  public void setRgDataEmissao(LocalDateTime rgDataEmissao) {
    this.rgDataEmissao = rgDataEmissao;
  }

  public Boolean getEstrangeiro() {
    return estrangeiro;
  }

  public void setEstrangeiro(Boolean estrangeiro) {
    this.estrangeiro = estrangeiro;
  }

  public String getPassaporte() {
    return passaporte;
  }

  public void setPassaporte(String passaporte) {
    this.passaporte = passaporte;
  }

  public LocalDateTime getDataNascimento() {
    return dataNascimento;
  }

  public void setDataNascimento(LocalDateTime dataNascimento) {
    this.dataNascimento = dataNascimento;
  }

  public String getNomePai() {
    return nomePai;
  }

  public void setNomePai(String nomePai) {
    this.nomePai = nomePai;
  }

  public String getNomeMae() {
    return nomeMae;
  }

  public void setNomeMae(String nomeMae) {
    this.nomeMae = nomeMae;
  }

  public LocalDateTime getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(LocalDateTime dataHoraInclusao) {
    this.dataHoraInclusao = dataHoraInclusao;
  }

  public LocalDateTime getDataHoraUltimaAtualizacao() {
    return dataHoraUltimaAtualizacao;
  }

  public void setDataHoraUltimaAtualizacao(LocalDateTime dataHoraUltimaAtualizacao) {
    this.dataHoraUltimaAtualizacao = dataHoraUltimaAtualizacao;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public LocalDateTime getDataHoraConfirmacaoEmail() {
    return dataHoraConfirmacaoEmail;
  }

  public void setDataHoraConfirmacaoEmail(LocalDateTime dataHoraConfirmacaoEmail) {
    this.dataHoraConfirmacaoEmail = dataHoraConfirmacaoEmail;
  }

  public Integer getDddTelefoneResidencial() {
    return dddTelefoneResidencial;
  }

  public void setDddTelefoneResidencial(Integer dddTelefoneResidencial) {
    this.dddTelefoneResidencial = dddTelefoneResidencial;
  }

  public Integer getTelefoneResidencial() {
    return telefoneResidencial;
  }

  public void setTelefoneResidencial(Integer telefoneResidencial) {
    this.telefoneResidencial = telefoneResidencial;
  }

  public Integer getDddTelefoneComercial() {
    return dddTelefoneComercial;
  }

  public void setDddTelefoneComercial(Integer dddTelefoneComercial) {
    this.dddTelefoneComercial = dddTelefoneComercial;
  }

  public Integer getTelefoneComercial() {
    return telefoneComercial;
  }

  public void setTelefoneComercial(Integer telefoneComercial) {
    this.telefoneComercial = telefoneComercial;
  }

  public Integer getDddTelefoneCelular() {
    return dddTelefoneCelular;
  }

  public void setDddTelefoneCelular(Integer dddTelefoneCelular) {
    this.dddTelefoneCelular = dddTelefoneCelular;
  }

  public Integer getTelefoneCelular() {
    return telefoneCelular;
  }

  public void setTelefoneCelular(Integer telefoneCelular) {
    this.telefoneCelular = telefoneCelular;
  }

  public LocalDateTime getDataInicioRelacionamento() {
    return dataInicioRelacionamento;
  }

  public void setDataInicioRelacionamento(LocalDateTime dataInicioRelacionamento) {
    this.dataInicioRelacionamento = dataInicioRelacionamento;
  }

  public LocalDateTime getDataFimRelacionamento() {
    return dataFimRelacionamento;
  }

  public void setDataFimRelacionamento(LocalDateTime dataFimRelacionamento) {
    this.dataFimRelacionamento = dataFimRelacionamento;
  }

  public String getRazaoSocial() {
    return razaoSocial;
  }

  public void setRazaoSocial(String razaoSocial) {
    this.razaoSocial = razaoSocial;
  }

  public String getNomeFantasia() {
    return nomeFantasia;
  }

  public void setNomeFantasia(String nomeFantasia) {
    this.nomeFantasia = nomeFantasia;
  }

  public String getInscricaoEstadual() {
    return inscricaoEstadual;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    this.inscricaoEstadual = inscricaoEstadual;
  }

  public String getInscricaoMunicipal() {
    return inscricaoMunicipal;
  }

  public void setInscricaoMunicipal(String inscricaoMunicipal) {
    this.inscricaoMunicipal = inscricaoMunicipal;
  }

  public LocalDateTime getDataFundacao() {
    return dataFundacao;
  }

  public void setDataFundacao(LocalDateTime dataFundacao) {
    this.dataFundacao = dataFundacao;
  }

  public String getAtividadePrincipal() {
    return atividadePrincipal;
  }

  public void setAtividadePrincipal(String atividadePrincipal) {
    this.atividadePrincipal = atividadePrincipal;
  }

  public String getFormaDeConstituicao() {
    return formaDeConstituicao;
  }

  public void setFormaDeConstituicao(String formaDeConstituicao) {
    this.formaDeConstituicao = formaDeConstituicao;
  }

  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  public Integer getIdSetorFilial() {
    return idSetorFilial;
  }

  public void setIdSetorFilial(Integer idSetorFilial) {
    this.idSetorFilial = idSetorFilial;
  }

  public String getMatricula() {
    return matricula;
  }

  public void setMatricula(String matricula) {
    this.matricula = matricula;
  }

  public SetorFilial getSetorFilial() {
    return setorFilial;
  }

  public void setSetorFilial(SetorFilial setorFilial) {
    this.setorFilial = setorFilial;
  }

  public Sexo getSexo() {
    return sexo;
  }

  public void setSexo(Sexo sexo) {
    this.sexo = sexo;
  }

  public Integer getIdSexo() {
    return idSexo;
  }

  public void setIdSexo(Integer idSexo) {
    this.idSexo = idSexo;
  }

  public List<Credencial> getCredenciais() {
    return credenciais;
  }

  public void setCredenciais(List<Credencial> credenciais) {
    this.credenciais = credenciais;
  }

  public List<EnderecoPessoa> getEnderecosPessoa() {
    return enderecosPessoa;
  }

  public void setEnderecosPessoa(List<EnderecoPessoa> enderecosPessoa) {
    this.enderecosPessoa = enderecosPessoa;
  }

  public Integer getDdiTelefoneCelular() {
    ddiTelefoneCelular = ddiTelefoneCelular == null ? DDI_PADRAO : ddiTelefoneCelular;
    return ddiTelefoneCelular;
  }

  public void setDdiTelefoneCelular(Integer ddiTelefoneCelular) {
    this.ddiTelefoneCelular = ddiTelefoneCelular;
  }

  public Integer getIdBanco() {
    return idBanco;
  }

  public void setIdBanco(Integer idBanco) {
    this.idBanco = idBanco;
  }

  public Integer getIdAgencia() {
    return idAgencia;
  }

  public void setIdAgencia(Integer idAgencia) {
    this.idAgencia = idAgencia;
  }

  public Integer getContaBancaria() {
    return contaBancaria;
  }

  public void setContaBancaria(Integer contaBancaria) {
    this.contaBancaria = contaBancaria;
  }

  public Integer getTipoContaBancaria() {
    return tipoContaBancaria;
  }

  public void setTipoContaBancaria(Integer tipoContaBancaria) {
    this.tipoContaBancaria = tipoContaBancaria;
  }

  public String getEmailProfissional() {
    return emailProfissional;
  }

  public void setEmailProfissional(String emailProfissional) {
    this.emailProfissional = emailProfissional;
  }

  public Integer getIdEstadoCivil() {
    return idEstadoCivil;
  }

  public void setIdEstadoCivil(Integer idEstadoCivil) {
    this.idEstadoCivil = idEstadoCivil;
  }

  public EstadoCivil getEstadoCivil() {
    return estadoCivil;
  }

  public void setEstadoCivil(EstadoCivil estadoCivil) {
    this.estadoCivil = estadoCivil;
  }

  public String getRgUfOrgaoEmissor() {
    return rgUfOrgaoEmissor;
  }

  public void setRgUfOrgaoEmissor(String rgUfOrgaoEmissor) {
    this.rgUfOrgaoEmissor = rgUfOrgaoEmissor;
  }

  public String getNomeRepresentanteLegal() {
    return nomeRepresentanteLegal;
  }

  public void setNomeRepresentanteLegal(String nomeRepresentanteLegal) {
    this.nomeRepresentanteLegal = nomeRepresentanteLegal;
  }

  public String getCpfRepresentanteLegal() {
    return cpfRepresentanteLegal;
  }

  public void setCpfRepresentanteLegal(String cpfRepresentanteLegal) {
    this.cpfRepresentanteLegal = cpfRepresentanteLegal;
  }

  public String getRgRepresentanteLegal() {
    return rgRepresentanteLegal;
  }

  public void setRgRepresentanteLegal(String rgRepresentanteLegal) {
    this.rgRepresentanteLegal = rgRepresentanteLegal;
  }

  public String getLogradouroRepresentanteLegal() {
    return logradouroRepresentanteLegal;
  }

  public void setLogradouroRepresentanteLegal(String logradouroRepresentanteLegal) {
    this.logradouroRepresentanteLegal = logradouroRepresentanteLegal;
  }

  public String getNumeroRepresentanteLegal() {
    return numeroRepresentanteLegal;
  }

  public void setNumeroRepresentanteLegal(String numeroRepresentanteLegal) {
    this.numeroRepresentanteLegal = numeroRepresentanteLegal;
  }

  public String getBairroRepresentanteLegal() {
    return bairroRepresentanteLegal;
  }

  public void setBairroRepresentanteLegal(String bairroRepresentanteLegal) {
    this.bairroRepresentanteLegal = bairroRepresentanteLegal;
  }

  public String getCidadeRepresentanteLegal() {
    return cidadeRepresentanteLegal;
  }

  public void setCidadeRepresentanteLegal(String cidadeRepresentanteLegal) {
    this.cidadeRepresentanteLegal = cidadeRepresentanteLegal;
  }

  public String getUfRepresentanteLegal() {
    return ufRepresentanteLegal;
  }

  public void setUfRepresentanteLegal(String ufRepresentanteLegal) {
    this.ufRepresentanteLegal = ufRepresentanteLegal;
  }

  public String getCepRepresentanteLegal() {
    return cepRepresentanteLegal;
  }

  public void setCepRepresentanteLegal(String cepRepresentanteLegal) {
    this.cepRepresentanteLegal = cepRepresentanteLegal;
  }

  public Integer getDddCelularRepresentanteLegal() {
    return dddCelularRepresentanteLegal;
  }

  public void setDddCelularRepresentanteLegal(Integer dddCelularRepresentanteLegal) {
    this.dddCelularRepresentanteLegal = dddCelularRepresentanteLegal;
  }

  public Integer getTelefonteCelularRepresentanteLegal() {
    return telefonteCelularRepresentanteLegal;
  }

  public void setTelefonteCelularRepresentanteLegal(Integer telefonteCelularRepresentanteLegal) {
    this.telefonteCelularRepresentanteLegal = telefonteCelularRepresentanteLegal;
  }

  public Integer getDddFixoRepresentanteLegal() {
    return dddFixoRepresentanteLegal;
  }

  public void setDddFixoRepresentanteLegal(Integer dddFixoRepresentanteLegal) {
    this.dddFixoRepresentanteLegal = dddFixoRepresentanteLegal;
  }

  public Integer getTelefoneFixoRepresentanteLegal() {
    return telefoneFixoRepresentanteLegal;
  }

  public void setTelefoneFixoRepresentanteLegal(Integer telefoneFixoRepresentanteLegal) {
    this.telefoneFixoRepresentanteLegal = telefoneFixoRepresentanteLegal;
  }

  public String getComplementoRepresentanteLegal() {
    return complementoRepresentanteLegal;
  }

  public void setComplementoRepresentanteLegal(String complementoRepresentanteLegal) {
    this.complementoRepresentanteLegal = complementoRepresentanteLegal;
  }

  public String getNomeCartaoImpresso() {
    return nomeCartaoImpresso;
  }

  public void setNomeCartaoImpresso(String nomeCartaoImpresso) {
    this.nomeCartaoImpresso = nomeCartaoImpresso;
  }

  public String getContaBancariaX() {
    return contaBancariaX;
  }

  public void setContaBancariaX(String contaBancariaX) {
    this.contaBancariaX = contaBancariaX;
  }

  public String getNomeMaeRepresentanteLegal() {
    return nomeMaeRepresentanteLegal;
  }

  public void setNomeMaeRepresentanteLegal(String nomeMaeRepresentanteLegal) {
    this.nomeMaeRepresentanteLegal = nomeMaeRepresentanteLegal;
  }

  public Date getDtNascimentoRepresentanteLegal() {
    return dtNascimentoRepresentanteLegal;
  }

  public void setDtNascimentoRepresentanteLegal(Date dtNascimentoRepresentanteLegal) {
    this.dtNascimentoRepresentanteLegal = dtNascimentoRepresentanteLegal;
  }

  public String getEmailRepresentanteLegal() {
    return emailRepresentanteLegal;
  }

  public void setEmailRepresentanteLegal(String emailRepresentanteLegal) {
    this.emailRepresentanteLegal = emailRepresentanteLegal;
  }

  public Boolean getDeficienteVisual() {
    return deficienteVisual;
  }

  public void setDeficienteVisual(Boolean deficienteVisual) {
    this.deficienteVisual = deficienteVisual;
  }

  public static long getSerialversionuid() {
    return serialVersionUID;
  }

  public Integer getTipoChavePix() {
    return tipoChavePix;
  }

  public void setTipoChavePix(Integer tipoChavePix) {
    this.tipoChavePix = tipoChavePix;
  }

  public String getChavePix() {
    return chavePix;
  }

  public void setChavePix(String chavePix) {
    this.chavePix = chavePix;
  }

  public ConsultaRestritaEnum getConsultaRestrita() {
    return consultaRestrita;
  }

  public void setConsultaRestrita(ConsultaRestritaEnum consultaRestrita) {
    this.consultaRestrita = consultaRestrita;
  }

  public String getNomePorTipoPessoa() {
    String nome = "";
    if (this.getTipoPessoa() != null
        && Constantes.PESSOA_JURIDICA.equals(this.getTipoPessoa().getTipoPessoa())) {
      nome = this.getNomeFantasia();
    } else {
      nome = this.getNomeCompleto();
    }
    return nome;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((documento == null) ? 0 : documento.hashCode());
    result = prime * result + ((idInstituicao == null) ? 0 : idInstituicao.hashCode());
    result = prime * result + ((idPessoa == null) ? 0 : idPessoa.hashCode());
    result = prime * result + ((idProcessadora == null) ? 0 : idProcessadora.hashCode());
    result = prime * result + ((idTipoPessoa == null) ? 0 : idTipoPessoa.hashCode());
    result = prime * result + ((idUsuarioInclusao == null) ? 0 : idUsuarioInclusao.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    Pessoa other = (Pessoa) obj;
    if (documento == null) {
      if (other.documento != null) return false;
    } else if (!documento.equals(other.documento)) return false;
    if (idInstituicao == null) {
      if (other.idInstituicao != null) return false;
    } else if (!idInstituicao.equals(other.idInstituicao)) return false;
    if (idPessoa == null) {
      if (other.idPessoa != null) return false;
    } else if (!idPessoa.equals(other.idPessoa)) return false;
    if (idProcessadora == null) {
      if (other.idProcessadora != null) return false;
    } else if (!idProcessadora.equals(other.idProcessadora)) return false;
    if (idTipoPessoa == null) {
      if (other.idTipoPessoa != null) return false;
    } else if (!idTipoPessoa.equals(other.idTipoPessoa)) return false;
    if (idUsuarioInclusao == null) {
      if (other.idUsuarioInclusao != null) return false;
    } else if (!idUsuarioInclusao.equals(other.idUsuarioInclusao)) return false;
    return true;
  }

  public String getCpfResponsavel() {
    return cpfResponsavel;
  }

  public void setCpfResponsavel(String cpfResponsavel) {
    this.cpfResponsavel = cpfResponsavel;
  }

  public String getNomeResponsavel() {
    return nomeResponsavel;
  }

  public void setNomeResponsavel(String nomeResponsavel) {
    this.nomeResponsavel = nomeResponsavel;
  }

  @Override
  public String toString() {
    return "Pessoa{"
        + "idPessoa="
        + idPessoa
        + ", idProcessadora="
        + idProcessadora
        + ", idInstituicao="
        + idInstituicao
        + ", idTipoPessoa="
        + idTipoPessoa
        + ", idSexo="
        + idSexo
        + ", documento='"
        + documento
        + '\''
        + ", nomeCompleto='"
        + nomeCompleto
        + '\''
        + ", naturalidade='"
        + naturalidade
        + '\''
        + ", nacionalidade='"
        + nacionalidade
        + '\''
        + ", rg='"
        + rg
        + '\''
        + ", rgOrgaoEmissor='"
        + rgOrgaoEmissor
        + '\''
        + ", rgDataEmissao="
        + rgDataEmissao
        + ", estrangeiro="
        + estrangeiro
        + ", passaporte='"
        + passaporte
        + '\''
        + ", dataNascimento="
        + dataNascimento
        + ", nomePai='"
        + nomePai
        + '\''
        + ", nomeMae='"
        + nomeMae
        + '\''
        + ", dataHoraInclusao="
        + dataHoraInclusao
        + ", dataHoraUltimaAtualizacao="
        + dataHoraUltimaAtualizacao
        + ", email='"
        + email
        + '\''
        + ", emailProfissional='"
        + emailProfissional
        + '\''
        + ", dataHoraConfirmacaoEmail="
        + dataHoraConfirmacaoEmail
        + ", dddTelefoneResidencial="
        + dddTelefoneResidencial
        + ", telefoneResidencial="
        + telefoneResidencial
        + ", dddTelefoneComercial="
        + dddTelefoneComercial
        + ", telefoneComercial="
        + telefoneComercial
        + ", dddTelefoneCelular="
        + dddTelefoneCelular
        + ", ddiTelefoneCelular="
        + ddiTelefoneCelular
        + ", telefoneCelular="
        + telefoneCelular
        + ", dataInicioRelacionamento="
        + dataInicioRelacionamento
        + ", dataFimRelacionamento="
        + dataFimRelacionamento
        + ", razaoSocial='"
        + razaoSocial
        + '\''
        + ", nomeFantasia='"
        + nomeFantasia
        + '\''
        + ", inscricaoEstadual='"
        + inscricaoEstadual
        + '\''
        + ", inscricaoMunicipal='"
        + inscricaoMunicipal
        + '\''
        + ", dataFundacao="
        + dataFundacao
        + ", atividadePrincipal='"
        + atividadePrincipal
        + '\''
        + ", formaDeConstituicao='"
        + formaDeConstituicao
        + '\''
        + ", idUsuarioInclusao="
        + idUsuarioInclusao
        + ", idUsuarioManutencao="
        + idUsuarioManutencao
        + ", idSetorFilial="
        + idSetorFilial
        + ", matricula='"
        + matricula
        + '\''
        + ", nomeEmbossado='"
        + nomeEmbossado
        + '\''
        + ", idEstadoCivil="
        + idEstadoCivil
        + ", estadoCivil="
        + estadoCivil
        + ", setorFilial="
        + setorFilial
        + ", idBanco="
        + idBanco
        + ", idAgencia="
        + idAgencia
        + ", contaBancaria="
        + contaBancaria
        + ", contaBancariaX='"
        + contaBancariaX
        + '\''
        + ", tipoContaBancaria="
        + tipoContaBancaria
        + ", rgUfOrgaoEmissor='"
        + rgUfOrgaoEmissor
        + '\''
        + ", cadastroOrigem="
        + cadastroOrigem
        + ", nomeRepresentanteLegal='"
        + nomeRepresentanteLegal
        + '\''
        + ", cpfRepresentanteLegal='"
        + cpfRepresentanteLegal
        + '\''
        + ", rgRepresentanteLegal='"
        + rgRepresentanteLegal
        + '\''
        + ", logradouroRepresentanteLegal='"
        + logradouroRepresentanteLegal
        + '\''
        + ", numeroRepresentanteLegal='"
        + numeroRepresentanteLegal
        + '\''
        + ", bairroRepresentanteLegal='"
        + bairroRepresentanteLegal
        + '\''
        + ", cidadeRepresentanteLegal='"
        + cidadeRepresentanteLegal
        + '\''
        + ", ufRepresentanteLegal='"
        + ufRepresentanteLegal
        + '\''
        + ", cepRepresentanteLegal='"
        + cepRepresentanteLegal
        + '\''
        + ", dddCelularRepresentanteLegal="
        + dddCelularRepresentanteLegal
        + ", telefonteCelularRepresentanteLegal="
        + telefonteCelularRepresentanteLegal
        + ", dddFixoRepresentanteLegal="
        + dddFixoRepresentanteLegal
        + ", telefoneFixoRepresentanteLegal="
        + telefoneFixoRepresentanteLegal
        + ", complementoRepresentanteLegal='"
        + complementoRepresentanteLegal
        + '\''
        + ", idParceiroAcumulo="
        + idParceiroAcumulo
        + ", nomeMaeRepresentanteLegal='"
        + nomeMaeRepresentanteLegal
        + '\''
        + ", dtNascimentoRepresentanteLegal="
        + dtNascimentoRepresentanteLegal
        + ", emailRepresentanteLegal='"
        + emailRepresentanteLegal
        + '\''
        + ", cnpjVinculante='"
        + cnpjVinculante
        + '\''
        + ", deficienteVisual="
        + deficienteVisual
        + ", cpfResponsavel='"
        + cpfResponsavel
        + '\''
        + ", nomeResponsavel='"
        + nomeResponsavel
        + '\''
        + ", nomeCartaoImpresso='"
        + nomeCartaoImpresso
        + '\''
        + '}';
  }
}
