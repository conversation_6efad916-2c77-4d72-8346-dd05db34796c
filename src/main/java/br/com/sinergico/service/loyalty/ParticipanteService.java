package br.com.sinergico.service.loyalty;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.ParceiroAcumulo;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.loyalty.ParceiroProgramaFidelidade;
import br.com.entity.loyalty.Participante;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.CadastrarContaPagamentoPessoaRequest;
import br.com.json.bean.loyalty.PreCadastroParticipanteTO;
import br.com.json.bean.loyalty.PreCadastroViaProcResponse;
import br.com.json.bean.loyalty.VerificacaoPreCadastroParticipanteTO;
import br.com.json.bean.suporte.ValorCargaProdutoInstituicao;
import br.com.sinergico.repository.loyalty.ParticipanteRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ParceiroAcumuloService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.VerificacaoImportacaoArquivoEnum;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.util.Abreviador;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.DocumentoUtil;
import br.com.sinergico.util.StringComparator;
import br.com.sinergico.validator.UtilValidator;
import java.io.IOException;
import java.io.InputStream;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import javax.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** Created by evandro on 15/01/18. */
@Service
public class ParticipanteService extends GenericService<Participante, Long> {

  private static final String CPF_INVALIDO = "0";

  private static final int TAMANHO_EMAIL = 80;

  private static final int TAMANHO_NOME_COMPLETO = 80;

  private static final String EMAIL_PATTERN =
      "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
          + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";

  private static final String DDD_PATTERN = "[0-9]{2}";

  private static final String TELEFONE_PATTERN = "[0-9]{9}";

  private static final String NOME_PATTERN = "[0-9]";

  private static final Pattern patternNome = Pattern.compile(NOME_PATTERN);

  private static final Pattern patternEmail =
      Pattern.compile(EMAIL_PATTERN, Pattern.CASE_INSENSITIVE);

  private static final Pattern paternDdd = Pattern.compile(DDD_PATTERN);

  private static final Pattern paternTelefone = Pattern.compile(TELEFONE_PATTERN);

  private Boolean erroRegistro = new Boolean(false);

  private static final Logger log = LoggerFactory.getLogger(ParticipanteService.class);

  private ParticipanteRepository repository;

  @Autowired private EmailService emailService;

  @Autowired
  public ParticipanteService(ParticipanteRepository repository) {
    super(repository);
    this.repository = repository;
  }

  @Autowired private ImportacaoPreCadastroService importacaoPreCadastroService;

  @Autowired private ParceiroAcumuloService parceiroAcumuloService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private PessoaLoyaltyService pessoaLoyaltyService;

  @Autowired private PessoaService pessoaService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  private final ReentrantLock lock = new ReentrantLock();

  public VerificacaoPreCadastroParticipanteTO verificaImportacaoPreCadastro(
      InputStream is, String nomeArquivo, SecurityUser user, Integer idProdutoInstituicao) {

    VerificacaoPreCadastroParticipanteTO verificacao = null;

    List<PreCadastroParticipanteTO> preCadastro = null;

    Map<String, VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO> map = null;

    preCadastro = importacaoPreCadastroService.getImportacao(is, null);

    verificacao = new VerificacaoPreCadastroParticipanteTO(nomeArquivo, preCadastro);

    verificaArquivoValoresRepetidos(verificacao, preCadastro);

    map = getVerificacoes(preCadastro, user, idProdutoInstituicao);

    verificacao.loadVerificacoesCargas(map);

    if (verificacao.getVerificacoes().size() > 0) {
      StringComparator comparator = new StringComparator();
      Collections.sort(verificacao.getVerificacoes(), comparator);
    }

    return verificacao;
  }

  public List<String> verificaArquivoValoresRepetidos(
      VerificacaoPreCadastroParticipanteTO verificaoTO, List<PreCadastroParticipanteTO> to) {
    PreCadastroParticipanteTO it = null;
    for (int j = 0; j < to.size(); j++) {
      it = to.get(j);
      it.setIdControl(j);
    }

    List<String> lista1;

    lista1 = verificaArquivoCpfRepetido(to);

    verificaoTO.setListaValorRepetido(lista1);

    return verificaoTO.getListaValorRepetido();
  }

  public List<String> verificaArquivoCpfRepetido(List<PreCadastroParticipanteTO> to) {
    List<String> novoArray = new ArrayList<String>();
    for (PreCadastroParticipanteTO tmp : to) {
      for (PreCadastroParticipanteTO chk : to) {
        if (tmp.getCpf() != null && !tmp.getCpf().isEmpty()) {
          if (!tmp.getIdControl().equals(chk.getIdControl())
              && tmp.getCpf().equals(chk.getCpf())
              && tmp.getDuplicatedRegister().equals(false)
              && chk.getDuplicatedRegister().equals(false)) {
            novoArray.add(
                "O CPF "
                    + chk.getCpf()
                    + ", do(a) participante "
                    + chk.getNomeCompleto()
                    + " é idêntico ao CPF do(a) participante "
                    + tmp.getNomeCompleto()
                    + "."
                    + "<< SOMENTE SERÁ REGISTRADO PARA: "
                    + tmp.getNomeCompleto()
                    + " >>");
            chk.setCpfDuplicated(true);
          }
        }
      }
    }
    return novoArray;
  }

  /** Buscar as verificações de uma lista importada */
  public Map<String, VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO> getVerificacoes(
      List<PreCadastroParticipanteTO> preCadastroTO,
      SecurityUser user,
      Integer idProdutoInstituicao) {

    Map<String, VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO> verificacoes =
        new HashMap<>();
    Map<String, VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO> tempVerificacoes = null;

    PreCadastroParticipanteTO to = null;

    for (int i = 0; i < preCadastroTO.size(); i++) {
      to = preCadastroTO.get(i);

      tempVerificacoes = getVerificacoesIteracao(to, user, i, idProdutoInstituicao);

      if (tempVerificacoes != null && !tempVerificacoes.isEmpty()) {
        verificacoes.putAll(tempVerificacoes);
      }
    }
    return verificacoes;
  }

  /** Buscar verifcicações de um registro da importação */
  public Map<String, VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO>
      getVerificacoesIteracao(
          PreCadastroParticipanteTO preCadastroTO,
          SecurityUser user,
          Integer linha,
          Integer idProdutoInstituicao) {

    Map<String, VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO> verificacoes =
        new HashMap<>();

    ParceiroAcumulo parceiro = new ParceiroAcumulo();

    String validaData = validadorData();

    erroRegistro = false;

    if (preCadastroTO.getIdParceiroAcumulo() == null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.PARCEIRO_ACUMULO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, " <<NÃO INFORMADO>> (Obrigatório)"));
      erroRegistro = true;
    }
    if (preCadastroTO.getIdParceiroAcumulo() != null) {
      parceiro = parceiroAcumuloService.findById(preCadastroTO.getIdParceiroAcumulo());
      if (parceiro == null) {
        throw new GenericServiceException(
            "Não existe parceiro de acúmulo cadastrado com o id: "
                + preCadastroTO.getIdParceiroAcumulo());
      }
    }

    // Verificando se o CPF foi informado
    if (preCadastroTO.getCpf() == null
        || preCadastroTO.getCpf().isEmpty()
        || preCadastroTO.getCpf().equals(CPF_INVALIDO)) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, " <<NÃO INFORMADO>> (Obrigatório)"));
      erroRegistro = true;
      // Verificando se o cpf é válido
    } else if (preCadastroTO.getCpf() != null && !DocumentoUtil.isCPF(preCadastroTO.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, "  <<INVÁLIDO>> (Obrigatório)"));
      erroRegistro = true;
    }

    ContaPagamento conta =
        contaPagamentoService.findByCpfAndProduto(preCadastroTO.getCpf(), idProdutoInstituicao, 1);

    // Verificando a existência de uma conta
    if (conta != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, preCadastroTO.getCpf()));
      erroRegistro = true;
    }

    //            if(preCadastroTO.getEmail()==null || preCadastroTO.getEmail().equals("-") ||
    // preCadastroTO.getEmail().isEmpty()){
    //                verificacoes.put(VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" +
    // linha, new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(0, linha, "<<NÃO
    // INFORMADO>> (Obrigatório)"));
    //                erroRegistro = true;
    //            }
    //            if(preCadastroTO.getEmail() != null && !preCadastroTO.getEmail().isEmpty() &&
    // !preCadastroTO.getEmail().equals("-")){
    //                if(!validarEmail(preCadastroTO.getEmail()) ){
    //                    verificacoes.put(VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":"
    // + linha, new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(0, linha, "<< "+
    // preCadastroTO.getEmail()+ " >>"));
    //                    erroRegistro = true;
    //                }
    //                if(preCadastroTO.getEmail().length()>TAMANHO_EMAIL){
    //                    verificacoes.put(VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":"
    // + linha, new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(0, linha, "<< LIMITE DE
    // 80 CARACTERES -- Adapte para até 80 caracteres >>"));
    //                    erroRegistro = true;
    //                }
    //            }

    //        	if (preCadastroTO.getNomeCompleto() !=null &&
    // !validarNome(preCadastroTO.getNomeCompleto())) {
    //                verificacoes.put(VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() +
    // ":" + linha, new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(0, linha,
    // preCadastroTO.getNomeCompleto().toString()));
    //                erroRegistro = true;
    //    		}else

    if (preCadastroTO.getNomeCompleto() != null) {
      if (preCadastroTO.getNomeCompleto().length() > TAMANHO_NOME_COMPLETO) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
                0, linha, "<< LIMITE DE 80 CARACTERES -- Adapte para até 80 caracteres >>"));
        erroRegistro = true;
      }
    }

    if (preCadastroTO.getDataNascimento() != null
        && !isDataNascimentoValida(preCadastroTO.getDataNascimento())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, preCadastroTO.getDataNascimento().toString()));
      erroRegistro = true;
    } else if (preCadastroTO.getDataNascimento() != null) {
      if (preCadastroTO.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
                0, linha, "<<DATA INCORRETA>>"));
        erroRegistro = true;
      }
    }

    if (preCadastroTO.getDddTelefone() != null
        && (preCadastroTO.getNumeroTelefone() == null
            || preCadastroTO.getNumeroTelefone().equals(""))) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0,
              linha,
              "CONTEM DDD <<"
                  + preCadastroTO.getDddTelefone()
                  + ">> MAS NÃO CONTEM NÚMERO DO TELEFONE"));
      erroRegistro = true;
    } else if ((preCadastroTO.getDddTelefone() == null || preCadastroTO.getDddTelefone().equals(""))
        && preCadastroTO.getNumeroTelefone() != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0,
              linha,
              "CONTEM NÚMERO DE TELEFONE <<"
                  + preCadastroTO.getNumeroTelefone()
                  + ">> MAS NÃO CONTEM DDD"));
      erroRegistro = true;
    } else if (preCadastroTO.getDddTelefone() != null
        && !preCadastroTO.getDddTelefone().isEmpty()
        && preCadastroTO.getNumeroTelefone() != null
        && !preCadastroTO.getNumeroTelefone().isEmpty()) {
      if (!isDDDCelularValido(preCadastroTO.getDddTelefone())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
                0, linha, "<<" + preCadastroTO.getDddTelefone() + ">>"));
        erroRegistro = true;
      } else if (!isCelularValido(preCadastroTO.getNumeroTelefone())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
                0, linha, "<<" + preCadastroTO.getDddTelefone() + ">>"));
        erroRegistro = true;
      }
    }

    if (preCadastroTO.getCpfDuplicated()
        && preCadastroTO.getCpf() != null
        && !preCadastroTO.getCpf().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              1, linha, preCadastroTO.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, "<<CPF DUPLICADO>>"));
    }
    if (preCadastroTO.getDuplicatedRegister()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              1, linha, preCadastroTO.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DUPLICADO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              0, linha, "<<REGISTRO DUPLICADO>>"));
    }
    if (erroRegistro) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoPreCadastroParticipanteTO.VerificacaoLinhaTO(
              1, linha, preCadastroTO.getCpf()));
    }

    return verificacoes;
  }

  public static boolean validarNome(String nomeCompleto) {
    Matcher matcher = patternNome.matcher(nomeCompleto);
    return matcher.matches();
  }

  public static boolean validarEmail(String email) {
    Matcher matcher = patternEmail.matcher(email);
    return matcher.matches();
  }

  public boolean isDDDCelularValido(String ddd) {
    if (StringUtils.isNotBlank(ddd)) {
      Matcher matcher = paternDdd.matcher(ddd);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  public boolean isCelularValido(String celular) {
    if (StringUtils.isNotBlank(celular)) {
      Matcher matcher = paternTelefone.matcher(celular);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  private String validadorData() {
    Date dataDt = new Date();
    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    dataDt = cal.getTime();
    String dataString = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    return dataString.substring(6, 10);
  }

  public boolean isDataNascimentoValida(Date dataNascimento) {
    if (dataNascimento != null) {
      return UtilValidator.isDataValida("EEE MMM dd kk:mm:ss z yyyy", dataNascimento.toString());
    }

    return Boolean.TRUE;
  }

  @Transactional
  public void SalvarContasPreCadastroParticipanteLoyalty(
      InputStream is, String nomeArquivo, Integer idProdutoInstituicao, SecurityUser user) {

    List<PreCadastroParticipanteTO> preCadastro = null;

    preCadastro = importacaoPreCadastroService.getImportacao(is, null);

    salvarCadastroCompletoByArquivo(preCadastro, idProdutoInstituicao, user);
  }

  @Transactional
  public List<ContaPagamento> salvarCadastroCompletoByArquivo(
      List<PreCadastroParticipanteTO> tos, Integer idProdutoInstituicao, SecurityUser user) {
    CadastrarContaPagamentoPessoaRequest pessoa;
    PreCadastroParticipanteTO to;

    List<ContaPagamento> listConta = new ArrayList<>();

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      if (to.getIsCadastroViaProcesso()
          || (to.getIsCadastroViaArquivo()
              && isCadastroCompletoArquivoValido(to, idProdutoInstituicao, user, i))) {
        pessoa = new CadastrarContaPagamentoPessoaRequest();

        ParceiroProgramaFidelidade parceiro = getParceiroProgramaFidelidade(idProdutoInstituicao);

        lock.lock();

        try {

          if (pessoaLoyaltyService.buscarPessoaInMaisPorDocumento(to.getCpf())) {
            throw new GenericServiceException(
                "Pessoa já cadastrada para a instituicao " + parceiro.getIdInstituicao());
          }

          preeencheDadosPessoa(user, parceiro, pessoa, to);
          validarInstituicaoPreencheDados(to, pessoa);
          pessoa.setValoresCargasProdutos(new ArrayList<>());
          pessoa
              .getValoresCargasProdutos()
              .add(new ValorCargaProdutoInstituicao(idProdutoInstituicao, Constantes.ZERO_DOUBLE));
          pessoa.setIdParceiroAcumulo(to.getIdParceiroAcumulo());

          if (to.isIntegracao() != null && to.isIntegracao()) {
            preenchePessoaInMaisAPartirDePessoaIMP(pessoa, to);
          }

          listConta.add(contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(pessoa));

          sendEmailPreCadastroInMais(to);
        } finally {
          lock.unlock();
        }
      }
    }
    return listConta;
  }

  private void validarInstituicaoPreencheDados(
      PreCadastroParticipanteTO to, CadastrarContaPagamentoPessoaRequest pessoa) {
    if ((Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(to.getIdInstituicao())
            || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(to.getIdInstituicao()))
        && (pessoa.getDddTelefoneCelular() == null || pessoa.getTelefoneCelular() == null)) {
      Pessoa pessoaIMP =
          pessoaService.findFirstByIdProcessadoraAndIdInstituicaoAndDocumento(
              Constantes.ID_PROCESSADORA_ITS_PAY,
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS,
              to.getCpf());
      if (pessoaIMP != null) {
        pessoa.setDddTelefoneCelular(
            pessoaIMP.getDddTelefoneCelular() != null
                ? pessoaIMP.getDddTelefoneCelular()
                : pessoaIMP.getDddTelefoneResidencial());
        pessoa.setTelefoneCelular(
            pessoaIMP.getTelefoneCelular() != null
                ? pessoaIMP.getTelefoneCelular()
                : pessoaIMP.getTelefoneResidencial());
      }
    }
  }

  private static void preeencheDadosPessoa(
      SecurityUser user,
      ParceiroProgramaFidelidade parceiro,
      CadastrarContaPagamentoPessoaRequest pessoa,
      PreCadastroParticipanteTO to) {
    if (parceiro != null) {
      pessoa.setIdInstituicao(parceiro.getIdInstituicao());
      pessoa.setIdProcessadora(parceiro.getIdProcessadora());
      pessoa.setIdRegional(parceiro.getIdRegional());
      pessoa.setIdFilial(parceiro.getIdFilial());
      pessoa.setIdPontoDeRelacionamento(parceiro.getIdPontoDeRelacionamento());
    }
    pessoa.setIdUsuarioInclusao(user.getIdUsuario());
    pessoa.setIsIntegracao(to.isIntegracao());
    pessoa.setIdPessoaOrigem(to.getIdPessoaOrigem());
    pessoa.setIdContaOrigem(to.getIdContaOrigem());
    if (to.getIsCadastroViaArquivo()) {
      pessoa.setCadastroOrigem(Constantes.ORIGEM_CADASTRO_VIA_ARQUIVO);
    } else if (to.getIsCadastroViaProcesso()) {
      pessoa.setCadastroOrigem(Constantes.ORIGEM_PRE_CADASTRO_VIA_PROCESSO);
    }

    pessoa.setTipoPessoa(Constantes.PESSOA_FISICA);
    pessoa.setEstrangeiro(Boolean.FALSE);
    if (to.getCpf() != null) {
      pessoa.setDocumento(to.getCpf());
      pessoa.setMatricula(to.getCpf());
    }
    if (to.getNomeCompleto() != null) pessoa.setNomeCompleto(to.getNomeCompleto());
    if (to.getNomeCompleto() == null)
      pessoa.setNomeCompleto(Constantes.PARTICIPANTE_PRE_CADASTRADO);

    pessoa.setNomeEmbossado(Abreviador.abreviarNome(pessoa.getNomeCompleto()));

    if (to.getDataNascimento() != null) pessoa.setDataNascimento(to.getDataNascimento());
    if (to.getEmail() != null) pessoa.setEmail(to.getEmail());
    if (to.getDddTelefone() != null) {
      Integer x = Integer.valueOf(to.getDddTelefone());
      pessoa.setDddTelefoneCelular(x);
    }
    if (to.getNumeroTelefone() != null) {
      Integer y = Integer.valueOf(to.getNumeroTelefone());
      pessoa.setTelefoneCelular(y);
    }
  }

  private void sendEmailPreCadastroInMais(PreCadastroParticipanteTO to) {
    String emailDeResposta;
    HierarquiaInstituicao instituicao =
        hierarquiaInstituicaoService.findByIdProcessadoraAndIdInstituicao(
            to.getIdProcessadora(), to.getIdInstituicao());
    if (instituicao != null && instituicao.getEmailReply() != null) {
      emailDeResposta = instituicao.getEmailReply();
    } else {
      emailDeResposta = "<EMAIL>";
    }
    if (to.getEmail() != null && to.getIsCadastroViaArquivo()) {
      try {
        emailService.sendEmailInmaisPreCadastro(
            to.getNomeCompleto(), emailDeResposta, to.getEmail(), to.getCpf());
      } catch (IOException e) {
        e.printStackTrace();
      }
    }
  }

  private ParceiroProgramaFidelidade getParceiroProgramaFidelidade(Integer idProdutoInstituicao) {
    ParceiroProgramaFidelidade parceiro = new ParceiroProgramaFidelidade();
    if (Constantes.PROD_INSTITUICAO_BASICO_IN_MAIS.equals(idProdutoInstituicao)) {

      parceiro =
          pessoaLoyaltyService.getParceiroProgramaFidelidadeNotNull(
              Constantes.ID_PROGRAMA_FIDELIDADE_IN_MAIS);

    } else if (Constantes.PROD_INSTITUICAO_BASICO_JOYPOINTS.equals(idProdutoInstituicao)) {

      parceiro =
          pessoaLoyaltyService.getParceiroProgramaFidelidadeNotNull(
              Constantes.ID_PROGRAMA_FIDELIDADE_JOYPOINTS);
    }
    return parceiro;
  }

  private void preenchePessoaInMaisAPartirDePessoaIMP(
      CadastrarContaPagamentoPessoaRequest pessoa, PreCadastroParticipanteTO to) {
    Pessoa pessoaIMP = pessoaService.findOneByIdPessoa(pessoa.getIdPessoaOrigem());
    if (pessoaIMP != null) {
      pessoa.setNaturalidade(pessoaIMP.getNaturalidade());
      pessoa.setNacionalidade(pessoaIMP.getNacionalidade());
      pessoa.setRg(pessoaIMP.getRg());
      pessoa.setRgOrgaoEmissor(pessoaIMP.getRgOrgaoEmissor());
      pessoa.setRgUfOrgaoEmissor(pessoaIMP.getRgUfOrgaoEmissor());
      pessoa.setRgDataEmissao(
          pessoaIMP.getRgDataEmissao() != null
              ? Date.from(pessoaIMP.getRgDataEmissao().atZone(ZoneId.systemDefault()).toInstant())
              : null);
      pessoa.setEstrangeiro(pessoaIMP.getEstrangeiro());
      pessoa.setPassaporte(pessoaIMP.getPassaporte());
      pessoa.setDataNascimento(
          pessoaIMP.getDataNascimento() != null
              ? Date.from(pessoaIMP.getDataNascimento().atZone(ZoneId.systemDefault()).toInstant())
              : null);
      pessoa.setNomePai(pessoaIMP.getNomePai());
      pessoa.setNomeMae(pessoaIMP.getNomeMae());
      pessoa.setEmail(pessoaIMP.getEmail());
      pessoa.setEmailProfissional(pessoaIMP.getEmailProfissional());
      pessoa.setDddTelefoneResidencial(pessoaIMP.getDddTelefoneResidencial());
      pessoa.setTelefoneResidencial(pessoaIMP.getTelefoneResidencial());
      pessoa.setDddTelefoneComercial(pessoaIMP.getDddTelefoneComercial());
      pessoa.setTelefoneComercial(pessoaIMP.getTelefoneComercial());
      pessoa.setDddTelefoneCelular(pessoaIMP.getDddTelefoneCelular());
      pessoa.setTelefoneCelular(pessoaIMP.getTelefoneCelular());
      pessoa.setIdSexo(pessoaIMP.getIdSexo());
      pessoa.setIdEstadoCivil(pessoaIMP.getIdEstadoCivil());
      pessoa.setIdUsuarioInclusao(pessoaIMP.getIdUsuarioInclusao());
      pessoa.setIdBanco(pessoaIMP.getIdBanco());
      pessoa.setIdAgencia(pessoaIMP.getIdAgencia());
      pessoa.setContaBancariaX(pessoaIMP.getContaBancariaX());
      pessoa.setContaBancaria(pessoaIMP.getContaBancaria());
      pessoa.setTipoContaBancaria(pessoaIMP.getTipoContaBancaria());
    }
  }

  public boolean isCadastroCompletoArquivoValido(
      PreCadastroParticipanteTO registro,
      Integer idProdutoInstituicao,
      SecurityUser user,
      Integer linha) {
    if (registro != null) {
      return getVerificacoesIteracao(registro, user, linha, idProdutoInstituicao).isEmpty();
    }

    return Boolean.FALSE;
  }

  public PreCadastroViaProcResponse preparaPreCadastroViaProcesso(
      SecurityUser user,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idParceiroAcumulo,
      String documento,
      String nome,
      Long idPessoaOrigem,
      Long idContaOrigem,
      Boolean isIntegracao) {

    PreCadastroParticipanteTO preCad = new PreCadastroParticipanteTO();
    preCad.setIdProcessadora(idProcessadora);
    preCad.setIdInstituicao(idInstituicao);
    preCad.setIdParceiroAcumulo(idParceiroAcumulo);
    preCad.setCpf(documento);
    preCad.setIsCadastroViaArquivo(Boolean.FALSE);
    preCad.setIsCadastroViaProcesso(Boolean.TRUE);
    preCad.setIntegracao(isIntegracao);
    preCad.setIdPessoaOrigem(idPessoaOrigem);
    preCad.setIdContaOrigem(idContaOrigem);
    if (nome != null) {
      preCad.setNomeCompleto(nome);
    }

    List<PreCadastroParticipanteTO> listPreCad = new ArrayList<PreCadastroParticipanteTO>();

    Integer idProdutoInstituicao = null;

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(idInstituicao)) {

      idProdutoInstituicao = Constantes.PROD_INSTITUICAO_BASICO_IN_MAIS;

      preCad.setIdProgramaFidelidade(Constantes.ID_PROGRAMA_FIDELIDADE_IN_MAIS);

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_JOY_POINTS.equals(idInstituicao)) {

      idProdutoInstituicao = Constantes.PROD_INSTITUICAO_BASICO_JOYPOINTS;

      preCad.setIdProgramaFidelidade(Constantes.ID_PROGRAMA_FIDELIDADE_JOYPOINTS);
    }

    listPreCad.add(preCad);

    List<ContaPagamento> contas =
        salvarCadastroCompletoByArquivo(listPreCad, idProdutoInstituicao, user);
    if (contas.size() == 0) {
      throw new GenericServiceException("Não foi possível salvar a conta");
    }

    ContaPagamento conta = contas.get(0);
    Credencial credencial =
        getCredencialService().buscarCredencialParaLancamentoManual(conta.getIdConta());

    log.info("conta existe: " + conta.getIdConta());

    PreCadastroViaProcResponse response =
        new PreCadastroViaProcResponse(
            conta.getIdConta(), conta.getIdAccountCode(), credencial.getTokenInterno());

    return response;
  }
}
