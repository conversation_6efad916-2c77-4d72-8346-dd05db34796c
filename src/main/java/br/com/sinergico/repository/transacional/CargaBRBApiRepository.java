package br.com.sinergico.repository.transacional;

import br.com.entity.transacional.CargaBRBApi;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CargaBRBApiRepository extends JpaRepository<CargaBRBApi, Long> {

  CargaBRBApi findByRrnAndStatus(String rrn, Integer status);

  CargaBRBApi findByRrnEstornadoAndStatus(String rrn, Integer status);
}
