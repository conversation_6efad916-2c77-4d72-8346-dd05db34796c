package br.com.sinergico.facade.cadastral;

import static br.com.sinergico.util.Util.getNullPropertyNames;

import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.cadastral.SetorFilial;
import br.com.entity.suporte.TipoRepresentanteLegal;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.AlterarAdicionalPessoa;
import br.com.json.bean.cadastral.AlterarPessoa;
import br.com.json.bean.cadastral.AlterarPessoaContato;
import br.com.json.bean.cadastral.AlterarPessoaPortador;
import br.com.json.bean.cadastral.AlterarRepresentanteLegal;
import br.com.json.bean.cadastral.CadastrarPessoaPDAFRequest;
import br.com.json.bean.cadastral.EnderecoPessoaRequest;
import br.com.json.bean.cadastral.GerarCredencialRequest;
import br.com.json.bean.cadastral.GetPessoaPortadorResponse;
import br.com.json.bean.cadastral.PessoaAdicionalPDAFRequest;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.cadastral.RepresentanteLegalRepository;
import br.com.sinergico.repository.suporte.TipoRepresentanteLegalRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GeradorCredencialService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.EnderecoPessoaService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.cadastral.RepresentanteLegalService;
import br.com.sinergico.service.cadastral.SetorFilialService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.SpringContextUtils;
import br.com.sinergico.util.Util;
import br.com.sinergico.util.bigdecimal.BigDecimalUtils;
import br.com.sinergico.validator.UtilValidator;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/** Created by evandro.soares on 05/09/17. */
@Service
public class PessoaFacade {

  public PessoaFacade() {
    // TODO Auto-generated constructor stub
  }

  @Autowired private SetorFilialService setorFilialService;

  @Autowired private PessoaService pessoaService;

  @Autowired private EnderecoPessoaService enderecoPessoaService;

  @Autowired private EmailService emailService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private RepresentanteLegalRepository representanteLegalRepository;

  @Autowired private TipoRepresentanteLegalRepository tipoRepresentanteRepository;

  @Autowired private CardService cardService;

  @Autowired private CredencialRepository credencialRepository;

  @Autowired private GeradorCredencialService geradorCredencialService;

  @Autowired private RepresentanteLegalService representanteLegalService;

  private static Logger log = LoggerFactory.getLogger(GeradorCredencialService.class);

  /** Status de uma credencial desbloqueada */
  private static final int STATUS_DESBLOQUEADO = 1;

  /** Status de uma credencial bloqueada na origem */
  private static final int STATUS_BLOQUEIO_ORIGEM = 0;

  public Pessoa updateAdcPessoa(AlterarAdicionalPessoa model) {

    Pessoa pessoaAdcBuscada = null;

    pessoaAdcBuscada = pessoaService.findById(model.getIdPessoa());

    String[] nullPropertyNames = pessoaService.getNullPropertyNames(model);

    if (pessoaAdcBuscada.getDataNascimento() != null) {
      pessoaAdcBuscada.setDataNascimento(
          DateUtil.dateToLocalDateTime(
              DateUtil.parseDate("dd/MM/yyyy", model.getDataNascimento())));
    }

    if (model.getPercentualLimiteAdicional() != null
        && BigDecimalUtils.is(model.getPercentualLimiteAdicional())
            .gt(ProdutoInstituicaoService.VALOR_MAXIMO_PERCENTUAL)) {
      throw new GenericServiceException(
          ProdutoInstituicaoService.MSG_ERRO_VALIDACAO_VALOR_PERCENTUAL_ADICIONAL);
    }

    ContaPessoa contaPessoa = pessoaAdcBuscada.getContasPessoa().stream().findFirst().get();

    BeanUtils.copyProperties(model, pessoaAdcBuscada, nullPropertyNames);

    contaPessoa.setPercentualLimiteAdicional(model.getPercentualLimiteAdicional());

    // update card da pessoa adicional
    List<Credencial> credenciais =
        getCredencialService()
            .recuperarCredenciaisPorIdConta(contaPessoa.getContaPagamento().getIdConta());

    log.info("Credenciais size :" + credenciais.size());
    for (Credencial credencial : credenciais) {

      log.info("Credencial getTokenInterno:" + credencial.getTokenInterno());
      log.info("Credencial getIdConta:" + credencial.getIdConta());
      log.info("Credencial getIdPessoa:" + credencial.getIdPessoa());
      log.info("contaPessoa.getPessoa().getIdPessoa()" + model.getIdPessoa());

      log.info(
          "credencial.getIdPessoa() == contaPessoa.getPessoa().getIdPessoa() ?"
              + (credencial.getIdPessoa().longValue() == model.getIdPessoa().longValue()));
      log.info(
          "( (credencial.getStatus() == STATUS_DESBLOQUEADO) || (credencial.getStatus() == STATUS_BLOQUEIO_ORIGEM) ) ?"
              + ((credencial.getStatus() == STATUS_DESBLOQUEADO)
                  || (credencial.getStatus() == STATUS_BLOQUEIO_ORIGEM)));

      if ((credencial.getIdPessoa().longValue() == model.getIdPessoa().longValue())
          && ((credencial.getStatus() == STATUS_DESBLOQUEADO)
              || (credencial.getStatus() == STATUS_BLOQUEIO_ORIGEM))) {

        GerarCredencialRequest request = new GerarCredencialRequest();
        request.setAdicional(Boolean.TRUE);
        request.setIdConta(contaPessoa.getContaPagamento().getIdConta());

        log.info("Atualizando cartao adicional. ");
        log.info("Token: " + credencial.getTokenInterno());
        log.info("contaPessoa.getIdPessoa(): " + contaPessoa.getIdPessoa());
        log.info("contaPessoa.getIdPessoa(): " + contaPessoa.getIdConta());
        log.info(
            "contaPessoa.getContaPagamento().getProdutoInstituicao().getId(): "
                + contaPessoa.getContaPagamento().getProdutoInstituicao().getId());
        log.info(
            "contaPessoa.getContaPagamento().getProdutoInstituicao().getProdutoInstituicaoConfiguracao(): "
                + contaPessoa
                    .getContaPagamento()
                    .getProdutoInstituicao()
                    .getProdutoInstituicaoConfiguracao()
                    .size());

        geradorCredencialService.atualizarInformacoesCartaoAdicional(
            request,
            contaPessoa
                .getContaPagamento()
                .getProdutoInstituicao()
                .getProdutoInstituicaoConfiguracao()
                .get(0),
            credencial,
            model.getPercentualLimiteAdicional());
      }
    }

    contaPessoaService.save(contaPessoa);

    return pessoaService.saveAndFlush(pessoaAdcBuscada);
  }

  public Pessoa updatePessoa(AlterarPessoa model, SecurityUser user, boolean checarUsuario) {

    if (model.getNomeEmbossado() != null
        && !UtilValidator.isNomeEmbossadoValid(model.getNomeEmbossado())) {
      throw new GenericServiceException("Nome Embossado inválido!");
    }

    Pessoa pessoaBuscada = null;
    if (model.getIdPessoa() != null) {
      pessoaBuscada = pessoaService.findById(model.getIdPessoa());
    }

    SetorFilial setorFilial = null;
    if (model.getIdSetorFilial() != null) {
      setorFilial = setorFilialService.findById(model.getIdSetorFilial());

      if (setorFilial == null) {
        throw new GenericServiceException(
            "Não foi possivel encontrar SetorFilial informado. idSetorFIlial = "
                + model.getIdSetorFilial());
      }
    }

    if (pessoaBuscada == null) {
      pessoaBuscada =
          pessoaService.findOneByHierarquia(
              setorFilial.getIdProcessadora(),
              setorFilial.getIdInstituicao(),
              model.getDocumento(),
              model.getTipoPessoa() != null ? model.getTipoPessoa() : model.getIdTipoPessoa(),
              setorFilial.getIdRegional(),
              setorFilial.getIdFilial(),
              setorFilial.getIdPontoRelacionamento());
    }

    if (pessoaBuscada == null) {
      throw new GenericServiceException("Pessoa Não encontrada. idPessoa = " + model.getIdPessoa());
    }

    if (checarUsuario) {
      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, pessoaBuscada);
    }

    // Caso seja b2b e tenha setor filial.
    if (setorFilial != null) {

      // Caso o documento tenha mudado, busca se já existe a pessoa pela hierarquia do setor filial.
      // Se já existir, não permitir a troca de documento pois
      // a conta deveria ter sido vinculada ao cadastro de pessoa já existe.
      if (pessoaBuscada != null && !pessoaBuscada.getDocumento().equals(model.getDocumento())) {
        Pessoa pessoaExistente =
            pessoaService.findOneByHierarquia(
                setorFilial.getIdProcessadora(),
                setorFilial.getIdInstituicao(),
                model.getDocumento(),
                model.getTipoPessoa() != null ? model.getTipoPessoa() : model.getIdTipoPessoa(),
                setorFilial.getIdRegional(),
                setorFilial.getIdFilial(),
                setorFilial.getIdPontoRelacionamento());

        if (pessoaExistente != null) {
          throw new GenericServiceException(
              "A troca de documento não é permitida pois já existe um cadastro de pessoa B2B com o mesmo documento. "
                  + "Caso o cadastro existente não possua o produto desta conta, adicione o produto ao cadastro B2B existente para criar uma nova conta.");
        }
      }
    } else {

      // Caso o documento tenha mudado, busca se já existe a pessoa pela hierarquia da instituição,
      // documento e tipo pessoa.
      // Se já existir, não permitir a troca de documento pois a conta deveria ter sido vinculada ao
      // cadastro de pessoa já existe.
      if (pessoaBuscada != null && !pessoaBuscada.getDocumento().equals(model.getDocumento())) {
        Pessoa pessoaExistente = null;

        Boolean isCredito = isPessoaCredito(pessoaBuscada.getIdPessoa());

        String msg = null;

        if (isCredito) {
          pessoaExistente =
              pessoaService
                  .findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsCredito(
                      model.getIdProcessadora(),
                      model.getIdInstituicao(),
                      model.getDocumento(),
                      model.getTipoPessoa());

          msg =
              "A troca de documento não é permitida pois já existe um cadastro de pessoa não B2B para produtos de crédito com o mesmo documento. ";
        } else {
          pessoaExistente =
              pessoaService
                  .findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsNotCredito(
                      model.getIdProcessadora(),
                      model.getIdInstituicao(),
                      model.getDocumento(),
                      model.getTipoPessoa());

          msg =
              "A troca de documento não é permitida pois já existe um cadastro de pessoa não B2B para produtos que não são de crédito com o mesmo documento. ";
        }

        if (pessoaExistente != null) {
          throw new GenericServiceException(
              msg
                  + "Caso o cadastro existente não possua o produto desta conta, crie uma nova conta com o documento desejado para vincular a pessoa existe ao produto.");
        }
      }
    }

    // copiando apenas os campos preenchidos do model pra o persistivel
    String[] nullPropertyNames = pessoaService.getNullPropertyNames(model);
    BeanUtils.copyProperties(model, pessoaBuscada, nullPropertyNames);
    pessoaBuscada.setDataHoraUltimaAtualizacao(LocalDateTime.now());
    pessoaBuscada.setIdUsuarioManutencao(model.getIdUsuarioManutencao());

    if (model.getCnpjVinculante() != null) {
      String cnpjSemMascara =
          model
              .getCnpjVinculante()
              .replaceAll("\\.", "")
              .replaceAll("-", "")
              .replaceAll("/", "")
              .replaceAll(" ", "");
      pessoaBuscada.setCnpjVinculante(cnpjSemMascara);
    }

    if (model.getConsultaRestrita() != null) {
      pessoaBuscada.setConsultaRestrita(model.getConsultaRestrita());
    }

    if (model.getIdConta() != null
        && model.getIdConta()
            != 0) { // se contem idConta quer dizer que está solicitando a inclusão de um nome para
      // o cartão
      ContaPessoa contaPessoa =
          contaPessoaService.findOneByIdPessoaAndIdConta(
              pessoaBuscada.getIdPessoa(), model.getIdConta().longValue());
      contaPessoa.setNomeCartaoImpresso(model.getNomeCartaoImpresso());
      contaPessoaService.saveAndFlush(contaPessoa);
    }

    if (model.getDataFundacao() != null) {
      pessoaBuscada.setDataFundacao(DateUtil.dateToLocalDateTime(model.getDataFundacao()));
    }

    if (model.getDataNascimento() != null) {
      pessoaBuscada.setDataNascimento(DateUtil.dateToLocalDateTime(model.getDataNascimento()));
    }

    if (model.getRgDataEmissao() != null) {
      pessoaBuscada.setRgDataEmissao(DateUtil.dateToLocalDateTime(model.getRgDataEmissao()));
    }
    if (model.getEnderecosPessoaRequest() != null
        && model.getEnderecosPessoaRequest().getEnderecos().size() > 0
        && !model.getEnderecosPessoaRequest().getEnderecos().isEmpty()) {
      List<EnderecoPessoa> enderecosPessoa = new ArrayList<EnderecoPessoa>();
      for (EnderecoPessoaRequest end : model.getEnderecosPessoaRequest().getEnderecos()) {
        EnderecoPessoa endPessoa = new EnderecoPessoa();
        if (end.getIdEndereco() != null) {
          endPessoa = enderecoPessoaService.findById(end.getIdEndereco());
        } else {
          endPessoa.setDtHrInclusao(new Date());
        }
        BeanUtils.copyProperties(end, endPessoa);
        endPessoa.setIdPessoa(model.getIdPessoa());
        endPessoa.setStatus(Constantes.ENDERECO_ATIVO);
        endPessoa.setDataHoraStatus(LocalDateTime.now());
        enderecoPessoaService.save(endPessoa);
      }
    }
    pessoaService.saveAndFlush(pessoaBuscada);
    return pessoaBuscada;
  }

  @org.springframework.transaction.annotation.Transactional
  public void alterarPessoaPortador(
      AlterarPessoaPortador model, SecurityUserPortador userPortador) {

    List<Pessoa> pessoas = getPessoasLogadas(userPortador);
    for (Pessoa pessoa : pessoas) {
      BeanUtils.copyProperties(model, pessoa, getNullPropertyNames(model));

      if (model.getDataNascimento() != null) {
        pessoa.setDataNascimento(DateUtil.dateToLocalDateTime(model.getDataNascimento()));
      }
      if (model.getRgDataEmissao() != null) {
        pessoa.setRgDataEmissao(DateUtil.dateToLocalDateTime(model.getRgDataEmissao()));
      }

      if (model.getEnderecosPessoaRequest() != null) {

        List<EnderecoPessoaRequest> enderecosPessoaRequest =
            model.getEnderecosPessoaRequest().getEnderecos();
        List<EnderecoPessoa> enderecosPessoa = pessoa.getEnderecosPessoa();
        if (enderecosPessoa != null) {
          for (EnderecoPessoaRequest enderecoRequest : enderecosPessoaRequest) {
            EnderecoPessoa enderecoMudar =
                enderecosPessoa.stream()
                    .filter(
                        endereco ->
                            enderecoRequest
                                .getIdTipoEndereco()
                                .equals(endereco.getIdTipoEndereco()))
                    .findFirst()
                    .orElse(null);
            if (enderecoMudar != null) {
              BeanUtils.copyProperties(
                  enderecoRequest, enderecoMudar, getNullPropertyNames(enderecoRequest));
              enderecoMudar.setIdPessoa(pessoa.getIdPessoa());
              enderecoPessoaService.save(enderecoMudar);
            }
          }
        }
      }
    }
    pessoaService.saveAll(pessoas);
  }

  public void editarPJPDAF(
      CadastrarPessoaPDAFRequest model,
      SecurityUser user,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao,
      String nomeSetorFilial) {

    BeanUtils.copyProperties(model, getNullPropertyNames(model));
    Pessoa pessoaPDAF =
        pessoaService.getPessoaPDAFByDocumentoAndHierarquia(
            model.getDocumento(),
            idRegional,
            idFilial,
            idPontoDeRelacionamento,
            idProdutoInstituicao);
    ContaPessoa contaPessoa =
        pessoaService.getContaPessoaPDAFByDocumentoAndHierarquia(
            model.getDocumento(),
            idRegional,
            idFilial,
            idPontoDeRelacionamento,
            idProdutoInstituicao);
    List<EnderecoPessoa> enderecoPessoaList =
        enderecoPessoaService.findByIdPessoaAndStatus(
            pessoaPDAF.getIdPessoa(), Constantes.ENDERECO_ATIVO);
    EnderecoPessoa enderecoPessoa = enderecoPessoaList.get(0);
    List<Pessoa> listaPessoa =
        pessoaService.getPessoasAdicionaisPDAFByIdConta(contaPessoa.getIdConta());

    List<PessoaAdicionalPDAFRequest> adicionais = new ArrayList<>();
    // Buscando/Salvando setor filial
    SetorFilial setorFilial = setorFilialService.saveByDescricao(nomeSetorFilial, user);

    Map<String, Object> additionalProperties = model.getAdditionalProperties();
    for (int i = 1; i <= 3; i++) {
      String nomeKey = "nome_adc_" + i;
      String dataNascimentoKey = "dt_nascimento_adc_" + i;
      String cpfKey = "cpf_adc_" + i;
      String cargoKey = "cargo_adc_" + i;
      String telefoneKey = "telefone_adc_" + i;
      String emailKey = "email_adc_" + i;
      String nomeMaeKey = "nome_mae_adc_" + i;
      String cepKey = "cep_adc_" + i;
      String logradouroKey = "logradouro_adc_" + i;
      String numeroKey = "numero_adc_" + i;
      String complementoKey = "complemento_adc_" + i;
      String bairroKey = "bairro_adc_" + i;
      String cidadeKey = "cidade_adc_" + i;
      String ufKey = "uf_adc_" + i;
      if (additionalProperties.containsKey(cpfKey) && additionalProperties.containsKey(nomeKey)) {
        PessoaAdicionalPDAFRequest adicional = new PessoaAdicionalPDAFRequest();
        adicional.setNome((String) additionalProperties.get(nomeKey));
        adicional.setDataNascimentoFromMap(additionalProperties.get(dataNascimentoKey));
        adicional.setCpf((String) additionalProperties.get(cpfKey));
        adicional.setCargo((String) additionalProperties.get(cargoKey));
        adicional.setTelefone((String) additionalProperties.get(telefoneKey));
        adicional.setEmail((String) additionalProperties.get(emailKey));
        adicional.setNomeMae((String) additionalProperties.get(nomeMaeKey));
        adicional.setCep((String) additionalProperties.get(cepKey));
        adicional.setLogradouro((String) additionalProperties.get(logradouroKey));
        adicional.setNumero((String) additionalProperties.get(numeroKey));
        adicional.setComplemento((String) additionalProperties.get(complementoKey));
        adicional.setBairro((String) additionalProperties.get(bairroKey));
        adicional.setCidade((String) additionalProperties.get(cidadeKey));
        adicional.setUf((String) additionalProperties.get(ufKey));
        adicionais.add(adicional);
      }
    }

    if (StringUtils.isNotBlank(model.getRazaoSocial())) {
      pessoaPDAF.setRazaoSocial(model.getRazaoSocial());
    }
    if (model.getDtFundacao() != null) {
      pessoaPDAF.setDataFundacao(DateUtil.dateToLocalDateTime(model.getDtFundacao()));
    }
    if (StringUtils.isNotBlank(model.getNomeFantasia())) {
      pessoaPDAF.setNomeFantasia(model.getNomeFantasia());
    }
    if (StringUtils.isNotBlank(model.getInscricaoEstadual())) {
      pessoaPDAF.setInscricaoEstadual(model.getInscricaoEstadual());
    }
    if (StringUtils.isNotBlank(model.getInscricaoMunicipal())) {
      pessoaPDAF.setInscricaoMunicipal(model.getInscricaoMunicipal());
    }
    if (StringUtils.isNotBlank(model.getAtividadePrincipal())) {
      pessoaPDAF.setAtividadePrincipal(model.getAtividadePrincipal());
    }
    if (StringUtils.isNotBlank(model.getFormaConstituicao())) {
      pessoaPDAF.setFormaDeConstituicao(model.getFormaConstituicao());
    }
    if (StringUtils.isNotBlank(model.getCepEmpresa())) {
      enderecoPessoa.setCep(model.getCepEmpresa());
    }
    if (StringUtils.isNotBlank(model.getLogradouroEmpresa())) {
      enderecoPessoa.setLogradouro(model.getLogradouroEmpresa());
    }
    if (StringUtils.isNotBlank(model.getNumeroEmpresa())) {
      enderecoPessoa.setNumero(model.getNumeroEmpresa());
    }
    if (StringUtils.isNotBlank(model.getComplementoEmpresa())) {
      enderecoPessoa.setComplemento(model.getComplementoEmpresa());
    }
    if (StringUtils.isNotBlank(model.getBairroEmpresa())) {
      enderecoPessoa.setBairro(model.getBairroEmpresa());
    }
    if (StringUtils.isNotBlank(model.getCidadeEmpresa())) {
      enderecoPessoa.setCidade(model.getCidadeEmpresa());
    }
    if (StringUtils.isNotBlank(model.getUfEmpresa())) {
      enderecoPessoa.setUf(model.getUfEmpresa());
    }
    if (StringUtils.isNotBlank(model.getDddTelefoneFixoEmpresa())) {
      pessoaPDAF.setDddTelefoneComercial(Integer.valueOf(model.getDddTelefoneFixoEmpresa()));
    }
    if (StringUtils.isNotBlank(model.getNroTelefoneFixoEmpresa())) {
      pessoaPDAF.setTelefoneComercial(Integer.valueOf(model.getNroTelefoneFixoEmpresa()));
    }
    if (StringUtils.isNotBlank(model.getDddCelularEmpresa())) {
      pessoaPDAF.setDddTelefoneCelular(Integer.valueOf(model.getDddCelularEmpresa()));
    }
    if (StringUtils.isNotBlank(model.getNroCelularEmpresa())) {
      pessoaPDAF.setTelefoneCelular(Integer.valueOf(model.getNroCelularEmpresa()));
    }
    if (StringUtils.isNotBlank(model.getEmail())) {
      pessoaPDAF.setEmail(model.getEmail());
    }
    if (StringUtils.isNotBlank(model.getCodBanco())) {
      pessoaPDAF.setIdBanco(Integer.valueOf(model.getCodBanco()));
    }
    if (StringUtils.isNotBlank(model.getCodAgencia())) {
      pessoaPDAF.setIdAgencia(Integer.valueOf(model.getCodAgencia()));
    }
    if (StringUtils.isNotBlank(model.getContaCorrente())) {
      pessoaPDAF.setContaBancaria(Integer.valueOf(model.getContaCorrente()));
    }
    pessoaPDAF.setDataHoraUltimaAtualizacao(LocalDateTime.now());
    pessoaPDAF.setIdUsuarioManutencao(user.getIdUsuarioManutencao());
    pessoaPDAF.setIdSetorFilial(setorFilial.getIdSetorFilial());
    enderecoPessoa.setIdUsuarioManutencao(user.getIdUsuarioManutencao());
    enderecoPessoa.setDtHrInclusao(new Date());
    enderecoPessoaService.save(enderecoPessoa);
    pessoaService.save(pessoaPDAF);

    if (!adicionais.isEmpty()) {
      for (PessoaAdicionalPDAFRequest adicionalPDAFRequest : adicionais) {
        for (Pessoa pessoa : listaPessoa) {
          if (Objects.equals(adicionalPDAFRequest.getCpf(), pessoa.getDocumento())) {
            List<EnderecoPessoa> enderecoPessoaAdicionalList =
                enderecoPessoaService.findByIdPessoaAndStatus(
                    pessoaPDAF.getIdPessoa(), Constantes.ENDERECO_ATIVO);
            EnderecoPessoa enderecoPessoaAdicional =
                !enderecoPessoaAdicionalList.isEmpty()
                    ? enderecoPessoaAdicionalList.get(0)
                    : new EnderecoPessoa();
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getNome())) {
              pessoa.setNomeCompleto(adicionalPDAFRequest.getNome());
            }
            if (adicionalPDAFRequest.getDataNascimento() != null) {
              pessoa.setDataNascimento(
                  DateUtil.dateToLocalDateTime(adicionalPDAFRequest.getDataNascimento()));
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getTelefone())) {
              Integer ddd = Integer.parseInt(adicionalPDAFRequest.getTelefone().substring(0, 2));
              Integer celular = Integer.parseInt(adicionalPDAFRequest.getTelefone().substring(2));
              pessoa.setDddTelefoneCelular(ddd);
              pessoa.setTelefoneCelular(celular);
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getEmail())) {
              pessoa.setEmail(adicionalPDAFRequest.getEmail());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getNomeMae())) {
              pessoa.setNomeMae(adicionalPDAFRequest.getNomeMae());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getCep())) {
              enderecoPessoaAdicional.setCep(adicionalPDAFRequest.getCep());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getLogradouro())) {
              enderecoPessoaAdicional.setLogradouro(adicionalPDAFRequest.getLogradouro());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getNumero())) {
              enderecoPessoaAdicional.setNumero(adicionalPDAFRequest.getNumero());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getComplemento())) {
              enderecoPessoaAdicional.setComplemento(adicionalPDAFRequest.getComplemento());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getBairro())) {
              enderecoPessoaAdicional.setBairro(adicionalPDAFRequest.getBairro());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getCidade())) {
              enderecoPessoaAdicional.setCidade(adicionalPDAFRequest.getCidade());
            }
            if (StringUtils.isNotBlank(adicionalPDAFRequest.getUf())) {
              enderecoPessoaAdicional.setUf(adicionalPDAFRequest.getUf());
            }
            enderecoPessoaAdicional.setIdUsuarioManutencao(user.getIdUsuarioManutencao());
            enderecoPessoaAdicional.setDtHrInclusao(new Date());
            pessoa.setIdUsuarioManutencao(user.getIdUsuarioManutencao());
            pessoa.setDataHoraUltimaAtualizacao(LocalDateTime.now());
            pessoa.setIdSetorFilial(setorFilial.getIdSetorFilial());
            enderecoPessoaService.save(enderecoPessoaAdicional);
            pessoaService.save(pessoa);
          }
        }
      }
    }
  }

  private List<Pessoa> getPessoasLogadas(SecurityUserPortador userPortador) {
    List<Pessoa> pessoas = new ArrayList<>();
    if (userPortador.getCpf().length() == 14) {
      pessoas =
          pessoaService.findByIdProcessadoraAndIdInstituicaoAndDocumento(
              userPortador.getIdProcessadora(),
              userPortador.getIdInstituicao(),
              userPortador.getCpf());
    } else {
      pessoas =
          pessoaService.findByIdProcessadoraAndIdInstituicaoAndDocumento(
              userPortador.getIdProcessadora(),
              userPortador.getIdInstituicao(),
              userPortador.getCpf());
    }

    if (pessoas == null || pessoas.isEmpty()) {
      throw new GenericServiceException("Não foi possível editar dados.", "Pessoa não encontrada.");
    }
    return pessoas;
  }

  public GetPessoaPortadorResponse getPessoaLogada(SecurityUserPortador userPortador) {
    List<Pessoa> listPessoas = getPessoasLogadas(userPortador);
    // Busca a pessoa mais recente
    listPessoas.sort(Comparator.comparing(Pessoa::getIdPessoa).reversed());
    Pessoa pessoa = listPessoas.get(0);
    GetPessoaPortadorResponse pessoaPortadorResponse = new GetPessoaPortadorResponse();
    BeanUtils.copyProperties(pessoa, pessoaPortadorResponse, getNullPropertyNames(pessoa));
    pessoaPortadorResponse.setDataNascimento(
        Date.from(pessoa.getDataNascimento().atZone(ZoneId.systemDefault()).toInstant()));

    return pessoaPortadorResponse;
  }

  private boolean isPessoaCredito(Long idPessoa) {
    return pessoaService.pessoaHasContaCreditoNaoB2B(idPessoa);
  }

  public Pessoa updateContatoPessoa(AlterarPessoaContato model, SecurityUser user) {

    Pessoa pessoaBuscada = pessoaService.findById(model.getIdPessoa());
    if (pessoaBuscada == null) {
      throw new GenericServiceException("Pessoa Não encontrada. idPessoa = " + model.getIdPessoa());
    }

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, pessoaBuscada);

    String[] nullPropertyNames = pessoaService.getNullPropertyNames(model);
    BeanUtils.copyProperties(model, pessoaBuscada, nullPropertyNames);

    pessoaService.saveAndFlush(pessoaBuscada);

    return pessoaBuscada;
  }

  public RepresentanteLegal updateRepresentanteLegal(AlterarRepresentanteLegal model) {
    RepresentanteLegal representanteBuscado =
        representanteLegalRepository.findById(model.getIdRepresentanteLegal()).orElse(null);

    if (representanteBuscado == null) {
      throw new GenericServiceException(
          "Representante Legal não encontrado. ",
          "IdRepresentanteLegal: " + model.getIdRepresentanteLegal());
    }
    if (representanteBuscado.getStatus() != null
        && model.getStatus() != null
        && !representanteBuscado.getStatus().equals(model.getStatus())) {
      representanteBuscado.setDataHoraStatus(new Date());
    }
    if (model.getIdInstituicao() != null) {
      if (model.getStatus() == 1
          && Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(model.getIdInstituicao())) {
        emailService.enviarEmailAtivacaoPaxPay(
            representanteBuscado.getEmail(),
            "<EMAIL>",
            model.getIdConta(),
            model.getNomeFantasia());
      }
    }
    BeanUtils.copyProperties(model, representanteBuscado, Util.getNullPropertyNames(model));
    return representanteLegalRepository.saveAndFlush(representanteBuscado);
  }

  public List<TipoRepresentanteLegal> getTiposRepresentanteLegal() {
    return tipoRepresentanteRepository.findAll();
  }

  protected CredencialService getCredencialService() {
    return SpringContextUtils.getBean(CredencialService.class);
  }
}
