package br.com.sinergico.controller.cadastral;

import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.B2bFaturaPixService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/b2b-fatura-pix")
public class B2bFaturaPixController extends UtilController {

  @Autowired private B2bFaturaPixService service;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @ApiOperation(
      value = "Gera referência interna para criação de um QRCode Copia e Cola para cargas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.GET,
      path = "/gerar-referencia-interna",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_QRCODE_PIX_FATURA"})
  public ResponseEntity<Map<String, Object>> gerarReferenciaInterna() {

    SecurityUser user = getAuthenticatedUser(request, em);
    String referenciaInterna = service.gerarReferenciaInterna(user);

    return new ResponseEntity<>(
        Collections.singletonMap("referenciaInterna", referenciaInterna), HttpStatus.OK);
  }
}
