## Spring
server.port=28080
spring.application.name=issuerBack
server.servlet.context-path=/api

#DIRETORIOS PADROES
issuer.dir.modelos.cadastro=/plataforma/backend/arquivos/modelos/cadastro/
issuer.dir.entrada=/plataforma/producao/entrada/
issuer.dir.processados=/plataforma/producao/processados/
issuer.dir.saida=/plataforma/producao/saida/
issuer.dir.erro=/plataforma/producao/erro/
issuer.dir.alterarstatus=/plataforma/backend/arquivos/alterar_status/
issuer.dir.extrato=/plataforma/backend/arquivos/arquivo_extrato/
issuer.dir.rakuten=/plataforma/backend/arquivos/rakuten/
issuer.dir.regulamento=/plataforma/backend/arquivos/regulamento/
issuer.dir.emissor.lancamentos=/plataforma/backend/arquivos/emissores/lancamentos/
issuer.dir.ocr=/plataforma/backend/arquivos/ocr/
issuer.dir.proposta=/plataforma/backend/arquivos/proposta/
issuer.dir.emissor.logos=/plataforma/backend/arquivos/emissores/logos/
common.passwords.file=/plataforma/backend/arquivos/senhas_comuns.txt
issuer.dir.plasticos=/plataforma/backend/arquivos/emissores/plasticos/
issuer.dir.processadora.logos=/plataforma/backend/arquivos/processadoras/logos/
issuer.mktplace.dir.imgsku=/plataforma/backend/arquivos/mktplace/parceiro/sku/
issuer.mktplace.dir.imglogo=/plataforma/backend/arquivos/mktplace/parceiro/logo/
api.dir.emissores=/plataforma/backend/arquivos/emissores/
api.dir.b2bclient.relatorios.modelos=/plataforma/backend/arquivos/b2bclient/relatorios/modelos/
api.dir.joypoints.relatorios.modelos=/plataforma/backend/arquivos/joypoints/relatorios/modelos/
api.dir.issuer.relatorios.modelos=/plataforma/backend/arquivos/emissores/relatorios/modelos/
api.dir.b2bclient.arquivos=/plataforma/backend/arquivos/b2bclient/arquivos/
itspay.gerenciadorrw.direntrada=/plataforma/producao/entrada
itspay.gerenciadorrw.dirproc=/plataforma/producao/processados/
folder.relatorio.assincrono=/plataforma/backend/arquivos/emissores/relatorios/
issuer.dir.adquirentes=/plataforma/backend/arquivos/adquirentes/
email.inmais.anexos=/plataforma/backend/arquivos/emissores/inmais/
issuer.dir.emissor.documentos=/plataforma/backend/arquivos/emissores/upload/
issuer.dir.emissor.chamados.anexos=/plataforma/backend/arquivos/emissores/chamados/anexos/
issuer.dir.emissor.propostas=/plataforma/backend/arquivos/emissores/propostas/
#



# Connection database
spring.datasource.driverClassName=org.postgresql.Driver
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.minimum-idle=50
spring.datasource.hikari.connection-timeout=30000
#JPA
spring.jpa.database-platform=br.com.sinergico.PostgreSQLDialectCustom
spring.jpa.properties.hibernate.jdbc.batch_size=1000
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.temp.use_jdbc_metadata_defaults=false

# thumeleaf configurations
server.compression.enabled=false
server.compression.mime-types=application/json,application/xml,text/html,text/xml,text/plain
spring.thymeleaf.mode= LEGACYHTML5
spring.thymeleaf.cache=false


#Usuario de monitoramento
monitoramento.basic.rest.user=teste
monitoramento.basic.rest.password=teste


# Habilitar os probes
management.endpoint.health.probes.enabled=true
# Habilitar o endpoint do health no actuator
management.endpoint.health.enabled=true
# Habilitar o endpoint do prometheus no actuator
management.endpoint.prometheus.enabled=true
# Habilitar o endpoint do prometheus no actuator
management.endpoint.loggers.enabled=true
# Desabilitar a coleta de metricas padrao
management.endpoints.enabled-by-default=true
#Habilitar o endpoint de restart do spring cloud
management.endpoint.restart.enabled=true
# Expor os endpoints health e prometheus do actuator
management.endpoints.web.exposure.include=health, prometheus, restart, loggers
management.server.port=38080