package br.com.json.bean.cadastral;

import java.io.Serializable;
import javax.validation.constraints.NotNull;

public class SegundaViaCredencialRequest implements Serializable {

  private static final long serialVersionUID = -2909651646385074061L;

  @NotNull private Long idCredencial;

  private Boolean cobrarTarifa;

  private Boolean deveForcarCobranca;

  private Boolean primeiraViaFisica = Boolean.FALSE;

  private Long idConta;

  public Long getIdCredencial() {
    return idCredencial;
  }

  public void setIdCredencial(Long idCredencial) {
    this.idCredencial = idCredencial;
  }

  public Boolean getDeveForcarCobranca() {
    return deveForcarCobranca;
  }

  public void setDeveForcarCobranca(Boolean deveForcarCobranca) {
    this.deveForcarCobranca = deveForcarCobranca;
  }

  public Boolean getCobrarTarifa() {
    return cobrarTarifa;
  }

  public void setCobrarTarifa(Boolean cobrarTarifa) {
    this.cobrarTarifa = cobrarTarifa;
  }

  public Boolean getPrimeiraViaFisica() {
    return primeiraViaFisica;
  }

  public void setPrimeiraViaFisica(Boolean primeiraViaFisica) {
    this.primeiraViaFisica = primeiraViaFisica;
  }

  public Long getIdConta() {
    return idConta;
  }

  public void setIdConta(Long idConta) {
    this.idConta = idConta;
  }
}
