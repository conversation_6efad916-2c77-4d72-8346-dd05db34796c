package br.com.sinergico.repository.transacional;

import br.com.entity.transacional.CobrancaBancaria;
import br.com.json.bean.transacional.GetCobrancasBancarias;
import java.util.Date;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CobrancaBancariaRepository extends JpaRepository<CobrancaBancaria, Long> {

  @Query(
      "select new br.com.json.bean.transacional.GetCobrancasBancarias(cb.idCobrancaBancaria, cb.idMotivo, mc.descMotivo, cb.valorBoleto, cb.dataVencimento, cb.dataPagamento, cb.nossoNumeroBeneficiario) "
          + "from CobrancaBancaria cb "
          + "inner join cb.motivoCobranca mc "
          + "where cb.idConta = :idConta")
  List<GetCobrancasBancarias> findByIdConta(@Param("idConta") Long idConta);

  @Query(value = "select nextval('transacional.seq_cobranca_bancaria')", nativeQuery = true)
  Long nextValSeqIdCobrancaBancaria();

  List<CobrancaBancaria> findByIdProdutoInstituicao(Integer idProdutoInstituicao);

  @Query(
      value =
          "select * from transacional.cobranca_bancaria where id_conta = :idConta and data_vencimento = :dataVencimento",
      nativeQuery = true)
  List<CobrancaBancaria> getCobrancaOriginalRegistrada(
      @Param("dataVencimento") Date dataVencimento, @Param("idConta") Long idConta);
}
