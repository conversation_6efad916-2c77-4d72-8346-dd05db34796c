package br.com.json.bean.cadastral;

import br.com.entity.cadastral.ParceriaComercial;
import br.com.entity.cadastral.ParceriaComercialContrato;
import br.com.entity.cadastral.ParceriaComercialContratoId;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.TipoParceriaComercial;
import java.io.Serializable;

public class ParceriaComercialContratoDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private ParceriaComercialContratoId parceriaComercialContratoId;

  private ProdutoContratado produtoContratado;

  private ParceriaComercial parceria;

  private TipoParceriaComercial tipoParceria;

  private Integer status;

  private Long idEmpresa;

  private String nomeFantasiaEmpresa;

  private String documentoEmpresa;

  private HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento;

  public ParceriaComercialContratoDTO() {}

  public ParceriaComercialContratoDTO(ParceriaComercialContrato parceriaComercialContrato) {
    this.parceriaComercialContratoId =
        new ParceriaComercialContratoId(
            parceriaComercialContrato.getParceriaComercialContratoId().getIdContrato(),
            parceriaComercialContrato.getParceriaComercialContratoId().getIdParceira(),
            parceriaComercialContrato.getParceriaComercialContratoId().getIdTipo());
    this.produtoContratado = parceriaComercialContrato.getProdutoContratado();
    this.parceria = parceriaComercialContrato.getParceria();
    this.tipoParceria = parceriaComercialContrato.getTipoParceria();
    this.status = parceriaComercialContrato.getStatus();
  }

  public ParceriaComercialContratoDTO(
      ParceriaComercialContrato parceriaComercialContrato,
      HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento) {
    this.parceriaComercialContratoId =
        new ParceriaComercialContratoId(
            parceriaComercialContrato.getParceriaComercialContratoId().getIdContrato(),
            parceriaComercialContrato.getParceriaComercialContratoId().getIdParceira(),
            parceriaComercialContrato.getParceriaComercialContratoId().getIdTipo());
    this.produtoContratado = parceriaComercialContrato.getProdutoContratado();
    this.parceria = parceriaComercialContrato.getParceria();
    this.tipoParceria = parceriaComercialContrato.getTipoParceria();
    this.status = parceriaComercialContrato.getStatus();
    this.hierarquiaPontoDeRelacionamento = hierarquiaPontoDeRelacionamento;
  }

  public ParceriaComercialContratoId getParceriaComercialContratoId() {
    return parceriaComercialContratoId;
  }

  public void setParceriaComercialContratoId(
      ParceriaComercialContratoId parceriaComercialContratoId) {
    this.parceriaComercialContratoId = parceriaComercialContratoId;
  }

  public ProdutoContratado getProdutoContratado() {
    return produtoContratado;
  }

  public void setProdutoContratado(ProdutoContratado produtoContratado) {
    this.produtoContratado = produtoContratado;
  }

  public ParceriaComercial getParceria() {
    return parceria;
  }

  public void setParceria(ParceriaComercial parceria) {
    this.parceria = parceria;
  }

  public TipoParceriaComercial getTipoParceria() {
    return tipoParceria;
  }

  public void setTipoParceria(TipoParceriaComercial tipoParceria) {
    this.tipoParceria = tipoParceria;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public Long getIdEmpresa() {
    return idEmpresa;
  }

  public void setIdEmpresa(Long idEmpresa) {
    this.idEmpresa = idEmpresa;
  }

  public String getNomeFantasiaEmpresa() {
    return nomeFantasiaEmpresa;
  }

  public void setNomeFantasiaEmpresa(String nomeFantasiaEmpresa) {
    this.nomeFantasiaEmpresa = nomeFantasiaEmpresa;
  }

  public String getDocumentoEmpresa() {
    return documentoEmpresa;
  }

  public void setDocumentoEmpresa(String documentoEmpresa) {
    this.documentoEmpresa = documentoEmpresa;
  }

  public HierarquiaPontoDeRelacionamento getHierarquiaPontoDeRelacionamento() {
    return hierarquiaPontoDeRelacionamento;
  }

  public void setHierarquiaPontoDeRelacionamento(
      HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento) {
    this.hierarquiaPontoDeRelacionamento = hierarquiaPontoDeRelacionamento;
  }
}
