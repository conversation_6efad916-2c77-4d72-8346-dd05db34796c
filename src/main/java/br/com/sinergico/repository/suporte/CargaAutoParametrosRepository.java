package br.com.sinergico.repository.suporte;

import br.com.entity.suporte.CargaAutoParametros;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CargaAutoParametrosRepository extends JpaRepository<CargaAutoParametros, Integer> {

  CargaAutoParametros findByNmParametro(String nomeParametro);

  @Query(
      "select cp "
          + "from CargaAutoParametros as cp where cp.idInstituicaoEspecifica = :idInstituicaoEspecifica or cp.idInstituicaoEspecifica is null "
          + "order by cp.id asc ")
  List<CargaAutoParametros> buscarParametrosCarga(
      @Param("idInstituicaoEspecifica") Integer idInstituicaoEspecifica);
}
