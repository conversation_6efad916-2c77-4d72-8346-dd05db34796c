package br.com.json.bean.suporte;

import br.com.entity.suporte.HierarquiaInstituicao;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

public class Instituicao implements Comparable<Instituicao> {

  private String descInstituicao;
  private String descProcessadora;
  private Integer idInstituicao;
  private Integer idProcessadora;
  private String cnpj;
  private String cep;
  private String bairro;
  private String cidade;
  private String uf;
  private String logradouro;
  private String numero;
  private String complemento;
  private String urlLogo;
  private String razaoSocial;
  private Boolean acessoPropriaConta;
  private String InscricaoEstadual;
  private String inscricaoMunicipal;
  private Boolean emiteNotaFiscal;
  private String emailReply;
  private Integer optanteSimplesNacional;
  private Integer incentivoFiscal;
  private Integer codigoMunicipio;
  private String codigoTributacao;
  private BigDecimal aliquotaIss;
  private Integer issRetido;
  private String serieRps;
  private Integer tipoRps;
  private Integer statusRps;
  private String idTagRps;
  private String itemListaServico;
  private Integer exigibilidadeIss;
  private BigDecimal multaBeneficio;
  private BigDecimal jurosBeneficio;
  private List<ContatoResponse> contatos;
  private String textoInfCentralAtendimento;
  private String canalEmail;
  private Boolean redefinirSenhaCaf;
  private Integer metodoSegurancaTransacao;
  private Integer idStatus;
  private LocalDateTime dtHrStatus;
  private Boolean blNotificacao = Boolean.FALSE;
  private String sgInstituicao;
  private Boolean tipoLoginCustomizavel;
  private Boolean tipoProdutoCustomizavel;
  private Boolean permiteProcessamentoLancamentos;

  public Instituicao setCanalEmail(String canalEmail) {
    this.canalEmail = canalEmail;
    return this;
  }

  public String getCanalEmail() {
    return canalEmail;
  }

  public String getEmailReply() {
    return emailReply;
  }

  public void setEmailReply(String emailReply) {
    this.emailReply = emailReply;
  }

  public List<ContatoResponse> getContatos() {
    return contatos;
  }

  public void setContatos(List<ContatoResponse> contatos) {
    this.contatos = contatos;
  }

  public String getInscricaoEstadual() {
    return InscricaoEstadual;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    InscricaoEstadual = inscricaoEstadual;
  }

  public String getInscricaoMunicipal() {
    return inscricaoMunicipal;
  }

  public void setInscricaoMunicipal(String inscricaoMunicipal) {
    this.inscricaoMunicipal = inscricaoMunicipal;
  }

  public String getSerieRps() {
    return serieRps;
  }

  public void setSerieRps(String serieRps) {
    this.serieRps = serieRps;
  }

  public Integer getTipoRps() {
    return tipoRps;
  }

  public void setTipoRps(Integer tipoRps) {
    this.tipoRps = tipoRps;
  }

  public Integer getStatusRps() {
    return statusRps;
  }

  public void setStatusRps(Integer statusRps) {
    this.statusRps = statusRps;
  }

  public String getIdTagRps() {
    return idTagRps;
  }

  public void setIdTagRps(String idTagRps) {
    this.idTagRps = idTagRps;
  }

  public String getItemListaServico() {
    return itemListaServico;
  }

  public void setItemListaServico(String itemListaServico) {
    this.itemListaServico = itemListaServico;
  }

  public Integer getExigibilidadeIss() {
    return exigibilidadeIss;
  }

  public void setExigibilidadeIss(Integer exigibilidadeIss) {
    this.exigibilidadeIss = exigibilidadeIss;
  }

  public BigDecimal getMultaBeneficio() {
    return multaBeneficio;
  }

  public void setMultaBeneficio(BigDecimal multaBeneficio) {
    this.multaBeneficio = multaBeneficio;
  }

  public BigDecimal getJurosBeneficio() {
    return jurosBeneficio;
  }

  public void setJurosBeneficio(BigDecimal jurosBeneficio) {
    this.jurosBeneficio = jurosBeneficio;
  }

  public Instituicao(String descInstituicao, Integer idInstituicao) {
    this.descInstituicao = descInstituicao;
    this.idInstituicao = idInstituicao;
  }

  public Instituicao(
      String descInstituicao,
      String descProcessadora,
      Integer idInstituicao,
      Integer idProcessadora) {
    this.descInstituicao = descInstituicao;
    this.descProcessadora = descProcessadora;
    this.idInstituicao = idInstituicao;
    this.idProcessadora = idProcessadora;
  }

  public Instituicao() {}

  public Instituicao(HierarquiaInstituicao instituicao) {
    this.descInstituicao = instituicao.getDescInstituicao();
    this.descProcessadora = instituicao.getHierarquiaProcessadora().getDescProcessadora();
    this.idInstituicao = instituicao.getIdInstituicao();
    this.idProcessadora = instituicao.getIdProcessadora();
    this.cnpj = instituicao.getCnpj();
    this.cep = instituicao.getCep();
    this.bairro = instituicao.getBairro();
    this.cidade = instituicao.getCidade();
    this.uf = instituicao.getUf();
    this.logradouro = instituicao.getLogradouro();
    this.numero = instituicao.getNumero();
    this.urlLogo = instituicao.getUrlLogo();
    this.razaoSocial = instituicao.getRazaoSocial();
    this.acessoPropriaConta = instituicao.getAcessoPropriaConta();
    this.emiteNotaFiscal = instituicao.getEmiteNotaFiscal();
    this.optanteSimplesNacional = instituicao.getOptanteSimplesNacional();
    this.incentivoFiscal = instituicao.getIncentivoFiscal();
    this.codigoMunicipio = instituicao.getCodigoMunicipio();
    this.codigoTributacao = instituicao.getCodigoTributacao();
    this.aliquotaIss = instituicao.getAliquotaIss();
    this.issRetido = instituicao.getIssRetido();
    this.multaBeneficio = instituicao.getMultaBeneficio();
    this.jurosBeneficio = instituicao.getJurosBeneficio();
    this.textoInfCentralAtendimento = instituicao.getTextoInfCentralAtendimento();
    this.emailReply = instituicao.getEmailReply();
    this.canalEmail = instituicao.getCanalEmail();
    this.redefinirSenhaCaf = instituicao.getRedefinirSenhaCaf();
    this.metodoSegurancaTransacao = instituicao.getMetodoSegurancaTransacao();
    this.idStatus = instituicao.getIdStatus();
    this.dtHrStatus = instituicao.getDtHrStatus();
    this.sgInstituicao = instituicao.getSgInstituicao();
    this.permiteProcessamentoLancamentos = instituicao.getPermiteProcessamentoLancamentos();
  }

  public String getDescProcessadora() {
    return descProcessadora;
  }

  public void setDescProcessadora(String descProcessadora) {
    this.descProcessadora = descProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public String getDescInstituicao() {
    return descInstituicao;
  }

  public void setDescInstituicao(String descInstituicao) {
    this.descInstituicao = descInstituicao;
  }

  public String getCnpj() {
    return cnpj;
  }

  public void setCnpj(String cnpj) {
    this.cnpj = cnpj;
  }

  public String getCep() {
    return cep;
  }

  public void setCep(String cep) {
    this.cep = cep;
  }

  public String getBairro() {
    return bairro;
  }

  public void setBairro(String bairro) {
    this.bairro = bairro;
  }

  public String getCidade() {
    return cidade;
  }

  public void setCidade(String cidade) {
    this.cidade = cidade;
  }

  public String getUf() {
    return uf;
  }

  public void setUf(String uf) {
    this.uf = uf;
  }

  public String getLogradouro() {
    return logradouro;
  }

  public void setLogradouro(String logradouro) {
    this.logradouro = logradouro;
  }

  public String getUrlLogo() {
    return urlLogo;
  }

  public void setUrlLogo(String urlLogo) {
    this.urlLogo = urlLogo;
  }

  public String getRazaoSocial() {
    return razaoSocial;
  }

  public void setRazaoSocial(String razaoSocial) {
    this.razaoSocial = razaoSocial;
  }

  @Override
  public int compareTo(Instituicao o) {
    return this.descInstituicao.compareTo(o.descInstituicao);
  }

  public Boolean getAcessoPropriaConta() {
    return acessoPropriaConta;
  }

  public void setAcessoPropriaConta(Boolean acessoPropriaConta) {
    this.acessoPropriaConta = acessoPropriaConta;
  }

  public Boolean getEmiteNotaFiscal() {
    return emiteNotaFiscal;
  }

  public void setEmiteNotaFiscal(Boolean emiteNotaFiscal) {
    this.emiteNotaFiscal = emiteNotaFiscal;
  }

  public Integer getOptanteSimplesNacional() {
    return optanteSimplesNacional;
  }

  public void setOptanteSimplesNacional(Integer optanteSimplesNacional) {
    this.optanteSimplesNacional = optanteSimplesNacional;
  }

  public Integer getIncentivoFiscal() {
    return incentivoFiscal;
  }

  public void setIncentivoFiscal(Integer incentivoFiscal) {
    this.incentivoFiscal = incentivoFiscal;
  }

  public Integer getCodigoMunicipio() {
    return codigoMunicipio;
  }

  public void setCodigoMunicipio(Integer codigoMunicipio) {
    this.codigoMunicipio = codigoMunicipio;
  }

  public String getCodigoTributacao() {
    return codigoTributacao;
  }

  public void setCodigoTributacao(String codigoTributacao) {
    this.codigoTributacao = codigoTributacao;
  }

  public BigDecimal getAliquotaIss() {
    return aliquotaIss;
  }

  public void setAliquotaIss(BigDecimal aliquotaIss) {
    this.aliquotaIss = aliquotaIss;
  }

  public Integer getIssRetido() {
    return issRetido;
  }

  public void setIssRetido(Integer issRetido) {
    this.issRetido = issRetido;
  }

  public String getNumero() {
    return numero;
  }

  public void setNumero(String numero) {
    this.numero = numero;
  }

  public String getComplemento() {
    return complemento;
  }

  public void setComplemento(String complemento) {
    this.complemento = complemento;
  }

  public String getTextoInfCentralAtendimento() {
    return textoInfCentralAtendimento;
  }

  public void setTextoInfCentralAtendimento(String textoInfCentralAtendimento) {
    this.textoInfCentralAtendimento = textoInfCentralAtendimento;
  }

  public Boolean getRedefinirSenhaCaf() {
    return redefinirSenhaCaf;
  }

  public void setRedefinirSenhaCaf(Boolean redefinirSenhaCaf) {
    this.redefinirSenhaCaf = redefinirSenhaCaf;
  }

  public Integer getMetodoSegurancaTransacao() {
    return metodoSegurancaTransacao;
  }

  public void setMetodoSegurancaTransacao(Integer metodoSegurancaTransacao) {
    this.metodoSegurancaTransacao = metodoSegurancaTransacao;
  }

  public Integer getIdStatus() {
    return idStatus;
  }

  public void setIdStatus(Integer idStatus) {
    this.idStatus = idStatus;
  }

  public LocalDateTime getDtHrStatus() {
    return dtHrStatus;
  }

  public void setDtHrStatus(LocalDateTime dtHrStatus) {
    this.dtHrStatus = dtHrStatus;
  }

  public Boolean getBlNotificacao() {
    return blNotificacao;
  }

  public void setBlNotificacao(Boolean blNotificacao) {
    this.blNotificacao = blNotificacao;
  }

  public String getSgInstituicao() {
    return sgInstituicao;
  }

  public void setSgInstituicao(String sgInstituicao) {
    this.sgInstituicao = sgInstituicao;
  }

  public Boolean getTipoLoginCustomizavel() {
    return tipoLoginCustomizavel;
  }

  public void setTipoLoginCustomizavel(Boolean tipoLoginCustomizavel) {
    this.tipoLoginCustomizavel = tipoLoginCustomizavel;
  }

  public Boolean getTipoProdutoCustomizavel() {
    return tipoProdutoCustomizavel;
  }

  public void setTipoProdutoCustomizavel(Boolean tipoProdutoCustomizavel) {
    this.tipoProdutoCustomizavel = tipoProdutoCustomizavel;
  }

  public Boolean getPermiteProcessamentoLancamentos() {
    return permiteProcessamentoLancamentos;
  }

  public void setPermiteProcessamentoLancamentos(Boolean permiteProcessamentoLancamentos) {
    this.permiteProcessamentoLancamentos = permiteProcessamentoLancamentos;
  }
}
