package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ProdutoContratado;
import br.com.sinergico.enums.TipoProdutoEnum;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

/**
 * <AUTHOR>
 */
public interface ProdutoContratadoRepository extends JpaRepository<ProdutoContratado, Long> {
  /**
   * metodo responsavel por buscar um produtoContratado(nao cancelado) conforme a hierarquia do
   * contratante
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @param idProdInstituicao
   * @return ProdutoContratado
   */
  ProdutoContratado
      findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamentoAndIdProdInstituicaoAndDtHrCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoRelacionamento,
          Integer idProdInstituicao);

  /**
   * metodo rseponsavel por buscar todos os produtos contratados ativos por uma empresa b2b
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  List<ProdutoContratado>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamentoAndDtHrCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoRelacionamento);

  /**
   * metodo rseponsavel por buscar todos os produtos contratados que possuam identificador jcard não
   * nulo
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  @Query(
      value =
          "select pc.* "
              + "from cadastral.produto_contratado pc "
              + "where (pc.id_processadora = :idProcessadora "
              + "and pc.id_instituicao = :idInstituicao "
              + "and pc.id_regional = :idRegional "
              + "and pc.id_filial = :idFilial "
              + "and pc.id_ponto_de_relacionamento = :idPontoRelacionamento "
              + "and pc.id_prod_instituicao = :idProdInstituicao) "
              + "and pc.identificador_jcard is not null "
              + "order by pc.id_contrato desc "
              + "limit 1",
      nativeQuery = true)
  ProdutoContratado getProdutoIdentificadorNotNull(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento,
      @Param("idProdInstituicao") Integer idProdInstituicao);

  /**
   * metodo rseponsavel por buscar todos os produtos contratados ativos e inativos por uma empresa
   * b2b
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  List<ProdutoContratado>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamento(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoRelacionamento);

  @Query(
      "select pc "
          + "from ProdutoContratado pc "
          + "inner join pc.produtoInstituicao pi "
          + "inner join pi.produtoInstituicaoConfiguracao pic "
          + "where pc.dtHrCancelamento is null "
          + "and pc.idProcessadora = :idProcessadora "
          + "and pc.idInstituicao = :idInstituicao "
          + "and pc.idRegional = :idRegional "
          + "and pc.idFilial = :idFilial "
          + "and pc.idPontoRelacionamento = :idPontoRelacionamento "
          + "and pic.idProdutoPlataforma = 4 "
          + "and pic.idRelacionamento = 2")
  List<ProdutoContratado> recuperarProdContratadoConvByEmpresa(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "select pc "
          + "from ProdutoContratado pc "
          + "inner join pc.produtoInstituicao pi "
          + "inner join pi.produtoInstituicaoConfiguracao pic "
          + "where pc.dtHrCancelamento is null "
          + "and pic.idProdutoPlataforma = 4 "
          + "and pic.idRelacionamento = 2")
  List<ProdutoContratado> getProdsContratadosConvenio();

  List<ProdutoContratado> findByIdentificadorExternoAndDtHrCancelamentoIsNull(String idExterno);

  @Query(
      value = "select nextval('cadastral.\"seq_id_externo_prod_instituicao\"')",
      nativeQuery = true)
  Integer getProximoIdExternoProdInstituicao();

  @Query(
      value =
          "select pi"
              + "  from ProdutoContratado pi, Credencial c, ContaPagamento cp"
              + " where c.idConta = cp.idConta"
              + "   and pi.idInstituicao = cp.idInstituicao"
              + "   and pi.idProdInstituicao = cp.idProdutoInstituicao"
              + "   and pi.idProcessadora = cp.idProcessadora"
              + "   and pi.idRegional = cp.idRegional"
              + "   and pi.idFilial = cp.idFilial"
              + "   and pi.idPontoRelacionamento = cp.idPontoDeRelacionamento"
              + "   and pi.dtHrCancelamento is null"
              + "   and c.idCredencial = ?1")
  ProdutoContratado buscarProdutoCadastradoPorCredencial(Long idCredencial);

  @Query(
      value =
          "select pi.idContrato"
              + "  from ProdutoContratado pi"
              + " where pi.idProdInstituicao = ?1"
              + "   and pi.dtHrCancelamento is null")
  List<Long> buscarIdsPorProdInstituicao(Integer prodInstituicao);

  @Query(
      "select pc "
          + "from ProdutoContratado pc "
          + "inner join pc.produtoInstituicao pi "
          + "inner join pi.produtoInstituicaoConfiguracao pic "
          + "where pc.dtHrCancelamento is null "
          + "and pc.idProcessadora = :idProcessadora "
          + "and pc.idInstituicao = :idInstituicao "
          + "and pc.idRegional = :idRegional "
          + "and pc.idFilial = :idFilial "
          + "and pc.idPontoRelacionamento = :idPontoRelacionamento "
          + "and pc.idProdInstituicao = :idProdInstituicao "
          + "and pic.idGrupoProduto = :idGrupoProduto "
          + "and pic.tipoProduto = :tipoProduto")
  ProdutoContratado findProdutoContratadoPorIdGrupoETipoProduto(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento,
      @Param("idProdInstituicao") Integer idProdInstituicao,
      @Param("idGrupoProduto") Long idGrupoProduto,
      @Param("tipoProduto") TipoProdutoEnum tipoProduto);

  @Query(
      value =
          " SELECT pc FROM ProdutoContratado pc \n " + " WHERE pc.idContrato = :idContratoCorresp ")
  ProdutoContratado findProdutoContratadoCorresp(
      @Param("idContratoCorresp") Long idContratoCorresp);

  @Query(
      value =
          " select * from cadastral.produto_contratado pc \n "
              + " where pc.id_contrato_corresp = :idContrato ",
      nativeQuery = true)
  ProdutoContratado findProdutoContratadoCorrespOrigem(@Param("idContrato") Long idContrato);
}
