package br.com.sinergico.repository.suporte;

import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import java.util.Collection;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface HierarquiaInstituicaoRepository
    extends JpaRepository<HierarquiaInstituicao, HierarquiaInstituicaoId> {

  List<HierarquiaInstituicao> findByIdProcessadora(Integer idProcessadora);

  HierarquiaInstituicao findByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao);

  List<HierarquiaInstituicao> findByIdProcessadoraAndIdInstituicaoNotIn(
      Integer idProcessadora, Collection<Integer> exclusao);

  @Query(
      "select i.urlLogo FROM HierarquiaInstituicao i where i.idProcessadora = :idProcessadora and i.idInstituicao = :idInstituicao")
  String findUrlLogoByIdProcessadoraAndIdInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      value =
          "select i.id_layout_padrao from suporte.hierarquia_instituicao i "
              + "where i.id_processadora = :idProcessadora "
              + "and i.id_instituicao = :idInstituicao",
      nativeQuery = true)
  Long getLayoutPadrao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      value =
          "select i.desc_instituicao from suporte.hierarquia_instituicao i "
              + "where i.id_processadora = :idProcessadora "
              + "and i.id_instituicao = :idInstituicao",
      nativeQuery = true)
  String getDescInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      value =
          " select i.plasticoImgArqPadrao from HierarquiaInstituicao i "
              + " where i.id.idProcessadora = :idProcessadora "
              + " and i.id.idInstituicao = :idInstituicao")
  String findPlasticoImgArqPadraoByIdProcessadoraAndIdInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer IdInstituicao);

  @Query(
      value =
          " select i.plasticoImgMobileArqPadrao from HierarquiaInstituicao i "
              + " where i.id.idProcessadora = :idProcessadora "
              + " and i.id.idInstituicao = :idInstituicao")
  String findPlaticoImgMobileArqPadraoByIdProcessadoraAndIdInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer IdInstituicao);

  @Query(
      "select h.enviaDadosTotvs from HierarquiaInstituicao h where h.id.idProcessadora = :idProcessadora and h.id.idInstituicao = :idInstituicao")
  Boolean enviaDadosTotvs(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  HierarquiaInstituicao findByIdInstituicao(Integer idInstituicao);
}
