package br.com.sinergico.service;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import br.com.client.rest.jcard.json.bean.CreateCard;
import br.com.client.rest.jcard.json.bean.CreateCardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.suporte.Plastico;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.cadastral.GerarCredencialRequest;
import br.com.sinergico.repository.suporte.PlasticoRepository;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.CredencialContaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.util.MockUtil;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import javax.persistence.NoResultException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GeradorCredencialServiceTest {

  @Spy @InjectMocks private GeradorCredencialService geradorCredencialService;

  @Mock private CredencialContaService credencialContaService;
  @Mock private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;
  @Mock private ProdutoInstituicaoService produtoInstituicaoService;
  @Mock private PessoaService pessoaService;
  @Mock private ContaPagamentoService contaPagamentoService;
  @Mock private CredencialService credencialService;
  @Mock private PlasticoRepository plasticoRepository;
  @Mock private CardService cardService;
  @Mock private ContaPessoaService contaPessoaService;

  private GerarCredencialRequest request;
  private SecurityUserPortador userPortador;

  private static final Long ID_GRUPO_PRODUTO = 1L;

  @BeforeEach
  void setup() {
    request = new GerarCredencialRequest();
    request.setIdConta(
        MockUtil.getLongAleatorioEntre(MockUtil.getIdContaMin(), MockUtil.getIdContaMax()));
    request.setIdPessoa(
        MockUtil.getLongAleatorioEntre(MockUtil.getIdPessoaMin(), MockUtil.getIdPessoaMax()));
    request.setVirtual(true);

    userPortador = new SecurityUserPortador();
    userPortador.setCpf("01234567890");
    userPortador.setDocumentoAcesso("01234567890");
  }

  @Test
  void deveLancarNoResultExceptionQuandoNaoEncontraContaPagamento() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(null);

    assertThrows(
        NoResultException.class,
        () -> geradorCredencialService.decideGerarCredencialGrupo(request));
  }

  @Test
  void deveLancarExcecaoQuandoCredencialVirtualOuFisicaRecenteEstaAtiva() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    ProdutoInstituicaoConfiguracao pic = new ProdutoInstituicaoConfiguracao();
    pic.setIdGrupoProduto(ID_GRUPO_PRODUTO);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(pic);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(request.getIdPessoa());
    pessoa.setDocumento(userPortador.getCpf());
    when(pessoaService.findById(request.getIdPessoa())).thenReturn(pessoa);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdConta(request.getIdConta());
    conta.setIdProcessadora(10);
    conta.setIdProdutoInstituicao(123456);
    conta.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(conta);

    Credencial cred = new Credencial();
    cred.setStatus(CredencialService.DESBLOQUEADO);
    when(credencialService.findUltimaCredencialVirtualOuFisica(
            eq(userPortador.getCpf()),
            eq(ID_GRUPO_PRODUTO),
            any(),
            any(),
            any(),
            any(),
            any(),
            eq(1)))
        .thenReturn(cred);

    GenericServiceException ex =
        assertThrows(
            GenericServiceException.class,
            () -> geradorCredencialService.decideGerarCredencialGrupo(request));
    assertTrue(ex.getMessage().contains("Já existe credencial virtual"));
  }

  @Test
  void deveGerarCredencialQuandoNaoHaGrupoProduto() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    ProdutoInstituicaoConfiguracao pic = new ProdutoInstituicaoConfiguracao();
    pic.setIdGrupoProduto(ID_GRUPO_PRODUTO);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(pic);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(request.getIdPessoa());
    pessoa.setDocumento(userPortador.getCpf());
    when(pessoaService.findById(request.getIdPessoa())).thenReturn(pessoa);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdConta(request.getIdConta());
    conta.setIdProcessadora(10);
    conta.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    conta.setIdProdutoInstituicao(123456);
    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(conta);

    ProdutoInstituicaoConfiguracao picSemGrupo = new ProdutoInstituicaoConfiguracao();
    picSemGrupo.setIdGrupoProduto(null);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(picSemGrupo);

    doReturn(new CredencialGerada(new Credencial()))
        .when(geradorCredencialService)
        .gerarCredencial(any(GerarCredencialRequest.class));

    CredencialGerada result = geradorCredencialService.decideGerarCredencialGrupo(request);

    assertNotNull(result);
    verify(geradorCredencialService, times(1)).gerarCredencial(any(GerarCredencialRequest.class));
    verify(credencialService, never())
        .findUltimaCredencialVirtualOuFisica(
            anyString(), anyLong(), any(), any(), any(), any(), any(), anyInt());
  }

  @Test
  void deveLancarNoResultExceptionQuandoNaoEncontraProdutoInstituicaoConfiguracao() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    ProdutoInstituicaoConfiguracao pic = new ProdutoInstituicaoConfiguracao();
    pic.setIdGrupoProduto(ID_GRUPO_PRODUTO);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(pic);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(request.getIdPessoa());
    when(pessoaService.findById(request.getIdPessoa())).thenReturn(pessoa);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdConta(request.getIdConta());
    conta.setIdProcessadora(10);
    conta.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    conta.setIdProdutoInstituicao(123456);
    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(conta);

    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(null);

    assertThrows(
        NoResultException.class,
        () -> geradorCredencialService.decideGerarCredencialGrupo(request));
  }

  @Test
  void naoDeveLancarExcecaoQuandoUserPortadorNullMasPessoaContaValida() {
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(request.getIdPessoa());
    when(pessoaService.findById(request.getIdPessoa())).thenReturn(pessoa);

    ProdutoInstituicaoConfiguracao pic = new ProdutoInstituicaoConfiguracao();
    pic.setIdGrupoProduto(ID_GRUPO_PRODUTO);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(pic);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdConta(request.getIdConta());
    conta.setIdProcessadora(10);
    conta.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    conta.setIdProdutoInstituicao(123456);
    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(conta);

    doReturn(new CredencialGerada(new Credencial()))
        .when(geradorCredencialService)
        .gerarCredencial(any(GerarCredencialRequest.class));

    assertDoesNotThrow(() -> geradorCredencialService.decideGerarCredencialGrupo(request));
  }

  @Test
  void deveLancarExcecaoQuandoPessoaNaoEstaAssociadaConta() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);

    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(499999L));

    GenericServiceException ex =
        assertThrows(
            GenericServiceException.class,
            () -> geradorCredencialService.decideGerarCredencialGrupo(request));

    assertTrue(
        ex.getMessage().contains(ConstantesErro.CAR_GERACAO_PESSOA_DIVERGENTE_CONTA.getMensagem()));
  }

  @Test
  void deveGerarNovaCredencialQuandoNaoExisteCredencialAnteriorNoGrupo() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    ProdutoInstituicaoConfiguracao pic = new ProdutoInstituicaoConfiguracao();
    pic.setIdGrupoProduto(ID_GRUPO_PRODUTO);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(pic);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(request.getIdPessoa());
    pessoa.setDocumento(userPortador.getCpf());
    when(pessoaService.findById(request.getIdPessoa())).thenReturn(pessoa);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdConta(request.getIdConta());
    conta.setIdProcessadora(10);
    conta.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    conta.setIdProdutoInstituicao(123456);
    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(conta);

    when(credencialService.findUltimaCredencialVirtualOuFisica(
            eq(userPortador.getCpf()),
            eq(ID_GRUPO_PRODUTO),
            any(),
            any(),
            any(),
            any(),
            any(),
            eq(1)))
        .thenReturn(null);

    Credencial novaCred = new Credencial();
    novaCred.setIdCredencial(1234L);
    doReturn(new CredencialGerada(novaCred))
        .when(geradorCredencialService)
        .gerarCredencial(any(GerarCredencialRequest.class));

    CredencialGerada result = geradorCredencialService.decideGerarCredencialGrupo(request);

    assertNotNull(result);
    verify(geradorCredencialService, times(1)).gerarCredencial(request);
    verify(credencialContaService, times(1))
        .vincularCredencialContasMesmoGrupo(eq(novaCred), eq(pessoa), eq(conta));
  }

  @Test
  void deveLancarExcecaoQuandoExisteCredencialAnteriorInativaNoGrupo() {
    lenient()
        .when(pessoaService.existeDocumentoPessoasConta(anyString(), eq(request.getIdConta())))
        .thenReturn(true);
    when(pessoaService.findIdPessoasByIdConta(request.getIdConta()))
        .thenReturn(Collections.singletonList(request.getIdPessoa()));

    ProdutoInstituicaoConfiguracao pic = new ProdutoInstituicaoConfiguracao();
    pic.setIdGrupoProduto(ID_GRUPO_PRODUTO);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(any(), any(), any()))
        .thenReturn(pic);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(request.getIdPessoa());
    pessoa.setDocumento(userPortador.getCpf());
    when(pessoaService.findById(request.getIdPessoa())).thenReturn(pessoa);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdConta(request.getIdConta());
    conta.setIdProcessadora(10);
    conta.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    conta.setIdProdutoInstituicao(123456);
    when(contaPagamentoService.findById(request.getIdConta())).thenReturn(conta);

    Credencial credAnterior = new Credencial();
    credAnterior.setStatus(1);
    credAnterior.setIdCredencial(1111L);
    when(credencialService.findUltimaCredencialVirtualOuFisica(
            eq(userPortador.getCpf()),
            eq(ID_GRUPO_PRODUTO),
            any(),
            any(),
            any(),
            any(),
            any(),
            eq(1)))
        .thenReturn(credAnterior);

    GenericServiceException ex =
        assertThrows(
            GenericServiceException.class,
            () -> geradorCredencialService.decideGerarCredencialGrupo(request));

    assertTrue(
        ex.getMessage()
            .contains(
                ConstantesErro.CAR_GERACAO_CARTAO_VIRTUAL_OU_FISICO_JA_EXISTE.format(
                    Boolean.TRUE.equals(request.getVirtual()) ? "virtual" : "físico")));
  }

  @Test
  void testGerarCredencialComSMS() {

    Integer[] instituicoesNaoRecebeSenha = {
      Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH,
      Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS,
      Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL,
      Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO,
      Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK,
      Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY,
      Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII,
      Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS,
      Constantes.ID_PRODUCAO_INSTITUICAO_WIZ,
      Constantes.ID_PRODUCAO_QISTA
    };

    Long idConta =
        MockUtil.getLongAleatorioEntre(MockUtil.getIdContaMin(), MockUtil.getIdContaMax());
    Long idPessoa =
        MockUtil.getLongAleatorioEntre(MockUtil.getIdPessoaMin(), MockUtil.getIdPessoaMax());

    Credencial credencial = new Credencial();
    credencial.setIdCredencial(123L);
    credencial.setVirtual(true);
    credencial.setStatus(1);
    credencial.setCsn(2);

    request.setIdPessoa(idPessoa);
    request.setIdConta(idConta);
    request.setOnboard(null);
    request.setMultiConta(false);
    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(idPessoa);

    ContaPagamento conta = new ContaPagamento();
    conta.setIdProcessadora(10);
    conta.setIdInstituicao(8801);
    conta.setIdProdutoInstituicao(880101);
    conta.setIdConta(idConta);
    conta.setIdStatusConta(1);

    ContaPessoa contaPessoa = new ContaPessoa();
    contaPessoa.setIdPessoa(idPessoa);
    contaPessoa.setIdConta(idConta);

    ProdutoInstituicaoConfiguracao prodInstConf = new ProdutoInstituicaoConfiguracao();
    prodInstConf.setIdCardProduct(123L);
    prodInstConf.setMesesValidadeFisico(36);

    Plastico plastico = new Plastico();
    plastico.setProdutoInstituicaoConfiguracao(prodInstConf);
    List<Plastico> plasticoList = List.of(plastico);

    CreateCardResponse createCardResponse = new CreateCardResponse();
    createCardResponse.setBin("teste");
    createCardResponse.setSuccess(Boolean.TRUE);
    createCardResponse.setBin("123");
    createCardResponse.setBinLength(123);
    createCardResponse.setBinextended("123");
    createCardResponse.setLastfour("123");
    createCardResponse.setSmart(Boolean.TRUE);
    createCardResponse.setToken("1234564");

    when(pessoaService.findById(anyLong())).thenReturn(pessoa);
    when(produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(anyInt(), anyInt(), anyInt()))
        .thenReturn(prodInstConf);
    when(plasticoRepository
            .findByIdProcessadoraAndIdInstituicaoAndIdProdutoInstituicaoAndDataCancelamentoEmissaoIsNull(
                anyInt(), anyInt(), anyInt()))
        .thenReturn(plasticoList);
    when(contaPessoaService.findOneByIdPessoaAndIdConta(anyLong(), anyLong()))
        .thenReturn(contaPessoa);
    when(credencialService.saveAndFlush(any(Credencial.class))).thenReturn(credencial);

    when(cardService.createCard(any(CreateCard.class))).thenReturn(createCardResponse);

    Integer contador = 0;
    for (Integer instituicao : instituicoesNaoRecebeSenha) {
      contador++;
      conta.setIdInstituicao(instituicao);
      when(contaPagamentoService.findById(anyLong())).thenReturn(conta);
      CredencialGerada credencialGerada =
          geradorCredencialService.gerarCredencial(request, false, BigDecimal.ZERO);
      assertNotNull(credencialGerada);
      verify(credencialService, times(contador)).saveAndFlush(any(Credencial.class));
    }
  }
}
