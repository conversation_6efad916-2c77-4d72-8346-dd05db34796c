package br.com.json.bean.cadastral;

import br.com.sinergico.enums.TipoPortadorLoginEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.NotEmpty;

@ApiModel(value = "FazerLoginPortador")
public class FazerLoginPortador implements Serializable {

  private static final long serialVersionUID = 1L;

  private String cpf;

  // TODO Foi retirada a validacao para resolver o problema do BRB Card, Voltar anotation @CNPJ
  // quando eles corrigirem o aplicativo!
  // @CNPJ
  private String cnpj;

  // Foi removido para login corporativo pois o documento acesso não é um CPF
  // @CPF
  private String documentoAcesso;

  @NotEmpty private String senha;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idProcessadora;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idInstituicao;

  private TipoPortadorLoginEnum tipoLogin;

  private Integer origemAcesso;

  private String deviceId;
  private Integer sistemaOperacional;
  private String versaoConhecida;
  private String versaoInstalada;

  private String architectureInfo;
  private String model;
  private String platformName;
  private String plataformVersion;
  private String versaoAplicativo;
  private Integer idApp;

  private BigDecimal latitude;
  private BigDecimal longitude;

  private Integer acessoSite;

  private Long grupoAcesso;

  /** O ID do dispositivo no gateway de push */
  private String pushNotificationDeviceId;

  private Boolean novoOnboard;

  public FazerLoginPortador(
      String cpf,
      String senha,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer origemAcesso,
      Integer acessoSite) {
    this();
    this.cpf = cpf;
    this.senha = senha;
    this.idProcessadora = idProcessadora;
    this.idInstituicao = idInstituicao;
    this.origemAcesso = origemAcesso;
    this.acessoSite = acessoSite;
  }

  public FazerLoginPortador(
      String cpf,
      String senha,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer origemAcesso) {
    this();
    this.cpf = cpf;
    this.senha = senha;
    this.idProcessadora = idProcessadora;
    this.idInstituicao = idInstituicao;
    this.origemAcesso = origemAcesso;
  }

  public FazerLoginPortador() {
    super();
  }

  public String getCnpj() {
    return cnpj;
  }

  public void setCnpj(String cnpj) {
    this.cnpj = cnpj;
  }

  public Integer getAcessoSite() {
    return acessoSite;
  }

  public void setAcessoSite(Integer acessoSite) {
    this.acessoSite = acessoSite;
  }

  public Integer getSistemaOperacional() {
    return sistemaOperacional;
  }

  public void setSistemaOperacional(Integer sistemaOperacional) {
    this.sistemaOperacional = sistemaOperacional;
  }

  public String getVersaoConhecida() {
    return versaoConhecida;
  }

  public void setVersaoConhecida(String versaoConhecida) {
    this.versaoConhecida = versaoConhecida;
  }

  public String getVersaoInstalada() {
    return versaoInstalada;
  }

  public void setVersaoInstalada(String versaoInstalada) {
    this.versaoInstalada = versaoInstalada;
  }

  public Integer getIdApp() {
    return idApp;
  }

  public void setIdApp(Integer idApp) {
    this.idApp = idApp;
  }

  public String getArchitectureInfo() {
    return architectureInfo;
  }

  public void setArchitectureInfo(String architectureInfo) {
    this.architectureInfo = architectureInfo;
  }

  public String getModel() {
    return model;
  }

  public void setModel(String model) {
    this.model = model;
  }

  public String getPlatformName() {
    return platformName;
  }

  public void setPlatformName(String platformName) {
    this.platformName = platformName;
  }

  public String getPlataformVersion() {
    return plataformVersion;
  }

  public void setPlataformVersion(String plataformVersion) {
    this.plataformVersion = plataformVersion;
  }

  public Integer getOrigemAcesso() {
    return origemAcesso;
  }

  public void setOrigemAcesso(Integer origemAcesso) {
    this.origemAcesso = origemAcesso;
  }

  public String getCpf() {
    return cpf;
  }

  public void setCpf(String cpf) {
    this.cpf = cpf;
  }

  public String getSenha() {
    return senha;
  }

  public void setSenha(String senha) {
    this.senha = senha;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public TipoPortadorLoginEnum getTipoLogin() {
    return tipoLogin;
  }

  public void setTipoLogin(TipoPortadorLoginEnum tipoLogin) {
    this.tipoLogin = tipoLogin;
  }

  public BigDecimal getLatitude() {
    return latitude;
  }

  public void setLatitude(BigDecimal latitude) {
    this.latitude = latitude;
  }

  public BigDecimal getLongitude() {
    return longitude;
  }

  public void setLongitude(BigDecimal longitude) {
    this.longitude = longitude;
  }

  public String getDeviceId() {
    return deviceId;
  }

  public void setDeviceId(String deviceId) {
    this.deviceId = deviceId;
  }

  public String getPushNotificationDeviceId() {
    return pushNotificationDeviceId;
  }

  public String getDocumentoAcesso() {
    return documentoAcesso;
  }

  public void setDocumentoAcesso(String documentoAcesso) {
    this.documentoAcesso = documentoAcesso;
  }

  public Long getGrupoAcesso() {
    return grupoAcesso;
  }

  public void setGrupoAcesso(Long grupoAcesso) {
    this.grupoAcesso = grupoAcesso;
  }

  public void setPushNotificationDeviceId(String pushNotificationDeviceId) {
    this.pushNotificationDeviceId = pushNotificationDeviceId;
  }

  public String getVersaoAplicativo() {
    return versaoAplicativo;
  }

  public void setVersaoAplicativo(String versaoAplicativo) {
    this.versaoAplicativo = versaoAplicativo;
  }

  public Boolean getNovoOnboard() {
    return novoOnboard;
  }

  public void setNovoOnboard(Boolean novoOnboard) {
    this.novoOnboard = novoOnboard;
  }

  @Override
  public String toString() {
    return "FazerLoginPortador [cpf="
        + cpf
        + ", idProcessadora="
        + idProcessadora
        + ", idInstituicao="
        + idInstituicao
        + ", origemAcesso="
        + origemAcesso
        + ", acessoSite="
        + acessoSite
        + "]";
  }
}
