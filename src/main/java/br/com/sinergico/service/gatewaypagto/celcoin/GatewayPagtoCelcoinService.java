package br.com.sinergico.service.gatewaypagto.celcoin;

import static br.com.sinergico.enums.TipoSegmentoEnum.findTypeById;

import br.com.client.rest.gatewaypagto.json.bean.CaptureResponse;
import br.com.client.rest.gatewaypagto.json.bean.CargaValor;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarLinhaDigitavelTitulo;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarLinhaDigitavelTituloResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarOperadoraPorDDDResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarPagamentoLinhaDigitavelResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarPagamentoLinhaDigitavelResponse.RegisterData;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarValorOperadoraResponse;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTitulo;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTituloResponse;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarRecargaRequest;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarRecargaResponse;
import br.com.client.rest.gatewaypagto.json.bean.OperadoraRecarga;
import br.com.client.rest.gatewaypagto.json.bean.PagamentoCelcoinResponse;
import br.com.client.rest.gatewaypagto.json.bean.ReciboPagamentoCelcoin;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponseRrn;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.gatewaypagto.ContratoGatewayPagto;
import br.com.entity.gatewaypagto.ContratoGatewayPagtoInstTransacaoConfig;
import br.com.entity.gatewaypagto.Convenio;
import br.com.entity.gatewaypagto.ConvenioTipoSegmento;
import br.com.entity.gatewaypagto.LogPagtoTituloTransacao;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.gatewaypagto.LogRecargaTransacao;
import br.com.entity.jcard.LogTransacoes;
import br.com.entity.loyalty.ResgatePagamentoConta;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.suporte.ParametroDefinicao;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.suporte.RendimentoProperties;
import br.com.entity.transacional.CodigoBarrasDTO;
import br.com.entity.transacional.CodigoTransacao;
import br.com.entity.transacional.ConsultaBoletoResponse;
import br.com.entity.transacional.ConsultaDadosBoletoDARFRequest;
import br.com.entity.transacional.ConsultaDadosBoletoFGTSRequest;
import br.com.entity.transacional.ConsultaDadosBoletoRequest;
import br.com.entity.transacional.ConsultaPagamentoDTO;
import br.com.entity.transacional.EstornoTituloRendimentoRequest;
import br.com.entity.transacional.EstornoTituloRendimentoResponse;
import br.com.entity.transacional.FgtsConsultaRendimentoRequest;
import br.com.entity.transacional.LancamentoManual;
import br.com.entity.transacional.PagamentoBoletosRendimentoRequest;
import br.com.entity.transacional.PagamentoConsumoRendimentoRequest;
import br.com.entity.transacional.PagamentoTituloRendimentoRequest;
import br.com.entity.transacional.PagamentoTituloRendimentoResponse;
import br.com.entity.transacional.PagamentoTituloRendimentoResponseV2;
import br.com.entity.transacional.PagamentoTributoDARFRendimentoRequest;
import br.com.entity.transacional.PagamentoTributoFGTSRendimentoRequest;
import br.com.entity.transacional.PagamentoTributoRendimentoRequest;
import br.com.entity.transacional.PontoControle;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.JcardServiceException;
import br.com.json.bean.cadastral.DadosConta;
import br.com.json.bean.loyalty.ResgateLoyaltyRequestVO;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.json.bean.transacional.CadastroLancamentoManual;
import br.com.json.bean.transacional.ConsultaPagamentoValue;
import br.com.json.bean.transacional.PagamentoConsultaStatusRendimentoProcResponse;
import br.com.json.bean.transacional.PagamentoConsultaStatusRendimentoResponse;
import br.com.json.bean.transacional.PagamentoConsultaStatusRendimentoResponseV2;
import br.com.json.bean.transacional.PagamentoRendimentoResponse;
import br.com.sinergico.celcoin.CelcoinService;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.enums.SituacaoBoletoRendimentoEnum;
import br.com.sinergico.enums.StatusPagamentoRendimentoEnum;
import br.com.sinergico.enums.StatusPagamentoTedRendimentoEnum;
import br.com.sinergico.enums.StatusTransacao;
import br.com.sinergico.enums.TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum;
import br.com.sinergico.enums.TipoBoletoEnum;
import br.com.sinergico.enums.TipoOperacaoEnum;
import br.com.sinergico.facade.gatewaypagto.GatewayPagamentoExternoFacade;
import br.com.sinergico.repository.gatewaypagto.ConvenioRepository;
import br.com.sinergico.repository.gatewaypagto.ConvenioTipoSegmentoRepository;
import br.com.sinergico.repository.gatewaypagto.DDDOperadoraRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloTransacaoRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.repository.gatewaypagto.LogRecargaTransacaoRepository;
import br.com.sinergico.repository.gatewaypagto.OperadoraValorRepository;
import br.com.sinergico.repository.loyalty.ResgatePagamentoContaRepository;
import br.com.sinergico.repository.loyalty.ResgatesExportacao;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.LimitesContaService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.gatewaypagto.GatewayPagtoExternoService;
import br.com.sinergico.service.jcard.LogTransacoesService;
import br.com.sinergico.service.jcard.TransactionService;
import br.com.sinergico.service.loyalty.ResgatePagamentoContaService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.service.suporte.AplicativoMensagemService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.suporte.cashbacklog.CashbackLogService;
import br.com.sinergico.service.transacional.CodigoTransacaoService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.util.UtilManejoPontos;
import br.com.sinergico.vo.Bank;
import br.com.sinergico.vo.BillDataDto;
import br.com.sinergico.vo.CapturaTransacaoCelcoinDTO;
import br.com.sinergico.vo.ConsultDto;
import br.com.sinergico.vo.InfoBearerDto;
import br.com.sinergico.vo.NotificationsRendimentoVO;
import br.com.sinergico.vo.PagamentoCelcoinDTO;
import br.com.sinergico.vo.PagamentoVO;
import br.com.sinergico.vo.RecargaDataDto;
import br.com.sinergico.vo.RecargaPhoneDto;
import br.com.sinergico.vo.RecargaResponse;
import br.com.sinergico.vo.RequestRecargaDto;
import br.com.sinergico.vo.ShowProvidersDto;
import br.com.sinergico.vo.StatusConsultDto;
import br.com.sinergico.vo.StatusProviderValuesDto;
import br.com.sinergico.vo.StatusProvidersDto;
import br.com.sinergico.vo.TransacaoBoletoResponseVO;
import br.com.sinergico.vo.TransacaoDTO;
import br.com.sinergico.vo.ValueDto;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.NoResultException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @since 11/02/2019
 */
@Service
public class GatewayPagtoCelcoinService extends GenericService<ResgatePagamentoConta, Long>
    implements ResgatesExportacao {

  private static final String STATUS_SUCESSO = "SUCESSO";
  private static final String STATUS_ERRO = "ERRO_DESCONHECIDO";
  private static final String TIPO_TRAN_CONSULTA_STATUS = "CONSULTASTATUS";
  private static final double MIN_VALOR = 0.0;
  private static final int COD_BRASIL = 55;
  private static final String TIPO_TRAN_RECARGA = "RECARGA";
  private static final String STRING_VAZIA = "";
  private static final String VERSAO_CELCOIN = "3.0";
  private static final Integer COD_SUCESSO = 0;
  private static final String TIPO_TRAN_CONSULTA_DADOS_CONTA = "CONSULTADADOSCONTA";
  private static final String TIPO_TRAN_CONSULTA_PENDENCIA = "CONSULTAPENDENCIA";
  private static final String TIPO_TRAN_RECEBER_CONTA = "RECEBERCONTA";
  private static final String ERRO_SALDO_INSUFICIENTE = "not.sufficient.funds";
  private static final String ERRO_FUNCTION_CODE = "invalid.functioncode";
  private static final String TIPO_TRAN_CONFIRM = "CONF";
  private static final int POSICAO_ZERO = 0;

  private static final String PARAM_CELC_URL_EFET_RECARGA = "celcoin.urlrecarga";
  private static final String PARAM_CELC_URL_EFET_PAGTO = "celcoin.urlpagto";
  private static final String PARAM_CELC_URL_CONSULT_TIT = "celcoin.urlcons.tit";
  private static final Double MAX_VALOR = 9999999.99;
  public static final int PAGAMENTO_SUCESSO_RENDIMENTO = 0;
  public static final int PAGAMENTO_FALHA_RENDIMENTO = 99;
  public static final int TIPO_PESSOA_JURIDICA = 2;
  private static final String PREVIOUSLY_VOIDED = "previously.voided";
  public static final String FGTS = "FGTS";
  public static final String DARF = "DARF";
  private static final int ERRO_PREVISTO = -1;

  private static final Logger logger = LoggerFactory.getLogger(GatewayPagtoCelcoinService.class);
  private final int VALOR_PARA_CODIGO_SUCESSO_RETORNADO_API_RECARGA = 200;

  @Autowired private LogPagtoTituloValidacaoRepository logPagtoValidacaoRepository;

  @Autowired private LogPagtoTituloTransacaoRepository logPagtoTransacaoRepository;

  @Autowired private LogRecargaTransacaoRepository logRecargaTransacaoRepository;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private TransactionService transactionService;

  @Autowired private LogTransacoesService logTransacoesService;

  @Autowired private CodigoTransacaoService codigoTransacaoService;

  @Autowired private DDDOperadoraRepository dddOperadoraRepository;

  @Autowired private OperadoraValorRepository operadoraValorRepository;

  @Autowired private ParametroValorService paramValorService;

  @Autowired private CashbackLogService cashbackLogService;

  @Autowired private ResgatePagamentoContaRepository repository;

  @Autowired private ResgatePagamentoContaService resgatePagamentoContaService;

  @Autowired private UtilManejoPontos utilManejoPontos;

  @Autowired private PessoaService pessoaService;

  @Autowired private GatewayPagtoExternoService gatewayPagtoExternoService;

  @Autowired private LimitesContaService limitesContaService;

  @Autowired private RestTemplate restTemplate;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private CelcoinService celcoinService;

  @Value("${service.banco-rendimento.url}")
  private String urlBancoRendimento;

  private RendimentoProperties propriedadesRendimento;

  @Autowired
  public GatewayPagtoCelcoinService(ResgatePagamentoContaRepository repository) {
    super(repository);
    this.repository = repository;
  }

  @Autowired private ConvenioRepository convenioRepository;

  @Autowired private ConvenioTipoSegmentoRepository convenioTipoSegmentoRepository;

  @Autowired private GatewayPagamentoExternoFacade gatewayPagtoFacade;

  @Autowired protected ContaPagamentoService contaPagamentoService;

  @Autowired private AplicativoMensagemService aplicativoMensagemService;

  private ConsultarLinhaDigitavelTituloResponse fromLogPagtoValidacaoToResponse(
      LogPagtoTituloValidacao logPagtoVal) {
    ConsultarLinhaDigitavelTituloResponse resp = new ConsultarLinhaDigitavelTituloResponse();
    BeanUtils.copyProperties(logPagtoVal, resp, Util.getNullPropertyNames(logPagtoVal));
    resp.setCodigoRetorno(COD_SUCESSO);
    resp.setProtocoloExterno(
        logPagtoVal.getProtocoloIdConsulta() != null ? logPagtoVal.getProtocoloIdConsulta() : null);
    resp.setProtocoloInterno(logPagtoVal.getIdLogPagtoTitulo());
    return resp;
  }

  public ConsultarLinhaDigitavelTituloResponse consultarLinhaDigitavelCelcoinV5(
      ConsultarLinhaDigitavelTitulo req, ContaPagamento conta, Long idUsuario)
      throws IOException, JsonProcessingException {

    ConsultarLinhaDigitavelTituloResponse consultaTitulo = null;
    LogPagtoTituloValidacao logPagtoVal = new LogPagtoTituloValidacao();
    logPagtoVal.setIdConta(conta.getIdConta());
    logPagtoVal.setLinhaDigitavel(req.getLinhaDigitavel());
    logPagtoVal.setDataInicio(new Date());
    logPagtoVal.setCodigoBarras(req.getCodigoBarras());

    // salva pra ter dados antes de enviar pra celcoin
    logPagtoVal = logPagtoValidacaoRepository.save(logPagtoVal);

    ConsultarPagamentoLinhaDigitavelResponse res = consultarLinhaDigitalCelcoinV5(req, logPagtoVal);
    Date dtHrfim = new Date();

    TipoBoletoEnum tipoBoleto = validaTipoBoleto(req.getLinhaDigitavel());

    // coverte o retorno da celcoin em o retorno ja existente para não ter necessidade de
    // mudar em todos os fronts
    if (res != null && res.getErrorCode().equals("000")) {
      consultaTitulo =
          coverteResponseCelcoinInConsultarLinhaDigitavelTituloResponse(res, tipoBoleto);
      logPagtoVal = fromCelcoinToLogPagtoValidacaoCelcoin(consultaTitulo, logPagtoVal);
      logPagtoVal.setHoraRecebimentoInicio(res.getIniteHour() != null ? res.getIniteHour() : null);
      logPagtoVal.setHoraRecebimentoFim(res.getEndHour() != null ? res.getEndHour() : null);
      logPagtoVal.setProtocoloIdConsulta(res.getTransactionId());
      logPagtoVal.setStatusTransacao(res.getStatus().equals("0") ? STATUS_SUCESSO : STRING_VAZIA);
      logPagtoVal.setDataFim(dtHrfim);
      logPagtoVal.setIdUsuarioInclusao(idUsuario);
      consultaTitulo = fromLogPagtoValidacaoToResponse(logPagtoVal);
    }

    if (res != null
        && res.getErrorCode() != null
        && !Integer.valueOf(res.getErrorCode()).equals(Integer.valueOf(0))) {
      logPagtoVal.setDataFim(dtHrfim);
      logPagtoVal.setStatusTransacao(STATUS_ERRO);
      logPagtoVal.setMensagemErro(res.getMessage());
      logPagtoVal.setIdUsuarioInclusao(idUsuario);
      logPagtoVal.setProtocoloIdConsulta(
          res.getTransactionId() != null ? res.getTransactionId() : null);
      logPagtoValidacaoRepository.save(logPagtoVal);
      throw new GenericServiceException(res.getMessage());
    }
    logPagtoValidacaoRepository.save(logPagtoVal);
    return consultaTitulo;
  }

  public ConsultarLinhaDigitavelTituloResponse consultarLinhaDigitavelRendimento(
      ConsultarLinhaDigitavelTitulo req, ContaPagamento conta, Long idUsuario) throws IOException {

    ConvenioTipoSegmento digitoSegmento =
        convenioTipoSegmentoRepository
            .findById(Integer.parseInt(req.getLinhaDigitavel().substring(1, 2)))
            .orElse(null);
    String convenio = "";

    if (req.getLinhaDigitavel().length() <= 44) {
      convenio = req.getLinhaDigitavel().substring(15, 19);
    } else {
      convenio = req.getLinhaDigitavel().substring(16, 20);
    }

    boolean isFgts = false;
    boolean isDarf = false;

    Convenio convenioAtivo =
        convenioRepository.findByAtivoAndCodigoConvenioAndConvenioTipoSegmento(
            true, convenio, digitoSegmento);
    int primeiroDigito = Integer.parseInt(req.getLinhaDigitavel().substring(0, 1));

    ConsultarLinhaDigitavelTituloResponse consultaTitulo = null;
    LogPagtoTituloValidacao logPagtoVal = new LogPagtoTituloValidacao();
    ConsultaBoletoResponse res = new ConsultaBoletoResponse();
    TipoBoletoEnum tipoBoleto =
        validaTipoBoleto(
            req.getLinhaDigitavel() != null ? req.getLinhaDigitavel() : req.getCodigoBarras());
    ;

    logPagtoVal.setIdConta(conta.getIdConta());
    logPagtoVal.setLinhaDigitavel(req.getLinhaDigitavel());
    logPagtoVal.setDataInicio(new Date());
    logPagtoVal.setCodigoBarras(req.getCodigoBarras());

    // salva pra ter dados antes de enviar pra celcoin
    logPagtoVal = logPagtoValidacaoRepository.save(logPagtoVal);

    if (primeiroDigito == 8) {
      if (convenioAtivo != null) {
        if (convenioAtivo.getCodigoConvenio().equals(convenio)) {
          if (convenioAtivo.getSiglaConvenio().equals("FGTS")) {
            isFgts = true;
          } else if (convenioAtivo.getSiglaConvenio().equals("DARF")) {
            isDarf = true;
          }
        }
      } else {
        throw new GenericServiceException(
            ConstantesErro.CONV_NAO_EXISTE_CONVENIO_PARA_INSTITUICAO.getMensagem());
      }
    }

    res = consultarLinhaDigitalRendimento(req, conta, isFgts, isDarf, tipoBoleto);

    Date dtHrfim = new Date();

    if (res != null && res.getIsSuccess()) {

      if (res.getValue() == null && res.getData() == null) {
        throw new NullPointerException(ConstantesErro.REND_RETORNO_NULO_VAZIO.getMensagem());
      }

      //			Passando apenas o valor de Darf por ser o unico retorno diferente dos outros boletos.
      consultaTitulo =
          coverteResponseRendimentoInConsultarLinhaDigitavelTituloResponse(res, isDarf, tipoBoleto);
      logPagtoVal = fromCelcoinToLogPagtoValidacaoCelcoin(consultaTitulo, logPagtoVal);
      logPagtoVal.setHoraRecebimentoInicio(
          res.getValue() != null
              ? res.getValue().getDataHoraConsultaBoleto() != null
                  ? res.getValue().getDataHoraConsultaBoleto().substring(11, 16)
                  : null
              : res.getData() != null
                  ? res.getData().getDataHoraConsultaBoleto() != null
                      ? res.getData().getDataHoraConsultaBoleto().substring(11, 16)
                      : null
                  : null);

      logPagtoVal.setHoraRecebimentoFim(
          res.getValue() != null
              ? res.getValue().getDataLimitePagamento() != null
                  ? res.getValue().getDataLimitePagamento().substring(11, 16)
                  : null
              : res.getData() != null
                  ? res.getData().getDataLimitePagamento() != null
                      ? res.getData().getDataLimitePagamento().substring(11, 16)
                      : null
                  : null);

      logPagtoVal.setStatusTransacao(res.getIsSuccess() ? STATUS_SUCESSO : STRING_VAZIA);

      if (TipoBoletoEnum.TITULO.equals(tipoBoleto)) {
        logPagtoVal.setCodigoEspecieTitulo(res.getData().getCodigoEspecieTitulo());
        if (!SituacaoBoletoRendimentoEnum.getCodigosPermitidos()
            .contains(res.getData().getCodigoSituacao())) {

          String mensagemErro =
              res.getData().getSituacao() != null
                  ? res.getData().getSituacao()
                  : res.getData().getMotivo();

          logPagtoVal.setCodigoSituacao(res.getData().getCodigoSituacao());
          logPagtoVal.setSituacao(
              res.getData().getSituacao() != null
                  ? res.getData().getSituacao()
                  : res.getData().getMotivo());
          logPagtoVal.setDataFim(dtHrfim);
          logPagtoVal.setIdUsuarioInclusao(idUsuario);
          consultaTitulo.setCodigoRetorno(ERRO_PREVISTO);
          consultaTitulo.setMensagemErro(mensagemErro);
          logger.info(ConstantesErro.REND_BOLETO_EM_SITUACAO_INAPTA.format(mensagemErro));
          logPagtoValidacaoRepository.save(logPagtoVal);
          throw new GenericServiceException(
              logPagtoVal.getSituacao(), HttpStatus.INTERNAL_SERVER_ERROR);
        }
      }

      logPagtoVal.setCodigoBarras(
          formatLinhaDigitavel(
              res.getData() != null
                  ? res.getData().getCodigoDeBarras()
                  : res.getValue().getCodigoDeBarras()));

      consultaTitulo = fromLogPagtoValidacaoToResponse(logPagtoVal);
    }

    logPagtoVal.setDataFim(dtHrfim);
    logPagtoVal.setIdUsuarioInclusao(idUsuario);

    logPagtoVal.setLinhaDigitavel(formatLinhaDigitavel(logPagtoVal.getLinhaDigitavel()));

    if (res != null && res.getIsFailure()) {
      String mensagemErro =
          res.getValue() != null
              ? res.getErroMessage().getMessage()
              : res.getData() != null
                  ? res.getNotifications().get(0).getMessage()
                  : ConstantesErro.REND_ERRO_GERAL_CONSULTA.getMensagem();
      logPagtoVal.setStatusTransacao(STATUS_ERRO);
      logPagtoVal.setMensagemErro(mensagemErro);
      logger.error(mensagemErro);
      logPagtoValidacaoRepository.save(logPagtoVal);
      throw new GenericServiceException(mensagemErro);
    }

    logPagtoValidacaoRepository.save(logPagtoVal);

    return consultaTitulo;
  }

  private ConsultarLinhaDigitavelTituloResponse
      coverteResponseRendimentoInConsultarLinhaDigitavelTituloResponse(
          ConsultaBoletoResponse respostaRendimento, Boolean isDarf, TipoBoletoEnum tipoBoleto) {
    ConsultarLinhaDigitavelTituloResponse consultarLinhaDigitavelTituloResponse =
        new ConsultarLinhaDigitavelTituloResponse();

    String valorPrincipalString = null;
    String valorPrincipalStringFormatado = null;
    Double valuePrincipalDouble = null;

    if (TipoBoletoEnum.TITULO.getId().equals(tipoBoleto.getId())) {
      consultarLinhaDigitavelTituloResponse.setCedente(
          respostaRendimento.getData().getNomeBeneficiario());
      if (respostaRendimento.getData().getDataVencimento() != null) {
        consultarLinhaDigitavelTituloResponse.setDataVencimento(
            DateUtil.convertStringToData(respostaRendimento.getData().getDataVencimento()));
      } else {
        consultarLinhaDigitavelTituloResponse.setDataVencimento(null);
      }

      consultarLinhaDigitavelTituloResponse.setLinhaDigitavel(
          respostaRendimento.getData().getLinhaDigitavel());
      consultarLinhaDigitavelTituloResponse.setValor(
          respostaRendimento.getData().getValorNominal().doubleValue());
      consultarLinhaDigitavelTituloResponse.setDocumentoBeneficiario(
          respostaRendimento.getData().getCnpjCpfBeneficiario());
      consultarLinhaDigitavelTituloResponse.setDocumentoSacado(
          respostaRendimento.getData().getCnpjCpfPagador());
      if (respostaRendimento.getData().getDataLimitePagamento() != null) {
        consultarLinhaDigitavelTituloResponse.setDataLimitePagamento(
            DateUtil.parseDate(
                "yyyy-MM-dd", respostaRendimento.getData().getDataLimitePagamento()));
      } else {
        consultarLinhaDigitavelTituloResponse.setDataLimitePagamento(null);
      }
      consultarLinhaDigitavelTituloResponse.setPermiteAlterarValor(
          respostaRendimento.getData().getPermitePagamentoParcial().equals("S"));
      consultarLinhaDigitavelTituloResponse.setNomeBeneficiario(
          respostaRendimento.getData().getNomeBeneficiario());
      consultarLinhaDigitavelTituloResponse.setNomeSacado(
          respostaRendimento.getData().getNomePagador());
      consultarLinhaDigitavelTituloResponse.setValorDescontoCalculado(
          respostaRendimento.getData().getDesconto() != null
              ? respostaRendimento.getData().getDesconto().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorJurosCalculado(
          respostaRendimento.getData().getJuros() != null
              ? respostaRendimento.getData().getJuros().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorMaximoPagamento(
          respostaRendimento.getData().getValorMaximo() != null
              ? respostaRendimento.getData().getValorMaximo().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorMinimoPagamento(
          respostaRendimento.getData().getValorMinimo() != null
              ? respostaRendimento.getData().getValorMinimo().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorMultaCalculado(
          respostaRendimento.getData().getMulta() != null
              ? respostaRendimento.getData().getMulta().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorNominal(
          respostaRendimento.getData().getValorNominal() != null
              ? respostaRendimento.getData().getValorNominal().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorPagamentoAtualizado(
          respostaRendimento.getData().getValorTotal() != null
              ? respostaRendimento.getData().getValorTotal().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorTotalAbatimento(
          respostaRendimento.getData().getAbatimento() != null
              ? respostaRendimento.getData().getAbatimento().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setTipoServico(
          respostaRendimento.getData().getTipoPagamentoDiverso());
      consultarLinhaDigitavelTituloResponse.setTipoAutorizacaoRecebimentoValorDivergente(
          respostaRendimento.getData().getTipoAutorizacaoRecebimentoValorDivergente() != null
              ? Integer.valueOf(
                  respostaRendimento.getData().getTipoAutorizacaoRecebimentoValorDivergente())
              : TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.NAO_ACEITAR_DIVERGENTE.getId());
      consultarLinhaDigitavelTituloResponse.setValorTotal(
          respostaRendimento.getData().getValorTotal() != null
              ? respostaRendimento.getData().getValorTotal().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setCodigoEspecieTitulo(
          respostaRendimento.getData().getCodigoEspecieTitulo());
      consultarLinhaDigitavelTituloResponse.setSituacao(respostaRendimento.getData().getSituacao());
    } else {
      if (isDarf) {
        valorPrincipalString = respostaRendimento.getValue().getValorPrincipal();
        valorPrincipalStringFormatado = valorPrincipalString.replace(",", ".");
        valuePrincipalDouble = Double.valueOf(valorPrincipalStringFormatado);
      }

      consultarLinhaDigitavelTituloResponse.setCedente(
          respostaRendimento.getValue().getNomeBeneficiario());
      if (respostaRendimento.getValue().getDataVencimento() != null) {
        consultarLinhaDigitavelTituloResponse.setDataVencimento(
            DateUtil.convertStringToData(respostaRendimento.getValue().getDataVencimento()));
      } else {
        consultarLinhaDigitavelTituloResponse.setDataVencimento(null);
      }
      consultarLinhaDigitavelTituloResponse.setLinhaDigitavel(
          respostaRendimento.getValue().getLinhaDigitavel());
      consultarLinhaDigitavelTituloResponse.setValor(
          isDarf
              ? valuePrincipalDouble
              : respostaRendimento.getValue().getValorTotal().doubleValue());
      consultarLinhaDigitavelTituloResponse.setDocumentoBeneficiario(
          respostaRendimento.getValue().getCnpjcpfBeneficiario());
      consultarLinhaDigitavelTituloResponse.setDocumentoSacado(
          respostaRendimento.getValue().getCnpjcpfPagador());
      if (respostaRendimento.getValue().getDataLimitePagamento() != null) {
        consultarLinhaDigitavelTituloResponse.setDataLimitePagamento(
            DateUtil.parseDate(
                "yyyy-MM-dd", respostaRendimento.getValue().getDataLimitePagamento()));
      } else {
        consultarLinhaDigitavelTituloResponse.setDataLimitePagamento(null);
      }
      consultarLinhaDigitavelTituloResponse.setPermiteAlterarValor(
          respostaRendimento.getValue().getPermiteAlterarValorTotal());
      consultarLinhaDigitavelTituloResponse.setNomeBeneficiario(
          respostaRendimento.getValue().getNomeBeneficiario());
      consultarLinhaDigitavelTituloResponse.setNomeSacado(
          respostaRendimento.getValue().getNomePagador());
      consultarLinhaDigitavelTituloResponse.setValorDescontoCalculado(
          respostaRendimento.getValue().getValorDesconto() != null
              ? respostaRendimento.getValue().getValorDesconto().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorJurosCalculado(
          respostaRendimento.getValue().getJuros() != null
              ? respostaRendimento.getValue().getJuros().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorMaximoPagamento(
          isDarf
              ? valuePrincipalDouble
              : respostaRendimento.getValue().getValorTotal().doubleValue());
      consultarLinhaDigitavelTituloResponse.setValorMinimoPagamento(
          isDarf
              ? valuePrincipalDouble
              : respostaRendimento.getValue().getValorTotal().doubleValue());
      consultarLinhaDigitavelTituloResponse.setValorMultaCalculado(
          respostaRendimento.getValue().getMulta() != null
              ? respostaRendimento.getValue().getMulta().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorNominal(
          isDarf
              ? valuePrincipalDouble
              : respostaRendimento.getValue().getValorTotal().doubleValue());
      consultarLinhaDigitavelTituloResponse.setValorPagamentoAtualizado(
          respostaRendimento.getValue().getValorTotal() != null
              ? respostaRendimento.getValue().getValorTotal().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setValorTotalAbatimento(
          respostaRendimento.getValue().getValorDesconto() != null
              ? respostaRendimento.getValue().getValorDesconto().doubleValue()
              : 0.0);
      consultarLinhaDigitavelTituloResponse.setTipoServico(
          respostaRendimento.getValue().getTipoPagamentoDiverso());
    }

    return consultarLinhaDigitavelTituloResponse;
  }

  private ConsultaBoletoResponse consultarLinhaDigitalRendimento(
      ConsultarLinhaDigitavelTitulo req,
      ContaPagamento conta,
      Boolean isFgts,
      Boolean isDarf,
      TipoBoletoEnum tipoBoleto)
      throws IOException {
    ConsultaBoletoResponse res = null;

    try {

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      this.propriedadesRendimento = new RendimentoProperties();

      endPoint.setUrl(Constantes.URL_RENDIMENTO_V7_PAGAMENTOS);
      endPoint.setUri(Constantes.URI_RENDIMENTO_CONSULTA_TITULO);
      this.propriedadesRendimento.setConsultaLinhaDigitavel(endPoint);

      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(conta.getIdInstituicao());
      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, this.propriedadesRendimento.getAutenticacao());
      headers.set("Content-Type", "application/json;charset=UTF-8");

      String url =
          this.propriedadesRendimento.getUrl()
              + this.propriedadesRendimento.getConsultaLinhaDigitavel().getUrl()
              + "/"
              + chaveInstituicao.getAgencia()
              + "/"
              + chaveInstituicao.getConta()
              + this.propriedadesRendimento.getConsultaLinhaDigitavel().getUri();

      ConsultaDadosBoletoRequest request = new ConsultaDadosBoletoRequest();

      if (TipoBoletoEnum.TITULO.getId().equals(tipoBoleto.getId())) {
        request.setCodigoDeBarrasOuLinhaDigitavel(req.getLinhaDigitavel());

        url =
            this.propriedadesRendimento.getUrl()
                + Constantes.URL_RENDIMENTO_EVOTITULOS
                + "/"
                + chaveInstituicao.getAgencia()
                + "/"
                + chaveInstituicao.getConta()
                + Constantes.URL_RENDIMENTO_CONSULTA_TITULO;

        HttpEntity<ConsultaDadosBoletoRequest> entity =
            new HttpEntity<ConsultaDadosBoletoRequest>(request, headers);
        ResponseEntity<ConsultaBoletoResponse> responseEntity =
            restTemplate.postForEntity(url, entity, ConsultaBoletoResponse.class);

        res =
            responseEntity.getBody() != null
                ? responseEntity.getBody()
                : tratativaResponseErroRendimento();

        res.setIsSuccess(
            res.getIsSuccess() == null
                ? res.getSuccess() != null ? res.getSuccess() : false
                : res.getIsSuccess());
        res.setIsFailure(
            res.getIsFailure() == null
                ? res.getSuccess() == null || !res.getSuccess()
                : res.getIsFailure());

      } else {
        url =
            this.propriedadesRendimento.getUrl()
                + Constantes.URL_RENDIMENTO_V7_PAGAMENTOS
                + "/"
                + chaveInstituicao.getAgencia()
                + "/"
                + chaveInstituicao.getConta()
                + Constantes.URI_RENDIMENTO_CONSULTA_TITULO;
        if (isDarf) {
          ConsultaDadosBoletoDARFRequest darfRequest = new ConsultaDadosBoletoDARFRequest();

          darfRequest.setCpfcnpj(req.getTributoDARF().getCpfcnpj());

          //				Se for código de barras (44) ou linha digitavel (47/48)
          if (req.getLinhaDigitavel().length() <= 44) {
            darfRequest.setCodigoBarras(req.getLinhaDigitavel());
            darfRequest.setLinhaDigitavel("");
          } else {
            darfRequest.setCodigoBarras("");
            darfRequest.setLinhaDigitavel(req.getLinhaDigitavel());
          }

          darfRequest.setTipo("DARFCodigoBarras");
          darfRequest.setDataVencimento(req.getTributoDARF().getDataVencimento());

          HttpEntity<ConsultaDadosBoletoDARFRequest> entity =
              new HttpEntity<ConsultaDadosBoletoDARFRequest>(darfRequest, headers);
          res = restTemplate.postForEntity(url, entity, ConsultaBoletoResponse.class).getBody();
        } else if (isFgts) {
          ConsultaDadosBoletoFGTSRequest fgtsRequest = new ConsultaDadosBoletoFGTSRequest();
          FgtsConsultaRendimentoRequest fgtsConsultaRendimentoRequest =
              new FgtsConsultaRendimentoRequest();

          fgtsConsultaRendimentoRequest.setTipoIdentificacaoContribuinte("");
          fgtsConsultaRendimentoRequest.setInscricaoTipo("");
          fgtsConsultaRendimentoRequest.setLacre("");
          fgtsConsultaRendimentoRequest.setLacredigito("");
          fgtsConsultaRendimentoRequest.setIdentificador("");

          //				Se for código de barras (44) ou linha digitavel (47/48)
          if (req.getLinhaDigitavel().length() <= 44) {
            fgtsRequest.setCodigoBarras(req.getLinhaDigitavel());
            fgtsRequest.setLinhaDigitavel("");
          } else {
            fgtsRequest.setCodigoBarras("");
            fgtsRequest.setLinhaDigitavel(req.getLinhaDigitavel());
          }

          fgtsRequest.setFgts(fgtsConsultaRendimentoRequest);

          HttpEntity<ConsultaDadosBoletoFGTSRequest> entity =
              new HttpEntity<ConsultaDadosBoletoFGTSRequest>(fgtsRequest, headers);
          res = restTemplate.postForEntity(url, entity, ConsultaBoletoResponse.class).getBody();
        } else {
          request.setLinhaDigitavel(
              req.getLinhaDigitavel() != null ? req.getLinhaDigitavel() : req.getCodigoBarras());

          HttpEntity<ConsultaDadosBoletoRequest> entity =
              new HttpEntity<ConsultaDadosBoletoRequest>(request, headers);
          res = restTemplate.postForEntity(url, entity, ConsultaBoletoResponse.class).getBody();
        }
      }

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.error("ERRO RENDIMENTO: " + e.getResponseBodyAsString());
      e.printStackTrace();
      ObjectMapper objectMapper = new ObjectMapper();
      JsonNode jsonNode = objectMapper.readTree(e.getResponseBodyAsString());
      if (jsonNode.isObject()) {
        return objectMapper.readValue(e.getResponseBodyAsString(), ConsultaBoletoResponse.class);
      } else {
        return tratativaResponseErroRendimento();
      }
    }
    return res;
  }

  public ConsultaBoletoResponse tratativaResponseErroRendimento() {

    ArrayList<NotificationsRendimentoVO> notificationsRendimentoVOArrayList = new ArrayList<>();
    NotificationsRendimentoVO notificationsRendimentoVO =
        new NotificationsRendimentoVO(
            "Conexão", "-99", ConstantesErro.REND_FALHA_CONEXAO_RENDIMENTO.getMensagem());

    notificationsRendimentoVOArrayList.add(notificationsRendimentoVO);

    ConsultaBoletoResponse consultaBoletoResponse =
        new ConsultaBoletoResponse(
            null, null, false, true, false, null, notificationsRendimentoVOArrayList);

    return consultaBoletoResponse;
  }

  public TipoBoletoEnum validaTipoBoleto(String codigoDeBarrasOuLinhaDigitavel) {
    Integer digitoSegmento = null;
    Integer primeiroDigito = null;
    digitoSegmento = Integer.parseInt(codigoDeBarrasOuLinhaDigitavel.substring(1, 2));
    primeiroDigito = Integer.parseInt(codigoDeBarrasOuLinhaDigitavel.substring(0, 1));

    if (primeiroDigito == 8) {
      switch (Objects.requireNonNull(findTypeById(digitoSegmento))) {
        case ORGAOS_GOVERNAMENTAIS:
        case PREFEITURA:
        case CARNES_OUTROS_ORGAOS:
        case MULTA_DE_TRANSITO:
        case USO_EXCLUSIVO_DO_BANCO:
          // Chamar API TRIBUTOS
          return TipoBoletoEnum.TRIBUTO;
        case SANEAMENTO:
        case ENERGIA_ELETRICA_E_GAS:
        case TELECOMUNICACOES:
          // PAGAMENTO CONSUMO
          return TipoBoletoEnum.CONSUMO;
        default:
          throw new GenericServiceException(
              ConstantesErro.SEG_SEGMENTO_INVALIDO.getMensagem(), HttpStatus.UNPROCESSABLE_ENTITY);
      }
    } else {
      return TipoBoletoEnum.TITULO;
    }
  }

  private String formatLinhaDigitavel(String text) {
    return text.replaceAll("[^0-9]+", "");
  }

  private ConsultarPagamentoLinhaDigitavelResponse consultarLinhaDigitalCelcoinV5(
      ConsultarLinhaDigitavelTitulo req, LogPagtoTituloValidacao logPagtoVal)
      throws IOException, GenericServiceException {
    ConsultarPagamentoLinhaDigitavelResponse res;
    try {
      ConsultaPagamentoDTO request = preencherRequestConsultaLinhaDigitavel(req, logPagtoVal);
      logger.info("Iniciando chamada de consulta pagamento para a API da celcoin" + request);

      res = celcoinService.authorizeBillPayments(request);

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.info(e.getResponseBodyAsString());
      e.printStackTrace();
      ObjectMapper objectMapper = new ObjectMapper();
      ConsultarPagamentoLinhaDigitavelResponse consultarPagamentoLinhaDigitavelResponse =
          objectMapper.readValue(
              e.getResponseBodyAsString(), ConsultarPagamentoLinhaDigitavelResponse.class);
      return consultarPagamentoLinhaDigitavelResponse;
    }
    return res;
  }

  private ConsultaPagamentoDTO preencherRequestConsultaLinhaDigitavel(
      ConsultarLinhaDigitavelTitulo req, LogPagtoTituloValidacao logPagtoVal) {

    ConsultaPagamentoDTO request = new ConsultaPagamentoDTO();
    request.setExternalTerminal(req.getIp());
    request.setExternalNSU(Integer.parseInt(logPagtoVal.getIdLogPagtoTitulo().toString()));

    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    barCode.setType(0);
    barCode.setDigitable(req.getLinhaDigitavel());
    barCode.setBarCode(req.getCodigoBarras());
    request.setBarcode(barCode);

    return request;
  }

  public EstornoTituloRendimentoResponse efetuarEstornoTitulo(
      String rrn, EstornoTituloRendimentoResponse response)
      throws JsonParseException, JsonMappingException, IOException {

    EstornoTituloRendimentoRequest request = new EstornoTituloRendimentoRequest();

    try {

      this.propriedadesRendimento = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_V1_ESTORNO);
      endPoint.setUri(Constantes.URI_RENDIMENTO_ESTORNO_TITULO);
      this.propriedadesRendimento.setEstornoTitulo(endPoint);

      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      LogPagtoTituloTransacao logPagtoTituloTransacao =
          logPagtoTransacaoRepository.findOneByRrn(rrn);

      DadosConta conta =
          getContaPagamentoFacade().getDadosConta(logPagtoTituloTransacao.getIdConta());
      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(conta.getIdInstituicao());

      if (logPagtoTituloTransacao != null
          && logPagtoTituloTransacao.getProtocoloIdRendimento() != null) {

        if (logPagtoTituloTransacao.getIdTransacaoDesfPagto() != null
            || logPagtoTituloTransacao.getIdTransacaoDesfPagtoInst() != null) {

          response.setFailure(true);
          response.setSuccess(false);
          response.setMessage("Este título já foi estornado antes.");

          return response;
        }

        request.setTransactionId(logPagtoTituloTransacao.getProtocoloIdRendimento());
        request.setValorTotalPagamento(BigDecimal.valueOf(logPagtoTituloTransacao.getValor()));

      } else {

        response.setFailure(true);
        response.setSuccess(false);
        response.setMessage("Não foi possível localizar o pagamento deste título.");

        return response;
      }

      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, this.propriedadesRendimento.getAutenticacao());
      headers.set("Content-Type", "application/json;charset=UTF-8");

      HttpEntity<EstornoTituloRendimentoRequest> entity =
          new HttpEntity<EstornoTituloRendimentoRequest>(request, headers);

      response =
          restTemplate
              .postForEntity(
                  this.propriedadesRendimento.getUrlEstornoTitulo()
                      + chaveInstituicao.getAgencia()
                      + "/"
                      + chaveInstituicao.getConta()
                      + this.propriedadesRendimento.getEstornoTitulo().getUri(),
                  entity,
                  EstornoTituloRendimentoResponse.class)
              .getBody();

      if (response.isSuccess()) {
        JcardResponseRrn reversal = transactionService.estonarTransacao(rrn);
        logPagtoTituloTransacao.setIdTransacaoDesfPagto(reversal.getRrn());
        logPagtoTituloTransacao.setProtocoloIdEstornoRendimento(
            response.getValue().getTransacaoId());

        String rrnContaGarantia = logPagtoTituloTransacao.getIdTransacaoPagtoInst();

        if (rrnContaGarantia != null) {
          JcardResponseRrn reversalGarantia = transactionService.estonarTransacao(rrn);
          logPagtoTituloTransacao.setIdTransacaoDesfPagtoInst(reversalGarantia.getRrn());
        }
        logPagtoTransacaoRepository.save(logPagtoTituloTransacao);
      }

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.info(e.getResponseBodyAsString());
      ObjectMapper objectMapper = new ObjectMapper();
      EstornoTituloRendimentoResponse estornoTituloRendimentoResponse =
          objectMapper.readValue(
              e.getResponseBodyAsString(), EstornoTituloRendimentoResponse.class);
      estornoTituloRendimentoResponse
          .getErroMessage()
          .setMessage(
              estornoTituloRendimentoResponse.getErroMessage().getErrors().get(0).getMessage());
      estornoTituloRendimentoResponse.setMessage(
          estornoTituloRendimentoResponse.getErroMessage().getErrors().get(0).getMessage());
      return estornoTituloRendimentoResponse;
    }

    return response;
  }

  private ConsultarLinhaDigitavelTituloResponse
      coverteResponseCelcoinInConsultarLinhaDigitavelTituloResponse(
          ConsultarPagamentoLinhaDigitavelResponse respostaCelcoin, TipoBoletoEnum tipoBoleto) {
    ConsultarLinhaDigitavelTituloResponse consultarLinhaDigitavelTituloResponse =
        new ConsultarLinhaDigitavelTituloResponse();

    consultarLinhaDigitavelTituloResponse.setDadosRegistro(respostaCelcoin.getRegisterData());
    if (respostaCelcoin.getDueDate() != null) {
      consultarLinhaDigitavelTituloResponse.setDataVencimento(
          DateUtil.parseDate("yyyy-MM-dd", respostaCelcoin.getDueDate()));
    } else {
      consultarLinhaDigitavelTituloResponse.setDataVencimento(null);
    }

    consultarLinhaDigitavelTituloResponse.setCedente(respostaCelcoin.getAssignor());
    consultarLinhaDigitavelTituloResponse.setNomeBeneficiario(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getRecipient()
            : null);

    consultarLinhaDigitavelTituloResponse.setLinhaDigitavel(respostaCelcoin.getDigitable());
    consultarLinhaDigitavelTituloResponse.setValor(
        respostaCelcoin.getType().equals(1)
            ? respostaCelcoin.getValue().doubleValue()
            : respostaCelcoin.getRegisterData().getOriginalValue().doubleValue());
    consultarLinhaDigitavelTituloResponse.setDocumentoBeneficiario(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getDocumentRecipient()
            : null);
    consultarLinhaDigitavelTituloResponse.setDocumentoSacado(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getDocumentPayer()
            : null);
    if (respostaCelcoin.getDueDate() != null || respostaCelcoin.getRegisterData() != null) {
      consultarLinhaDigitavelTituloResponse.setDataLimitePagamento(
          DateUtil.parseDate(
              "yyyy-MM-dd",
              respostaCelcoin.getType().equals(1)
                  ? respostaCelcoin.getDueDate()
                  : respostaCelcoin.getRegisterData().getDueDateRegister().toString()));
    }
    consultarLinhaDigitavelTituloResponse.setPermiteAlterarValor(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getAllowChangevalue()
            : null);
    consultarLinhaDigitavelTituloResponse.setTipoAutorizacaoRecebimentoValorDivergente(
        (consultarLinhaDigitavelTituloResponse.getPermiteAlterarValor() == null
                || !consultarLinhaDigitavelTituloResponse.getPermiteAlterarValor())
            ? TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.NAO_ACEITAR_DIVERGENTE.getId()
            : TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.ENTRE_MINIMO_MAXIMO.getId());
    consultarLinhaDigitavelTituloResponse.setNomeSacado(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getPayer()
            : null);
    consultarLinhaDigitavelTituloResponse.setValorDescontoCalculado(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getDiscountValue().doubleValue()
            : null);
    consultarLinhaDigitavelTituloResponse.setValorJurosCalculado(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getInterestValueCalculated().doubleValue()
            : null);
    if (respostaCelcoin.getType().equals(1) && respostaCelcoin.getMaxValue() != null) {
      consultarLinhaDigitavelTituloResponse.setValorMaximoPagamento(
          respostaCelcoin.getMaxValue().doubleValue());
    } else if (respostaCelcoin.getRegisterData() != null) {
      consultarLinhaDigitavelTituloResponse.setValorMaximoPagamento(
          respostaCelcoin.getRegisterData().getMaxValue().doubleValue());
    }
    if (respostaCelcoin.getType().equals(1) && respostaCelcoin.getMinValue() != null) {
      consultarLinhaDigitavelTituloResponse.setValorMinimoPagamento(
          respostaCelcoin.getMinValue().doubleValue());
    } else if (respostaCelcoin.getRegisterData() != null) {
      consultarLinhaDigitavelTituloResponse.setValorMinimoPagamento(
          respostaCelcoin.getRegisterData().getMinValue().doubleValue());
    }
    consultarLinhaDigitavelTituloResponse.setValorMultaCalculado(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getFineValueCalculated().doubleValue()
            : null);
    consultarLinhaDigitavelTituloResponse.setValorNominal(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getOriginalValue().doubleValue()
            : null);
    consultarLinhaDigitavelTituloResponse.setValorPagamentoAtualizado(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getTotalUpdated().doubleValue()
            : null);
    consultarLinhaDigitavelTituloResponse.setValorTotalAbatimento(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getTotalWithDiscount().doubleValue()
            : null);
    consultarLinhaDigitavelTituloResponse.setValorTotalAcrescimo(
        respostaCelcoin.getRegisterData() != null
            ? respostaCelcoin.getRegisterData().getTotalWithAdditional().doubleValue()
            : null);
    consultarLinhaDigitavelTituloResponse.setDataLiquidacao(respostaCelcoin.getSettleDate());
    consultarLinhaDigitavelTituloResponse.setTipoServico(
        respostaCelcoin.getType() == null || respostaCelcoin.getType() == 0
            ? "Undefined"
            : respostaCelcoin.getType() == 1 ? "Concessionaria" : "FichaCompensacao");

    if (TipoBoletoEnum.TRIBUTO.getId().equals(tipoBoleto.getId())
        || TipoBoletoEnum.CONSUMO.getId().equals(tipoBoleto.getId())) {
      consultarLinhaDigitavelTituloResponse.setNomeBeneficiario(respostaCelcoin.getAssignor());
      consultarLinhaDigitavelTituloResponse.setValorPagamentoAtualizado(
          respostaCelcoin.getValue().doubleValue());
    }

    return consultarLinhaDigitavelTituloResponse;
  }

  private LogPagtoTituloValidacao fromCelcoinToLogPagtoValidacaoCelcoin(
      ConsultarLinhaDigitavelTituloResponse consultaTitulo, LogPagtoTituloValidacao logPagtoVal) {

    BeanUtils.copyProperties(
        consultaTitulo, logPagtoVal, Util.getNullPropertyNames(consultaTitulo));
    logPagtoVal.setTipoServico(
        consultaTitulo.getTipoServico() != null ? consultaTitulo.getTipoServico() : null);
    logPagtoVal.setStatusTransacao(STATUS_SUCESSO);

    if (consultaTitulo.getDataVencimento() != null) {
      logPagtoVal.setDataVencimento(consultaTitulo.getDataVencimento());
    }

    if (consultaTitulo.getDataLiquidacao() != null) {
      logPagtoVal.setDataLiquidacao(
          DateUtil.parseDate("dd/MM/yyyy", consultaTitulo.getDataLiquidacao()));
    }
    RegisterData dadosRegistro = consultaTitulo.getDadosRegistro();
    if (dadosRegistro != null) {
      logPagtoVal = preencherDadosRegistroCelcoin(logPagtoVal, dadosRegistro);
    } else {
      logPagtoVal = preencherDadosRegistroRendimento(logPagtoVal, consultaTitulo);
    }

    logPagtoVal.setPermiteAlterarValor(consultaTitulo.getPermiteAlterarValor());
    logPagtoVal.setStatusTransacao(
        consultaTitulo.getSucesso() != null ? consultaTitulo.getSucesso().toString() : null);
    logPagtoVal.setCodResultado(
        consultaTitulo.getCodigoRetorno() != null
            ? consultaTitulo.getCodigoRetorno().toString()
            : null);
    return logPagtoVal;
  }

  private LogPagtoTituloValidacao preencherDadosRegistroCelcoin(
      LogPagtoTituloValidacao logPagtoVal, RegisterData dadosRegistro) {

    logPagtoVal.setDocumentoBeneficiario(dadosRegistro.getDocumentRecipient());
    logPagtoVal.setDocumentoSacado(dadosRegistro.getDocumentPayer());
    if (dadosRegistro.getDueDateRegister() != null) {
      logPagtoVal.setDataLimitePagamento(
          DateUtil.parseDate("yyyy-MM-dd", dadosRegistro.getDueDateRegister()));
    } else {
      logPagtoVal.setDataLimitePagamento(null);
    }
    if (dadosRegistro.getNextBusinessDay() != null) {
      logPagtoVal.setDataProximoDiaUtil(
          DateUtil.parseDate("yyyy-MM-dd", dadosRegistro.getNextBusinessDay()));
    } else {
      logPagtoVal.setDataProximoDiaUtil(null);
    }
    logPagtoVal.setNomeBeneficiario(dadosRegistro.getRecipient());
    logPagtoVal.setNomeSacado(dadosRegistro.getPayer());
    logPagtoVal.setValorDescontoCalculado(dadosRegistro.getDiscountValue().doubleValue());
    logPagtoVal.setValorJurosCalculado(dadosRegistro.getInterestValueCalculated().doubleValue());
    logPagtoVal.setValorMaximoPagamento(dadosRegistro.getMaxValue().doubleValue());
    logPagtoVal.setValorMinimoPagamento(dadosRegistro.getMinValue().doubleValue());
    logPagtoVal.setValorMultaCalculado(dadosRegistro.getFineValueCalculated().doubleValue());
    logPagtoVal.setValorNominal(dadosRegistro.getOriginalValue().doubleValue());
    logPagtoVal.setValorPagamentoAtualizado(dadosRegistro.getTotalUpdated().doubleValue());
    logPagtoVal.setValorTotalAbatimento(dadosRegistro.getTotalWithDiscount().doubleValue());
    logPagtoVal.setValorTotalAcrescimo(dadosRegistro.getTotalWithAdditional().doubleValue());
    logPagtoVal.setPermiteAlterarValor(dadosRegistro.getAllowChangevalue());
    if (dadosRegistro.getDueDateRegister() != null) {
      logPagtoVal.setDataVencimentoRegistro(
          DateUtil.parseDate("yyyy-MM-dd", dadosRegistro.getDueDateRegister()));
    } else {
      logPagtoVal.setDataVencimentoRegistro(null);
    }

    if (logPagtoVal.getDataVencimento() == null
        && logPagtoVal.getDataVencimentoRegistro() != null) {
      logPagtoVal.setDataVencimento(logPagtoVal.getDataVencimentoRegistro());
    }
    return logPagtoVal;
  }

  private LogPagtoTituloValidacao preencherDadosRegistroRendimento(
      LogPagtoTituloValidacao logPagtoVal, ConsultarLinhaDigitavelTituloResponse consultaTitulo) {

    logPagtoVal.setDocumentoBeneficiario(consultaTitulo.getDocumentoBeneficiario());
    logPagtoVal.setDocumentoSacado(consultaTitulo.getDocumentoSacado());
    if (consultaTitulo.getDataLimitePagamento() != null) {
      logPagtoVal.setDataLimitePagamento(consultaTitulo.getDataLimitePagamento());
    } else {
      logPagtoVal.setDataLimitePagamento(null);
    }
    logPagtoVal.setDataProximoDiaUtil(null);
    logPagtoVal.setNomeBeneficiario(consultaTitulo.getNomeBeneficiario());
    logPagtoVal.setNomeSacado(consultaTitulo.getNomeSacado());
    logPagtoVal.setValorDescontoCalculado(consultaTitulo.getValorDescontoCalculado());
    logPagtoVal.setValorJurosCalculado(consultaTitulo.getValorJurosCalculado());
    logPagtoVal.setValorMaximoPagamento(consultaTitulo.getValorMaximoPagamento());
    logPagtoVal.setValorMinimoPagamento(consultaTitulo.getValorMinimoPagamento());
    logPagtoVal.setValorMultaCalculado(consultaTitulo.getValorMultaCalculado());
    logPagtoVal.setValorNominal(consultaTitulo.getValorNominal());
    logPagtoVal.setValorPagamentoAtualizado(consultaTitulo.getValorPagamentoAtualizado());
    logPagtoVal.setValorTotalAbatimento(consultaTitulo.getValorTotalAbatimento());
    logPagtoVal.setValorTotalAcrescimo(consultaTitulo.getValorTotalAcrescimo());
    logPagtoVal.setValorTotal(consultaTitulo.getValorTotal());
    logPagtoVal.setPermiteAlterarValor(consultaTitulo.getPermiteAlterarValor());
    if (consultaTitulo.getDataVencimentoRegistro() != null) {
      logPagtoVal.setDataVencimentoRegistro(consultaTitulo.getDataVencimentoRegistro());
    } else {
      logPagtoVal.setDataVencimentoRegistro(null);
    }

    if (logPagtoVal.getDataVencimento() == null
        && logPagtoVal.getDataVencimentoRegistro() != null) {
      logPagtoVal.setDataVencimento(logPagtoVal.getDataVencimentoRegistro());
    }
    return logPagtoVal;
  }

  public EfetuarPagamentoTituloResponse efetuarPagamentoTituloRendimentoV3(
      LogPagtoTituloValidacao logPagtoValidacao,
      ContaPagamento contaUsuarioLogado,
      ContratoGatewayPagto contratoUtilizado,
      Long idUsuario,
      EfetuarPagamentoTitulo req,
      String cpf,
      String nome,
      Integer tipoPessoa,
      GetSaldoConta saldoConta,
      boolean isPortador,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      ContaPagamento contaInstituicao,
      GetSaldoConta saldoContaInst,
      Integer idMoeda)
      throws IOException, JsonProcessingException {

    logger.info("Iniciando Pagamento Titulo Rendimento");

    EfetuarPagamentoTituloResponse resposta = new EfetuarPagamentoTituloResponse();

    Date dtHrInicioPagto = new Date();
    validarValorPagto(logPagtoValidacao, req);
    checarSeValorEstaDeAcordoComRegraTipoValorDivergenteRendimento(logPagtoValidacao, req);

    BigDecimal valorAPagarRendimento = getValorAPagar(logPagtoValidacao, req);

    BigDecimal valorAPagarJcard = getValorAPagar(logPagtoValidacao, req);

    // Verifica se a conta é de PONTOS para fazer conversão de valor
    if (idMoeda.equals(999)) {
      valorAPagarJcard = valorAPagarJcard.multiply(new BigDecimal(100));
    }

    verificarSaldoSuficiente(saldoConta, valorAPagarJcard);

    // se o saldo estiver preenchido eh porque precisa validar o saldo da
    // instituicao
    if (saldoContaInst != null) {
      verificarSaldoSuficiente(saldoContaInst, valorAPagarJcard);
    }
    verificarSaldoSuficiente(saldoConta, valorAPagarJcard);

    String rrnInst = null;
    String rrnConta = null;

    boolean possuiContaGarantia =
        contratoInstituicao.getDebitarGarantiaInst() != null
            && contratoInstituicao.getDebitarGarantiaInst();

    if (possuiContaGarantia) {
      // Efetuar pagamento na Conta Garantia
      rrnInst =
          resgatePagamentoContaService.sensibilizarConta(
              contaInstituicao,
              valorAPagarJcard,
              contratoUtilizado,
              isPortador,
              idUsuario,
              req.getIp(),
              contratoInstituicao.getCodTransacaoInst(),
              contratoInstituicao.getTextoExtrato()
                  + " : "
                  + logPagtoValidacao.getNomeBeneficiario());
    }

    // Sensibilizar conta do Portador
    try {
      rrnConta =
          resgatePagamentoContaService.sensibilizarConta(
              contaUsuarioLogado,
              valorAPagarJcard,
              contratoUtilizado,
              isPortador,
              idUsuario,
              req.getIp(),
              contratoInstituicao.getCodTransacaoPortador(),
              contratoInstituicao.getTextoExtrato()
                  + " : "
                  + logPagtoValidacao.getNomeBeneficiario());
    } catch (Exception e) {
      resposta.setMensagemErro(ConstantesErro.SAL_FALHA_AO_SENSIBILIZAR_SALDO.getMensagem());
      resposta.setSucesso(false);
      return resposta;
    }

    LogPagtoTituloTransacao logPagtoTituloTransacao = new LogPagtoTituloTransacao();
    TipoBoletoEnum tipoBoleto = validaTipoBoleto(logPagtoValidacao.getLinhaDigitavel());

    try {
      logPagtoTituloTransacao.setIdTransacaoPagto(rrnConta);
      logPagtoTituloTransacao.setIdTransacaoPagtoInst(rrnInst);
      logPagtoTituloTransacao.setDataHoraInicioPagto(dtHrInicioPagto);
      logPagtoTituloTransacao.setIdContratoGatewayPagtoTituloInst(
          contratoInstituicao.getIdContratoGatewayPagto());
      logPagtoTituloTransacao.setValor(valorAPagarRendimento.doubleValue());
      logPagtoTituloTransacao.setIdLogPagtoTitulo(logPagtoValidacao.getIdLogPagtoTitulo());
      logPagtoTituloTransacao.setIdConta(contaUsuarioLogado.getIdConta());
      logPagtoTituloTransacao.setIdUsuarioInclusao(idUsuario);

      if (contratoUtilizado
          .getTipoGatewayPagto()
          .equals(GatewayPagtoExternoService.COD_GATEWAY_RENDIMENTO)) {

        logPagtoTituloTransacao.setStatusPagamento(
            StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());

        if (tipoBoleto.equals(TipoBoletoEnum.TITULO)) {
          logPagtoTituloTransacao.setStatusPagamento(
              StatusTransacao.EM_EFETIVACAO.getCodigo()); // Mudar status transação
        }
      }

      logPagtoTituloTransacao = logPagtoTransacaoRepository.save(logPagtoTituloTransacao);

      PagamentoTituloRendimentoResponse res =
          this.chamarServicoRendimentoDeAcordoComSegmento(
              logPagtoValidacao.getLinhaDigitavel(),
              contratoUtilizado,
              logPagtoValidacao,
              req,
              cpf,
              nome,
              tipoPessoa,
              valorAPagarRendimento,
              contratoInstituicao,
              logPagtoTituloTransacao);

      if (res.getSuccess()) {

        Date dtHrfimPagto = new Date();
        logPagtoTituloTransacao.setDataHoraFimPagto(dtHrfimPagto);

        logPagtoTituloTransacao.setResultadoPagto(PAGAMENTO_SUCESSO_RENDIMENTO);
        logPagtoTituloTransacao.setResultadoConfirm(PAGAMENTO_SUCESSO_RENDIMENTO);
        logPagtoTituloTransacao.setDescResultadoConfirm(res.getData().getStatus());
        logPagtoTituloTransacao.setStatusConfirmacao(res.getData().getStatus());
        logPagtoTituloTransacao.setStatusTransacao(res.getData().getStatus());

        String tipoConvenio = this.consultarConveniosBoleto(logPagtoValidacao.getLinhaDigitavel());

        logPagtoTituloTransacao.setAutenticacaoRendimento(
            res.getData().getDetalhesComprovante().getAutenticacao() != null
                ? res.getData().getDetalhesComprovante().getAutenticacao()
                : null);
        logPagtoTituloTransacao.setDataLiquidacao(
            res.getData().getDataEfetivacao() != null
                ? DateUtil.parseDate("yyyy-MM-dd", res.getData().getDataEfetivacao())
                : null);
        logPagtoTituloTransacao.setDataOperacao(
            res.getData().getDataPagamento() != null
                ? DateUtil.parseDate("yyyy-MM-dd", res.getData().getDataPagamento())
                : new Date());
        logPagtoTituloTransacao.setProtocoloIdRendimento(res.getData().getTransacaoId());

        fromRespostaContaToLogPagtoV3(res, logPagtoTituloTransacao, nome, tipoConvenio);

        resposta.setSucesso(true);
        resposta.setComprovanteFormatado(logPagtoTituloTransacao.getComprovanteFormatado());
        // Por se tratar de tipos da dados diferentes, não está sendo pego  a autenticação do
        // rendimento, sendo passado para o VO abaixo que é usado no front
        resposta.setAutenticacao(null);
        resposta.setCodigoRetorno(PAGAMENTO_SUCESSO_RENDIMENTO);
        if (logPagtoTituloTransacao.getDataLiquidacao() != null) {
          resposta.setDataLiquidacao(logPagtoTituloTransacao.getDataLiquidacao());
        }
        resposta.setDataOperacao(logPagtoTituloTransacao.getDataOperacao());
        resposta.setProtocoloId(
            logPagtoTituloTransacao.getProtocoloIdRendimento() != null
                ? logPagtoTituloTransacao.getProtocoloIdRendimento().toString()
                : null);

        TransacaoBoletoResponseVO transacaoBoletoResponseVO = new TransacaoBoletoResponseVO();
        transacaoBoletoResponseVO.setPagador(nome);
        transacaoBoletoResponseVO.setDestinatario(
            res.getData().getDetalhesComprovante().getFavorecido() != null
                ? res.getData().getDetalhesComprovante().getFavorecido()
                : res.getData().getDetalhesComprovante().getConvenio());
        transacaoBoletoResponseVO.setValorTitulo(
            Util.currencyFormat(BigDecimal.valueOf(logPagtoTituloTransacao.getValor())));
        int primeiroDigito =
            Integer.parseInt(logPagtoValidacao.getLinhaDigitavel().substring(0, 1));
        if (primeiroDigito == 8) {
          transacaoBoletoResponseVO.setValorCobrado(
              Util.currencyFormat(
                  BigDecimal.valueOf(Double.valueOf(res.getData().getValorEfetivacao()))));
        } else {
          transacaoBoletoResponseVO.setValorCobrado(res.getData().getValorEfetivacao());
        }
        transacaoBoletoResponseVO.setAutenticacao(
            logPagtoTituloTransacao.getAutenticacaoRendimento());
        transacaoBoletoResponseVO.setProtocoloId(
            logPagtoTituloTransacao.getProtocoloIdRendimento());
        transacaoBoletoResponseVO.setCodigoDeBarras(logPagtoValidacao.getLinhaDigitavel());
        transacaoBoletoResponseVO.setIdConta(logPagtoTituloTransacao.getIdConta().toString());
        transacaoBoletoResponseVO.setDataOperacao(
            DateUtil.dateFormat(
                DateUtil.DD_MM_YYYY_HH_MM_SS, logPagtoTituloTransacao.getDataHoraInicioPagto()));
        if (logPagtoTituloTransacao.getDataLiquidacao() != null) {
          transacaoBoletoResponseVO.setDataLiquidacao(
              DateUtil.dateFormat(
                  DateUtil.DD_MM_YYYY_HH_MM_SS, logPagtoTituloTransacao.getDataLiquidacao()));
        }

        if (Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(
            contaUsuarioLogado.getIdInstituicao())) {
          transacaoBoletoResponseVO.setIdConta("RP3 BANK");
        }

        resposta.setBoleto(transacaoBoletoResponseVO);

        // Salvar o limite diario
        limitesContaService.salvarLimiteDiario(
            BigDecimal.valueOf(req.getValor()), contaUsuarioLogado.getIdConta());

      } else {
        logPagtoTituloTransacao.setResultadoPagto(PAGAMENTO_FALHA_RENDIMENTO);
        logPagtoTituloTransacao.setDescResultadoPagto(res.getMessage());
        logPagtoTituloTransacao.setMensagemErro(res.getMessage());
        resposta.setDataOperacao(logPagtoTituloTransacao.getDataOperacao());

        resposta.setSucesso(false);
        resposta.setCodigoRetorno(PAGAMENTO_FALHA_RENDIMENTO);
        resposta.setMensagemErro(res.getMessage());

        JcardResponseRrn estornoJcard = transactionService.estonarTransacao(rrnConta);
        logPagtoTituloTransacao.setIdTransacaoDesfPagto(estornoJcard.getRrn());

        logger.error("Retorno completo do Rendimento: ", res);
      }
      logPagtoTransacaoRepository.save(logPagtoTituloTransacao);

      return resposta;
    } catch (Exception e) {
      transactionService.estonarTransacao(rrnConta);

      logger.error("Pagamento de Contas ERROR: {}", e.getMessage());
      e.printStackTrace();

      ObjectMapper objectMapper = new ObjectMapper();
      PagamentoTituloRendimentoResponseV2 responseTituloRendimento = null;

      try {
        responseTituloRendimento =
            objectMapper.readValue(e.getMessage(), PagamentoTituloRendimentoResponseV2.class);
      } catch (IOException ex) {
        responseTituloRendimento = new PagamentoTituloRendimentoResponseV2();
        logger.error(ConstantesErro.REND_ERRO_AO_TRADUZIR_MSG.getMensagem());
      }

      logPagtoTituloTransacao.setResultadoPagto(PAGAMENTO_FALHA_RENDIMENTO);
      logPagtoTituloTransacao.setDescResultadoPagto(
          responseTituloRendimento.getNotifications().get(0).getMessage());
      logPagtoTituloTransacao.setMensagemErro(
          responseTituloRendimento.getNotifications().get(0).getMessage());
      logPagtoTituloTransacao.setStatusPagamento(StatusTransacao.ERRO.getCodigo());

      logPagtoTransacaoRepository.save(logPagtoTituloTransacao);

      resposta.setDataOperacao(logPagtoTituloTransacao.getDataOperacao());

      resposta.setSucesso(false);
      resposta.setCodigoRetorno(PAGAMENTO_FALHA_RENDIMENTO);
      resposta.setMensagemErro(responseTituloRendimento.getNotifications().get(0).getMessage());

      return resposta;
    }
  }

  private void fromRespostaContaToLogPagtoV3(
      PagamentoTituloRendimentoResponse resp,
      LogPagtoTituloTransacao logTran,
      String nome,
      String tipoConvenio) {

    try {
      //		SEM USO DEVIDO QUE O COMPROVANTE É MONTADO DIRETAMENTE NO FRONT DO APP, TENDO APENAS QUE
      // RETORNAS OS DADOS NECESSÁRIOS
      logTran.setAutenticacaoRendimento(
          resp.getData().getDetalhesComprovante().getAutenticacao() != null
              ? resp.getData().getDetalhesComprovante().getAutenticacao()
              : null);

      logTran.setStatusTransacao(resp.getMessage());
      logTran.setDataLiquidacao(
          resp.getData().getDataEfetivacao() != null
              ? DateUtil.parseDate("yyyy-MM-dd", resp.getData().getDataEfetivacao())
              : null);
      logTran.setDataOperacao(
          resp.getData().getDataPagamento() != null
              ? DateUtil.parseDate("yyyy-MM-dd", resp.getData().getDataPagamento())
              : new Date());
      logTran.setProtocoloIdRendimento(resp.getData().getTransacaoId());

      String valor =
          resp.getData().getValor() != null
              ? resp.getData().getValor().replace(',', '.')
              : resp.getData().getValorTotal() != null
                  ? resp.getData().getValorTotal().replace(',', '.')
                  : resp.getData().getValorPago().replace(',', '.');

      String autenticacao =
          resp.getData().getDetalhesComprovante().getAutenticacao() != null
              ? "-----------------------------------------------\r\n"
                  + "\r\n"
                  + "AUTENTICAÇÃO "
                  + resp.getData().getDetalhesComprovante().getAutenticacao()
              : "";

      String codigoDeBarras =
          tipoConvenio.equals("DARF")
              ? resp.getData().getDetalhesComprovante().getCodigoDeBarra()
              : resp.getData().getDetalhesComprovante().getCodigoDeBarras();

      if (resp.getSuccess()) {
        //			String parsedDateEfetivacao =
        // DateUtil.convertStringLocalDateToDataFormatoBR(resp.getData().getDataEfetivacao());
        String parsedDatePagamento =
            DateUtil.convertStringLocalDateToDataHoraFormatoBR(resp.getData().getDataPagamento());

        String beneficiario =
            resp.getData().getDetalhesComprovante().getFavorecido() != null
                ? resp.getData().getDetalhesComprovante().getFavorecido()
                : resp.getData().getDetalhesComprovante().getConvenio();

        String comprovanteStr =
            "PROTOCOLO "
                + resp.getData().getTransacaoId()
                + "\r\n        "
                + "\r\n"
                + "----------------------------------------------\r\n"
                + " CÓDIGO DE BARRAS: \r\n "
                + codigoDeBarras
                + " \r\n\n"
                + " BENEF: "
                + beneficiario
                + "\r\n"
                + " PAGADOR: "
                + nome
                + "\r\n"
                + " CONTA: "
                + logTran.getIdConta()
                + "\r\n"
                + "-----------------------------------------------\r\n"
                + " DATA DO PAGAMENTO: "
                + parsedDatePagamento
                + "\r\n"
                +
                //			         " DATA DE LIQUIDACAO: " + parsedDateEfetivacao + "\r\n" +
                " VALOR TITULO: "
                + Util.currencyFormat(new BigDecimal(valor))
                + "\r\n"
                +
                //			         " VALOR COBRADO: " + Util.currencyFormat(new
                // BigDecimal(resp.getData().getValorEfetivacao().replace(',', '.'))) + "\r\n\n" +
                " VALIDO COMO RECIBO DE PAGAMENTO \r\n"
                + autenticacao
                + "\r\n"
                + "===============================================\r\n";

        logTran.setComprovanteFormatado(comprovanteStr);
      }
    } catch (Exception e) {
      logger.warn("Campo nulo, não foi possivel salvar dados de comprovante no banco.");
      e.printStackTrace();
    }
  }

  private PagamentoTituloRendimentoResponse efetuarPagamentoRendimentoV3(
      ContratoGatewayPagto contrato,
      LogPagtoTituloValidacao logPagto,
      EfetuarPagamentoTitulo req,
      String cpf,
      String nome,
      Integer tipoPessoa,
      BigDecimal valorAPagar,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      LogPagtoTituloTransacao logTransacao) {

    PagamentoTituloRendimentoResponse response = null;
    PagamentoConsumoRendimentoRequest request = new PagamentoConsumoRendimentoRequest();

    //		PAGAMENTO DE AGUA, LUZ, TELEFONE ETC
    try {

      this.propriedadesRendimento = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_V1_PAGAMENTO_TITULO);
      endPoint.setUri(Constantes.URI_RENDIMENTO_CONTA_CONSUMO);
      this.propriedadesRendimento.setPagamentoTitulo(endPoint);
      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      DadosConta conta = getContaPagamentoFacade().getDadosConta(logPagto.getIdConta());
      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(conta.getIdInstituicao());

      request = preencherRequestRendimentoV3(logPagto, valorAPagar, logTransacao);

      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, propriedadesRendimento.getAutenticacao());

      HttpEntity<PagamentoConsumoRendimentoRequest> entity =
          new HttpEntity<PagamentoConsumoRendimentoRequest>(request, headers);

      response =
          restTemplate
              .postForEntity(
                  this.propriedadesRendimento.getUrlPagamentoTitulo()
                      + chaveInstituicao.getAgencia()
                      + "/"
                      + chaveInstituicao.getConta()
                      + this.propriedadesRendimento.getPagamentoTitulo().getUri(),
                  entity,
                  PagamentoTituloRendimentoResponse.class)
              .getBody();

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.error(
          "Pagamento de Consumo API Rendimento error. Request: {} . Response: {} ",
          request,
          e.getResponseBodyAsString());
      e.printStackTrace();

      logger.info(e.getResponseBodyAsString());
      ObjectMapper objectMapper = new ObjectMapper();
      PagamentoTituloRendimentoResponse pagamentoTituloRendimentoResponse = null;

      try {
        pagamentoTituloRendimentoResponse =
            objectMapper.readValue(
                e.getResponseBodyAsString(), PagamentoTituloRendimentoResponse.class);
      } catch (IOException ex) {
        pagamentoTituloRendimentoResponse = new PagamentoTituloRendimentoResponse();
        logger.error(ConstantesErro.REND_ERRO_AO_TRADUZIR_MSG.getMensagem());
      }

      pagamentoTituloRendimentoResponse.setSuccess(false);
      pagamentoTituloRendimentoResponse.setMessage(
          pagamentoTituloRendimentoResponse.getNotifications() != null
                  && pagamentoTituloRendimentoResponse.getNotifications().get(0).getMessage()
                      != null
              ? pagamentoTituloRendimentoResponse.getNotifications().get(0).getMessage()
              : ConstantesErro.REND_ERRO_AO_REALIZAR_PAGAMENTO_CONSUMO.getMensagem());
      return pagamentoTituloRendimentoResponse;
    }
    return response;
  }

  private PagamentoTituloRendimentoResponse efetuarPagamentoTributoRendimento(
      ContratoGatewayPagto contrato,
      LogPagtoTituloValidacao logPagto,
      EfetuarPagamentoTitulo req,
      String cpf,
      String nome,
      Integer tipoPessoa,
      BigDecimal valorAPagar,
      LogPagtoTituloTransacao logTransacao,
      Boolean isDarf,
      Boolean isFgts) {
    PagamentoTituloRendimentoResponse response = null;
    PagamentoBoletosRendimentoRequest request = null;

    //		PAGAMENTO DE TRIBUTO - PREFEITURA, ORGAOS GOVERNAMENTAIS, CARNES E MULTAS DE TRANSITO
    try {

      this.propriedadesRendimento = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_V1_PAGAMENTO_TITULO);
      endPoint.setUri(Constantes.URI_RENDIMENTO_TRIBUTOS);
      this.propriedadesRendimento.setPagamentoTitulo(endPoint);
      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, propriedadesRendimento.getAutenticacao());

      if (isDarf) {

        request = new PagamentoTributoDARFRendimentoRequest();
        request = preencherRequestTributoDARFRendimentoV3(logPagto, valorAPagar, nome, req);
        HttpEntity<PagamentoBoletosRendimentoRequest> entity =
            new HttpEntity<PagamentoBoletosRendimentoRequest>(request, headers);

        response =
            restTemplate
                .postForEntity(
                    this.propriedadesRendimento.getUrlPagamentoTitulo()
                        + chaveInstituicao.getAgencia()
                        + "/"
                        + chaveInstituicao.getConta()
                        + this.propriedadesRendimento.getPagamentoTitulo().getUri(),
                    entity,
                    PagamentoTituloRendimentoResponse.class)
                .getBody();

      } else if (isFgts) {

        request = new PagamentoTributoFGTSRendimentoRequest();

        request =
            preencherRequestTributoFGTSRendimentoV3(
                logPagto, valorAPagar, req.getFgtsRendimentoRequest());
        HttpEntity<PagamentoBoletosRendimentoRequest> entity =
            new HttpEntity<PagamentoBoletosRendimentoRequest>(request, headers);

        response =
            restTemplate
                .postForEntity(
                    this.propriedadesRendimento.getUrlPagamentoTitulo()
                        + chaveInstituicao.getAgencia()
                        + "/"
                        + chaveInstituicao.getConta()
                        + this.propriedadesRendimento.getPagamentoTitulo().getUri(),
                    entity,
                    PagamentoTituloRendimentoResponse.class)
                .getBody();
      } else {

        request = new PagamentoTributoRendimentoRequest();

        request = preencherRequestTributoRendimentoV3(logPagto, valorAPagar, logTransacao);
        HttpEntity<PagamentoBoletosRendimentoRequest> entity =
            new HttpEntity<PagamentoBoletosRendimentoRequest>(request, headers);

        response =
            restTemplate
                .postForEntity(
                    this.propriedadesRendimento.getUrlPagamentoTitulo()
                        + chaveInstituicao.getAgencia()
                        + "/"
                        + chaveInstituicao.getConta()
                        + this.propriedadesRendimento.getPagamentoTitulo().getUri(),
                    entity,
                    PagamentoTituloRendimentoResponse.class)
                .getBody();
      }
    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.error(
          "Pagamento de Tributos API Rendimento error. Request: {} . Response: {} ",
          request,
          e.getResponseBodyAsString());
      e.printStackTrace();

      logger.info(e.getResponseBodyAsString());
      ObjectMapper objectMapper = new ObjectMapper();

      PagamentoTituloRendimentoResponse pagamentoTituloRendimentoResponse = null;

      try {
        pagamentoTituloRendimentoResponse =
            objectMapper.readValue(
                e.getResponseBodyAsString(), PagamentoTituloRendimentoResponse.class);
      } catch (IOException ex) {
        pagamentoTituloRendimentoResponse = new PagamentoTituloRendimentoResponse();
        logger.error(ConstantesErro.REND_ERRO_AO_TRADUZIR_MSG.getMensagem());
      }

      pagamentoTituloRendimentoResponse.setSuccess(false);
      pagamentoTituloRendimentoResponse.setMessage(
          pagamentoTituloRendimentoResponse.getNotifications() != null
                  && pagamentoTituloRendimentoResponse.getNotifications().get(0).getMessage()
                      != null
              ? pagamentoTituloRendimentoResponse.getNotifications().get(0).getMessage()
              : ConstantesErro.REND_ERRO_AO_REALIZAR_PAGAMENTO_TRIBUTOS.getMensagem());
      return pagamentoTituloRendimentoResponse;
    }
    return response;
  }

  private PagamentoTituloRendimentoResponse efetuarPagamentoTituloRendimento(
      LogPagtoTituloValidacao logPagto,
      String cpf,
      String nome,
      Integer tipoPessoa,
      BigDecimal valorAPagar) {
    PagamentoTituloRendimentoResponse response = null;
    PagamentoTituloRendimentoRequest request = new PagamentoTituloRendimentoRequest();

    //		PAGAMENTO DE TITULO - e commerce e afins
    try {

      Boolean ambienteHomol = null;

      this.propriedadesRendimento = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();

      endPoint.setUrl(Constantes.URL_RENDIMENTO_EVOTITULOS);
      endPoint.setUri(Constantes.URL_RENDIMENTO_NOVO_PAGAMENTO_TITULO);
      this.propriedadesRendimento.setPagamentoTitulo(endPoint);
      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      DadosConta conta = getContaPagamentoFacade().getDadosConta(logPagto.getIdConta());
      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(conta.getIdInstituicao());

      request =
          preencherRequestTituloRendimentoV3(logPagto, valorAPagar, cpf, nome, tipoPessoa, null);

      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, propriedadesRendimento.getAutenticacao());

      HttpEntity<PagamentoTituloRendimentoRequest> entity =
          new HttpEntity<PagamentoTituloRendimentoRequest>(request, headers);

      String url =
          this.propriedadesRendimento.getUrlPagamentoTitulo()
              + "/"
              + chaveInstituicao.getAgencia()
              + "/"
              + chaveInstituicao.getConta()
              + this.propriedadesRendimento.getPagamentoTitulo().getUri();

      response =
          restTemplate
              .postForEntity(url, entity, PagamentoTituloRendimentoResponse.class)
              .getBody();

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.error(
          "Pagamento de Títulos API Rendimento error. Request: {} . Response Error: {} ",
          request,
          e.getResponseBodyAsString());
      e.printStackTrace();

      ObjectMapper objectMapper = new ObjectMapper();
      PagamentoTituloRendimentoResponse pagamentoTituloRendimentoResponse = null;
      PagamentoTituloRendimentoResponseV2 responseTituloRendimento = null;

      try {
        responseTituloRendimento =
            objectMapper.readValue(
                e.getResponseBodyAsString(), PagamentoTituloRendimentoResponseV2.class);
      } catch (IOException ex) {
        responseTituloRendimento = new PagamentoTituloRendimentoResponseV2();
        responseTituloRendimento.setSuccess(false);
        logger.error(ConstantesErro.REND_ERRO_AO_TRADUZIR_MSG.getMensagem());
      }

      pagamentoTituloRendimentoResponse =
          montarResponseRendimentoV2toOriginal(responseTituloRendimento);

      return pagamentoTituloRendimentoResponse;
    }
    return response;
  }

  private PagamentoConsumoRendimentoRequest preencherRequestRendimentoV3(
      LogPagtoTituloValidacao logPagto,
      BigDecimal valorAPagar,
      LogPagtoTituloTransacao logTransacao) {

    PagamentoConsumoRendimentoRequest pagamentoConsumoRendimentoRequest =
        new PagamentoConsumoRendimentoRequest();
    BillDataDto billDataDto = new BillDataDto();
    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    billDataDto.setValue(valorAPagar.floatValue());
    billDataDto.setOriginalValue(
        logPagto.getValorNominal() == null ? 0.00F : logPagto.getValorNominal().floatValue());
    billDataDto.setValueWithDiscount(
        logPagto.getValorDescontoCalculado() == null
            ? 0.00F
            : logPagto.getValorDescontoCalculado().floatValue());
    billDataDto.setValueWithAdditional(
        logPagto.getValorTotalAcrescimo() == null
            ? 0.00F
            : logPagto.getValorTotalAcrescimo().floatValue());

    barCode.setBarCode(STRING_VAZIA);
    barCode.setDigitable(logPagto.getLinhaDigitavel());
    barCode.setType(2);

    pagamentoConsumoRendimentoRequest.setCodigoDeControle(
        logTransacao.getIdLogPagtoTituloTransacao().toString());
    pagamentoConsumoRendimentoRequest.setCodigoDeBarrasOuLinhaDigitavel(barCode.getDigitable());
    pagamentoConsumoRendimentoRequest.setValor(valorAPagar);
    pagamentoConsumoRendimentoRequest.setIdentificacaoDaOperacaoNoExtrato(
        "Pagamento de conta de consumo");

    return pagamentoConsumoRendimentoRequest;
  }

  private PagamentoTributoRendimentoRequest preencherRequestTributoRendimentoV3(
      LogPagtoTituloValidacao logPagto,
      BigDecimal valorAPagar,
      LogPagtoTituloTransacao logTransacao) {

    PagamentoTributoRendimentoRequest pagamentoTributoRendimentoRequest =
        new PagamentoTributoRendimentoRequest();
    BillDataDto billDataDto = new BillDataDto();
    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    billDataDto.setValue(valorAPagar.floatValue());
    billDataDto.setOriginalValue(
        logPagto.getValorNominal() == null ? 0.00F : logPagto.getValorNominal().floatValue());
    billDataDto.setValueWithDiscount(
        logPagto.getValorDescontoCalculado() == null
            ? 0.00F
            : logPagto.getValorDescontoCalculado().floatValue());
    billDataDto.setValueWithAdditional(
        logPagto.getValorTotalAcrescimo() == null
            ? 0.00F
            : logPagto.getValorTotalAcrescimo().floatValue());

    barCode.setBarCode(STRING_VAZIA);
    barCode.setDigitable(logPagto.getLinhaDigitavel());
    barCode.setType(1);

    pagamentoTributoRendimentoRequest.setCodigoControleTransacao(
        logTransacao.getIdLogPagtoTituloTransacao().toString());
    pagamentoTributoRendimentoRequest.setCodigoDeBarrasOuLinhaDigitavel(barCode.getDigitable());
    pagamentoTributoRendimentoRequest.setValorDoPagamento(valorAPagar);
    pagamentoTributoRendimentoRequest.setValorPrincipal(valorAPagar);
    pagamentoTributoRendimentoRequest.setIdentificacaoDaOperacaoNoExtrato(
        "Pagamento de conta tributo");

    return pagamentoTributoRendimentoRequest;
  }

  private PagamentoTributoFGTSRendimentoRequest preencherRequestTributoFGTSRendimentoV3(
      LogPagtoTituloValidacao logPagto,
      BigDecimal valorAPagar,
      PagamentoTributoFGTSRendimentoRequest tributoFGTS) {

    PagamentoTributoFGTSRendimentoRequest pagamentoTributoFGTSRendimentoRequest =
        new PagamentoTributoFGTSRendimentoRequest();

    BillDataDto billDataDto = new BillDataDto();
    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    billDataDto.setValue(valorAPagar.floatValue());
    billDataDto.setOriginalValue(
        logPagto.getValorNominal() == null ? 0.00F : logPagto.getValorNominal().floatValue());
    billDataDto.setValueWithDiscount(
        logPagto.getValorDescontoCalculado() == null
            ? 0.00F
            : logPagto.getValorDescontoCalculado().floatValue());
    billDataDto.setValueWithAdditional(
        logPagto.getValorTotalAcrescimo() == null
            ? 0.00F
            : logPagto.getValorTotalAcrescimo().floatValue());

    barCode.setBarCode(STRING_VAZIA);
    barCode.setDigitable(logPagto.getLinhaDigitavel());
    barCode.setType(1);

    if (tributoFGTS.getInscricao() != null && !tributoFGTS.getInscricao().isEmpty()) {
      if (tributoFGTS.getInscricao().length() == 14) {
        tributoFGTS.setTipoIdentificacaoContribuinte(1);
      } else if (tributoFGTS.getInscricao().length() == 11) {
        tributoFGTS.setTipoIdentificacaoContribuinte(2);
      } else {
        throw new GenericServiceException(
            "Inscrição com valor inválido", HttpStatus.UNPROCESSABLE_ENTITY);
      }
    } else {
      throw new GenericServiceException(
          "Inscrição com valor nulo ou vazio", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    pagamentoTributoFGTSRendimentoRequest.setCodigoDeBarrasOuLinhaDigitavel(barCode.getDigitable());
    pagamentoTributoFGTSRendimentoRequest.setValorDoPagamento(valorAPagar);
    pagamentoTributoFGTSRendimentoRequest.setIdentificacaoDaOperacaoNoExtrato("Tributo FGTS");
    pagamentoTributoFGTSRendimentoRequest.setTipoIdentificacaoContribuinte(
        tributoFGTS.getTipoIdentificacaoContribuinte());
    pagamentoTributoFGTSRendimentoRequest.setInscricao(tributoFGTS.getInscricao());
    pagamentoTributoFGTSRendimentoRequest.setIdentificador(
        tributoFGTS.getIdentificador() != null ? tributoFGTS.getIdentificador() : "");
    pagamentoTributoFGTSRendimentoRequest.setLacre(
        tributoFGTS.getLacre() != null ? tributoFGTS.getLacre() : "");
    pagamentoTributoFGTSRendimentoRequest.setLacreDigito(
        tributoFGTS.getLacreDigito() != null ? tributoFGTS.getLacreDigito() : "");
    pagamentoTributoFGTSRendimentoRequest.setCodigoControleTransacao(
        tributoFGTS.getCodigoControleTransacao() != null
            ? tributoFGTS.getCodigoControleTransacao()
            : "");

    return pagamentoTributoFGTSRendimentoRequest;
  }

  private PagamentoTributoDARFRendimentoRequest preencherRequestTributoDARFRendimentoV3(
      LogPagtoTituloValidacao logPagto,
      BigDecimal valorAPagar,
      String nome,
      EfetuarPagamentoTitulo req) {

    PagamentoTributoDARFRendimentoRequest tributoDARF = new PagamentoTributoDARFRendimentoRequest();

    tributoDARF = req.getDarfRendimentoRequest();

    if (tributoDARF.getValorJuros().doubleValue() > 0.0
        || tributoDARF.getValorMulta().doubleValue() > 0.0) {
      Double totalComTributos =
          tributoDARF.getValorTotal().doubleValue()
              + tributoDARF.getValorMulta().doubleValue()
              + tributoDARF.getValorJuros().doubleValue();

      tributoDARF.setValorTotal(BigDecimal.valueOf(totalComTributos));

      req.setValor(tributoDARF.getValorTotal().doubleValue());
    }

    valorAPagar = BigDecimal.valueOf(req.getValor());

    PagamentoTributoDARFRendimentoRequest pagamentoTributoDARFRendimentoRequest =
        new PagamentoTributoDARFRendimentoRequest();
    BillDataDto billDataDto = new BillDataDto();
    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    billDataDto.setValue(valorAPagar.floatValue());
    billDataDto.setOriginalValue(
        logPagto.getValorNominal() == null ? 0.00F : logPagto.getValorNominal().floatValue());
    billDataDto.setValueWithDiscount(
        logPagto.getValorDescontoCalculado() == null
            ? 0.00F
            : logPagto.getValorDescontoCalculado().floatValue());
    billDataDto.setValueWithAdditional(
        logPagto.getValorTotalAcrescimo() == null
            ? 0.00F
            : logPagto.getValorTotalAcrescimo().floatValue());

    barCode.setBarCode(STRING_VAZIA);
    barCode.setDigitable(logPagto.getLinhaDigitavel());
    barCode.setType(1);

    pagamentoTributoDARFRendimentoRequest.setCodigoDeBarrasOuLinhaDigitavel(barCode.getDigitable());
    pagamentoTributoDARFRendimentoRequest.setValorDoPagamento(valorAPagar);
    pagamentoTributoDARFRendimentoRequest.setValorPrincipal(valorAPagar);
    pagamentoTributoDARFRendimentoRequest.setTipo("DARFCodigoBarras");
    pagamentoTributoDARFRendimentoRequest.setNome(nome);
    pagamentoTributoDARFRendimentoRequest.setPeriodoApuracao(
        tributoDARF.getPeriodoApuracao() != null ? tributoDARF.getPeriodoApuracao() : "");
    pagamentoTributoDARFRendimentoRequest.setIdentificacaoDaOperacaoNoExtrato(
        "Tributo DARF COM BARRA");
    pagamentoTributoDARFRendimentoRequest.setCpfcnpj(
        tributoDARF.getCpfcnpj() != null ? tributoDARF.getCpfcnpj() : "");
    pagamentoTributoDARFRendimentoRequest.setCodigoReceitaFederal_Id(
        tributoDARF.getCodigoReceitaFederal_Id() != null
            ? tributoDARF.getCodigoReceitaFederal_Id()
            : "");
    pagamentoTributoDARFRendimentoRequest.setDataVencimento(
        tributoDARF.getDataVencimento() != null ? tributoDARF.getDataVencimento() : "");
    pagamentoTributoDARFRendimentoRequest.setValorMulta(
        tributoDARF.getValorMulta() != null ? tributoDARF.getValorMulta() : BigDecimal.valueOf(0));
    pagamentoTributoDARFRendimentoRequest.setValorJuros(
        tributoDARF.getValorJuros() != null ? tributoDARF.getValorJuros() : BigDecimal.valueOf(0));
    pagamentoTributoDARFRendimentoRequest.setValorjurosencargos(
        tributoDARF.getValorjurosencargos() != null
            ? tributoDARF.getValorjurosencargos()
            : BigDecimal.valueOf(0));
    pagamentoTributoDARFRendimentoRequest.setValorTotal(valorAPagar);

    return pagamentoTributoDARFRendimentoRequest;
  }

  private PagamentoTituloRendimentoRequest preencherRequestTituloRendimentoV3(
      LogPagtoTituloValidacao logPagto,
      BigDecimal valorAPagar,
      String documento,
      String nome,
      Integer tipoPessoa,
      Boolean ambienteHomol) {

    PagamentoTituloRendimentoRequest pagamentoTituloRendimentoRequest =
        new PagamentoTituloRendimentoRequest();
    BillDataDto billDataDto = new BillDataDto();
    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    billDataDto.setValue(valorAPagar.floatValue());
    billDataDto.setOriginalValue(
        logPagto.getValorNominal() == null ? 0.00F : logPagto.getValorNominal().floatValue());
    billDataDto.setValueWithDiscount(
        logPagto.getValorDescontoCalculado() == null
            ? 0.00F
            : logPagto.getValorDescontoCalculado().floatValue());
    billDataDto.setValueWithAdditional(
        logPagto.getValorTotalAcrescimo() == null
            ? 0.00F
            : logPagto.getValorTotalAcrescimo().floatValue());

    barCode.setBarCode(logPagto.getCodigoBarras());
    barCode.setDigitable(logPagto.getLinhaDigitavel());

    barCode.setType(1);

    pagamentoTituloRendimentoRequest.setCodigoDeBarrasOuLinhaDigitavel(
        barCode.getBarCode() != null ? barCode.getBarCode() : barCode.getDigitable());
    pagamentoTituloRendimentoRequest.setValorDoPagamento(valorAPagar);
    pagamentoTituloRendimentoRequest.setValorDoTitulo(
        BigDecimal.valueOf(logPagto.getValorNominal()));
    pagamentoTituloRendimentoRequest.setIdentificacaoDaOperacaoNoExtrato(
        "Pagamento de conta titulo");
    pagamentoTituloRendimentoRequest.setCnpjCpfPagador(documento);
    pagamentoTituloRendimentoRequest.setRazaoSocialPagador(nome);
    pagamentoTituloRendimentoRequest.setTipoPessoaPagador(tipoPessoa == 1 ? "F" : "J");
    pagamentoTituloRendimentoRequest.setCodigoDeControle(null);
    pagamentoTituloRendimentoRequest.setUrlParceiro(null);

    return pagamentoTituloRendimentoRequest;
  }

  private void validarValorPagto(
      LogPagtoTituloValidacao entityProtocolo, EfetuarPagamentoTitulo req) {
    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getPermiteAlterarValor()
        && req.getValor() != null
        && req.getValor().compareTo(Double.valueOf(MIN_VALOR)) == MIN_VALOR) {
      throw new GenericServiceException(
          ConstantesErro.REND_VALOR_NAO_PODE_SER_ZERADO.getMensagem());
    }
    // 9999999,99.
    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getPermiteAlterarValor()
        && req.getValor() != null
        && req.getValor().compareTo(MAX_VALOR) >= MIN_VALOR) {
      throw new GenericServiceException(ConstantesErro.REND_VALOR_FORA_DO_PERMITIDO.getMensagem());
    }
  }

  private String desfazerTransacao(
      ContaPagamento contaUsuarioLogado,
      BigDecimal valorAPagar,
      boolean isPortador,
      Long idUsuario,
      String rrn,
      String textoExtrato,
      Long codTransacao) {

    CodigoTransacao codigo = new CodigoTransacao();
    BigDecimal amount = valorAPagar;

    codigo = codigoTransacaoService.findById(codTransacao.intValue());

    JcardResponseRrn jcardResponseRrn =
        lancamentoService.doLancamentoEstorno(
            contaUsuarioLogado.getIdConta(),
            rrn,
            codigo.getCodTranEstorno(),
            Constantes.CODIGO_MOEDA_PADRAO,
            amount);

    LancamentoManual lancamento = new LancamentoManual();

    lancamento.setCodTransacao(codigo.getCodTranEstorno());
    lancamento.setIdConta(contaUsuarioLogado.getIdConta());
    lancamento.setValor(amount);
    lancamento.setTextoExtrato(textoExtrato);
    lancamento.setTextoComentario("");
    lancamento.setDataHoraLancamento(LocalDateTime.now());
    lancamento.setIdProcessadora(contaUsuarioLogado.getIdProcessadora());
    lancamento.setIdInstituicao(contaUsuarioLogado.getIdInstituicao());
    lancamento.setIdUsuario(
        isPortador ? Constantes.ID_USUARIO_INCLUSAO_PORTADOR : idUsuario.intValue());
    lancamento.setRrn(jcardResponseRrn.getRrn());
    lancamento.setRrnEstornado(rrn);
    lancamentoService.saveAndFlush(lancamento);
    return jcardResponseRrn.getRrn();
  }

  private void confirmarPagtoContaCelcoinRecargaV5(EfetuarRecargaResponse res) {

    celcoinService.makeCaptureVoidTopups(res.getIdTransacaoInterna(), "", 0);
  }

  private String sensibilizarConta(
      ContaPagamento conta,
      BigDecimal valorAPagar,
      ContratoGatewayPagto contrato,
      boolean isPortador,
      Long idUsuario,
      String ipOrigem,
      Long codTransacao,
      String textoExtrato) {
    Boolean test = false;
    String rrn = null;

    try {

      CadastroLancamentoManual lancamento = new CadastroLancamentoManual();

      lancamento.setCodTransacao(codTransacao.intValue());
      lancamento.setIdConta(conta.getIdConta());
      lancamento.setValor(valorAPagar);
      lancamento.setTextoExtrato(textoExtrato);
      lancamento.setSinal(Constantes.DEBITADO);

      JcardResponse lancaResp =
          lancamentoService.saveLancamento(
              lancamento, idUsuario.intValue(), ipOrigem, isPortador, false);
      if (!lancaResp.getSuccess()) {
        throw new GenericServiceException(
            "Erro ao tentar sensibilizar conta. Detalhe: " + lancaResp.getErrors());
      }

      rrn = lancaResp.getRrn();

    } catch (JcardServiceException e) {
      if (e.getMensagem().contains(ERRO_SALDO_INSUFICIENTE)) { // not.sufficient.funds
        throw new GenericServiceException("Erro ao sensilizar conta, conta sem saldo suficiente.");
      }
      // Caso o produto nao possa receber essa cobrança
      if (e.getMensagem().contains(ERRO_FUNCTION_CODE)) { // invalid.functioncode
        throw new GenericServiceException(
            "Este produto não possui o function code fazer esta operação via gateway externo.");
      }
      throw new GenericServiceException("Erro ao tentar fazer esta operação via gateway externo");
    } catch (Exception e) {
      logger.error("--------------erro inexperado " + e.getMessage(), e);
      if (!test) {
        throw new GenericServiceException("Ocorreu um erro inexperado. ", e.getMessage());
      }
    }
    return rrn;
  }

  private BigDecimal getValorAPagar(
      LogPagtoTituloValidacao entityProtocolo, EfetuarPagamentoTitulo req) {
    BigDecimal valorAPagar = null;

    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getPermiteAlterarValor()
        && req.getValor() != null
        && req.getValor().compareTo(Double.valueOf(0.0)) > 0) {
      valorAPagar = new BigDecimal(req.getValor().toString());
    } else if (entityProtocolo.getValorPagamentoAtualizado() != null) {
      valorAPagar = new BigDecimal(entityProtocolo.getValorPagamentoAtualizado().toString());
    } else if (entityProtocolo.getValor() != null) {
      valorAPagar = new BigDecimal(entityProtocolo.getValor().toString());
    }

    if (valorAPagar == null) {
      throw new GenericServiceException(ConstantesErro.REND_VALOR_DEVE_SER_INFORMADO.getMensagem());
    }
    return valorAPagar;
  }

  private void verificarSaldoSuficiente(GetSaldoConta saldoConta, BigDecimal valorAPagar) {

    if (saldoConta == null) {
      throw new GenericServiceException(ConstantesErro.SAL_ERRO_CONSULTA.getMensagem());
    }

    if (saldoConta.getSaldoDisponivel() == null
        || saldoConta.getSaldoDisponivel().compareTo(valorAPagar) < 0) {
      throw new GenericServiceException(ConstantesErro.SAL_SALDO_INSUFICIENTE.getMensagem());
    }
  }

  public EfetuarPagamentoTituloResponse efetuarPagamentoTituloV5(
      LogPagtoTituloValidacao logPagtoValidacao,
      ContaPagamento contaUsuarioLogado,
      ContratoGatewayPagto contratoUtilizado,
      Long idUsuario,
      EfetuarPagamentoTitulo req,
      String cpf,
      GetSaldoConta saldoConta,
      boolean isPortador,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      ContaPagamento contaInstituicao,
      GetSaldoConta saldoContaInst,
      Integer idMoeda)
      throws IOException {

    logger.info("Iniciando Pagamento Titulo Celcoin");

    EfetuarPagamentoTituloResponse resposta = new EfetuarPagamentoTituloResponse();

    Date dtHrInicioPagto = new Date();
    validarValorPagto(logPagtoValidacao, req);

    BigDecimal valorAPagarCelcoin = getValorAPagar(logPagtoValidacao, req);

    BigDecimal valorAPagarJcard = getValorAPagar(logPagtoValidacao, req);

    Long codTransacaoConta = null;

    // Verifica se a conta é de PONTOS para fazer conversão de valor
    if (idMoeda.equals(999)) {
      valorAPagarJcard = valorAPagarJcard.multiply(new BigDecimal(100));
    }

    verificarSaldoSuficiente(saldoConta, valorAPagarJcard);

    // se o saldo estiver preenchido eh porque precisa validar o saldo da
    // instituicao
    if (saldoContaInst != null) {
      verificarSaldoSuficiente(saldoContaInst, valorAPagarJcard);
    }

    boolean lancamentoContaOk = false;
    boolean lancamentoInstOk = false;
    boolean lancamentoErro = false;
    String rrnInst = null;
    String rrnConta = null;
    String msgErroLancamentoInst = null;
    String msgErroLancamentoConta = null;

    // precisa fazer lancamento na conta da instituicao?
    if (saldoContaInst != null) {
      try {
        Long codTransacao = contratoInstituicao.getCodTransacaoInst();

        if (codTransacao == null || codTransacao.equals(0)) {
          throw new GenericServiceException(
              "CodTransacaoInst não configurado para o contrato da instituicao. idContrato: "
                  + contratoInstituicao.getIdContratoGatewayPagtoInstTransacaoConfig());
        }

        rrnInst =
            resgatePagamentoContaService.sensibilizarConta(
                contaInstituicao,
                valorAPagarJcard,
                contratoUtilizado,
                isPortador,
                idUsuario,
                req.getIp(),
                codTransacao,
                contratoInstituicao.getTextoExtrato()
                    + " : "
                    + logPagtoValidacao.getNomeBeneficiario());
        lancamentoInstOk = true;
      } catch (GenericServiceException e) {
        logger.warn(e.getMensagem(), e);
        lancamentoErro = true;
        msgErroLancamentoInst = e.getMensagem();
      } catch (Exception e) {
        lancamentoErro = true;
        logger.error(e.getMessage(), e);
      }
    }

    if (lancamentoInstOk || saldoContaInst == null) {
      try {
        Long codTransacao = contratoInstituicao.getCodTransacaoPortador();
        codTransacaoConta = codTransacao;

        if (codTransacao == null || codTransacao.equals(0)) {
          throw new GenericServiceException(
              "CodTransação não configurado para o contrato da instituicao. idContrato: "
                  + contratoInstituicao.getIdContratoGatewayPagtoInstTransacaoConfig());
        }
        String nomeBeneficiario =
            logPagtoValidacao.getNomeBeneficiario() == null
                ? logPagtoValidacao.getCedente()
                : logPagtoValidacao.getNomeBeneficiario();

        rrnConta =
            resgatePagamentoContaService.sensibilizarConta(
                contaUsuarioLogado,
                valorAPagarJcard,
                contratoUtilizado,
                isPortador,
                idUsuario,
                req.getIp(),
                codTransacao,
                contratoInstituicao.getTextoExtrato() + " : " + nomeBeneficiario);
        lancamentoContaOk = true;
      } catch (GenericServiceException e) {
        logger.warn(e.getMensagem(), e);
        lancamentoErro = true;
        msgErroLancamentoConta = e.getMensagem();
      } catch (Exception e) {
        lancamentoErro = true;
        logger.error(e.getMessage(), e);
      }
    }

    LogPagtoTituloTransacao logTransacao = new LogPagtoTituloTransacao();
    logTransacao.setDataHoraInicioPagto(dtHrInicioPagto);
    logTransacao.setIdContratoGatewayPagtoTituloInst(
        contratoInstituicao.getIdContratoGatewayPagto());
    logTransacao.setValor(valorAPagarCelcoin.doubleValue());
    logTransacao.setIdLogPagtoTitulo(logPagtoValidacao.getIdLogPagtoTitulo());
    logTransacao.setIdConta(contaUsuarioLogado.getIdConta());
    logTransacao.setIdUsuarioInclusao(idUsuario);
    logTransacao = logPagtoTransacaoRepository.save(logTransacao);

    PagamentoCelcoinResponse res = null;
    Date dtHrfimPagto = new Date();
    if (lancamentoContaOk) {
      res =
          efetuarPagamentoCelcoinV5(
              contratoUtilizado, logPagtoValidacao, req, cpf, valorAPagarCelcoin, logTransacao);
      if (res != null && res.getErrorCode().equals("000")) {
        logTransacao = fromRespostaContaToLogPagtoV5(res, logTransacao);

        LogPagtoTituloValidacao logValidacao = new LogPagtoTituloValidacao();
        logValidacao =
            logPagtoValidacaoRepository.findPagamentoTituloValidacaoByIdLogPag(
                logTransacao.getIdLogPagtoTitulo());
        TransacaoBoletoResponseVO transacaoBoletoResponseVO = new TransacaoBoletoResponseVO();
        transacaoBoletoResponseVO.setPagador(null);
        transacaoBoletoResponseVO.setDestinatario(logValidacao.getCedente());
        transacaoBoletoResponseVO.setValorTitulo(
            Util.currencyFormat(BigDecimal.valueOf(logTransacao.getValor())));
        int primeiroDigito =
            Integer.parseInt(logPagtoValidacao.getLinhaDigitavel().substring(0, 1));
        if (primeiroDigito == 8) {
          transacaoBoletoResponseVO.setValorCobrado(
              Util.currencyFormat(BigDecimal.valueOf(logTransacao.getValor())));
        } else {
          transacaoBoletoResponseVO.setValorCobrado(
              Util.currencyFormat(BigDecimal.valueOf(logTransacao.getValor())));
        }
        transacaoBoletoResponseVO.setAutenticacao(logTransacao.getAutenticacaoAPI());
        transacaoBoletoResponseVO.setProtocoloId(logTransacao.getProtocoloId().toString());
        transacaoBoletoResponseVO.setCodigoDeBarras(logPagtoValidacao.getLinhaDigitavel());
        transacaoBoletoResponseVO.setIdConta(logTransacao.getIdConta().toString());
        transacaoBoletoResponseVO.setDataOperacao(
            DateUtil.dateFormat(
                DateUtil.DD_MM_YYYY_HH_MM_SS, logTransacao.getDataHoraInicioPagto()));
        transacaoBoletoResponseVO.setDataLiquidacao(
            DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, logTransacao.getDataLiquidacao()));

        if (Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(
            contaUsuarioLogado.getIdInstituicao())) {
          transacaoBoletoResponseVO.setIdConta("RP3 BANK");
        }

        resposta.setBoleto(transacaoBoletoResponseVO);
      }
    }

    logger.info("+++++++++" + logTransacao);
    logTransacao.setDataHoraFimPagto(dtHrfimPagto);

    String codigoErro = res != null && res.getErrorCode() != null ? res.getErrorCode() : "-1";
    Integer codRetorno = Integer.valueOf(codigoErro);
    logTransacao.setResultadoPagto(codRetorno);
    logTransacao.setDescResultadoPagto(
        res != null && res.getMessage() != null ? res.getMessage() : null);

    // fez o lancamento ok na celcoin?
    if (codigoErro != null && codRetorno.equals(0)) {
      // ja confirma na celcoin, quer seja com sucesso ou cancelada
      try {
        Date dtInicioConf = new Date();
        CaptureResponse confirm = null;
        StatusConsultDto resStatus = null;
        TransacaoDTO dadosOperacao = null;

        // Confirmando por enquanto para seguir o fluxo de pagamento e confimação na
        // celcoin ate ajustarmos o novo fluxo com a celcoin
        if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(
            contaUsuarioLogado.getIdInstituicao())) {
          // if(req.getValor() <= VALOR_MIN_PAGAMENTO) {
          confirm = confirmarPagtoContaCelcoinPagtoV5(res, logPagtoValidacao);
          // }

        } else {
          confirm = confirmarPagtoContaCelcoinPagtoV5(res, logPagtoValidacao);
        }

        // Para impedir que um erro na API de status cause o desfazimento da transacao. O ideal eh
        // analisar as causa
        // ou a necessidade de uso desta API neste local
        try {
          resStatus = consultarStatusOperacaoV5(logTransacao, contratoInstituicao);

          if (resStatus != null) {
            dadosOperacao = resStatus.getTransaction();
          }
          logTransacao.setResultadoConfirm(dadosOperacao.getStatus());
          logTransacao.setDescResultadoConfirm(dadosOperacao.getMessage());
          logTransacao.setStatusConfirmacao(dadosOperacao.getMessage());

          Date dtFimConf = new Date();
          logTransacao.setDataHoraInicioConfirm(dtInicioConf);
          logTransacao.setDataHoraFimConfirm(dtFimConf);
          logTransacao.setIdTransacaoPagto(rrnConta);

          if (confirm != null && confirm.getMessage() != null) {
            logTransacao.setMensagemErro(confirm.getMessage());
          }

          if (!lancamentoContaOk) {
            throw new GenericServiceException(
                "Não foi possível confirmar com sucesso a transação! Desfazer as transações internas. ");
          }
          resposta.setSucesso(true);
          resposta.setComprovanteFormatado(logTransacao.getComprovanteFormatado());
          resposta.setAutenticacao(logTransacao.getAutenticacao());
          resposta.setCodigoRetorno(codRetorno);
          resposta.setDataLiquidacao(logTransacao.getDataLiquidacao());
          resposta.setDataOperacao(logTransacao.getDataOperacao());
          resposta.setProtocoloId(
              logTransacao.getProtocoloId() != null
                  ? logTransacao.getProtocoloId().toString()
                  : null);

          // Salvar o limite diario
          limitesContaService.salvarLimiteDiario(
              BigDecimal.valueOf(req.getValor()), contaUsuarioLogado.getIdConta());

        } catch (Exception e) {
          e.printStackTrace();
          logger.warn(e.getMessage(), e);
          logTransacao = logPagtoTransacaoRepository.save(logTransacao);
        }

      } catch (Exception e) {
        e.printStackTrace();
        logger.warn(e.getMessage(), e);
        String rrnDesf =
            desfazerTransacao(
                contaUsuarioLogado,
                valorAPagarJcard,
                isPortador,
                idUsuario,
                rrnConta,
                "EST. PAGAMENTO CONTA",
                codTransacaoConta);
        if (rrnInst != null) {
          String rrnDesfInst =
              desfazerTransacao(
                  contaUsuarioLogado,
                  valorAPagarJcard,
                  isPortador,
                  idUsuario,
                  rrnInst,
                  "EST. PAGAMENTO CONTA INST",
                  codTransacaoConta);
          logTransacao.setIdTransacaoDesfPagtoInst(rrnDesfInst);
        }
        logTransacao.setIdTransacaoDesfPagto(rrnDesf);
      }
    } else {
      // entra no if pra desfazer transação quando ja sensibilizou a conta
      if (!lancamentoErro) {
        String rrnDesf =
            desfazerTransacao(
                contaUsuarioLogado,
                valorAPagarJcard,
                isPortador,
                idUsuario,
                rrnConta,
                "EST. PAGAMENTO CONTA",
                codTransacaoConta);
        if (rrnInst != null) {
          String rrnDesfInst =
              desfazerTransacao(
                  contaUsuarioLogado,
                  valorAPagarJcard,
                  isPortador,
                  idUsuario,
                  rrnInst,
                  "EST. PAGAMENTO CONTA INST",
                  codTransacaoConta);
          logTransacao.setIdTransacaoDesfPagtoInst(rrnDesfInst);
        }
        logTransacao.setIdTransacaoDesfPagto(rrnDesf);
        resposta.setMensagemErro(logTransacao.getDescResultadoPagto());
        resposta.setSucesso(false);
      } else {
        // cai nesse fluxo quando teve erro no momento de sensibilizar a conta
        if (msgErroLancamentoConta != null && !msgErroLancamentoConta.isEmpty()) {
          logTransacao.setMensagemErro(msgErroLancamentoConta);
          resposta.setMensagemErro(msgErroLancamentoConta);
        } else if (msgErroLancamentoInst != null && !msgErroLancamentoInst.isEmpty()) {
          logTransacao.setMensagemErro(msgErroLancamentoInst);
          resposta.setMensagemErro(msgErroLancamentoInst);
        } else {
          logTransacao.setMensagemErro(null);
        }
        logTransacao.setStatusTransacao(Constantes.STATUS_ERRO);
        resposta.setSucesso(false);
        resposta.setCodigoRetorno(codRetorno);
      }
    }

    if (idMoeda.equals(999)) {

      try {
        if (lancamentoContaOk && lancamentoInstOk && !lancamentoErro) {
          List<PontoControle> listSaldoPontosControle =
              utilManejoPontos.buscaPontosControleComSaldo(contaUsuarioLogado.getIdConta());
          String finalRrn = rrnConta.substring(3);

          utilManejoPontos.atualizarResgateEmPontoControle(
              rrnConta,
              Constantes.SEM_TARIFA,
              valorAPagarJcard.longValueExact(),
              listSaldoPontosControle,
              Long.valueOf(finalRrn));
        }
      } catch (Exception e) {
        e.printStackTrace();
        logger.error("Falha ao sensebilizar safra de pontos.");
      }
    }

    logTransacao = logPagtoTransacaoRepository.save(logTransacao);

    return resposta;
  }

  private PagamentoCelcoinResponse efetuarPagamentoCelcoinV5(
      ContratoGatewayPagto contrato,
      LogPagtoTituloValidacao logPagto,
      EfetuarPagamentoTitulo req,
      String cpf,
      BigDecimal valorAPagar,
      LogPagtoTituloTransacao logTransacao)
      throws IOException {

    Pessoa pessoaConta = pessoaService.findPessoaTitularConta(logTransacao.getIdConta());
    PagamentoCelcoinDTO request = null;
    request =
        preencherRequestCelcoin(contrato, logPagto, req, valorAPagar, logTransacao, pessoaConta);
    request.setCpfcnpj(cpf);

    return celcoinService.doBillPayment(request);
  }

  private PagamentoCelcoinDTO preencherRequestCelcoin(
      ContratoGatewayPagto contrato,
      LogPagtoTituloValidacao logPagto,
      EfetuarPagamentoTitulo req,
      BigDecimal valorAPagar,
      LogPagtoTituloTransacao logTransacao,
      Pessoa pessoaConta) {

    PagamentoCelcoinDTO pagamentoCelcoinDTO = new PagamentoCelcoinDTO();
    BillDataDto billDataDto = new BillDataDto();
    CodigoBarrasDTO barCode = new CodigoBarrasDTO();
    InfoBearerDto infoBearerDto = new InfoBearerDto();
    billDataDto.setValue(valorAPagar.floatValue());
    billDataDto.setOriginalValue(
        logPagto.getValorNominal() == null
            ? Float.valueOf(0.00f)
            : logPagto.getValorNominal().floatValue());
    billDataDto.setValueWithDiscount(
        logPagto.getValorDescontoCalculado() == null
            ? Float.valueOf(0.00f)
            : logPagto.getValorDescontoCalculado().floatValue());
    billDataDto.setValueWithAdditional(
        logPagto.getValorTotalAcrescimo() == null
            ? Float.valueOf(0.00f)
            : logPagto.getValorTotalAcrescimo().floatValue());

    barCode.setBarCode(STRING_VAZIA);
    barCode.setDigitable(logPagto.getLinhaDigitavel());
    barCode.setType(
        logPagto.getTipoServico().equals("Concessionaria")
            ? 1
            : logPagto.getTipoServico().equals("FichaCompensacao") ? 2 : 0);

    infoBearerDto.setNameBearer(pessoaConta.getNomeCompleto());
    infoBearerDto.setDocumentBearer(pessoaConta.getDocumento());
    infoBearerDto.setMethodPaymentCode(Constantes.TIPO_DEBITO_EM_CONTA);

    pagamentoCelcoinDTO.setExternalNSU(logTransacao.getIdLogPagtoTituloTransacao().intValue());
    pagamentoCelcoinDTO.setExternalTerminal(logTransacao.getIdLogPagtoTituloTransacao().toString());
    pagamentoCelcoinDTO.setBillData(billDataDto);
    pagamentoCelcoinDTO.setBarCode(barCode);
    pagamentoCelcoinDTO.setInfoBearerDto(infoBearerDto);
    pagamentoCelcoinDTO.setDueDate(
        DateUtil.dateFormat(
            "yyyy-MM-dd HH:mm:ss",
            logPagto.getDataVencimento() == null
                ? logPagto.getDataLiquidacao()
                : logPagto.getDataVencimento()));
    pagamentoCelcoinDTO.setTransactionIdAuthorize(logPagto.getProtocoloIdConsulta());
    pagamentoCelcoinDTO.setUserType(
        logPagto.getTipoServico().equals("Concessionaria")
            ? 1
            : logPagto.getTipoServico().equals("FichaCompensacao") ? 2 : 0);
    pagamentoCelcoinDTO.setCorban(STRING_VAZIA);

    return pagamentoCelcoinDTO;
  }

  private LogPagtoTituloTransacao fromRespostaContaToLogPagtoV5(
      PagamentoCelcoinResponse resp, LogPagtoTituloTransacao logTran) {

    logTran.setAutenticacao(
        resp.getAuthentication() != null ? Long.valueOf(resp.getAuthentication()) : null);
    ReciboPagamentoCelcoin comprovante = resp.getReceipt();

    if (comprovante != null) {
      logTran.setComprovanteFormatado(comprovante.getReceiptformatted());
      logTran.setStatusTransacao(Constantes.STATUS_SUCESSO);
    }
    logTran.setDataLiquidacao(DateUtil.parseDate("yyyy-MM-dd", resp.getSettleDate()));
    logTran.setDataOperacao(DateUtil.parseDate("yyyy-MM-dd", resp.getCreateDate()));
    logTran.setProtocoloId(resp.getTransactionId());

    if (resp.getAuthenticationAPI() != null) {
      logger.info("PAGTO_CONTA++++++BLOCO1: " + resp.getAuthenticationAPI().get("Bloco1"));
      logger.info("PAGTO_CONTA++++++BLOCO2: " + resp.getAuthenticationAPI().get("Bloco2"));
      logger.info(
          "PAGTO_CONTA++++++BLOCOCompleto: " + resp.getAuthenticationAPI().get("BlocoCompleto"));

      try {
        ObjectMapper mapper = new ObjectMapper();

        String autenticacaoAPIJSON = mapper.writeValueAsString(resp.getAuthenticationAPI());
        logTran.setAutenticacaoAPI(autenticacaoAPIJSON);
      } catch (Exception e) {
        logger.warn("Não foi possivel transformar objeto em json PAGTO_CELCOIN: ", e);
      }
    }

    return logTran;
  }

  private CaptureResponse confirmarPagtoContaCelcoinPagtoV5(
      PagamentoCelcoinResponse res, LogPagtoTituloValidacao logPagtoValidacao) {
    CapturaTransacaoCelcoinDTO request = new CapturaTransacaoCelcoinDTO();
    request.setTransactionId(res.getTransactionId());
    request.setExternalnsu(logPagtoValidacao.getIdLogPagtoTitulo().intValue());
    request.setExternalTerminal((logPagtoValidacao.getIdLogPagtoTitulo().toString()));

    return celcoinService.captureTransaction(request);
  }

  public StatusConsultDto consultarStatusOperacaoV5(
      LogPagtoTituloTransacao logTransacao,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao) {
    ConsultDto request = null;

    request = montarRequestConsultaDetalhesTransacao(logTransacao);
    return celcoinService.doConsultTransactionStatusByTransactionId(request);
  }

  private ConsultDto montarRequestConsultaDetalhesTransacao(LogPagtoTituloTransacao logTransacao) {
    ConsultDto request = new ConsultDto();

    request.setExternalnsu(logTransacao.getIdLogPagtoTitulo().intValue());
    request.setExternalTerminal(logTransacao.getIdLogPagtoTitulo().toString());
    request.setOperationDate(STRING_VAZIA);
    request.setTransactionId(logTransacao.getProtocoloId());

    return request;
  }

  private LogRecargaTransacao fromRespostaRecargaToLogRecargaV5(
      EfetuarRecargaResponse resp, LogRecargaTransacao logTran) {
    BeanUtils.copyProperties(resp, logTran, Util.getNullPropertyNames(resp));
    logTran.setAutenticacao(resp.getAutenticacao() != null ? resp.getAutenticacao() : null);

    if (resp.getComprovanteFormatado() != null) {
      logTran.setComprovanteFormatado(resp.getComprovanteFormatado());
      logTran.setDataOperacao(new Date());
      logTran.setStatusTransacao("SUCESSO");
    }
    if (resp.getAuthenticationApi() != null) {
      logger.info("RECARGA++++++BLOCO1: {}", resp.getAuthenticationApi().getBloco1());
      logger.info("RECARGA++++++BLOCO2: {}", resp.getAuthenticationApi().getBloco2());
      logger.info("RECARGA++++++BLOCOCompleto: {}", resp.getAuthenticationApi().getBlocoCompleto());

      try {
        ObjectMapper mapper = new ObjectMapper();

        String autenticacaoAPIJSON = mapper.writeValueAsString(resp.getAuthenticationApi());
        logTran.setAutenticacaoAPI(autenticacaoAPIJSON);
      } catch (Exception e) {
        logger.warn("Não foi possivel transformar objeto em json RECARGA_CELCOIN: ", e);
      }
    }
    logTran.setProtocoloId(
        resp.getProtocoloId() != null ? Long.valueOf(resp.getProtocoloId()) : null);
    return logTran;
  }

  public ConsultarOperadoraPorDDDResponse consultarOperadoraPorDDD(Integer ddd, Boolean voucher) {

    StatusProvidersDto statusProvidersDto = celcoinService.getProvidersByStateCode(ddd);

    List<ShowProvidersDto> newProviders = new ArrayList<>();
    if (!voucher) {
      for (ShowProvidersDto temp : statusProvidersDto.getProviders()) {
        if (temp.getCategory() == 1) {
          newProviders.add(temp);
        }
      }
    } else {
      for (ShowProvidersDto temp : statusProvidersDto.getProviders()) {
        if (temp.getCategory() != 1
            && temp.getProviderId() != 2142
            && temp.getProviderId() != 2107) {
          newProviders.add(temp);
        }
      }
    }

    if (newProviders.isEmpty()) {
      throw new GenericServiceException("DDD inválido,tente novamente!");
    }
    ConsultarOperadoraPorDDDResponse response = new ConsultarOperadoraPorDDDResponse();
    List<OperadoraRecarga> ops = new ArrayList<>();
    for (ShowProvidersDto providersResult : newProviders) {
      OperadoraRecarga opRecarga = fromShowProvidersDtoToOperadoraRecargaV5(providersResult);
      ops.add(opRecarga);
    }
    response.setOperadoras(ops);
    response.setCodigoRetorno(COD_SUCESSO);
    response.setSucesso(true);
    return response;
  }

  private OperadoraRecarga fromShowProvidersDtoToOperadoraRecargaV5(ShowProvidersDto providers) {
    OperadoraRecarga opRec = new OperadoraRecarga();
    opRec.setCategoriaOperadora(providers.getCategory().toString());
    opRec.setNome(providers.getName());
    opRec.setOperadoraId((long) providers.getProviderId());
    opRec.setTipoRecargaOperadora(providers.getTipoRecargaNameProvider().toString());
    opRec.setValorMaximo((double) providers.getMaxValue());
    opRec.setValorMinimo((double) providers.getMinValue());
    return opRec;
  }

  public ConsultarValorOperadoraResponse consultarValorOperadoraV5(
      Integer ddd, Integer operadoraId) {
    ConsultarValorOperadoraResponse response = new ConsultarValorOperadoraResponse();

    StatusProviderValuesDto statusProviderValuesDto =
        celcoinService.getProvidersValuesByStateCodeAndProviderId(ddd, operadoraId);

    List<CargaValor> valoresRecarga = new ArrayList<>();

    for (ValueDto opVal : statusProviderValuesDto.getValue()) {
      CargaValor cv = fromOperadoraValorToCargaValorV5(opVal);
      valoresRecarga.add(cv);
    }
    valoresRecarga.sort(Comparator.comparing(CargaValor::getValor));

    response.setCodigoRetorno(COD_SUCESSO);
    response.setValoresRecarga(valoresRecarga);
    response.setSucesso(true);
    return response;
  }

  private CargaValor fromOperadoraValorToCargaValorV5(ValueDto opVal) {
    CargaValor cv = new CargaValor();

    cv.setNomeProduto(opVal.getProductName());
    cv.setValor(opVal.getMaxValue());
    cv.setDetalhes(opVal.getDetail());
    return cv;
  }

  public EfetuarRecargaResponse efetuarRecargaV5(
      EfetuarRecargaRequest req,
      ContaPagamento contaUsuarioLogado,
      ContratoGatewayPagto contratoUtilizado,
      Long idUsuario,
      String cpf,
      GetSaldoConta saldoConta,
      boolean isPortador,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      ContaPagamento contaInstituicao,
      GetSaldoConta saldoContaInst,
      Integer categoria,
      Integer idMoeda) {

    // coisas temporarias para responder ok ou erro dinamicamente
    EfetuarRecargaResponse resposta = new EfetuarRecargaResponse();
    Date dtHrInicioPagto = new Date();
    LogRecargaTransacao logTransacao = new LogRecargaTransacao();
    logTransacao.setDataHoraInicioPagto(dtHrInicioPagto);
    logTransacao.setIdConta(contaUsuarioLogado.getIdConta());
    logTransacao.setIdCategoria(categoria);
    logTransacao.setIdUsuarioInclusao(idUsuario);

    logTransacao.setDdd(req.getDdd());
    logTransacao.setOperadoraId(req.getOperadoraId() != null ? req.getOperadoraId() : null);
    logTransacao.setNumero(req.getNumero());

    BigDecimal valorAPagarCelcoin = new BigDecimal(req.getValor().toString());

    BigDecimal valorAPagarJcard = new BigDecimal(req.getValor().toString());

    Long codTransacaoConta = null;

    // Verifica se a conta é de PONTOS para fazer conversão de valor
    if (idMoeda.equals(999)) {
      valorAPagarJcard = valorAPagarJcard.multiply(new BigDecimal(100));
    }

    verificarSaldoSuficiente(saldoConta, valorAPagarJcard);
    logTransacao.setValor(valorAPagarCelcoin.doubleValue());

    // operadoraId reconhecida pelo gateway de pagamento (Celcoin)
    req.setOperadoraId(req.getOperadoraId());

    logTransacao = logRecargaTransacaoRepository.save(logTransacao);

    EfetuarRecargaResponse res = efetuarCargaCelcoinV5(req, cpf, valorAPagarCelcoin, logTransacao);
    Date dtHrfimPagto = new Date();

    logTransacao = fromRespostaRecargaToLogRecargaV5(res, logTransacao);

    logger.info("+++++++++" + logTransacao);
    logTransacao.setDataHoraFimPagto(dtHrfimPagto);

    logTransacao = logRecargaTransacaoRepository.save(logTransacao);

    // se o pagamento foi feito com sucesso do lado da celcoin
    // entao eh para sensibilizar a conta da pessoa
    String codigoErro = res.getCodigoErro() == null ? "-1" : res.getCodigoErro();
    Integer codRetorno = Integer.valueOf(codigoErro);

    // fez o lancamento ok na celcoin?
    if (codigoErro != null
        && codRetorno.equals(0)
        && (res.getMensagemErro() == null || res.getMensagemErro().isEmpty())) {

      boolean lancamentoContaOk = false;
      String rrnConta = null;
      boolean lancamentoInstOk = false;
      String rrnInst = null;

      String msgErroLancamentoInst = null;
      String msgErroLancamentoConta = null;

      // precisa fazer lancamento na conta da instituicao?
      if (saldoContaInst != null && contratoInstituicao.getDebitarGarantiaInst()) {
        try {
          Long codTransacao = contratoInstituicao.getCodTransacaoInst();

          if (codTransacao == null || codTransacao.equals(0)) {
            throw new GenericServiceException(
                "CodTransacaoInst não configurado para o contrato da instituicao. idContrato: "
                    + contratoInstituicao.getIdContratoGatewayPagtoInstTransacaoConfig());
          }

          rrnInst =
              sensibilizarConta(
                  contaInstituicao,
                  valorAPagarJcard,
                  contratoUtilizado,
                  isPortador,
                  idUsuario,
                  req.getIp(),
                  codTransacao,
                  contratoInstituicao.getTextoExtrato()
                      + " : "
                      + req.getOperadora()
                      + "-"
                      + req.getNumero());
          lancamentoInstOk = true;
          logTransacao.setIdTransacaoPagtoInst(rrnInst);
        } catch (GenericServiceException e) {
          logger.warn(e.getMensagem(), e);
          msgErroLancamentoInst = e.getMensagem();
        } catch (Exception e) {
          logger.error(e.getMessage(), e);
        }
      }

      // se foi feito o lancamento de debito na instittuicao ou entao nao era pra fazer entao
      // sensibilizar a conta
      if (lancamentoInstOk || saldoContaInst == null) {
        try {
          Long codTransacao = contratoInstituicao.getCodTransacaoPortador();
          codTransacaoConta = codTransacao;

          if (codTransacao == null || codTransacao.equals(0)) {
            throw new GenericServiceException(
                "CodTransação não configurado para o contrato da instituicao. idContrato: "
                    + contratoInstituicao.getIdContratoGatewayPagtoInstTransacaoConfig());
          }

          rrnConta =
              sensibilizarConta(
                  contaUsuarioLogado,
                  valorAPagarJcard,
                  contratoUtilizado,
                  isPortador,
                  idUsuario,
                  req.getIp(),
                  codTransacao,
                  contratoInstituicao.getTextoExtrato()
                      + " : "
                      + req.getOperadora()
                      + "-"
                      + req.getNumero());

          lancamentoContaOk = true;

          // Salvar o limite diario
          limitesContaService.salvarLimiteDiario(
              BigDecimal.valueOf(req.getValor()), contaUsuarioLogado.getIdConta());
        } catch (GenericServiceException e) {
          logger.warn(e.getMensagem(), e);
          msgErroLancamentoConta = e.getMensagem();
        } catch (Exception e) {
          logger.error(e.getMessage(), e);
        }
      }

      try {

        Date dtInicioConf = new Date();
        confirmarPagtoContaCelcoinRecargaV5(res);
        Date dtFimConf = new Date();
        String resultConf = lancamentoContaOk ? "SUCESSO" : "CANCELADA";
        logTransacao.setStatusConfirmacao(resultConf);
        logTransacao.setDataHoraInicioConfirm(dtInicioConf);
        logTransacao.setDataHoraFimConfirm(dtFimConf);
        logTransacao.setIdTransacaoPagto(rrnConta);
        logTransacao.setNumero(req.getNumero());

        if (!lancamentoContaOk) {
          throw new GenericServiceException(
              "Não foi possível efetuar o pagamento. Tente novamente mais tarde!");
        }
        res.setSucesso(true);
        resposta.setSucesso(true);
        resposta.setComprovanteFormatado(logTransacao.getComprovanteFormatado());
        resposta.setAutenticacao(logTransacao.getAutenticacao());
        resposta.setCodigoRetorno(codRetorno);
        resposta.setDataLiquidacao(logTransacao.getDataLiquidacao());
        resposta.setDataOperacao(logTransacao.getDataOperacao());
        resposta.setProtocoloId(
            logTransacao.getProtocoloId() != null
                ? logTransacao.getProtocoloId().toString()
                : null);

      } catch (Exception e) {
        //				e.printStackTrace();
        logger.warn(e.getMessage(), e);
        String rrnDesf =
            desfazerTransacao(
                contaUsuarioLogado,
                valorAPagarJcard,
                isPortador,
                idUsuario,
                rrnConta,
                "EST. PAGAMENTO CONTA",
                codTransacaoConta);
        logTransacao.setIdTransacaoDesfPagto(rrnDesf);

        if (rrnInst != null) {
          String rrnDesfInst =
              desfazerTransacao(
                  contaUsuarioLogado,
                  valorAPagarJcard,
                  isPortador,
                  idUsuario,
                  rrnInst,
                  "EST. PAGAMENTO CONTA INST",
                  codTransacaoConta);
          logTransacao.setIdTransacaoDesfPagtoInst(rrnDesfInst);
        }
      }

      if (idMoeda.equals(999)) {
        cashbackLogService.saveLogTransacoesLoyalty(
            null,
            contaUsuarioLogado.getIdConta(),
            null,
            contaUsuarioLogado.getProdutoInstituicao().getIdProdInstituicao() == null
                ? 0L
                : contaUsuarioLogado.getProdutoInstituicao().getIdProdInstituicao().longValue(),
            rrnConta,
            codTransacaoConta.intValue(),
            valorAPagarJcard,
            null,
            null,
            null,
            null,
            "Recarga de celular",
            null);
      }

      logTransacao = logRecargaTransacaoRepository.save(logTransacao);

      //			BeanUtils.copyProperties(logTransacao, resposta,Util.getNullPropertyNames(logTransacao));

      // se nao conseguiu lancar a transacao na celcoin, só retorna o codigo e mensagem de erro
    } else {
      // logTransacao;
      BeanUtils.copyProperties(logTransacao, resposta, Util.getNullPropertyNames(logTransacao));
      resposta.setCodigoRetorno(codRetorno);
    }
    return res;
  }

  private EfetuarRecargaResponse efetuarCargaCelcoinV5(
      EfetuarRecargaRequest req,
      String cpf,
      BigDecimal valorAPagar,
      LogRecargaTransacao logTransacao) {
    EfetuarRecargaResponse responseRecarga = new EfetuarRecargaResponse();

    RequestRecargaDto request = new RequestRecargaDto();
    RecargaDataDto valorRecarga = new RecargaDataDto();
    RecargaPhoneDto phoneRecarga = new RecargaPhoneDto();
    valorRecarga.setValue(valorAPagar.floatValue());
    phoneRecarga.setCountryCode(COD_BRASIL);
    phoneRecarga.setStateCode(req.getDdd());
    phoneRecarga.setNumber(req.getNumero().intValue());
    request.setTopupData(valorRecarga);
    request.setPhone(phoneRecarga);
    request.setCpfCnpj(cpf);
    request.setExternalNsu(logTransacao.getIdLogRecargaTransacao().intValue());
    request.setExternalTerminal(req.getIp());
    request.setSignerCode(TIPO_TRAN_RECARGA);
    request.setProviderId(req.getOperadoraId().intValue());

    RecargaResponse recargaResponse = celcoinService.efetuarRecarga(request);

    responseRecarga.setAutenticacao(recargaResponse.getAuthentication().longValue());
    responseRecarga.setNsu(recargaResponse.getNsuNameProvider());
    responseRecarga.setComprovanteFormatado(recargaResponse.getReceipt().getReceiptformatted());
    responseRecarga.setDataLiquidacao(recargaResponse.getSettleDate());
    responseRecarga.setDataOperacao(recargaResponse.getCreateDate());
    responseRecarga.setIdTransacaoInterna(recargaResponse.getTransactionId());
    responseRecarga.setMensagemErro(recargaResponse.getMessage());
    responseRecarga.setProtocoloId(recargaResponse.getTransactionId().toString());
    responseRecarga.setSucesso(
        recargaResponse.getStatus() == VALOR_PARA_CODIGO_SUCESSO_RETORNADO_API_RECARGA
            ? true
            : false);
    responseRecarga.setCodigoErro(recargaResponse.getErrorCode());
    responseRecarga.setAuthenticationApi(recargaResponse.getAuthenticationApi());
    responseRecarga.setStatusTransacao(recargaResponse.getMessage());

    return responseRecarga;
  }

  private String getValorParametro(Integer idProcessadora, Integer idInstituicao, String param) {
    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(param);

    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(idProcessadora);
    valor.setIdInstituicao(idInstituicao);

    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);
    validaParams(params, param);
    return params.get(POSICAO_ZERO).getValorParametro();
  }

  private void validaParams(List<ParametroValor> params, String nomeParam) {
    if (params == null || params.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: " + nomeParam);
    }
  }

  public List<LogPagtoTituloTransacao> consultarPagamentosConta(
      Long idConta, Date dataInicio, Date dataFim) {
    dataFim = DateUtil.dataComUltimaHora(dataFim);
    dataInicio = DateUtil.dataComPrimeiraHora(dataInicio);

    return logPagtoTransacaoRepository.findPagamentosByIdConta(idConta, dataInicio, dataFim);
  }

  public List<LogRecargaTransacao> consultarRecargasConta(
      Long idConta, Date dataInicio, Date dataFim) {
    dataFim = DateUtil.dataComUltimaHora(dataFim);
    dataInicio = DateUtil.dataComPrimeiraHora(dataInicio);
    return logRecargaTransacaoRepository.findRecargasByIdConta(idConta, dataInicio, dataFim);
  }

  public LogPagtoTituloTransacao consultarPagamentoByIdTransacao(
      Long idConta, String idTransacaoPagto) {
    return logPagtoTransacaoRepository.findPagamentosByIdContaAndTransacao(
        idConta, idTransacaoPagto);
  }

  public LogRecargaTransacao consultarRecargasContaByCodigoTransacao(
      Long idConta, String idTransacaoPagto) {
    return logRecargaTransacaoRepository.findRecargaByIdContaAndTransacao(
        idConta, idTransacaoPagto);
  }

  public Long buscaQuantidadeDeVoucher(Long idConta, Integer diasAtras) {
    return logRecargaTransacaoRepository.buscaQuantidadeVoucher(idConta, diasAtras);
  }

  public String getComprovanteRecargaByUserAndIdTransacao(
      SecurityUserPortador user, String idTransacao) {
    return logRecargaTransacaoRepository.findComprovanteByIdsContasAndIdTransacao(
        user.getContasPortador(), idTransacao);
  }

  public String getComprovantePagamentoByUserAndIdTransacao(
      SecurityUserPortador user, String idTransacao) {
    return logPagtoTransacaoRepository.findComprovanteByIdsContasAndIdTransacao(
        user.getContasPortador(), idTransacao);
  }

  public String getComprovanteRecargaByIdTransacao(String idTransacao) {
    return logRecargaTransacaoRepository.findComprovanteByIdTransacaoAndIsStatusSucesso(
        idTransacao);
  }

  public String getComprovantePagamentoByIdTransacao(String idTransacao) {
    return logPagtoTransacaoRepository.findComprovanteByIdTransacaoAndIsStatusSucesso(idTransacao);
  }

  public <E> E findByHierarquiaOrderByInclusao(ResgateLoyaltyRequestVO filtroVo, Class<E> clazz) {
    return repository.findByHierarquiaOrderByInclusao(filtroVo, clazz);
  }

  public ResgatePagamentoConta findByRrn(String rrn) {
    return repository.findOneByRrn(rrn);
  }

  public List<Bank> resgatarBancosCelcoin() {
    return celcoinService.getBanks().getBanks();
  }

  /**
   * Aponta para API de acordo com o tipo de segmento da linha digitavel Sendo APIs do Rendimento
   * para Titulo (e-commerce), Tributo, Consumo
   *
   * @param linhaDigitavel
   * @param contratoUtilizado
   * @param logPagtoValidacao
   * @param req
   * @param cpf
   * @param nome
   * @param tipoPessoa
   * @param valorAPagarRendimento
   * @param contratoInstituicao
   * @param logPagtoTituloTransacao
   * @return
   * @throws IOException
   */
  public PagamentoTituloRendimentoResponse chamarServicoRendimentoDeAcordoComSegmento(
      String linhaDigitavel,
      ContratoGatewayPagto contratoUtilizado,
      LogPagtoTituloValidacao logPagtoValidacao,
      EfetuarPagamentoTitulo req,
      String cpf,
      String nome,
      Integer tipoPessoa,
      BigDecimal valorAPagarRendimento,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      LogPagtoTituloTransacao logPagtoTituloTransacao)
      throws IOException {

    PagamentoTituloRendimentoResponse res = new PagamentoTituloRendimentoResponse();
    Convenio convenio = new Convenio();
    ConvenioTipoSegmento convenioTipoSegmento = new ConvenioTipoSegmento();
    Integer digitoSegmento = null;
    String digitoConvenio = "";
    boolean isFgts = false;
    boolean isDarf = false;

    try {
      digitoSegmento = Integer.parseInt(linhaDigitavel.substring(1, 2));
      int primeiroDigito = Integer.parseInt(linhaDigitavel.substring(0, 1));

      if (linhaDigitavel.length() <= 44) {
        digitoConvenio = linhaDigitavel.substring(15, 19);
      } else {
        digitoConvenio = linhaDigitavel.substring(16, 20);
      }

      convenioTipoSegmento = convenioTipoSegmentoRepository.findById(digitoSegmento).orElse(null);
      convenio =
          convenioRepository.findByAtivoAndCodigoConvenioAndConvenioTipoSegmento(
              true, digitoConvenio, convenioTipoSegmento);

      if (primeiroDigito == 8) {
        if (convenio != null) {
          if (convenio.getCodigoConvenio().equals(digitoConvenio)) {
            if (convenio.getSiglaConvenio().equals("FGTS")) {
              isFgts = true;
            } else if (convenio.getSiglaConvenio().equals("DARF")) {
              isDarf = true;
            }
          }
        } else {
          throw new GenericServiceException(
              ConstantesErro.CONV_NAO_EXISTE_CONVENIO_PARA_INSTITUICAO.getMensagem());
        }
      } else {
        res =
            efetuarPagamentoTituloRendimento(
                logPagtoValidacao, cpf, nome, tipoPessoa, valorAPagarRendimento);

        return res;
      }
    } catch (NumberFormatException e) {
      throw new GenericServiceException(ConstantesErro.SEG_DIGITO_NAO_ENCONTRADO.getMensagem());
    } catch (Exception e) {
      logger.error(ConstantesErro.SEG_CONV_ERRO_GERAL_NAO_ESPECIFICADO.getMensagem(), e);
    }

    try {
      if (convenio != null) {
        Integer primeiroDigito = null;
        primeiroDigito = Integer.parseInt(linhaDigitavel.substring(0, 1));

        if (primeiroDigito == 8) {
          switch (Objects.requireNonNull(findTypeById(digitoSegmento))) {
            case ORGAOS_GOVERNAMENTAIS:
            case PREFEITURA:
            case CARNES_OUTROS_ORGAOS:
            case MULTA_DE_TRANSITO:
            case USO_EXCLUSIVO_DO_BANCO:
              //							Chamar API TRIBUTOS
              res =
                  efetuarPagamentoTributoRendimento(
                      contratoUtilizado,
                      logPagtoValidacao,
                      req,
                      cpf,
                      nome,
                      tipoPessoa,
                      valorAPagarRendimento,
                      logPagtoTituloTransacao,
                      isDarf,
                      isFgts);
              break;
            case SANEAMENTO:
            case ENERGIA_ELETRICA_E_GAS:
            case TELECOMUNICACOES:
              //							PAGAMENTO CONSUMO
              res =
                  efetuarPagamentoRendimentoV3(
                      contratoUtilizado,
                      logPagtoValidacao,
                      req,
                      cpf,
                      nome,
                      tipoPessoa,
                      valorAPagarRendimento,
                      contratoInstituicao,
                      logPagtoTituloTransacao);
              break;
            default:
              throw new GenericServiceException(
                  ConstantesErro.SEG_SEGMENTO_INVALIDO.getMensagem(),
                  HttpStatus.UNPROCESSABLE_ENTITY);
          }
        } else {
          //							Chamar API de TITULOS
          res =
              efetuarPagamentoTituloRendimento(
                  logPagtoValidacao, cpf, nome, tipoPessoa, valorAPagarRendimento);
        }

      } else {
        res =
            efetuarPagamentoTituloRendimento(
                logPagtoValidacao, cpf, nome, tipoPessoa, valorAPagarRendimento);
      }
    } catch (Exception e) {
      logger.info(ConstantesErro.BOL_ERRO_AO_REALIZAR_PAG.getMensagem(), e);
    }
    return res;
  }

  public String consultarConveniosBoleto(String numeroBoleto) {
    if (numeroBoleto == null || numeroBoleto.isEmpty()) {
      throw new GenericServiceException(
          ConstantesErro.BOL_NUM_BOLETO_NAO_INFORMADO.getMensagem(),
          HttpStatus.UNPROCESSABLE_ENTITY);
    }
    String digitoConvenio;
    String convenioStr = "";
    int digitoSegmento = Integer.parseInt(numeroBoleto.substring(1, 2));
    int primeiroDigito = Integer.parseInt(numeroBoleto.substring(0, 1));

    ConvenioTipoSegmento convenioTipoSegmento;
    Convenio convenio;

    if (numeroBoleto.length() <= 44) {
      digitoConvenio = numeroBoleto.substring(15, 19);
    } else {
      digitoConvenio = numeroBoleto.substring(16, 20);
    }

    try {
      convenioTipoSegmento = convenioTipoSegmentoRepository.findById(digitoSegmento).orElse(null);
      convenio =
          convenioRepository.findByAtivoAndCodigoConvenioAndConvenioTipoSegmento(
              true, digitoConvenio, convenioTipoSegmento);

      if (primeiroDigito == 8) {
        if (convenio != null) {
          if (convenio.getCodigoConvenio().equals(digitoConvenio)) {
            if (FGTS.equals(convenio.getSiglaConvenio())
                || DARF.equals(convenio.getSiglaConvenio())) {
              convenioStr = convenio.getSiglaConvenio();
            }
          } else {
            throw new GenericServiceException(
                ConstantesErro.CONV_NAO_EXISTE_CONVENIO_PARA_INSTITUICAO.getMensagem(),
                HttpStatus.UNPROCESSABLE_ENTITY);
          }
        } else {
          throw new GenericServiceException(
              ConstantesErro.CONV_NAO_ENCONTRADO.getMensagem(), HttpStatus.NOT_FOUND);
        }
      }
    } catch (GenericServiceException e) {
      logger.error(e.getMensagem(), e);
      throw new GenericServiceException(e.getMensagem(), e.getHttpStatus());
    }
    return convenioStr;
  }

  public String validarConsultaContratoAtivo(
      Integer tipoTransacao, SecurityUserPortador userPortador) {
    ContaPagamento conta = new ContaPagamento();
    conta.setIdInstituicao(userPortador.getIdInstituicao());
    conta.setIdProcessadora(userPortador.getIdProcessadora());
    return this.consultarContratoAtivo(tipoTransacao, conta);
  }

  public String validarConsultaContratoAtivo(
      Integer tipoTransacao, SecurityUserCorporativo userCorporativo) {
    ContaPagamento conta = new ContaPagamento();
    conta.setIdInstituicao(userCorporativo.getIdInstituicao());
    conta.setIdProcessadora(userCorporativo.getIdProcessadora());
    return this.consultarContratoAtivo(tipoTransacao, conta);
  }

  private String consultarContratoAtivo(Integer tipoTransacao, ContaPagamento conta) {

    String contrato = "";

    try {

      TipoOperacaoEnum tipoOperacaoEnum = this.pegarTipoOperacao(tipoTransacao);

      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao =
          this.gatewayPagtoFacade.getContratoAtivoPorTipoTransacao(conta, tipoOperacaoEnum);
      ContratoGatewayPagto contratoUtilizado =
          this.gatewayPagtoFacade.getContratoUtilizado(contratoInstituicao);

      if (contratoUtilizado.getTipoGatewayPagto() == 2) {
        contrato = "Rendimento";
      } else {
        contrato = "Celcoin";
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
      throw new GenericServiceException("Não foi possível buscar contrato ativo.");
    }

    return contrato;
  }

  public TipoOperacaoEnum pegarTipoOperacao(Integer tipoTransacao) {

    TipoOperacaoEnum tipoOperacaoEnum = null;

    switch (tipoTransacao) {
      case 1:
        tipoOperacaoEnum = TipoOperacaoEnum.PAGAMENTO;
        break;
      case 2:
        tipoOperacaoEnum = TipoOperacaoEnum.RECARGA_OPERADORA;
        break;
      case 3:
        tipoOperacaoEnum = TipoOperacaoEnum.VOUCHER;
        break;
      case 4:
        tipoOperacaoEnum = TipoOperacaoEnum.TED;
        break;
    }
    return tipoOperacaoEnum;
  }

  public PagamentoConsultaStatusRendimentoProcResponse
      consultarStatusPagamentoAndConfirmarRendimento(PagamentoVO pagamento) throws IOException {

    ContaPagamento contaPagamento = contaPagamentoService.findByIdNotNull(pagamento.getIdConta());

    PagamentoConsultaStatusRendimentoResponse pagamentoConsultaStatusRendimentoResponse =
        new PagamentoConsultaStatusRendimentoResponse();
    PagamentoConsultaStatusRendimentoResponseV2 pagamentoConsultaStatusRendimentoResponseV2 =
        new PagamentoConsultaStatusRendimentoResponseV2();
    if (pagamento.getTipoBoleto().equals(TipoBoletoEnum.TITULO)) {
      pagamentoConsultaStatusRendimentoResponseV2 = consultarStatusPagamentoV2(pagamento);
    } else {
      pagamentoConsultaStatusRendimentoResponse = consultarStatusPagamento(pagamento);
    }

    return alterarStatusAndGeraComprovantePagamento(
        pagamentoConsultaStatusRendimentoResponse,
        pagamentoConsultaStatusRendimentoResponseV2,
        pagamento,
        contaPagamento);
  }

  public PagamentoConsultaStatusRendimentoProcResponse alterarStatusAndGeraComprovantePagamento(
      PagamentoConsultaStatusRendimentoResponse pagamentoConsultaStatusRendimentoResponse,
      PagamentoConsultaStatusRendimentoResponseV2 pagamentoConsultaStatusRendimentoResponseV2,
      PagamentoVO pagamento,
      ContaPagamento contaPagamento) {

    LogPagtoTituloTransacao logPagtoTituloTransacao = new LogPagtoTituloTransacao();
    PagamentoConsultaStatusRendimentoProcResponse procResponse =
        new PagamentoConsultaStatusRendimentoProcResponse();

    if ((Objects.nonNull(pagamentoConsultaStatusRendimentoResponse.getValue())
            && pagamentoConsultaStatusRendimentoResponse.getIsSuccess())
        || (Objects.nonNull(pagamentoConsultaStatusRendimentoResponseV2.getData())
            && pagamentoConsultaStatusRendimentoResponseV2.getSuccess())) {
      LogTransacoes logTransacoes = new LogTransacoes();
      ConsultaPagamentoValue consultaPagamentoValue = new ConsultaPagamentoValue();
      JcardResponse jcardResponse = new JcardResponse();
      consultaPagamentoValue =
          pagamento.getTipoBoleto().equals(TipoBoletoEnum.TITULO)
              ? pagamentoConsultaStatusRendimentoResponseV2.getData().get(0)
              : pagamentoConsultaStatusRendimentoResponse.getValue();
      String status = consultaPagamentoValue.getStatus();
      String motivoRecusaRend;

      status = status.toLowerCase();

      if (pagamento.getTipoBoleto().equals(TipoBoletoEnum.TITULO)) {
        motivoRecusaRend =
            consultaPagamentoValue.getMotivo() != null
                ? consultaPagamentoValue.getMotivo()
                : "Motivo não informado pelo rendimento";

        status = status.replace(" ", "");

        logPagtoTituloTransacao =
            logPagtoTransacaoRepository.findOneByRrn(pagamento.getIdTransacaoPgto());
        ProdutoInstituicaoConfiguracao produto =
            lancamentoService.getProduto(
                contaPagamento.getIdProcessadora(),
                contaPagamento.getIdInstituicao(),
                contaPagamento.getIdProdutoInstituicao());

        logger.info("Status Pagamento Rendimento: " + consultaPagamentoValue.getStatus());

        if (status.equals(StatusPagamentoRendimentoEnum.SUCESSO.getStatus().toLowerCase())) {
          procResponse.setIsSuccess(true);
          procResponse.setIsFailure(false);
          if (logPagtoTituloTransacao
              .getStatusPagamento()
              .equals(StatusTransacao.EM_EFETIVACAO.getCodigo())) {
            logPagtoTituloTransacao.setStatusPagamento(StatusTransacao.TRANSFERIDO.getCodigo());
            logPagtoTituloTransacao.setDataHoraStatus(new Date());
            logPagtoTituloTransacao.setDataLiquidacao(
                consultaPagamentoValue.getDataEfetivacao() != null
                    ? DateUtil.parseDate("yyyy-MM-dd", consultaPagamentoValue.getDataEfetivacao())
                    : DateUtil.parseDate("yyyy-MM-dd", consultaPagamentoValue.getDataPagamento()));
            logPagtoTituloTransacao.setStatusConfirmacao("Sucesso");
            procResponse.setMessage("Pagamento foi efetuado com sucesso!");
          } else {
            procResponse.setMessage("Pagamento foi efetuado com sucesso!");
            logger.info("Boleto já estava APROVADO e o status se manteve.");
          }
        } else if (status.equals(StatusPagamentoRendimentoEnum.REJEITADO.getStatus().toLowerCase())
            || status.equals(StatusPagamentoRendimentoEnum.CANCELADO.getStatus().toLowerCase())
            || status.equals(StatusPagamentoRendimentoEnum.ERRO.getStatus().toLowerCase())) {

          String mensagem =
              status.equals(StatusPagamentoRendimentoEnum.REJEITADO.getStatus().toLowerCase())
                  ? "Pagamento Rejeitado - Id: "
                      + logPagtoTituloTransacao.getIdLogPagtoTitulo()
                      + " - Motivo: "
                      + motivoRecusaRend
                  : status.equals(StatusPagamentoRendimentoEnum.CANCELADO.getStatus().toLowerCase())
                      ? "Pagamento Cancelado - Id: "
                          + logPagtoTituloTransacao.getIdLogPagtoTitulo()
                          + " - Motivo: "
                          + motivoRecusaRend
                      : "Erro ao realizar pagamento - Id: "
                          + logPagtoTituloTransacao.getIdLogPagtoTitulo()
                          + " - Motivo: "
                          + motivoRecusaRend;

          logger.warn(mensagem);

          String codigoStatus =
              status.equals(StatusPagamentoRendimentoEnum.REJEITADO.getStatus().toLowerCase())
                  ? StatusTransacao.REJEITADO.getCodigo()
                  : status.equals(StatusPagamentoRendimentoEnum.CANCELADO.getStatus().toLowerCase())
                      ? StatusTransacao.CANCELADO.getCodigo()
                      : StatusTransacao.ERRO.getCodigo();

          if (logPagtoTituloTransacao.getProtocoloIdEstornoRendimento() == null) {

            try {
              logger.info("Realizando estorno");
              jcardResponse =
                  transactionService.estonarTransacao(
                      logPagtoTituloTransacao.getIdTransacaoPagto());

              if (jcardResponse.getSuccess()) {
                logger.info("Pagamento Estornado com sucesso.");

                aplicativoMensagemService.enviarMensagemPosProcessamentoProc(
                    contaPagamento, produto, false);

                logPagtoTituloTransacao.setStatusPagamento(codigoStatus);
                logPagtoTituloTransacao.setDataHoraStatus(new Date());
                logPagtoTituloTransacao.setResponseMessage("Sucesso");
                logPagtoTituloTransacao.setTxMotivoRecusRend(motivoRecusaRend);
                procResponse.setMessage("Pagamento estornado com sucesso.");
              } else {
                throw new JcardServiceException(jcardResponse.getErrors());
              }
            } catch (JcardServiceException e) {
              String erroJcard;

              if (e.getMensagem().contains(PREVIOUSLY_VOIDED)) {
                logPagtoTituloTransacao.setStatusPagamento(codigoStatus);
                logPagtoTituloTransacao.setDataHoraStatus(new Date());
                logPagtoTituloTransacao.setStatusConfirmacao("Sucesso");
                logPagtoTituloTransacao.setTxMotivoRecusRend(motivoRecusaRend);

                logger.info(
                    "Pagamento ja foi estornado anteriormente. Salvando status e seguindo fluxo normal.");
                procResponse.setMessage("Pagamento ja foi estornado anteriormente");
              } else {
                erroJcard =
                    "Errors: "
                        + jcardResponse.getErrors()
                        + "\n"
                        + "Mensagem: "
                        + e.getMensagem()
                        + "\n"
                        + "Detalhes: "
                        + e.getDetalhes();
                logger.warn("Estorno Pagamento não realizado!");
                logger.info(
                    "Salvando erro Jcard e voltando status do pagamento para 'AGUARDANDO APROVAÇÃO'");

                //
                //	logPagtoTituloTransacao.setStatusPagamento(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
                logPagtoTituloTransacao.setStatusPagamento(
                    StatusTransacao.EM_EFETIVACAO.getCodigo());
                logPagtoTituloTransacao.setDataHoraStatus(new Date());
                logger.error(erroJcard);
                procResponse.setMessage("Não foi possível realizar estorno. Valide o log do jcard");
              }
            }
          } else {
            logTransacoes =
                logTransacoesService.findByRrn(logPagtoTituloTransacao.getIdTransacaoPagto());

            if (logTransacoes.getVoidId() != null) {
              logger.warn("Estorno não foi realizado. Pagamento já foi estornada anteriormente");
              logPagtoTituloTransacao.setIdTransacaoPagto(logTransacoes.getRrn());
              logPagtoTituloTransacao.setStatusPagamento(codigoStatus);
              logPagtoTituloTransacao.setDataHoraStatus(new Date());
              logPagtoTituloTransacao.setResponseMessage("Sucesso");
              logPagtoTituloTransacao.setTxMotivoRecusRend(motivoRecusaRend);
            }
          }
          procResponse.setIsSuccess(true);
          procResponse.setIsFailure(false);
        }
      } else {
        String[] statusSplited = status.split("-");

        if (statusSplited.length > 1) {
          status = statusSplited[0].trim();
          motivoRecusaRend = statusSplited[1].replace("/", "").trim();
        } else {
          status = statusSplited[0];
          motivoRecusaRend = "Motivo não informado pelo rendimento";
        }
        status = status.replace(" ", "");

        logPagtoTituloTransacao =
            logPagtoTransacaoRepository.findOneByRrn(pagamento.getIdTransacaoPgto());
        ProdutoInstituicaoConfiguracao produto =
            lancamentoService.getProduto(
                contaPagamento.getIdProcessadora(),
                contaPagamento.getIdInstituicao(),
                contaPagamento.getIdProdutoInstituicao());

        logger.info("Status Pagamento Rendimento: " + consultaPagamentoValue.getStatus());

        if (status.equals(StatusPagamentoTedRendimentoEnum.EFETIVADO.getStatus())) {
          procResponse.setIsSuccess(true);
          procResponse.setIsFailure(false);
          if (logPagtoTituloTransacao
              .getStatusPagamento()
              .equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
            logPagtoTituloTransacao.setStatusPagamento(StatusTransacao.TRANSFERIDO.getCodigo());
            logPagtoTituloTransacao.setDataHoraStatus(new Date());
            logPagtoTituloTransacao.setStatusConfirmacao("Sucesso");
            procResponse.setMessage("Pagamento foi efetuado com sucesso!");
          } else {
            procResponse.setMessage("Pagamento foi efetuado com sucesso!");
            logger.info("Boleto já estava APROVADO e o status se manteve.");
          }
        } else if (status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
            || status.equals(StatusPagamentoTedRendimentoEnum.REJEITADO.getStatus())) {

          String mensagem =
              status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                  ? "Pagamento Recusado - Id: "
                      + logPagtoTituloTransacao.getIdLogPagtoTitulo()
                      + " - Motivo: "
                      + motivoRecusaRend
                  : "Pagamento Rejeitado pelo rendimento - Transação barrada pela regra de negócio";

          logger.warn(mensagem);

          if (logPagtoTituloTransacao.getProtocoloIdEstornoRendimento() == null) {
            try {
              logger.info("Realizando estorno");
              jcardResponse =
                  transactionService.estonarTransacao(
                      logPagtoTituloTransacao.getIdTransacaoPagto());

              if (jcardResponse.getSuccess()) {
                logger.info("Pagamento Estornado com sucesso.");

                aplicativoMensagemService.enviarMensagemPosProcessamentoProc(
                    contaPagamento, produto, false);

                logPagtoTituloTransacao.setStatusPagamento(
                    status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                        ? StatusTransacao.REPROVADO.getCodigo()
                        : StatusTransacao.REJEITADO.getCodigo());
                logPagtoTituloTransacao.setDataHoraStatus(new Date());
                logPagtoTituloTransacao.setResponseMessage("Sucesso");
                logPagtoTituloTransacao.setTxMotivoRecusRend(motivoRecusaRend);
                procResponse.setMessage("Pagamento estornado com sucesso.");
              } else {
                throw new JcardServiceException(jcardResponse.getErrors());
              }
            } catch (JcardServiceException e) {
              String erroJcard;

              if (e.getMensagem().contains(PREVIOUSLY_VOIDED)) {
                logPagtoTituloTransacao.setStatusPagamento(
                    status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                        ? StatusTransacao.REPROVADO.getCodigo()
                        : StatusTransacao.REJEITADO.getCodigo());
                logPagtoTituloTransacao.setDataHoraStatus(new Date());
                logPagtoTituloTransacao.setStatusConfirmacao("Sucesso");
                logPagtoTituloTransacao.setTxMotivoRecusRend(motivoRecusaRend);

                logger.info(
                    "Pagamento ja foi estornado anteriormente. Salvando status e seguindo fluxo normal.");
                procResponse.setMessage("Pagamento ja foi estornado anteriormente");
              } else {
                erroJcard =
                    "Errors: "
                        + jcardResponse.getErrors()
                        + "\n"
                        + "Mensagem: "
                        + e.getMensagem()
                        + "\n"
                        + "Detalhes: "
                        + e.getDetalhes();
                logger.warn("Estorno Pagamento não realizado!");
                logger.info(
                    "Salvando erro Jcard e voltando status do pagamento para 'AGUARDANDO APROVAÇÃO'");
                logPagtoTituloTransacao.setStatusPagamento(
                    StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
                logPagtoTituloTransacao.setDataHoraStatus(new Date());
                logger.error(erroJcard);
                procResponse.setMessage("Não foi possível realizar estorno. Valide o log do jcard");
              }
            }
          } else {
            logTransacoes =
                logTransacoesService.findByRrn(logPagtoTituloTransacao.getIdTransacaoPagto());

            if (logTransacoes.getVoidId() != null) {
              logger.warn("Estorno não foi realizado. Pagamento já foi estornada anteriormente");
              logPagtoTituloTransacao.setIdTransacaoPagto(logTransacoes.getRrn());
              logPagtoTituloTransacao.setStatusPagamento(
                  status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                      ? StatusTransacao.REPROVADO.getCodigo()
                      : StatusTransacao.REJEITADO.getCodigo());
              logPagtoTituloTransacao.setDataHoraStatus(new Date());
              logPagtoTituloTransacao.setResponseMessage("Sucesso");
              logPagtoTituloTransacao.setTxMotivoRecusRend(motivoRecusaRend);
              procResponse.setMessage("Pagamento ja foi estornado anteriormente");
            }
          }
          procResponse.setIsSuccess(true);
          procResponse.setIsFailure(false);
        }
      }
      logPagtoTransacaoRepository.save(logPagtoTituloTransacao);
      procResponse.setValue(logPagtoTituloTransacao);

    } else {
      logger.warn(
          "Objeto Response da consulta nulo ou vazio. "
              + pagamentoConsultaStatusRendimentoResponse.getErroMessage());
      procResponse.setIsFailure(true);
      procResponse.setIsSuccess(false);
      procResponse.setValue(logPagtoTituloTransacao);
      procResponse.setMessage(
          "Objeto Response da consulta nulo ou vazio. "
              + pagamentoConsultaStatusRendimentoResponse.getErroMessage().getMessage());
    }
    return procResponse;
  }

  private PagamentoConsultaStatusRendimentoResponse consultarStatusPagamento(PagamentoVO pagamento)
      throws IOException {

    ResponseEntity<PagamentoConsultaStatusRendimentoResponse> response;
    try {
      this.propriedadesRendimento = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_V2_PAGAMENTOS_TED);
      endPoint.setUri(Constantes.URI_RENDIMENTO_CONSULTA_STATUS_TED);
      this.propriedadesRendimento.setConsultarStatusPagamento(endPoint);
      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, propriedadesRendimento.getAutenticacao());

      HttpEntity<PagamentoRendimentoResponse> entity =
          new HttpEntity<PagamentoRendimentoResponse>(headers);

      response =
          restTemplate.exchange(
              this.propriedadesRendimento.getUrl()
                  + this.propriedadesRendimento.getConsultarStatusPagamento().getUrl()
                  + "/"
                  + chaveInstituicao.getAgencia()
                  + "/"
                  + chaveInstituicao.getConta()
                  + this.propriedadesRendimento.getConsultarStatusPagamento().getUri()
                  + pagamento.getProtocoloIdRendimento(),
              HttpMethod.GET,
              entity,
              PagamentoConsultaStatusRendimentoResponse.class);
    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.info(e.getResponseBodyAsString());
      ObjectMapper objectMapper = new ObjectMapper();
      PagamentoConsultaStatusRendimentoResponse pagamentoConsultaStatusRendimentoResponse =
          objectMapper.readValue(
              e.getResponseBodyAsString(), PagamentoConsultaStatusRendimentoResponse.class);
      pagamentoConsultaStatusRendimentoResponse.setErroMessage(
          pagamentoConsultaStatusRendimentoResponse.getErroMessage());

      return pagamentoConsultaStatusRendimentoResponse;
    }

    return response.getBody();
  }

  private PagamentoConsultaStatusRendimentoResponseV2 consultarStatusPagamentoV2(
      PagamentoVO pagamento) throws IOException {

    ResponseEntity<PagamentoConsultaStatusRendimentoResponseV2> response;
    try {
      this.propriedadesRendimento = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_EVOTITULOS);
      endPoint.setUri(Constantes.URI_RENDIMENTO_CONSULTA_STATUS_TITULO);
      this.propriedadesRendimento.setConsultarStatusPagamento(endPoint);
      this.propriedadesRendimento.setUrl(urlBancoRendimento);

      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.propriedadesRendimento.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, propriedadesRendimento.getAutenticacao());

      HttpEntity<?> entity = new HttpEntity<>(headers);

      response =
          restTemplate.exchange(
              this.propriedadesRendimento.getUrl()
                  + this.propriedadesRendimento.getConsultarStatusPagamento().getUrl()
                  + "/"
                  + chaveInstituicao.getAgencia()
                  + "/"
                  + chaveInstituicao.getConta()
                  + this.propriedadesRendimento.getConsultarStatusPagamento().getUri()
                  + pagamento.getProtocoloIdRendimento(),
              HttpMethod.GET,
              entity,
              PagamentoConsultaStatusRendimentoResponseV2.class);

      logger.info("Response ao consultar Status Pagamento: {}", response);
    } catch (Exception e) {
      logger.info(e.getMessage());
      return montaResponseError(e.getMessage(), e.getLocalizedMessage());
    }
    return response.getBody();
  }

  public PagamentoConsultaStatusRendimentoResponseV2 montaResponseError(
      String message, String localizedMessage) {
    PagamentoConsultaStatusRendimentoResponseV2 pagamentoConsultaStatusRendimentoResponseV2 =
        new PagamentoConsultaStatusRendimentoResponseV2();
    NotificationsRendimentoVO notifications =
        new NotificationsRendimentoVO(message, "500", localizedMessage);
    ArrayList<NotificationsRendimentoVO> listaNotifications = new ArrayList<>();
    listaNotifications.add(notifications);
    pagamentoConsultaStatusRendimentoResponseV2.setSuccess(Boolean.FALSE);
    pagamentoConsultaStatusRendimentoResponseV2.setData(new ArrayList<>());
    pagamentoConsultaStatusRendimentoResponseV2.setErroMessage(null);
    pagamentoConsultaStatusRendimentoResponseV2.setNotifications(listaNotifications);

    return pagamentoConsultaStatusRendimentoResponseV2;
  }

  public PagamentoTituloRendimentoResponse montarResponseRendimentoV2toOriginal(
      PagamentoTituloRendimentoResponseV2 response) {
    PagamentoTituloRendimentoResponse pagamentoConsultaStatusRendimentoResponseV2 =
        new PagamentoTituloRendimentoResponse();
    if (response.getSuccess().equals(false)
        && response.getNotifications().get(0).getMessage() == null) {
      response
          .getNotifications()
          .get(0)
          .setMessage(ConstantesErro.REND_ERRO_AO_REALIZAR_PAGAMENTO_TITULOS.getMensagem());
    }
    NotificationsRendimentoVO notifications =
        new NotificationsRendimentoVO(
            response.getNotifications().get(0).getDomain(),
            response.getNotifications().get(0).getErrorCode(),
            response.getNotifications().get(0).getMessage());
    ArrayList<NotificationsRendimentoVO> listaNotifications = new ArrayList<>();
    listaNotifications.add(notifications);
    pagamentoConsultaStatusRendimentoResponseV2.setSuccess(response.getSuccess());
    pagamentoConsultaStatusRendimentoResponseV2.setData(response.getData());
    pagamentoConsultaStatusRendimentoResponseV2.setNotificationsVO(listaNotifications);
    pagamentoConsultaStatusRendimentoResponseV2.setMessage(notifications.getMessage());

    return pagamentoConsultaStatusRendimentoResponseV2;
  }

  public void checarSeValorEstaDeAcordoComRegraTipoValorDivergenteRendimento(
      LogPagtoTituloValidacao entityProtocolo, EfetuarPagamentoTitulo req) {
    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getTipoAutorizacaoRecebimentoValorDivergente() != null) {
      if (TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.QUALQUER_VALOR
          .getId()
          .equals(entityProtocolo.getTipoAutorizacaoRecebimentoValorDivergente())) {
        if (req.getValor() > entityProtocolo.getValorMaximoPagamento()) {
          throw new GenericServiceException(
              ConstantesErro.REND_VALOR_MAXIMO_NAO_PERMITIDO.getMensagem(),
              HttpStatus.UNPROCESSABLE_ENTITY);
        }
      } else if (TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.ENTRE_MINIMO_MAXIMO
          .getId()
          .equals(entityProtocolo.getTipoAutorizacaoRecebimentoValorDivergente())) {
        if (req.getValor() < entityProtocolo.getValorMinimoPagamento()
            && req.getValor() > entityProtocolo.getValorMaximoPagamento()) {
          throw new GenericServiceException(
              ConstantesErro.REND_VALOR_DIFERENTE_DO_INTERVALO_PERMITIDO.getMensagem(),
              HttpStatus.UNPROCESSABLE_ENTITY);
        }
      } else if (TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.NAO_ACEITAR_DIVERGENTE
          .getId()
          .equals(entityProtocolo.getTipoAutorizacaoRecebimentoValorDivergente())) {
        if (req.getValor() > entityProtocolo.getValorTotal()
            || req.getValor() < entityProtocolo.getValorTotal()) {
          throw new GenericServiceException(
              ConstantesErro.REND_VALOR_DIFERENTE_DO_REGISTRADO.getMensagem(),
              HttpStatus.UNPROCESSABLE_ENTITY);
        }
      } else if (TipoAutorizacaoRecebimentoValorDivergenteBoletoEnum.SOMENTE_MINIMO
          .getId()
          .equals(entityProtocolo.getTipoAutorizacaoRecebimentoValorDivergente())) {
        if (req.getValor() > entityProtocolo.getValorMinimoPagamento()
            || req.getValor() < entityProtocolo.getValorMinimoPagamento()) {
          throw new GenericServiceException(
              ConstantesErro.REND_VALOR_DIFERENTE_DO_MINIMO.getMensagem(),
              HttpStatus.UNPROCESSABLE_ENTITY);
        }
      }
    }
  }
}
