package br.com.sinergico.service.cadastral;

import static br.com.sinergico.enums.CodigoStatusReasonCafEnum.*;

import br.com.caelum.stella.format.CPFFormatter;
import br.com.entity.cadastral.AntifraudeCliente;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.Proposta;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.suporte.AntifraudeCafInstituicaoConfig;
import br.com.entity.suporte.AntifraudeCafPortador;
import br.com.entity.suporte.AntifraudeInstituicao;
import br.com.entity.suporte.GatewaySMS;
import br.com.entity.suporte.LogEventoConta;
import br.com.entity.suporte.LogSMS;
import br.com.entity.suporte.PortadorDispositivo;
import br.com.entity.suporte.TokenCafDTO;
import br.com.entity.suporte.ViewCafportAttributesInfo;
import br.com.entity.suporte.ViewCafportHistoryInfo;
import br.com.entity.suporte.ViewCafportJson;
import br.com.entity.suporte.ViewCafportManualReprovalReasonsInfo;
import br.com.entity.suporte.ViewCafportSectionsCpfInfo;
import br.com.entity.suporte.ViewCafportSectionsFacematchInfo;
import br.com.entity.suporte.ViewCafportSectionsOcrInfo;
import br.com.entity.suporte.ViewCafportStatusReasonsInfo;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.antifraude.AntifraudeValidateClientRequest;
import br.com.json.bean.antifraude.AprovacaoManualCaf;
import br.com.json.bean.antifraude.AprovacaoManualCafVO;
import br.com.json.bean.antifraude.CombateFraudeCaf;
import br.com.json.bean.antifraude.IgnorarValidacao;
import br.com.json.bean.antifraude.TokenCaf;
import br.com.json.bean.antifraude.WebhookCaf;
import br.com.json.bean.cadastral.CheckNivelHierarquiaEnum;
import br.com.json.bean.combateafraude.AlteracaoStatusCafResponse;
import br.com.json.bean.combateafraude.DadosOcrCafResponse;
import br.com.json.bean.combateafraude.ReviewManualCafRequest;
import br.com.json.bean.combateafraude.VerificacaoOcrCafResponse;
import br.com.json.bean.esafer.EsaferEmailResponse;
import br.com.json.bean.esafer.EsaferEmailTypeResponse;
import br.com.json.bean.esafer.EsaferGsmResponse;
import br.com.json.bean.esafer.EsaferOcrField;
import br.com.json.bean.esafer.EsaferOcrResponse;
import br.com.json.bean.esafer.EsaferOcrResult;
import br.com.json.bean.esafer.EsaferReceitaResponse;
import br.com.json.bean.esafer.EsaferVerifyAccountResponse;
import br.com.json.bean.suporte.AntifraudeCafPortadorInfoViewVO;
import br.com.json.bean.suporte.AntifraudeCafPortadorIssuerVO;
import br.com.json.bean.suporte.ComunicadoConta;
import br.com.json.bean.suporte.ComunicadoContaViaSMS;
import br.com.json.bean.suporte.PortadoresCafFiltroRequest;
import br.com.sinergico.client.CombateFraudeClient;
import br.com.sinergico.enums.AntifraudeCafFacialObjetivosEnum;
import br.com.sinergico.enums.AntifraudeCafPortadorStatusEnum;
import br.com.sinergico.enums.AntifraudeClienteStatusEnum;
import br.com.sinergico.enums.CodigoStatusReasonCafEnum;
import br.com.sinergico.enums.TipoDocumentoPropostaEnum;
import br.com.sinergico.repository.cadastral.AntifraudeClienteRepository;
import br.com.sinergico.repository.suporte.AntifraudeCafInstituicaoConfigRepository;
import br.com.sinergico.repository.suporte.AntifraudeCafPortadorRepository;
import br.com.sinergico.repository.suporte.AntifraudeInstituicaoRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.repository.suporte.impl.AntifraudeCafPortadorRepositoryImpl;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.mktplace.ImagemService;
import br.com.sinergico.service.suporte.AntifraudeLogConsultaService;
import br.com.sinergico.service.suporte.ComunicadorPortadorSMSService;
import br.com.sinergico.service.suporte.GatewaySMSService;
import br.com.sinergico.service.suporte.LogEventoContaService;
import br.com.sinergico.service.suporte.LogSMSService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.suporte.PortadorDispositivoService;
import br.com.sinergico.service.suporte.RegistroValidacaoFacialCafService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.ContaPagamentoCafVO;
import br.com.sinergico.vo.DocumentoOCRPropostaVO;
import br.com.sinergico.vo.DocumentoOCRVO;
import br.com.sinergico.vo.OcrResponseVO;
import br.com.sinergico.vo.PortadoresCafFiltroVO;
import br.com.sinergico.vo.combateFraude.ImagesCafResponseVO;
import br.com.sinergico.vo.combateFraude.StatusCafLoginDTO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureException;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AntifraudeService {

  private static final long QUANTIDADE_DE_DIAS_PARA_REVALIDACAO_RECEITA = 90l;
  private static final Integer ENVIO_TOKEN_SMS = 7;
  private static final Integer MIN_DDD = 11;
  private static final Integer MAX_DDD = 99;
  private static final int MAX_CEL = 999999999;
  private static final int MIN_CEL = 910000000;

  @Value("${secret.key.caf}")
  private String secretKeyCaf;

  @Value("${issuer.dir.ocr}")
  private String issuerDirOcr;

  @Autowired private AntifraudeClienteRepository repository;

  @Autowired private AntifraudeLogConsultaService logService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ImagemService imagemService;

  @Autowired private RepresentanteLegalService representanteLegalService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired private AntifraudeInstituicaoRepository antifraudeInstituicaoRepository;

  @Autowired private PortadorDispositivoService portadorDispositivoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private RegistroValidacaoFacialCafService registroValidacaoFacialCafService;

  @Autowired private AntifraudeCafPortadorRepository antifraudeCafPortadorRepository;

  @Autowired
  private AntifraudeCafInstituicaoConfigRepository antifraudeCafInstituicaoConfigRepository;

  @Autowired private CombateFraudeClient combateFraudeClient;

  @Autowired private DocumentoContaService documentoContaService;

  @Autowired private EmailService emailService;

  @Autowired private CafApiService cafApiService;

  @Autowired private AntifraudeCafPortadorRepositoryImpl antifraudeCafPortadorRepositoryImpl;

  @Autowired private LogAntifraudeService logAntifraudeService;

  @Autowired private ComunicadorPortadorSMSService agenteComunicadorService;

  @Autowired private GatewaySMSService gatewaySMSService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private HierarquiaInstituicaoRepository instituicaoRepository;

  @Autowired private LogEventoContaService logEventoContaService;

  @Autowired private LogSMSService logSMSService;

  private Logger logger = LoggerFactory.getLogger(this.getClass());

  public EsaferVerifyAccountResponse verifyAccount(
      Long idInstituicao, String documento, String documentoRepresentante) {
    Boolean precisaRefazerOCREsaferApos90Dias = false;

    AntifraudeInstituicao antifraudeInstituicao = findInstituicaoConfiguration(idInstituicao);
    AntifraudeCliente antifraudeCliente =
        Util.isNotNull(documentoRepresentante) && !documentoRepresentante.isEmpty()
            ? repository.findByIdInstituicaoAndDocumentoAndDocumentoRepresentante(
                idInstituicao, formatString(documento), formatString(documentoRepresentante))
            : repository.findByIdInstituicaoAndDocumento(idInstituicao, formatString(documento));

    /**
     * Caso não exista um registro de antifraude para o CPF que vem como parâmetro Ele deve retornar
     * imediatamente o response pedindo a validação do OCR/Esafer
     */
    if (antifraudeCliente == null) {
      return new EsaferVerifyAccountResponse.Builder()
          .precisaDeValidacaoEsafer(true)
          .temValidacaoDeOcr(false)
          .forcarValidacao(false)
          .isSenhaRedefinidaOCR(false)
          .build();
    }

    Boolean forcarValidacaoDoEsafer =
        (Objects.nonNull(antifraudeCliente.getForcarValidacao())
            && antifraudeCliente.getForcarValidacao());

    Boolean temValidacaoDeOcr =
        (Objects.nonNull(antifraudeCliente.getOcrValidado()) && antifraudeCliente.getOcrValidado());
    Boolean temValidacaoDaReceita =
        (Objects.nonNull(antifraudeCliente.getReceitaValidado())
            && antifraudeCliente.getReceitaValidado());
    Boolean statusEhIgualAEmAnalise =
        (Objects.nonNull(antifraudeCliente.getStatus())
            && antifraudeCliente
                .getStatus()
                .equals(AntifraudeClienteStatusEnum.PENDING.getStatus()));
    Boolean temOcrReceitaEStatusEmAnalise =
        (statusEhIgualAEmAnalise || !temValidacaoDeOcr || !temValidacaoDaReceita);

    Boolean estaEmStandby =
        (Objects.nonNull(antifraudeCliente.getStandby()) && antifraudeCliente.getStandby());

    // Se o CPF do usuário precisar ser revalidado daqui a 90 dias
    if (antifraudeCliente.getRefazerValidacaoReceita()) {
      LocalDateTime now = LocalDateTime.now();
      long daysBetween =
          ChronoUnit.DAYS.between(antifraudeCliente.getDataPrimeiraVerificacao(), now);

      if (daysBetween <= QUANTIDADE_DE_DIAS_PARA_REVALIDACAO_RECEITA) {
        return new EsaferVerifyAccountResponse.Builder()
            .precisaDeValidacaoEsafer(false)
            .temValidacaoDeOcr(true)
            .forcarValidacao(true)
            .isSenhaRedefinidaOCR(true)
            .build();
      } else {
        precisaRefazerOCREsaferApos90Dias = !precisaRefazerOCREsaferApos90Dias;
      }
    }

    Boolean ocrEsaferStatusReprovado =
        (Objects.nonNull(antifraudeCliente.getStatus())
            && antifraudeCliente.getStatus() == AntifraudeClienteStatusEnum.REPROVED.getStatus());

    LocalDateTime minusMonths =
        LocalDateTime.now().minusMonths(antifraudeInstituicao.getQtdMesesRevalidacao());
    Boolean precisaRefazerOCREsaferApos1Ano =
        (Objects.nonNull(antifraudeCliente.getDataUltimaVerificacao())
            && antifraudeCliente.getDataUltimaVerificacao().isBefore(minusMonths));

    /**
     * O retorno padrão da função é o do caso de um portador com o cadastro 100%. Se o indicador de
     * forçar validação for verdadeiro, ele retorna um response forçando a liberação da entrada no
     * app Caso qualquer um dos casos dentro do else if seja verdadeiro ele retorna um response
     * pedindo a validação dos OCR/Esafer
     */
    if (forcarValidacaoDoEsafer) {
      return new EsaferVerifyAccountResponse.Builder()
          .precisaDeValidacaoEsafer(false)
          .temValidacaoDeOcr(true)
          .forcarValidacao(true)
          .isSenhaRedefinidaOCR(antifraudeCliente.getSenhaRedefinidaOCR())
          .build();
    } else if (temOcrReceitaEStatusEmAnalise
        || estaEmStandby
        || precisaRefazerOCREsaferApos90Dias
        || ocrEsaferStatusReprovado
        || precisaRefazerOCREsaferApos1Ano) {
      return new EsaferVerifyAccountResponse.Builder()
          .precisaDeValidacaoEsafer(true)
          .temValidacaoDeOcr(false)
          .forcarValidacao(false)
          .isSenhaRedefinidaOCR(false)
          .build();
    }
    return new EsaferVerifyAccountResponse.Builder()
        .precisaDeValidacaoEsafer(false)
        .temValidacaoDeOcr(true)
        .forcarValidacao(false)
        .isSenhaRedefinidaOCR(antifraudeCliente.getSenhaRedefinidaOCR())
        .build();
  }

  public EsaferVerifyAccountResponse verifyAccountPJ(
      Long idInstituicao, String documento, String documentoRepresentante) {
    Boolean precisaRefazerOCREsaferApos90Dias = false;

    AntifraudeInstituicao antifraudeInstituicao = findInstituicaoConfiguration(idInstituicao);
    AntifraudeCliente antifraudeCliente =
        repository.findByIdInstituicaoAndDocumentoAndDocumentoRepresentante(
            idInstituicao, formatString(documento), formatString(documentoRepresentante));

    /**
     * Caso não exista um registro de antifraude para o CPF que vem como parâmetro Ele deve retornar
     * imediatamente o response pedindo a validação do OCR/Esafer
     */
    if (antifraudeCliente == null) {
      return new EsaferVerifyAccountResponse.Builder()
          .precisaDeValidacaoEsafer(true)
          .temValidacaoDeOcr(false)
          .forcarValidacao(false)
          .isSenhaRedefinidaOCR(false)
          .build();
    }

    Boolean forcarValidacaoDoEsafer =
        (Objects.nonNull(antifraudeCliente.getForcarValidacao())
            && antifraudeCliente.getForcarValidacao());

    Boolean temValidacaoDeOcr =
        (Objects.nonNull(antifraudeCliente.getOcrValidado()) && antifraudeCliente.getOcrValidado());
    Boolean temValidacaoDaReceita =
        (Objects.nonNull(antifraudeCliente.getReceitaValidado())
            && antifraudeCliente.getReceitaValidado());
    Boolean statusEhIgualAEmAnalise =
        (Objects.nonNull(antifraudeCliente.getStatus())
            && antifraudeCliente
                .getStatus()
                .equals(AntifraudeClienteStatusEnum.PENDING.getStatus()));
    Boolean temOcrReceitaEStatusEmAnalise =
        (statusEhIgualAEmAnalise || !temValidacaoDeOcr || !temValidacaoDaReceita);

    Boolean estaEmStandby =
        (Objects.nonNull(antifraudeCliente.getStandby()) && antifraudeCliente.getStandby());

    // Se o CPF do usuário precisar ser revalidado daqui a 90 dias
    if (antifraudeCliente.getRefazerValidacaoReceita()) {
      LocalDateTime now = LocalDateTime.now();
      long daysBetween =
          ChronoUnit.DAYS.between(antifraudeCliente.getDataPrimeiraVerificacao(), now);

      if (daysBetween <= QUANTIDADE_DE_DIAS_PARA_REVALIDACAO_RECEITA) {
        return new EsaferVerifyAccountResponse.Builder()
            .precisaDeValidacaoEsafer(false)
            .temValidacaoDeOcr(true)
            .forcarValidacao(true)
            .isSenhaRedefinidaOCR(true)
            .build();
      } else {
        precisaRefazerOCREsaferApos90Dias = !precisaRefazerOCREsaferApos90Dias;
      }
    }

    Boolean ocrEsaferStatusReprovado =
        (Objects.nonNull(antifraudeCliente.getStatus())
            && antifraudeCliente.getStatus() == AntifraudeClienteStatusEnum.REPROVED.getStatus());

    LocalDateTime minusMonths =
        LocalDateTime.now().minusMonths(antifraudeInstituicao.getQtdMesesRevalidacao());
    Boolean precisaRefazerOCREsaferApos1Ano =
        (Objects.nonNull(antifraudeCliente.getDataUltimaVerificacao())
            && antifraudeCliente.getDataUltimaVerificacao().isBefore(minusMonths));

    /**
     * O retorno padrão da função é o do caso de um portador com o cadastro 100%. Se o indicador de
     * forçar validação for verdadeiro, ele retorna um response forçando a liberação da entrada no
     * app Caso qualquer um dos casos dentro do else if seja verdadeiro ele retorna um response
     * pedindo a validação dos OCR/Esafer
     */
    if (forcarValidacaoDoEsafer) {
      return new EsaferVerifyAccountResponse.Builder()
          .precisaDeValidacaoEsafer(false)
          .temValidacaoDeOcr(true)
          .forcarValidacao(true)
          .isSenhaRedefinidaOCR(antifraudeCliente.getSenhaRedefinidaOCR())
          .build();
    } else if (temOcrReceitaEStatusEmAnalise
        || estaEmStandby
        || precisaRefazerOCREsaferApos90Dias
        || ocrEsaferStatusReprovado
        || precisaRefazerOCREsaferApos1Ano) {
      return new EsaferVerifyAccountResponse.Builder()
          .precisaDeValidacaoEsafer(true)
          .temValidacaoDeOcr(false)
          .forcarValidacao(false)
          .isSenhaRedefinidaOCR(false)
          .build();
    }
    return new EsaferVerifyAccountResponse.Builder()
        .precisaDeValidacaoEsafer(false)
        .temValidacaoDeOcr(true)
        .forcarValidacao(false)
        .isSenhaRedefinidaOCR(antifraudeCliente.getSenhaRedefinidaOCR())
        .build();
  }

  public AntifraudeCliente findByIdInstituicaoAndDocumento(Long idInstituicao, String documento) {
    return repository.findByIdInstituicaoAndDocumento(idInstituicao, documento);
  }

  public AntifraudeCliente findByIdInstituicaoAndDocumentoAndDataUltimaVerificacaoIsAfter(
      Long idInstituicao, String documento, LocalDateTime dataLimite) {
    return repository.findByIdInstituicaoAndDocumentoAndDataUltimaVerificacaoIsAfter(
        idInstituicao, documento, dataLimite);
  }

  public AntifraudeCliente findByIdInstituicaoAndDocumentoAndDocumentoRepresentante(
      Long idInstituicao, String documento, String cpfRepresentante) {
    return repository.findByIdInstituicaoAndDocumentoAndDocumentoRepresentante(
        idInstituicao, documento, cpfRepresentante);
  }

  private void salvarDocumentosOCR(
      String front,
      String back,
      String documento,
      String documentoRepresentante,
      Long idInstituicao)
      throws IOException {

    String pasta = issuerDirOcr;
    String caminhoInstituicao = pasta + idInstituicao;
    File diretorioInstituicao = new File(caminhoInstituicao);
    if (!diretorioInstituicao.exists()) {
      diretorioInstituicao.mkdir();
    }

    String caminhoIntermediario = caminhoInstituicao + "/" + documento;
    File directoryIntermediario = new File(caminhoIntermediario);
    if (!directoryIntermediario.exists()) {
      directoryIntermediario.mkdir();
    }

    String caminhoFinal = caminhoIntermediario;
    if (documentoRepresentante != null) {
      caminhoFinal = caminhoFinal + "/" + documentoRepresentante;
    }
    File directory = new File(caminhoFinal);
    if (!directory.exists()) {
      directory.mkdir();
    }
    String partSeparator = ",";

    if (front != null) {
      String frontWithoutSeparator =
          front.contains(partSeparator) ? front.split(partSeparator)[1] : front;
      byte[] decodedImg =
          Base64.getDecoder().decode(frontWithoutSeparator.getBytes(StandardCharsets.UTF_8));
      Path destinationFile =
          Paths.get(
              caminhoFinal,
              String.format(
                  "%s-front.jpeg",
                  documentoRepresentante != null ? documentoRepresentante : documento));
      Files.write(destinationFile, decodedImg);
    }

    if (back != null) {
      String backWithoutSeparator =
          back.contains(partSeparator) ? back.split(partSeparator)[1] : back;
      byte[] decodedImg =
          Base64.getDecoder().decode(backWithoutSeparator.getBytes(StandardCharsets.UTF_8));
      Path destinationFile =
          Paths.get(
              caminhoFinal,
              String.format(
                  "%s-back.jpeg",
                  documentoRepresentante != null ? documentoRepresentante : documento));
      Files.write(destinationFile, decodedImg);
    }
  }

  //    public OcrResponseVO startClientValidation(AntifraudeValidateClientRequest request,
  // AntifraudeCliente antifraudeCliente) {
  //        log.info("[ANTIFRAUDE LOG] Iniciando validação geral para documento: " +
  // request.getDocumento());
  //
  //        AntifraudeInstituicao antifraudeInstituicao =
  // findInstituicaoConfiguration(request.getIdInstituicao());
  //        if (antifraudeCliente.getStandby()) {
  //            antifraudeCliente.setStandby(false);
  //        }
  //        antifraudeCliente.setDataUltimaVerificacao(LocalDateTime.now());
  //        antifraudeCliente.setPlataformaValidacao(request.getPlataformaValidacao());
  //
  //        // GSM Request E-safer
  //        if (antifraudeInstituicao.getValidarGsm()) {
  //            try {
  //                EsaferGsmRequest gsmRequest = EsaferGsmRequest.builder()
  //                        .number(formatString(request.getPhoneNumber()))
  //                        .areaCode(formatString(request.getPhoneAreaCode()))
  //                        .countryCode(formatString(request.getPhoneCountryCode()))
  //                        .cpf(request.getDocumento())
  //                        .build();
  //                String gsmTraceabilityCode = esaferClient.consultarGSM(gsmRequest,
  // request.getIdInstituicao(), request.getDocumento());
  //                logService.saveLog(request.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.GSM_REQUEST);
  //                antifraudeCliente.setGsmCode(gsmTraceabilityCode);
  //                antifraudeCliente.setGsmValidado(false);
  //                antifraudeCliente.setGsmRequest(gsmRequest.getCountryCode() +
  // gsmRequest.getAreaCode() + gsmRequest.getNumber());
  //            } catch (Exception e) {
  //                log.error("[ANTIFRAUDE LOG] Erro request esafer gsm: ", e);
  //                throw new GenericServiceException("Aconteceu algum erro ao solicitar
  // validação");
  //            }
  //        }
  //
  //        // Email Request E-safer
  //        if (antifraudeInstituicao.getValidarEmail()) {
  //            try {
  //                EsaferEmailRequest emailRequest = new EsaferEmailRequest();
  //                emailRequest.setCpf(request.getDocumento());
  //                String emailTraceabilityCode = esaferClient.consultarEmail(emailRequest,
  // request.getIdInstituicao(), request.getDocumento());
  //                logService.saveLog(request.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.EMAIL_REQUEST);
  //                antifraudeCliente.setEmailCode(emailTraceabilityCode);
  //                antifraudeCliente.setEmailValidado(false);
  //                antifraudeCliente.setEmailRequest(request.getEmail());
  //            } catch (Exception e) {
  //                log.error("[ANTIFRAUDE LOG] Erro request esafer email: ", e);
  //                throw new GenericServiceException("Aconteceu algum erro ao solicitar
  // validação");
  //            }
  //        }
  //
  //        // Receita Federal Request E-safer
  //        if (antifraudeInstituicao.getValidarReceita()) {
  //            try {
  //                EsaferReceitaRequest receitaRequest = new EsaferReceitaRequest();
  //                receitaRequest.setCpf(request.getDocumentoRepresentante() != null ?
  // request.getDocumentoRepresentante() : request.getDocumento());
  //                String receitaTraceabilityCode = esaferClient.consultarReceita(receitaRequest,
  // request.getIdInstituicao(), request.getDocumentoRepresentante() != null ?
  // request.getDocumentoRepresentante() : request.getDocumento());
  //                logService.saveLog(request.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.RECEITA_REQUEST);
  //                antifraudeCliente.setReceitaCode(receitaTraceabilityCode);
  //                antifraudeCliente.setReceitaValidado(false);
  //            } catch (Exception e) {
  //                log.error("[ANTIFRAUDE LOG] Erro request esafer receita: ", e);
  //                throw new GenericServiceException("Aconteceu algum erro ao solicitar
  // validação");
  //            }
  //        }
  //
  //        antifraudeCliente = repository.saveAndFlush(antifraudeCliente);
  //
  //        log.info("[ANTIFRAUDE LOG] Validação de conta solicitada - AntifraudeClienteId: " +
  // antifraudeCliente.getIdAntifraudeClient());
  //
  //        return verifyAccountStatus(request, antifraudeCliente);
  //    }

  //    public OcrResponseVO verifyOcrResult(AntifraudeCliente antifraudeCliente,
  // EsaferReceitaBasicData basicData, Boolean receitaScoreInvalido, AntifraudeValidateClientRequest
  // request) {
  //        AntifraudeClienteStatusEnum status = AntifraudeClienteStatusEnum.PENDING;
  //
  //        if (antifraudeCliente == null) {
  //            throw new GenericServiceException("Informações não encontradas para o documento: " +
  // antifraudeCliente.getDocumentoRepresentante() != null ?
  // antifraudeCliente.getDocumentoRepresentante() : antifraudeCliente.getDocumento());
  //        }
  //        boolean frontValid = false;
  //        boolean backValid = false;
  //        ArrayList<EsaferOcrResponse> response = new ArrayList<>();
  //
  //        AntifraudeInstituicao antifraudeInstituicao =
  // findInstituicaoConfiguration(antifraudeCliente.getIdInstituicao());
  //
  //        try {
  //            //ESSE SLEEP SERVE PARA AGUARDAR A RESPOSTA DA CHAMADA EXTERNA DO ESAFER
  //            //NÃO REMOVER!!!
  //            Long esaferTimeout =
  // parametroValorService.findValorParametroLong(Constantes.PARAMETRO_VALOR_ESAFER);
  //            Thread.sleep(esaferTimeout);
  //            if (StringUtils.isNotEmpty(antifraudeCliente.getOcrCodeFront())) {
  //                EsaferOcrResponse frontOcrResponse =
  // esaferClient.verificarOcr(antifraudeCliente.getOcrCodeFront(),
  // antifraudeCliente.getIdInstituicao(), antifraudeCliente.getDocumentoRepresentante() != null ?
  // antifraudeCliente.getDocumentoRepresentante() : antifraudeCliente.getDocumento());
  //                logService.saveLog(antifraudeCliente.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.OCR_RESPONSE);
  //                log.info("[ANTIFRAUDE LOG] CODE FRONT: " + frontOcrResponse);
  //                response.add(frontOcrResponse);
  //                if (frontOcrResponse != null && frontOcrResponse.getStatusCode().equals("01")) {
  //                    frontValid = true;
  //                }
  //            } else {
  //                frontValid = true;
  //            }
  //            if (StringUtils.isNotEmpty(antifraudeCliente.getOcrCodeBack())) {
  //                EsaferOcrResponse backOcrResponse =
  // esaferClient.verificarOcr(antifraudeCliente.getOcrCodeBack(),
  // antifraudeCliente.getIdInstituicao(), antifraudeCliente.getDocumentoRepresentante() != null ?
  // antifraudeCliente.getDocumentoRepresentante() : antifraudeCliente.getDocumento());
  //                logService.saveLog(antifraudeCliente.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.OCR_RESPONSE);
  //                log.info("[ANTIFRAUDE LOG] CODE BACK: " + backOcrResponse);
  //                response.add(backOcrResponse);
  //                if (backOcrResponse != null && backOcrResponse.getStatusCode().equals("01")) {
  //                    backValid = true;
  //                }
  //            } else {
  //                backValid = true;
  //            }
  //        } catch (InterruptedException e) {
  //            throw new RuntimeException(e);
  //        }
  //
  //        Integer validadorCount = 0;
  //        if (frontValid && backValid) {
  //            if (Util.isNotNull(response) && Util.isNotNull(basicData)) {
  //                log.info("[ANTIFRAUDE LOG] Iniciando validação do documento com a receita.");
  //
  //                //Foi inserido esse if para retirar o ocr da kredit.
  //                if (antifraudeInstituicao.getIdInstituicao() !=
  // Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.longValue()) {
  //                    for (EsaferOcrResponse esaferOcrResponse : response) {
  //                        if (esaferOcrResponse.getResult() != null &&
  // esaferOcrResponse.getResult().size() > 0) {
  //                            for (EsaferOcrResult esaferOcrResult :
  // esaferOcrResponse.getResult()) {
  //                                if (esaferOcrResult.getFields() != null &&
  // esaferOcrResult.getFields().size() > 0) {
  //                                    for (EsaferOcrField responseField :
  // esaferOcrResult.getFields()) {
  //                                        if (responseField.getValue() == null) continue;
  //
  //                                        if (responseField.getName().equals("nome")) {
  //                                            if (basicData.getName() != null &&
  // !basicData.getName().isEmpty() &&
  // !responseField.getValue().equalsIgnoreCase(basicData.getName())) {
  //                                                int value =
  // StringUtils.getLevenshteinDistance(responseField.getValue().toUpperCase(),
  // basicData.getName().toUpperCase());
  //                                                if (value >
  // (Constantes.PORCENTAGEM_LEVENSHTEIN_DISTANCE * basicData.getName().length())) {
  //                                                    throw new GenericServiceException("O nome
  // identificado na Receita é diferente do documento enviado, inicie novamente seu cadastro.");
  //                                                } else {
  //                                                    validadorCount++;
  //                                                }
  //                                            } else if (basicData.getName() != null &&
  // !basicData.getName().isEmpty() &&
  // responseField.getValue().equalsIgnoreCase(basicData.getName())) {
  //                                                validadorCount++;
  //                                            }
  //
  //                                        } else if (responseField.getName().equals("cpf")) {
  //                                            if (basicData.getTaxIdNumber() != null &&
  // !basicData.getTaxIdNumber().isEmpty() && !Objects.equals(responseField.getValue(),
  // basicData.getTaxIdNumber())
  //                                                    || (basicData.getTaxIdNumber() == null ||
  // basicData.getTaxIdNumber().isEmpty())) {
  //                                                throw new GenericServiceException("O CPF
  // informado no cadastro é diferente do documento enviado, inicie novamente seu cadastro.");
  //                                            } else if (basicData.getTaxIdNumber() != null &&
  // !basicData.getTaxIdNumber().isEmpty() && Objects.equals(responseField.getValue(),
  // basicData.getTaxIdNumber())) {
  //                                                validadorCount++;
  //                                            }
  //
  //                                        } else if (responseField.getName().equals("nome_mae")) {
  //                                            if (basicData.getMotherName() != null &&
  // !basicData.getMotherName().isEmpty() &&
  // !responseField.getValue().equalsIgnoreCase(basicData.getMotherName())) {
  //                                                int value =
  // StringUtils.getLevenshteinDistance(responseField.getValue().toUpperCase(),
  // basicData.getMotherName().toUpperCase());
  //                                                if (value >
  // (Constantes.PORCENTAGEM_LEVENSHTEIN_DISTANCE * basicData.getMotherName().length())) {
  //                                                    throw new GenericServiceException("O Nome da
  // Mãe identificado na Receita é diferente do documento enviado, inicie novamente seu cadastro.");
  //                                                } else {
  //                                                    validadorCount++;
  //                                                }
  //                                            } else if (basicData.getMotherName() != null &&
  // !basicData.getMotherName().isEmpty() &&
  // responseField.getValue().equalsIgnoreCase(basicData.getMotherName())) {
  //                                                validadorCount++;
  //                                            }
  //                                        } else if
  // (responseField.getName().equals("data_nascimento")) {
  //                                            if (basicData.getBirthDate() != null &&
  // !basicData.getBirthDate().isEmpty()) {
  //                                                SimpleDateFormat dataEsafer = new
  // SimpleDateFormat("dd/MM/yyyy");
  //                                                SimpleDateFormat dataReceita = new
  // SimpleDateFormat("yyyy-MM-dd");
  //                                                String dataSemHora =
  // basicData.getBirthDate().substring(0, 10);
  //
  //                                                try {
  //                                                    Date dateFormatada =
  // dataReceita.parse(dataSemHora);
  //                                                    String dataStringFormatada =
  // dataEsafer.format(dateFormatada);
  //                                                    if
  // (!Objects.equals(responseField.getValue(), dataStringFormatada)) {
  //                                                        throw new GenericServiceException("A
  // Data de Nascimento identificada na Receita é diferente do documento enviado, inicie novamente
  // seu cadastro.");
  //                                                    } else {
  //                                                        validadorCount++;
  //                                                    }
  //                                                } catch (ParseException e) {
  //                                                    throw new RuntimeException(e);
  //                                                }
  //                                            }
  //                                        } else if (responseField.getName().equals("rg")) {
  //                                            List<String> pessoasMesmoRgList =
  // pessoaService.findAllByRg(responseField.getValue());
  //                                            pessoasMesmoRgList.stream().filter(r ->
  // !r.equals(basicData.getTaxIdNumber())).findFirst()
  //                                                    .ifPresent(s -> {
  //                                                                throw new
  // GenericServiceException("Existem divergências entre os dados do documento.");
  //                                                            }
  //                                                    );
  //                                            validadorCount++;
  //                                        }
  //
  //                                    }
  //                                } else {
  //                                    throw new GenericServiceException("Ocorreu um erro ao
  // validar os dados do documento.");
  //                                }
  //                            }
  //                        } else {
  //                            throw new GenericServiceException("Ocorreu um erro ao validar os
  // dados do documento.");
  //                        }
  //                    }
  //                }
  //            }
  //
  //            //Foi inserido esse if para retirar o ocr da kredit.
  //            if (antifraudeInstituicao.getIdInstituicao() !=
  // Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.longValue()) {
  //                if (validadorCount < Constantes.QUANTITY_VALIDATIONS_OCR) {
  //                    throw new GenericServiceException("Ocorreu um erro ao validar os dados do
  // documento. Verifique o documento enviado ou entre em contato com a instituição.");
  //                }
  //            }
  //
  //            // Validar documento do cliente com resultado da ocr?
  //            antifraudeCliente.setOcrValidado(true);
  //
  // antifraudeCliente.setOcrScore(antifraudeInstituicao.getScoreOcrDocumentacaoValida());
  //
  //            if ((!antifraudeInstituicao.getValidarOcr() || antifraudeCliente.getOcrValidado())
  // &&
  //                    (!antifraudeInstituicao.getValidarGsm() ||
  // antifraudeCliente.getGsmValidado()) &&
  //                    (!antifraudeInstituicao.getValidarEmail() ||
  // antifraudeCliente.getEmailValidado()) &&
  //                    (!antifraudeInstituicao.getValidarReceita() ||
  // antifraudeCliente.getReceitaValidado())) {
  //
  //                double totalScore = 0.0;
  //                totalScore += antifraudeInstituicao.getValidarOcr() ?
  // antifraudeCliente.getOcrScore() : 0.0;
  //                totalScore += antifraudeInstituicao.getValidarGsm() ?
  // antifraudeCliente.getGsmScore() : 0.0;
  //                totalScore += antifraudeInstituicao.getValidarEmail() ?
  // antifraudeCliente.getEmailScore() : 0.0;
  //                totalScore += antifraudeInstituicao.getValidarReceita() ?
  // antifraudeCliente.getReceitaScore() : 0.0;
  //
  //                antifraudeCliente.setTotalScore(totalScore);
  //                if (totalScore >= antifraudeInstituicao.getScoreNotaDeCorte()) {
  //                    status = AntifraudeClienteStatusEnum.APPROVED;
  //                } else {
  //                    status = AntifraudeClienteStatusEnum.REPROVED;
  //                }
  //            }
  //            antifraudeCliente.setStatus(status.ordinal());
  //
  //            repository.save(antifraudeCliente);
  //        } else {
  //            throw new GenericServiceException("Aconteceu algum erro ao validar seu documento,
  // tente novamente mais tarde.");
  //        }
  //
  //        OcrResponseVO ocrResponseVO = new OcrResponseVO();
  //        ArrayList<EsaferOcrResponse> ocrResponse = new ArrayList<EsaferOcrResponse>();
  //        EsaferOcrResponse ocrResponseNonList = new EsaferOcrResponse();
  //
  //        if (Util.isNotNull(response.get(0))) {
  //            try {
  //                BeanUtils.copyProperties(ocrResponseNonList, response.get(0));
  //                ocrResponse.add(ocrResponseNonList);
  //                ocrResponseVO.setResponse(ocrResponse);
  //                ocrResponseVO.setReceitaScoreInvalido(receitaScoreInvalido);
  //                ocrResponseVO.setStatus(status.ordinal());
  //                return ocrResponseVO;
  //            } catch (IllegalAccessException | InvocationTargetException e) {
  //                throw new RuntimeException(e);
  //            }
  //        } else {
  //            throw new GenericServiceException("Ocorreu um erro na operação.");
  //        }
  //
  //    }

  //    public OcrResponseVO verifyAccountStatus(AntifraudeValidateClientRequest request,
  // AntifraudeCliente antifraudeCliente) {
  //        EsaferReceitaBasicData basicData = new EsaferReceitaBasicData();
  //        boolean receitaScoreInvalido = false;
  //        if (antifraudeCliente == null) {
  //            throw new GenericServiceException("Informações não encontradas para o documento: " +
  // request.getDocumentoRepresentante() != null ? request.getDocumentoRepresentante() :
  // request.getDocumento());
  //        }
  //        AntifraudeInstituicao antifraudeInstituicao =
  // findInstituicaoConfiguration(antifraudeCliente.getIdInstituicao());
  //
  ////        if (antifraudeInstituicao.getValidarOcr() && !antifraudeCliente.getOcrValidado()) {
  ////            this.verifyOcrResult(idInstituicao, documento, null);
  ////        }
  //        if (antifraudeInstituicao.getValidarGsm() && !antifraudeCliente.getGsmValidado()) {
  //            try {
  //                EsaferGsmResponse esaferGsmResponse =
  // esaferClient.verificarGSM(antifraudeCliente.getGsmCode(), antifraudeCliente.getIdInstituicao(),
  // antifraudeCliente.getDocumento());
  //                logService.saveLog(antifraudeCliente.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.GSM_RESPONSE);
  //                if (esaferGsmResponse != null && esaferGsmResponse.getStatusCode().equals("01"))
  // {
  //                    antifraudeCliente.setGsmValidado(true);
  //                    antifraudeCliente.setGsmScore(this.calculateGsmScore(esaferGsmResponse,
  // antifraudeInstituicao));
  //                }
  //            } catch (Exception e) {
  //                antifraudeCliente.setGsmValidado(true);
  //                antifraudeCliente.setGsmScore(0.0);
  //                log.error("[ANTIFRAUDE LOG] Ocorreu algum erro ao chamar API de verificar gsm
  // Esafer", e);
  //            }
  //        }
  //        if (antifraudeInstituicao.getValidarEmail() && !antifraudeCliente.getEmailValidado()) {
  //            try {
  //                EsaferEmailResponse emailResponse =
  // esaferClient.verificarEmail(antifraudeCliente.getEmailCode(),
  // antifraudeCliente.getIdInstituicao(), antifraudeCliente.getDocumento());
  //                logService.saveLog(antifraudeCliente.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.EMAIL_RESPONSE);
  //                if (emailResponse != null && emailResponse.getStatusCode().equals("01")) {
  //                    antifraudeCliente.setEmailValidado(true);
  //                    antifraudeCliente.setEmailScore(this.calculateEmailScore(emailResponse,
  // antifraudeInstituicao, antifraudeCliente.getEmailRequest()));
  //                }
  //            } catch (Exception e) {
  //                antifraudeCliente.setEmailValidado(true);
  //                antifraudeCliente.setEmailScore(0.0);
  //                log.error("[ANTIFRAUDE LOG] Ocorreu algum erro ao chamar API de verificar email
  // Esafer", e);
  //            }
  //        }
  //        if (antifraudeInstituicao.getValidarReceita() &&
  // !antifraudeCliente.getReceitaValidado()) {
  //
  //            try {
  //                EsaferReceitaResponse receitaResponse =
  // esaferClient.verificarReceita(antifraudeCliente.getReceitaCode(),
  // antifraudeCliente.getIdInstituicao(), request.getDocumentoRepresentante() != null ?
  // request.getDocumentoRepresentante() : request.getDocumento());
  //                logService.saveLog(antifraudeCliente.getIdInstituicao(),
  // antifraudeCliente.getIdAntifraudeClient(), AntifraudeLogConsultaService.RECEITA_RESPONSE);
  //                if (receitaResponse != null && receitaResponse.getStatusCode().equals("01")) {
  //                    antifraudeCliente.setReceitaValidado(true);
  //
  // antifraudeCliente.setReceitaScore(this.calculateReceitaScore(receitaResponse,
  // antifraudeInstituicao));
  //
  //                    BeanUtils.copyProperties(basicData,
  // receitaResponse.getResult().get(0).getBasicData());
  //
  //                    LocalDateTime now = LocalDateTime.now();
  //                    long compareDataPrimeiraVerificacaoAndDataHoje = ChronoUnit.DAYS
  //                            .between(antifraudeCliente.getDataPrimeiraVerificacao(), now);
  //
  //                    if
  // (antifraudeCliente.getReceitaScore().equals(antifraudeInstituicao.getScoreReceitaDocumentacaoInvalida())) {
  //                        if (compareDataPrimeiraVerificacaoAndDataHoje <=
  // QUANTIDADE_DE_DIAS_PARA_REVALIDACAO_RECEITA) {
  //
  // antifraudeCliente.setReceitaScore(antifraudeInstituicao.getScoreReceitaDocumentacaoValida());
  //                            antifraudeCliente.setRefazerValidacaoReceita(true);
  //                        } else {
  //                            receitaScoreInvalido = true;
  //                        }
  //                    }
  //                } else {
  //                    receitaScoreInvalido = true;
  //                }
  //            } catch (Exception e) {
  //                antifraudeCliente.setReceitaValidado(true);
  //                antifraudeCliente.setReceitaScore(0.0);
  //                log.error("[ANTIFRAUDE LOG] Ocorreu algum erro ao chamar API de verificar
  // receita Esafer", e);
  //            }
  //        }
  //        // Prolixo porem simples:
  //        // Verificação se todos os campos estão validados, de acordo com a configuração da
  // instituição
  //        repository.saveAndFlush(antifraudeCliente);
  //
  //        return verifyOcrResult(antifraudeCliente, basicData, receitaScoreInvalido, request);
  //    }

  //    public Map<String, Object> getDetailedResult(Long idInstituicao, String documento) {
  //        AntifraudeCliente antifraudeCliente =
  // repository.findByIdInstituicaoAndDocumento(idInstituicao, formatString(documento));
  //        if (antifraudeCliente == null) {
  //            throw new GenericServiceException("Informações não encontradas para o documento: " +
  // documento);
  //        }
  //        EsaferEmailResponse emailResponse = null;
  //        EsaferGsmResponse esaferGsmResponse = null;
  //        EsaferReceitaResponse receitaResponse = null;
  //        if (StringUtils.isNotEmpty(antifraudeCliente.getGsmCode())) {
  //            esaferGsmResponse = esaferClient.verificarGSM(antifraudeCliente.getGsmCode(),
  // antifraudeCliente.getIdInstituicao(), antifraudeCliente.getDocumento());
  //            logService.saveLog(idInstituicao, antifraudeCliente.getIdAntifraudeClient(),
  // AntifraudeLogConsultaService.GSM_RESPONSE);
  //        }
  //        if (StringUtils.isNotEmpty(antifraudeCliente.getEmailCode())) {
  //            emailResponse = esaferClient.verificarEmail(antifraudeCliente.getEmailCode(),
  // antifraudeCliente.getIdInstituicao(), antifraudeCliente.getDocumento());
  //            logService.saveLog(idInstituicao, antifraudeCliente.getIdAntifraudeClient(),
  // AntifraudeLogConsultaService.EMAIL_RESPONSE);
  //        }
  //        if (StringUtils.isNotEmpty(antifraudeCliente.getReceitaCode())) {
  //            receitaResponse = esaferClient.verificarReceita(antifraudeCliente.getReceitaCode(),
  // antifraudeCliente.getIdInstituicao(), antifraudeCliente.getDocumento());
  //            logService.saveLog(idInstituicao, antifraudeCliente.getIdAntifraudeClient(),
  // AntifraudeLogConsultaService.RECEITA_RESPONSE);
  //        }
  //        return GenericJsonResponse.builder()
  //                .append("emailReponse", emailResponse)
  //                .append("gsmResponse", esaferGsmResponse)
  //                .append("receitaResponse", receitaResponse)
  //                .build();
  //    }

  public void setAccountToStandby(Long idInstituicao, String documento) {
    AntifraudeCliente antifraudeCliente =
        repository.findByIdInstituicaoAndDocumento(idInstituicao, formatString(documento));
    if (antifraudeCliente == null) {
      log.info(
          "[ANTIFRAUDE LOG] [Standby] Informações não encontradas para o documento: "
              + formatString(documento));
      return;
    }
    antifraudeCliente.setStandby(true);
    log.info("[ANTIFRAUDE LOG] Conta em Standy: " + antifraudeCliente.getIdAntifraudeClient());
    repository.save(antifraudeCliente);
  }

  private Double calculateGsmScore(
      EsaferGsmResponse gsmResponse, AntifraudeInstituicao antifraudeInstituicao) {
    if (gsmResponse == null
        || gsmResponse.getActivationTime() == null
        || gsmResponse.getActivationTime().size() == 0) {
      return 0.0;
    }
    // Question 1: Tipo de plano
    if (gsmResponse.getPlanType().equals("01")) { // Pós Pago
      // Question 2: Tempo de plano
      if (gsmResponse.getActivationTime().get(0)) { // + 6 meses
        return antifraudeInstituicao.getScoreGsmPosPagoFaixa1();
      } else if (gsmResponse.getActivationTime().get(1)) { // 3 a 6 meses
        return antifraudeInstituicao.getScoreGsmPosPagoFaixa2();
      } else if (gsmResponse.getActivationTime().get(2)) { // 1 a 3 meses
        return antifraudeInstituicao.getScoreGsmPosPagoFaixa3();
      } else if (gsmResponse.getActivationTime().get(3)) { // 0 a 1 meses
        return antifraudeInstituicao.getScoreGsmPosPagoFaixa4();
      }
    } else if (gsmResponse.getPlanType().equals("02")) { // Pré Pago
      // Question 2: Tempo de plano
      if (gsmResponse.getActivationTime().get(0)) { // + 6 meses
        return antifraudeInstituicao.getScoreGsmPrePagoFaixa1();
      } else if (gsmResponse.getActivationTime().get(1)) { // 3 a 6 meses
        return antifraudeInstituicao.getScoreGsmPrePagoFaixa2();
      } else if (gsmResponse.getActivationTime().get(2)) { // 1 a 3 meses
        return antifraudeInstituicao.getScoreGsmPrePagoFaixa3();
      } else if (gsmResponse.getActivationTime().get(3)) { // 0 a 1 meses
        return antifraudeInstituicao.getScoreGsmPrePagoFaixa4();
      }
    }
    return 0.0;
  }

  private Double calculateEmailScore(
      EsaferEmailResponse emailResponse,
      AntifraudeInstituicao antifraudeInstituicao,
      String emailRequest) {
    if (emailResponse == null
        || emailResponse.getResult() == null
        || emailResponse.getResult().size() == 0) {
      return 0.0;
    }
    double totalScore = 0.0;
    Optional<EsaferEmailTypeResponse> findEmail =
        emailResponse.getResult().stream()
            .filter(
                e ->
                    e.getEmailAddress() != null
                        && e.getEmailAddress().equalsIgnoreCase(emailRequest))
            .findFirst();

    EsaferEmailTypeResponse emailTypeResponse;
    if (findEmail.isPresent()) {
      emailTypeResponse = findEmail.get();
      totalScore += antifraudeInstituicao.getScoreEmailEncontrado();
    } else {
      if (antifraudeInstituicao.getScoreEmailNaoEncontrado() < 0) {
        return 0.0; // Eliminatório
      }
      emailTypeResponse = emailResponse.getResult().get(0);
      totalScore += antifraudeInstituicao.getScoreEmailNaoEncontrado();
    }

    // Question: O domínio do e-mail é válido?
    if (emailTypeResponse.getValidationStatus().equalsIgnoreCase("VALID")) {
      totalScore += antifraudeInstituicao.getScoreEmailDominioValido();
    } else {
      if (antifraudeInstituicao.getScoreEmailDominioInvalido() < 0) {
        return 0.0; // Eliminatório
      }
      totalScore += antifraudeInstituicao.getScoreEmailDominioInvalido();
    }

    // Question: O e-mail é principal?
    if (emailTypeResponse.getIsMain()) {
      totalScore += antifraudeInstituicao.getScoreEmailPrincipalSim();
    } else {
      totalScore += antifraudeInstituicao.getScoreEmailPrincipalNao();
    }

    /**
     * // Question: o e-mail é recente? if (emailTypeResponse.getIsRecent()) { totalScore +=
     * antifraudeInstituicao.getScoreEmailRecenteSim(); } else { totalScore +=
     * antifraudeInstituicao.getScoreEmailRecenteNao(); }
     *
     * <p>// Question: A conta de e-mail está ativa? if (emailTypeResponse.getIsActive()) {
     * totalScore += antifraudeInstituicao.getScoreEmailAtivoSim(); } else { totalScore +=
     * antifraudeInstituicao.getScoreEmailAtivoNao(); }
     *
     * <p>// Question: Quantidade de sucessos nas verificações do e-mail double verificationScore =
     * 0.0; if (Integer.parseInt(emailTypeResponse.getEmailBadPassages()) > 0) { verificationScore =
     * antifraudeInstituicao.getScoreEmailValidacoesComErro(); } if
     * (Integer.parseInt(emailTypeResponse.getLast18MonthsPassages()) > 0) { verificationScore =
     * antifraudeInstituicao.getScoreEmailValidacoes18Meses(); } if
     * (Integer.parseInt(emailTypeResponse.getLast12MonthsPassages()) > 0) { verificationScore =
     * antifraudeInstituicao.getScoreEmailValidacoes12Meses(); } if
     * (Integer.parseInt(emailTypeResponse.getLast6MonthsPassages()) > 0) { verificationScore =
     * antifraudeInstituicao.getScoreEmailValidacoes6Meses(); } if
     * (Integer.parseInt(emailTypeResponse.getLast3MonthsPassages()) > 0) { verificationScore =
     * antifraudeInstituicao.getScoreEmailValidacoes3Meses(); } totalScore += verificationScore;
     *
     * <p>// Question: Data de criação da conta // Older formatter MMM dd, yyyy HH:mm:ss a
     * DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"); long
     * creationMonthsBetween = ChronoUnit.MONTHS.between(
     * YearMonth.from(LocalDate.parse(emailTypeResponse.getCreationDate(), formatter)),
     * YearMonth.from(LocalDate.now()) ); double creationMonthScore = 0.0; if (creationMonthsBetween
     * <= 3) { creationMonthScore = antifraudeInstituicao.getScoreEmailCriacaoMenor3(); } else if
     * (creationMonthsBetween <= 6) { creationMonthScore =
     * antifraudeInstituicao.getScoreEmailCriacaoMenor6(); } else if (creationMonthsBetween <= 9) {
     * creationMonthScore = antifraudeInstituicao.getScoreEmailCriacaoMenor9(); } else {
     * creationMonthScore = antifraudeInstituicao.getScoreEmailCriacaoMaior9(); } totalScore +=
     * creationMonthScore;
     *
     * <p>// Question: Data da última atualização na conta long attMonthsBetween =
     * ChronoUnit.MONTHS.between(
     * YearMonth.from(LocalDate.parse(emailTypeResponse.getLastUpdateDate(), formatter)),
     * YearMonth.from(LocalDate.now()) ); double attMonthScore = 0.0; if (attMonthsBetween <= 3) {
     * attMonthScore = antifraudeInstituicao.getScoreEmailAtualizacaoMenor3(); } else if
     * (attMonthsBetween <= 6) { attMonthScore =
     * antifraudeInstituicao.getScoreEmailAtualizacaoMenor6(); } else if (attMonthsBetween <= 9) {
     * attMonthScore = antifraudeInstituicao.getScoreEmailAtualizacaoMenor9(); } else {
     * attMonthScore = antifraudeInstituicao.getScoreEmailAtualizacaoMaior9(); } totalScore +=
     * attMonthScore;
     */
    return totalScore;
  }

  private Double calculateReceitaScore(
      EsaferReceitaResponse receitaResponse, AntifraudeInstituicao antifraudeInstituicao) {
    // Question: CPF é válido ?
    if (receitaResponse.getResult() != null
        && receitaResponse.getResult().size() > 0
        && receitaResponse.getResult().get(0).getBasicData() != null
        && receitaResponse
            .getResult()
            .get(0)
            .getBasicData()
            .getTaxIdStatus()
            .equalsIgnoreCase("REGULAR")) {
      return antifraudeInstituicao.getScoreReceitaDocumentacaoValida();
    }

    return antifraudeInstituicao.getScoreReceitaDocumentacaoInvalida();
  }

  private AntifraudeCliente findOrCreateAntifraudeCliente(
      Long idInstituicao, String documento, String documentoRepresentante) {
    AntifraudeCliente antifraudeCliente;
    if (documentoRepresentante != null) {
      antifraudeCliente =
          repository
              .findFirstByIdInstituicaoAndDocumentoAndDocumentoRepresentanteOrderByDataUltimaVerificacaoDesc(
                  idInstituicao, formatString(documento), formatString(documentoRepresentante));
    } else {
      antifraudeCliente =
          repository.findFirstByIdInstituicaoAndDocumentoOrderByDataUltimaVerificacaoDesc(
              idInstituicao, formatString(documento));
    }
    if (antifraudeCliente == null) {
      antifraudeCliente = new AntifraudeCliente();
      antifraudeCliente.setIdInstituicao(idInstituicao);
      antifraudeCliente.setDocumento(documento);
      antifraudeCliente.setStandby(false);
      antifraudeCliente.setDataPrimeiraVerificacao(LocalDateTime.now());
      antifraudeCliente.setStatus(AntifraudeClienteStatusEnum.PENDING.getStatus());
      antifraudeCliente.setForcarValidacao(false);
      antifraudeCliente.setRefazerValidacaoReceita(false);
      if (documentoRepresentante != null) {
        antifraudeCliente.setDocumentoRepresentante(documentoRepresentante);
      }
      antifraudeCliente = repository.save(antifraudeCliente);
    }
    return antifraudeCliente;
  }

  public AntifraudeInstituicao findInstituicaoConfiguration(Long idInstituicao) {
    AntifraudeInstituicao instituicao =
        antifraudeInstituicaoRepository.findById(idInstituicao).orElse(null);
    if (instituicao != null) {
      return instituicao;
    } else {
      throw new GenericServiceException(
          "[ANTIFRAUDE LOG] Configuração não encontrada para instituicão: " + idInstituicao);
    }
  }

  public Boolean findInstituicaoConfig(Long idInstituicao) {
    AntifraudeInstituicao instituicao =
        antifraudeInstituicaoRepository.findById(idInstituicao).orElse(null);
    return instituicao != null;
  }

  private String formatString(String text) {
    return text.replaceAll("[^0-9]+", "");
  }

  public List<DocumentoOCRVO> getDocumentosOCRPJ(Long idConta) {
    List<DocumentoOCRVO> listaDocumentos = new ArrayList<>();

    List<RepresentanteLegal> representantes = representanteLegalService.findByIdConta(idConta);
    for (RepresentanteLegal representante : representantes) {
      DocumentoOCRVO docs = getDocumentosOCRRepresentante(idConta, representante.getCpf());
      docs.setRepresentante(representante);
      listaDocumentos.add(docs);
    }

    return listaDocumentos;
  }

  public DocumentoOCRVO getDocumentosOCRRepresentante(Long idConta, String cpf) {
    ContaPagamento contaPagamento =
        contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta);
    List<ContaPessoa> contasPessoa = contaPagamento.getContasPessoa();

    if (!contasPessoa.isEmpty()) {
      String documento = contasPessoa.get(0).getPessoa().getDocumento();
      String pasta = issuerDirOcr;
      DocumentoOCRVO documentoOCRVO = new DocumentoOCRVO();

      String caminhoInstituicao = pasta + contaPagamento.getIdInstituicao();
      String pastaImages = caminhoInstituicao + "/" + documento + "/" + cpf;

      File folder = new File(pastaImages);
      File[] listOfFiles = folder.listFiles();

      if (listOfFiles != null) {
        Arrays.stream(listOfFiles)
            .forEach(
                file -> {
                  if (file.getName().contains("-front")) {
                    documentoOCRVO.setImageFront(pastaImages + "/" + file.getName());
                  }
                  if (file.getName().contains("-back")) {
                    documentoOCRVO.setImageBack(pastaImages + "/" + file.getName());
                  }
                });
      }

      AntifraudeCliente antifraudeCliente =
          repository.findByIdInstituicaoAndDocumentoAndDocumentoRepresentante(
              Long.valueOf(contaPagamento.getIdInstituicao()), documento, cpf);
      documentoOCRVO.setAntifraudeCliente(antifraudeCliente);

      return documentoOCRVO;

    } else {
      throw new GenericServiceException("ContaPessoa não encontrada para conta informada!");
    }
  }

  public DocumentoOCRVO getDocumentosOCR(Long idConta) {
    ContaPagamento contaPagamento =
        contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta);
    List<ContaPessoa> contasPessoa = contaPagamento.getContasPessoa();

    if (!contasPessoa.isEmpty()) {
      String documento = contasPessoa.get(0).getPessoa().getDocumento();
      String pasta = issuerDirOcr;
      DocumentoOCRVO documentoOCRVO = new DocumentoOCRVO();

      String caminhoInstituicao = pasta + contaPagamento.getIdInstituicao();
      String pastaImages = caminhoInstituicao + "/" + documento;

      File folder = new File(pastaImages);
      File[] listOfFiles = folder.listFiles();

      if (listOfFiles != null) {
        Arrays.stream(listOfFiles)
            .forEach(
                file -> {
                  if (file.getName().contains("-front")) {
                    documentoOCRVO.setImageFront(pastaImages + "/" + file.getName());
                  }
                  if (file.getName().contains("-back")) {
                    documentoOCRVO.setImageBack(pastaImages + "/" + file.getName());
                  }
                });
      }

      AntifraudeCliente antifraudeCliente =
          repository.findByIdInstituicaoAndDocumento(
              Long.valueOf(contaPagamento.getIdInstituicao()), documento);
      documentoOCRVO.setAntifraudeCliente(antifraudeCliente);

      return documentoOCRVO;

    } else {
      throw new GenericServiceException("ContaPessoa não encontrada para conta informada!");
    }
  }

  public InputStream abrirDocumentoOCR(HashMap<String, String> request) {
    String caminhoDocumento = request.get("urlDocumento");
    return imagemService.buscarImagem(caminhoDocumento);
  }

  public void forcarValidacao(Long idAntifraudeCliente) {
    Optional<AntifraudeCliente> antifraudeClienteOptional =
        repository.findByIdAntifraudeClient(idAntifraudeCliente);
    if (antifraudeClienteOptional.isPresent()) {
      AntifraudeCliente antifraudeCliente = antifraudeClienteOptional.get();
      antifraudeCliente.setForcarValidacao(Boolean.TRUE);
      antifraudeCliente.setOcrValidado(Boolean.TRUE);
      antifraudeCliente.setStatus(AntifraudeClienteStatusEnum.APPROVED.getStatus());
      repository.save(antifraudeCliente);

      invalidarPortadoresDispositivo(antifraudeCliente);

    } else {
      throw new GenericServiceException("AntifraudeCliente nao encontrado pelo id!");
    }
  }

  private void invalidarPortadoresDispositivo(AntifraudeCliente antifraudeCliente) {
    List<PortadorDispositivo> portadorDispositivos =
        portadorDispositivoService
            .buscarPortadorDispositivosPorDocumentoAndIdInstituicaoAndDataInvalidadoIsNull(
                antifraudeCliente.getDocumento(), antifraudeCliente.getIdInstituicao());

    if (portadorDispositivos != null && !portadorDispositivos.isEmpty()) {
      for (PortadorDispositivo portadorDispositivo : portadorDispositivos) {
        portadorDispositivo.setDataInvalidado(LocalDateTime.now());
      }
      portadorDispositivoService.saveAll(portadorDispositivos);
    }
  }

  public DocumentoOCRPropostaVO findDocumentoAntifraudeProposta(Proposta proposta) {

    String documento = proposta.getDocumento();
    String pasta = issuerDirOcr;
    DocumentoOCRPropostaVO documentoOCRVO = new DocumentoOCRPropostaVO();

    String caminhoInstituicao = pasta + proposta.getIdInstituicao();
    String pastaImages = caminhoInstituicao + "/" + documento;

    File folder = new File(pastaImages);
    File[] listOfFiles = folder.listFiles();

    if (listOfFiles != null) {
      Arrays.stream(listOfFiles)
          .forEach(
              file -> {
                if (file.getName().contains("-front")) {
                  documentoOCRVO.setImageFront(pastaImages + "/" + file.getName());
                  documentoOCRVO.setFileNameFront(file.getName());
                }
                if (file.getName().contains("-back")) {
                  documentoOCRVO.setImageBack(pastaImages + "/" + file.getName());
                  documentoOCRVO.setFileNameBack(file.getName());
                }
              });
    }

    return documentoOCRVO;
  }

  public AntifraudeCliente salvar(AntifraudeCliente antifraudeCliente) {
    return repository.save(antifraudeCliente);
  }

  private OcrResponseVO montarResponseKreditOCR(AntifraudeValidateClientRequest request) {
    Pessoa pessoa =
        pessoaService.findFirstByIdProcessadoraAndIdInstituicaoAndDocumento(
            Constantes.ID_PROCESSADORA_ITS_PAY,
            request.getIdInstituicao().intValue(),
            request.getDocumento());
    if (pessoa == null) {
      throw new GenericServiceException("Pessoa não encontrada para informações enviadas");
    }
    OcrResponseVO ocrResponseVO = new OcrResponseVO();
    List<EsaferOcrResponse> listEsafer = new ArrayList<>();
    List<EsaferOcrResult> esaferOcrResultList = new ArrayList<>();
    List<EsaferOcrField> fields = new ArrayList<>();

    EsaferOcrResponse esaferOcrResponse = new EsaferOcrResponse();
    EsaferOcrResult esaferOcrResult = new EsaferOcrResult();

    List<String> tags = new ArrayList<>();
    String tag = "cnh_frente";
    tags.add(tag);

    esaferOcrResponse.setStatus("Ok");
    esaferOcrResponse.setStatusCode("200");
    esaferOcrResponse.setElapsedMilliseconds("12500");
    esaferOcrResponse.setQueryId("OcrValidoAutomaticamente");

    esaferOcrResult.setDeskewImage("");
    esaferOcrResult.setDocQuality("bom");
    esaferOcrResult.setDocQualityScore(0.90);
    esaferOcrResult.setDocType("cnh_frente");
    esaferOcrResult.setPageIndex(1L);
    esaferOcrResult.setTables(null);
    esaferOcrResult.setTags(tags);

    if (pessoa.getNomeCompleto() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("nome");
      field.setScore(0.90);
      field.setValue(pessoa.getNomeCompleto());
      fields.add(field);
    }
    if (pessoa.getNomeMae() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("nome_mae");
      field.setScore(0.90);
      field.setValue(pessoa.getNomeMae());
    }
    if (pessoa.getNomePai() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("nome_pai");
      field.setScore(0.90);
      field.setValue(pessoa.getNomePai());
      fields.add(field);
    }
    if (pessoa.getDataNascimento() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("data_nascimento");
      field.setScore(0.90);
      field.setValue(pessoa.getDataNascimento().format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
      fields.add(field);
    }
    if (pessoa.getNaturalidade() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("naturalidade");
      field.setScore(0.90);
      field.setValue(pessoa.getNaturalidade());
      fields.add(field);
    }
    if (pessoa.getEnderecosPessoa() != null && !pessoa.getEnderecosPessoa().isEmpty()) {
      if (pessoa.getEnderecosPessoa().get(0).getUf() != null) {
        EsaferOcrField field = new EsaferOcrField();
        field.setName("estado");
        field.setScore(0.90);
        field.setValue(pessoa.getEnderecosPessoa().get(0).getUf());
        fields.add(field);
      }
      if (pessoa.getEnderecosPessoa().get(0).getCidade() != null) {
        EsaferOcrField field = new EsaferOcrField();
        field.setName("cidade");
        field.setScore(0.90);
        field.setValue(pessoa.getEnderecosPessoa().get(0).getCidade());
        fields.add(field);
      }
    }
    if (pessoa.getRg() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("rg");
      field.setScore(0.90);
      field.setValue(pessoa.getRg());
      fields.add(field);
    }
    if (pessoa.getRgOrgaoEmissor() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("orgao_emissor");
      field.setScore(0.90);
      field.setValue(pessoa.getRgOrgaoEmissor());
      fields.add(field);
    }
    if (pessoa.getRgUfOrgaoEmissor() != null) {
      EsaferOcrField field = new EsaferOcrField();
      field.setName("orgao_emissor");
      field.setScore(0.90);
      field.setValue(pessoa.getRgUfOrgaoEmissor());
      fields.add(field);
    }

    esaferOcrResult.setFields(fields);
    esaferOcrResponse.setResult(esaferOcrResultList);
    esaferOcrResultList.add(esaferOcrResult);
    listEsafer.add(esaferOcrResponse);

    ocrResponseVO.setStatus(1);
    ocrResponseVO.setReceitaScoreInvalido(false);
    ocrResponseVO.setResponse(listEsafer);

    return ocrResponseVO;
  }

  public VerificacaoOcrCafResponse startValidationCaf(
      CombateFraudeCaf request, Optional<String> optIdApp) throws IOException {

    AntifraudeCafInstituicaoConfig instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(request.getIdInstituicao());

    if (instituicaoConfig == null) {
      throw new GenericServiceException("Instituição não configurada para validação do OCR - CAF.");
    }

    if (request.getDocumentoRepresentante() != null
        && request.getArquivo() == null
        && !(Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_FINANCIAL, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS, request.getIdInstituicao())
            || Objects.equals(Constantes.ID_PRODUCAO_INSTITUICAO_WIZ, request.getIdInstituicao())
            || Objects.equals(Constantes.ID_PRODUCAO_QISTA, request.getIdInstituicao())
            || Objects.equals(
                Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU, request.getIdInstituicao()))) {
      throw new GenericServiceException(
          "Contrato social é obrigatório para validação do OCR - CAF.");
    }

    verificarSelfieCaf(request.getJwt());

    if (request.getDocumentoRepresentante() != null) {
      ContaPagamento contaPagamento =
          this.pessoaService.buscarPorCnpjECpjRepresentante(
              request.getIdInstituicao(),
              request.getDocumento(),
              request.getDocumentoRepresentante());

      String partSeparator = ",";
      byte[] bytes = null;
      if (!(Objects.equals(
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS, request.getIdInstituicao())
          || Objects.equals(
              Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS, request.getIdInstituicao())
          || Objects.equals(
              Constantes.ID_PRODUCAO_INSTITUICAO_FINANCIAL, request.getIdInstituicao())
          || Objects.equals(Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO, request.getIdInstituicao())
          || Objects.equals(Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK, request.getIdInstituicao())
          || Objects.equals(Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII, request.getIdInstituicao())
          || Objects.equals(
              Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS, request.getIdInstituicao())
          || Objects.equals(Constantes.ID_PRODUCAO_INSTITUICAO_WIZ, request.getIdInstituicao())
          || Objects.equals(Constantes.ID_PRODUCAO_QISTA, request.getIdInstituicao())
          || Objects.equals(
              Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU, request.getIdInstituicao()))) {
        if (request.getArquivo().contains(partSeparator)) {
          String encodedFile = request.getArquivo().split(partSeparator)[1];
          bytes = Base64.getDecoder().decode(encodedFile.getBytes(StandardCharsets.UTF_8));
        } else {
          bytes = Base64.getDecoder().decode(request.getArquivo());
        }
        InputStream inputStream = new ByteArrayInputStream(bytes);

        this.documentoContaService.createImgDocumentoConta(
            inputStream,
            "contrato-social.pdf",
            contaPagamento,
            TipoDocumentoPropostaEnum.CONTRATO_SOCIAL.getId(),
            null,
            PortadorLoginService.USUARIO_PORTADOR);
      }
    }
    request.getRequestCaf().setTemplateId(instituicaoConfig.getIdTemplate());
    VerificacaoOcrCafResponse responseCaf =
        this.combateFraudeClient.verificaDocumentosCaf(request.getRequestCaf());
    findOrCreateAntifraudeCafPortador(request, responseCaf, instituicaoConfig);

    AntifraudeCafPortador antifraudeCafPortador = null;
    if (request.getDocumentoRepresentante() != null) {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository
              .findFirstByTxDocumentoAndTxDocumentoRepresentanteAndIdCafInstituicaoConfig(
                  request.getDocumento(),
                  request.getDocumentoRepresentante(),
                  instituicaoConfig.getId());
    } else {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository.findByTxDocumentoAndIdCafInstituicaoConfig(
              request.getDocumento(), instituicaoConfig.getId());
    }

    if (antifraudeCafPortador != null
        && (Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(instituicaoConfig.getIdInstituicao())
            && optIdApp.isEmpty())) {
      this.enviarSmsPortador(instituicaoConfig, antifraudeCafPortador, null);
    }

    return responseCaf;
  }

  private void findOrCreateAntifraudeCafPortador(
      CombateFraudeCaf request,
      VerificacaoOcrCafResponse responseCaf,
      AntifraudeCafInstituicaoConfig instituicaoConfig) {

    AntifraudeCafPortador antifraudeCafPortador;

    if (request.getDocumentoRepresentante() != null) {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository
              .findFirstByTxDocumentoAndTxDocumentoRepresentanteAndIdCafInstituicaoConfig(
                  request.getDocumento(),
                  request.getDocumentoRepresentante(),
                  instituicaoConfig.getId());
    } else {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository.findFirstByTxDocumentoAndIdCafInstituicaoConfig(
              request.getDocumento(), instituicaoConfig.getId());
    }

    if (antifraudeCafPortador == null) {

      antifraudeCafPortador = new AntifraudeCafPortador();
      antifraudeCafPortador.setIdCafInstituicaoConfig(instituicaoConfig.getId());
      antifraudeCafPortador.setTxDocumento(request.getDocumento());
      antifraudeCafPortador.setDtHrVerificacao(LocalDateTime.now());
      antifraudeCafPortador.setBlFraude(Boolean.FALSE);
      antifraudeCafPortador.setBlIgnorarValidacao(Boolean.FALSE);
      antifraudeCafPortador.setBlForcarValidacao(Boolean.FALSE);
      antifraudeCafPortador.setTxDocumentoRepresentante(request.getDocumentoRepresentante());
    }

    antifraudeCafPortador.setIdRequest(responseCaf.getRequestId());
    antifraudeCafPortador.setIdTransaction(responseCaf.getId());
    antifraudeCafPortador.setDtHrUltimaVerificacao(LocalDateTime.now());
    antifraudeCafPortadorRepository.save(antifraudeCafPortador);
    this.logAntifraudeService.salvarLog(antifraudeCafPortador);
  }

  public DadosOcrCafResponse getStatusValidacaoCaf(
      String documento,
      Integer idInstituicao,
      TokenCaf tokenCaf,
      Optional<String> optCpfRepresentante,
      Optional<String> optIdApp)
      throws IOException {

    AntifraudeCafInstituicaoConfig instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(idInstituicao);

    if (instituicaoConfig == null) {
      throw new GenericServiceException("Instituição não configurada para validação do OCR.");
    }

    AntifraudeCafPortador antifraudeCafPortador;
    if (optCpfRepresentante.isPresent()) {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository
              .findFirstByTxDocumentoAndTxDocumentoRepresentanteAndIdCafInstituicaoConfig(
                  documento, optCpfRepresentante.get(), instituicaoConfig.getId());
    } else {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository.findByTxDocumentoAndIdCafInstituicaoConfig(
              documento, instituicaoConfig.getId());
    }
    if (antifraudeCafPortador == null) {
      throw new GenericServiceException("Portador não encontrado para validação do OCR.");
    }
    DadosOcrCafResponse response =
        this.combateFraudeClient.getStatusValidacaoCaf(antifraudeCafPortador.getIdTransaction());
    antifraudeCafPortador =
        saveDadosOcrCaf(antifraudeCafPortador, response, instituicaoConfig, tokenCaf);
    if (antifraudeCafPortador.getTxDocumentoRepresentante() != null) {
      this.enviarEmailAprovacao(instituicaoConfig, antifraudeCafPortador);
    }

    // Enviar SMS para portador
    if (Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(instituicaoConfig.getIdInstituicao())
        && !response.getStatus().equals(AntifraudeCafPortadorStatusEnum.PROCESSING.getStatus())
        && optIdApp.isEmpty()) {
      this.enviarSmsPortador(instituicaoConfig, antifraudeCafPortador, response);
    }
    return response;
  }

  private AntifraudeCafPortador saveDadosOcrCaf(
      AntifraudeCafPortador antifraudeCafPortador,
      DadosOcrCafResponse response,
      AntifraudeCafInstituicaoConfig instituicaoConfig,
      TokenCaf tokenCaf)
      throws IOException {

    ImagesCafResponseVO images = response.getImages();

    if (tokenCaf == null) {
      antifraudeCafPortador.setTxUrlSelfie(images.getSelfie());
    } else {
      TokenCafDTO tokenCafDTO = verificarSelfieCaf(tokenCaf.getJwt());
      antifraudeCafPortador.setTxUrlSelfie(tokenCafDTO.getImageUrl());
    }

    antifraudeCafPortador.setBlFraude(response.getFraud());
    antifraudeCafPortador.setIdStatus(response.getStatus());
    antifraudeCafPortador.setDtHrStatus(new Date());
    antifraudeCafPortador.setIdTipoDoc(response.getType());
    antifraudeCafPortador.setTxUrlFrontDoc(images.getFront());
    antifraudeCafPortador.setTxUrlBackDoc(images.getBack());
    antifraudeCafPortador.setTxResponse(new ObjectMapper().writeValueAsString(response));
    antifraudeCafPortador.setBlForcarValidacao(false);

    return antifraudeCafPortadorRepository.save(antifraudeCafPortador);
  }

  private AntifraudeCafPortador consultaDadosOcrCaf(
      AntifraudeCafPortador antifraudeCafPortador,
      DadosOcrCafResponse response,
      AntifraudeCafInstituicaoConfig instituicaoConfig,
      TokenCaf tokenCaf)
      throws IOException {

    ImagesCafResponseVO images = response.getImages();

    if (tokenCaf == null) {
      antifraudeCafPortador.setTxUrlSelfie(images.getSelfie());
    } else {
      TokenCafDTO tokenCafDTO = verificarSelfieCaf(tokenCaf.getJwt());
      antifraudeCafPortador.setTxUrlSelfie(tokenCafDTO.getImageUrl());
    }
    antifraudeCafPortador.setTxUrlFrontDoc(images.getFront());
    antifraudeCafPortador.setTxUrlBackDoc(images.getBack());
    return antifraudeCafPortador;
  }

  public AntifraudeCafPortador buscarCaf(
      String documento, Integer idInstituicao, String documentoRepresentante) throws IOException {
    AntifraudeCafInstituicaoConfig instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(idInstituicao);
    if (instituicaoConfig == null) {
      throw new GenericServiceException("Instituição não configurada para validação do OCR - CAF.");
    }
    AntifraudeCafPortador antifraudeCafPortador;
    if (documentoRepresentante != null) {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository
              .findFirstByTxDocumentoAndTxDocumentoRepresentanteAndIdCafInstituicaoConfig(
                  documento, documentoRepresentante, instituicaoConfig.getId());
    } else {
      antifraudeCafPortador =
          antifraudeCafPortadorRepository.findFirstByTxDocumentoAndIdCafInstituicaoConfig(
              documento, instituicaoConfig.getId());
    }
    return antifraudeCafPortador;
  }

  public AntifraudeCafPortador
      findByIdCafInstituicaoConfigAndTxDocumentoAndDtHrUltimaVerificacaoIsAfter(
          Long idInstituicao, String documento, LocalDateTime dataLimite) {
    // return
    // antifraudeCafPortadorRepository.findByIdCafInstituicaoConfigAndTxDocumentoAndDtHrUltimaVerificacaoIsAfter(idInstituicao, documento, dataLimite);
    return antifraudeCafPortadorRepository
        .findByIdCafInstituicaoConfigAndTxDocumentoAndDtHrUltimaVerificacaoIsAfterOrBlForcarValidacaoIsTrue(
            idInstituicao, documento, dataLimite);
  }

  public AntifraudeCafInstituicaoConfig buscarConfiguracaoCaf(Integer idInstituicao) {
    return antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(idInstituicao);
  }

  public AntifraudeCafPortador getStatusCaf(String documento, Integer idInstituicao) {
    AntifraudeCafInstituicaoConfig instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(idInstituicao);
    if (instituicaoConfig == null) {
      throw new GenericServiceException("Instituição não configurada para validação do OCR.");
    }
    return antifraudeCafPortadorRepository.findByTxDocumentoAndIdCafInstituicaoConfig(
        documento, instituicaoConfig.getId());
  }

  public Boolean alterarStatusWebhook(WebhookCaf webhookCaf) throws IOException {
    if (!webhookCaf.getType().equals("status_updated")) {
      logger.info("Webhook - Status diferente de mudança do status do onboard");
      return false;
    }
    AntifraudeCafInstituicaoConfig instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findByIdTemplate(webhookCaf.getTemplateId());
    if (instituicaoConfig == null) {
      logger.info("Webhook - Configuração do template da CAF não encontrado");
      return false;
    }
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeCafPortadorRepository.findAntifraudeCafPortadorByIdTransaction(
            webhookCaf.getUuid());
    if (antifraudeCafPortador == null) {
      logger.info("Webhook - Portador CAF não encontrado");
      return false;
    }
    DadosOcrCafResponse response =
        this.combateFraudeClient.getStatusValidacaoCaf(antifraudeCafPortador.getIdTransaction());
    antifraudeCafPortador =
        this.saveDadosOcrCaf(antifraudeCafPortador, response, instituicaoConfig, null);
    if (antifraudeCafPortador.getIdStatus() != null
        && antifraudeCafPortador
            .getIdStatus()
            .equals(AntifraudeCafPortadorStatusEnum.APPROVED.getStatus())) {
      this.enviarEmailAprovacao(instituicaoConfig, antifraudeCafPortador);
    }

    // Enviar SMS para portador
    if (Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(instituicaoConfig.getIdInstituicao())
        && !response.getStatus().equals(AntifraudeCafPortadorStatusEnum.PROCESSING.getStatus())) {
      this.enviarSmsPortador(instituicaoConfig, antifraudeCafPortador, response);
    }
    return true;
  }

  public List<AntifraudeCafPortador> findAllByContaByInstituicao(
      Integer idConta, Integer idInstituicao) throws IOException {
    List<AntifraudeCafPortador> antifraudeCafPortador =
        antifraudeCafPortadorRepository.findAllByContaByInstituicao(idConta, idInstituicao);
    List<AntifraudeCafPortador> antifraudeCafPortadorRetorno = new ArrayList<>();

    antifraudeCafPortador.stream()
        .forEach(
            antifraudeCafPortadorItem -> {
              if (antifraudeCafPortadorItem
                      .getDtHrUltimaVerificacao()
                      .isBefore(LocalDateTime.now().minusHours(1l))
                  || !AntifraudeCafPortadorStatusEnum.APPROVED
                      .getStatus()
                      .equalsIgnoreCase(antifraudeCafPortadorItem.getIdStatus())) {
                DadosOcrCafResponse response =
                    this.combateFraudeClient.getStatusValidacaoCaf(
                        antifraudeCafPortadorItem.getIdTransaction());
                try {
                  AntifraudeCafPortador antifraudeCafPortadorAtualizado =
                      this.saveDadosOcrCaf(antifraudeCafPortadorItem, response, null, null);
                  antifraudeCafPortadorRetorno.add(antifraudeCafPortadorAtualizado);
                } catch (IOException e) {
                  antifraudeCafPortadorRetorno.add(antifraudeCafPortadorItem);
                }
              } else {
                antifraudeCafPortadorRetorno.add(antifraudeCafPortadorItem);
              }
            });
    return antifraudeCafPortadorRetorno;
  }

  public List<AntifraudeCafPortador> findAllByContaByInstituicaoSemConsultaCAF(
      Integer idConta, Integer idInstituicao) throws IOException {
    List<AntifraudeCafPortador> antifraudeCafPortador =
        antifraudeCafPortadorRepository.findAllByContaByInstituicao(idConta, idInstituicao);
    List<AntifraudeCafPortador> antifraudeCafPortadorRetorno = new ArrayList<>();

    antifraudeCafPortador.stream()
        .forEach(
            antifraudeCafPortadorItem -> {
              DadosOcrCafResponse response =
                  this.combateFraudeClient.getStatusValidacaoCaf(
                      antifraudeCafPortadorItem.getIdTransaction());
              try {
                AntifraudeCafPortador antifraudeCafPortadorAtualizado =
                    this.consultaDadosOcrCaf(antifraudeCafPortadorItem, response, null, null);
                antifraudeCafPortadorRetorno.add(antifraudeCafPortadorAtualizado);
              } catch (IOException e) {
                antifraudeCafPortadorRetorno.add(antifraudeCafPortadorItem);
              }
            });
    return antifraudeCafPortadorRetorno;
  }

  public AntifraudeCafPortador findByContaByInstituicaoByIdAcp(
      Integer idConta, Integer idInstituicao, Integer idAcp) throws IOException {
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeCafPortadorRepository.findAllByContaByInstituicaoByIdAcp(
            idConta, idInstituicao, idAcp);
    AntifraudeCafPortador antifraudeCafPortadorAtualizado = new AntifraudeCafPortador();

    if (antifraudeCafPortador
            .getDtHrUltimaVerificacao()
            .isBefore(LocalDateTime.now().minusHours(1l))
        || !AntifraudeCafPortadorStatusEnum.APPROVED
            .getStatus()
            .equalsIgnoreCase(antifraudeCafPortador.getIdStatus())) {
      DadosOcrCafResponse response =
          this.combateFraudeClient.getStatusValidacaoCaf(antifraudeCafPortador.getIdTransaction());
      try {
        antifraudeCafPortadorAtualizado =
            this.saveDadosOcrCaf(antifraudeCafPortador, response, null, null);
      } catch (IOException e) {
        log.warn(
            ConstantesErro.CAF_ERRO_AO_SALVAR_DADOS.format(
                "Erro causado ao buscar fotos do portador."));
        return antifraudeCafPortadorAtualizado;
      }
    } else {
      return antifraudeCafPortadorAtualizado;
    }

    return antifraudeCafPortadorAtualizado;
  }

  public AntifraudeCafPortador forcarRevalidacaoCaf(Integer idAntifraude, IgnorarValidacao model) {
    Boolean forcarValidacao = true;
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeCafPortadorRepository.findById(Long.valueOf(idAntifraude)).orElse(null);
    Optional<AntifraudeCafInstituicaoConfig> instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findById(model.getIdCafInst());

    // Enviar SMS por usuário
    if (instituicaoConfig.isPresent()
        && Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(
            instituicaoConfig.get().getIdInstituicao())) {
      this.enviarSmsUser(instituicaoConfig.get(), antifraudeCafPortador, forcarValidacao, null);
    }

    if (antifraudeCafPortador != null) {
      antifraudeCafPortador.setBlForcarValidacao(true);
      return antifraudeCafPortadorRepository.save(antifraudeCafPortador);
    }

    return null;
  }

  public AntifraudeCafInstituicaoConfig getDadosConfiguracaoInstituicao(Integer idInstituicao) {
    return antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(idInstituicao);
  }

  public void enviarEmailAprovacao(
      AntifraudeCafInstituicaoConfig instituicaoConfig,
      AntifraudeCafPortador antifraudeCafPortador) {
    try {
      Pessoa pessoa =
          pessoaService.findPessoaAtualizadaRecente(
              Constantes.ID_PROCESSADORA_ITS_PAY,
              instituicaoConfig.getIdInstituicao(),
              antifraudeCafPortador.getTxDocumento());
      if (pessoa != null
          && antifraudeCafPortador.getIdStatus() != null
          && AntifraudeCafPortadorStatusEnum.APPROVED
              .getStatus()
              .equals(antifraudeCafPortador.getIdStatus())
          && Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(
              instituicaoConfig.getIdInstituicao())) {
        this.emailService.enviarEmailAprovacaoCaf(
            pessoa.getEmail(), instituicaoConfig.getIdInstituicao());
      }
    } catch (Exception e) {
      logger.error(
          "E-mail de aprovação do representante com problema, e-mail possivelmente não enviado");
    }
  }

  public Boolean registraValidacaoFacialCaf(
      String documento,
      Integer idInstituicao,
      Integer idObjetivo,
      Optional<String> optCpfRepresentante,
      TokenCaf tokenCaf) {
    AntifraudeCafFacialObjetivosEnum objetivo =
        AntifraudeCafFacialObjetivosEnum.encontraObjetivoPorId(idObjetivo);
    registroValidacaoFacialCafService.registraValidacaoBemSucedida(
        documento, idInstituicao, objetivo, optCpfRepresentante, tokenCaf);
    return true;
  }

  public AntifraudeCafPortador encontraCafAprovadoPorDocumentoEInstituicao(
      String documento, Integer idInstituicao) {
    return antifraudeCafPortadorRepository.findByDocumentoAndInstituicaoAndStatus(
        documento, idInstituicao, AntifraudeCafPortadorStatusEnum.APPROVED.getStatus());
  }

  public AntifraudeCafPortador encontraCafAprovadoPorDocumentoRepresentanteEInstituicao(
      String documento, String documentoRepresentante, Integer idInstituicao) {
    return antifraudeCafPortadorRepository
        .findByDocumentoAndInstituicaoAndDocumentoRepresentanteAndStatus(
            documento,
            documentoRepresentante,
            idInstituicao,
            AntifraudeCafPortadorStatusEnum.APPROVED.getStatus());
  }

  public AntifraudeCafPortador encontraCafAprovadoOuPendentePorDocumentoEInstituicao(
      String documento, Integer idInstituicao) {
    return antifraudeCafPortadorRepository.findByDocumentoAndInstituicaoAndStatusIn(
        documento,
        idInstituicao,
        Arrays.asList(
            AntifraudeCafPortadorStatusEnum.APPROVED.getStatus(),
            AntifraudeCafPortadorStatusEnum.PENDING.getStatus()));
  }

  public TokenCafDTO verificarSelfieCaf(String jwt) {

    if (jwt == null) {
      throw new GenericServiceException("Sem informações para validar o reconhecimento facial.");
    }

    try {
      Claims claimsJWT =
          Jwts.parser().setSigningKey(secretKeyCaf.getBytes()).parseClaimsJws(jwt).getBody();

      // Converte o Map para JSON usando Gson
      Gson gson = new Gson();
      String claims = gson.toJson(claimsJWT);

      // Desserializa o JSON em um objeto TokenCafDTO
      TokenCafDTO payload = gson.fromJson(claims, TokenCafDTO.class);

      // Verifica o campo isAlive
      if (!Boolean.TRUE.equals(payload.getIsAlive())) {
        throw new GenericServiceException("Validação de reconhecimento facial reprovado.");
      }

      return payload;

    } catch (SignatureException e) {
      throw new GenericServiceException("Não foi possível validar o reconhecimento facial.");
    } catch (Exception e) {
      throw new GenericServiceException("Erro ao processar o reconhecimento facial.");
    }
  }

  public ResponseEntity<Boolean> sendTokenRequest(
      String idCaf, AprovacaoManualCaf aprovacaoManualCaf) {
    try {
      String token = cafApiService.obterToken();
      if (token == null) {
        log.error("Falha ao obter o token para acesso à API CAF.");
        return new ResponseEntity<>(false, HttpStatus.UNAUTHORIZED);
      }

      // Alterando o status
      ResponseEntity<Object> response =
          cafApiService.alterarStatus(
              token, idCaf, aprovacaoManualCaf.getStatus(), aprovacaoManualCaf.getReason());
      if (response == null || response.getBody() == null) {
        log.error("Falha ao alterar status para idCaf " + idCaf);
        return new ResponseEntity<>(false, HttpStatus.BAD_REQUEST);
      }

      HttpStatus statusCode = response.getStatusCode();
      if (statusCode.is2xxSuccessful()) {
        return new ResponseEntity<>(true, HttpStatus.OK);
      } else {
        log.error(
            "Falha ao alterar status para idCaf " + idCaf + ". Resposta com status: " + statusCode);
        return new ResponseEntity<>(false, statusCode);
      }
    } catch (Exception e) {
      log.error("Erro ao enviar requisição de token ou alterar status: " + e.getMessage(), e);
      return new ResponseEntity<>(false, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  public ResponseEntity<HashMap<String, String>> realizarReviewCaf(
      AprovacaoManualCafVO aprovacaoManualCafVO) {
    HashMap<String, String> map = new HashMap<>();

    if (aprovacaoManualCafVO.getTransactionId() == null
        || aprovacaoManualCafVO.getAction() == null) {
      log.error(ConstantesErro.CAF_TRANSACTION_ID_OU_ACTION_NULO.getMensagem());
      throw new GenericServiceException(
          ConstantesErro.CAF_ERRO_AO_ALTERAR_STATUS.format(
              "Validar campos ou entre em contato com o suporte."),
          HttpStatus.BAD_REQUEST);
    } else if ((aprovacaoManualCafVO.getAction() != null
            && aprovacaoManualCafVO.getAction().equals("REPROVE"))
        && aprovacaoManualCafVO.getReason() == null) {
      log.error(ConstantesErro.CAF_CAMPO_REASON_NULO.getMensagem());
      throw new GenericServiceException(
          ConstantesErro.CAF_ERRO_AO_ALTERAR_STATUS.format(
              "Motivo da reprovação deve ser informado."),
          HttpStatus.BAD_REQUEST);
    }

    ReviewManualCafRequest reviewManualCafRequest =
        cafApiService.prepararReviewCafRequest(
            aprovacaoManualCafVO.getAction(), aprovacaoManualCafVO.getReason());
    ResponseEntity<AlteracaoStatusCafResponse> response =
        combateFraudeClient.realizarAlteracaoStatusNaCaf(
            reviewManualCafRequest, aprovacaoManualCafVO.getTransactionId());

    Optional<AntifraudeCafInstituicaoConfig> instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findById(aprovacaoManualCafVO.getCafInstituicao());
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeCafPortadorRepository.findById(
            aprovacaoManualCafVO.getIdCaf(), aprovacaoManualCafVO.getCafInstituicao());

    // Enviar SMS por usuário
    if (instituicaoConfig.isPresent()
        && antifraudeCafPortador != null
        && Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(
            instituicaoConfig.get().getIdInstituicao())) {
      this.enviarSmsUser(
          instituicaoConfig.get(), antifraudeCafPortador, null, aprovacaoManualCafVO);
    }

    map.put("msg", response.getBody().getMessage());

    return new ResponseEntity<>(map, response.getStatusCode());
  }

  public List<AntifraudeCafPortador> buscarSolicitacoesCaf(
      Integer idInstituicao, SecurityUser user) {
    if (!user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.PROCESSADORA.getIdNivelHierarquia())
        && !user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.INSTITUICAO.getIdNivelHierarquia())) {
      throw new AccessDeniedException("O usuário não possui permissão para ação");
    }
    return antifraudeCafPortadorRepository.findAllByInstituicao(idInstituicao);
  }

  public Long contarSolicitacoesCaf(
      PortadoresCafFiltroVO filtroVO, Integer idInstituicao, SecurityUser user) {
    if (!user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.PROCESSADORA.getIdNivelHierarquia())
        && !user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.INSTITUICAO.getIdNivelHierarquia())) {
      throw new AccessDeniedException("O usuário não possui permissão para ação");
    }
    return antifraudeCafPortadorRepository.buscarListaPortadoresCaf(
        filtroVO, idInstituicao, Long.class);
  }

  public List<AntifraudeCafPortadorInfoViewVO> buscaTodos() {
    return antifraudeCafPortadorRepository.resgataTodasInformacoesAntifraudeCafPortador();
  }

  public List<AntifraudeCafPortadorInfoViewVO> buscaTodosCustom(
      Integer idInstituicao, Integer first, Integer max) {
    return antifraudeCafPortadorRepository.resgataTodasInformacoesAntifraudeCafPortadorCustom(
        idInstituicao, first, max);
  }

  public List<AntifraudeCafPortadorInfoViewVO> buscaPorID(Long idAntifraudeCafPortador) {
    return antifraudeCafPortadorRepository.resgataTodasInformacoesAntifraudeCafPortadorPorId(
        idAntifraudeCafPortador);
  }

  public List<AntifraudeCafPortadorInfoViewVO> buscaPorIDCustom(Long idAntifraudeCafPortador) {
    return antifraudeCafPortadorRepository.resgataTodasInformacoesAntifraudeCafPortadorPorIdCustom(
        idAntifraudeCafPortador);
  }

  public List<AntifraudeCafPortadorInfoViewVO> buscaPorDocumento(String documento) {
    return antifraudeCafPortadorRepository.resgataTodasInformacoesAntifraudeCafPortadorPorDocumento(
        documento);
  }

  public List<AntifraudeCafPortadorInfoViewVO> buscaPorDocumentoCustom(String documento) {
    return antifraudeCafPortadorRepository
        .resgataTodasInformacoesAntifraudeCafPortadorPorDocumentoCustom(documento);
  }

  public List<AntifraudeCafPortadorIssuerVO> buscarTodasInformacoesAntifraudeCafPortadores(
      PortadoresCafFiltroVO filtroVO, Integer idInstituicao, SecurityUser user) {

    List<AntifraudeCafPortadorIssuerVO> antifraudeCafPortadorIssuerVOList = new ArrayList<>();
    try {
      List<AntifraudeCafPortador> portadoresCafPaginado =
          this.buscarSolicitacoesByIdInstituicaoPaginado(filtroVO, idInstituicao, user);

      for (AntifraudeCafPortador portador : portadoresCafPaginado) {

        AntifraudeCafPortadorIssuerVO antifraudeCafPortadorIssuerVO =
            new AntifraudeCafPortadorIssuerVO();

        List<ContaPagamentoCafVO> contaPagamentoCafVOList =
            this.contaPagamentoService.buscarDadosContaCaf(
                idInstituicao, portador.getTxDocumento());

        ViewCafportSectionsOcrInfo vcsoi =
            antifraudeCafPortadorRepository.findViewOcrInfoByIdAntifraudePortador(portador.getId());
        ViewCafportSectionsCpfInfo vcsci =
            antifraudeCafPortadorRepository.findViewCpfInfoByIdAntifraudePortador(portador.getId());
        ViewCafportSectionsFacematchInfo vcsfi =
            antifraudeCafPortadorRepository.findViewFacematchInfoByIdAntifraudePortador(
                portador.getId());
        List<ViewCafportHistoryInfo> vchi =
            antifraudeCafPortadorRepository.findViewCafportHistoryInfo(portador.getId());
        List<ViewCafportStatusReasonsInfo> vcsri =
            antifraudeCafPortadorRepository.findViewCafportViewStatusReasonsInfo(portador.getId());
        ViewCafportJson vcj =
            antifraudeCafPortadorRepository.findViewCafPortJsonInfoByIdAntifraudePortador(
                portador.getId());
        ViewCafportAttributesInfo vai =
            antifraudeCafPortadorRepository.findViewAttributesInfoByIdAntifraudePortador(
                portador.getId());

        if (vcsfi.getConfianca() != null) {
          double doubleConfiaca = Double.parseDouble(vcsfi.getConfianca());
          BigDecimal bigDecimalConfianca = BigDecimal.valueOf(doubleConfiaca * 100);
          antifraudeCafPortadorIssuerVO.setConfianca(
              String.format("%.2f", bigDecimalConfianca) + "%");
        } else {
          antifraudeCafPortadorIssuerVO.setConfianca("Não registrado");
        }

        antifraudeCafPortadorIssuerVO.setIdAcp(portador.getId());
        antifraudeCafPortadorIssuerVO.setIdTransaction(portador.getIdTransaction());
        antifraudeCafPortadorIssuerVO.setTemplateId(
            vcj.getIdTemplate() != null ? vcj.getIdTemplate() : "");
        antifraudeCafPortadorIssuerVO.setIdCafInstituicaoConfig(
            portador.getIdCafInstituicaoConfig());
        antifraudeCafPortadorIssuerVO.setIdRequest(
            portador.getIdRequest() != null ? portador.getIdRequest() : "");
        antifraudeCafPortadorIssuerVO.setDataCriacao(portador.getDtHrVerificacao());
        antifraudeCafPortadorIssuerVO.setDataUltimaModificacao(portador.getDtHrUltimaVerificacao());
        antifraudeCafPortadorIssuerVO.setStatus(
            parseStatusEnToPt(vcj.getStatus() != null ? vcj.getStatus() : portador.getIdStatus()));
        antifraudeCafPortadorIssuerVO.setTipoDoc(
            portador.getIdTipoDoc() != null ? portador.getIdTipoDoc() : "");
        antifraudeCafPortadorIssuerVO.setIdentico(
            Objects.equals(vcsfi.getIdentico(), "true") ? "Sim" : "Não");
        antifraudeCafPortadorIssuerVO.setNomeCompleto(
            vcsoi.getNome() != null ? vcsoi.getNome() : "");
        antifraudeCafPortadorIssuerVO.setNomePai(
            vcsoi.getNomePai() != null ? vcsoi.getNomePai() : "");
        antifraudeCafPortadorIssuerVO.setNomeMae(
            vcsoi.getNomeMae() != null ? vcsoi.getNomeMae() : "");
        antifraudeCafPortadorIssuerVO.setDataNascimento(
            vcsoi.getDataNascimento() != null ? vcsoi.getDataNascimento() : "");
        antifraudeCafPortadorIssuerVO.setFotoFrente(
            vai.getImagemFrente() != null ? vai.getImagemFrente() : "");
        antifraudeCafPortadorIssuerVO.setFotoVerso(
            vai.getImagemVerso() != null ? vai.getImagemVerso() : "");
        antifraudeCafPortadorIssuerVO.setSelfie(
            vai.getImagemSelfie() != null ? vai.getImagemSelfie() : "");
        if (portador.getTxDocumento().length() > 11) {
          Pessoa pessoaJuridica =
              pessoaService.findPessoaByDocumentoAndIdInstituicao(
                  portador.getTxDocumento(), idInstituicao);
          antifraudeCafPortadorIssuerVO.setDadosTipoConta("Conta PJ");
          antifraudeCafPortadorIssuerVO.setNomeEmpresa(
              pessoaJuridica != null
                  ? pessoaJuridica.getRazaoSocial()
                  : pessoaJuridica.getNomeFantasia());
        } else {
          antifraudeCafPortadorIssuerVO.setDadosTipoConta("Conta PF");
          antifraudeCafPortadorIssuerVO.setNomeEmpresa("-");
        }
        antifraudeCafPortadorIssuerVO.setCpf(vcsoi.getCpf() != null ? vcsoi.getCpf() : "");
        antifraudeCafPortadorIssuerVO.setDataEmissaoCpf(
            vcsci.getDataEmissao() != null ? vcsci.getDataEmissao() : "");
        antifraudeCafPortadorIssuerVO.setRg(vcsoi.getRg() != null ? vcsoi.getRg() : "");
        antifraudeCafPortadorIssuerVO.setOrgaoExpeditor(
            vcsoi.getAutoridadeEmissora() != null ? vcsoi.getAutoridadeEmissora() : "");
        antifraudeCafPortadorIssuerVO.setUfRg(
            vcsoi.getRgEstadoEmissao() != null ? vcsoi.getRgEstadoEmissao() : "");
        antifraudeCafPortadorIssuerVO.setCnh(
            vcsoi.getNumeroDeRegistro() != null ? vcsoi.getNumeroDeRegistro() : "");
        antifraudeCafPortadorIssuerVO.setValidadeCnh(
            vcsoi.getDataDeExpiracao() != null ? vcsoi.getDataDeExpiracao() : "");
        antifraudeCafPortadorIssuerVO.setCategoriaCnh(
            vcsoi.getCategoria() != null ? vcsoi.getCategoria() : "");

        antifraudeCafPortadorIssuerVO.setDocumentoIdentificado(
            this.pegarInformacoesStatusReasonCaf(
                vcsri, DOCUMENTO_IDENTIFICADO.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setDocumentoLegivel(
            this.pegarInformacoesStatusReasonCaf(vcsri, DOCUMENTO_LEGIVEL.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setCpfPresente(
            this.pegarInformacoesStatusReasonCaf(vcsri, COF_PRESENTE.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setSelfiePresente(
            this.pegarInformacoesStatusReasonCaf(vcsri, SELFIE_PRESENTE.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setNumeroCpfValido(
            this.pegarInformacoesStatusReasonCaf(vcsri, NUMERO_CPF_VALIDO.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setFacematch(
            this.pegarInformacoesStatusReasonCaf(vcsri, FACEMATCH.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setConsultaDeCpf(
            this.pegarInformacoesStatusReasonCaf(vcsri, CONSULTA_DE_CPF.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setCpfRegular(
            this.pegarInformacoesStatusReasonCaf(vcsri, CPF_REGULAR.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setNomesEquivalentes(
            this.pegarInformacoesStatusReasonCaf(vcsri, NOMES_EQUIVALENTES.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setObito(
            this.pegarInformacoesStatusReasonCaf(vcsri, OBITO.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setPresencaEmSancoes(
            this.pegarInformacoesStatusReasonCaf(vcsri, PRESENCA_EM_SANCOES.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setTemSancoesEncontradasOFAC(
            this.pegarInformacoesStatusReasonCaf(
                vcsri, TEM_SANCOES_ENCONTRADAS_OFAC.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setDocNaoECarteiraDeTrabalho(
            this.pegarInformacoesStatusReasonCaf(
                vcsri, DOC_NAO_E_CARTEIRA_DE_TRABALHO.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setDocNaoEPassaporte(
            this.pegarInformacoesStatusReasonCaf(vcsri, DOC_NAO_E_PASSAPORTE.getDescricaoCodigo()));
        antifraudeCafPortadorIssuerVO.setSolicitacaoRevisaoSelfieManual(
            this.pegarInformacoesStatusReasonCaf(
                vcsri, SOLICITACAO_REVISAO_SELFIE_MANUAL.getDescricaoCodigo()));

        antifraudeCafPortadorIssuerVO.setIdConta(contaPagamentoCafVOList.get(0).getIdConta());
        antifraudeCafPortadorIssuerVO.setTipoPessoa(contaPagamentoCafVOList.get(0).getTipoPessoa());

        checarRevisaManual(vcsri, antifraudeCafPortadorIssuerVO);

        antifraudeCafPortadorIssuerVO.setTxtRegrasDeCompliance(
            this.verificaSeTodasAsRegrasEstaoSatisfeitas(vcsri));

        antifraudeCafPortadorIssuerVOList.add(antifraudeCafPortadorIssuerVO);
      }
    } catch (Exception e) {
      e.printStackTrace();
      throw new GenericServiceException("Não foi possível realizar busca de dados CAF.");
    }

    antifraudeCafPortadorIssuerVOList.sort(
        Comparator.comparing(AntifraudeCafPortadorIssuerVO::getDataUltimaModificacao));
    return antifraudeCafPortadorIssuerVOList;
  }

  private String pegarInformacoesStatusReasonCaf(
      List<ViewCafportStatusReasonsInfo> vcsri, String codigo) {
    ViewCafportStatusReasonsInfo viewStatus = new ViewCafportStatusReasonsInfo();
    for (int i = 0; i < vcsri.size(); i++) {
      if (vcsri.get(i).getCodigo() != null && vcsri.get(i).getCodigo().equals(codigo)) {
        viewStatus = vcsri.get(i);
      }
    }
    return parseStatusEnToPt(viewStatus.getStatusResultado());
  }

  private String parseStatusEnToPt(String termo) {
    String status = "";
    if (termo == null || termo.isEmpty()) {
      return "";
    } else {
      termo = termo.replace("\"", "");
      switch (termo) {
        case "APPROVED":
          status = "APROVADO";
          break;
        case "REPROVED":
          status = "REPROVADO";
          break;
        case "PENDING":
          status = "PENDENTE";
          break;
        case "PROCESSING":
          status = "PROCESSANDO";
          break;
      }
    }
    return status;
  }

  /**
   * Contabiliza quantidade de status aprovado, caso tenha 14 (quantidade minima necessaria de
   * acordo com compliance) salva mensagem de todas as refgras fora satisfeitas.
   *
   * @param vcsri
   * @return
   */
  private String verificaSeTodasAsRegrasEstaoSatisfeitas(List<ViewCafportStatusReasonsInfo> vcsri) {
    int countCriterioMinimoEstabelecido = 0;
    int qtdCriterioMinimoEstabelecido = 14;

    String mensagem = "";
    CodigoStatusReasonCafEnum codigoStatusReasonCafEnum = null;

    for (int i = 0; i < vcsri.size(); i++) {
      if (vcsri.get(i).getStatusResultado() != null && vcsri.get(i).getCodigo() != null) {
        if (vcsri.get(i).getStatusResultado().equals("APPROVED")
            && vcsri
                .get(i)
                .getCodigo()
                .equals(valueOfDescricao(vcsri.get(i).getCodigo()).getDescricaoCodigo())
            && valueOfDescricao(vcsri.get(i).getCodigo()).getObrigatorio() == true) {
          countCriterioMinimoEstabelecido += 1;
        }
      }
    }

    if (countCriterioMinimoEstabelecido >= qtdCriterioMinimoEstabelecido) {
      mensagem = "Todas as regras mínimas obrigatórias foram satisfeitas";
    } else {
      mensagem = "Todas as regras mínimas obrigatórias não foram satisfeitas";
    }

    return mensagem;
  }

  private AntifraudeCafPortadorIssuerVO checarRevisaManual(
      List<ViewCafportStatusReasonsInfo> vcsri,
      AntifraudeCafPortadorIssuerVO antifraudeCafPortadorIssuerVO) {
    for (ViewCafportStatusReasonsInfo view : vcsri) {
      if (view.getCategoria().equals("MANUAL_REVIEW")) {
        ViewCafportManualReprovalReasonsInfo manualReproval =
            antifraudeCafPortadorRepository.findViewCafportManualReprovalReasonsInfo(
                view.getIdAcp());
        antifraudeCafPortadorIssuerVO.setStatusRevisaoManual(
            parseStatusEnToPt(view.getStatusResultado()));
        antifraudeCafPortadorIssuerVO.setRevisaoManualDescricao(
            manualReproval.getMotivo() != null
                ? manualReproval.getMotivo()
                : view.getDescricao() != null ? view.getDescricao() : "");
        antifraudeCafPortadorIssuerVO.setRevisaoManualData(view.getData());
      }
    }

    return antifraudeCafPortadorIssuerVO;
  }

  public AntifraudeCafPortadorIssuerVO buscarInformacoesPortadorCaf(
      Long idAcp, Integer idInstituicao) {

    AntifraudeCafPortador portador = antifraudeCafPortadorRepository.findViewCafPortadorInfo(idAcp);

    List<ContaPagamentoCafVO> contaPagamentoCafVOList =
        this.contaPagamentoService.buscarDadosContaCaf(idInstituicao, portador.getTxDocumento());
    AntifraudeCafPortadorIssuerVO antifraudeCafPortadorIssuerVO =
        new AntifraudeCafPortadorIssuerVO();
    ViewCafportSectionsOcrInfo vcsoi =
        antifraudeCafPortadorRepository.findViewOcrInfoByIdAntifraudePortador(idAcp);
    ViewCafportSectionsCpfInfo vcsci =
        antifraudeCafPortadorRepository.findViewCpfInfoByIdAntifraudePortador(idAcp);
    ViewCafportSectionsFacematchInfo vcsfi =
        antifraudeCafPortadorRepository.findViewFacematchInfoByIdAntifraudePortador(idAcp);
    List<ViewCafportHistoryInfo> vchi =
        antifraudeCafPortadorRepository.findViewCafportHistoryInfo(idAcp);
    List<ViewCafportStatusReasonsInfo> vcsri =
        antifraudeCafPortadorRepository.findViewCafportViewStatusReasonsInfo(idAcp);
    ViewCafportJson vcj =
        antifraudeCafPortadorRepository.findViewCafPortJsonInfoByIdAntifraudePortador(idAcp);
    ViewCafportAttributesInfo vai =
        antifraudeCafPortadorRepository.findViewAttributesInfoByIdAntifraudePortador(
            portador.getId());

    if (vcsfi.getConfianca() != null) {
      double doubleConfiaca = Double.parseDouble(vcsfi.getConfianca());
      BigDecimal bigDecimalConfianca = BigDecimal.valueOf(doubleConfiaca * 100);
      antifraudeCafPortadorIssuerVO.setConfianca(String.format("%.2f", bigDecimalConfianca) + "%");
    } else {
      antifraudeCafPortadorIssuerVO.setConfianca("Não registrado");
    }

    antifraudeCafPortadorIssuerVO.setIdAcp(portador.getId());
    antifraudeCafPortadorIssuerVO.setIdTransaction(portador.getIdTransaction());
    antifraudeCafPortadorIssuerVO.setTemplateId(
        vcj.getIdTemplate() != null ? vcj.getIdTemplate() : "");
    antifraudeCafPortadorIssuerVO.setIdCafInstituicaoConfig(portador.getIdCafInstituicaoConfig());
    antifraudeCafPortadorIssuerVO.setIdRequest(
        portador.getIdRequest() != null ? portador.getIdRequest() : "");
    antifraudeCafPortadorIssuerVO.setDataCriacao(vchi.get(0).getData());
    antifraudeCafPortadorIssuerVO.setDataUltimaModificacao(
        vchi.get(vchi.size() - 1) == null
            ? portador.getDtHrUltimaVerificacao()
            : vchi.get(vchi.size() - 1).getData());
    antifraudeCafPortadorIssuerVO.setStatus(
        parseStatusEnToPt(vcj.getStatus() != null ? vcj.getStatus() : portador.getIdStatus()));
    antifraudeCafPortadorIssuerVO.setTipoDoc(
        portador.getIdTipoDoc() != null ? portador.getIdTipoDoc() : "");
    antifraudeCafPortadorIssuerVO.setIdentico(
        Objects.equals(vcsfi.getIdentico(), "true") ? "Sim" : "Não");
    antifraudeCafPortadorIssuerVO.setNomeCompleto(vcsoi.getNome() != null ? vcsoi.getNome() : "");
    antifraudeCafPortadorIssuerVO.setNomePai(vcsoi.getNomePai() != null ? vcsoi.getNomePai() : "");
    antifraudeCafPortadorIssuerVO.setNomeMae(vcsoi.getNomeMae() != null ? vcsoi.getNomeMae() : "");
    antifraudeCafPortadorIssuerVO.setDataNascimento(
        vcsoi.getDataNascimento() != null ? vcsoi.getDataNascimento() : "");
    antifraudeCafPortadorIssuerVO.setFotoFrente(
        vai.getImagemFrente() != null ? vai.getImagemFrente() : "");
    antifraudeCafPortadorIssuerVO.setFotoVerso(
        vai.getImagemVerso() != null ? vai.getImagemVerso() : "");
    antifraudeCafPortadorIssuerVO.setSelfie(
        vai.getImagemSelfie() != null ? vai.getImagemSelfie() : "");
    antifraudeCafPortadorIssuerVO.setCpf(vcsoi.getCpf() != null ? vcsoi.getCpf() : "");
    antifraudeCafPortadorIssuerVO.setDataEmissaoCpf(
        vcsci.getDataEmissao() != null ? vcsci.getDataEmissao() : "");
    antifraudeCafPortadorIssuerVO.setRg(vcsoi.getRg() != null ? vcsoi.getRg() : "");
    antifraudeCafPortadorIssuerVO.setOrgaoExpeditor(
        vcsoi.getAutoridadeEmissora() != null ? vcsoi.getAutoridadeEmissora() : "");
    antifraudeCafPortadorIssuerVO.setUfRg(
        vcsoi.getRgEstadoEmissao() != null ? vcsoi.getRgEstadoEmissao() : "");
    antifraudeCafPortadorIssuerVO.setCnh(
        vcsoi.getNumeroDeRegistro() != null ? vcsoi.getNumeroDeRegistro() : "");
    antifraudeCafPortadorIssuerVO.setValidadeCnh(
        vcsoi.getDataDeExpiracao() != null ? vcsoi.getDataDeExpiracao() : "");
    antifraudeCafPortadorIssuerVO.setCategoriaCnh(
        vcsoi.getCategoria() != null ? vcsoi.getCategoria() : "");

    antifraudeCafPortadorIssuerVO.setDocumentoIdentificado(
        this.pegarInformacoesStatusReasonCaf(vcsri, DOCUMENTO_IDENTIFICADO.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setDocumentoLegivel(
        this.pegarInformacoesStatusReasonCaf(vcsri, DOCUMENTO_LEGIVEL.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setCpfPresente(
        this.pegarInformacoesStatusReasonCaf(vcsri, COF_PRESENTE.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setSelfiePresente(
        this.pegarInformacoesStatusReasonCaf(vcsri, SELFIE_PRESENTE.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setNumeroCpfValido(
        this.pegarInformacoesStatusReasonCaf(vcsri, NUMERO_CPF_VALIDO.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setFacematch(
        this.pegarInformacoesStatusReasonCaf(vcsri, FACEMATCH.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setConsultaDeCpf(
        this.pegarInformacoesStatusReasonCaf(vcsri, CONSULTA_DE_CPF.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setCpfRegular(
        this.pegarInformacoesStatusReasonCaf(vcsri, CPF_REGULAR.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setNomesEquivalentes(
        this.pegarInformacoesStatusReasonCaf(vcsri, NOMES_EQUIVALENTES.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setObito(
        this.pegarInformacoesStatusReasonCaf(vcsri, OBITO.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setPresencaEmSancoes(
        this.pegarInformacoesStatusReasonCaf(vcsri, PRESENCA_EM_SANCOES.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setTemSancoesEncontradasOFAC(
        this.pegarInformacoesStatusReasonCaf(
            vcsri, TEM_SANCOES_ENCONTRADAS_OFAC.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setDocNaoECarteiraDeTrabalho(
        this.pegarInformacoesStatusReasonCaf(
            vcsri, DOC_NAO_E_CARTEIRA_DE_TRABALHO.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setDocNaoEPassaporte(
        this.pegarInformacoesStatusReasonCaf(vcsri, DOC_NAO_E_PASSAPORTE.getDescricaoCodigo()));
    antifraudeCafPortadorIssuerVO.setSolicitacaoRevisaoSelfieManual(
        this.pegarInformacoesStatusReasonCaf(
            vcsri, SOLICITACAO_REVISAO_SELFIE_MANUAL.getDescricaoCodigo()));

    antifraudeCafPortadorIssuerVO.setIdConta(contaPagamentoCafVOList.get(0).getIdConta());
    antifraudeCafPortadorIssuerVO.setTipoPessoa(contaPagamentoCafVOList.get(0).getTipoPessoa());

    checarRevisaManual(vcsri, antifraudeCafPortadorIssuerVO);

    antifraudeCafPortadorIssuerVO.setTxtRegrasDeCompliance(
        this.verificaSeTodasAsRegrasEstaoSatisfeitas(vcsri));

    return antifraudeCafPortadorIssuerVO;
  }

  public ResponseEntity<String> ignoraValidacaoCaf(IgnorarValidacao request, SecurityUser user) {

    AntifraudeCafPortador portador =
        antifraudeCafPortadorRepository.findById(request.getIdCaf(), request.getIdCafInst());
    portador.setBlIgnorarValidacao(request.getBlIgnorarValidacao());
    portador.setIdUsuarioManutencao(user.getIdUsuario().longValue());
    portador.setDtHrManutencao(new Date());

    antifraudeCafPortadorRepository.save(portador);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  public ResponseEntity<String> forcaAprovacao(IgnorarValidacao request, SecurityUser user) {
    Boolean forcarValidacao = false;
    Optional<AntifraudeCafInstituicaoConfig> instituicaoConfig =
        antifraudeCafInstituicaoConfigRepository.findById(request.getIdCafInst());
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeCafPortadorRepository.findById(request.getIdCaf(), request.getIdCafInst());
    antifraudeCafPortador.setIdStatus(request.getStatus());
    antifraudeCafPortador.setIdUsuarioManutencao(user.getIdUsuario().longValue());
    antifraudeCafPortador.setDtHrManutencao(new Date());

    antifraudeCafPortadorRepository.save(antifraudeCafPortador);

    // Enviar SMS
    if (instituicaoConfig.isPresent()
        && Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(
            instituicaoConfig.get().getIdInstituicao())) {
      this.enviarSmsUser(instituicaoConfig.get(), antifraudeCafPortador, forcarValidacao, null);
    }
    return new ResponseEntity<>(HttpStatus.OK);
  }

  private void montarEEnviarSMSCafPortador(
      ContaPagamento contaPagamento,
      Credencial credencialDestino,
      Pessoa pessoa,
      DadosOcrCafResponse response) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    if (contaPagamento.getIdConta() != null) {
      comunicado.setIdConta(contaPagamento.getIdConta());
    }
    GatewaySMS gateway =
        getGatewaySMS(contaPagamento.getIdProcessadora(), contaPagamento.getIdInstituicao());

    comunicado.setIdCredencial(credencialDestino.getIdCredencial());
    comunicado.setIdGatewaySMS(gateway.getIdGatewaySMS());
    String mensagem = getMensagemCafPortador(gateway, response);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(ENVIO_TOKEN_SMS);

    Long celularNumero = null;
    String celular =
        pessoa.getDddTelefoneCelular().toString().concat(pessoa.getTelefoneCelular().toString());
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = Long.valueOf(celular);
      ddd = Integer.valueOf(celular.substring(0, 2));
      restante = Integer.valueOf(celular.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celular + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celular + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        getLogEventoConta(comunicado, gateway.getIdProcessadora(), gateway.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogSMS logSms = new LogSMS();
    BeanUtils.copyProperties(comunicado, logSms);
    logSms.setNumeroCelular(new Long(celular));

    logSms.setDataHoraRegistro(new Date());
    logSms.setDataHoraAgendamento(new Date());
    logSms.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logSms.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    logSms = logSMSService.save(logSms);

    comunicado.setIdLogEventoConta(logSms.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logSms.getIdLogEventoConta());

    enviarSms(comunicado);
  }

  private void montarEEnviarCafUser(
      ContaPagamento contaPagamento,
      Credencial credencialDestino,
      Pessoa pessoa,
      Boolean forcarValidacao,
      AprovacaoManualCafVO aprovacaoManualCafVO) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    if (contaPagamento.getIdConta() != null) {
      comunicado.setIdConta(contaPagamento.getIdConta());
    }
    GatewaySMS gateway =
        getGatewaySMS(contaPagamento.getIdProcessadora(), contaPagamento.getIdInstituicao());

    comunicado.setIdCredencial(credencialDestino.getIdCredencial());
    comunicado.setIdGatewaySMS(gateway.getIdGatewaySMS());
    String mensagem = getMensagemCafUser(gateway, forcarValidacao, aprovacaoManualCafVO);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(ENVIO_TOKEN_SMS);

    Long celularNumero = null;
    String celular =
        pessoa.getDddTelefoneCelular().toString().concat(pessoa.getTelefoneCelular().toString());
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = Long.valueOf(celular);
      ddd = Integer.valueOf(celular.substring(0, 2));
      restante = Integer.valueOf(celular.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celular + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celular + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        getLogEventoConta(comunicado, gateway.getIdProcessadora(), gateway.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogSMS logSms = new LogSMS();
    BeanUtils.copyProperties(comunicado, logSms);
    logSms.setNumeroCelular(new Long(celular));

    logSms.setDataHoraRegistro(new Date());
    logSms.setDataHoraAgendamento(new Date());
    logSms.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logSms.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    logSms = logSMSService.save(logSms);

    comunicado.setIdLogEventoConta(logSms.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logSms.getIdLogEventoConta());

    enviarSms(comunicado);
  }

  private String getMensagemCafPortador(GatewaySMS gateway, DadosOcrCafResponse response) {
    String mensagem = null;

    if (response == null) {
      mensagem =
          " Estamos analisando os seus dados e em breve te avisaremos quando a analise for concluída.";
    } else {
      if (Objects.equals(
          response.getStatus(), AntifraudeCafPortadorStatusEnum.APPROVED.getStatus())) {
        mensagem =
            " Tudo certo por aqui! A análise de sua conta foi concluída. Acesse já o aplicativo com o CPF e senha cadastrada.";
      } else if (Objects.equals(
          response.getStatus(), AntifraudeCafPortadorStatusEnum.REPROVED.getStatus())) {
        mensagem =
            " Algo deu errado com seu cadastro. Revisamos seus dados e habilitamos o registro. Tente completar o processo novamente.";
      } else if (Objects.equals(
          response.getStatus(), AntifraudeCafPortadorStatusEnum.PENDING.getStatus())) {
        mensagem =
            " Precisamos revisar seu cadastro. Avisaremos por aqui assim que a analise estiver concluida. Obrigado pela paciência!";
      }
    }

    StringBuilder texto = new StringBuilder();
    String descInstituicao =
        instituicaoRepository.getDescInstituicao(
            gateway.getIdProcessadora(), gateway.getIdInstituicao());
    texto.append(descInstituicao != null ? "Banese Benefícios" + ":" : "");
    texto.append(mensagem);
    return texto.toString();
  }

  private String getMensagemCafUser(
      GatewaySMS gateway, Boolean forcarValidacao, AprovacaoManualCafVO aprovacaoManualCafVO) {
    String mensagem = null;
    if (aprovacaoManualCafVO != null) {
      mensagem =
          aprovacaoManualCafVO.getAction().equals("APPROVE")
              ? " Tudo certo por aqui! A análise de sua conta foi concluída. Acesse já o aplicativo com o CPF e senha cadastrada."
              : " Vamos precisar de uma segunda análise de seu cadastro do Banese Benefícios. Te comunicaremos por aqui quando a análise for concluída.";
    } else {
      mensagem =
          forcarValidacao
              ? " Analisamos os seus dados e habilitamos o cadastro para que faça o processo novamente."
              : " A análise de sua conta foi concluída. Acesse o aplicativo com o login e senha cadastrados.";
    }

    StringBuilder texto = new StringBuilder();
    String descInstituicao =
        instituicaoRepository.getDescInstituicao(
            gateway.getIdProcessadora(), gateway.getIdInstituicao());
    texto.append(descInstituicao != null ? "Banese Benefícios" + ":" : "");
    texto.append(mensagem);
    return texto.toString();
  }

  private boolean isCelularValido(Integer dddTelefoneCelular, Integer telefoneCelular) {
    return (dddTelefoneCelular >= MIN_DDD && dddTelefoneCelular <= MAX_DDD)
        && (telefoneCelular > MIN_CEL && telefoneCelular < MAX_CEL);
  }

  private LogEventoConta getLogEventoConta(
      ComunicadoContaViaSMS comunicado, Integer idProcessadora, Integer idInstituicao) {
    LogEventoConta evento = getLogEventoConta(comunicado);
    evento.setIdProcessadora(idProcessadora);
    evento.setIdInstituicao(idInstituicao);
    return evento;
  }

  private <Req extends ComunicadoConta> LogEventoConta getLogEventoConta(Req comunicado) {
    LogEventoConta logEvento = new LogEventoConta();
    logEvento.setIdConta(comunicado.getIdConta());
    logEvento.setIdCredencial(comunicado.getIdCredencial());
    logEvento.setTipoEvento(comunicado.getTipoEventoConta());
    return logEvento;
  }

  private GatewaySMS getGatewaySMS(Integer idProcessadora, Integer idInstituicao) {
    GatewaySMS gatewaySMS =
        gatewaySMSService.findFirstByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (gatewaySMS == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar gatewaySMS. IdProcessadora: "
              + idProcessadora
              + " , idInstituicao: "
              + idInstituicao);
    }
    return gatewaySMS;
  }

  @Async
  public void enviarSms(ComunicadoContaViaSMS comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  private void enviarSmsPortador(
      AntifraudeCafInstituicaoConfig instituicaoConfig,
      AntifraudeCafPortador antifraudeCafPortador,
      DadosOcrCafResponse response) {
    Pessoa pessoa =
        pessoaService.findPessoaAtualizadaRecente(
            Constantes.ID_PROCESSADORA_ITS_PAY,
            instituicaoConfig.getIdInstituicao(),
            antifraudeCafPortador.getTxDocumento());

    if (pessoa != null) {
      List<ContaPagamento> contas =
          contaPagamentoService.findByContasPessoaIdPessoa(pessoa.getIdPessoa());
      Credencial credencialDestino = null;
      ContaPagamento conta = null;
      if (contas != null) {
        for (ContaPagamento contaPagamento : contas) {
          credencialDestino =
              credencialService.buscarCredencialMaisRecente(
                  contaPagamento.getIdConta(),
                  pessoa.getIdPessoa(),
                  Constantes.TITULARIDADE_CREDENCIAL);
          conta = contaPagamento;
          if (credencialDestino != null) {
            break;
          }
        }

        if (credencialDestino != null) {
          this.montarEEnviarSMSCafPortador(conta, credencialDestino, pessoa, response);
        }
      }
    }
  }

  private void enviarSmsUser(
      AntifraudeCafInstituicaoConfig instituicaoConfig,
      AntifraudeCafPortador antifraudeCafPortador,
      Boolean forcarValidacao,
      AprovacaoManualCafVO aprovacaoManualCafVO) {
    Pessoa pessoa =
        pessoaService.findPessoaAtualizadaRecente(
            Constantes.ID_PROCESSADORA_ITS_PAY,
            instituicaoConfig.getIdInstituicao(),
            antifraudeCafPortador.getTxDocumento());
    List<ContaPagamento> contas =
        contaPagamentoService.findByContasPessoaIdPessoa(pessoa.getIdPessoa());
    Credencial credencialDestino = null;
    ContaPagamento conta = null;
    if (contas != null) {
      for (ContaPagamento contaPagamento : contas) {
        credencialDestino =
            credencialService.buscarCredencialMaisRecente(
                contaPagamento.getIdConta(),
                pessoa.getIdPessoa(),
                Constantes.TITULARIDADE_CREDENCIAL);
        conta = contaPagamento;
        if (credencialDestino != null) {
          break;
        }
      }

      if (credencialDestino != null) {
        this.montarEEnviarCafUser(
            conta, credencialDestino, pessoa, forcarValidacao, aprovacaoManualCafVO);
      }
    }
  }

  public List<AntifraudeCafPortador> buscarSolicitacoesByIdInstituicaoPaginado(
      PortadoresCafFiltroVO filtroVO, Integer idInstituicao, SecurityUser user) {

    if (!user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.PROCESSADORA.getIdNivelHierarquia())
        && !user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.INSTITUICAO.getIdNivelHierarquia())) {
      throw new AccessDeniedException("O usuário não possui permissão para ação");
    }

    return antifraudeCafPortadorRepository.buscarListaPortadoresCaf(
        filtroVO, idInstituicao, List.class);
  }

  public org.springframework.data.domain.Page<AntifraudeCafPortadorIssuerVO>
      buscarTodasInformacoesAntifraudeCafPortadoresFiltroDinamico(
          PortadoresCafFiltroRequest portadoresCafFiltroRequest,
          Integer idInstituicao,
          Integer max,
          Integer first) {

    Pageable pageable = PageRequest.of(first, max);

    String nomePortadorLogin = null;
    if (portadoresCafFiltroRequest.getNome() != null) {
      nomePortadorLogin = "%" + portadoresCafFiltroRequest.getNome() + "%";
    }

    String documentoPortado = null;
    if (portadoresCafFiltroRequest.getCpf() != null) {
      CPFFormatter cpfFormatter = new CPFFormatter();
      var documentoPortadoFormatado = cpfFormatter.unformat(portadoresCafFiltroRequest.getCpf());
      documentoPortado = documentoPortadoFormatado.toString();
    }

    LocalDateTime dataCadastro = null;
    String dataCadastroResilienteANulo = null;
    if (portadoresCafFiltroRequest.getData() != null
        && !portadoresCafFiltroRequest.getData().isEmpty()) {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
      dataCadastro =
          LocalDateTime.parse(
              portadoresCafFiltroRequest.getData(), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
      dataCadastroResilienteANulo = dataCadastro.format(formatter);
    }

    LocalDateTime dataCadastroFinal = null;
    String dataCadastroResilienteANuloFinal = null;
    if (portadoresCafFiltroRequest.getDataFinal() != null
        && !portadoresCafFiltroRequest.getDataFinal().isEmpty()) {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
      dataCadastroFinal =
          LocalDateTime.parse(
              portadoresCafFiltroRequest.getDataFinal(), DateTimeFormatter.ISO_LOCAL_DATE_TIME);
      dataCadastroResilienteANuloFinal = dataCadastroFinal.format(formatter);
    }

    Page<AntifraudeCafPortadorIssuerVO> antifraudeCafPortadorIssuerVO =
        this.antifraudeCafPortadorRepository.buscarAntifraudeCafPortadorsDinamico(
            idInstituicao,
            documentoPortado,
            nomePortadorLogin,
            dataCadastroResilienteANulo,
            dataCadastroResilienteANuloFinal,
            portadoresCafFiltroRequest.getStatus(),
            pageable);

    return antifraudeCafPortadorIssuerVO;
  }

  public StatusCafLoginDTO verificarStatusCafLogin(
      String cpf, Integer idInstituicao, String documentoRepresentante) throws IOException {
    AntifraudeCafPortador antifraudeCafPortador =
        this.buscarCaf(cpf, idInstituicao, documentoRepresentante);
    StatusCafLoginDTO statusCafLoginDTO = new StatusCafLoginDTO();
    statusCafLoginDTO.setIsValidacaoNecessaria(false);
    statusCafLoginDTO.setEncaminharAtendimento(false);
    statusCafLoginDTO.setOnboardRealizado(false);
    statusCafLoginDTO.setIgnorarValidacao(false);
    statusCafLoginDTO.setIsReprovado(false);

    if (antifraudeCafPortador == null) {
      statusCafLoginDTO.setIsValidacaoNecessaria(true);
    }
    if (antifraudeCafPortador != null && antifraudeCafPortador.getIdStatus() == null) {
      statusCafLoginDTO.setEncaminharAtendimento(true);
    }
    if (antifraudeCafPortador != null
        && antifraudeCafPortador.getIdStatus() != null
        && !antifraudeCafPortador
            .getIdStatus()
            .equals(AntifraudeCafPortadorStatusEnum.APPROVED.getStatus())) {
      statusCafLoginDTO.setEncaminharAtendimento(true);
    }
    if (antifraudeCafPortador != null && antifraudeCafPortador.getBlForcarValidacao()) {
      statusCafLoginDTO.setIsValidacaoNecessaria(true);
      statusCafLoginDTO.setEncaminharAtendimento(false);
    }
    if (antifraudeCafPortador != null && antifraudeCafPortador.getBlIgnorarValidacao()) {
      statusCafLoginDTO.setIsValidacaoNecessaria(false);
      statusCafLoginDTO.setEncaminharAtendimento(false);
      statusCafLoginDTO.setIgnorarValidacao(true);
    }
    if (antifraudeCafPortador != null
        && antifraudeCafPortador.getTxUrlSelfie() != null
        && !antifraudeCafPortador.getTxUrlSelfie().isEmpty()
        && AntifraudeCafPortadorStatusEnum.APPROVED
            .getStatus()
            .equals(antifraudeCafPortador.getIdStatus())) {
      statusCafLoginDTO.setEncaminharAtendimento(false);
      statusCafLoginDTO.setOnboardRealizado(true);
    }

    if (antifraudeCafPortador != null
        && antifraudeCafPortador.getTxUrlSelfie() != null
        && !antifraudeCafPortador.getTxUrlSelfie().isEmpty()
        && (AntifraudeCafPortadorStatusEnum.REPROVED
            .getStatus()
            .equals(antifraudeCafPortador.getIdStatus()))) {
      statusCafLoginDTO.setEncaminharAtendimento(true);
      statusCafLoginDTO.setOnboardRealizado(true);
      statusCafLoginDTO.setIsReprovado(true);
    }

    return statusCafLoginDTO;
  }
}
