package br.com.sinergico.service.cadastral;

import static java.util.Objects.isNull;

import br.com.client.rest.gatewaypagto.json.bean.ConsultarLinhaDigitavelTituloBRB;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTituloBRB;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTituloBRBResponse;
import br.com.client.rest.gatewaypagto.json.bean.GatewayPagtoResponse;
import br.com.entity.cadastral.AntifraudeConta;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.gatewaypagto.ContratoGatewayPagto;
import br.com.entity.gatewaypagto.ContratoGatewayPagtoInstTransacaoConfig;
import br.com.entity.gatewaypagto.LogPagtoTituloTransacao;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.transacional.CodigoTransacao;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.brb.BuscaBoletoResponse;
import br.com.json.bean.brb.BuscaSaldoResponse;
import br.com.json.bean.brb.BuscarAgenciaContaResponse;
import br.com.json.bean.brb.DadosAgenciaContaBrbPay;
import br.com.json.bean.brb.FavorecidoDTOTransferenciaBRBRequest;
import br.com.json.bean.brb.PagamentoComplementaDadosBRB;
import br.com.json.bean.brb.PagamentoComplementaDadosBRBResponse;
import br.com.json.bean.brb.PagamentoEfetivaPagamentoBRB;
import br.com.json.bean.brb.PagamentoEfetivaPagamentoBRBResponse;
import br.com.json.bean.brb.ParametrosCobrancaNPCDTOEfetivaPagamento;
import br.com.json.bean.brb.ResultadoComplementaDadosBRBResponse;
import br.com.json.bean.brb.ResultadoConsultaLinhaDigitavelBRB;
import br.com.json.bean.brb.ResultadoEfetivaPagamentoBRBResponse;
import br.com.json.bean.brb.TransferenciaContasBRBRequest;
import br.com.json.bean.brb.TransferenciaEntreContasBRBConfirmarRequest;
import br.com.json.bean.brb.TransferenciaEntreContasBRBConfirmarResponse;
import br.com.json.bean.brb.TransferenciaEntreContasBRBRequest;
import br.com.json.bean.brb.TransferenciaEntreContasBRBResponse;
import br.com.json.bean.brb.combateafraude.CAFRequest;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.client.BRBACOClient;
import br.com.sinergico.client.CombateFraudeClient;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloTransacaoRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.gatewaypagto.celcoin.GatewayPagtoCelcoinService;
import br.com.sinergico.service.jcard.JcardAntifraudeContaService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.CodigoTransacaoService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.Util;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service responsável por prover as regras de negócio referentes à integração BRBCard - ItsPay
 *
 * <AUTHOR>
 */
@Service
public class ContaPagamentoBRBService {

  private static final int ERRO_PREVISTO = -1;
  private static final int ERRO_IMPREVISTO = -99;
  private static final int MAX_LINHA_DIG = 48;
  private static final double MIN_VALOR = 0.0;
  private static final Integer ERRO_PAGAMENTO_BRB = -1;

  private static final Double MAX_VALOR = 9999999.99;

  private static Logger log = LoggerFactory.getLogger(GatewayPagtoCelcoinService.class);

  @Autowired private LogPagtoTituloValidacaoRepository logPagtoValidacaoRepository;

  @Autowired private LogPagtoTituloTransacaoRepository logPagtoTransacaoRepository;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private CodigoTransacaoService codigoTransacaoService;

  @Autowired private BRBACOClient acoClient;

  @Autowired private CombateFraudeClient combateFraudeClient;

  @Autowired private AntifraudeContaService antifraudeContaService;

  @Autowired private JcardAntifraudeContaService jcardAntifraudeContaService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private EmailService emailService;

  @Lazy @Autowired private CredencialService credencialService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private EventoService eventoService;

  /**
   * Método responsável por buscar o saldo de uma conta BrbPay
   *
   * @param idConta
   * @param cpf
   * @return
   */
  public BuscaSaldoResponse buscarSaldo(Long idConta, String cpf) {
    ContaPagamento contaOrigem = contaPagamentoService.findById(idConta);

    if (contaOrigem == null) {
      throw new GenericServiceException("Número de conta não existente.");
    }

    AntifraudeConta antifraudeConta = antifraudeContaService.findByIdConta(idConta);

    if (antifraudeConta == null) {
      throw new GenericServiceException("Antifraude não existente para conta.");
    }

    DadosAgenciaContaBrbPay agenciaEConta =
        antifraudeContaService.retornaDadosDeAgenciaEConta(antifraudeConta);

    return acoClient.consultarSaldo(
        Long.parseLong(agenciaEConta.getConta()), Long.parseLong(agenciaEConta.getAgencia()), cpf);
  }

  /**
   * Método que busca os dados da Agencia e Conta do BrbPay Através de uma Conta Pagamento e do seu
   * respectivo registrto no Antifraude Conta
   *
   * @param idConta
   * @param cpf
   * @return
   */
  public BuscarAgenciaContaResponse buscarAgenciaConta(Long idConta, String cpf) {
    ContaPagamento contaOrigem = contaPagamentoService.findById(idConta);

    if (contaOrigem == null) {
      throw new GenericServiceException("Número de conta não existente.");
    }

    AntifraudeConta antifraudeConta = antifraudeContaService.findByIdConta(idConta);

    if (antifraudeConta == null) {
      throw new GenericServiceException("Antifraude não existente para conta.");
    }

    DadosAgenciaContaBrbPay agenciaEConta =
        antifraudeContaService.retornaDadosDeAgenciaEConta(antifraudeConta);

    BuscarAgenciaContaResponse response =
        new BuscarAgenciaContaResponse(agenciaEConta.getAgencia(), agenciaEConta.getConta());

    return response;
  }

  /**
   * Método que faz a consulta de linha digitável Salvando um logPagtoTitulovalidacao no banco para
   * futuras consultas
   *
   * @param req
   * @param cpf
   * @param userPortador
   * @return
   */
  public BuscaBoletoResponse consultarLinhaDigitavel(
      ConsultarLinhaDigitavelTituloBRB req, SecurityUserPortador userPortador, String cpf) {

    BuscaBoletoResponse resultado = new BuscaBoletoResponse();

    try {
      validarLinhaDigitavel(req);
      Map<String, Object> params = getContaAndUsuarioLogado(null, userPortador, req.getIdConta());

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      Long idUsuario = params.get("idUsuario") != null ? (Long) params.get("idUsuario") : null;

      verificarPermissaoConta(conta);

      resultado = consultarLinhaDigitavel(req, conta, idUsuario, cpf);

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  /**
   * Método que faz a consulta de linha digitável Salvando um logPagtoTitulovalidacao no banco para
   * futuras consultas
   *
   * @param req
   * @param conta
   * @param idUsuario
   * @param cpf
   * @return
   */
  private BuscaBoletoResponse consultarLinhaDigitavel(
      ConsultarLinhaDigitavelTituloBRB req, ContaPagamento conta, Long idUsuario, String cpf) {

    LogPagtoTituloValidacao logPagtoVal = salvaLogPagtoTituloValidacaoStart(req, conta);

    BuscaBoletoResponse response = new BuscaBoletoResponse();
    response = buscarDadosDeAgenciaEContaNoBRBAco(req, cpf, response);

    if (response.getCodigoErro() != null && !new Integer(response.getCodigoErro()).equals(0)) {
      throw new GenericServiceException(response.getMensagemErro());
    } else if (Objects.nonNull(response.getSucesso())
        && response.getSucesso().equals(Boolean.FALSE)) {
      throw new GenericServiceException("Ocorreu um erro inesperado! Tente novamente mais tarde.");
    }

    salvaLogTituloPagtoValidacaoEnd(idUsuario, logPagtoVal, response);

    return response;
  }

  /**
   * Método que faz a consulta de linha digitável buscando no BRBACOClient
   *
   * @param req
   * @param cpf
   * @return
   */
  private BuscaBoletoResponse buscarDadosDeAgenciaEContaNoBRBAco(
      ConsultarLinhaDigitavelTituloBRB req, String cpf, BuscaBoletoResponse response) {
    try {
      if (req.getLinhaDigitavel().substring(0, 1).equals("8")) {
        response =
            acoClient.consultarBoleto(
                req.getLinhaDigitavel().substring(0, 12),
                req.getLinhaDigitavel().substring(12, 24),
                req.getLinhaDigitavel().substring(24, 36),
                req.getLinhaDigitavel().substring(36, 48),
                req.getContaOrigem(),
                req.getAgenciaOrigem(),
                cpf);
      } else {
        response =
            acoClient.consultarBoleto(
                req.getLinhaDigitavel().substring(0, 10),
                req.getLinhaDigitavel().substring(10, 21),
                req.getLinhaDigitavel().substring(21, 32),
                req.getLinhaDigitavel().substring(32, 33),
                req.getLinhaDigitavel().substring(33, 47),
                req.getContaOrigem(),
                req.getAgenciaOrigem(),
                req.getDataPagamento(),
                cpf);
      }
    } catch (GenericServiceException e) {
      response.setCodigoRetorno(ERRO_PREVISTO);
      response.setMensagemErro(e.getMensagem());
      response.setSucesso(false);
      log.error(e.getMensagem(), e);
    } catch (Exception e) {
      response.setCodigoRetorno(ERRO_IMPREVISTO);
      response.setMensagemErro(e.getMessage());
      response.setSucesso(false);
      log.error(e.getMessage(), e);
    }
    return response;
  }

  /**
   * Método que salva um LogPagtoTituloValidacao no início de uma consulta de código de barras
   *
   * @param req
   * @param conta
   * @return
   */
  private LogPagtoTituloValidacao salvaLogPagtoTituloValidacaoStart(
      ConsultarLinhaDigitavelTituloBRB req, ContaPagamento conta) {
    LogPagtoTituloValidacao logPagtoVal = new LogPagtoTituloValidacao();
    logPagtoVal.setIdConta(conta.getIdConta());
    logPagtoVal.setLinhaDigitavel(req.getLinhaDigitavel());
    logPagtoVal.setDataInicio(new Date());

    logPagtoVal = logPagtoValidacaoRepository.save(logPagtoVal);
    return logPagtoVal;
  }

  /**
   * Método que salva um LogPagtoTituloValidacao no final de uma consulta de código de barras
   *
   * @param idUsuario
   * @param logPagtoVal
   * @param response
   * @return
   */
  private void salvaLogTituloPagtoValidacaoEnd(
      Long idUsuario, LogPagtoTituloValidacao logPagtoVal, BuscaBoletoResponse response) {
    Date dtHrfim = new Date();

    logPagtoVal = fromBRBToLogPagtoValidacao(response.getResultado(), logPagtoVal);

    logPagtoVal.setDataFim(dtHrfim);
    logPagtoVal.setIdUsuarioInclusao(idUsuario);
    logPagtoVal.setCodigoBarras(response.getResultado().getCodigoBarras());

    logPagtoVal = logPagtoValidacaoRepository.save(logPagtoVal);

    response.setProtocoloInterno(logPagtoVal.getIdLogPagtoTitulo());
  }

  private LogPagtoTituloValidacao fromBRBToLogPagtoValidacao(
      ResultadoConsultaLinhaDigitavelBRB res, LogPagtoTituloValidacao logPagtoVal) {

    BeanUtils.copyProperties(res, logPagtoVal, Util.getNullPropertyNames(res));

    logPagtoVal = preencherDadosRegistroLogPagtoTituloValidacao(logPagtoVal, res);

    return logPagtoVal;
  }

  /**
   * Método que preenche os dados do LogPagtoTituloValidacao retornados do Response do BRB
   *
   * @param logPagtoVal
   * @param dadosRegistro
   * @return
   */
  private LogPagtoTituloValidacao preencherDadosRegistroLogPagtoTituloValidacao(
      LogPagtoTituloValidacao logPagtoVal, ResultadoConsultaLinhaDigitavelBRB dadosRegistro) {
    logPagtoVal.setDocumentoBeneficiario(
        dadosRegistro.getParametrosCobrancaNPCDTO().getCnpjCpfBeneficiario());
    logPagtoVal.setDocumentoSacado(dadosRegistro.getCpfCnpjSacado());
    logPagtoVal.setNomeBeneficiario(
        dadosRegistro.getParametrosCobrancaNPCDTO().getNomeRazaoSocialBeneficiario());
    logPagtoVal.setNomeSacado(dadosRegistro.getNomeSacado());
    logPagtoVal.setValorDescontoCalculado(dadosRegistro.getValorDescontoAbatimento());
    logPagtoVal.setValorJurosCalculado(dadosRegistro.getValorJuros());
    logPagtoVal.setValorMaximoPagamento(
        dadosRegistro.getParametrosCobrancaNPCDTO().getValorMaximoPermitido());
    logPagtoVal.setValorMinimoPagamento(
        dadosRegistro.getParametrosCobrancaNPCDTO().getValorMinimoPermitido());
    logPagtoVal.setValorMultaCalculado(dadosRegistro.getParametrosCobrancaNPCDTO().getValorMulta());
    logPagtoVal.setValorNominal(dadosRegistro.getParametrosCobrancaNPCDTO().getValorTitulo());
    logPagtoVal.setValorPagamentoAtualizado(
        dadosRegistro.getParametrosCobrancaNPCDTO().getValorPagamentoExistente());
    logPagtoVal.setValorTotalAbatimento(dadosRegistro.getValorDescontoAbatimento());
    logPagtoVal.setValorTotalAcrescimo(dadosRegistro.getValorOutrosAcrescimos());
    logPagtoVal.setValor(dadosRegistro.getValorTotal());

    if (logPagtoVal.getDataVencimento() == null
        && logPagtoVal.getDataVencimentoRegistro() != null) {
      logPagtoVal.setDataVencimento(logPagtoVal.getDataVencimentoRegistro());
    }
    return logPagtoVal;
  }

  /**
   * Serviço responsável por efetuar o pagamento de boleto fazendo uma requisição na API do BRB
   *
   * @param logPagtoValidacao
   * @param contaUsuarioLogado
   * @param contratoUtilizado
   * @param idUsuario
   * @param req
   * @param cpf
   * @param saldoConta
   * @param isPortador
   * @param contratoInstituicao
   * @param contaInstituicao
   * @param saldoContaInst
   * @param idMoeda
   * @return
   */
  public EfetuarPagamentoTituloBRBResponse efetuarPagamentoTitulo(
      LogPagtoTituloValidacao logPagtoValidacao,
      ContaPagamento contaUsuarioLogado,
      ContratoGatewayPagto contratoUtilizado,
      Long idUsuario,
      EfetuarPagamentoTituloBRB req,
      String cpf,
      GetSaldoConta saldoConta,
      boolean isPortador,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      ContaPagamento contaInstituicao,
      GetSaldoConta saldoContaInst,
      Integer idMoeda) {

    EfetuarPagamentoTituloBRBResponse resposta = new EfetuarPagamentoTituloBRBResponse();

    LogPagtoTituloTransacao logTransacao =
        validaInformacoesDeSaldoEValorESalvaLog(
            logPagtoValidacao, contaUsuarioLogado, idUsuario, req, contratoInstituicao, cpf);

    PagamentoEfetivaPagamentoBRBResponse res = efetuarPagamentoBRB(req);

    if (res.getCodigoErro().equals(ERRO_PAGAMENTO_BRB)) {
      logTransacao.setMensagemErro(res.getMsg());
      logTransacao.setStatusTransacao("ERRODESCONHECIDO");
      logTransacao = logPagtoTransacaoRepository.save(logTransacao);
      resposta.setCodigoRetorno(res.getCodigoErro());
      resposta.setMensagemErro(res.getMsg());
      return resposta;
    }

    salvarLogPagtoTituloTransacaoEnd(logPagtoValidacao, logTransacao, res);

    resposta.setComprovanteFormatado(res.getResultado().getComprovanteHTML());
    resposta.setCodigoRetorno(res.getCodigoErro());
    return resposta;
  }

  /**
   * Método que valida Informações do saldo da Conta do BRB E valida se o valor de pagamento é maior
   * do que 0 Caso tudo seja validado com sucesso, salva o LogPagtoTituloValidacao
   *
   * @param logPagtoValidacao
   * @param contaUsuarioLogado
   * @param idUsuario
   * @param req
   * @param contratoInstituicao
   * @return
   */
  private LogPagtoTituloTransacao validaInformacoesDeSaldoEValorESalvaLog(
      LogPagtoTituloValidacao logPagtoValidacao,
      ContaPagamento contaUsuarioLogado,
      Long idUsuario,
      EfetuarPagamentoTituloBRB req,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      String cpf) {
    Date dtHrInicioPagto = new Date();
    validarValorPagto(logPagtoValidacao, req);

    BigDecimal valorAPagarBRB = getValorAPagar(logPagtoValidacao, req);

    AntifraudeConta antifraudeContaOrigem =
        antifraudeContaService.findByIdConta(contaUsuarioLogado.getIdConta());

    DadosAgenciaContaBrbPay agenciaEConta =
        antifraudeContaService.retornaDadosDeAgenciaEConta(antifraudeContaOrigem);

    verificarSaldoSuficiente(
        cpf, agenciaEConta.getAgencia(), agenciaEConta.getConta(), valorAPagarBRB.longValue());

    LogPagtoTituloTransacao logTransacao =
        salvarLogPagtoTituloTransacaoStart(
            logPagtoValidacao,
            contaUsuarioLogado,
            idUsuario,
            contratoInstituicao,
            dtHrInicioPagto,
            valorAPagarBRB);
    return logTransacao;
  }

  /**
   * Salva LogPagtoTituloTransacao com informações iniciais da Transação à ser realizada
   *
   * @param logPagtoValidacao
   * @param contaUsuarioLogado
   * @param idUsuario
   * @param contratoInstituicao
   * @param dtHrInicioPagto
   * @param valorAPagarBRB
   * @return
   */
  private LogPagtoTituloTransacao salvarLogPagtoTituloTransacaoStart(
      LogPagtoTituloValidacao logPagtoValidacao,
      ContaPagamento contaUsuarioLogado,
      Long idUsuario,
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao,
      Date dtHrInicioPagto,
      BigDecimal valorAPagarBRB) {
    LogPagtoTituloTransacao logTransacao = new LogPagtoTituloTransacao();
    logTransacao.setDataHoraInicioPagto(dtHrInicioPagto);
    logTransacao.setIdContratoGatewayPagtoTituloInst(
        contratoInstituicao.getIdContratoGatewayPagto());
    logTransacao.setValor(valorAPagarBRB.doubleValue());
    logTransacao.setIdLogPagtoTitulo(logPagtoValidacao.getIdLogPagtoTitulo());
    logTransacao.setProtocoloId(
        logPagtoValidacao.getProtocoloIdConsulta() != null
            ? logPagtoValidacao.getProtocoloIdConsulta()
            : null);
    logTransacao.setIdConta(contaUsuarioLogado.getIdConta());
    logTransacao.setIdUsuarioInclusao(idUsuario);

    logTransacao = logPagtoTransacaoRepository.save(logTransacao);
    return logTransacao;
  }

  /**
   * Método que salva LogPagtoTituloTransacao com dados da transação realizada com sucesso ou erro
   *
   * @param logPagtoValidacao
   * @param logTransacao
   * @param res
   */
  private void salvarLogPagtoTituloTransacaoEnd(
      LogPagtoTituloValidacao logPagtoValidacao,
      LogPagtoTituloTransacao logTransacao,
      PagamentoEfetivaPagamentoBRBResponse res) {
    Date dtHrfimPagto = new Date();

    logTransacao =
        fromResultadoEfetivaPagamentoBRBResponseToLogPagtoTransacao(
            res.getResultado(), logPagtoValidacao, logTransacao);
    log.info("+++++++++" + logTransacao);

    logTransacao.setDataHoraFimPagto(dtHrfimPagto);

    logTransacao.setResultadoPagto(res.getCodigoErro());
    logTransacao.setDescResultadoPagto(res.getMsg());

    logTransacao = logPagtoTransacaoRepository.save(logTransacao);
  }

  /**
   * Função que monta as requisições de efetivação e as envia para o BRB
   *
   * @param req
   * @return
   */
  private PagamentoEfetivaPagamentoBRBResponse efetuarPagamentoBRB(EfetuarPagamentoTituloBRB req) {

    PagamentoEfetivaPagamentoBRBResponse response = new PagamentoEfetivaPagamentoBRBResponse();
    try {
      PagamentoComplementaDadosBRB request =
          preencherDadosEfetuarPagamentoTituloBRBToPagamentoComplementaDados(req);
      request.setXSessionId(req.getXSessionId());
      String dataSolicitacao = request.getDataSolicitacao();
      PagamentoComplementaDadosBRBResponse responseComplementaDados =
          acoClient.pagamentoComplementaDados(request);
      if (isNull(responseComplementaDados.getCodigoErro())) {
        PagamentoEfetivaPagamentoBRB requestEfetivaPagamento =
            preencherDadosPagamentoComplementaDadosBRBToPagamentoEfetivaPagamento(
                responseComplementaDados.getResultado(),
                req.getXSessionId(),
                req.getRequest().getSenhaCartao(),
                dataSolicitacao);
        response = acoClient.pagamentoEfetivarPagamento(requestEfetivaPagamento);
        if (isNull(responseComplementaDados.getCodigoErro())) {
          response.setCodigoErro(0);
        }
      }

    } catch (Exception e) {
      log.error("Ocorreu um erro: " + e.getMessage(), e);
      response.setCodigoErro(ERRO_PAGAMENTO_BRB);
      response.setMsg("Erro ao efetuar o pagamento no BRB: " + e.getMessage());
      return response;
    }
    return response;
  }

  private LogPagtoTituloTransacao fromResultadoEfetivaPagamentoBRBResponseToLogPagtoTransacao(
      ResultadoEfetivaPagamentoBRBResponse resp,
      LogPagtoTituloValidacao logPagto,
      LogPagtoTituloTransacao logTran) {

    BeanUtils.copyProperties(resp, logTran, Util.getNullPropertyNames(resp));

    logTran.setAutenticacao(null);

    logTran.setComprovanteFormatado(resp.getComprovanteHTML());
    // logTran.setProtocoloIdConfirmacao(logPagto.getProtocoloIdConsulta().longValue());

    logTran.setStatusTransacao(resp.getTransacaoPendente() ? "SUCESSO" : "ERRO DESCONHECIDO");

    return logTran;
  }

  private PagamentoComplementaDadosBRB
      preencherDadosEfetuarPagamentoTituloBRBToPagamentoComplementaDados(
          EfetuarPagamentoTituloBRB req) {
    PagamentoComplementaDadosBRB request = new PagamentoComplementaDadosBRB();
    BeanUtils.copyProperties(req.getRequest(), request);
    ZoneId zoneId = ZoneId.of("America/Sao_Paulo");
    LocalDateTime now = LocalDateTime.now();
    request.setDataPagamento(
        now.atZone(zoneId).withNano(0).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
    request.setDataPagamentoDate(
        now.atZone(zoneId).withNano(0).format(DateTimeFormatter.ISO_OFFSET_DATE_TIME));
    return request;
  }

  private PagamentoEfetivaPagamentoBRB
      preencherDadosPagamentoComplementaDadosBRBToPagamentoEfetivaPagamento(
          ResultadoComplementaDadosBRBResponse req,
          String xSessionId,
          String senhaCartao,
          String dataSolicitacao) {
    PagamentoEfetivaPagamentoBRB request = new PagamentoEfetivaPagamentoBRB();
    ParametrosCobrancaNPCDTOEfetivaPagamento parametrosCobrancaNPCDTOEfetivaPagamento =
        new ParametrosCobrancaNPCDTOEfetivaPagamento();
    BeanUtils.copyProperties(
        req.getParametrosCobrancaNPCDTO(), parametrosCobrancaNPCDTOEfetivaPagamento);
    BeanUtils.copyProperties(req, request);
    request.setDataPagamentoDate(req.getDataPagamento());
    request.setDataSolicitacao(dataSolicitacao);
    request.setParametrosCobrancaNPCDTO(parametrosCobrancaNPCDTOEfetivaPagamento);
    request.setXSessionId(xSessionId);
    request.setSenhaCartao(senhaCartao);

    return request;
  }

  private void validarValorPagto(
      LogPagtoTituloValidacao entityProtocolo, EfetuarPagamentoTituloBRB req) {
    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getPermiteAlterarValor()
        && req.getValor() != null
        && req.getValor().compareTo(new Double(MIN_VALOR)) == MIN_VALOR) {
      throw new GenericServiceException("Valor a pagar deve ser maior que R$0,00");
    }
    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getPermiteAlterarValor()
        && req.getValor() != null
        && req.getValor().compareTo(MAX_VALOR) >= MIN_VALOR) {
      throw new GenericServiceException("Valor a pagar não pode ser maior que R$9999999,99");
    }
  }

  private BigDecimal getValorAPagar(
      LogPagtoTituloValidacao entityProtocolo, EfetuarPagamentoTituloBRB req) {
    BigDecimal valorAPagar = null;

    if (entityProtocolo.getPermiteAlterarValor() != null
        && entityProtocolo.getPermiteAlterarValor()
        && req.getValor() != null
        && req.getValor().compareTo(new Double(0.0)) > 0) {
      valorAPagar = new BigDecimal(req.getValor().toString());
    } else if (entityProtocolo.getValorPagamentoAtualizado() != null) {
      valorAPagar = new BigDecimal(entityProtocolo.getValorPagamentoAtualizado().toString());
    } else if (entityProtocolo.getValor() != null) {
      valorAPagar = new BigDecimal(entityProtocolo.getValor().toString());
    }

    if (valorAPagar == null) {
      throw new GenericServiceException("Valor a pagar deve ser informado.");
    }
    return valorAPagar;
  }

  private void verificarSaldoSuficiente(
      String cpf, String agenciaContaOrigem, String numeroContaOrigem, Long valorTransferencia) {

    BuscaSaldoResponse saldoContaOrigem = null;
    try {
      saldoContaOrigem =
          acoClient.consultarSaldo(
              Long.parseLong(numeroContaOrigem), Long.parseLong(agenciaContaOrigem), cpf);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível executar a consulta de saldo: ", e.getMessage());
    }

    if (saldoContaOrigem.getResultado().getSaldo().longValue() < valorTransferencia) {
      throw new GenericServiceException("Saldo insuficiente para a transação.");
    }
  }

  /**
   * Metodo responsável por efetuar uma transferencia entre contas BRB
   *
   * @param model
   * @param tokenJWT
   * @param comSenha
   * @return boolean
   */
  @Transactional
  public boolean transferenciaEntreContasBRB(
      TransferenciaContasBRBRequest model, String tokenJWT, String cpf, Boolean comSenha) {

    if (comSenha) {
      Map<String, Object> map =
          credencialService.validarSenha(
              model.getPinCredencialOrigem(), model.getIdCredencial(), tokenJWT);
      Boolean valida = (Boolean) map.get("sucesso");
      String msg = (String) map.get("erro");
      if (!valida) {
        throw new GenericServiceException(
            model.getIdInstituicaoOrigem() == 4001 ? msg : "Pin Inválido para a credencial.");
      }
    }

    Credencial credencialOrigem = credencialService.findById(model.getIdCredencial());

    if (credencialOrigem == null) {
      throw new GenericServiceException("Credencial Origem não encontrado ");
    }

    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
        credencialOrigem, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    Credencial credencialDestino = credencialService.findById(model.getIdCredencialDestino());

    if (credencialDestino == null) {
      throw new GenericServiceException("Credencial Destino não encontrado ");
    }

    ContaPagamento contaOrigem = contaPagamentoService.findById(credencialOrigem.getIdConta());
    travaContasService.travaContas(
        contaOrigem.getIdConta(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
    ContaPagamento contaDestino = contaPagamentoService.findById(credencialDestino.getIdConta());
    travaContasService.travaContas(
        contaDestino.getIdConta(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    ProdutoInstituicaoConfiguracao produtoOrigem =
        lancamentoService.getProduto(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoDestino =
        lancamentoService.getProduto(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());

    if (produtoOrigem.getMoeda().getIdMoeda() != produtoDestino.getMoeda().getIdMoeda()) {
      throw new GenericServiceException("Produtos com moedas diferentes.");
    }

    CodigoTransacao codigoTransacao = codigoTransacaoService.findById(model.getCodigoTransacao());

    if (codigoTransacao == null) {
      throw new GenericServiceException("Codigo de transação não encontrado");
    }

    AntifraudeConta antifraudeContaOrigem =
        antifraudeContaService.findByIdConta(contaOrigem.getIdConta());

    DadosAgenciaContaBrbPay agenciaEContaOrigem =
        antifraudeContaService.retornaDadosDeAgenciaEConta(antifraudeContaOrigem);

    BuscaSaldoResponse saldoContaOrigem = null;
    try {
      saldoContaOrigem =
          acoClient.consultarSaldo(
              Long.parseLong(agenciaEContaOrigem.getAgencia()),
              Long.parseLong(agenciaEContaOrigem.getConta()),
              cpf);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível executar a consulta de saldo: ", e.getMessage());
    }

    if (saldoContaOrigem.getResultado().getSaldo().longValue() < model.getValorTransferencia()) {
      throw new GenericServiceException("Saldo insuficiente para a transação.");
    }

    TransferenciaEntreContasBRBRequest body =
        criaJsonDeTransferenciaEntreContasBRB(
            model, agenciaEContaOrigem.getAgencia(), agenciaEContaOrigem.getConta());

    TransferenciaEntreContasBRBConfirmarResponse response =
        realizarTransferenciaBRB(body, model, cpf);

    boolean sucesso = response != null;

    // Se a transferencia for feita do nosso lado, implementar
    // TransMesmaInstituicaoIdCredencial req = new
    // TransMesmaInstituicaoIdCredencial();
    // BeanUtils.copyProperties(model, req);
    // boolean sucesso = facade.transMesmaInstituicaoIdCredencialBRB(req, tokenJWT,
    // credencialOrigem, credencialDestino, codigoTransacao.getCodTransacao(),
    // comSenha);

    if (sucesso) {
      Pessoa pessoa = pessoaService.findPessoaTitularConta(contaDestino.getIdConta());
      eventoService.publicarMovimentacaoFinanceiraEvent(
          Servicos.TRANSFERENCIA_INTERNA_PORTADOR.getDescricao(),
          contaOrigem.getIdInstituicao(),
          contaDestino.getIdInstituicao(),
          contaOrigem.getIdConta(),
          contaDestino.getIdConta(),
          codigoTransacao.getCodTransacao(),
          null,
          new BigDecimal(model.getValorTransferencia()),
          pessoa != null ? pessoa.getDocumento() : "0");
    }

    return sucesso;
  }

  /**
   * Função que povoa o Objeto de Transferência Entre Contas do BRB
   *
   * @param model
   * @param agenciaContaOrigem
   * @param numeroContaOrigem
   * @return
   */
  private TransferenciaEntreContasBRBRequest criaJsonDeTransferenciaEntreContasBRB(
      TransferenciaContasBRBRequest model, String agenciaContaOrigem, String numeroContaOrigem) {
    TransferenciaEntreContasBRBRequest body = new TransferenciaEntreContasBRBRequest();
    FavorecidoDTOTransferenciaBRBRequest favorecidoDTORequest =
        new FavorecidoDTOTransferenciaBRBRequest();

    body.setAgencia(Integer.parseInt(agenciaContaOrigem));
    body.setAgenciaOrigem(Integer.parseInt(agenciaContaOrigem));
    body.setConta(Integer.parseInt(numeroContaOrigem));
    body.setValorTransferencia(model.getValorTransferencia());
    body.setDataTransacao(new Date().toString());
    body.setTipoConta(0);
    body.setModalidadeConta(0);
    favorecidoDTORequest.setNumeroAgencia(model.getAgencia());
    favorecidoDTORequest.setNumeroBanco(70);
    favorecidoDTORequest.setNumeroConta(model.getContaBancaria());
    body.setFavorecidoDTO(favorecidoDTORequest);
    return body;
  }

  /**
   * Função responsável por realizar a transferência entre contas BRB acessando a API
   *
   * @param model
   * @param body
   * @param cpf
   * @return
   */
  public TransferenciaEntreContasBRBConfirmarResponse realizarTransferenciaBRB(
      TransferenciaEntreContasBRBRequest body, TransferenciaContasBRBRequest model, String cpf) {
    TransferenciaEntreContasBRBResponse responseInicial =
        acoClient.transferenciaBRBtoBRB(body, cpf);

    if (responseInicial.getCodigoErro() != 0) {
      return null;
    }

    TransferenciaEntreContasBRBConfirmarRequest requestConfirmacao =
        new TransferenciaEntreContasBRBConfirmarRequest();
    BeanUtils.copyProperties(body, requestConfirmacao);
    TransferenciaEntreContasBRBConfirmarResponse responseFinal =
        acoClient.transferenciaBRBtoBRBConfirmar(requestConfirmacao, cpf);

    if (responseFinal.getCodigoErro() != "0") {
      return null;
    }

    return responseFinal;
  }

  private void tratarGenericServiceException(
      GenericServiceException e, GatewayPagtoResponse resultado) {
    resultado.setSucesso(false);
    resultado.setCodigoRetorno(-1);
    resultado.setMensagemErro(e.getMensagem());
    e.printStackTrace();
  }

  private void tratarException(Exception e, GatewayPagtoResponse resultado) {
    resultado.setSucesso(false);
    resultado.setCodigoRetorno(999);
    resultado.setMensagemErro("Ocorreu um erro interno: " + e.getMessage());
    e.printStackTrace();
  }

  private void verificarPermissaoConta(ContaPagamento conta) {
    if (!temPermissaoAcessarConta(conta)) {
      throw new GenericServiceException("Usuário sem permissão para efetuar a operação");
    }
  }

  private boolean temPermissaoAcessarConta(ContaPagamento conta) {
    return conta != null;
  }

  private Map<String, Object> getContaAndUsuarioLogado(
      SecurityUser user, SecurityUserPortador userPortador, Long idConta) {

    Map<String, Object> params = new HashMap<>();

    if (user != null) {

      ContaPagamento conta = contaPagamentoService.findContaByHierarquia(idConta, user);
      params.put("conta", conta);
      params.put("idUsuario", new Long(user.getIdUsuario()));
      params.put("isPortador", false);

    } else if (userPortador != null) {
      params.put("idUsuario", userPortador.getIdLogin());

      Integer idInstituicao =
          Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY.equals(userPortador.getIdInstituicao())
              ? Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY
              : userPortador.getIdInstituicao();

      List<ContaPagamento> contas =
          contaPagamentoService.findByCpf(
              userPortador.getCpf(),
              userPortador.getIdProcessadora(),
              idInstituicao,
              userPortador.getIdTipoPessoa());
      for (ContaPagamento conta : contas) {
        if (conta.getIdConta().equals(idConta)) {
          params.put("conta", conta);
          params.put("isPortador", true);
        }
      }
    }

    return params;
  }

  private void validarLinhaDigitavel(ConsultarLinhaDigitavelTituloBRB req) {
    if ((req.getLinhaDigitavel() == null || req.getLinhaDigitavel().trim().isEmpty())) {
      throw new GenericServiceException(
          "O Campo linha digitável ou Código de barras deve ser informado!");
    }

    if ((req.getLinhaDigitavel() != null && req.getLinhaDigitavel().length() > MAX_LINHA_DIG)) {
      throw new GenericServiceException(
          "Verifique a linha digitável/Código de Barras informado.Ela deve ter no máximo 48 dígitos!");
    }
  }

  public LinkedHashMap<?, ?> enviarDadosParaCaf(CAFRequest model) {
    LinkedHashMap<?, ?> response = this.combateFraudeClient.enviarDocumentosParaCaf(model);
    return response;
  }
}
