package br.com.sinergico.repository.suporte;

import br.com.entity.suporte.Contato;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ContatoRepository extends JpaRepository<Contato, Long> {

  /**
   * Metodo responsavel por buscar contatos de uma hierarquia.
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  List<Contato>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamento(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento);

  /**
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  List<Contato>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoOrderByDataHoraStatus(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento);

  /**
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  List<Contato>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoOrderByIdContato(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento);

  /**
   * Metodo responsavel por buscar contatos de uma hierarquia por status do contato. considere
   * 1-ativo e 9-inativo
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  List<Contato>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndStatus(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento,
          Integer status);

  List<Contato> findByIdProcessadoraAndIdInstituicaoAndIdNivelHierarquia(
      Integer idProcessadora, Integer idInstituicao, int nivelInstituicao);
}
