package br.com.sinergico.service.suporte;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.contains;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import br.com.entity.suporte.TravaServicos;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.repository.suporte.TravaServicosRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.vo.DescricaoAndIdInstituicaoVO;
import br.com.sinergico.vo.TravaOuDestravaVO;
import br.com.sinergico.vo.TravaServicosCacheVo;
import br.com.util.MockUtil;
import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.LoggerContext;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

@ExtendWith(MockitoExtension.class)
class TravaServicosServiceTest {

  @Mock private TravaServicosRepository travaServicosRepository;

  @Mock private RestTemplate restTemplate;

  @InjectMocks private TravaServicosService travaServicosService;

  @BeforeEach
  void setUp() {
    LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
    ch.qos.logback.classic.Logger loggerName = loggerContext.getLogger("org.springframework.test");
    loggerName.setLevel(Level.INFO);
    ReflectionTestUtils.setField(
        travaServicosService,
        "servidoresLimparCaches",
        "https://issuer.itspay-hom.com.br;http://localhost:8080");
    ReflectionTestUtils.setField(travaServicosService, "basicUser", "user");
    ReflectionTestUtils.setField(travaServicosService, "basicPass", "pass");
  }

  @Test
  @DisplayName("Deve retornar objeto TravaServicos ao buscar por instituição e serviço")
  void testFindByIdInstituicaoAndServico() {
    TravaServicos travaMock = new TravaServicos();
    Integer instituicaoAleatoria = MockUtil.getIdInstituicaoAleatoria();
    Servicos servicoAleatorio = MockUtil.getServicoAleatorio();
    when(travaServicosRepository.encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            instituicaoAleatoria, servicoAleatorio.getId()))
        .thenReturn(travaMock);

    TravaServicos result =
        travaServicosService.findByIdInstituicaoAndServico(
            instituicaoAleatoria, servicoAleatorio.getId());
    assertNotNull(result, "Resultado deve ser objeto não nulo");
    verify(travaServicosRepository, times(1))
        .encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            instituicaoAleatoria, servicoAleatorio.getId());
  }

  @Test
  @DisplayName("Deve lançar GenericServiceException se serviço estiver travado")
  void testTravaServicosQuandoServicoTravado() {
    TravaServicos travaMock = new TravaServicos();
    travaMock.setTravado(true);
    when(travaServicosRepository.encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            anyInt(), anyInt()))
        .thenReturn(travaMock);

    assertThrows(
        GenericServiceException.class,
        () ->
            travaServicosService.travaServicos(
                MockUtil.getIdInstituicaoAleatoria(), MockUtil.getServicoAleatorio()),
        "Deveria lançar GenericServiceException");
  }

  @Test
  @DisplayName("HttpStatus deve ser 503 (Service Unavailable) se serviço estiver travado")
  void testTravaServicosQuandoServicoTravadoComStatus503() {
    TravaServicos travaMock = new TravaServicos();
    travaMock.setTravado(true);
    when(travaServicosRepository.encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            anyInt(), anyInt()))
        .thenReturn(travaMock);

    GenericServiceException thrownException =
        assertThrows(
            GenericServiceException.class,
            () ->
                travaServicosService.travaServicos(
                    MockUtil.getIdInstituicaoAleatoria(), MockUtil.getServicoAleatorio()),
            "Deveria lançar GenericServiceException");

    assertEquals(HttpStatus.SERVICE_UNAVAILABLE, thrownException.getHttpStatus());
  }

  @Test
  @DisplayName("Não deve lançar exceção se serviço NÃO estiver travado")
  void testTravaServicosQuandoServicoNaoTravado() {
    when(travaServicosRepository.encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            anyInt(), anyInt()))
        .thenReturn(null);

    assertDoesNotThrow(
        () ->
            travaServicosService.travaServicos(
                MockUtil.getIdInstituicaoAleatoria(), MockUtil.getServicoAleatorio()),
        "Não deve ser retornada uma exceção");
  }

  @Test
  @DisplayName("Deve formatar a chave corretamente (com padding) para cache")
  void testFormataChave() {
    Integer instituicaoAleatoria = MockUtil.getIdInstituicaoAleatoria();
    Servicos servicoAleatorio = MockUtil.getServicoAleatorio();
    String chave = travaServicosService.formataChave(instituicaoAleatoria, servicoAleatorio);
    String esperado =
        StringUtils.leftPad(instituicaoAleatoria.toString(), 6, "0")
            + StringUtils.leftPad(String.valueOf(servicoAleatorio.getId()), 2, "0");
    assertEquals(esperado, chave, "A formatação deve retornar como esperado");
  }

  @Test
  @DisplayName("Deve retornar false quando não encontrado registro de trava no banco")
  void testEncontraParametrosDeTravaNaCacheOuBancoSemRegistro() {
    Integer instituicaoAleatoria = MockUtil.getIdInstituicaoAleatoria();
    Servicos servicoAleatorio = MockUtil.getServicoAleatorio();
    when(travaServicosRepository.encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            instituicaoAleatoria, servicoAleatorio.getId()))
        .thenReturn(null);

    Boolean travado =
        travaServicosService.encontraParametrosDeTravaNaCacheOuBanco(
            instituicaoAleatoria, servicoAleatorio);
    assertFalse(travado, "Deveria retornar false se não encontrar registro no banco");
  }

  @Test
  @DisplayName("Deve retornar true quando existir registro de trava no banco")
  void testEncontraParametrosDeTravaNaCacheOuBancoComRegistroTrava() {
    Integer instituicaoAleatoria = MockUtil.getIdInstituicaoAleatoria();
    Servicos servicoAleatorio = MockUtil.getServicoAleatorio();
    TravaServicos travaMock = new TravaServicos();
    travaMock.setTravado(true);
    when(travaServicosRepository.encontraPrimeiroTruePorInstituicaoEnviadaOuTodasEServicoUsado(
            instituicaoAleatoria, servicoAleatorio.getId()))
        .thenReturn(travaMock);

    Boolean travado =
        travaServicosService.encontraParametrosDeTravaNaCacheOuBanco(
            instituicaoAleatoria, servicoAleatorio);
    assertTrue(travado, "Deveria retornar true se há registro travado no banco");
  }

  @Test
  @DisplayName(
      "Deve retornar trava combinada (instituição + geral) quando ambos estiverem no cache com data futura")
  void testEncontraParametrosTravaCacheAmbosFuturos() {
    Integer instituicao = MockUtil.getIdInstituicaoAleatoria();
    Servicos servico = MockUtil.getServicoAleatorio();

    String chaveParametroInstituicao = travaServicosService.formataChave(instituicao, servico);
    String chaveParametroGeral = travaServicosService.formataChave(null, servico);

    TravaServicos travaMock = new TravaServicos();
    travaMock.setTravado(true);
    travaMock.setIdInstituicao(instituicao);
    travaMock.setServico(servico.getId());
    travaMock.setDtHrModificacao(LocalDateTime.now());

    TravaServicosCacheVo cacheInstituicao = new TravaServicosCacheVo(travaMock);
    cacheInstituicao.setDtHrExpiracao(new Timestamp(System.currentTimeMillis() + 60000));

    TravaServicosCacheVo cacheGeral = new TravaServicosCacheVo(travaMock);
    cacheGeral.setTravado(false);
    cacheGeral.setDtHrExpiracao(new Timestamp(System.currentTimeMillis() + 60000));

    @SuppressWarnings("unchecked")
    ConcurrentHashMap<String, TravaServicosCacheVo> cacheRef =
        (ConcurrentHashMap<String, TravaServicosCacheVo>)
            ReflectionTestUtils.getField(TravaServicosService.class, "cacheTravaServicos");

    cacheRef.put(chaveParametroInstituicao, cacheInstituicao);
    cacheRef.put(chaveParametroGeral, cacheGeral);

    Boolean travado =
        travaServicosService.encontraParametrosDeTravaNaCacheOuBanco(instituicao, servico);

    assertTrue(travado, "Deveria retornar true pois a instituição está travada no cache");

    cacheRef.clear();
  }

  @Test
  @DisplayName("Deve entrar no else e buscar do banco quando cache está expirado ou inválido")
  void testEncontraParametrosCacheExpiradoOuInvalido() {
    Integer instituicao = MockUtil.getIdInstituicaoAleatoria();
    Servicos servico = MockUtil.getServicoAleatorio();
    String chaveParametroInstituicao = travaServicosService.formataChave(instituicao, servico);

    TravaServicos travaMock = new TravaServicos();
    travaMock.setTravado(true);
    travaMock.setIdInstituicao(instituicao);
    travaMock.setServico(servico.getId());
    travaMock.setDtHrModificacao(LocalDateTime.now());

    TravaServicosCacheVo cacheVo = new TravaServicosCacheVo(travaMock);

    @SuppressWarnings("unchecked")
    ConcurrentHashMap<String, TravaServicosCacheVo> cacheRef =
        (ConcurrentHashMap<String, TravaServicosCacheVo>)
            ReflectionTestUtils.getField(TravaServicosService.class, "cacheTravaServicos");
    cacheRef.put(chaveParametroInstituicao, cacheVo);

    Boolean travado =
        travaServicosService.encontraParametrosDeTravaNaCacheOuBanco(instituicao, servico);

    assertTrue(travado, "Deveria retornar true pois o banco indicou trava habilitada");
    TravaServicosCacheVo novoCacheVo = cacheRef.get(chaveParametroInstituicao);
    assertNotNull(novoCacheVo, "Deve retornar um objeto de Cache");
    assertTrue(novoCacheVo.isTravado(), "A cache deve estar travada");

    cacheRef.clear();
  }

  @Test
  @DisplayName("Deve limpar cache com sucesso via autenticação BASIC = true")
  void testLimpaCacheViaBasic() {
    Map<String, Object> result = travaServicosService.limpaCache(true);
    assertNotNull(result, "Deve existir um objeto de retorno não nulo");
    assertTrue(result.containsKey("msg"), "Deve existir uma mensagem de retorno");
    assertEquals(
        "Limpeza de cache via autenticação Basic bem sucedida!",
        result.get("msg"),
        "A mensagem de retorno deve ser a esperada");
  }

  @Test
  @DisplayName("Deve tentar limpar cache chamando endpoints externos se BASIC = false")
  void testLimpaCacheViaChamadaExterna() {
    when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Void.class)))
        .thenReturn(ResponseEntity.ok().build());

    Map<String, Object> result = travaServicosService.limpaCache(false);

    assertNotNull(result, "Deve existir um objeto de retorno não nulo");
    assertTrue(result.containsKey("msg"), "Deve existir uma mensagem de retorno");
    assertEquals(
        "Cache de travas dos servidores limpos!",
        result.get("msg"),
        "A mensagem de retorno deve ser a esperada");

    verify(restTemplate, times(2))
        .postForEntity(contains("/limpar-cache-basic"), any(HttpEntity.class), eq(Void.class));
  }

  @Test
  @DisplayName("Deve tratar exceção ao limpar cache em endpoints externos (BASIC = false)")
  void testLimpaCacheViaChamadaExternaComErro() {
    when(restTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(Void.class)))
        .thenThrow(new RestClientException("Erro simulado"));

    Map<String, Object> result = travaServicosService.limpaCache(false);

    assertNotNull(result, "Deve existir um objeto de retorno não nulo");
    assertTrue(result.containsKey("msg"), "Deve existir uma mensagem de retorno");
    assertEquals(
        "Não foi possível realizar a limpeza de cache da instância.",
        result.get("msg"),
        "A mensagem de retorno deve ser a esperada");

    verify(restTemplate, atLeastOnce())
        .postForEntity(anyString(), any(HttpEntity.class), eq(Void.class));
  }

  @Test
  @DisplayName("Deve consultar descrição e ID de instituição")
  void testConsultaDescAndIdInstituicao() {
    DescricaoAndIdInstituicaoVO vo1 = new DescricaoAndIdInstituicaoVO();
    vo1.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());

    DescricaoAndIdInstituicaoVO vo2 = new DescricaoAndIdInstituicaoVO();
    Integer novaInstituicao;
    do {
      novaInstituicao = MockUtil.getIdInstituicaoAleatoria();
    } while (novaInstituicao.equals(vo1.getIdInstituicao()));
    vo2.setIdInstituicao(novaInstituicao);

    DescricaoAndIdInstituicaoVO vo3 = new DescricaoAndIdInstituicaoVO();
    vo3.setIdInstituicao(vo1.getIdInstituicao());

    when(travaServicosRepository.consultaDescAndIdInstituicao())
        .thenReturn(Arrays.asList(vo1, vo2, vo3));

    List<DescricaoAndIdInstituicaoVO> result = travaServicosService.consultaDescAndIdInstituicao();

    assertEquals(2, result.size(), "A lista resultante deve conter 2 elementos distintos");
    verify(travaServicosRepository, times(1)).consultaDescAndIdInstituicao();
  }

  @Test
  @DisplayName("Deve retornar true ao travar/destravar serviço existente")
  void testEnviaInformacoesParaTravar() {
    Integer instituicaoAleatoria = MockUtil.getIdInstituicaoAleatoria();
    Servicos servicoAleatorio = MockUtil.getServicoAleatorio();
    TravaServicos travaMock = new TravaServicos();
    travaMock.setIdInstituicao(instituicaoAleatoria);
    travaMock.setServico(servicoAleatorio.getId());
    travaMock.setTravado(false);

    when(travaServicosRepository.findTravaServicosByIdInstituicaoAndServico(
            instituicaoAleatoria, servicoAleatorio.getId()))
        .thenReturn(travaMock);

    TravaOuDestravaVO travaOuDestravaVO = new TravaOuDestravaVO();
    travaOuDestravaVO.setIdInstituicao(instituicaoAleatoria);
    travaOuDestravaVO.setIdServico(servicoAleatorio.getId());
    travaOuDestravaVO.setTrava(true);

    SecurityUser user = new SecurityUser();
    user.setIdUsuario(MockUtil.getIdUsuarioPortadorInteger());

    boolean result = travaServicosService.enviaInformacoesParaTravar(travaOuDestravaVO, user);
    assertTrue(result, "Deveria retornar true pois encontrou registro e fez o update");
    assertTrue(travaMock.isTravado(), "Deverá estar travado");
    verify(travaServicosRepository, times(1)).save(any(TravaServicos.class));
  }

  @Test
  @DisplayName("Deve retornar false quando não existe registro para travar/destravar")
  void testEnviaInformacoesParaTravarRegistroNaoExistente() {
    Integer instituicaoAleatoria = MockUtil.getIdInstituicaoAleatoria();
    Servicos servicoAleatorio = MockUtil.getServicoAleatorio();
    when(travaServicosRepository.findTravaServicosByIdInstituicaoAndServico(anyInt(), anyInt()))
        .thenReturn(null);

    TravaOuDestravaVO travaOuDestravaVO = new TravaOuDestravaVO();
    travaOuDestravaVO.setIdInstituicao(instituicaoAleatoria);
    travaOuDestravaVO.setIdServico(servicoAleatorio.getId());
    travaOuDestravaVO.setTrava(true);

    SecurityUser user = new SecurityUser();
    user.setIdUsuario(MockUtil.getIdUsuarioPortadorInteger());

    boolean result = travaServicosService.enviaInformacoesParaTravar(travaOuDestravaVO, user);
    assertFalse(result, "Deveria retornar false pois não encontrou registro");
    verify(travaServicosRepository, never()).save(any(TravaServicos.class));
  }

  @Test
  @DisplayName("Deve retornar a lista de TravaServicos para uma instituicao específica")
  void testStatusServicosPorInstituicao() {
    TravaServicos ts1 = new TravaServicos();
    TravaServicos ts2 = new TravaServicos();
    List<TravaServicos> mockList = Arrays.asList(ts1, ts2);

    Integer instituicao = MockUtil.getIdInstituicaoAleatoria();
    when(travaServicosRepository
            .findTravaServicosByIdInstituicaoOrderByTravadoDescDtHrModificacaoDesc(instituicao))
        .thenReturn(mockList);

    List<TravaServicos> result = travaServicosService.statusServicosPorInstituicao(instituicao);

    assertNotNull(result, "O Resultado não pode ser nulo");
    assertEquals(2, result.size(), "Deve retornar lista com dois itens");

    verify(travaServicosRepository, times(1))
        .findTravaServicosByIdInstituicaoOrderByTravadoDescDtHrModificacaoDesc(instituicao);
  }
}
