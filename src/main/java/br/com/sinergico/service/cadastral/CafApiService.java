package br.com.sinergico.service.cadastral;

import br.com.client.rest.caf.json.bean.CafApiTokenResponse;
import br.com.json.bean.brb.combateafraude.CAFTokenRequest;
import br.com.json.bean.combateafraude.ReviewManualCafRequest;
import br.com.sinergico.service.suporte.ParametroValorService;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

@Service
public class CafApiService {

  @Autowired private ParametroValorService parametroValorService;

  @Autowired private RestTemplate restTemplateUntrustedCerts;

  @Value("${usuario.api.caf}")
  private String usuarioApiCaf;

  @Value("${senha.api.caf}")
  private String senhaApiCaf;

  private static final String URL_OBTER_TOKEN = "https://api.auth.combateafraude.com/token";
  private static final String URL_ALTERAR_STATUS =
      "https://api.trust.combateafraude.com/executions/{idCaf}/manual-revision";

  private static final String URL_ALTERAR_STATUSV1 =
      "https://api.combateafraude.com/v1/transactions/{transactionId}/review";
  private static final Logger log = LoggerFactory.getLogger(CafApiService.class);

  public String obterToken() {
    try {
      Map<String, String> credentials = new HashMap<>();
      credentials.put("username", usuarioApiCaf);
      credentials.put("password", senhaApiCaf);

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);

      HttpEntity<Map<String, String>> request = new HttpEntity<>(credentials, headers);
      ResponseEntity<CafApiTokenResponse> response =
          restTemplateUntrustedCerts.postForEntity(
              URL_OBTER_TOKEN, request, CafApiTokenResponse.class);

      if ((response.getStatusCode() == HttpStatus.OK
              || response.getStatusCode() == HttpStatus.CREATED)
          && response.getBody() != null) {
        return response.getBody().getAccess_token();
      }
    } catch (Exception e) {
      log.error("Erro ao obter Caf token: " + e.getMessage(), e);
    }

    return null;
  }

  public ResponseEntity<Object> alterarStatus(
      String token, String idCaf, String status, String reason) {
    return doPostRequestAlterarStatus(prepararRequest(status, reason), token, idCaf);
  }

  public ResponseEntity<Object> alterarStatusCaf(
      String token, String transactionId, String action, String reason) {
    return doPostRequestAlterarStatusVO(
        prepararReviewCafRequest(action, reason), token, transactionId);
  }

  private CAFTokenRequest prepararRequest(String status, String reason) {
    CAFTokenRequest cafTokenRequest = new CAFTokenRequest();
    cafTokenRequest.setStatus(status);
    cafTokenRequest.setReason(reason);
    return cafTokenRequest;
  }

  public ReviewManualCafRequest prepararReviewCafRequest(String action, String reason) {
    ReviewManualCafRequest reviewManualCafRequest = new ReviewManualCafRequest();
    reviewManualCafRequest.setAction(action);
    reviewManualCafRequest.setReason(reason);
    return reviewManualCafRequest;
  }

  public ResponseEntity<Object> doPostRequestAlterarStatus(
      CAFTokenRequest request, String token, String idCaf) {
    return doPostAlteracaoStatus(request, token, idCaf);
  }

  public ResponseEntity<Object> doPostRequestAlterarStatusVO(
      ReviewManualCafRequest request, String token, String transactionId) {
    return doPostAlteracaoStatusVO(request, token, transactionId);
  }

  protected ResponseEntity<Object> doPostAlteracaoStatus(
      CAFTokenRequest cafTokenRequest, String token, String idCaf) {
    try {

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.add("Authorization", "Bearer " + token);
      headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

      String requestBody = new ObjectMapper().writeValueAsString(cafTokenRequest);
      headers.setContentLength(requestBody.getBytes(StandardCharsets.UTF_8).length);

      HttpEntity<CAFTokenRequest> body = new HttpEntity<>(cafTokenRequest, headers);
      String uri = URL_ALTERAR_STATUS.replace("{idCaf}", idCaf);

      ResponseEntity<Object> exchange =
          restTemplateUntrustedCerts.exchange(uri, HttpMethod.POST, body, Object.class);

      // Return the entire response entity
      return exchange;

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      System.out.println(e.getMessage());
      log.error("Erro HTTP ao tentar alterar status: " + e.getMessage(), e);
      // Create a new ResponseEntity with the error status
      return new ResponseEntity<>(null, e.getStatusCode());
    } catch (Exception e) {
      log.error("Erro geral ao tentar alterar status: " + e.getMessage(), e);
      // Create a new ResponseEntity with a generic error status
      return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  protected ResponseEntity<Object> doPostAlteracaoStatusVO(
      ReviewManualCafRequest reviewManualCafRequest, String token, String transactionId) {
    try {

      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.APPLICATION_JSON);
      headers.add("Authorization", token);

      String requestBody = new ObjectMapper().writeValueAsString(reviewManualCafRequest);
      headers.setContentLength(requestBody.getBytes(StandardCharsets.UTF_8).length);

      HttpEntity<ReviewManualCafRequest> body = new HttpEntity<>(reviewManualCafRequest, headers);
      String uri = URL_ALTERAR_STATUSV1.replace("{transactionId}", transactionId);

      ResponseEntity<Object> exchange =
          restTemplateUntrustedCerts.exchange(uri, HttpMethod.POST, body, Object.class);

      // Return the entire response entity
      return exchange;

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      log.error("Erro HTTP ao tentar alterar status: " + e.getMessage(), e);
      // Create a new ResponseEntity with the error status
      return new ResponseEntity<>(null, e.getStatusCode());
    } catch (Exception e) {
      log.error("Erro geral ao tentar alterar status: " + e.getMessage(), e);
      // Create a new ResponseEntity with a generic error status
      return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private HttpHeaders getHeaderAlteracaoStatus(String token) throws Exception {
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.add("Authorization", "Bearer " + token);
    return headers;
  }
}
