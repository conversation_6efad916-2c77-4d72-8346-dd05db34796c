package br.com.sinergico.controller.cadastral;

import br.com.entity.cadastral.EnderecoPessoa;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.EnderecoPessoaRequest;
import br.com.json.bean.cadastral.EnderecoPessoaUpdatePorContaRequest;
import br.com.json.bean.cadastral.EnderecoPessoaUpdateRequest;
import br.com.json.bean.cadastral.EnderecosPessoaRequest;
import br.com.sinergico.annotation.Documento;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.EnderecoPessoaService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/endereco")
public class EnderecoPessoaController extends UtilController {

  private static final int POSICAO_0 = 0;
  @Autowired private EnderecoPessoaService enderecoService;
  @Autowired private TravaServicosService travaServicosService;
  @Autowired private EntityManager em;
  @Autowired private HttpServletRequest request;

  @ApiOperation(value = "Serviço responsável por cadastrar um endereço de uma Pessoa já existente")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/criar",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_ENDERECO_PESSOA_EXISTENTE"})
  public ResponseEntity<Map<String, Object>> create(
      @RequestBody @Valid EnderecoPessoaRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de endereço", result);
    }
    EnderecoPessoa endereco = new EnderecoPessoa();
    prepareEndereco(model, endereco);

    enderecoService.cadastrarEnderecosPessoa(model.getIdUsuarioInclusao(), endereco);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Adicionado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por cadastrar um endereço de uma Pessoa em pre cadastro")
  @RequestMapping(
      value = "/criar-pre-cadastro",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, Object>> createEnderecoPreCadastrado(
      @RequestBody @Valid EnderecoPessoaRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de endereço", result);
    }
    EnderecoPessoa endereco = new EnderecoPessoa();
    prepareEndereco(model, endereco);

    enderecoService.cadastrarEnderecosPessoa(model.getIdUsuarioInclusao(), endereco);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Adicionado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por cadastrar vários endereços de uma Pessoa já existente")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/criar/varios",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_VARIOS_ENDERECOS_PESSOA_EXISTENTE"})
  public ResponseEntity<Map<String, Object>> createMany(
      @RequestBody @Valid EnderecosPessoaRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de endereços", result);
    }

    if (model.getEnderecos() == null) {
      throw new InvalidRequestException("Endereços devem ser preenchidos", result);
    }

    EnderecoPessoa[] enderecos = new EnderecoPessoa[model.getEnderecos().size()];
    prepareEnderecos(model, enderecos);

    enderecoService.cadastrarEnderecosPessoa(
        model.getEnderecos().get(POSICAO_0).getIdUsuarioInclusao(), enderecos);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereços Adicionados com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  /**
   * @param documento
   * @param tipoPessoa
   * @param idProc
   * @param idInst
   * @param status
   * @return
   */
  @ApiOperation(
      value = "Serviço responsável buscar os endereços de uma Pessoa pelo status",
      response = EnderecoPessoa.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value =
          "/{documento}/pessoa/{tipoPessoa}/processadora/{idProc}/instituicao/{idInst}/status/{status}/",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_ENDERECOS_PESSOA_STATUS"})
  public ResponseEntity<List<EnderecoPessoa>> findByDocumentoAndStatus(
      @PathVariable @Documento String documento,
      @PathVariable Integer tipoPessoa,
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer status) {

    List<EnderecoPessoa> enderecos =
        enderecoService.findByDocumentoAndStatus(documento, idProc, idInst, tipoPessoa, status);

    return new ResponseEntity<>(enderecos, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por atualizar um endereço de uma Pessoa")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizar-por-conta/{idConta}/{idTipoEndereco}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_ENDERECO_PESSOA"})
  public ResponseEntity<Map<String, Object>> update(
      @PathVariable Long idConta,
      @PathVariable Integer idTipoEndereco,
      @RequestBody @Valid EnderecoPessoaUpdatePorContaRequest model,
      BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de endereço", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    enderecoService.updatePorConta(idConta, idTipoEndereco, model);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço atualizado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por atualizar um endereço de uma Pessoa")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/atualizar",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_ENDERECO_PESSOA"})
  public ResponseEntity<Map<String, Object>> update(
      @RequestBody @Valid EnderecoPessoaUpdateRequest model,
      BindingResult result,
      HttpServletRequest httpServletRequest) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de endereço", result);
    }

    callFunctionUserOrFunctionPortadorOrFunctionEstabelecimento(
        httpServletRequest,
        model,
        enderecoService::atualizarEnderecoUser,
        enderecoService::atualizarEnderecoPortador,
        enderecoService::atualizarEnderecoUserEstabelecimento,
        em);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Adicionado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por confirmar e atualizar um endereço de uma Pessoa")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/confirmar",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, Object>> confirmar(
      @RequestBody @Valid EnderecoPessoaUpdateRequest model,
      BindingResult result,
      HttpServletRequest httpServletRequest) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de endereço", result);
    }

    model.setConfirmarEndereco(true);
    callFunctionUserOrFunctionPortador(
        httpServletRequest,
        model,
        enderecoService::atualizarEnderecoUser,
        enderecoService::atualizarEnderecoPortador,
        em);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Adicionado com sucesso");
    map.put("ok", true);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por confirmar um endereço de uma Pessoa")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/confirmar/{idEndereco}",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, Object>> confirmar(
      @PathVariable Long idEndereco, HttpServletRequest httpServletRequest) {

    callFunctionUserOrFunctionPortador(
        httpServletRequest,
        idEndereco,
        enderecoService::confirmaEnderecoUser,
        enderecoService::confirmarEnderecoPortador,
        em);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Confirmado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por ativar um endereço de uma Pessoa")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/ativar/{idEndereco}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATIVAR_ENDERECO_PESSOA"})
  public ResponseEntity<Map<String, Object>> ativar(@PathVariable Long idEndereco) {

    enderecoService.ativarEndereco(idEndereco);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Ativado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por desativar um endereço de uma Pessoa")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/desativar/{idEndereco}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESATIVAR_ENDERECO_PESSOA"})
  public ResponseEntity<Map<String, Object>> desativar(@PathVariable Long idEndereco) {

    enderecoService.desativarEndereco(idEndereco);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Endereço Desativado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  private EnderecoPessoa prepareEndereco(EnderecoPessoaRequest model, EnderecoPessoa endereco) {
    BeanUtils.copyProperties(model, endereco, getNullPropertyNames(model));
    return endereco;
  }

  private EnderecoPessoa[] prepareEnderecos(
      EnderecosPessoaRequest model, EnderecoPessoa[] enderecos) {

    for (int i = 0; i < model.getEnderecos().size(); i++) {
      EnderecoPessoa endereco = new EnderecoPessoa();
      enderecos[i] = prepareEndereco(model.getEnderecos().get(i), endereco);
    }

    return enderecos;
  }

  @ApiOperation(
      value = "Consultar registros de endereço pessoa",
      response = Boolean.class,
      notes = "Consulta registros de endereço pessoa")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/endereco-pessoa",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({
    "ROLE_BUSCAR_ENDERECOS_PESSOA_STATUS"
  }) // Utilizando ROLE já disponível em ambiente produtivo para portadores da instituição 2401
  public ResponseEntity<List<EnderecoPessoa>> consultarEnderecoPessoa() {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    List<EnderecoPessoa> lista = enderecoService.consultarEnderecoPessoa(userPortador);

    return new ResponseEntity<>(lista, HttpStatus.OK);
  }
}
