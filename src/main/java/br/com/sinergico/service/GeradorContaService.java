package br.com.sinergico.service;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.ContaGerada;
import br.com.json.bean.cadastral.ContaPagamentoRequest;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DVUtil;
import com.google.common.base.Strings;
import java.time.LocalDateTime;
import java.util.Objects;
import javax.persistence.NoResultException;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GeradorContaService {

  private static final int TIPO_CONTA_40 = 40;
  private static final int TIPO_CONTA_30 = 30;
  private static final int ID_REL_1 = 1;
  private static final int BASE_10 = 10;
  private static final int TAM_SEQ_CONTA = 6;
  private static final char CHAR_ZERO = '0';
  private static final Integer STATUS_ATIVO = 1;
  private static final int ID_REL_2 = 2;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfService;

  @Autowired private ContaPagamentoService contaService;

  @Autowired private HierarquiaInstituicaoService instituicaoService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Transactional
  public ContaGerada createContaPagamento(ContaPagamentoRequest model, ContaPagamento conta) {

    HierarquiaInstituicao hierarquiaInstituicao = obterHierarquiaInstituicao(model);

    ProdutoInstituicaoConfiguracao produto = obterProdutoInstituicaoConfiguracao(model);

    ProdutoInstituicao produtoInstituicao = obterProdutoInstituicao(produto);

    // se o produto for b2b a conta ja nascera ativa
    if (produtoInstituicao.getB2b()) {
      conta.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);
    }

    String idConta = null;

    // garantir a integridade da conta gerada
    synchronized (hierarquiaInstituicao) {
      idConta = montarConta(hierarquiaInstituicao, produto.getIdRelacionamento());
      updateHierarquiaInstituicao(hierarquiaInstituicao);

      conta.setIdContaPagamento(idConta);
      conta.setIdProdutoInstituicao(model.getIdProdutoInstituicao());
      conta.setIdProdutoInstituidor(produto.getIdProdutoInstituidor());
      conta.setIdProdutoPlataforma(produto.getIdProdutoPlataforma());
      conta.setIdRelacionamento(produto.getIdRelacionamento());
      LocalDateTime data = LocalDateTime.now();
      conta.setDataHoraInclusao(data);
      conta.setDataHoraStatusConta(data);

      contaService.save(conta);
    }

    return new ContaGerada(idConta);
  }

  private HierarquiaInstituicao obterHierarquiaInstituicao(ContaPagamentoRequest model) {
    HierarquiaInstituicao hierarquiaInstituicao =
        instituicaoService.findById(
            new HierarquiaInstituicaoId(model.getIdProcessadora(), model.getIdInstituicao()));
    if (Objects.isNull(hierarquiaInstituicao)) {
      throw new NoResultException(
          "Não foi possível iniciar a geração da conta com os parametros informados: processadora ="
              + model.getIdProcessadora()
              + ", instituicao = "
              + model.getIdInstituicao()
              + ", filial = "
              + model.getIdFilial());
    }
    return hierarquiaInstituicao;
  }

  private ProdutoInstituicaoConfiguracao obterProdutoInstituicaoConfiguracao(
      ContaPagamentoRequest model) {
    ProdutoInstituicaoConfiguracao produto =
        prodInstConfService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            model.getIdProcessadora(), model.getIdProdutoInstituicao(), model.getIdInstituicao());

    if (produto == null) {
      throw new GenericServiceException(
          "Não foi possível iniciar a geração da conta. ProdutoInstituicaoConfiguracao não encontrado.  processadora ="
              + model.getIdProcessadora()
              + ", instituicao = "
              + model.getIdInstituicao()
              + ",idProdutoInstituicao = "
              + model.getIdProdutoInstituicao());
    }
    return produto;
  }

  private ProdutoInstituicao obterProdutoInstituicao(ProdutoInstituicaoConfiguracao produto) {
    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(produto.getIdProdInstituicao());

    if (Objects.isNull(produtoInstituicao)) {
      throw new GenericServiceException(
          "Não foi possível iniciar a geração da conta. ProdutoInstituicao não encontrado.",
          "IdProdutoInstituicao: " + produto.getIdProdInstituicao());
    }
    return produtoInstituicao;
  }

  private void updateHierarquiaInstituicao(HierarquiaInstituicao hierarquiaInstituicao) {
    instituicaoService.save(hierarquiaInstituicao);
  }

  /**
   * Identificador do tipo de conta multiplicado por 10 + sequencial na instituição de 6 posições +
   * dígito verificador de 1 posição.
   *
   * @param hierarquiaInstituicao
   * @return
   */
  private String montarConta(
      HierarquiaInstituicao hierarquiaInstituicao, Integer idRelacionamento) {

    StringBuilder conta = new StringBuilder();

    Integer tipoConta = idRelacionamento * BASE_10;

    hierarquiaInstituicao.setSeqConta(hierarquiaInstituicao.getSeqConta() + 1);

    conta.append(tipoConta.toString());
    conta.append(
        Strings.padStart(hierarquiaInstituicao.getSeqConta().toString(), TAM_SEQ_CONTA, CHAR_ZERO));
    conta.append(DVUtil.montarDVBase10(conta.toString()));
    return conta.toString();
  }

  /**
   * Identificador do tipo de conta multiplicado por 10 + sequencial na instituição de 6 posições +
   * dígito verificador de 1 posição.
   *
   * @param hierarquiaInstituicao
   * @return
   */
  public static String montarContaCredPreemitida(
      HierarquiaInstituicao hierarquiaInstituicao, Integer idRelacionamento) {
    Integer tipoConta = 0;

    StringBuilder conta = new StringBuilder();

    tipoConta = getTipoContaCredPreEmitida(idRelacionamento);

    hierarquiaInstituicao.setSeqContaPreEmitida(hierarquiaInstituicao.getSeqContaPreEmitida() + 1);

    conta.append(tipoConta.toString());
    conta.append(
        Strings.padStart(
            hierarquiaInstituicao.getSeqContaPreEmitida().toString(), TAM_SEQ_CONTA, CHAR_ZERO));
    conta.append(DVUtil.montarDVBase10(conta.toString()));
    return conta.toString();
  }

  private static Integer getTipoContaCredPreEmitida(Integer idRelacionamento) {
    switch (idRelacionamento) {
      case ID_REL_1:
        return TIPO_CONTA_30;
      case ID_REL_2:
        return TIPO_CONTA_40;
      default:
        return TIPO_CONTA_30;
    }
  }
}
