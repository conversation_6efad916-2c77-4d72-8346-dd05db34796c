package br.com.sinergico.service.cadastral;

import static br.com.sinergico.enums.TipoPortadorLoginEnum.*;
import static br.com.sinergico.util.Constantes.*;

import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.entity.cadastral.AntifraudeCliente;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PessoaResponsavelDependente;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.ProdutoInstituicaoDependentes;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.cadastral.TipoLoginProduto;
import br.com.entity.cadastral.TipoPortadorLogin;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AntifraudeCafInstituicaoConfig;
import br.com.entity.suporte.AntifraudeCafPortador;
import br.com.entity.suporte.AntifraudeOcrValidado;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.LogAcessoPortador;
import br.com.entity.suporte.ParametroDefinicao;
import br.com.entity.suporte.ParametroProcessamentoSistema;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.suporte.PortadorDispositivo;
import br.com.entity.suporte.TipoStatus;
import br.com.entity.suporte.TokenRedefinicaoSenha;
import br.com.enumVO.ModeloEmailEnum;
import br.com.enumVO.TipoLogRequestError;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.antifraude.AlterarDispositivo;
import br.com.json.bean.antifraude.TokenCaf;
import br.com.json.bean.brb.EnvioEmailRequest;
import br.com.json.bean.cadastral.CadastrarPortadorLogin;
import br.com.json.bean.cadastral.CadastrarPortadorLoginContaDigital;
import br.com.json.bean.cadastral.CadastrarPortadorLoginPreCadastradoRequest;
import br.com.json.bean.cadastral.DadosPortador;
import br.com.json.bean.cadastral.DetalhesAcessoPortador;
import br.com.json.bean.cadastral.DetalhesPortadorLogin;
import br.com.json.bean.cadastral.FazerLoginPortador;
import br.com.json.bean.cadastral.FazerLoginPortadorResponse;
import br.com.json.bean.cadastral.PortadorLoginCredencialVO;
import br.com.json.bean.cadastral.PortadorLoginVo;
import br.com.json.bean.cadastral.RecuperarSenhaEmailPortador;
import br.com.json.bean.cadastral.RecuperarSenhaPortador;
import br.com.json.bean.cadastral.TrocarEmail;
import br.com.json.bean.cadastral.ValidarDadosOnboardVO;
import br.com.json.bean.cadastral.ValidarDispositivo;
import br.com.json.bean.cadastral.ValidarPortadorLogin;
import br.com.json.bean.enums.TipoLoginRegistroEnum;
import br.com.json.bean.suporte.GerarTokenCadastroLogin;
import br.com.json.bean.suporte.RedefinirSenhaPortador;
import br.com.json.bean.suporte.TrocarSenhaEmailPortador;
import br.com.json.bean.suporte.TrocarSenhaPortador;
import br.com.json.bean.suporte.ValidarCadastroOnboardVO;
import br.com.json.bean.suporte.VerificaVersaoResponse;
import br.com.sinergico.controller.Token;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.AntifraudeCafFacialObjetivosEnum;
import br.com.sinergico.enums.AntifraudeCafPortadorStatusEnum;
import br.com.sinergico.enums.AplicativoEnum;
import br.com.sinergico.enums.EmailBlacklist;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.enums.OneSignalEnum;
import br.com.sinergico.enums.RegraTipoPortadorEnum;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.facade.transacional.ControleGarantiaFacade;
import br.com.sinergico.repository.cadastral.PortadorLoginRepository;
import br.com.sinergico.repository.suporte.AcessoServicoRepository;
import br.com.sinergico.repository.suporte.AntifraudeOcrValidadoRepository;
import br.com.sinergico.security.PasswordValidatorService;
import br.com.sinergico.security.PortadorAuthentication;
import br.com.sinergico.security.PortadorService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.security.TokenAuthenticationPortadorService;
import br.com.sinergico.service.AplicativoFrontendService;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.pix.ContaTransacionalService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.LogAcessoPortadorService;
import br.com.sinergico.service.suporte.LogRequestErrorIpBlacklistService;
import br.com.sinergico.service.suporte.LogRequestErrorService;
import br.com.sinergico.service.suporte.ParametroProcessamentoSistemaService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.suporte.PortadorDispositivoService;
import br.com.sinergico.service.suporte.RegistroValidacaoFacialCafService;
import br.com.sinergico.service.suporte.TipoStatusService;
import br.com.sinergico.service.suporte.TokenAcessoService;
import br.com.sinergico.service.suporte.TokenRedefinicaoSenhaService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.VersaoAplicativoService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.totvs.api.PreRegistroContatoTotvsService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.DocumentoUtil;
import br.com.sinergico.util.PessoaUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.validator.UtilValidator;
import br.com.sinergico.vo.HierarquiaInstituicaoVO;
import br.com.sinergico.vo.vcn.DadosSensiveisCredencial;
import com.google.common.base.Strings;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PortadorLoginService extends GenericService<PortadorLogin, Long> {

  private static final String URL_RECADASTRAR_SENHA_PORTADOR = "url.reset.senha.port";
  private static final String CREATED = "created";

  private static final String MSG = "msg";

  private static final String DETALHE = "DTL";

  private static final String MSG_DADOS_INV_CRIAR_LOGIN =
      "Não foi possível Realizar Operação. Dados inválidos. ";
  public static final String DETALHE_ERRO_DATA_VALIDADE_CREDENCIAL =
      "Data de validade da credencial não informada. Padrão MM/yy.";
  public static final String DETALHE_ERRO_DATA_VALIDADE_INCORRETA =
      "Data de validade da informada não confere. Padrão MM/yy.";
  public static final String DETALHE_ERRO_LOGIN_JA_EXISTENTE =
      "Cadastro já existente. Por favor, realize o login.";
  public static final String DETALHE_ERRO_LOGIN_INEXISTENTE =
      "Login não encontrado para as informações enviadas.";

  private static final Integer PESSOA_FISICA = 1;

  @SuppressWarnings("unnused")
  private static final Integer PESSOA_JURIDICA = 2;

  private static final Integer CANCELADO = 9;

  @SuppressWarnings("unnused")
  private static final Integer ORIGEM_CELULAR = 1;

  @SuppressWarnings("unnused")
  private static final Integer ORIGEM_WEB = 2;

  private static final Integer STATUS_ATIVO = 1;
  private static final Integer DESBLOQUEADO = 1;
  public static final Integer USUARIO_PORTADOR = 999999;
  public static final int TITULAR = 1;

  public static final int INSTITUICAO_BLOQUEADA = 9;

  private static final Logger log = LoggerFactory.getLogger(PortadorLoginService.class);
  public static final int TAMANHO_SENHA_REDEFINICAO = 16;
  public static final String ATUALIZA_CELULAR_ONBOARD = "atualiza.cel.onboard";

  private PortadorLoginRepository portadorLoginRepository;

  @Autowired private HttpSession session;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private AcessoServicoRepository acessoServicoRepository;

  @Autowired private TipoStatusService tipoStatusService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private PortadorDispositivoService portadorDispositivoService;

  @Autowired private RegistroValidacaoFacialCafService registroValidacaoFacialCafService;

  @Autowired private ControleGarantiaFacade controleGarantiaFacade;

  @Autowired private VersaoAplicativoService versaoAplicativoService;

  @Autowired private LogAcessoPortadorService logAcessoPortadorService;

  @Autowired private CardService cardService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ContaTransacionalService contaTransacionalService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private EmailService emailService;

  @Autowired private ParametroValorService paramValorService;

  @Autowired private PortadorService portadorService;

  @Autowired private PortadorLoginContaService portadorLoginContaService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private TokenRedefinicaoSenhaService tokenRedefinicaoSenhaService;

  @Autowired @Lazy private AntifraudeService antifraudeService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired private LogRequestErrorService logRequestErrorService;

  @Autowired private ParametroProcessamentoSistemaService parametroProcessamentoSistemaService;

  @Autowired private RepresentanteLegalService representanteLegalService;

  @Autowired private AntifraudeOcrValidadoRepository vallooOcrRepository;

  @Autowired private LogRequestErrorIpBlacklistService logRequestErrorIpBlacklistService;

  @Autowired private ProdutoInstituicaoDependentesService produtoInstituicaoDependentesService;

  @Autowired private PessoaResponsavelDependenteService pessoaResponsavelDependenteService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private PasswordValidatorService passwordValidatorService;

  @Autowired private TipoLoginProdutoService tipoLoginProdutoService;

  @Autowired private TokenAuthenticationPortadorService tokenAuthenticationPortadorService;

  @Autowired private UtilService utilService;

  @Autowired private PreRegistroContatoTotvsService preRegistroContatoTotvsService;

  @Autowired private TokenAcessoService tokenAcessoService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private EventoService eventoService;

  @Autowired private AplicativoFrontendService aplicativoFrontendService;

  @Autowired private EnderecoPessoaService enderecoService;

  @Autowired
  public PortadorLoginService(PortadorLoginRepository repo) {
    super(repo);
    portadorLoginRepository = repo;
  }

  public List<PortadorLogin> findLoginsWithPessoa(Pessoa pessoa, Integer titularidade) {
    return findLoginsWithPessoa(pessoa, titularidade, null);
  }

  public List<PortadorLogin> findLoginsWithPessoa(
      Pessoa pessoa, Integer titularidade, TipoPortadorLoginEnum tipoPortadorLoginEnum) {

    return findLoginsWithPessoa(
        pessoa.getIdProcessadora(),
        pessoa.getIdInstituicao(),
        pessoa.getDocumento(),
        titularidade,
        pessoa.getIdSetorFilial(),
        tipoPortadorLoginEnum);
  }

  public List<PortadorLogin> findLoginsWithPessoa(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      Integer titularidade,
      Integer setorFilial,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginRepository.findLoginsWithPessoa(
        idProcessadora, idInstituicao, documento, titularidade, setorFilial, tipoPortadorLoginEnum);
  }

  /**
   * Usado para logins RegraTipoPortadorEnum.SIMPLES, RegraTipoPortadorEnum.RESPONSAVEL,
   * RegraTipoPortadorEnum.DEPENDENTE
   */
  public PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          String cpf,
          TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginRepository
        .findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, cpf, tipoPortadorLoginEnum);
  }

  /**
   * Usado para logins RegraTipoPortadorEnum.PESSOA_ADICIONAL_PF_CONTA_PJ e
   * RegraTipoPortadorEnum.REPRESENTANTE_LEGAL
   */
  public PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndTipoLoginAndGrupoAcessoIsNullDataHoraCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          String cpf,
          String documentoAcesso,
          TipoPortadorLoginEnum tipoLogin) {
    return portadorLoginRepository
        .findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndTipoLoginAndGrupoAcessoIsNullDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, cpf, documentoAcesso, tipoLogin);
  }

  /** Usado para logins RegraTipoPortadorEnum.MULTIBENEFICIOS */
  public PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndGrupoAcessoAndTipoLoginAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          String cpf,
          Long grupoAcesso,
          TipoPortadorLoginEnum tipoLogin) {
    return portadorLoginRepository
        .findByIdProcessadoraAndIdInstituicaoAndCpfAndGrupoAcessoAndTipoLoginAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, cpf, grupoAcesso, tipoLogin);
  }

  /** Usado para logins RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL */
  public PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndGrupoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          String cpf,
          String documentoAcesso,
          Long grupoAcesso,
          TipoPortadorLoginEnum tipoLogin) {
    return portadorLoginRepository
        .findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndGrupoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, cpf, documentoAcesso, grupoAcesso, tipoLogin);
  }

  /** Usado para logins RegraTipoPortadorEnum.CORPORATIVO */
  public PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndDocumentoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
          Integer idProcessadora,
          Integer idInstituicao,
          String documentoAcesso,
          TipoPortadorLoginEnum tipoLogin) {
    return portadorLoginRepository
        .findByIdProcessadoraAndIdInstituicaoAndDocumentoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, documentoAcesso, tipoLogin);
  }

  public void registraIMEIPrimeiroAcesso(Long idLogin, CadastrarPortadorLogin model) {
    try {
      PortadorDispositivo dispositivo = new PortadorDispositivo();
      BeanUtils.copyProperties(model, dispositivo, getNullPropertyNames(model));
      dispositivo.setDataCadastro(LocalDateTime.now());
      dispositivo.setPermiteNotificacao(Boolean.FALSE);
      dispositivo.setIdLogin(idLogin);
      dispositivo.setPerguntouPermiteNotificacao(Boolean.FALSE);
      portadorDispositivoService.save(dispositivo);

      AntifraudeCliente antifraudeCliente =
          Util.isNotNull(model.getCnpj())
                  && !model.getCnpj().isEmpty()
                  && Util.isNotNull(model.getCpf())
                  && !model.getCpf().isEmpty()
              ? antifraudeService.findByIdInstituicaoAndDocumentoAndDocumentoRepresentante(
                  model.getIdInstituicao().longValue(), model.getCnpj(), model.getCpf())
              : antifraudeService.findByIdInstituicaoAndDocumento(
                  model.getIdInstituicao().longValue(), model.getCpf());

      if (antifraudeCliente != null) {
        antifraudeCliente.setSenhaRedefinidaOCR(true);
        antifraudeService.salvar(antifraudeCliente);
      }

    } catch (Exception e) {
      e.printStackTrace();
      log.info("Não foi possível realizar o cadastro: " + e.getMessage());
    }
  }

  @Transactional
  public void updateEmailPortadorLoginByPessoa(String email, Pessoa pessoa) {

    if (Constantes.PESSOA_JURIDICA.equals(pessoa.getIdTipoPessoa())) {
      return;
    }

    List<PortadorLogin> portadorLoginAcessoContaTitular = findLoginsWithPessoa(pessoa, TITULAR);
    if (portadorLoginAcessoContaTitular != null && !portadorLoginAcessoContaTitular.isEmpty()) {
      portadorLoginAcessoContaTitular.forEach(portador -> portador.setEmail(email));
      portadorLoginRepository.saveAll(portadorLoginAcessoContaTitular);
    }

    List<PortadorLogin> portadorLoginAcessoContaAdicional =
        findLoginsWithPessoa(pessoa, Constantes.ADICIONAL);
    if (portadorLoginAcessoContaAdicional != null && !portadorLoginAcessoContaAdicional.isEmpty()) {
      portadorLoginAcessoContaAdicional.forEach(portador -> portador.setEmail(email));
      portadorLoginRepository.saveAll(portadorLoginAcessoContaAdicional);
    }
  }

  public ResponseEntity<FazerLoginPortadorResponse> doLogin(
      HttpServletRequest request, FazerLoginPortador login) throws IOException {

    if (request.getHeader("Proxy-Authorization") != null) {
      throw new GenericServiceException("Acesso impedido.", HttpStatus.UNAUTHORIZED);
    }

    HierarquiaInstituicaoVO hierarquiaInstituicao =
        controleGarantiaFacade.getInstituicao(login.getIdProcessadora(), login.getIdInstituicao());

    if (hierarquiaInstituicao.getStatus().equals(INSTITUICAO_BLOQUEADA)) {
      throw new GenericServiceException("Serviço temporariamente indisponível para a instituição");
    }

    logRequestErrorIpBlacklistService.verificarIPBloqueado(request, login);

    boolean isCpf = login.getCpf() != null && !login.getCpf().isEmpty();
    if (!isCpf && (login.getCnpj() == null || login.getCnpj().isEmpty())) {
      log.error(
          "isCpf = "
              + (login.getCpf() != null && !login.getCpf().isEmpty())
              + ", login.getCpf = "
              + login.getCpf()
              + ", login.getCnpj = "
              + login.getCnpj());
      throw new GenericServiceException("Erro de definição CPF/CNPJ");
    }

    PortadorLogin portadorLogin = buscarLogin(login, "Erro ao efetuar login");
    tratarLoginCorporativo(portadorLogin, login);

    String msgErro =
        (login.getDocumentoAcesso() != null && !login.getDocumentoAcesso().isEmpty())
            ? "CPF / CNPJ ou senha inválido."
            : (isCpf ? "CPF ou senha inválido." : "CNPJ ou senha inválido.");

    if (portadorLogin != null
        && portadorLogin.getTipoLoginEnum() != null
        && RegraTipoPortadorEnum.CORPORATIVO.equals(
            portadorLogin.getTipoLoginEnum().getRegraTipoPortadorLoginEnum())) {
      msgErro = "CNPJ / Número do cartão ou senha inválido.";
    }

    verificarELancarErroAutenticacaoPortador(request, login, portadorLogin, msgErro);

    portadorLoginContaService.criaPortadoresConta(portadorLogin);

    if (hierarquiaInstituicao.getValidaContaAtiva()) {
      contaPagamentoService.validaContaAtiva(portadorLogin);
    }

    if (isPortadorSenhaExpirada(portadorLogin)) {
      HttpStatus sendHttpStatus = HttpStatus.FORBIDDEN;
      HashMap<String, String> mapLogRequestError =
          this.logRequestErrorService.preencherMapLogRequestError(
              request, login, TipoLogRequestError.LOGIN, sendHttpStatus);
      this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

      String msg =
          "Sua senha está expirada! Siga os passos do e-mail enviado à você. "
              + "Caso você não recebeu o e-mail, entre na opção \"Esqueci a senha\" e realize uma nova solicitação.";
      throw new GenericServiceException(msg, sendHttpStatus);
    }

    Integer idInstituicaoParaConsultarPessoa =
        Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY.equals(login.getIdInstituicao())
            ? 2401
            : login.getIdInstituicao();
    login.setIdInstituicao(idInstituicaoParaConsultarPessoa);

    // Recuperar Contas Pagamentos do Portador
    carregarContasPortador(portadorLogin);

    // Recuperar Credenciais do Portador
    carregarCredenciaisPortador(portadorLogin);

    Token portadorToken = new Token(makeAuthenication(request, session, portadorLogin));

    LogAcessoPortador lap = new LogAcessoPortador();
    lap.setDataHoraAcesso(LocalDateTime.now());
    String remoteAddr = request.getHeader("X-Forwarded-For");
    remoteAddr = remoteAddr == null ? request.getRemoteAddr() : remoteAddr;
    lap.setIp(remoteAddr);
    BeanUtils.copyProperties(login, lap, getNullPropertyNames(login));
    lap.setIdLogin(portadorLogin.getIdLogin());

    FazerLoginPortadorResponse resp = registrarLogin(portadorLogin, lap, portadorToken.getToken());
    // nao remover essa propriedade de setar o id login
    resp.setIdLogin(portadorLogin.getIdLogin());
    AntifraudeCafInstituicaoConfig antifraudeCafInstituicaoConfig =
        this.antifraudeService.buscarConfiguracaoCaf(portadorLogin.getIdInstituicao());
    if (antifraudeCafInstituicaoConfig != null) {
      AntifraudeCafPortador antifraudeCafPortador =
          this.antifraudeService.buscarCaf(
              portadorLogin.getCpf(), portadorLogin.getIdInstituicao(), login.getDocumentoAcesso());
      resp.setIsAntifraudeValidacaoNecessaria(false);
      resp.setEncaminharAtendimento(false);
      resp.setOnboardRealizado(false);

      // Verificar se validacaoCaf está habilitado antes de setar IsAntifraudeValidacaoNecessaria
      Boolean validacaoCafHabilitada =
          aplicativoFrontendService.validacaoCafHabilitada(
              portadorLogin.getIdInstituicao(), portadorLogin.getIdProcessadora());

      if (antifraudeCafPortador == null && validacaoCafHabilitada) {
        resp.setIsAntifraudeValidacaoNecessaria(true);
      }
      if (antifraudeCafPortador != null && antifraudeCafPortador.getIdStatus() == null) {
        resp.setEncaminharAtendimento(true);
      }
      if (antifraudeCafPortador != null
          && antifraudeCafPortador.getIdStatus() != null
          && !antifraudeCafPortador
              .getIdStatus()
              .equals(AntifraudeCafPortadorStatusEnum.APPROVED.getStatus())) {
        resp.setEncaminharAtendimento(true);
      }
      if (antifraudeCafPortador != null
          && antifraudeCafPortador.getBlForcarValidacao()
          && validacaoCafHabilitada) {
        resp.setIsAntifraudeValidacaoNecessaria(true);
        resp.setEncaminharAtendimento(false);
      }
      this.validarDispositivoUsuarioInstituicao(login, portadorLogin, resp);
      if (antifraudeCafPortador != null && antifraudeCafPortador.getBlIgnorarValidacao()) {
        resp.setIsAntifraudeValidacaoNecessaria(false);
        resp.setEncaminharAtendimento(false);
        resp.setIsDeviceIdValido(true);
        resp.setLimitesPIXAlterados(false);
      }
      // comentado para nao ser executado enquanto nao definir a regra de troca de dispositivo
      //       resp.setLimitesPIXAlterados(this.alterarLimitesPIXTrocaDispositivo(login,
      // portadorLogin));
      if (antifraudeCafPortador != null
          && antifraudeCafPortador.getTxUrlSelfie() != null
          && !antifraudeCafPortador.getTxUrlSelfie().isEmpty()
          && AntifraudeCafPortadorStatusEnum.APPROVED
              .getStatus()
              .equals(antifraudeCafPortador.getIdStatus())) {
        resp.setEncaminharAtendimento(false);
        resp.setOnboardRealizado(true);
      }
      this.validaVersaoInstalada(login, portadorLogin, resp);
    }

    request.getSession().setAttribute("portadorLogin", portadorLogin);
    return new ResponseEntity<>(resp, HttpStatus.OK);
  }

  private void tratarLoginCorporativo(PortadorLogin portadorLogin, FazerLoginPortador login) {

    if (portadorLogin != null
        && portadorLogin
            .getTipoLoginEnum()
            .getRegraTipoPortadorLoginEnum()
            .equals(RegraTipoPortadorEnum.CORPORATIVO)) {
      Pessoa pessoa =
          pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
              portadorLogin.getIdProcessadora(),
              portadorLogin.getIdInstituicao(),
              login.getCnpj(),
              Constantes.PESSOA_JURIDICA);

      if (!pessoa.getDocumento().equals(login.getCnpj())) {
        throw new GenericServiceException("Dados não conferem", HttpStatus.UNAUTHORIZED);
      }
    }
  }

  private void verificarELancarErroAutenticacaoPortador(
      HttpServletRequest request,
      FazerLoginPortador login,
      PortadorLogin portadorLogin,
      String msgErro) {

    if (portadorLogin == null || !portadorLogin.getSenha().equals(login.getSenha().trim())) {

      HashMap<String, String> mapLogRequestError =
          this.logRequestErrorService.preencherMapLogRequestError(
              request, login, TipoLogRequestError.LOGIN, HttpStatus.UNAUTHORIZED);
      this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

      throw new BadCredentialsException(msgErro);
    }
  }

  /* TODO: Alterando a forma de identificar dispositivo alterado de modo paliativo em 21/01/2025.
  TODO: Reajustar quando for encontrado o melhor modo de identificar dispositivos únicos, além do uso de DeviceID */
  private void validarDispositivoUsuarioInstituicao(
      FazerLoginPortador login, PortadorLogin portadorLogin, FazerLoginPortadorResponse resp) {

    PortadorDispositivo dispositivo =
        portadorDispositivoService
            .findFirstByIdLoginAndDataInvalidadoIsNullOrderByIdPortadorDispositivoDesc(
                portadorLogin.getIdLogin());

    if (!Objects.isNull(dispositivo)) {
      String dispositivoModel =
          dispositivo.getModel() != null ? dispositivo.getModel().trim().toUpperCase() : "";
      String loginModel = login.getModel() != null ? login.getModel().trim().toUpperCase() : "";

      dispositivo.setLatitude(login.getLatitude());
      dispositivo.setLongitude(login.getLongitude());
      dispositivo.setDeviceId(login.getDeviceId());
      dispositivo.setModel(login.getModel());
      dispositivo.setPlataformVersion(login.getPlataformVersion());
      dispositivo.setArchitectureInfo(login.getArchitectureInfo());
      dispositivo.setPlatformName(login.getPlatformName());
      dispositivo.setVersaoAplicativo(login.getVersaoAplicativo());
      dispositivo.setPushNotificationDeviceId(login.getPushNotificationDeviceId());
      portadorDispositivoService.save(dispositivo);
      if (!loginModel.equals(dispositivoModel)) {
        eventoService.publicarTrocaDispositivoEvent(dispositivo);
      }
      log.info(
          "CPF: "
              + login.getCpf()
              + ", Instituicao: "
              + login.getIdInstituicao()
              + ", deviceId: "
              + login.getDeviceId()
              + ", ArchitectureInfo: "
              + login.getArchitectureInfo()
              + ", PlatformName: "
              + login.getPlatformName()
              + ", Model: "
              + login.getModel());
    }

    // REMOVER ESTE SET TRUE QUANDO FOREM SOLUCIONADAS AS PENDENCIAS DE TROCA DE DISPOSITIVO POR
    // VALOR UNICO
    resp.setIsDeviceIdValido(true);
  }

  /* TODO: Alterando a forma de identificar dispositivo alterado de modo paliativo em 21/01/2025.
  TODO: Reajustar quando for encontrado o melhor modo de identificar dispositivos únicos, além do uso de DeviceID */
  public Boolean alterarLimitesPIXTrocaDispositivo(
      FazerLoginPortador loginPortador, PortadorLogin portadorLogin) {

    // REMOVER ESTE EARLY RETURN QUANDO FOREM SOLUCIONADAS AS PENDENCIAS ACIMA
    if (portadorLogin != null) {
      return Boolean.FALSE;
    }

    //    if (loginPortador == null || loginPortador.getDeviceId() == null) {
    //      return Boolean.FALSE;
    //    }

    if (loginPortador == null
        || loginPortador.getArchitectureInfo() == null
        || loginPortador.getPlatformName() == null
        || loginPortador.getModel() == null) {
      return Boolean.FALSE;
    }

    List<PortadorDispositivo> dispositivosExistentes =
        portadorDispositivoService.findByIdLogin(portadorLogin.getIdLogin());

    //    boolean dispositivoEncontrado =
    //        dispositivosExistentes.stream()
    //            .anyMatch(
    //                dispositivo ->
    //                    dispositivo.getDeviceId() != null
    //                        && dispositivo
    //                            .getDeviceId()
    //                            .trim()
    //                            .equalsIgnoreCase(loginPortador.getDeviceId().trim()));

    boolean dispositivoEncontrado =
        dispositivosExistentes.stream()
            .anyMatch(
                dispositivo ->
                    dispositivo.getArchitectureInfo() != null
                        && dispositivo.getPlatformName() != null
                        && dispositivo.getModel() != null
                        && dispositivo
                            .getArchitectureInfo()
                            .trim()
                            .equalsIgnoreCase(loginPortador.getArchitectureInfo().trim())
                        && dispositivo
                            .getPlatformName()
                            .trim()
                            .equalsIgnoreCase(loginPortador.getPlatformName().trim())
                        && dispositivo
                            .getModel()
                            .trim()
                            .equalsIgnoreCase(loginPortador.getModel().trim()));

    if (dispositivoEncontrado) {
      return Boolean.FALSE;
    } else {
      contaTransacionalService.alteraLimitesTrocaDispositivo(portadorLogin);
      return Boolean.TRUE;
    }
  }

  /**
   * Método que valida se a versão instalada precisa ser atualizada
   *
   * @param login
   * @param portadorLogin
   * @param resp
   */
  private void validaVersaoInstalada(
      FazerLoginPortador login, PortadorLogin portadorLogin, FazerLoginPortadorResponse resp) {
    // verifica versão do aparelho
    if (login.getVersaoInstalada() != null && !login.getVersaoInstalada().isEmpty()) {
      VerificaVersaoResponse verificarVersao =
          versaoAplicativoService.verificarVersao(
              login.getIdProcessadora(), login.getIdInstituicao(),
              login.getSistemaOperacional(), login.getIdApp(),
              login.getVersaoConhecida(), login.getVersaoInstalada());
      BeanUtils.copyProperties(verificarVersao, resp, getNullPropertyNames(verificarVersao));
      resp.setRequisitarAtualizacao(!verificarVersao.getPermiteContinuar());
      resp.setRequisicaoAtualizacaoMensagem(verificarVersao.getMensagem());
      resp.setVersaoMaisRecente(verificarVersao.getVersaoAtual());
      resp.setOneSignal(OneSignalEnum.getByIdInstituicao(portadorLogin.getIdInstituicao()));
    }
  }

  public ResponseEntity<?> refreshToken(
      SecurityUserPortador portadorLogin, HttpServletRequest request) {

    // Recuperar Contas Pagamentos do Portador
    carregarContasPortador(portadorLogin);

    // Recuperar Credenciais do Portador
    carregarCredenciaisPortador(portadorLogin);

    Token portadorToken = new Token(makeAuthenication(request, session, portadorLogin));

    FazerLoginPortadorResponse resp = new FazerLoginPortadorResponse();
    resp.setToken(portadorToken.getToken());

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(
        portadorLogin.getIdInstituicao())) {
      contaPagamentoService.validaContaAtiva(portadorLogin);
      List<Credencial> credenciais = credencialService.obterCredenciaisPortador(portadorLogin);
      credencialService.validaAlgumaCredencialFisicaAtivaOuTemp(credenciais);
    }

    return new ResponseEntity<>(resp, HttpStatus.OK);
  }

  private void carregarContasPortador(PortadorLogin portadorLogin) {
    portadorLogin.setContasPortador(
        portadorLoginContaService.buscarIdsContasPagamentoAssociadosAoLogin(
            portadorLogin.getIdLogin()));
  }

  private void carregarCredenciaisPortador(PortadorLogin portadorLogin) {
    portadorLogin.setCredenciaisPortador(
        getCredencialService().recuperarCredenciaisPortador(portadorLogin.getIdLogin()));
  }

  private boolean isPortadorSenhaExpirada(PortadorLogin portadorLogin) {
    return portadorLogin.getSenhaExpirada();
  }

  private void registrarDispositivo(
      SecurityUserPortador userPortador, ValidarDispositivo validarDispositivo) {
    PortadorDispositivo dispositivo =
        portadorDispositivoService
            .findFirstByIdLoginAndDataInvalidadoIsNullOrderByIdPortadorDispositivoDesc(
                userPortador.getIdLogin());
    String deviceIdDispositivo =
        dispositivo != null && dispositivo.getDeviceId() != null
            ? dispositivo.getDeviceId().trim().toUpperCase()
            : "";
    String loginDeviceId = validarDispositivo.getDeviceId().trim().toUpperCase();

    if (dispositivo != null && deviceIdDispositivo.equals(loginDeviceId)) {
      dispositivo.setVersaoAplicativo(validarDispositivo.getVersaoAplicativo());
      dispositivo.setPushNotificationDeviceId(validarDispositivo.getPushNotificationDeviceId());
      dispositivo.setPerguntouPermiteNotificacao(Boolean.TRUE);
      portadorDispositivoService.save(dispositivo);
    }

    if (!deviceIdDispositivo.equals(loginDeviceId)) {
      dispositivo = new PortadorDispositivo();
      BeanUtils.copyProperties(
          validarDispositivo, dispositivo, getNullPropertyNames(validarDispositivo));
      dispositivo.setDataCadastro(LocalDateTime.now());
      dispositivo.setPermiteNotificacao(Boolean.FALSE);
      dispositivo.setIdLogin(userPortador.getIdLogin());
      dispositivo.setPerguntouPermiteNotificacao(Boolean.FALSE);
      dispositivo.setPushNotificationDeviceId(validarDispositivo.getPushNotificationDeviceId());
      dispositivo.setVersaoAplicativo(validarDispositivo.getVersaoAplicativo());
      portadorDispositivoService.save(dispositivo);
    }
  }

  public FazerLoginPortadorResponse registrarLogin(
      PortadorLogin portadorLogin, LogAcessoPortador lap, String portadorToken) {

    // pego a data do ultimo acesso antes de atualizar as informações
    LocalDateTime dataHoraUltimoAcesso = portadorLogin.getDataHoraUltimoAcesso();

    // atualizo a data do ultimo acesso
    /*
     * LocalDateTime now = LocalDateTime.now();
     * portadorLogin.setDataHoraUltimoAcesso(now);
     *
     * save(portadorLogin); logAcessoPortadorservice.save(lap);
     */

    FazerLoginPortadorResponse resp = new FazerLoginPortadorResponse();
    resp.setDataHoraUltimoAcessso(dataHoraUltimoAcesso);
    resp.setToken(portadorToken);
    return resp;
  }

  public void trocarSenhaPortador(TrocarSenhaPortador trocarSenha) {

    PortadorLogin portadorLogin = getPortadorLoginValido(trocarSenha);

    validarTempoRedefinicaoSenha(portadorLogin, false);

    portadorLogin.setSenha(trocarSenha.getNovaSenha());

    save(portadorLogin);
  }

  /**
   * Funcao que faz a definicao da senha do portador no cadastro
   *
   * @param trocarSenha
   */
  public void redefinirSenhaPortador(
      RedefinirSenhaPortador trocarSenha, HttpServletRequest request) {
    PortadorLogin portadorLogin;

    Boolean cafNecessario =
        encontraConfiguracaoCaf(
            trocarSenha.getIdInstituicao(),
            trocarSenha.getCpf(),
            Optional.ofNullable(trocarSenha.getCpfRepresentante()));
    if (cafNecessario) {

      Long idValidacao =
          registroValidacaoFacialCafService.checaValidacao(
              trocarSenha.getCpf(),
              trocarSenha.getIdInstituicao(),
              AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_LOGIN,
              AntifraudeCafFacialObjetivosEnum.ONBOARDING_CAF_FORCADO);
      if (idValidacao == null) {
        throw new GenericServiceException(
            "Não foi possível reconhecer a validação facial. Por favor, tente novamente!",
            HttpStatus.UNAUTHORIZED);
      }

      portadorLogin =
          validaAntigaSenhaDoPortadorERedefineSenha(trocarSenha, cafNecessario, request);

      registroValidacaoFacialCafService.efetivaValidacao(idValidacao);

    } else {

      if (trocarSenha.getToken() == null || trocarSenha.getToken().isEmpty()) {
        throw new GenericServiceException(
            "Token de confirmação necessário para alteração de senha.", HttpStatus.BAD_REQUEST);
      }

      List<TokenRedefinicaoSenha> tokenAcessoList =
          tokenRedefinicaoSenhaService
              .findAllByTokenIgnoreCaseAndIdInstituicaoAndIdProcessadoraAndDocumentoOrderByDataHoraGeracaoDesc(
                  trocarSenha.getToken(),
                  trocarSenha.getIdInstituicao(),
                  trocarSenha.getIdProcessadora(),
                  Util.isNotNull(trocarSenha.getCpfRepresentante())
                          && !trocarSenha.getCpfRepresentante().isEmpty()
                      ? trocarSenha.getCpfRepresentante()
                      : trocarSenha.getCpf());

      ParametroProcessamentoSistema parametroProcessamentoSistemaTokenGenerico =
          parametroProcessamentoSistemaService.findFirstByIdInstituicaoAndTipoParametro(
              trocarSenha.getIdInstituicao(), Constantes.TKN_REDEF_SENHA);

      // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
      // nao exista mesmo
      if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
        if (!validaParametroProcessamentoSistemaTokenGenerico(
            parametroProcessamentoSistemaTokenGenerico, trocarSenha)) {

          HashMap<String, String> mapLogRequestError =
              this.logRequestErrorService.preencherMapLogRequestError(
                  request,
                  trocarSenha,
                  TipoLogRequestError.REDEFINIR_SENHA,
                  HttpStatus.UNAUTHORIZED);
          this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

          throw new GenericServiceException(
              "Ocorreu um erro ao redefinir a senha, entre em contato com o nosso Suporte.",
              HttpStatus.UNAUTHORIZED);
        } else {
          portadorLogin =
              validaAntigaSenhaDoPortadorERedefineSenha(trocarSenha, cafNecessario, request);
          save(portadorLogin);
          return;
        }
      }

      TokenRedefinicaoSenha token = tokenAcessoList.get(0);
      validarDataValidade(tokenAcessoList.get(0));
      // validarChaveExterna(trocarSenha, token);

      if (Objects.isNull(token.getInTokenValidado())
          || token.getInTokenValidado()
          || token.getInTokenExpirado()
          || Objects.nonNull(token.getDataHoraUtilizacao())) {
        HashMap<String, String> mapLogRequestError =
            this.logRequestErrorService.preencherMapLogRequestError(
                request, trocarSenha, TipoLogRequestError.REDEFINIR_SENHA, HttpStatus.FORBIDDEN);
        this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

        throw new GenericServiceException(
            "Ocorreu um erro ao redefinir a senha, entre em contato com o nosso Suporte.",
            HttpStatus.FORBIDDEN);
      }

      portadorLogin =
          validaAntigaSenhaDoPortadorERedefineSenha(trocarSenha, cafNecessario, request);

      token.setInTokenExpirado(true);
      token.setInTokenValidado(true);
      token.setDataHoraUtilizacao(LocalDateTime.now());
      tokenRedefinicaoSenhaService.save(token);
    }

    save(portadorLogin);
  }

  public void trocarEmailESenhaPrimeiroAcesso(TrocarSenhaEmailPortador trocarSenhaEmail) {
    // Troca senha
    PortadorLogin portadorLogin =
        findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            trocarSenhaEmail.getIdProcessadora(),
            trocarSenhaEmail.getIdInstituicao(),
            trocarSenhaEmail.getCpf(),
            trocarSenhaEmail.getTipoLogin());

    HashMap<String, Object> map = new HashMap<>();
    if (portadorLogin == null) {

      map.put("msg", "Portador não encontrado para as informações enviadas.");
      map.put(
          "DTL",
          "IdProcessadora: "
              + trocarSenhaEmail.getIdProcessadora()
              + ", idInstituicao: "
              + trocarSenhaEmail.getIdInstituicao()
              + ",documento: "
              + trocarSenhaEmail.getCpf());
      map.put("sucesso", false);
      throw new GenericServiceException(map);
    }

    portadorLogin.setSenha(trocarSenhaEmail.getNovaSenha());
    save(portadorLogin);

    // Troca email
    if (EmailBlacklist.isBlacklistMail(trocarSenhaEmail.getNovoEmail())) {
      throw new GenericServiceException(
          "O domínio utilizado no email está na blacklist. Email: "
              + trocarSenhaEmail.getNovoEmail());
    }

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            portadorLogin.getIdProcessadora(),
            portadorLogin.getIdInstituicao(),
            portadorLogin.getCpf(),
            portadorLogin.getCpf().length() <= 11
                ? Constantes.PESSOA_FISICA
                : Constantes.PESSOA_JURIDICA);

    if (pessoa == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar uma pessoa para os dados informados. " + trocarSenhaEmail);
    }

    portadorLogin.setEmail(trocarSenhaEmail.getNovoEmail());
    pessoa.setEmail(trocarSenhaEmail.getNovoEmail());

    save(portadorLogin);
    atualizarPessoa(pessoa);
  }

  private PortadorLogin validaAntigaSenhaDoPortadorERedefineSenha(
      RedefinirSenhaPortador trocarSenha, Boolean cafNecessario, HttpServletRequest request) {

    if (ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(trocarSenha.getIdInstituicao())) {
      try {
        List<String> tiposLogin =
            determinaTipoLoginPorAplicativoEDocumento(7, trocarSenha.getCpf());
        if (tiposLogin != null && !tiposLogin.isEmpty()) {
          TipoPortadorLoginEnum tipoLoginEscoteirosDependenteOuResponsavel =
              TipoPortadorLoginEnum.getTipoLoginByName(tiposLogin.get(0));
          trocarSenha.setTipoLogin(tipoLoginEscoteirosDependenteOuResponsavel);
        }
      } catch (Exception e) {
        // TODO remover quando o aplicativo tiver corrigido
      }
    }
    PortadorLogin portadorLogin =
        buscarLoginEGarantirExistencia(
            trocarSenha.getIdProcessadora(),
            trocarSenha.getIdInstituicao(),
            trocarSenha.getCpf(),
            trocarSenha.getCpfRepresentante(),
            trocarSenha.getGrupoAcesso(),
            trocarSenha.getTipoLogin(),
            DETALHE_ERRO_LOGIN_INEXISTENTE);

    if (portadorLogin.getSenha().equals(trocarSenha.getNovaSenha())) {

      HashMap<String, String> mapLogRequestError =
          this.logRequestErrorService.preencherMapLogRequestError(
              request, trocarSenha, TipoLogRequestError.REDEFINIR_SENHA, HttpStatus.UNAUTHORIZED);
      this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

      throw new GenericServiceException(
          "A nova senha deve ser diferente da senha anterior. Tente novamente.",
          HttpStatus.UNAUTHORIZED);
    }

    portadorLogin.setSenha(trocarSenha.getNovaSenha());
    portadorLogin.setSenhaExpirada(false);

    if (cafNecessario) {
      AntifraudeCafPortador antifraudeCafPortador =
          this.antifraudeService.getStatusCaf(
              trocarSenha.getCpf(), portadorLogin.getIdInstituicao());
      if (antifraudeCafPortador != null
          && antifraudeCafPortador.getIdStatus() != null
          && antifraudeCafPortador
              .getIdStatus()
              .equals(AntifraudeCafPortadorStatusEnum.APPROVED.getStatus())) {
        this.criarPortadorDispositivo(trocarSenha, portadorLogin);
      }
    } else {
      this.criarPortadorDispositivo(trocarSenha, portadorLogin);
    }
    return portadorLogin;
  }

  private void criarPortadorDispositivo(
      RedefinirSenhaPortador redefinirSenhaPortador, PortadorLogin portadorLogin) {
    PortadorDispositivo dispositivo = new PortadorDispositivo();
    BeanUtils.copyProperties(
        redefinirSenhaPortador, dispositivo, getNullPropertyNames(redefinirSenhaPortador));
    dispositivo.setDataCadastro(LocalDateTime.now());
    dispositivo.setPermiteNotificacao(Boolean.FALSE);
    dispositivo.setIdLogin(portadorLogin.getIdLogin());
    dispositivo.setPerguntouPermiteNotificacao(Boolean.FALSE);
    portadorDispositivoService.save(dispositivo);
  }

  private boolean validaParametroProcessamentoSistemaTokenGenerico(
      ParametroProcessamentoSistema pps, RedefinirSenhaPortador rsp) {
    if (Objects.isNull(pps)) {
      return false;
    }
    return pps.getTexto().equals(rsp.getToken());
  }

  private void validarDataValidade(TokenRedefinicaoSenha tokenAcesso) {
    if (LocalDateTime.now().isAfter(tokenAcesso.getDataHoraExpiracaoToken())) {
      tokenAcesso.setInTokenExpirado(true);
      tokenRedefinicaoSenhaService.save(tokenAcesso);
      throw new GenericServiceException(
          "Código expirado. Solicite um novo", "Código expirado solicite um novo");
    }
  }

  private PortadorLogin getPortadorLoginValido(TrocarSenhaPortador trocarSenha) {

    PortadorLogin portadorLogin =
        buscarLoginEGarantirExistencia(
            trocarSenha.getIdProcessadora(),
            trocarSenha.getIdInstituicao(),
            trocarSenha.getCnpj() != null ? trocarSenha.getCnpj() : trocarSenha.getCpf(),
            (trocarSenha.getCnpj() != null && trocarSenha.getCpf() != null)
                ? trocarSenha.getCpf()
                : null,
            trocarSenha.getGrupoAcesso(),
            trocarSenha.getTipoLogin(),
            "Portador não encontrado para as informações enviadas.");
    if (!portadorLogin.getSenha().equals(trocarSenha.getSenha())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put(MSG, "Senha do usuário logado não confere");
      map.put("sucesso", false);
      throw new GenericServiceException(map);
    }
    return portadorLogin;
  }

  public String makeAuthenication(
      HttpServletRequest request, HttpSession session, PortadorLogin usuario) {

    List<String> distinctRoles =
        acessoServicoRepository.retornaRolesDistintasDoGrupoPortadorDaInstituicao(
            usuario.getIdProcessadora(), usuario.getIdInstituicao());
    SecurityUserPortador securityUser = new SecurityUserPortador(usuario, distinctRoles);
    PortadorService userService = new PortadorService();
    userService.addUser(session, securityUser);

    PortadorAuthentication userAuthentication = new PortadorAuthentication(securityUser);
    String portadorToken = tokenAuthenticationPortadorService.addAuthentication(userAuthentication);
    Authentication authentication = tokenAuthenticationPortadorService.getAuthentication(request);
    SecurityContextHolder.getContext().setAuthentication(authentication);

    return portadorToken;
  }

  public void refreshAuthentication(HttpServletRequest request, SecurityUserPortador securityUser) {

    try {
      PortadorService userService = portadorService;
      userService.addUser(request.getSession(), securityUser);

      Authentication authentication = tokenAuthenticationPortadorService.getAuthentication(request);
      SecurityContextHolder.getContext().setAuthentication(authentication);
    } catch (Exception e) {
      log.error("Não foi possível realizar o refreshAutentication: " + e.getMessage());
    }
  }

  public void create(CadastrarPortadorLogin model) {

    HashMap<String, Object> map = new HashMap<>();

    if (EmailBlacklist.isBlacklistMail(model.getEmail())) {
      throw new GenericServiceException(
          "O domínio utilizado no email está na blacklist. Email: " + model.getEmail());
    }

    if (!hierarquiaInstituicaoExists(model.getIdInstituicao(), model.getIdProcessadora())) {
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(model.getSenha())) {
      throw new GenericServiceException("Apenas os caracteres especiais !@#$%&*+= são aceitos.");
    }

    if (!passwordValidatorService.validate(
        model.getSenha(), Arrays.asList(model.getCpf(), model.getCnpj()))) {
      throw new GenericServiceException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números"
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.");
    }

    PortadorLogin portadorLogin = preparePortadorLogin(model);
    erroLoginCamposCpfOuCnpjNaoPreenchido(portadorLogin, map);

    validaELancaErroPortadorLoginPreviamenteExistente(
        portadorLogin.getIdProcessadora(),
        portadorLogin.getIdInstituicao(),
        portadorLogin.getCpf(),
        portadorLogin.getDocumentoAcesso(),
        portadorLogin.getGrupoAcesso(),
        portadorLogin.getTipoLoginEnum(),
        MSG_DADOS_INV_CRIAR_LOGIN);

    Credencial cred;
    if (model.getCredencial() != null) {
      if (portadorLogin
          .getTipoLoginEnum()
          .getRegraTipoPortadorLoginEnum()
          .equals(RegraTipoPortadorEnum.CORPORATIVO)) {
        cred =
            buscarCredencialPeloHash512HexUnauthorized(
                UtilController.encodeSenhaSHA512(model.getCredencial()).toUpperCase());

        String numeroCompleto = model.getCredencial();
        portadorLogin.setDocumentoAcesso(
            numeroCompleto.substring(0, 4)
                + numeroCompleto.substring(numeroCompleto.length() - 4, numeroCompleto.length()));
        portadorLogin.setCpf(model.getCnpj());
      } else {
        cred = buscarCredencialPeloHash512HexUnauthorized(model);
      }
    } else {
      Credencial credencial;
      if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(model.getIdInstituicao())
          && portadorLogin.getDocumentoAcesso() != null) {
        credencial =
            credencialService.buscarCredencialAdicionalMaisRecente(
                portadorLogin.getDocumentoAcesso(),
                model.getIdProcessadora(),
                model.getIdInstituicao(),
                1);
      } else {
        credencial =
            credencialService.buscarCredencialNaoBloqueadaMaisRecente(
                model.getCnpj() != null && !model.getCnpj().isEmpty()
                    ? model.getCnpj()
                    : model.getCpf(),
                model.getIdProcessadora(),
                model.getIdInstituicao(),
                model.getCnpj() != null && !model.getCnpj().isEmpty() ? 2 : 1);
      }
      String numeroCompleto = credencialService.getNumeroCredencialEmClaro(credencial);
      cred =
          buscarCredencialPeloHash512HexUnauthorized(
              UtilController.encodeSenhaSHA512(numeroCompleto).toUpperCase());
    }

    Pessoa pessoa = getPessoaNotNull(cred.getIdPessoa());

    if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(model.getIdInstituicao())) {
      if (portadorLogin.getTipoPessoa() == PessoaUtil.TIPO_PESSOA_FISICA
          && portadorLogin.getDocumentoAcesso() == null) {
        validaDataValidadeCredencial(
            model.getDataValidadeCredencial(),
            DateUtil.localDateTimeToDate(cred.getDataValidade()),
            map);
      }
      if (portadorLogin.getTipoPessoa() == PessoaUtil.TIPO_PESSOA_FISICA
          && portadorLogin.getDocumentoAcesso() != null) {
        validaExistenciaPessoaAdicional(map, portadorLogin);
      }
    }

    if (!Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(model.getIdInstituicao())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model.getIdInstituicao())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI.equals(model.getIdInstituicao())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK.equals(model.getIdInstituicao())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA.equals(model.getIdInstituicao())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(model.getIdInstituicao())
        && (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(model.getIdInstituicao())
            && Constantes.PESSOA_FISICA.equals(pessoa.getIdTipoPessoa()))) {
      validaDataDeNascimento(model, map, pessoa);
    }

    if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_DAXPAY.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(model.getIdInstituicao())
        || (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(model.getIdInstituicao())
            && Constantes.PESSOA_JURIDICA.equals(pessoa.getIdTipoPessoa()))) {
      ContaPagamento contaAtualizar = contaPagamentoService.findById(cred.getIdConta());
      contaAtualizar.setDataHoraStatusConta(LocalDateTime.now());
      contaAtualizar.setIdStatusConta(STATUS_ATIVO);
      contaAtualizar.setIdUsuarioManutencao(USUARIO_PORTADOR);

      if (portadorLogin.getTipoPessoa() == PessoaUtil.TIPO_PESSOA_JURIDICA) {
        validarExistenciaRepresentanteLegal(model, contaAtualizar.getIdConta());
      }

      contaPagamentoService.save(contaAtualizar);
    }

    if (StringUtils.isNotBlank(model.getEmail())
        && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(
            model
                .getIdInstituicao())) { // Na teoria somente o INMAIS precisa reatualizar o email de
      // um pre-cadastrado
      pessoa.setEmail(model.getEmail());
      portadorLogin.setEmail(model.getEmail());
      atualizarPessoa(pessoa);
    } else {
      if (pessoa.getEmail() != null) {
        portadorLogin.setEmail(pessoa.getEmail());
      }
    }

    getContaPessoaTitularNotNull(pessoa, cred);

    TipoStatus tipoStatus = getTipoStatusValido(cred, pessoa);

    validarTipoStatusNotCancelado(map, tipoStatus, pessoa);

    salvarPortadorLogin(portadorLogin);

    if (portadorLogin
        .getTipoLoginEnum()
        .getRegraTipoPortadorLoginEnum()
        .equals(RegraTipoPortadorEnum.CORPORATIVO)) {
      portadorLoginContaService.criaPortadoresContaCorporativo(portadorLogin, model.getCnpj());
    } else {
      portadorLoginContaService.criaPortadoresConta(portadorLogin);
    }

    if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_FINANCIAL.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_WIZ.equals(model.getIdInstituicao())
        || Constantes.ID_PRODUCAO_QISTA.equals(model.getIdInstituicao())) {
      registraIMEIPrimeiroAcesso(portadorLogin.getIdLogin(), model);
    }
  }

  public PortadorLogin preparePortadorLogin(CadastrarPortadorLogin model) {
    PortadorLogin portadorLogin = new PortadorLogin();
    model.setCredencial(model.getCredencial() != null ? model.getCredencial().toUpperCase() : null);
    BeanUtils.copyProperties(model, portadorLogin, getNullPropertyNames(model));
    portadorLogin.setDataHoraCadastro(LocalDateTime.now());
    portadorLogin.setSenha(encodeSenhaSHA256(model.getSenha()));
    portadorLogin.setSenhaExpirada(false);
    new PortadorLoginVo(model.getCnpj(), model.getCpf(), model.getIdInstituicao())
        .setPortadorLoginFromPortadorLoginVo(portadorLogin);

    portadorLogin.setTipoLoginEnum(determinaTipoLogin(model));

    return portadorLogin;
  }

  private void validarExistenciaRepresentanteLegal(CadastrarPortadorLogin model, Long idConta) {
    representanteLegalService.validaExistenciaRepresentanteLegal(model.getCpf(), idConta);
  }

  public void createPortadorLoginImediatamenteAposCriacaoConta(
      CadastrarPortadorLogin model, PortadorLogin portadorLogin, Credencial credencial) {

    PortadorLogin portadorLoginPreviamenteExistente =
        buscarLogin(
            portadorLogin.getIdProcessadora(),
            portadorLogin.getIdInstituicao(),
            portadorLogin.getCpf(),
            portadorLogin.getDocumentoAcesso(),
            portadorLogin.getGrupoAcesso(),
            portadorLogin.getTipoLoginEnum(),
            MSG_DADOS_INV_CRIAR_LOGIN);

    if (portadorLoginPreviamenteExistente != null) {
      return;
    }

    Pessoa pessoa = getPessoaNotNull(credencial.getIdPessoa());
    if (StringUtils.isNotBlank(model.getEmail())) {
      pessoa.setEmail(model.getEmail());
      portadorLogin.setEmail(model.getEmail());
      atualizarPessoa(pessoa);
    } else {
      if (pessoa.getEmail() != null) {
        portadorLogin.setEmail(pessoa.getEmail());
      }
    }

    salvarPortadorLogin(portadorLogin);
  }

  @Transactional
  public void createContaDigital(CadastrarPortadorLoginContaDigital model) {

    if (!hierarquiaInstituicaoExists(model.getIdInstituicao(), model.getIdProcessadora())) {
      log.error(
          String.format(
              "Processadora ou instituição não existe para tentativa de criação de portadorLogin : %s",
              model));
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    Integer idInstituicaoParaConsultarPessoa =
        Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY.equals(model.getIdInstituicao())
            ? 2401
            : model.getIdInstituicao();

    Pessoa pessoa =
        pessoaService.findFirstByIdProcessadoraAndIdInstituicaoAndDocumento(
            model.getIdProcessadora(), idInstituicaoParaConsultarPessoa, model.getCpf());
    if (pessoa == null) {
      log.error(
          String.format(
              "Pessoa não existe para tentativa de criação de portadorLogin : %s", model));
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    ContaPessoa contaPessoaTitular =
        contaPessoaService.findOneByIdPessoaAndIdContaAndIdTitularidade(
            pessoa.getIdPessoa(), model.getIdConta(), TITULAR);
    if (contaPessoaTitular == null) {
      log.error(
          String.format("Conta não existe para tentativa de criação de portadorLogin : %s", model));
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    buscarLoginEGarantirExistencia(
        model.getIdProcessadora(),
        model.getIdInstituicao(),
        model.getCpf(),
        null,
        null,
        LEGADO_SIMPLES,
        MSG_DADOS_INV_CRIAR_LOGIN);

    ContaPagamento contaPagamento = contaPessoaTitular.getContaPagamento();

    if (model.getProposta() != null && model.getProposta()) {
      contaPagamento.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);
      contaPagamentoService.salvarConta(contaPagamento);
    }

    if (!contaPagamento.getIdStatusV2().equals(Constantes.TIPO_STATUS_DESBLOQUEADO)) {
      throw new GenericServiceException(
          MSG_DADOS_INV_CRIAR_LOGIN, MSG_DADOS_INV_CRIAR_LOGIN + "Conta Bloqueada");
    }

    CadastrarPortadorLogin cadastrarPortadorLogin = new CadastrarPortadorLogin();
    cadastrarPortadorLogin.setCpf(model.getCpf());
    cadastrarPortadorLogin.setIdInstituicao(model.getIdInstituicao());
    cadastrarPortadorLogin.setIdProcessadora(model.getIdProcessadora());
    cadastrarPortadorLogin.setOrigemCadastroLogin(model.getOrigemCadastroLogin());
    cadastrarPortadorLogin.setEmail(pessoa.getEmail());
    cadastrarPortadorLogin.setIsEsqueciSenha(false);
    cadastrarPortadorLogin.setTipoLogin(model.getTipoLogin());

    PortadorLogin portadorLogin = preparePortadorLogin(cadastrarPortadorLogin);
    portadorLogin.setSenha(
        (model.getProposta() != null && model.getProposta())
            ? model.getSenha()
            : encodeSenhaSHA256(model.getSenha()));

    salvarPortadorLogin(portadorLogin);

    portadorLoginContaService.criaPortadoresConta(portadorLogin);

    if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(contaPagamento.getIdInstituicao())) {
      emailService.sendBoasVindasKredit(pessoa, contaPagamento);

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(
        contaPagamento.getIdInstituicao())) {
      emailService.sendBoasVindasPaxPay(pessoa, null);

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_FANBANK.equals(
        contaPagamento.getIdInstituicao())) {
      emailService.sendBoasVindasFanBank(pessoa, contaPagamento);

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK.equals(
        contaPagamento.getIdInstituicao())) {
      emailService.sendBoasVindas1DBank(pessoa, contaPagamento);

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_DAXPAY.equals(
        contaPagamento.getIdInstituicao())) {
      // DAXPAY não recebe e-mail

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
        contaPagamento.getIdInstituicao())) {
      // INMAIS PRÊMIOS não recebe e-mail
    } else if (utilService
        .getProdutoInstituicaoBrbPay()
        .equals(contaPagamento.getIdProdutoInstituicao())) {
      // BRBPay ja recebe email em ContaPagamentoBRBService
    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA.equals(
        contaPagamento.getIdInstituicao())) {
      emailService.sendBoasVindasDiaDia(pessoa, null);
    } else {
      emailService.sendBoasVindas(pessoa);
    }

    if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(model.getIdInstituicao())
        && model.getDeviceId() != null) {

      PortadorDispositivo dispositivo = new PortadorDispositivo();
      BeanUtils.copyProperties(model, dispositivo, getNullPropertyNames(model));
      dispositivo.setDataCadastro(LocalDateTime.now());
      dispositivo.setPermiteNotificacao(Boolean.FALSE);
      dispositivo.setIdLogin(portadorLogin.getIdLogin());
      dispositivo.setPerguntouPermiteNotificacao(Boolean.FALSE);
      portadorDispositivoService.save(dispositivo);

      AntifraudeCliente antifraudeCliente =
          antifraudeService.findByIdInstituicaoAndDocumento(
              pessoa.getIdInstituicao().longValue(), pessoa.getDocumento());
      antifraudeCliente.setSenhaRedefinidaOCR(true);
      antifraudeService.salvar(antifraudeCliente);
    }
  }

  @Transactional
  public void cadastrarPortadorLogin(DadosPortador dadosPortador) {

    CadastrarPortadorLogin cadastrarPortadorLogin = new CadastrarPortadorLogin();

    cadastrarPortadorLogin.setCredencial(
        cadastrarPortadorLogin.getCredencial() != null
            ? cadastrarPortadorLogin.getCredencial().toUpperCase()
            : null);
    cadastrarPortadorLogin.setCnpj(
        dadosPortador.getDocumentoAcesso() != null ? dadosPortador.getDocumento() : null);
    cadastrarPortadorLogin.setCpf(
        dadosPortador.getDocumentoAcesso() != null
            ? dadosPortador.getDocumentoAcesso()
            : dadosPortador.getDocumento());
    cadastrarPortadorLogin.setIdInstituicao(dadosPortador.getIdInstituicao());
    cadastrarPortadorLogin.setIdProcessadora(dadosPortador.getIdProcessadora());
    cadastrarPortadorLogin.setOrigemCadastroLogin(Constantes.ORIGEM_CADASTRO_MANUAL);
    cadastrarPortadorLogin.setEmail(dadosPortador.getEmail());
    cadastrarPortadorLogin.setIsEsqueciSenha(false);
    cadastrarPortadorLogin.setGrupoAcesso(dadosPortador.getGrupoAcesso());
    cadastrarPortadorLogin.setTipoLogin(dadosPortador.getTipoLogin());

    PortadorLogin portadorLogin = preparePortadorLogin(cadastrarPortadorLogin);

    portadorLoginRepository.saveAndFlush(portadorLogin);

    portadorLoginContaService.criaPortadoresConta(portadorLogin);
  }

  public void validaPortador(ValidarPortadorLogin model) {
    HashMap<String, Object> map = new HashMap<>();

    validarHierarquiaExiste(model, map);

    if (model.getTipoLoginRegistro() == TipoLoginRegistroEnum.CADASTRO) {

      Credencial cred = buscarCredencialPeloHash512Hex(model);

      Pessoa pessoa = getPessoaNotNull(cred.getIdPessoa());

      if (Util.isNotNull(model.getDataValidadeCredencial())) {
        validaDataValidadeCredencial(
            model.getDataValidadeCredencial(),
            DateUtil.localDateTimeToDate(cred.getDataValidade()),
            map);
      }

      // verifica se a data de nascimento é diferente da enviada.
      if (pessoa.getTipoPessoa().getId() == 1) {
        validaDataDeNascimento(model, map, pessoa);
      } else {
        validaDataFundacao(model.getDataNascimento(), map, pessoa);
      }
      getContaPessoaTitularNotNull(pessoa, cred);

      TipoStatus tipoStatus = getTipoStatusValido(cred, pessoa);

      validarTipoStatusNotCancelado(map, tipoStatus, pessoa);
      model.setIdConta(cred.getIdConta());
      model.setIdCredencial(cred.getIdCredencial());

    } else {

      Credencial cred = buscarCredencialPeloHash512Hex(model.getCredencial());
      Pessoa pessoa = pessoaService.findById(cred.getIdPessoa());

      if (Util.isNotNull(model.getDataValidadeCredencial())) {
        validaDataValidadeCredencial(
            model.getDataValidadeCredencial(),
            DateUtil.localDateTimeToDate(cred.getDataValidade()),
            map);
      }

      model.setIdConta(cred.getIdConta());
      model.setIdCredencial(cred.getIdCredencial());
    }
  }

  private void validarTipoStatusNotCancelado(
      HashMap<String, Object> map, TipoStatus tipoStatus, Pessoa pessoa) {
    if (tipoStatus.getTipoGrupoStatus().getIdGrupoStatus().equals(CANCELADO)) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(
          DETALHE,
          "Cartão não operativo. "
              + mensagemParametrizadaInstituicao(
                  pessoa.getIdInstituicao(), pessoa.getIdProcessadora()));
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  public void criarPortadorLoginPreCadastro(CadastrarPortadorLoginPreCadastradoRequest model) {

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumento(
            model.getIdProcessadora(), model.getIdInstituicao(), model.getDocumento());

    if (pessoa == null) {
      log.error(
          String.format(
              "Pessoa não existe para tentativa de criação de portadorLogin : %s", model));
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    travaServicosService.travaServicos(pessoa.getIdInstituicao(), Servicos.CRIAR_PORTADOR_LOGIN);

    if (!Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(pessoa.getIdInstituicao())
        && !Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
            pessoa.getIdInstituicao())) {
      log.error(
          String.format(
              "Pessoa não InMais para tentativa de criação de portadorLogin : %s", model));
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    if (loginExistsGrupoAcessoNullAndDocumentoAcessoNull(
        pessoa.getIdProcessadora(),
        pessoa.getIdInstituicao(),
        pessoa.getDocumento(),
        LEGADO_SIMPLES)) {
      log.error(
          String.format(
              "PortadorLogin já existe para tentativa de criação de portadorLogin : %s", model));
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    CadastrarPortadorLogin cadastrarPortadorLogin = new CadastrarPortadorLogin();
    cadastrarPortadorLogin.setCredencial(
        cadastrarPortadorLogin.getCredencial() != null
            ? cadastrarPortadorLogin.getCredencial().toUpperCase()
            : null);
    cadastrarPortadorLogin.setCpf(model.getDocumento());
    cadastrarPortadorLogin.setIdInstituicao(model.getIdInstituicao());
    cadastrarPortadorLogin.setIdProcessadora(model.getIdProcessadora());
    cadastrarPortadorLogin.setOrigemCadastroLogin(model.getOrigemAcesso());
    cadastrarPortadorLogin.setIsEsqueciSenha(false);
    cadastrarPortadorLogin.setTipoLogin(model.getTipoLogin());

    PortadorLogin portadorLogin = preparePortadorLogin(cadastrarPortadorLogin);
    String senhaHash = Util.encodeSenhaSHA(model.getSenha());
    portadorLogin.setSenha(senhaHash);

    portadorLoginRepository.saveAndFlush(portadorLogin);

    portadorLoginContaService.criaPortadoresConta(portadorLogin);

    criaValidacaoOcrPreCadastrado(portadorLogin.getIdLogin(), model);

    contaPagamentoService.desbloquearContaPreCadastro(
        pessoa.getIdPessoa(), TITULAR, model.getIdInstituicao());
  }

  public void criaValidacaoOcrPreCadastrado(
      Long idLogin, CadastrarPortadorLoginPreCadastradoRequest model) {
    try {
      PortadorDispositivo dispositivo = new PortadorDispositivo();
      BeanUtils.copyProperties(model, dispositivo, getNullPropertyNames(model));
      dispositivo.setDataCadastro(LocalDateTime.now());
      dispositivo.setPermiteNotificacao(Boolean.FALSE);
      dispositivo.setIdLogin(idLogin);
      dispositivo.setPerguntouPermiteNotificacao(Boolean.FALSE);
      portadorDispositivoService.save(dispositivo);
    } catch (Exception e) {
      log.info("Não foi possível realizar o cadastro: " + e.getMessage());
    }
  }

  private void validaDataDeNascimento(
      ValidarPortadorLogin model, HashMap<String, Object> map, Pessoa pessoa) {
    validaDataDeNascimento(model.getDataNascimento(), map, pessoa);
  }

  private void validaDataDeNascimento(
      CadastrarPortadorLogin model, HashMap<String, Object> map, Pessoa pessoa) {
    if (Util.isNotNull(model.getCpf())) {
      validaDataDeNascimento(model.getDataNascimento(), map, pessoa);
    } else {
      validaDataFundacao(model.getDataNascimento(), map, pessoa);
    }
  }

  private void validaDataFundacao(Date dataFundacao, HashMap<String, Object> map, Pessoa pessoa) {
    if (!dataFundacao.equals(DateUtil.localDateTimeToDate(pessoa.getDataFundacao()))
        && !dataFundacao.equals(DateUtil.localDateTimeToDate(pessoa.getDataNascimento()))) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(
          DETALHE,
          mensagemParametrizadaInstituicao(pessoa.getIdInstituicao(), pessoa.getIdProcessadora()));
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  private void validaDataDeNascimento(
      Date dataNascimento, HashMap<String, Object> map, Pessoa pessoa) {

    if (isDataDiferente(dataNascimento, pessoa)) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(
          DETALHE,
          mensagemParametrizadaInstituicao(pessoa.getIdInstituicao(), pessoa.getIdProcessadora()));
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  private boolean isDataDiferente(Date dataNascimento, Pessoa pessoa) {
    return !(DateUtil.differenceDates(
            dataNascimento, DateUtil.localDateTimeToDate(pessoa.getDataNascimento()))
        == 0);
  }

  private void validaDataValidadeCredencial(
      String dataValidadeCredencialRequest,
      Date dataValidadeCredencialExistente,
      HashMap<String, Object> map) {

    if (dataValidadeCredencialRequest == null) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(DETALHE, DETALHE_ERRO_DATA_VALIDADE_CREDENCIAL);
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }

    String[] preparaValidadeRequest = dataValidadeCredencialRequest.split("/");
    String validadeRequest = preparaValidadeRequest[1] + preparaValidadeRequest[0];

    String validadeExistente = DateUtil.dateFormat("yyMM", dataValidadeCredencialExistente);

    if (!validadeRequest.equals(validadeExistente)) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(DETALHE, DETALHE_ERRO_DATA_VALIDADE_INCORRETA);
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  private TipoStatus getTipoStatusValido(Credencial cred, Pessoa pessoa) {

    TipoStatus tipoStatus =
        cred.getTipoStatus() == null
            ? tipoStatusService.findById(cred.getStatus())
            : cred.getTipoStatus();

    if (tipoStatus == null) {
      throw new GenericServiceException(
          MSG_DADOS_INV_CRIAR_LOGIN,
          "Não é possível criar Login para a credencial. TipoStatus Não encontrado. ");
    }
    return tipoStatus;
  }

  private ContaPessoa getContaPessoaTitularNotNull(Pessoa pessoa, Credencial cred) {
    ContaPessoa contaPessoaTitular =
        contaPessoaService.findOneByIdPessoaAndIdContaAndIdTitularidade(
            pessoa.getIdPessoa(), cred.getIdConta(), cred.getTitularidade());

    if (contaPessoaTitular == null) {
      throw new GenericServiceException(
          MSG_DADOS_INV_CRIAR_LOGIN,
          MSG_DADOS_INV_CRIAR_LOGIN
              + "Pessoa não é titular da conta. "
              + mensagemParametrizadaInstituicao(
                  pessoa.getIdInstituicao(), pessoa.getIdProcessadora()));
    }
    return contaPessoaTitular;
  }

  private Pessoa getPessoaNotNull(Long idPessoa) {
    Pessoa pessoa = pessoaService.findById(idPessoa);

    return validaPessoaNull(pessoa);
  }

  private Pessoa validaPessoaNull(Pessoa pessoa) {
    HashMap<String, Object> map = new HashMap<>();
    if (pessoa == null) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(DETALHE, "Pessoa não encontrada para informações enviadas.");
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
    return pessoa;
  }

  public PortadorLogin buscarLogin(FazerLoginPortador fazerLoginPortador, String erroMsg) {

    PortadorLoginVo portadorLoginVo =
        new PortadorLoginVo(
            fazerLoginPortador.getCnpj(),
            fazerLoginPortador.getCpf(),
            fazerLoginPortador.getIdInstituicao());
    return buscarLogin(
        fazerLoginPortador.getIdProcessadora(),
        fazerLoginPortador.getIdInstituicao(),
        portadorLoginVo.getDocumento(),
        fazerLoginPortador.getDocumentoAcesso() != null
            ? fazerLoginPortador.getDocumentoAcesso()
            : portadorLoginVo.getDocumentoAcesso(),
        fazerLoginPortador.getGrupoAcesso(),
        fazerLoginPortador.getTipoLogin(),
        erroMsg);
  }

  public PortadorLogin buscarLogin(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso,
      TipoPortadorLoginEnum tipoPortadorLoginEnum,
      String erroMsg) {

    tipoPortadorLoginEnum =
        determinaTipoLogin(
            documentoAcesso,
            grupoAcesso,
            SecurityUserPortador.getIdTipoPessoa(
                documento, documentoAcesso, idInstituicao, tipoPortadorLoginEnum),
            tipoPortadorLoginEnum);

    switch (tipoPortadorLoginEnum.getRegraTipoPortadorLoginEnum()) {
      case SIMPLES:
      case RESPONSAVEL:
      case DEPENDENTE:
        return findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, documento, tipoPortadorLoginEnum);
      case MULTIBENEFICIOS:
        validarInstituicaoPermitidaPortadorLoginMultibeneficios(idInstituicao, erroMsg);
        return findByIdProcessadoraAndIdInstituicaoAndCpfAndGrupoAcessoAndTipoLoginAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, documento, grupoAcesso, tipoPortadorLoginEnum);
      case REPRESENTANTE_LEGAL:
      case PESSOA_ADICIONAL_PF_CONTA_PJ:
        validarInstituicaoPermitidaPortadorLoginDocumentoAcesso(idInstituicao, erroMsg);
        return findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndTipoLoginAndGrupoAcessoIsNullDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, documento, documentoAcesso, tipoPortadorLoginEnum);
      case MULTIBENEFICIOS_REPRESENTANTE_LEGAL:
        validarInstituicaoPermitidaPortadorLoginMultibeneficios(idInstituicao, erroMsg);
        validarInstituicaoPermitidaPortadorLoginDocumentoAcesso(idInstituicao, erroMsg);
        return findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndGrupoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
            idProcessadora,
            idInstituicao,
            documento,
            documentoAcesso,
            grupoAcesso,
            tipoPortadorLoginEnum);
      case CORPORATIVO:
        validarInstituicaoPermitidaPortadorLoginDocumentoAcesso(idInstituicao, erroMsg);
        return findByIdProcessadoraAndIdInstituicaoAndDocumentoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, documentoAcesso, tipoPortadorLoginEnum);
      default:
        throw new GenericServiceException(erroMsg + ". RegraTipoPortadorLogin inexistente.");
    }
  }

  private static void validarInstituicaoPermitidaPortadorLoginMultibeneficios(
      Integer idInstituicao, String erroMsg) {
    if (!(Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_WIZ.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU.equals(idInstituicao)
        || Constantes.ID_PRODUCAO_QISTA.equals(idInstituicao))) {
      HashMap<String, Object> map = new HashMap<>();
      map.put(MSG, erroMsg);
      map.put(DETALHE, "Instituição não permite uso de campo grupoAcesso.");
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  private static void validarInstituicaoPermitidaPortadorLoginDocumentoAcesso(
      Integer idInstituicao, String erroMsg) {
    if (!Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_WIZ.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_QISTA.equals(idInstituicao)) {
      HashMap<String, Object> map = new HashMap<>();
      map.put(MSG, erroMsg);
      map.put(DETALHE, "Instituição não permite uso de documentoAcesso.");
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  public PortadorLogin buscarLoginEGarantirExistencia(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso,
      TipoPortadorLoginEnum tipoPortadorLoginEnum,
      String erroMsg) {
    HashMap<String, Object> map = new HashMap<>();
    PortadorLogin portadorLogin =
        buscarLogin(
            idProcessadora,
            idInstituicao,
            documento,
            documentoAcesso,
            grupoAcesso,
            tipoPortadorLoginEnum,
            erroMsg);
    if (portadorLogin == null) {
      erroLoginCadastroInexistente(map, erroMsg);
    }
    return portadorLogin;
  }

  public void validaELancaErroPortadorLoginPreviamenteExistente(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso,
      TipoPortadorLoginEnum tipoPortadorLoginEnum,
      String erroMsg) {

    HashMap<String, Object> map = new HashMap<>();
    PortadorLogin portadorLogin =
        buscarLogin(
            idProcessadora,
            idInstituicao,
            documento,
            documentoAcesso,
            grupoAcesso,
            tipoPortadorLoginEnum,
            erroMsg);
    if (portadorLogin != null) {
      erroLoginCadastroExistente(map, erroMsg);
    }
  }

  @SuppressWarnings("unnused")
  private boolean loginPreviamenteExistente(PortadorLogin portadorLogin) {
    return loginPreviamenteExistente(
        portadorLogin.getIdProcessadora(),
        portadorLogin.getIdInstituicao(),
        portadorLogin.getCpf(),
        portadorLogin.getDocumentoAcesso(),
        portadorLogin.getGrupoAcesso(),
        portadorLogin.getTipoLoginEnum());
  }

  @SuppressWarnings("unnused")
  private boolean loginPreviamenteExistente(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {

    try {
      validaELancaErroPortadorLoginPreviamenteExistente(
          idProcessadora,
          idInstituicao,
          documento,
          documentoAcesso,
          grupoAcesso,
          tipoPortadorLoginEnum,
          MSG_DADOS_INV_CRIAR_LOGIN);
    } catch (GenericServiceException e) {
      if (DETALHE_ERRO_LOGIN_JA_EXISTENTE.equals(e.getDetalhes())) {
        return true;
      } else {
        throw e;
      }
    }
    return false;
  }

  private static void erroLoginCadastroExistente(HashMap<String, Object> map, String errorMsg) {
    map.put(MSG, errorMsg);
    // O BRB estará usando essa mensagem de erro para detectar falha na criação do login (Projeto
    // PDAF).
    // Comunicar com eles para sincronizar mudança, quando essa for necessária.
    map.put(DETALHE, DETALHE_ERRO_LOGIN_JA_EXISTENTE);
    map.put(CREATED, false);
    throw new GenericServiceException(map);
  }

  private static void erroLoginCadastroInexistente(HashMap<String, Object> map, String errorMsg) {
    map.put(MSG, errorMsg);
    map.put(DETALHE, DETALHE_ERRO_LOGIN_INEXISTENTE);
    map.put("sucesso", false);
    throw new GenericServiceException(map);
  }

  private static void erroLoginCamposCpfOuCnpjNaoPreenchido(
      PortadorLogin portadorLogin, HashMap<String, Object> map) {
    if (portadorLogin.getCpf() == null) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(DETALHE, "Campos cpf e cnpj não preenchidos.");
      map.put(CREATED, false);
      throw new GenericServiceException(map);
    }
  }

  private void validaExistenciaPessoaAdicional(
      HashMap<String, Object> map, PortadorLogin portadorLogin) {
    boolean existePessoaAdicional =
        pessoaService.existePessoaAdicional(
            portadorLogin.getIdProcessadora(),
            portadorLogin.getIdInstituicao(),
            portadorLogin.getDocumentoAcesso());
    if (!existePessoaAdicional) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(DETALHE, "Pessoa adicional não existe.");
      map.put(CREATED, false);
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    }
  }

  public boolean validarContratoMigracaoAceito(Integer idInstituicao, String documento) {
    PortadorLogin portadorLogin =
        portadorLoginRepository.findByCpfAndIdInstituicao(documento, idInstituicao);
    if (portadorLogin.getDtHrContrato() == null) {
      return false;
    } else {
      return true;
    }
  }

  private void validarHierarquiaExiste(ValidarPortadorLogin model, HashMap<String, Object> map) {
    validarHierarquiaExiste(model.getIdInstituicao(), model.getIdProcessadora(), map);
  }

  private void validarHierarquiaExiste(
      Integer idInstituicao, Integer idProcessadora, HashMap<String, Object> map) {
    if (!hierarquiaInstituicaoExists(idInstituicao, idProcessadora)) {
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(CREATED, false);
      map.put(
          DETALHE,
          "Não foi possivel encontrar Instituicao informada. idInstituicao = "
              + idInstituicao
              + " idProcessadora = "
              + idProcessadora);

      throw new GenericServiceException(map);
    }
  }

  private void atualizarPessoa(Pessoa pessoa) {
    preRegistroContatoTotvsService.atualizarContatoPessoaTotvs(pessoa);
    pessoaService.save(pessoa);
  }

  private void salvarPortadorLogin(PortadorLogin portadorLogin) {
    save(portadorLogin);
  }

  private Credencial buscarCredencialPeloHash512HexAndPessoa(
      Long idPessoa, String hashHexCredencial) {
    Credencial credencial = null;
    GetCardResponse card = buscarCardResponseCredencialPeloHash512Hex(hashHexCredencial);

    if (card.getSuccess()) {
      String tokenInterno = card.getCard().getToken();

      credencial = getCredencialFacade().findOneByTokenInternoAndIdPessoa(tokenInterno, idPessoa);
    }

    return credencial;
  }

  private Credencial buscarCredencialPeloHash512HexUnauthorized(CadastrarPortadorLogin model) {
    return buscarCredencialPeloHash512HexUnauthorized(model.getCredencial());
  }

  private Credencial buscarCredencialPeloHash512Hex(ValidarPortadorLogin model) {
    return buscarCredencialPeloHash512Hex(model.getCredencial());
  }

  private Credencial buscarCredencialPeloHash512Hex(String hashHexCredencial) {
    Credencial credencial = null;

    GetCardResponse card = buscarCardResponseCredencialPeloHash512Hex(hashHexCredencial);

    if (card.getSuccess()) {
      String tokenInterno = card.getCard().getToken();

      credencial = getCredencialFacade().findOneByTokenInterno(tokenInterno);
    }
    return credencial;
  }

  private Credencial buscarCredencialPeloHash512HexUnauthorized(String hashHexCredencial) {
    Credencial credencial = null;

    GetCardResponse card =
        buscarCardResponseCredencialPeloHash512HexUnauthorized(hashHexCredencial);

    if (card.getSuccess()) {
      String tokenInterno = card.getCard().getToken();

      credencial = getCredencialFacade().findOneByTokenInterno(tokenInterno);
    }
    return credencial;
  }

  private GetCardResponse buscarCardResponseCredencialPeloHash512Hex(String hashHexCredencial) {
    GetCardResponse resp = cardService.getToken(hashHexCredencial);
    if (resp == null || resp.getCard() == null) {
      HashMap<String, Object> map = new HashMap<String, Object>();
      map.put(MSG, MSG_DADOS_INV_CRIAR_LOGIN);
      map.put(CREATED, false);
      map.put(DETALHE, "Não foi possivel encontrar credencial informada.");

      throw new GenericServiceException(map);
    }
    return resp;
  }

  private GetCardResponse buscarCardResponseCredencialPeloHash512HexUnauthorized(
      String hashHexCredencial) {
    GetCardResponse resp = cardService.getToken(hashHexCredencial);
    if (resp == null || resp.getCard() == null) {
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }
    return resp;
  }

  public boolean loginExistsGrupoAcessoNullAndDocumentoAcessoNull(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    PortadorLogin loginsPortadores =
        findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            idProcessadora, idInstituicao, documento, tipoPortadorLoginEnum);
    return loginsPortadores != null;
  }

  public boolean loginExistsRepresentanteLegal(
      Integer idProcessadora, Integer idInstituicao, String documento, String documentoAcesso) {
    PortadorLogin loginsPortadores =
        findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndTipoLoginAndGrupoAcessoIsNullDataHoraCancelamentoIsNull(
            idProcessadora,
            idInstituicao,
            documento,
            documentoAcesso,
            LEGADO_DOCUMENTO_ACESSO_REPRESENTANTE_LEGAL);
    return loginsPortadores != null;
  }

  public PortadorLogin resgataLoginMultiBeneficios(
      Integer idProcessadora, Integer idInstituicao, String documento, Long grupoAcesso) {
    PortadorLogin logins =
        findByIdProcessadoraAndIdInstituicaoAndCpfAndGrupoAcessoAndTipoLoginAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
            idProcessadora,
            idInstituicao,
            documento.trim(),
            grupoAcesso,
            TipoPortadorLoginEnum.LEGADO_MULTIBENEFICIOS);
    return logins;
  }

  private boolean hierarquiaInstituicaoExists(Integer idInstituicao, Integer idProcessadora) {
    HierarquiaInstituicaoId id = new HierarquiaInstituicaoId(idProcessadora, idInstituicao);
    return hierarquiaInstituicaoService.findById(id) != null;
  }

  public void trocarEmailPortador(TrocarEmail trocarEmail, SecurityUserPortador userPortador) {

    PortadorLogin portadorLogin = new PortadorLogin(userPortador);

    if (portadorLogin.getIdLogin() == null) {
      throw new GenericServiceException(
          "Portador não encontrado para os parâmetros informados." + trocarEmail);
    }

    if (EmailBlacklist.isBlacklistMail(trocarEmail.getEmail())) {
      throw new GenericServiceException(
          "O domínio utilizado no email está na blacklist. Email: " + trocarEmail.getEmail());
    }

    if (ID_PRODUCAO_INSTITUICAO_FINANCIAL.equals(portadorLogin.getIdInstituicao())) {
      throw new GenericServiceException("Não é possível alterar email", HttpStatus.UNAUTHORIZED);
    }

    List<Pessoa> pessoaList = null;

    if (userPortador
        .getTipoLoginEnum()
        .getRegraTipoPortadorLoginEnum()
        .equals(RegraTipoPortadorEnum.CORPORATIVO)) {
      List<Long> credenciais =
          credencialService.recuperarCredenciaisPortador(userPortador.getIdLogin());
      if (!credenciais.isEmpty()) {
        Credencial credencial = credencialService.findByIdCredencial(credenciais.get(0));
        if (credencial != null) {
          pessoaList = new ArrayList<>();
          pessoaList.add(credencial.getPessoa());
        }
      }
    } else {
      pessoaList = pessoaService.findPessoaListWithLogin(portadorLogin);
    }

    if (pessoaList == null || pessoaList.isEmpty()) {
      throw new GenericServiceException(
          "Não foi possível encontrar uma pessoa para os dados informados. " + trocarEmail);
    }

    for (Pessoa pessoa : pessoaList) {
      pessoa.setEmail(trocarEmail.getEmail());
      if (StringUtils.isNotBlank(trocarEmail.getDdd())) {
        pessoa.setDddTelefoneCelular(Integer.valueOf(trocarEmail.getDdd()));
        pessoa.setTelefoneCelular(Integer.valueOf(trocarEmail.getTelefone()));
      }
      atualizarPessoa(pessoa);
    }

    portadorLogin.setEmail(trocarEmail.getEmail());
    save(portadorLogin);
  }

  public HashMap<String, String> buscarEmailPortador(
      Integer idProcessadora, Integer idInstituicao, String documento) {

    PortadorLogin portadorLogin =
        buscarLoginEGarantirExistencia(
            idProcessadora,
            idInstituicao,
            documento,
            null,
            null,
            LEGADO_SIMPLES,
            "Nenhum Login encontrado para os dados enviados.");

    HashMap<String, String> map = new HashMap<>();
    map.put("email", portadorLogin.getEmail());

    if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(idInstituicao)) {
      Pessoa pessoa =
          pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
              idProcessadora,
              idInstituicao,
              documento,
              documento.length() <= 11 ? Constantes.PESSOA_FISICA : Constantes.PESSOA_JURIDICA);

      if (pessoa == null) {
        throw new GenericServiceException(
            "Não foi possível encontrar uma pessoa com documento: " + documento);
      }
      String telefone = pessoa.getDddTelefoneCelular() + "-" + pessoa.getTelefoneCelular();
      map.put("telefone", telefone);
    }

    return map;
  }

  public List<DetalhesPortadorLogin> buscarLogins(Long idConta) {
    try {
      List<DetalhesPortadorLogin> lista = new ArrayList<DetalhesPortadorLogin>();
      List<PortadorLogin> logins = portadorLoginRepository.findByIdConta(idConta);

      for (PortadorLogin tmp : logins) {
        DetalhesPortadorLogin target = new DetalhesPortadorLogin();
        BeanUtils.copyProperties(tmp, target);
        lista.add(target);
      }

      return lista;

    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível listar os logins deste portador!");
    }
  }

  public List<DetalhesAcessoPortador> buscarAcessosLogin(Long idLogin, Integer first, Integer max) {
    try {
      return logAcessoPortadorService.buscarAcessosUltimos6Meses(idLogin, first, max);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível listar os acessos deste login!", e);
    }
  }

  @Transactional
  public ResponseEntity<?> recuperarSenha(RecuperarSenhaEmailPortador recuperarSenha) {
    PortadorLogin portadorLogin =
        buscarLoginEGarantirExistencia(
            recuperarSenha.getIdProcessadora(),
            recuperarSenha.getIdInstituicao(),
            recuperarSenha.getDocumento(),
            null,
            null,
            LEGADO_SIMPLES,
            "Nenhum Login encontrado para os dados enviados.");

    validarTempoRedefinicaoSenha(portadorLogin, false);

    Pessoa pessoa =
        validaPessoa(
            portadorLogin, recuperarSenha.getIdProcessadora(), recuperarSenha.getIdInstituicao());

    if (!portadorLogin.getEmail().equalsIgnoreCase(recuperarSenha.getEmail())) {
      log.error(
          "Email não enviado. Não corresponde a esse documento. "
              + recuperarSenha.getEmail()
              + " do documento "
              + recuperarSenha.getDocumento()
              + " e instituicao "
              + recuperarSenha.getIdInstituicao());
      throw new GenericServiceException("Email não correspondente a esse documento.");
    }

    String senha = Util.generateRandomLetrasENumerosECaracteresEspeciais(TAMANHO_SENHA_REDEFINICAO);
    ;
    portadorLogin.setSenha(encodeSenhaSHA256(senha));
    portadorLogin.setSenhaExpirada(true);

    portadorLoginRepository.save(portadorLogin);

    return mandaEmailEsqueciSenha(portadorLogin, pessoa, senha);
  }

  private Pessoa validaPessoa(
      PortadorLogin portadorLogin, Integer idProcessadora, Integer idInsitituicao) {

    Integer idInstituicaoPortador =
        Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY.equals(idInsitituicao) ? 2401 : idInsitituicao;
    idInsitituicao = idInstituicaoPortador;

    Pessoa pessoa =
        pessoaService
            .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataNascimentoNotNullOrderByIdPessoaDesc(
                idProcessadora,
                idInsitituicao,
                portadorLogin.getCpf(),
                DocumentoUtil.getTipoPessoa(
                    portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));

    if (Objects.isNull(pessoa)) {
      pessoa =
          pessoaService
              .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataFundacaoNotNullOrderByIdPessoaDesc(
                  idProcessadora,
                  idInsitituicao,
                  portadorLogin.getCpf(),
                  DocumentoUtil.getTipoPessoa(
                      portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));
    }

    if (Objects.nonNull(pessoa)
        && Objects.nonNull(pessoa.getTipoPessoa())
        && Constantes.PESSOA_FISICA.equals(pessoa.getTipoPessoa().getId())) {
      if (Objects.isNull(pessoa.getDataNascimento())) {
        log.warn("Não foi encontrado Pessoa com data de nascimento não nula.");
        pessoa =
            pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
                idProcessadora,
                idInsitituicao,
                portadorLogin.getCpf(),
                DocumentoUtil.getTipoPessoa(
                    portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));

        if (pessoa == null) {
          throw new GenericServiceException(
              "Login de acesso não identificado. Pessoa não encontrada. ",
              "Pessoa não encontrada. ");
        }
      }
    } else {
      if (Objects.isNull(pessoa.getDataFundacao())) {
        log.warn("Não foi encontrado Pessoa com data de nascimento não nula.");
        pessoa =
            pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
                idProcessadora,
                idInsitituicao,
                portadorLogin.getCpf(),
                DocumentoUtil.getTipoPessoa(
                    portadorLogin.getIdTipoPessoa(), portadorLogin.getCpf()));

        if (pessoa == null) {
          throw new GenericServiceException(
              "Login de acesso não identificado. Pessoa não encontrada. ",
              "Pessoa não encontrada. ");
        }
      }
    }

    return pessoa;
  }

  @Transactional
  public ResponseEntity<?> recuperarSenha(RecuperarSenhaPortador recuperarSenha) {

    // GAMBIARRA: Resolve problema de recuperação de senha do aplicativo iOS devido ao app enviar o
    // documento com caracteres especiais.
    if (recuperarSenha.getIdInstituicao() != null && recuperarSenha.getIdInstituicao() == 1401) {
      String documentoFormatado = recuperarSenha.getDocumento();
      documentoFormatado = documentoFormatado.replaceAll("[^0-9]", "");
      recuperarSenha.setDocumento(documentoFormatado);
    }

    PortadorLogin portadorLogin =
        buscarLoginEGarantirExistencia(
            recuperarSenha.getIdProcessadora(),
            recuperarSenha.getIdInstituicao(),
            recuperarSenha.getDocumento(),
            null,
            null,
            recuperarSenha.getTipoLogin(),
            (recuperarSenha.getIdInstituicao() == 4001
                ? "Login de acesso não identificado"
                : "Login de acesso não identificado. Nenhum login encontrado para os dados enviados."));

    validarTempoRedefinicaoSenha(portadorLogin, false);

    List<Pessoa> pessoaList = pessoaService.findPessoaListWithLogin(portadorLogin);
    if (pessoaList == null || pessoaList.isEmpty()) {
      throw new GenericServiceException("Pessoa não encontrada.");
    }
    Pessoa pessoa = pessoaList.get(0);

    if (StringUtils.isBlank(portadorLogin.getEmail())) {
      portadorLogin.setEmail(pessoa.getEmail());
    }

    if (Util.isNotNull(portadorLogin.getCpf())) {
      // Solucao para colocar data que podem estar no formato yyyy-MM-dd ou yyyy-MM-ddT03:00:00.000Z
      // no formato yyyy-MM-dd
      String modelDtNascString =
          recuperarSenha.getDataNascimento().indexOf('T') == -1
              ? recuperarSenha.getDataNascimento()
              : recuperarSenha
                  .getDataNascimento()
                  .substring(0, recuperarSenha.getDataNascimento().indexOf('T'));
      if (portadorLogin.getCpf().length() > 11) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dtFundacao = pessoa.getDataFundacao().format(formatter);
        if (pessoa.getDataFundacao() == null || !(dtFundacao.compareTo(modelDtNascString) == 0)) {
          throw new GenericServiceException(
              "Login de acesso não identificado. Data fundação não confere. ",
              "Data fundação não confere ");
        }
      } else {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String dtNasc = pessoa.getDataNascimento().format(formatter);
        // LocalDateTime dtNasc = LocalDateTime.of(recuperarSenha.getDataNascimento(),
        // LocalTime.MIN);
        if (pessoa.getDataNascimento() == null || !(dtNasc.compareTo(modelDtNascString) == 0)) {
          throw new GenericServiceException(
              "Login de acesso não identificado. Data de nascimento não confere. ",
              "Data de nascimento não confere ");
        }
      }
    } else {
      return null;
    }

    String senha = Util.generateRandomLetrasENumerosECaracteresEspeciais(TAMANHO_SENHA_REDEFINICAO);
    portadorLogin.setSenha(encodeSenhaSHA256(senha));
    portadorLogin.setSenhaExpirada(true);

    portadorLoginRepository.save(portadorLogin);

    return mandaEmailEsqueciSenha(portadorLogin, pessoa, senha);
  }

  @Transactional
  public ResponseEntity<?> recuperarSenhaPortadorLogin(Integer idLogin) {

    PortadorLogin portadorLogin = portadorLoginRepository.getOne(idLogin.longValue());

    validarTempoRedefinicaoSenha(portadorLogin, false);

    List<Pessoa> pessoaList = pessoaService.findPessoaListWithLogin(portadorLogin);
    if (pessoaList == null || pessoaList.isEmpty()) {
      throw new GenericServiceException("Pessoa não encontrada.");
    }
    Pessoa pessoa = pessoaList.get(0);

    if (StringUtils.isBlank(portadorLogin.getEmail())) {
      portadorLogin.setEmail(pessoa.getEmail());
    }

    String senha = Util.generateRandomLetrasENumerosECaracteresEspeciais(TAMANHO_SENHA_REDEFINICAO);
    ;
    portadorLogin.setSenha(encodeSenhaSHA256(senha));
    portadorLogin.setSenhaExpirada(true);

    portadorLoginRepository.save(portadorLogin);

    return mandaEmailRecuperarSenha(portadorLogin, pessoa, senha);
  }

  @Transactional
  public void reativarSenhaPortadorLogin(Integer idLogin) {

    PortadorLogin portadorLogin = portadorLoginRepository.getOne(idLogin.longValue());
    if (portadorLogin == null) {
      throw new GenericServiceException("Portador login não encontrado.");
    }
    portadorLogin.setSenhaExpirada(false);

    portadorLoginRepository.save(portadorLogin);
  }

  @Transactional
  public void excluirPortadorLogin(Long idLogin) {
    PortadorLogin portadorLogin = portadorLoginRepository.getOne(idLogin.longValue());
    if (portadorLogin != null) {
      portadorLogin.setDataHoraCancelamento(LocalDateTime.now());
      portadorLoginRepository.save(portadorLogin);
    } else {
      throw new GenericServiceException("Portador login não encontrado!");
    }
  }

  private ResponseEntity<?> mandaEmailEsqueciSenha(
      PortadorLogin portadorLogin, Pessoa pessoa, String senhaDescriptografada) {
    Map<String, Object> map = new HashMap<>();

    try {
      if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY.equals(portadorLogin.getIdInstituicao())) {
        EnvioEmailRequest envioEmailRequest =
            new EnvioEmailRequest.Builder()
                .setEmail(pessoa.getEmail())
                .setParametro1(pessoa.getNomeCompleto())
                .setParametro2(senhaDescriptografada)
                .setModelo(ModeloEmailEnum.RECUPERACAO_SENHA.getId())
                .build();
        emailService.sendEmailBRB(envioEmailRequest);
      } else {
        emailService.sendPasswordEmailPortador(
            pessoa,
            portadorLogin,
            senhaDescriptografada,
            getUrlRecadastrarSenhaPortador(pessoa.getIdProcessadora(), pessoa.getIdInstituicao()),
            portadorLogin.getIdInstituicao());
      }
    } catch (Exception e) {
      e.printStackTrace();
      log.error(
          "Erro ao enviar email de esqueci senha. Email:"
              + portadorLogin.getEmail()
              + " CPF:"
              + portadorLogin.getCpf()
              + " Conta:"
              + pessoa.getIdPessoa());
      map.put("msg", "Ocorreu um erro no envio do email. Tente novamente mais tarde. ");
      map.put("sucesso", false);
      return new ResponseEntity<Map<String, Object>>(map, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    String emailMascarado = getEmailMascarado(portadorLogin.getEmail());
    log.info(
        "Sucesso preparando envio de email de esqueci senha. Email:"
            + portadorLogin.getEmail()
            + " CPF:"
            + portadorLogin.getCpf()
            + " ID Pessoa:"
            + pessoa.getIdPessoa());
    map.put(
        "msg",
        "Uma senha temporária foi encaminhada para seu email "
            + emailMascarado
            + " . Acesse seu email e siga as instruções para recadastro.");
    map.put("sucesso", true);
    return new ResponseEntity(map, HttpStatus.OK);
  }

  private ResponseEntity<?> mandaEmailRecuperarSenha(
      PortadorLogin portadorLogin, Pessoa pessoa, String senhaDescriptografada) {
    Map<String, Object> map = new HashMap<>();

    try {
      emailService.sendEmailTemporaryPassword(pessoa, portadorLogin, senhaDescriptografada);
    } catch (Exception e) {
      e.printStackTrace();
      log.error(
          "Erro ao enviar email de recuperar senha. Email:"
              + portadorLogin.getEmail()
              + " CPF:"
              + portadorLogin.getCpf()
              + " Conta:"
              + pessoa.getIdPessoa());
      map.put("msg", "Ocorreu um erro no envio do email. Tente novamente mais tarde. ");
      map.put("sucesso", false);
      return new ResponseEntity<Map<String, Object>>(map, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    String emailMascarado = getEmailMascarado(portadorLogin.getEmail());
    log.info(
        "Sucesso preparando envio de email de recuperar senha. Email:"
            + portadorLogin.getEmail()
            + " CPF:"
            + portadorLogin.getCpf()
            + " ID Pessoa:"
            + pessoa.getIdPessoa());
    map.put(
        "msg",
        "Uma senha temporária foi encaminhada para seu email "
            + emailMascarado
            + " . Acesse seu email e siga as instruções para recadastro.");
    map.put("sucesso", true);
    return new ResponseEntity(map, HttpStatus.OK);
  }

  private static String getEmailMascarado(String email) {
    if (email != null && !email.isEmpty()) {
      String[] partes = email.split("@");
      String emailMasc =
          partes[0].charAt(0) + Strings.repeat("*", partes[0].length()) + "@" + partes[1];
      return emailMasc;
    }
    throw new GenericServiceException("Email invalido", "Email não Cadastrado");
  }

  public Integer countAcessosLogin(Long idLogin) {
    return logAcessoPortadorService.countAcessosUltimos6Meses(idLogin);
  }

  public Map<String, Object> recadastrarSenha(TrocarSenhaPortador trocarSenhaPortador) {
    PortadorLogin portadorLogin = getPortadorLoginValido(trocarSenhaPortador);
    portadorLogin.setSenha(trocarSenhaPortador.getNovaSenha());
    portadorLogin.setSenhaExpirada(false);
    salvarPortadorLogin(portadorLogin);

    Map<String, Object> resp = new HashMap<>();
    resp.put("msg", "Senha recadastrada com Sucesso. Faça o login utilizando a nova Senha.");
    resp.put("sucesso", true);
    return resp;
  }

  private String getUrlRecadastrarSenhaPortador(Integer idProcessadora, Integer idInstituicao) {
    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(URL_RECADASTRAR_SENHA_PORTADOR);

    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(idProcessadora);
    valor.setIdInstituicao(idInstituicao);

    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    if (params == null || params.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: "
              + URL_RECADASTRAR_SENHA_PORTADOR);
    }
    return params.get(0).getValorParametro();
  }

  private String mensagemParametrizadaInstituicao(Integer instituicaoId, Integer idProcessadora) {

    HierarquiaInstituicao instituicao =
        hierarquiaInstituicaoService.findByIdProcessadoraAndIdInstituicao(
            idProcessadora, instituicaoId);

    if (instituicao != null && instituicao.getTextoInfCentralAtendimento() != null) {
      return "\n" + instituicao.getTextoInfCentralAtendimento();
    } else {
      return "";
    }
  }

  public PortadorLogin findByCpfAndIdInstituicao(String documento, Integer idInstituicao) {
    return portadorLoginRepository.findByCpfAndIdInstituicao(documento, idInstituicao);
  }

  public Boolean salvarContratoMigracao(Integer idInstituicao, String documento) {
    PortadorLogin portadorLogin = findByCpfAndIdInstituicao(documento, idInstituicao);
    portadorLogin.setDtHrContrato(LocalDateTime.now());
    try {
      portadorLoginRepository.save(portadorLogin);
    } catch (Exception e) {
      e.printStackTrace();
      log.error("Erro ao atualizar contrato.");

      return false;
    }
    return true;
  }

  public Boolean verificaOpcaoAberturaConta(Integer idInstituicao, Integer idProcessadora) {

    String filterVoucher =
        parametroValorService.findSingleResult("show.button", idInstituicao, idProcessadora);

    return "true".equalsIgnoreCase(filterVoucher);
  }

  public Boolean verificarNecessidadeOCR(
      SecurityUserPortador userPortador, ValidarDispositivo dispositivoEnviado) {
    if ((dispositivoEnviado.getNovoOnboard() != null && dispositivoEnviado.getNovoOnboard())
            && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(
                userPortador.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
            userPortador.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(userPortador.getIdInstituicao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(userPortador.getIdInstituicao())) {
      Boolean valido = this.verificarNecessidadeOCRCaf(userPortador, dispositivoEnviado);
      if (!valido) {
        this.registrarDispositivo(userPortador, dispositivoEnviado);
      }
      return valido;
    }
    return this.verificaNecessidadeValidacaoOCR(userPortador, dispositivoEnviado);
  }

  public Boolean verificarNecessidadeOCRCaf(
      SecurityUserPortador userPortador, ValidarDispositivo dispositivoEnviado) {
    // regra de um ano do ocr feito, realizar novamente
    AntifraudeCafInstituicaoConfig antifraudeCafInstituicaoConfig =
        this.antifraudeService.buscarConfiguracaoCaf(userPortador.getIdInstituicao());
    LocalDateTime dataLimite = LocalDateTime.now().minusYears(1L);
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeService.findByIdCafInstituicaoConfigAndTxDocumentoAndDtHrUltimaVerificacaoIsAfter(
            antifraudeCafInstituicaoConfig.getId(), userPortador.getCpf(), dataLimite);
    if (antifraudeCafPortador == null) {
      return true;
    }

    if (antifraudeCafPortador.getBlIgnorarValidacao()) {
      return false;
    }

    if (antifraudeCafPortador.getBlForcarValidacao()
        || antifraudeCafPortador.getTxUrlSelfie() == null
        || antifraudeCafPortador.getTxUrlFrontDoc() == null) {
      return true;
    }

    if (antifraudeCafPortador.getIdStatus() != null
        && !antifraudeCafPortador
            .getIdStatus()
            .equals(AntifraudeCafPortadorStatusEnum.APPROVED.getStatus())) {
      return true;
    }
    return this.dipositivoValido(userPortador.getIdLogin(), dispositivoEnviado);
  }

  public Boolean verificaNecessidadeValidacaoOCR(
      SecurityUserPortador userPortador, ValidarDispositivo login) {

    AntifraudeOcrValidado migradosVallooOCR =
        vallooOcrRepository.findOneByIdLogin(userPortador.getIdLogin());

    PortadorDispositivo dispositivo =
        portadorDispositivoService
            .findFirstByIdLoginAndDataInvalidadoIsNullOrderByIdPortadorDispositivoDesc(
                userPortador.getIdLogin());

    if (dispositivo != null) {
      if (dispositivo.getDeviceId() != null) {
        String deviceIdPortadorDispositivo = dispositivo.getDeviceId().trim().toUpperCase();
        String loginDeviceId = login.getDeviceId().trim().toUpperCase();

        String architectureInfoPortadorDispositivo =
            dispositivo.getArchitectureInfo().trim().toUpperCase();
        String loginArchitectureInfo = login.getArchitectureInfo().trim().toUpperCase();

        String modelPortadorDispositivo = dispositivo.getModel().trim().toUpperCase();
        String loginModel = login.getModel().trim().toUpperCase();

        String platformNamePortadorDispositivo = dispositivo.getPlatformName().trim().toUpperCase();
        String loginPlatformName = login.getPlatformName().trim().toUpperCase();

        if (migradosVallooOCR != null) {
          if (!migradosVallooOCR.getMigrado()) {
            if (!deviceIdPortadorDispositivo.equals(loginDeviceId)
                || !architectureInfoPortadorDispositivo.equals(loginArchitectureInfo)
                || !modelPortadorDispositivo.equals(loginModel)
                || !platformNamePortadorDispositivo.equals(loginPlatformName)) {

              portadorDispositivoService.invalidarDispositivo(dispositivo);
              portadorDispositivoService.registrarNovoDispositivo(login, userPortador);
            }
            migradosVallooOCR.setMigrado(Boolean.TRUE);
            migradosVallooOCR.setDtHrMigrado(LocalDateTime.now());
            vallooOcrRepository.save(migradosVallooOCR);
            return false;

          } else {
            LocalDateTime dataLimite = LocalDateTime.now().minusYears(1L);
            AntifraudeCliente antifraudeCliente =
                antifraudeService.findByIdInstituicaoAndDocumentoAndDataUltimaVerificacaoIsAfter(
                    userPortador.getIdInstituicao().longValue(), userPortador.getCpf(), dataLimite);

            if (antifraudeCliente != null) {
              if (antifraudeCliente.getOcrValidado()) {
                if (deviceIdPortadorDispositivo.equals(loginDeviceId)) {
                  return false;

                  // Se todos os dados forem iguais, deve retornar FALSE para o front significando
                  // que NÃO é necessário passar pelo OCR.
                } else
                  return !(architectureInfoPortadorDispositivo.equals(loginArchitectureInfo)
                      && modelPortadorDispositivo.equals(loginModel)
                      && platformNamePortadorDispositivo.equals(loginPlatformName));
              }
            }
          }
        }
      }
    }
    return true;
  }

  /**
   * Valida se o tempo (em minutos) de redefinição da senha é menor que o tempo definido para
   * redefinir senha da instituição do portador.
   *
   * @param portadorLogin Portador Login
   */
  public void validarTempoRedefinicaoSenha(
      PortadorLogin portadorLogin, Boolean isSolicitacaoToken) {

    LocalDateTime ultimaRedefinicao = portadorLogin.getDataHoraUltimaRedefinicao();
    Integer parametroUtilizado =
        isSolicitacaoToken
            ? Constantes.PARAMETRO_TEMPO_VALIDADE_TOKEN_REDIFINICAO_SENHA
            : Constantes.PARAMETRO_TEMPO_VALIDADE_REDIFINICAO_SENHA;
    Long valorTempoParametro =
        paramValorService.findByParametroDefinicaoAndIdInstituicao(
            parametroUtilizado, portadorLogin.getIdInstituicao());
    if (!isSolicitacaoToken) {
      portadorLogin.setDataHoraUltimaRedefinicao(LocalDateTime.now());
    }

    if (valorTempoParametro != null
        && ultimaRedefinicao != null
        && ChronoUnit.MINUTES.between(ultimaRedefinicao, LocalDateTime.now())
            < valorTempoParametro) {
      throw new GenericServiceException(
          (isSolicitacaoToken
              ? "Solicitação de token para redefinição de senha não permitida em menos de "
                  + valorTempoParametro
                  + " minutos."
              : "Redefinição de senha não permitida em menos de "
                  + valorTempoParametro
                  + " minutos."),
          HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }

  public PortadorLogin encontraPortadorLoginResponsavelAtravesDoPortadorLoginDependente(
      Long idLogin) {

    return portadorLoginRepository.encontraPortadorLoginResponsavelAtravesDoPortadorLoginDependente(
        idLogin, TipoPortadorLoginEnum.DEPENDENTE.name(), TipoPortadorLoginEnum.RESPONSAVEL.name());
  }

  public ValidarCadastroOnboardVO validarCadastroOnboard(
      ValidarDadosOnboardVO validarDadosOnboardVO) throws IOException {
    ValidarCadastroOnboardVO validacao = new ValidarCadastroOnboardVO();

    Pessoa pessoa;
    if (validarDadosOnboardVO.getGrupoAcesso() != null) {
      pessoa =
          pessoaService.findPessoaAtualizadaRecenteComGrupoAcesso(
              validarDadosOnboardVO.getIdProcessadora(),
              validarDadosOnboardVO.getIdInstituicao(),
              validarDadosOnboardVO.getDocumento(),
              validarDadosOnboardVO.getGrupoAcesso());
    } else {
      pessoa =
          pessoaService.findPessoaAtualizadaRecente(
              validarDadosOnboardVO.getIdProcessadora(),
              validarDadosOnboardVO.getIdInstituicao(),
              validarDadosOnboardVO.getDocumento());
    }

    if (pessoa == null) {
      throw new GenericServiceException("Documento não cadastrado.", HttpStatus.FORBIDDEN);
    }

    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(ATUALIZA_CELULAR_ONBOARD);
    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(validarDadosOnboardVO.getIdProcessadora());
    valor.setIdInstituicao(validarDadosOnboardVO.getIdInstituicao());
    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    boolean atualizarCelular = params != null && !params.isEmpty();

    PortadorLogin portador =
        buscarLogin(
            validarDadosOnboardVO.getIdProcessadora(),
            validarDadosOnboardVO.getIdInstituicao(),
            validarDadosOnboardVO.getDocumento(),
            validarDadosOnboardVO.getDocumentoRepresentante(),
            validarDadosOnboardVO.getGrupoAcesso(),
            validarDadosOnboardVO.getTipoLogin(),
            "Erro ao buscar Login no cadastro do CAF");
    if (pessoa != null && validarDadosOnboardVO.getDocumentoRepresentante() != null) {
      List<ContaPagamento> contasPagamento =
          contaPagamentoService.findByIdPessoa(pessoa.getIdPessoa());
      for (ContaPagamento contaPagamento : contasPagamento) {
        RepresentanteLegal representanteLegal =
            this.representanteLegalService.findOneByIdContaAndCpfAndStatus(
                contaPagamento.getIdConta(),
                validarDadosOnboardVO.getDocumentoRepresentante(),
                RepresentanteLegalService.REPRESENTANTE_LEGAL_ATIVO);

        if (representanteLegal == null) {
          validacao.setDadosInvalidos(true);
        }

        if (representanteLegal != null && atualizarCelular) {
          String ddd = validarDadosOnboardVO.getTelefone().substring(0, 2);
          String celular = validarDadosOnboardVO.getTelefone().substring(2);
          representanteLegal.setDddCelular(Integer.parseInt(ddd));
          representanteLegal.setTelefoneCelular(Integer.parseInt(celular));
          representanteLegalService.save(representanteLegal);
        }

        if (representanteLegal != null
            && (representanteLegal.getDddCelular() == null
                || representanteLegal.getTelefoneCelular() == null)) {
          validacao.setDadosInvalidos(true);
        }

        if (representanteLegal != null
            && representanteLegal.getDddCelular() != null
            && representanteLegal.getTelefoneCelular() != null
            && !Objects.equals(
                validarDadosOnboardVO.getTelefone(),
                representanteLegal.getDddCelular().toString()
                    + representanteLegal.getTelefoneCelular().toString())) {
          validacao.setDadosInvalidos(true);
        }

        if (representanteLegal != null) {
          validacao.setDataNascimento(
              representanteLegal
                  .getDataNascimento()
                  .toInstant()
                  .atZone(ZoneId.systemDefault())
                  .toLocalDateTime());
        }
        validacao.setPreCadastro(representanteLegal != null && portador == null);
      }

      validacao.setPossuiLogin(portador != null);

    } else {
      validacao.setPossuiLogin(portador != null);
      validacao.setDadosInvalidos(pessoa == null);

      if (pessoa != null) {
        ProdutoInstituicaoDependentes produtoInstituicaoDependentes =
            produtoInstituicaoDependentesService.encontraProdutoResponsavelDependentePorIdPessoa(
                pessoa.getIdPessoa());
        if (produtoInstituicaoDependentes != null) {
          ContaPagamento contaPagamento =
              contaPagamentoService.encontraContaPorIdPessoaTitularidadeEInstituicao(
                  pessoa.getIdPessoa(), TITULAR, pessoa.getIdInstituicao());
          if (produtoInstituicaoDependentes
              .getProdutoInstituicaoDependentesId()
              .getIdProdutoResponsavel()
              .equals(contaPagamento.getIdProdutoInstituicao())) {
            validacao.setTipoPortadorLogin(TipoPortadorLoginEnum.RESPONSAVEL);
          } else {
            validacao.setTipoPortadorLogin(TipoPortadorLoginEnum.DEPENDENTE);
            PessoaResponsavelDependente relacao =
                pessoaResponsavelDependenteService.encontraRelacaoExistentePorIdPessoa(
                    pessoa.getIdPessoa());
            validacao.setDependenteAutorizado(relacao != null && relacao.getLoginAutorizado());
          }
        }
      }

      atualizaPessoaValidando(validarDadosOnboardVO, validacao, pessoa, atualizarCelular);
      // setar pre-cadastro
      validacao.setPreCadastro(pessoa != null && portador == null);
    }

    // setar se tem algo com a situacao do onboard
    AntifraudeCafPortador antifraudeCafPortador =
        this.antifraudeService.buscarCaf(
            validarDadosOnboardVO.getDocumento(),
            validarDadosOnboardVO.getIdInstituicao(),
            validarDadosOnboardVO.getDocumentoRepresentante());
    if (antifraudeCafPortador != null && antifraudeCafPortador.getIdStatus() == null) {
      validacao.setEncaminharAtendimento(true);
      return validacao;
    }
    if (antifraudeCafPortador != null && antifraudeCafPortador.getBlForcarValidacao()) {
      validacao.setEncaminharAtendimento(false);
      return validacao;
    }
    if (antifraudeCafPortador != null
        && antifraudeCafPortador.getIdStatus() != null
        && !antifraudeCafPortador
            .getIdStatus()
            .equals(AntifraudeCafPortadorStatusEnum.APPROVED.toString())) {
      validacao.setEncaminharAtendimento(true);
      return validacao;
    }
    if (antifraudeCafPortador != null
        && antifraudeCafPortador.getIdStatus() != null
        && antifraudeCafPortador
            .getIdStatus()
            .equals(AntifraudeCafPortadorStatusEnum.APPROVED.toString())) {
      validacao.setEncaminharAtendimento(false);
      validacao.setCadastroAprovado(true);
    }
    if (antifraudeCafPortador != null && antifraudeCafPortador.getBlForcarValidacao()) {
      validacao.setEncaminharAtendimento(false);
      validacao.setCadastroAprovado(false);
      return validacao;
    }
    if (portador != null) {
      ValidarDispositivo validarDispositivo = new ValidarDispositivo();
      validarDispositivo.setDeviceId(validarDadosOnboardVO.getDeviceId());
      validarDispositivo.setModel(validarDadosOnboardVO.getModel());
      validarDispositivo.setArchitectureInfo(validarDadosOnboardVO.getArchitectureInfo());
      validarDispositivo.setPlataformVersion(validarDadosOnboardVO.getPlataformVersion());
      validarDispositivo.setPlatformName(validarDadosOnboardVO.getPlatformName());
      validarDispositivo.setSistemaOperacional(validarDadosOnboardVO.getSistemaOperacional());
      Boolean dispositivoValido = !this.dipositivoValido(portador.getIdLogin(), validarDispositivo);
      validacao.setDispositivoValido(dispositivoValido);
    }
    return validacao;
  }

  public ValidarCadastroOnboardVO validarCadastroOnboardSemCaf(
      ValidarDadosOnboardVO validarDadosOnboardVO) throws IOException {

    ValidarCadastroOnboardVO validacao = new ValidarCadastroOnboardVO();

    Pessoa pessoa;
    if (validarDadosOnboardVO.getGrupoAcesso() != null) {
      pessoa =
          pessoaService.findPessoaAtualizadaRecenteComGrupoAcesso(
              validarDadosOnboardVO.getIdProcessadora(),
              validarDadosOnboardVO.getIdInstituicao(),
              validarDadosOnboardVO.getDocumento(),
              validarDadosOnboardVO.getGrupoAcesso());
    } else {
      pessoa =
          pessoaService.findPessoaAtualizadaRecente(
              validarDadosOnboardVO.getIdProcessadora(),
              validarDadosOnboardVO.getIdInstituicao(),
              validarDadosOnboardVO.getDocumento());
    }

    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(ATUALIZA_CELULAR_ONBOARD);
    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(validarDadosOnboardVO.getIdProcessadora());
    valor.setIdInstituicao(validarDadosOnboardVO.getIdInstituicao());
    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    boolean atualizarCelular = params != null && !params.isEmpty();

    PortadorLogin portador =
        buscarLogin(
            validarDadosOnboardVO.getIdProcessadora(),
            validarDadosOnboardVO.getIdInstituicao(),
            validarDadosOnboardVO.getDocumento(),
            validarDadosOnboardVO.getDocumentoRepresentante(),
            validarDadosOnboardVO.getGrupoAcesso(),
            validarDadosOnboardVO.getTipoLogin(),
            "Erro ao buscar Login no cadastro do CAF");

    Boolean validacaoCafHabilitada =
        aplicativoFrontendService.validacaoCafHabilitada(
            validarDadosOnboardVO.getIdInstituicao(), validarDadosOnboardVO.getIdProcessadora());

    if (validacaoCafHabilitada) {
      throw new GenericServiceException(
          "É necessário fazer validação via CAF para este aplicativo.");
    }

    validacao.setValidacaoCafHabilitada(false);

    if (pessoa != null && validarDadosOnboardVO.getDocumentoRepresentante() != null) {
      List<ContaPagamento> contasPagamento =
          contaPagamentoService.findByIdPessoa(pessoa.getIdPessoa());
      for (ContaPagamento contaPagamento : contasPagamento) {
        RepresentanteLegal representanteLegal =
            this.representanteLegalService.findOneByIdContaAndCpfAndStatus(
                contaPagamento.getIdConta(),
                validarDadosOnboardVO.getDocumentoRepresentante(),
                RepresentanteLegalService.REPRESENTANTE_LEGAL_ATIVO);

        if (representanteLegal == null) {
          validacao.setDadosInvalidos(true);
        }

        if (representanteLegal != null && atualizarCelular) {
          String ddd = validarDadosOnboardVO.getTelefone().substring(0, 2);
          String celular = validarDadosOnboardVO.getTelefone().substring(2);
          representanteLegal.setDddCelular(Integer.parseInt(ddd));
          representanteLegal.setTelefoneCelular(Integer.parseInt(celular));
          representanteLegalService.save(representanteLegal);
        }

        if (representanteLegal != null
            && (representanteLegal.getDddCelular() == null
                || representanteLegal.getTelefoneCelular() == null)) {
          validacao.setDadosInvalidos(true);
        }

        if (representanteLegal != null
            && representanteLegal.getDddCelular() != null
            && representanteLegal.getTelefoneCelular() != null
            && !Objects.equals(
                validarDadosOnboardVO.getTelefone(),
                representanteLegal.getDddCelular().toString()
                    + representanteLegal.getTelefoneCelular().toString())) {
          validacao.setDadosInvalidos(true);
        }

        if (representanteLegal != null) {
          validacao.setDataNascimento(
              representanteLegal
                  .getDataNascimento()
                  .toInstant()
                  .atZone(ZoneId.systemDefault())
                  .toLocalDateTime());
        }
        validacao.setPreCadastro(representanteLegal != null && portador == null);
      }

      validacao.setPossuiLogin(portador != null);

    } else {
      validacao.setPossuiLogin(portador != null);
      validacao.setDadosInvalidos(pessoa == null);

      if (pessoa != null) {
        ProdutoInstituicaoDependentes produtoInstituicaoDependentes =
            produtoInstituicaoDependentesService.encontraProdutoResponsavelDependentePorIdPessoa(
                pessoa.getIdPessoa());
        if (produtoInstituicaoDependentes != null) {
          ContaPagamento contaPagamento =
              contaPagamentoService.encontraContaPorIdPessoaTitularidadeEInstituicao(
                  pessoa.getIdPessoa(), TITULAR, pessoa.getIdInstituicao());
          if (produtoInstituicaoDependentes
              .getProdutoInstituicaoDependentesId()
              .getIdProdutoResponsavel()
              .equals(contaPagamento.getIdProdutoInstituicao())) {
            validacao.setTipoPortadorLogin(TipoPortadorLoginEnum.RESPONSAVEL);
          } else {
            validacao.setTipoPortadorLogin(TipoPortadorLoginEnum.DEPENDENTE);
            PessoaResponsavelDependente relacao =
                pessoaResponsavelDependenteService.encontraRelacaoExistentePorIdPessoa(
                    pessoa.getIdPessoa());
            validacao.setDependenteAutorizado(relacao != null && relacao.getLoginAutorizado());
          }
        }
      }

      atualizaPessoaValidando(validarDadosOnboardVO, validacao, pessoa, atualizarCelular);
      // setar pre-cadastro
      validacao.setPreCadastro(pessoa != null && portador == null);
    }

    // Define valores padrão para os campos relacionados ao CAF
    validacao.setEncaminharAtendimento(false);
    validacao.setCadastroAprovado(true);

    // Popular dados pessoais se a pessoa foi encontrada
    if (pessoa != null) {
      validacao.setNomeCompleto(pessoa.getNomeCompleto());
      validacao.setRazaoSocial(pessoa.getRazaoSocial());
      validacao.setEmail(pessoa.getEmail());
      validacao.setDataFundacao(pessoa.getDataFundacao());
      validacao.setNacionalidade(pessoa.getNacionalidade());
      validacao.setNaturalidade(pessoa.getNaturalidade());
      validacao.setEstadoCivil(pessoa.getIdEstadoCivil());
      validacao.setIdSexo(pessoa.getIdSexo());

      // Buscar endereço residencial
      EnderecoPessoa enderecoResidencial =
          enderecoService.findOneByIdPessoaAndIdTipoEnderecoAndStatus(
              pessoa.getIdPessoa(), Constantes.ENDERECO_RESIDENCIAL, Constantes.ENDERECO_ATIVO);

      if (enderecoResidencial != null) {
        ValidarCadastroOnboardVO.EnderecoResidencial endereco =
            new ValidarCadastroOnboardVO.EnderecoResidencial();
        endereco.setCep(enderecoResidencial.getCep());
        endereco.setLogradouro(enderecoResidencial.getLogradouro());
        endereco.setNumero(enderecoResidencial.getNumero());
        endereco.setComplemento(enderecoResidencial.getComplemento());
        endereco.setBairro(enderecoResidencial.getBairro());
        endereco.setCidade(enderecoResidencial.getCidade());
        endereco.setUf(enderecoResidencial.getUf());
        validacao.setEnderecoResidencial(endereco);
      }
    }

    // Enviar token SMS para validação do onboard
    try {
      enviarTokenValidacaoOnboard(validarDadosOnboardVO);
    } catch (Exception e) {
      log.warn("Erro ao enviar token de validação onboard: " + e.getMessage());
      // Não bloqueia o fluxo se houver erro no envio do SMS
    }

    if (portador != null) {
      ValidarDispositivo validarDispositivo = new ValidarDispositivo();
      validarDispositivo.setDeviceId(validarDadosOnboardVO.getDeviceId());
      validarDispositivo.setModel(validarDadosOnboardVO.getModel());
      validarDispositivo.setArchitectureInfo(validarDadosOnboardVO.getArchitectureInfo());
      validarDispositivo.setPlataformVersion(validarDadosOnboardVO.getPlataformVersion());
      validarDispositivo.setPlatformName(validarDadosOnboardVO.getPlatformName());
      validarDispositivo.setSistemaOperacional(validarDadosOnboardVO.getSistemaOperacional());
      Boolean dispositivoValido = !this.dipositivoValido(portador.getIdLogin(), validarDispositivo);
      validacao.setDispositivoValido(dispositivoValido);
    }
    return validacao;
  }

  /**
   * Envia token SMS para validação do onboard
   *
   * @param validarDadosOnboardVO dados do onboard
   */
  private void enviarTokenValidacaoOnboard(ValidarDadosOnboardVO validarDadosOnboardVO) {
    try {
      GerarTokenCadastroLogin gerarToken = new GerarTokenCadastroLogin();
      gerarToken.setIdProcessadora(validarDadosOnboardVO.getIdProcessadora());
      gerarToken.setIdInstituicao(validarDadosOnboardVO.getIdInstituicao());
      String telefone = validarDadosOnboardVO.getTelefone();
      String chaveExterna = telefone + LocalDateTime.now().toLocalDate().toString();
      gerarToken.setChave(chaveExterna);
      gerarToken.setCelular(telefone);
      tokenAcessoService.gerarTokenCadastroLogin(gerarToken);

      log.info(
          "Token de validação onboard gerado e enviado para telefone: "
              + telefone.substring(0, 4)
              + "****");
    } catch (Exception e) {
      log.error("Erro ao enviar token de validação onboard: " + e.getMessage(), e);
      throw new GenericServiceException("Erro ao enviar token de validação: " + e.getMessage());
    }
  }

  private void atualizaPessoaValidando(
      ValidarDadosOnboardVO validarDadosOnboardVO,
      ValidarCadastroOnboardVO validacao,
      Pessoa pessoa,
      boolean atualizarCelular) {
    if (pessoa != null && atualizarCelular) {
      String ddd = validarDadosOnboardVO.getTelefone().substring(0, 2);
      String celular = validarDadosOnboardVO.getTelefone().substring(2);
      pessoa.setDddTelefoneCelular(Integer.parseInt(ddd));
      pessoa.setTelefoneCelular(Integer.parseInt(celular));
      pessoaService.save(pessoa);
    }

    if (pessoa != null
        && (pessoa.getDddTelefoneCelular() == null || pessoa.getTelefoneCelular() == null)) {
      validacao.setDadosInvalidos(true);
    }

    if (pessoa != null
        && pessoa.getDddTelefoneCelular() != null
        && pessoa.getTelefoneCelular() != null
        && !Objects.equals(
            validarDadosOnboardVO.getTelefone(),
            pessoa.getDddTelefoneCelular().toString() + pessoa.getTelefoneCelular().toString())) {
      validacao.setDadosInvalidos(true);
    }

    if (pessoa != null) {
      validacao.setDataNascimento(pessoa.getDataNascimento());
    }
  }

  public Boolean dipositivoValido(Long idPortadorLogin, ValidarDispositivo dispositivoEnviado) {
    PortadorDispositivo dispositivo =
        portadorDispositivoService
            .findFirstByIdLoginAndDataInvalidadoIsNullOrderByIdPortadorDispositivoDesc(
                idPortadorLogin);
    if (dispositivo == null) {
      return true;
    }
    if (dispositivo.getDeviceId() == null) {
      return true;
    }
    String deviceIdPortadorDispositivo = dispositivo.getDeviceId().trim().toUpperCase();
    String loginDeviceId = dispositivoEnviado.getDeviceId().trim().toUpperCase();

    // Se todos os dados forem iguais, deve retornar FALSE para o front
    // significando que NÃO é necessário passar pelo OCR.
    return !deviceIdPortadorDispositivo.equals(loginDeviceId);
  }

  public Boolean encontraConfiguracaoCaf(
      Integer idInstituicao, String documento, Optional<String> optCpfRepresentante) {
    Boolean redefinirSenhaComCAF =
        produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(idInstituicao);
    if (redefinirSenhaComCAF) {
      AntifraudeCafPortador antifraudeCafPortador = null;
      if (optCpfRepresentante.isPresent()) {
        antifraudeCafPortador =
            antifraudeService.encontraCafAprovadoPorDocumentoRepresentanteEInstituicao(
                documento, optCpfRepresentante.get(), idInstituicao);
      } else {
        antifraudeCafPortador =
            antifraudeService.encontraCafAprovadoPorDocumentoEInstituicao(documento, idInstituicao);
      }

      if (antifraudeCafPortador == null) {
        throw new GenericServiceException(
            ConstantesErro.CAF_NAO_ENCONTRADA_RECONHECIMENTO_FACIAL.getMensagem(),
            HttpStatus.FORBIDDEN);
      }
    }
    return redefinirSenhaComCAF;
  }

  public Boolean encontraConfiguracaoCafPorIdConta(Long idConta, SecurityUserPortador portador) {
    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(idConta, portador);
    return produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta);
  }

  public void alterarDispositivo(
      SecurityUserPortador securityUserPortador, AlterarDispositivo alterarDispositivo) {
    TokenCaf tokenCaf = new TokenCaf();
    tokenCaf.setJwt(alterarDispositivo.getTokenCaf());
    this.antifraudeService.registraValidacaoFacialCaf(
        alterarDispositivo.getDocumento(),
        securityUserPortador.getIdInstituicao(),
        AntifraudeCafFacialObjetivosEnum.TROCA_DE_DISPOSITIVO.getIdObjetivo(),
        Optional.ofNullable(securityUserPortador.getDocumentoAcesso()),
        tokenCaf);
    boolean valido =
        this.registroValidacaoFacialCafService.checaEEfetivaValidacao(
            alterarDispositivo.getDocumento(),
            securityUserPortador.getIdInstituicao(),
            AntifraudeCafFacialObjetivosEnum.TROCA_DE_DISPOSITIVO);
    if (valido) {
      this.registrarDispositivo(securityUserPortador, alterarDispositivo.getDispositivo());
    }
  }

  public TipoPortadorLoginEnum determinaTipoLogin(CadastrarPortadorLogin cadastrarPortadorLogin) {
    PortadorLoginVo portadorLoginVo =
        new PortadorLoginVo(
            cadastrarPortadorLogin.getCnpj(),
            cadastrarPortadorLogin.getCpf(),
            cadastrarPortadorLogin.getIdInstituicao());
    TipoPortadorLoginEnum tipoPortadorLoginEnum =
        determinaTipoLogin(
            portadorLoginVo.getDocumentoAcesso(),
            cadastrarPortadorLogin.getGrupoAcesso(),
            portadorLoginVo.getIdTipoPessoa(),
            cadastrarPortadorLogin.getTipoLogin());
    cadastrarPortadorLogin.setTipoLogin(tipoPortadorLoginEnum);
    return tipoPortadorLoginEnum;
  }

  @SuppressWarnings("unnused")
  public TipoPortadorLoginEnum determinaTipoLogin(FazerLoginPortador fazerLoginPortador) {
    PortadorLoginVo portadorLoginVo =
        new PortadorLoginVo(
            fazerLoginPortador.getCnpj(),
            fazerLoginPortador.getCpf(),
            fazerLoginPortador.getIdInstituicao());
    TipoPortadorLoginEnum tipoPortadorLoginEnum =
        determinaTipoLogin(
            fazerLoginPortador.getDocumentoAcesso() != null
                ? fazerLoginPortador.getDocumentoAcesso()
                : portadorLoginVo.getDocumentoAcesso(),
            fazerLoginPortador.getGrupoAcesso(),
            portadorLoginVo.getIdTipoPessoa(),
            fazerLoginPortador.getTipoLogin());
    fazerLoginPortador.setTipoLogin(tipoPortadorLoginEnum);
    return tipoPortadorLoginEnum;
  }

  public TipoPortadorLoginEnum determinaTipoLogin(
      String documentoAcesso,
      Long grupoAcesso,
      Integer tipoPessoa,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {

    if (tipoPortadorLoginEnum != null) {
      return tipoPortadorLoginEnum;
    }

    if (grupoAcesso != null) {
      return TipoPortadorLoginEnum.LEGADO_MULTIBENEFICIOS;
    }

    if (documentoAcesso != null) {
      if (tipoPessoa.equals(PESSOA_FISICA)) {
        return TipoPortadorLoginEnum.LEGADO_DOCUMENTO_ACESSO_PESSOA_ADICIONAL;
      } else {
        return TipoPortadorLoginEnum.LEGADO_DOCUMENTO_ACESSO_REPRESENTANTE_LEGAL;
      }
    }

    return TipoPortadorLoginEnum.LEGADO_SIMPLES;
  }

  public List<String> determinaTipoLoginPorAplicativoEDocumento(Integer idApp, String documento) {
    List<String> tipoPortadorLogin = new ArrayList<>();
    AplicativoEnum aplicativoEnum = AplicativoEnum.devolveEnumPeloId(idApp);
    if (aplicativoEnum == null) {
      throw new GenericServiceException(
          ConstantesErro.PTL_APLICATIVO_NAO_IDENTIFICADO.getMensagem(), HttpStatus.BAD_REQUEST);
    }
    switch (aplicativoEnum) {
      case ACAMPAY:
        tipoPortadorLogin =
            portadorLoginRepository.encontraTipoLoginPorAppEDocumento(idApp, documento);
        if (tipoPortadorLogin.isEmpty()) {
          throw new GenericServiceException(
              ConstantesErro.PTL_TIPO_LOGIN_NAO_IDENTIFICADO.format(documento),
              HttpStatus.BAD_REQUEST);
        }
        break;
      case BANESE_CORPORATIVO:
        tipoPortadorLogin =
            portadorLoginRepository.encontraTipoLoginPorAppEDocumentoAcesso(idApp, documento);
        if (tipoPortadorLogin.isEmpty()) {
          throw new GenericServiceException(
              ConstantesErro.PTL_TIPO_LOGIN_NAO_IDENTIFICADO.format(documento),
              HttpStatus.BAD_REQUEST);
        }
        break;
      default:
        throw new GenericServiceException(
            ConstantesErro.PTL_TIPO_LOGIN_NAO_IDENTIFICADO.format(documento),
            HttpStatus.BAD_REQUEST);
    }
    return tipoPortadorLogin;
  }

  public List<String> resgataTodosTipoLogin(Integer idProdInstituicao) {
    List<TipoLoginProduto> tipoLoginProdutosList =
        tipoLoginProdutoService.encontraAssociadoPorProduto(idProdInstituicao);

    if (tipoLoginProdutosList != null && !tipoLoginProdutosList.isEmpty()) {
      return tipoLoginProdutosList.stream()
          .map(TipoLoginProduto::getTipoLogin)
          .map(TipoPortadorLogin::getTipoLogin)
          .map(TipoPortadorLoginEnum::name)
          .flatMap(
              tipo -> {
                if (tipo.startsWith("LEGADO_")) {
                  return Stream.of("PADRÃO");
                } else {
                  return Stream.of(tipo);
                }
              })
          .distinct()
          .collect(Collectors.toList());
    }
    return Arrays.stream(TipoPortadorLoginEnum.values())
        .flatMap(
            tipo -> {
              if (tipo.name().startsWith("LEGADO_")) {
                return Stream.of("PADRÃO");
              } else {
                return Stream.of(tipo.name());
              }
            })
        .distinct()
        .collect(Collectors.toList());
  }

  public String determinaTipoLoginIdConta(Long idConta) {
    return portadorLoginRepository.encontraTipoLoginPorIdConta(idConta);
  }

  public PortadorLoginCredencialVO validarDadosCadastroCorporativo(
      PortadorLoginCredencialVO portadorLoginCredencialVO) {

    String ultimos4Digitos =
        portadorLoginCredencialVO
            .getNumeroCartao()
            .substring(portadorLoginCredencialVO.getNumeroCartao().length() - 4);

    List<Credencial> credencialList =
        credencialService
            .findCredenciaisByDocumentoAndInstituicaoAndIdProcessadoraAndTipoPessoaAndStatus(
                portadorLoginCredencialVO.getIdProcessadora(),
                portadorLoginCredencialVO.getIdInstituicao(),
                portadorLoginCredencialVO.getIdProdInstituicao(),
                portadorLoginCredencialVO.getCnpj(),
                portadorLoginCredencialVO.getTipoPessoa(),
                Arrays.asList(0, 1, 5),
                Integer.valueOf(ultimos4Digitos));

    if (credencialList == null || credencialList.isEmpty()) {
      throw new GenericServiceException("Credencial não encontrada.", HttpStatus.BAD_REQUEST);
    }

    Credencial credencial = credencialList.get(0);

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            portadorLoginCredencialVO.getIdProcessadora(),
            portadorLoginCredencialVO.getIdInstituicao(),
            portadorLoginCredencialVO.getCnpj(),
            portadorLoginCredencialVO.getTipoPessoa());

    if (pessoa == null) {
      throw new GenericServiceException("Pessoa não encontrada.", HttpStatus.BAD_REQUEST);
    }

    ContaPagamento conta = contaPagamentoService.findById(credencial.getIdConta());

    if (!conta
        .getProdutoInstituicao()
        .getInstituicao()
        .getIdInstituicao()
        .equals(ID_PRODUCAO_INSTITUICAO_MULVI)) {
      log.info(
          "Instituição: "
              + conta.getProdutoInstituicao().getInstituicao()
              + " não permitida para validação de dados corporativo.");
      throw new RuntimeException("Não foi possível validar os dados.");
    }

    SecurityUser securityUser = new SecurityUser();
    securityUser.setHierarquiaType(HierarquiaType.INSTITUICAO);

    DadosSensiveisCredencial dadosSensiveis = credencialService.getDadosSensiveisCred(credencial);

    // Obtém a senha descriptografada do Jcard sem padding
    String senha = credencialService.getSenhaDescriptografadaDoJcardSemPadding(credencial);

    // Verifica se a senha fornecida é igual à senha descriptografada
    if (!senha.equals(portadorLoginCredencialVO.getSenha())) {
      log.info("Senha fornecida não corresponde à senha armazenada.");
    }

    if (!dadosSensiveis.getPan().equals(portadorLoginCredencialVO.getNumeroCartao())) {
      log.info("Número do cartão fornecido não corresponde ao PAN.");
    }

    if (!pessoa.getDocumento().equals(portadorLoginCredencialVO.getCnpj())) {
      log.info("CPF fornecido não corresponde ao documento da pessoa.");
    }

    if (!dadosSensiveis.getCvv2().equals(portadorLoginCredencialVO.getCve())) {
      log.info("CVE fornecido não corresponde ao CVV2 dos dados sensíveis.");
    }

    if (!senha.equals(portadorLoginCredencialVO.getSenha())
        || !dadosSensiveis.getPan().equals(portadorLoginCredencialVO.getNumeroCartao())
        || !pessoa.getDocumento().equals(portadorLoginCredencialVO.getCnpj())
        || !dadosSensiveis.getCvv2().equals(portadorLoginCredencialVO.getCve())) {
      log.info("Falha na validação dos dados. Lançando exceção.");
      throw new GenericServiceException(
          "Não foi possível validar os dados.", HttpStatus.BAD_REQUEST);
    }

    return portadorLoginCredencialVO;
  }

  /**
   * Cancela o PortadorLogin e altera o status da credencial.
   *
   * @param idConta ID da conta associada ao PortadorLogin a ser cancelado.
   */
  @Transactional
  public void cancelarPortadorLogin(
      Long idConta,
      SecurityUser user,
      String token,
      String senhaUsuarioLogado,
      HttpServletRequest request) {
    AcessoUsuario acessoUsuarioCompleto =
        acessoUsuarioService.findByLoginAndSenha(user.getLogin().toUpperCase(), senhaUsuarioLogado);
    if (acessoUsuarioCompleto == null) {
      throw new GenericServiceException(
          acessoUsuarioService.USUARIO_NAO_CADASTRO_SENHA_INVALIDA, HttpStatus.UNAUTHORIZED);
    }

    List<ContaPessoa> contas = contaPessoaService.findByIdConta(idConta);
    if (contas == null || contas.isEmpty()) {
      throw new GenericServiceException("Conta não encontrada.");
    }
    ContaPessoa conta = contas.get(0);

    if (conta.getContaPagamento().getIdInstituicao() != user.getIdInstituicao()) {
      throw new GenericServiceException("Conta não pertence a instituião do usuário autenticado.");
    }

    Credencial credencialFisica =
        credencialService.alterarCredencialCorporativo(conta, user, token, senhaUsuarioLogado);

    // Constrói o documentoAcesso a partir dos 4 primeiros dígitos do bin6 e ultimos4Digitos
    String documentoAcesso =
        credencialFisica.getBin6().toString().substring(0, 4)
            + credencialFisica.getUltimos4Digitos();
    PortadorLogin portadorLogin =
        buscarPortadorLoginPorInstituicaoProcessadoraEDocumento(
            conta.getPessoa().getIdInstituicao(),
            conta.getPessoa().getIdProcessadora(),
            documentoAcesso);

    // Cancela o PortadorLogin se existir
    if (portadorLogin != null) {
      portadorLogin.setDataHoraCancelamento(LocalDateTime.now());
      save(portadorLogin);
    }
  }

  /**
   * Busca um PortadorLogin por instituição, processadora e documento de acesso.
   *
   * @param idInstituicao ID da instituição.
   * @param idProcessadora ID da processadora.
   * @param documentoAcesso Documento de acesso.
   * @return PortadorLogin encontrado ou null se não existir.
   */
  public PortadorLogin buscarPortadorLoginPorInstituicaoProcessadoraEDocumento(
      Integer idInstituicao, Integer idProcessadora, String documentoAcesso) {
    return portadorLoginRepository.findByIdInstituicaoAndIdProcessadoraAndDocumentoAcesso(
        idInstituicao, idProcessadora, documentoAcesso);
  }
}
