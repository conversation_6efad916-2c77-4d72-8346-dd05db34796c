package br.com.sinergico.util;

import br.com.entity.suporte.Feriado;
import br.com.sinergico.service.suporte.FeriadoService;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.Locale;
import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.hibernate.bytecode.spi.NotInstrumentedException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DateUtil {
  private static Log log = LogFactory.getLog(DateUtil.class);

  public static final String YYMM = "yyMM";
  public static final String DD_MM_YYYY = "dd/MM/yyyy";
  public static final String DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss";
  public static final String DATE_TIME_FORMAT_ISO_8601_SPACE_VARIANT = "yyyy-MM-dd HH:mm:ss";
  public static final String DATE_FORMAT_ISO8601 = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
  private static final Integer UM_DIA_UTIL = 1;

  private static final Integer DEZ_DIAS_CORRIDOS = 10;
  private static final Integer NOVE_DIAS_CORRIDOS = 9;
  private static final Integer OITO_DIAS_CORRIDOS = 8;
  private static final Integer QUATORZE_DIAS_CORRIDOS = 14;
  private static final Integer TREZE_DIAS_CORRIDOS = 13;
  private static final Integer DOZE_DIAS_CORRIDOS = 12;
  private static final Integer ONZE_DIA_CORRIDO = 11;

  public static final int SEGUNDOS = 1;
  public static final int MINUTOS = 2;
  public static final int HORAS = 3;
  public static final int DIAS = 4;

  @Autowired private FeriadoService feriadoService;

  public static Calendar getCalendarUltimaMigracaoFaturaBahamas() {
    Calendar calendar = Calendar.getInstance();
    calendar.set(2017, Calendar.NOVEMBER, 12, 0, 0, 0);
    calendar.set(Calendar.HOUR, calendar.getActualMaximum(Calendar.HOUR));
    calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
    calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
    calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));
    return calendar;
  }

  public static Calendar getCalendarMigracaoTerminadaFaturaBahamas() {
    Calendar calendar = Calendar.getInstance();
    calendar.set(2017, Calendar.NOVEMBER, 13, 0, 0, 0);
    return setZeroHour(calendar);
  }

  public static Calendar setZeroHour(Calendar calendar) {
    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar;
  }

  public static Date setZeroHourOfDate(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    calendar.set(Calendar.HOUR_OF_DAY, 0);
    calendar.set(Calendar.MINUTE, 0);
    calendar.set(Calendar.SECOND, 0);
    calendar.set(Calendar.MILLISECOND, 0);
    return calendar.getTime();
  }

  public static Calendar getCalendarUltimaMigracaoINMAIS_DATA() {
    Calendar calendar = Calendar.getInstance();
    calendar.set(2017, Calendar.OCTOBER, 8, 0, 0, 0);
    calendar.set(Calendar.HOUR, calendar.getActualMaximum(Calendar.HOUR));
    calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
    calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
    calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));
    return calendar;
  }

  public static Calendar getCalendarMigracaoTerminadaINMAIS_DATA() {
    Calendar calendar = Calendar.getInstance();
    calendar.set(2017, Calendar.OCTOBER, 9, 0, 0, 0);
    return setZeroHour(calendar);
  }

  // Transforma Date em String - passando formato e data
  // retorna data em String
  public static String dateFormat(String mask, Date date) {

    String saida = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat(mask, Locale.forLanguageTag("pt"));
      SimpleDateFormat.getAvailableLocales();
      saida = sdf.format(date);
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado." + e.getMessage());
    }
    return saida;
  }

  public static String dateTimeFormat(String mask, LocalDateTime dateTime) {
    String saida = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat(mask, Locale.forLanguageTag("pt"));
      SimpleDateFormat.getAvailableLocales();
      saida = sdf.format(dateTime);
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado." + e.getMessage());
    }
    return saida;
  }

  // Transforma String em Date - passando mascara e data
  // retorna data em Date
  public static Date parseDate(String mask, String date) {
    Date saida = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat(mask);
      saida = sdf.parse(date);
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado.");
    }
    return saida;
  }

  // Transforma String em String - passando string
  // retorna string no formato brasileiro
  public static String parseStringDate(String date) {
    String saida = null;
    String[] list = null;
    try {
      list = date.split("-");
      saida = list[2] + "/" + list[1] + "/" + list[0];
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado.");
    }
    return saida;
  }

  public static Calendar localDateTimeToCalendar(LocalDateTime date) {
    Calendar c = Calendar.getInstance();
    c.setTime(Date.from(date.atZone(ZoneId.systemDefault()).toInstant()));
    return c;
  }

  public static int daysBetween(LocalDateTime day1, LocalDateTime day2) {
    return daysBetween(localDateTimeToCalendar(day1), localDateTimeToCalendar(day2));
  }

  public static int daysBetween(Calendar day1, LocalDateTime day2) {
    return daysBetween(day1, localDateTimeToCalendar(day2));
  }

  public static int daysBetween(Calendar day1, Calendar day2) {
    Calendar dayOne = (Calendar) day1.clone(), dayTwo = (Calendar) day2.clone();

    if (dayOne.get(Calendar.YEAR) == dayTwo.get(Calendar.YEAR)) {
      return Math.abs(dayOne.get(Calendar.DAY_OF_YEAR) - dayTwo.get(Calendar.DAY_OF_YEAR));
    } else {
      if (dayTwo.get(Calendar.YEAR) > dayOne.get(Calendar.YEAR)) {
        // swap them
        Calendar temp = dayOne;
        dayOne = dayTwo;
        dayTwo = temp;
      }
      int extraDays = 0;

      int dayOneOriginalYearDays = dayOne.get(Calendar.DAY_OF_YEAR);

      while (dayOne.get(Calendar.YEAR) > dayTwo.get(Calendar.YEAR)) {
        dayOne.add(Calendar.YEAR, -1);
        // getActualMaximum() important for leap years
        extraDays += dayOne.getActualMaximum(Calendar.DAY_OF_YEAR);
      }

      return extraDays - dayTwo.get(Calendar.DAY_OF_YEAR) + dayOneOriginalYearDays;
    }
  }

  public int workDaysBetween(LocalDateTime day1, LocalDateTime day2) {
    return workDaysBetween(localDateTimeToCalendar(day1), localDateTimeToCalendar(day2));
  }

  public int workDaysBetween(Calendar day1, LocalDateTime day2) {
    return workDaysBetween(day1, localDateTimeToCalendar(day2));
  }

  public int workDaysBetween(Calendar day1, Calendar day2) {
    Calendar dayOne = (Calendar) day1.clone(), dayTwo = (Calendar) day2.clone();
    Calendar index = (Calendar) dayOne.clone();
    int diasUteis = 0;
    while (index.get(Calendar.YEAR) != dayTwo.get(Calendar.YEAR)
        || index.get(Calendar.DAY_OF_YEAR) != dayTwo.get(Calendar.DAY_OF_YEAR)) {
      if (index.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY
          && index.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY
          && !feriadoService.isFeriado(index.getTime())) {
        diasUteis = diasUteis + 1;
      }
      index.add(Calendar.DATE, 1);
    }
    return diasUteis;
  }

  // Transforma Date em LocalDateTime usando Instant
  // retorna data em LocalDateTime
  public static LocalDateTime dateToLocalDateTime(Date date) {
    Instant instant = Instant.ofEpochMilli(date.getTime());
    if (instant == null) {
      return null;
    }
    return LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
  }

  // Trnasforma LocalDateTime em Date usando Instant
  // retorna data em Date
  public static Date localDateTimeToDate(LocalDateTime localDateTime) {
    if (localDateTime == null) {
      return null;
    }
    Instant instant = localDateTime.atZone(ZoneId.systemDefault()).toInstant();
    if (instant == null) {
      return null;
    }
    return Date.from(instant);
  }

  // Valida data
  // retorna true ou false
  public static Boolean validDate(String mask, String date) {
    Date saida = null;
    try {
      SimpleDateFormat sdf = new SimpleDateFormat(mask);
      saida = sdf.parse(date);
    } catch (Exception e) {
      log.error(e);
      return Boolean.FALSE;
    }
    return saida != null ? Boolean.TRUE : Boolean.FALSE;
  }

  // soma ou subtrai dias na data - passando data e dias
  public static Date addDayDate(Date date, Integer days) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.DAY_OF_MONTH, days);

    return calendar.getTime();
  }

  /**
   * @param data
   * @param tempoAAumentar
   * @param medidaTempo(1-segundos,2,minutos,3-horas,4-dias
   * @return
   */
  public static Date aumentar(Date data, Integer tempoAAumentar, Integer medidaTempo) {

    switch (medidaTempo) {
      case SEGUNDOS:
        return aumentarSegundos(data, tempoAAumentar);
      case MINUTOS:
        return aumentarMinutos(data, tempoAAumentar);
      case HORAS:
        return aumentarHoras(data, tempoAAumentar);
      case DIAS:
        return aumentarDias(data, tempoAAumentar);
      default:
        return data;
    }
  }

  public static Date diminuirSegundos(Date data, Integer segundosADimuniuir) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime minusSeconds = localDate.minusSeconds(segundosADimuniuir);

    return localDateTimeToDate(minusSeconds);
  }

  public static Date diminuirMinutos(Date data, Integer minutosADimuniuir) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime minusMinutes = localDate.minusMinutes(minutosADimuniuir);

    return localDateTimeToDate(minusMinutes);
  }

  public static Date diminuirDias(Date data, Integer diasADiminuir) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime resultado = localDate.minusDays(diasADiminuir);

    return localDateTimeToDate(resultado);
  }

  public static Date aumentarSegundos(Date data, Integer segundosAAumentar) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime resultado = localDate.plusSeconds(segundosAAumentar);

    return localDateTimeToDate(resultado);
  }

  public static Date aumentarMinutos(Date data, Integer minutosAAumentar) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime resultado = localDate.plusMinutes(minutosAAumentar);

    return localDateTimeToDate(resultado);
  }

  public static Date aumentarHoras(Date data, Integer horasAAumentar) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime resultado = localDate.plusHours(horasAAumentar);

    return localDateTimeToDate(resultado);
  }

  public static Date aumentarDias(Date data, Integer diasAAumentar) {
    LocalDateTime localDate = dateToLocalDateTime(data);
    LocalDateTime minusMinutes = localDate.plusDays(diasAAumentar);

    return localDateTimeToDate(minusMinutes);
  }

  // soma ou subtrai meses na data - passando data e meses
  public static Date addMonthDate(Date date, Integer months) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    calendar.add(Calendar.MONTH, months);

    return calendar.getTime();
  }

  // transforma Date em LocalDateTime usando String na conversão
  // retorna LocalDateTime - formato = yyyy-MM-dd HH:mm
  public static LocalDateTime convertDateToStringToLocalDateTime(String pFormato, Date cal) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    String ret = new SimpleDateFormat(pFormato).format(cal.getTime());
    ret = ret + " 00:00";
    LocalDateTime dateAntecipadoOk = LocalDateTime.parse(ret, formatter);
    return dateAntecipadoOk;
  }

  public static LocalDateTime convertDateToLocalDateTimeAndSeconds(String pFormato, Date cal) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    String ret = new SimpleDateFormat(pFormato).format(cal.getTime());
    // ret = ret + " 00:00";
    LocalDateTime dateAntecipadoOk = LocalDateTime.parse(ret, formatter);
    return dateAntecipadoOk;
  }

  public static LocalDate convertToLocalDateViaInstant(final Date dateToConvert) {
    return dateToConvert.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
  }

  public static LocalDate convertStringToLocalDate(final String mask, final String date) {
    return convertToLocalDateViaInstant(parseDate(mask, date));
  }

  public static Date convertXmlGregorianCalendarToDate(XMLGregorianCalendar calendar) {
    if (calendar != null) {
      GregorianCalendar c = calendar.toGregorianCalendar();
      return c.getTime();
    }
    return null;
  }

  public static XMLGregorianCalendar convertDateToXmlGregorianCalendar(Date date) {

    if (date != null) {
      GregorianCalendar c = new GregorianCalendar();
      c.setTime(date);

      XMLGregorianCalendar date2 = null;

      try {
        date2 = DatatypeFactory.newInstance().newXMLGregorianCalendar(c);
      } catch (DatatypeConfigurationException e) {
        e.printStackTrace();
      }
      return date2;
    }
    return null;
  }

  // Verifica se é dia útil
  public Boolean isDayUtil(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) {
      return Boolean.FALSE;
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
      return Boolean.FALSE;
    }
    Feriado feriado = feriadoService.findById(calendar.getTime());

    if (feriado != null) {
      return Boolean.FALSE;
    }

    return Boolean.TRUE;
  }

  // Verifica qual a próxima quinta-feira da semana posterior
  public Date encontraQuintaFeiraOuProxDiaUtil(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);

    if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY) { // segunda
      calendar.setTime(addDayDate(calendar.getTime(), DEZ_DIAS_CORRIDOS));
      return getProximoDiaUtil(calendar.getTime());
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.TUESDAY) { // terça
      calendar.setTime(addDayDate(calendar.getTime(), NOVE_DIAS_CORRIDOS));
      return getProximoDiaUtil(calendar.getTime());
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.WEDNESDAY) { // quarta
      calendar.setTime(addDayDate(calendar.getTime(), OITO_DIAS_CORRIDOS));
      return getProximoDiaUtil(calendar.getTime());
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.THURSDAY) { // quinta
      calendar.setTime(addDayDate(calendar.getTime(), QUATORZE_DIAS_CORRIDOS));
      return getProximoDiaUtil(calendar.getTime());
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.FRIDAY) { // sexta
      calendar.setTime(addDayDate(calendar.getTime(), TREZE_DIAS_CORRIDOS));
      return getProximoDiaUtil(calendar.getTime());
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY) { // sabado
      calendar.setTime(addDayDate(calendar.getTime(), DOZE_DIAS_CORRIDOS));
      return getProximoDiaUtil(calendar.getTime());
    } else if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) { // domingo
      calendar.setTime(addDayDate(calendar.getTime(), ONZE_DIA_CORRIDO));
      return getProximoDiaUtil(calendar.getTime());
    }
    return null;
  }

  // Encontra próximo dia útil - recursivo
  public Date getProximoDiaUtil(Date data) {
    Date dataUtil = null;
    if (isDayUtil(data)) {
      dataUtil = data;
    } else {
      data = addDayDate(data, UM_DIA_UTIL);
      dataUtil = getProximoDiaUtil(data);
    }
    return dataUtil;
  }

  // Encontra próximo dia útil - recursivo
  public Date getProximoDiaUtilForcado(Date data) {
    Date dataUtil = null;
    data = addDayDate(data, UM_DIA_UTIL);
    if (isDayUtil(data)) {
      dataUtil = data;
    } else {
      dataUtil = getProximoDiaUtil(data);
    }
    return dataUtil;
  }

  // compara duas datas e retorna a quantidade de dias entre elas
  public static Integer differenceDates(Date date1, Date date2) {
    Calendar calendar1 = Calendar.getInstance();
    calendar1.setTime(date1);

    Calendar calendar2 = Calendar.getInstance();
    calendar2.setTime(date2);

    Integer diff =
        (int) ((calendar1.getTime().getTime() - calendar2.getTime().getTime()) / 86400000l);

    return diff;
  }

  // Encontra dia útil anterior - recursivo
  public Date getDiaUtilAnterior(Date data) {
    Date dataUtil = null;
    if (isDayUtil(data)) {
      dataUtil = data;
    } else {
      data = addDayDate(data, -1);
      dataUtil = getDiaUtilAnterior(data);
    }
    return dataUtil;
  }

  public static Calendar dateToCalendar(Date date) {
    Calendar calendar = Calendar.getInstance();
    calendar.setTime(date);
    return calendar;
  }

  public static Integer getUltimoDiaDoMes(Calendar c) {
    return c.getActualMaximum(Calendar.DAY_OF_MONTH);
  }

  public static Date dataComUltimaHora(Date dataFim) {
    LocalDateTime dataFimLDT = dateToLocalDateTime(dataFim);
    LocalDateTime dataFinal2359 = dataFimLDT.withHour(ConstantesB2B.MAX_HORA);
    dataFinal2359 = dataFinal2359.withMinute(ConstantesB2B.MAX_MINUTOS);
    dataFinal2359 = dataFinal2359.withSecond(ConstantesB2B.MAX_SEGUNDOS);
    return localDateTimeToDate(dataFinal2359);
  }

  public static Date dataComPrimeiraHora(Date dataInicio) {
    LocalDateTime dataInitLDT = dateToLocalDateTime(dataInicio);
    LocalDateTime dataInit0000 = dataInitLDT.withHour(ConstantesB2B.MIN_HORA);
    dataInit0000 = dataInit0000.withMinute(ConstantesB2B.MIN_MINUTOS);
    dataInit0000 = dataInit0000.withSecond(ConstantesB2B.MIN_SEGUNDOS);
    return localDateTimeToDate(dataInit0000);
  }

  public static LocalDateTime dataComUltimaHoraLocaDateTime(LocalDateTime dataFim) {
    LocalDateTime dataFinal2359 = dataFim.withHour(ConstantesB2B.MAX_HORA);
    dataFinal2359 = dataFinal2359.withMinute(ConstantesB2B.MAX_MINUTOS);
    dataFinal2359 = dataFinal2359.withSecond(ConstantesB2B.MAX_SEGUNDOS);
    return dataFinal2359;
  }

  public static LocalDateTime dataComPrimeiraHoraLocaDateTime(LocalDateTime dataInicio) {

    LocalDateTime dataInit0000 = dataInicio.withHour(ConstantesB2B.MIN_HORA);
    dataInit0000 = dataInit0000.withMinute(ConstantesB2B.MIN_MINUTOS);
    dataInit0000 = dataInit0000.withSecond(ConstantesB2B.MIN_SEGUNDOS);
    return dataInit0000;
  }

  public static Boolean isAnoAtual(int ano) {
    Calendar anoAtual = Calendar.getInstance();
    anoAtual.setTime(new Date());
    if (anoAtual.getWeekYear() == ano) return Boolean.TRUE;
    else return Boolean.FALSE;
  }

  public static String generateStringTodayNoTime() {
    String dataFormat = "dd/MM/yyyy";
    String dataAtual;
    java.util.Date agora = new Date();
    SimpleDateFormat formatacao = new SimpleDateFormat(dataFormat);
    dataAtual = formatacao.format(agora);

    return dataAtual;
  }

  public static String generateStringTimeNow() {
    String horaFormat = "HH:mm";
    String horaAtual;
    java.util.Date agora = new Date();
    SimpleDateFormat formatacao = new SimpleDateFormat(horaFormat);
    horaAtual = formatacao.format(agora);

    return horaAtual;
  }

  public static Date setDayofDate(Date date, int day) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.set(Calendar.DAY_OF_MONTH, day);

    return cal.getTime();
  }

  public static Date setMonthofDate(Date date, int month) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.set(Calendar.MONTH, month - 1);

    return cal.getTime();
  }

  public static Date setYearofDate(Date date, int year) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.set(Calendar.YEAR, year);

    return cal.getTime();
  }

  public static int getCurrentMonthInt() {
    Date date = new Date();
    LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    int month = localDate.getMonthValue();

    return month;
  }

  public static int getDayFromDate(Date date) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    return cal.get(Calendar.DAY_OF_MONTH);
  }

  public static int getMonthFromDate(Date date) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    return cal.get(Calendar.MONTH);
  }

  public static Date getDateSemTime(Date dateComTime) {
    if (dateComTime != null) {
      Calendar calendar = Calendar.getInstance();
      calendar.setTime(dateComTime);
      calendar.set(Calendar.HOUR_OF_DAY, 0);
      calendar.set(Calendar.MINUTE, 0);
      calendar.set(Calendar.SECOND, 0);
      calendar.set(Calendar.MILLISECOND, 0);
      return calendar.getTime();
    }
    return null;
  }

  public static String localDateTimeFormat(String mask, LocalDateTime date) {
    try {
      if (date == null) return null;
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern(mask);
      return date.format(formatter);
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado." + e.getMessage());
    }
  }

  public static LocalTime getHoraInicioDia() {
    return LocalTime.of(0, 0, 0);
  }

  public static LocalTime getHoraFimDia() {
    return LocalTime.of(23, 59, 59);
  }

  public static LocalDate getDiaAnterior() {
    return LocalDate.now().minusDays(1);
  }

  public static Date getDiaAnteriorComHorarioAsDate(LocalTime horario) {
    return DateUtil.localDateTimeToDate(LocalDateTime.of(getDiaAnterior(), horario));
  }

  public static String convertLocalDateToStringDiaMesAno(LocalDateTime data) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
    return data.format(formatter);
  }

  public static String convertStringLocalDateToDataFormatoBR(String data) {
    String saida = null;
    String[] date, list = null;
    try {
      list = data.split("T");
      date = list[0].split("-");
      saida = date[2] + "/" + date[1] + "/" + date[0];
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado.");
    }
    return saida;
  }

  public static String convertStringLocalDateToDataHoraFormatoBR(String data) {
    String saida = null;
    String[] date, list, hora = null;
    try {
      list = data.split("T");
      date = list[0].split("-");
      hora = list[1].split("\\.");
      saida = date[2] + "/" + date[1] + "/" + date[0] + " " + hora[0];
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado.");
    }
    return saida;
  }

  public static Date convertStringToData(String date) {
    Date saida = null;
    String[] dataStringList;
    String novaSaida;
    String dataString;

    try {
      dataString = date.substring(0, 10);
      dataStringList = dataString.split("/");
      dataStringList = dataString.split("-");
      novaSaida = dataStringList[0] + "-" + dataStringList[1] + "-" + dataStringList[2];
      saida = parseDate("yyyy-MM-dd", novaSaida);
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado.");
    }
    return saida;
  }

  public static String convertStringDateToDataHoraFormatoBR(String data) {
    String saida = null;
    String[] date, list, hora = null;
    try {
      list = data.split(" ");
      date = list[0].split("-");
      hora = list[1].split("\\.");
      saida = date[2] + "/" + date[1] + "/" + date[0] + " " + hora[0];
    } catch (Exception e) {
      log.error(e);
      throw new NotInstrumentedException("Formato não suportado.");
    }
    return saida;
  }
}
