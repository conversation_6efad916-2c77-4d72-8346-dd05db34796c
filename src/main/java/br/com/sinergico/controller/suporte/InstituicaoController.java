package br.com.sinergico.controller.suporte;

import br.com.entity.cadastral.CampanhaIndiqueGanhe;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.BuscaGenerica;
import br.com.json.bean.cadastral.CadastrarCampanhaIndiqueGanhe;
import br.com.json.bean.cadastral.CampanhaIndiqueGanheResponse;
import br.com.json.bean.suporte.CadastrarInstituicao;
import br.com.json.bean.suporte.ConfiguracaoInstituicaoDTO;
import br.com.json.bean.suporte.Instituicao;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.PortadorAuthentication;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.security.UserAuthentication;
import br.com.sinergico.service.cadastral.CampanhaIndiqueGanheService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.HierarquiaInstituicaoUsuarioVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/api/instituicao")
public class InstituicaoController extends UtilController {

  public static final Integer STATUS_ATIVO = 1;
  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private HierarquiaInstituicaoService service;

  @Autowired private CampanhaIndiqueGanheService campanhaIndiqueGanheSevice;

  @ApiOperation(value = "Serviço responsável por Alterar uma instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_INSTITUICAO"})
  public ResponseEntity<HashMap<String, Object>> update(
      @RequestBody @Valid CadastrarInstituicao model,
      @PathVariable Integer id,
      BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na validação da Alteração da Instituição.", result);
    }
    HierarquiaInstituicao inst = service.update(getAuthenticatedUser(request, em), id, model);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Instituição Alterada com sucesso");
    map.put("idInstituicao", inst.getId());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por cadastrar uma instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_INSTITUICAO"})
  public ResponseEntity<HashMap<String, Object>> create(
      @RequestBody @Valid CadastrarInstituicao model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Falha na validação do cadastro da Instituicao", result);
    }

    HierarquiaInstituicao inst = service.create(getAuthenticatedUser(request, em), model, result);
    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Instituição cadastrada com sucesso");
    map.put("idInstituicao", inst.getId().getIdInstituicao());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Buscar todas as Instituições Cadastradas",
      response = Instituicao.class,
      responseContainer = "List",
      notes = "Retorna a instituição do usuário cadastrado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<Instituicao> getAll() {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());
    List<Instituicao> model = new ArrayList<>();
    List<HierarquiaInstituicao> listIntituicao = service.findAll();
    listIntituicao.forEach(
        it -> {
          model.add(new Instituicao(it));
        });

    Collections.sort(model);

    return model;
  }

  @ApiOperation(
      value = "Buscar todas as Instituições Cadastradas pela hierarquia do usuario e com paginação",
      response = Instituicao.class,
      responseContainer = "List",
      notes = "")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/get/{first}/{max}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<Instituicao> getAllByFiltros(
      @PathVariable Integer first,
      @PathVariable Integer max,
      @RequestBody @Valid BuscaGenerica pegaTermo) {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());
    List<Instituicao> model = new ArrayList<>();
    List<HierarquiaInstituicao> listIntituicao = service.findAll(pegaTermo.getTermo(), first, max);

    listIntituicao.forEach(
        it -> {
          model.add(new Instituicao(it));
        });

    return model;
  }

  @ApiOperation(
      value = "Contar todas as Instituições Cadastradas pela hierarquia do usuario e com paginação",
      response = Instituicao.class,
      responseContainer = "List",
      notes = "")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/count",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public Integer countAllByFiltros(@RequestBody @Valid BuscaGenerica model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());
    return service.countBy(model.getTermo());
  }

  @ApiOperation(value = "Salva a imagem da logo")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(method = RequestMethod.POST, path = "/imagem/{idInstituicao}")
  @Secured("ROLE_SALVAR_IMAGEM_LOGO")
  public ResponseEntity<HttpStatus> createByFile(
      @RequestParam("file") MultipartFile file, @PathVariable Integer idInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);

    HashMap<String, Object> map = new HashMap<>();

    try {
      service.salvarLogo(
          file.getInputStream(),
          file.getOriginalFilename(),
          user.getIdProcessadora(),
          idInstituicao,
          user);
    } catch (IOException e) {
      map.put("msg", "Não foi possível adicionar a imagem da logo!");
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    map.put("msg", "Imagem adiconada com sucesso");
    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(value = "Abre a imagem da logo", notes = "Abre a imagem da logo")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/abrir/imagem/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Secured("ROLE_ABRIR_IMAGEM_LOGO_BY_INSTITUICAO")
  public ResponseEntity<InputStreamResource> getImagemFisico(@PathVariable Integer idInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);

    InputStream input = service.mostrarImagem(user.getIdProcessadora(), idInstituicao);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Abre a imagem da logo pelo usuário logado",
      notes = "Abre a imagem da logo pelo usuário logado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/abrir/imagem-logo/usuario",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public ResponseEntity<InputStreamResource> getImagemLogoUsuario() {

    SecurityUser user = getAuthenticatedUser(request, em);

    InputStream input = service.recuperarImagemLogoByUser(user);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(value = "Abre a imagem da logo pelo usuário estabelecimento logado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  @RequestMapping(
      value = "/acquirer/abrir/imagem-logo/usuario",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public ResponseEntity<InputStreamResource> getImagemLogoUsuarioEstabelecimento() {

    SecurityUserEstabelecimento user = getAuthenticatedUserEstabelecimento(request, em);

    InputStream input = service.recuperarImagemLogoByUserEstabelecimento(user);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar todas as Instituições de uma processadora",
      response = Instituicao.class,
      responseContainer = "list")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/get-by-processadora",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<Instituicao> getAllInstByProcessadora() {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (user.getIdInstituicao() != null) {
      return new ArrayList<Instituicao>();
    }

    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());
    List<Instituicao> model = new ArrayList<>();
    List<HierarquiaInstituicao> listIntituicao = service.findAll();
    listIntituicao.forEach(
        it -> {
          if (STATUS_ATIVO.equals(it.getIdStatus())) {
            model.add(new Instituicao(it));
          }
        });

    Collections.sort(model);

    return model;
  }

  @ApiOperation(
      value = "Buscar a Instituição de dado Ponto de relacionamento pela hierarquia do usuario",
      response = Instituicao.class,
      notes = "")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/get/instituicao",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Instituicao> getByUsuarioLogado() {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    Instituicao intituicao = null;

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null) {
      if (authentication instanceof UserAuthentication) {
        UserAuthentication userAuthentication = (UserAuthentication) authentication;
        SecurityUser securityUser = userAuthentication.getDetails();
        SecHierarquia.checkHierarquia(securityUser, securityUser.getHierarquiaType());
        intituicao =
            service.findByIdProcessadoraAndIdInstituicaoWithContatos(
                securityUser.getIdProcessadora(), securityUser.getIdInstituicao());
      } else if (authentication instanceof PortadorAuthentication) {
        PortadorAuthentication portadorAuthentication = (PortadorAuthentication) authentication;
        SecurityUserPortador securityUserPortador = portadorAuthentication.getDetails();
        intituicao =
            service.findByIdProcessadoraAndIdInstituicaoWithContatos(
                securityUserPortador.getIdProcessadora(), securityUserPortador.getIdInstituicao());
      }
    }

    return new ResponseEntity<Instituicao>(intituicao, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar a Instituição de dado uma processadora e uma instutuicao",
      response = Instituicao.class,
      notes = "")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/get/instituicao/{processadora}/{instituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Instituicao> getByUsuario(
      @PathVariable("processadora") Integer processadora,
      @PathVariable("instituicao") Integer instituicaoId) {
    Instituicao instituicao =
        service.findInstituicaoByIdProcessadoraAndIdInstituicao(processadora, instituicaoId);
    return new ResponseEntity<Instituicao>(instituicao, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar campanhas de uma instituição",
      response = CampanhaIndiqueGanhe.class,
      responseContainer = "list")
  @RequestMapping(
      value = "/buscar-campanhas-instituicao/{idInstituicao}",
      method = {RequestMethod.GET},
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<?> getCampanhasInstituicao(
      @PathVariable Integer idInstituicao, HttpServletRequest request) {

    List<CampanhaIndiqueGanheResponse> resposta;

    resposta = campanhaIndiqueGanheSevice.getCampanhasInstituicao(idInstituicao);

    return new ResponseEntity<>(resposta, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por criar uma nova campanha")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cadastrar-campanha",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MODIFICAR_CAMPANHA"})
  public ResponseEntity<Map<String, Object>> create(
      @RequestBody @Valid CadastrarCampanhaIndiqueGanhe model) throws Exception {

    SecurityUser user = getAuthenticatedUser(request, em);

    campanhaIndiqueGanheSevice.cadastrarCampanha(user, model);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Campanha cadastrada com sucesso.");

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por Inativar um campanha")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/inativar-campanha/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MODIFICAR_CAMPANHA"})
  public ResponseEntity<Map<String, Object>> inativarCampanha(@PathVariable Integer id) {

    SecurityUser user = getAuthenticatedUser(request, em);

    campanhaIndiqueGanheSevice.inativarCampanha(id, user.getIdUsuario());
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Campanha inativada com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por Ativar um campanha")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/ativar-campanha/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MODIFICAR_CAMPANHA"})
  public ResponseEntity<Map<String, Object>> ativarCampanha(@PathVariable Integer id) {

    SecurityUser user = getAuthenticatedUser(request, em);

    campanhaIndiqueGanheSevice.ativarCampanha(id, user.getIdUsuario());
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Campanha ativada com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar todas as Instituições com funcionalidade de notificação habilitada")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/notificacao-habilitada",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<Instituicao> getAllByNotificacaoHabilitada() {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());
    List<Instituicao> model = new ArrayList<>();
    List<HierarquiaInstituicao> listIntituicao = service.findAllByNotificacaoHabilitada();
    listIntituicao.forEach(
        it -> {
          model.add(new Instituicao(it));
        });

    Collections.sort(model);

    return model;
  }

  @ApiOperation(
      value =
          "Realiza busca de dados da instituição do usuario logado de acordo com sua hierarquia")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-dados-instituicao/{idUsuario}",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_MODIFICAR_CAMPANHA"})
  public HierarquiaInstituicaoUsuarioVO buscarDadosInstituicaoUsuarioLogadoByHierarquia(
      @PathVariable Integer idUsuario) {
    return service.getDadosInstituicaoUsuarioByHierarquia(idUsuario);
  }

  @ApiOperation(value = "Realiza busca de dados da instituição que possuem CAF ativo")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/dados-instituicao-caf",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_MODIFICAR_CAMPANHA"})
  public List<HierarquiaInstituicaoUsuarioVO> buscarDadosInstituicaoCafAtivo() {
    return service.buscarDadosInstituicaoCafAtivo();
  }

  @ApiOperation(value = "Serviço responsável por alterar o status de uma instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-status/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_INSTITUICAO"})
  public ResponseEntity<HashMap<String, Object>> alterarStatusInstituicao(
      @PathVariable Integer id) {

    SecurityUser user = getAuthenticatedUser(request, em);
    HierarquiaInstituicao inst = service.alterarStatus(user, id);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Status da Instituição alterado com sucesso");
    map.put("idInstituicao", inst.getId().getIdInstituicao());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por alterar status de permitir processamento de lançamentos")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/permite-processamento-lancamentos/{idInstituicao}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_PERMITIR_PROCESSAMENTO_CARGA"})
  public ResponseEntity<HashMap<String, Object>> updatePermiteProcessamentoLancamentos(
      @PathVariable Integer idInstituicao) {
    HierarquiaInstituicao inst =
        service.updatePermiteProcessamentoLancamentos(
            getAuthenticatedUser(request, em), idInstituicao);
    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Instituição Alterada com sucesso");
    map.put("idInstituicao", inst.getId());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por alterar a configuracao da instituicao",
      response = HierarquiaInstituicao.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "atualizar/configuracao-instituicao",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONFIGURACAO_INSTITUCIONAL_ALTERACAO_CONFIGURACAO"})
  public ResponseEntity<HierarquiaInstituicao> configuracaoInstituicaoAtualizar(
      @RequestBody ConfiguracaoInstituicaoDTO configuracaoInstituicaoDTO) {

    SecurityUser user = getAuthenticatedUser(request, em);

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user,
        Constantes.ID_PROCESSADORA_ITS_PAY,
        configuracaoInstituicaoDTO.getIdHierarquiaInstitucao(),
        null,
        null,
        null);

    HierarquiaInstituicao inst =
        service.configuracaoInstituicaoAtualizar(configuracaoInstituicaoDTO);

    return new ResponseEntity<>(inst, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por alterar a configuracao da instituicao",
      response = HierarquiaInstituicao.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "buscar/configuracao-instituicao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONFIGURACAO_INSTITUCIONAL"})
  public ResponseEntity<HierarquiaInstituicao> configuracaoInstituicaoBuscar(
      @RequestBody ConfiguracaoInstituicaoDTO configuracaoInstituicaoDTO) {

    SecurityUser user = getAuthenticatedUser(request, em);

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user,
        Constantes.ID_PROCESSADORA_ITS_PAY,
        configuracaoInstituicaoDTO.getIdHierarquiaInstitucao(),
        null,
        null,
        null);

    HierarquiaInstituicao inst = service.configuracaoInstituicaoBuscar(configuracaoInstituicaoDTO);

    return new ResponseEntity<>(inst, HttpStatus.OK);
  }
}
