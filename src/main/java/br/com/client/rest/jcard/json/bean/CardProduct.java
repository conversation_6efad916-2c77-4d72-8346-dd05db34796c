package br.com.client.rest.jcard.json.bean;

import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

@JsonInclude(Include.NON_NULL)
public class CardProduct implements Serializable {

  private static final long serialVersionUID = -4615006214710490353L;

  private String endDate;
  private String binExtended;
  private String serviceCode;
  private String bin;
  private Integer binLength;
  private String binExtendedVirtual;
  private Boolean active;
  private Boolean moto;
  private Boolean tips;
  private String issuer;
  private Boolean smart;
  private Boolean randomCardNumber;
  private String paymentScheme;
  private Integer cardNumberLength;
  private Boolean pos;
  private String binVirtual;
  private Boolean ecommerce;
  private Integer pinLength;
  private String name;
  private Boolean anonymous;
  private String externalAccount;
  private Long id;
  private Boolean atm;
  private String startDate;
  private boolean additionalCardLimited;
  private Double additionalCardLimitPercentage;
  private BigDecimal percentualLimiteSaque;
  private TipoProdutoEnum productType;

  public String getEndDate() {
    return endDate;
  }

  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }

  public String getBinExtended() {
    return binExtended;
  }

  public void setBinExtended(String binExtended) {
    this.binExtended = binExtended;
  }

  public String getServiceCode() {
    return serviceCode;
  }

  public void setServiceCode(String serviceCode) {
    this.serviceCode = serviceCode;
  }

  public String getBin() {
    return bin;
  }

  public void setBin(String bin) {
    this.bin = bin;
  }

  public Integer getBinLength() {
    return binLength;
  }

  public void setBinLength(Integer binLength) {
    this.binLength = binLength;
  }

  public String getBinExtendedVirtual() {
    return binExtendedVirtual;
  }

  public void setBinExtendedVirtual(String binExtendedVirtual) {
    this.binExtendedVirtual = binExtendedVirtual;
  }

  public Boolean getActive() {
    return active;
  }

  public void setActive(Boolean active) {
    this.active = active;
  }

  public Boolean getMoto() {
    return moto;
  }

  public void setMoto(Boolean moto) {
    this.moto = moto;
  }

  public Boolean getTips() {
    return tips;
  }

  public void setTips(Boolean tips) {
    this.tips = tips;
  }

  public String getIssuer() {
    return issuer;
  }

  public void setIssuer(String issuer) {
    this.issuer = issuer;
  }

  public Boolean getSmart() {
    return smart;
  }

  public void setSmart(Boolean smart) {
    this.smart = smart;
  }

  public Boolean getRandomCardNumber() {
    return randomCardNumber;
  }

  public void setRandomCardNumber(Boolean randomCardNumber) {
    this.randomCardNumber = randomCardNumber;
  }

  public String getPaymentScheme() {
    return paymentScheme;
  }

  public void setPaymentScheme(String paymentScheme) {
    this.paymentScheme = paymentScheme;
  }

  public Integer getCardNumberLength() {
    return cardNumberLength;
  }

  public void setCardNumberLength(Integer cardNumberLength) {
    this.cardNumberLength = cardNumberLength;
  }

  public Boolean getPos() {
    return pos;
  }

  public void setPos(Boolean pos) {
    this.pos = pos;
  }

  public String getBinVirtual() {
    return binVirtual;
  }

  public void setBinVirtual(String binVirtual) {
    this.binVirtual = binVirtual;
  }

  public Boolean getEcommerce() {
    return ecommerce;
  }

  public void setEcommerce(Boolean ecommerce) {
    this.ecommerce = ecommerce;
  }

  public Integer getPinLength() {
    return pinLength;
  }

  public void setPinLength(Integer pinLength) {
    this.pinLength = pinLength;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Boolean getAnonymous() {
    return anonymous;
  }

  public void setAnonymous(Boolean anonymous) {
    this.anonymous = anonymous;
  }

  public String getExternalAccount() {
    return externalAccount;
  }

  public void setExternalAccount(String externalAccount) {
    this.externalAccount = externalAccount;
  }

  public Long getId() {
    return id;
  }

  public void setId(Long id) {
    this.id = id;
  }

  public Boolean getAtm() {
    return atm;
  }

  public void setAtm(Boolean atm) {
    this.atm = atm;
  }

  public String getStartDate() {
    return startDate;
  }

  public void setStartDate(String startDate) {
    this.startDate = startDate;
  }

  public boolean isAdditionalCardLimited() {
    return additionalCardLimited;
  }

  public void setAdditionalCardLimited(boolean additionalCardLimited) {
    this.additionalCardLimited = additionalCardLimited;
  }

  public Double getAdditionalCardLimitPercentage() {
    return additionalCardLimitPercentage;
  }

  public void setAdditionalCardLimitPercentage(Double additionalCardLimitPercentage) {
    this.additionalCardLimitPercentage = additionalCardLimitPercentage;
  }

  public BigDecimal getPercentualLimiteSaque() {
    return percentualLimiteSaque;
  }

  public void setPercentualLimiteSaque(BigDecimal percentualLimiteSaque) {
    this.percentualLimiteSaque = percentualLimiteSaque;
  }

  public TipoProdutoEnum getProductType() {
    return productType;
  }

  public void setProductType(TipoProdutoEnum productType) {
    this.productType = productType;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }
}
