package br.com.sinergico.service.cadastral;

import br.com.entity.adq.Estabelecimento;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.suporte.TipoEndereco;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.EnderecoPessoaUpdatePorContaRequest;
import br.com.json.bean.cadastral.EnderecoPessoaUpdateRequest;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.repository.cadastral.EnderecoPessoaRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.suporte.TipoEnderecoService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Util;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
public class EnderecoPessoaService extends GenericService<EnderecoPessoa, Long> {

  private static final Integer ATIVO = 1;
  private static final Integer INATIVO = 0;
  private static final Integer PESSOA_FISICA = 1;
  private static final Integer PESSOA_JURIDICA = 2;
  private static final Integer AMBAS_PESSOAS = 0;
  private static final int MIN_LENGHT = 0;
  private EnderecoPessoaRepository enderecoRepository;

  @Lazy @Autowired private PessoaService pessoaService;

  @Autowired private TipoEnderecoService tipoEnderecoService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired
  public EnderecoPessoaService(EnderecoPessoaRepository repo) {
    super(repo);
    enderecoRepository = repo;
  }

  /**
   * Método responsável por cadastrar um ou mais endereços de uma pessoa
   *
   * @param idUsuario
   * @param enderecos
   * @return
   */
  public List<EnderecoPessoa> cadastrarEnderecosPessoa(
      Integer idUsuario, EnderecoPessoa... enderecos) {

    List<EnderecoPessoa> enderecosSaveds = new ArrayList<>();

    if (enderecos != null && enderecos.length > MIN_LENGHT) {

      for (EnderecoPessoa enderecoPessoa : enderecos) {

        Pessoa pessoa = pessoaService.findById(enderecoPessoa.getIdPessoa());
        if (pessoa == null) {
          throw new GenericServiceException(
              "Pessoa não encontrada para cadastro de endereço.PessoaId= "
                  + enderecoPessoa.getIdPessoa());
        }

        List<TipoEndereco> tiposEnderecos = null;

        // ver cadastro de PF e PJ conforme tipo de endereco e
        // aplicabilidade
        if (!(pessoa.getIdTipoPessoa().equals(PESSOA_FISICA)
            || pessoa.getIdTipoPessoa().equals(PESSOA_JURIDICA))) {
          throw new GenericServiceException(
              "Tipo de pessoa não suportado.IdTipoPessoa: " + pessoa.getIdTipoPessoa());
        }
        List<Integer> tipoPessoas = new ArrayList<>();
        tipoPessoas.add(pessoa.getIdTipoPessoa());
        tipoPessoas.add(AMBAS_PESSOAS);

        tiposEnderecos = tipoEnderecoService.findByAplicabilidadeIn(tipoPessoas);

        if (tiposEnderecos == null) {
          throw new GenericServiceException("Nenhum Tipo de Endereço encontrado.");
        }
        TipoEndereco tipoEndereco = new TipoEndereco();
        tipoEndereco.setIdTipoEndereco(enderecoPessoa.getIdTipoEndereco());

        if (!tiposEnderecos.contains(tipoEndereco)) {
          throw new GenericServiceException(
              "Tipo de Endereço Não permitido para o tipo de Pessoa.");
        }

        if (!Util.isNotNull(enderecoPessoa.getLogradouro())) {
          throw new GenericServiceException(
              "Logradouro não pode ser vazio, Consulte o endereço cadastrado.");
        }

        EnderecoPessoa enderecoBuscado =
            enderecoRepository.findOneByIdPessoaAndIdTipoEnderecoAndStatus(
                enderecoPessoa.getIdPessoa(), enderecoPessoa.getIdTipoEndereco(), ATIVO);

        // se encontrar um endereco ativo, desativa
        if (enderecoBuscado != null) {
          enderecoBuscado.setStatus(INATIVO);
          enderecoBuscado.setIdUsuarioManutencao(idUsuario);
          enderecoBuscado.setDataHoraStatus(LocalDateTime.now());
          save(enderecoBuscado);
        }
        enderecoPessoa.setStatus(ATIVO);
        enderecoPessoa.setDataHoraStatus(LocalDateTime.now());
        enderecoPessoa.setDtHrInclusao(new Date());
        enderecosSaveds.add(save(enderecoPessoa));
      }
    }
    return enderecosSaveds;
  }

  public void ativarEndereco(Long idEndereco) {

    alterarStatusEndereco(idEndereco, INATIVO);
  }

  public void confirmaEndereco(Long idEndereco) {
    EnderecoPessoa enderecoPessoa = findById(idEndereco);
    enderecoPessoa.setDtHrConfirmacao(new Date());
    enderecoRepository.save(enderecoPessoa);
  }

  public void desativarEndereco(Long idEndereco) {

    alterarStatusEndereco(idEndereco, ATIVO);
  }

  public void alterarStatusEndereco(Long idEndereco, Integer status) {
    EnderecoPessoa enderecoBuscado = findById(idEndereco);

    if (enderecoBuscado == null) {
      throw new GenericServiceException("Endereço não encontrado. idEndereco: " + idEndereco);
    }
    enderecoBuscado.setStatus(status);
    enderecoBuscado.setDataHoraStatus(LocalDateTime.now());
    enderecoRepository.save(enderecoBuscado);
  }

  public void updatePorConta(
      Long idConta, Integer idTipoEndereco, EnderecoPessoaUpdatePorContaRequest model) {

    EnderecoPessoa endereco = new EnderecoPessoa();
    BeanUtils.copyProperties(model, endereco, getNullPropertyNames(model));

    EnderecoPessoa enderecoBuscado =
        enderecoRepository.findEnderecoPessoaByIdContaAndTipoEndereco(idConta, idTipoEndereco);
    if (enderecoBuscado == null) {
      throw new GenericServiceException("Endereço não encontrado para a conta: " + idConta);
    }

    BeanUtils.copyProperties(endereco, enderecoBuscado, getNullPropertyNames(endereco));
    enderecoRepository.save(enderecoBuscado);
  }

  private EnderecoPessoa buscarEValidarPessoaEndereco(Long idEndereco, Long idPessoa) {
    EnderecoPessoa enderecoBuscado = findById(idEndereco);
    if (enderecoBuscado == null) {
      throw new GenericServiceException("Endereço não encontrado. idEndereco: " + idEndereco);
    }

    if (!enderecoBuscado.getIdPessoa().equals(idPessoa)) {
      throw new GenericServiceException(
          "IdPessoa não pode ser modificado. idEndereco: " + idEndereco);
    }
    return enderecoBuscado;
  }

  public boolean atualizarEnderecoUserEstabelecimento(
      EnderecoPessoaUpdateRequest enderecoPessoaUpdateRequest,
      SecurityUserEstabelecimento userEstabelecimento) {

    EnderecoPessoa enderecoBuscado =
        buscarEValidarPessoaEndereco(
            enderecoPessoaUpdateRequest.getIdEndereco(), enderecoPessoaUpdateRequest.getIdPessoa());

    Estabelecimento estabelecimento = userEstabelecimento.getEstabelecimento();

    travaServicosService.travaServicos(
        estabelecimento.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    UtilController.checkHierarquiaUsuarioEstabelecimentoLogado(
        userEstabelecimento, enderecoBuscado.getPessoa());

    BeanUtils.copyProperties(
        enderecoPessoaUpdateRequest,
        enderecoBuscado,
        getNullPropertyNames(enderecoPessoaUpdateRequest));
    if (enderecoPessoaUpdateRequest.isConfirmarEndereco()) {
      enderecoBuscado.setDtHrConfirmacao(new Date());
      enderecoBuscado.setOrigemConfirmacao(enderecoBuscado.getOrigemConfirmacao());
    }
    enderecoRepository.save(enderecoBuscado);
    return true;
  }

  public boolean atualizarEnderecoUser(
      EnderecoPessoaUpdateRequest enderecoPessoaUpdateRequest, SecurityUser user) {

    EnderecoPessoa enderecoBuscado =
        buscarEValidarPessoaEndereco(
            enderecoPessoaUpdateRequest.getIdEndereco(), enderecoPessoaUpdateRequest.getIdPessoa());

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user, enderecoBuscado.getPessoa());

    BeanUtils.copyProperties(
        enderecoPessoaUpdateRequest,
        enderecoBuscado,
        getNullPropertyNames(enderecoPessoaUpdateRequest));
    if (enderecoPessoaUpdateRequest.isConfirmarEndereco()) {
      enderecoBuscado.setDtHrConfirmacao(new Date());
      enderecoBuscado.setOrigemConfirmacao(enderecoBuscado.getOrigemConfirmacao());
    }
    enderecoRepository.save(enderecoBuscado);
    return true;
  }

  public boolean atualizarEnderecoPortador(
      EnderecoPessoaUpdateRequest enderecoPessoaUpdateRequest, SecurityUserPortador userPortador) {

    EnderecoPessoa enderecoBuscado =
        buscarEValidarPessoaEndereco(
            enderecoPessoaUpdateRequest.getIdEndereco(), enderecoPessoaUpdateRequest.getIdPessoa());

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    List<ContaPagamento> contaPagamentoList =
        contaPagamentoService.findContasByIdEndereco(enderecoBuscado.getIdEndereco());
    contaPagamentoService.validaListContaPeloRequestEPortador(contaPagamentoList, userPortador);

    BeanUtils.copyProperties(
        enderecoPessoaUpdateRequest,
        enderecoBuscado,
        getNullPropertyNames(enderecoPessoaUpdateRequest));
    if (enderecoPessoaUpdateRequest.isConfirmarEndereco()) {
      enderecoBuscado.setDtHrConfirmacao(new Date());
      enderecoBuscado.setOrigemConfirmacao(enderecoBuscado.getOrigemConfirmacao());
    }
    enderecoRepository.save(enderecoBuscado);
    return true;
  }

  public boolean confirmaEnderecoUser(Long idEndereco, SecurityUser user) {

    EnderecoPessoa enderecoBuscado = findById(idEndereco);
    if (enderecoBuscado == null) {
      throw new GenericServiceException("Endereço não encontrado. idEndereco: " + idEndereco);
    }

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user, enderecoBuscado.getPessoa());

    enderecoBuscado.setDtHrConfirmacao(new Date());
    enderecoBuscado.setOrigemConfirmacao(enderecoBuscado.getOrigemConfirmacao());
    enderecoRepository.save(enderecoBuscado);
    return true;
  }

  public boolean confirmarEnderecoPortador(Long idEndereco, SecurityUserPortador userPortador) {

    EnderecoPessoa enderecoBuscado = findById(idEndereco);
    if (enderecoBuscado == null) {
      throw new GenericServiceException("Endereço não encontrado. idEndereco: " + idEndereco);
    }

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    List<ContaPagamento> contaPagamentoList =
        contaPagamentoService.findContasByIdEndereco(enderecoBuscado.getIdEndereco());
    contaPagamentoService.validaListContaPeloRequestEPortador(contaPagamentoList, userPortador);

    enderecoBuscado.setDtHrConfirmacao(new Date());
    enderecoBuscado.setOrigemConfirmacao(enderecoBuscado.getOrigemConfirmacao());
    enderecoRepository.save(enderecoBuscado);
    return true;
  }

  public List<EnderecoPessoa> findByIdPessoaAndStatus(Long idPessoa, Integer status) {
    return enderecoRepository.findByIdPessoaAndStatus(idPessoa, status);
  }

  public List<EnderecoPessoa> findByDocumentoAndStatus(
      String documento,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idTipoPessoa,
      Integer status) {

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            idProcessadora, idInstituicao, documento, idTipoPessoa);
    if (pessoa == null) {
      throw new GenericServiceException(
          "Pessoa não encontrada para busca de endereço.Documento= "
              + documento
              + ", idInstituicao= "
              + idInstituicao
              + " ,idTipoPessoa= "
              + idTipoPessoa);
    }
    return enderecoRepository.findByIdPessoaAndStatus(pessoa.getIdPessoa(), status);
  }

  public List<EnderecoPessoa> findByIdPessoaIn(List<Long> idsPessoas) {
    return enderecoRepository.findByIdPessoaIn(idsPessoas);
  }

  public List<EnderecoPessoa> findByIdPessoa(Long idPessoa) {
    return enderecoRepository.findByIdPessoa(idPessoa);
  }

  public EnderecoPessoa findOneByIdPessoaAndIdTipoEnderecoAndStatus(
      Long idPessoa, Integer idTipoEndereco, Integer status) {
    return enderecoRepository.findOneByIdPessoaAndIdTipoEnderecoAndStatus(
        idPessoa, idTipoEndereco, status);
  }

  public List<EnderecoPessoa> consultarEnderecoPessoa(PortadorLogin userPortador) {
    List<Pessoa> pessoasPortador = pessoaService.getPessoasPortador(userPortador);
    List<Long> listaIdPessoa =
        pessoasPortador.stream().map(Pessoa::getIdPessoa).collect(Collectors.toList());
    List<EnderecoPessoa> enderecoPessoaList =
        enderecoRepository.findByIdPessoaInAndStatus(listaIdPessoa, 1);
    return new ArrayList<>(
        enderecoPessoaList.stream()
            .collect(
                Collectors.toMap(
                    endereco ->
                        Arrays.asList(
                            endereco.getTipoEndereco(),
                            endereco.getCep(),
                            endereco.getLogradouro(),
                            endereco.getNumero(),
                            endereco.getComplemento(),
                            endereco.getBairro(),
                            endereco.getCidade(),
                            endereco.getUf()),
                    endereco -> endereco,
                    (enderecoExistente, enderecoDuplicado) -> enderecoExistente))
            .values());
  }
}
