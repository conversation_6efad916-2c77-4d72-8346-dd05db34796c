package br.com.sinergico.controller.suporte;

import br.com.BaseControllerTest;
import br.com.json.bean.cadastral.BuscaGenericaFiltro;
import br.com.json.bean.suporte.AcessoUsuarioDTO;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class UsuarioControllerTest extends BaseControllerTest {

  @Mock private AcessoUsuarioService mockAcessoUsuarioService;

  @InjectMocks private UsuarioController usuarioController;

  @Override
  public void setup() {
    super.setup();
  }

  @Test
  void findByDynamicFiltersNulo() {

    final BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(null, null, null, null, null, null, null, null, null, null);

    Page<AcessoUsuarioDTO> acessoUsuario =
        mockAcessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    final ResponseEntity<Page<AcessoUsuarioDTO>> buscaGenericaFiltroResponseEntity =
        new ResponseEntity<>(acessoUsuario, HttpStatus.OK);

    buscaGenericaFiltroResponseEntity.hasBody();
  }

  @Test
  void findByDynamicFiltersNaoNulo() {

    final BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(null, null, null, null, null, null, null, null, null, null);

    Page<AcessoUsuarioDTO> acessoUsuario =
        mockAcessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    final ResponseEntity<Page<AcessoUsuarioDTO>> buscaGenericaFiltroResponseEntity =
        new ResponseEntity<>(acessoUsuario, HttpStatus.OK);
  }
}
