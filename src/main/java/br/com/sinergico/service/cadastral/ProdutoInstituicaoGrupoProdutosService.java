package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.ProdutoInstituicaoGrupoProdutos;
import br.com.entity.cadastral.ProdutoInstituicaoGrupoProdutosId;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoGrupoProdutosRepository;
import br.com.sinergico.service.GenericService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ProdutoInstituicaoGrupoProdutosService
    extends GenericService<ProdutoInstituicaoGrupoProdutos, ProdutoInstituicaoGrupoProdutosId> {

  public static final int MAXIMO_PRODUTOS_VINCULADOS_A_GRUPOS = 20;

  @Autowired ProdutoInstituicaoGrupoProdutosRepository produtoInstituicaoGrupoProdutosRepository;

  @Autowired GrupoProdutosService grupoProdutosService;

  @Autowired ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired
  public ProdutoInstituicaoGrupoProdutosService(ProdutoInstituicaoGrupoProdutosRepository repo) {
    super(repo);
    produtoInstituicaoGrupoProdutosRepository = repo;
  }

  public void inserirProdutoEmGrupoProdutos(Integer idProdInstituicao, Long idGrupo) {

    List<ProdutoInstituicaoGrupoProdutos> listaGruposDoProduto =
        produtoInstituicaoGrupoProdutosRepository
            .findByProdutoInstituicaoGrupoProdutosId_IdProdutoInstituicao(idProdInstituicao);

    if (!listaGruposDoProduto.isEmpty()) {
      throw new GenericServiceException("Produto já vinculado a algum grupo");
    }

    List<ProdutoInstituicaoGrupoProdutos> listaProdutosDoGrupo =
        produtoInstituicaoGrupoProdutosRepository
            .findByProdutoInstituicaoGrupoProdutosId_IdGrupoOrderByPosicaoHierarquiaDesc(idGrupo);

    if (listaProdutosDoGrupo.size() == MAXIMO_PRODUTOS_VINCULADOS_A_GRUPOS) {
      throw new GenericServiceException(
          "O grupo já contém o limite de "
              + MAXIMO_PRODUTOS_VINCULADOS_A_GRUPOS
              + " produtos vinculados.");
    }

    ProdutoInstituicaoGrupoProdutos produtoGrupo = new ProdutoInstituicaoGrupoProdutos();
    ProdutoInstituicaoGrupoProdutosId produtoGrupoId =
        new ProdutoInstituicaoGrupoProdutosId(idProdInstituicao, idGrupo);
    produtoGrupo.setProdutoInstituicaoGrupoProdutosId(produtoGrupoId);
    produtoGrupo.setPosicaoHierarquia(
        listaProdutosDoGrupo.isEmpty()
            ? 1
            : listaProdutosDoGrupo.get(0).getPosicaoHierarquia() + 1);

    save(produtoGrupo);
  }

  public ProdutoInstituicaoGrupoProdutos findOne(ProdutoInstituicaoGrupoProdutosId id) {
    return produtoInstituicaoGrupoProdutosRepository.findById(id).orElse(null);
  }

  public List<ProdutoInstituicaoGrupoProdutos> findByGrupoId(Long idGrupo) {
    return produtoInstituicaoGrupoProdutosRepository.findByProdutoInstituicaoGrupoProdutosIdIdGrupo(
        idGrupo);
  }
}
