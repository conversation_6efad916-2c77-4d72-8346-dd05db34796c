package br.com.sinergico.service.suporte;

import static br.com.sinergico.service.cadastral.PortadorLoginService.DETALHE_ERRO_LOGIN_INEXISTENTE;
import static br.com.sinergico.service.cadastral.RepresentanteLegalService.REPRESENTANTE_LEGAL_ATIVO;

import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.suporte.GatewaySMS;
import br.com.entity.suporte.LogEventoConta;
import br.com.entity.suporte.LogSMS;
import br.com.entity.suporte.TokenRedefinicaoSenha;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.ComunicadoConta;
import br.com.json.bean.suporte.ComunicadoContaViaSMS;
import br.com.json.bean.suporte.GerarTokenRedefinicaoSenha;
import br.com.json.bean.suporte.UtilizarTokenRedefinirSenhaRequest;
import br.com.sinergico.repository.cadastral.ProdutoPlataformaRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.repository.suporte.TokenRedefinicaoSenhaRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.cadastral.RepresentanteLegalService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TokenRedefinicaoSenhaService extends GenericService<TokenRedefinicaoSenha, Long> {

  private static final int TEMPO_EXPIRACAO_TOKEN_CADASTRO_LOGIN = 5;

  private static final Integer TAMANHO_TOKEN_CADASTRO_LOGIN = 8;

  private static final Integer TIPO_EVENTO_TOKEN_CADASTRO_LOGIN = 7;
  private static final int MAX_CEL = 999999999;

  private static final int MIN_CEL = 910000000;
  private static final Integer MIN_DDD = 11;

  private static final Integer MAX_DDD = 99;

  private TokenRedefinicaoSenhaRepository tokenRepository;

  @Autowired private AgenteComunicadorPortadorService agenteComunicadorService;

  @Autowired private LogEventoContaService logEventoContaService;

  @Autowired private GatewaySMSService gatewaySMSService;

  @Autowired private LogSMSService logSMSService;

  @Autowired private HierarquiaInstituicaoRepository instituicaoRepository;

  @Autowired private ProdutoPlataformaRepository prodPlatRepository;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired PessoaService pessoaService;

  @Autowired RepresentanteLegalService representanteLegalService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private UtilService utilService;

  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

  private static Logger log = LoggerFactory.getLogger(TokenAcessoService.class);

  @Autowired
  public TokenRedefinicaoSenhaService(TokenRedefinicaoSenhaRepository repo) {
    super(repo);
    tokenRepository = repo;
  }

  @Transactional
  public TokenRedefinicaoSenha gerarTokenRedefinicaoSenha(GerarTokenRedefinicaoSenha model1) {

    if (model1.getDocumento().length() != 11
        && !(model1.getDocumento().length() == 14
            && Util.isNotNull(model1.getCpfRepresentante()))) {
      throw new GenericServiceException("Conjunto documento e cpfRepresentante inesperados.");
    }

    PortadorLogin portadorLogin =
        portadorLoginService.buscarLoginEGarantirExistencia(
            model1.getIdProcessadora(),
            model1.getIdInstituicao(),
            model1.getDocumento(),
            model1.getCpfRepresentante(),
            model1.getGrupoAcesso(),
            model1.getTipoLogin(),
            model1.getIdInstituicao() == 4001
                ? "Login de acesso não identificado"
                : DETALHE_ERRO_LOGIN_INEXISTENTE);

    portadorLoginService.validarTempoRedefinicaoSenha(portadorLogin, true);

    List<Pessoa> pessoaList = pessoaService.findPessoaListWithLogin(portadorLogin);
    if (pessoaList.isEmpty()) {
      throw new GenericServiceException(
          "Login de acesso não identificado. Pessoa não encontrada. ", "Pessoa não encontrada. ");
    }
    Pessoa pessoaEncontrada;
    // Validação data de nascimento para pessoas fisicas
    if (Constantes.PESSOA_FISICA.equals(portadorLogin.getIdTipoPessoa())) {
      // Solucao para colocar data que podem estar no formato yyyy-MM-dd ou yyyy-MM-ddT03:00:00.000Z
      // no formato yyyy-MM-dd
      String modelDtNascString =
          model1.getDataNascimento().indexOf('T') == -1
              ? model1.getDataNascimento()
              : model1.getDataNascimento().substring(0, model1.getDataNascimento().indexOf('T'));
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

      pessoaEncontrada =
          pessoaList.stream()
              .filter(
                  pessoa ->
                      (Constantes.PESSOA_FISICA.equals(pessoa.getIdTipoPessoa())
                          && pessoa.getDataNascimento() != null
                          && pessoa
                                  .getDataNascimento()
                                  .format(formatter)
                                  .compareTo(modelDtNascString)
                              == 0))
              .min((p1, p2) -> p2.getDataHoraInclusao().compareTo(p1.getDataHoraInclusao()))
              .orElseThrow(
                  () ->
                      new GenericServiceException(
                          "Login de acesso não identificado. Data de nascimento não confere. ",
                          "Data de nascimento não confere "));
    } else {
      pessoaEncontrada = pessoaList.get(0);
    }

    // Casos com representante legal
    if (portadorLogin.getDocumentoAcesso() != null
        && Constantes.PESSOA_JURIDICA.equals(portadorLogin.getIdTipoPessoa())) {

      List<ContaPessoa> contaPessoaList =
          contaPessoaService.findByIdPessoa(pessoaEncontrada.getIdPessoa());
      if (contaPessoaList.isEmpty()) {
        throw new GenericServiceException("Conta não encontrada.");
      }

      List<RepresentanteLegal> representanteLegalList =
          representanteLegalService.findByCpfAndStatus(
              portadorLogin.getDocumentoAcesso(), REPRESENTANTE_LEGAL_ATIVO);
      RepresentanteLegal representanteLegalEscolhido = null;
      for (RepresentanteLegal representanteLegal : representanteLegalList) {
        for (ContaPessoa contaPessoa : contaPessoaList) {
          if (representanteLegal.getIdConta().equals(contaPessoa.getIdConta())) {
            representanteLegalEscolhido = representanteLegal;
            break;
          }
        }
      }
      if (representanteLegalEscolhido == null) {
        throw new GenericServiceException("Não foi encontrado o representante.");
      }

      if (!model1
          .getDataNascimento()
          .equals(simpleDateFormat.format(representanteLegalEscolhido.getDataNascimento()))) {
        throw new GenericServiceException("Dados inválidos. Tente novamente.");
      }

      String chaveExterna =
          representanteLegalEscolhido
              .getDddCelular()
              .toString()
              .concat(representanteLegalEscolhido.getTelefoneCelular().toString())
              .concat(LocalDate.now().toString());
      TokenRedefinicaoSenha tokenRedefinicaoSenha = gerarToken(model1, chaveExterna);
      String tokenGerado = tokenRedefinicaoSenha.getToken();
      montarEEnviarSMSRepresentante(tokenGerado, model1, representanteLegalEscolhido);
      return tokenRedefinicaoSenha;
    }

    if (pessoaEncontrada.getDddTelefoneCelular() == null
        || pessoaEncontrada.getTelefoneCelular() == null) {
      throw new GenericServiceException("Não há telefone cadastrado para recebimento de token.");
    }

    String chaveExterna =
        pessoaEncontrada
            .getDddTelefoneCelular()
            .toString()
            .concat(pessoaEncontrada.getTelefoneCelular().toString())
            .concat(LocalDate.now().toString());
    TokenRedefinicaoSenha tokenRedefinicaoSenha = gerarToken(model1, chaveExterna);
    String tokenGerado = tokenRedefinicaoSenha.getToken();
    montarEEnviarSMS(tokenGerado, model1, pessoaEncontrada);
    portadorLoginService.save(portadorLogin);
    return tokenRedefinicaoSenha;
  }

  private void montarEEnviarSMS(String token, GerarTokenRedefinicaoSenha model1, Pessoa pessoa) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    if (model1.getIdConta() != null) {
      comunicado.setIdConta(model1.getIdConta());
    }
    GatewaySMS gateway = getGatewaySMS(model1.getIdProcessadora(), model1.getIdInstituicao());

    comunicado.setIdCredencial(model1.getIdCredencial());
    comunicado.setIdGatewaySMS(gateway.getIdGatewaySMS());
    String mensagem = getMensagem(gateway, model1, token);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(TIPO_EVENTO_TOKEN_CADASTRO_LOGIN);

    Long celularNumero = null;
    String celular =
        pessoa.getDddTelefoneCelular().toString().concat(pessoa.getTelefoneCelular().toString());
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = new Long(celular);
      ddd = new Integer(celular.substring(0, 2));
      restante = new Integer(celular.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celular + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celular + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        getLogEventoConta(comunicado, gateway.getIdProcessadora(), gateway.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogSMS logSms = new LogSMS();
    BeanUtils.copyProperties(comunicado, logSms);
    logSms.setNumeroCelular(new Long(celular));

    logSms.setDataHoraRegistro(new Date());
    logSms.setDataHoraAgendamento(new Date());
    logSms.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logSms.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    logSms = logSMSService.save(logSms);

    comunicado.setIdLogEventoConta(logSms.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logSms.getIdLogEventoConta());

    enviarSms(comunicado);
  }

  private void montarEEnviarSMSRepresentante(
      String token, GerarTokenRedefinicaoSenha model1, RepresentanteLegal representante) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    if (model1.getIdConta() != null) {
      comunicado.setIdConta(model1.getIdConta());
    }
    GatewaySMS gateway = getGatewaySMS(model1.getIdProcessadora(), model1.getIdInstituicao());

    comunicado.setIdCredencial(model1.getIdCredencial());
    comunicado.setIdGatewaySMS(gateway.getIdGatewaySMS());
    String mensagem = getMensagem(gateway, model1, token);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(TIPO_EVENTO_TOKEN_CADASTRO_LOGIN);

    Long celularNumero = null;
    String celular =
        representante
            .getDddCelular()
            .toString()
            .concat(representante.getTelefoneCelular().toString());
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = new Long(celular);
      ddd = new Integer(celular.substring(0, 2));
      restante = new Integer(celular.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celular + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celular + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        getLogEventoConta(comunicado, gateway.getIdProcessadora(), gateway.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogSMS logSms = new LogSMS();
    BeanUtils.copyProperties(comunicado, logSms);
    logSms.setNumeroCelular(new Long(celular));

    logSms.setDataHoraRegistro(new Date());
    logSms.setDataHoraAgendamento(new Date());
    logSms.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logSms.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    logSms = logSMSService.save(logSms);

    comunicado.setIdLogEventoConta(logSms.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logSms.getIdLogEventoConta());

    enviarSms(comunicado);
  }

  private String getMensagem(GatewaySMS gateway, GerarTokenRedefinicaoSenha model1, String token) {
    StringBuilder texto = new StringBuilder();
    String descInstituicao =
        instituicaoRepository.getDescInstituicao(
            gateway.getIdProcessadora(), gateway.getIdInstituicao());
    texto.append(descInstituicao != null ? descInstituicao + ":" : "");
    texto.append(" Prezado cliente, use o código de validação ");
    texto.append(token);
    texto.append(" para prosseguir");
    if (model1.getIdConta() != null
        && (model1.getIdInstituicao() == null
            || !Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model1.getIdInstituicao()))) {
      String descProdPlat =
          prodPlatRepository.getDescProdutoPlataformaByIdConta(model1.getIdConta());
      texto.append(" ");
      texto.append(descProdPlat);
    }
    texto.append(".");
    return texto.toString();
  }

  private boolean isCelularValido(Integer dddTelefoneCelular, Integer telefoneCelular) {
    return (dddTelefoneCelular >= MIN_DDD && dddTelefoneCelular <= MAX_DDD)
        && (telefoneCelular > MIN_CEL && telefoneCelular < MAX_CEL);
  }

  private LogEventoConta getLogEventoConta(
      ComunicadoContaViaSMS comunicado, Integer idProcessadora, Integer idInstituicao) {
    LogEventoConta evento = getLogEventoConta(comunicado);
    evento.setIdProcessadora(idProcessadora);
    evento.setIdInstituicao(idInstituicao);
    return evento;
  }

  private GatewaySMS getGatewaySMS(Integer idProcessadora, Integer idInstituicao) {
    GatewaySMS gatewaySMS =
        gatewaySMSService.findFirstByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (gatewaySMS == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar gatewaySMS. IdProcessadora: "
              + idProcessadora
              + " , idInstituicao: "
              + idInstituicao);
    }
    return gatewaySMS;
  }

  private <Req extends ComunicadoConta> LogEventoConta getLogEventoConta(Req comunicado) {
    LogEventoConta logEvento = new LogEventoConta();
    logEvento.setIdConta(comunicado.getIdConta());
    logEvento.setIdCredencial(comunicado.getIdCredencial());
    logEvento.setTipoEvento(comunicado.getTipoEventoConta());
    return logEvento;
  }

  private void enviarSms(ComunicadoContaViaSMS comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  public Boolean utilizarToken(UtilizarTokenRedefinirSenhaRequest model) {
    List<TokenRedefinicaoSenha> tokenAcessoList =
        findByChaveExternaAndTokenIgnoreCaseAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            model.getChaveExterna(), model.getToken());

    // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
    // nao exista mesmo
    if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
      throw new GenericServiceException(
          "Código inválido.",
          "Verifique o código digitado ou solicite um novo",
          HttpStatus.UNAUTHORIZED);
    }

    validarDataValidade(tokenAcessoList.get(0));

    //		tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());
    //		tokenAcessoList.get(0).setInTokenExpirado(false);
    //		tokenAcessoList.get(0).setInTokenValidado(true);

    return save(tokenAcessoList.get(0)) != null;
  }

  private void validarDataValidade(TokenRedefinicaoSenha tokenAcesso) {
    if (LocalDateTime.now().isAfter(tokenAcesso.getDataHoraExpiracaoToken())) {
      throw new GenericServiceException(
          "Código expirado. Solicite um novo", "Código expirado solicite um novo");
    }
  }

  public synchronized TokenRedefinicaoSenha gerarToken(
      GerarTokenRedefinicaoSenha model, String chaveExterna) {

    String tokenGerado = null;
    // se nao entao gera um novo pra mim
    if (utilService.isAmbienteHomologacao()) {
      tokenGerado = "12345678";
    } else {
      tokenGerado = Util.generateRandomLetrasENumerosUpperCase(TAMANHO_TOKEN_CADASTRO_LOGIN);
    }
    return saveTokenGerado(model, tokenGerado, chaveExterna);
  }

  private void cancelarTokensAntigos(List<TokenRedefinicaoSenha> tokenRedefinicaoSenhaList) {
    for (TokenRedefinicaoSenha tokenRedefinicaoSenha : tokenRedefinicaoSenhaList) {
      tokenRedefinicaoSenha.setDataHoraCancelamentoChave(LocalDateTime.now());
    }
    saveAll(tokenRedefinicaoSenhaList);
  }

  private TokenRedefinicaoSenha saveTokenGerado(
      GerarTokenRedefinicaoSenha model, String tokenGerado, String chaveExterna) {
    TokenRedefinicaoSenha tokenRedefinicaoSenha = new TokenRedefinicaoSenha();
    tokenRedefinicaoSenha.setChaveExterna(chaveExterna);
    LocalDateTime agora = LocalDateTime.now();
    Date agoraDate = DateUtil.localDateTimeToDate(agora);

    tokenRedefinicaoSenha.setDataHoraGeracao(agora);
    tokenRedefinicaoSenha.setToken(tokenGerado);
    tokenRedefinicaoSenha.setInTokenExpirado(false);
    tokenRedefinicaoSenha.setInTokenValidado(false);
    tokenRedefinicaoSenha.setDocumento(
        Util.isNotNull(model.getCpfRepresentante()) && !model.getCpfRepresentante().isEmpty()
            ? model.getCpfRepresentante()
            : model.getDocumento());

    tokenRedefinicaoSenha.setIdProcessadora(model.getIdProcessadora());
    tokenRedefinicaoSenha.setIdInstituicao(model.getIdInstituicao());
    Integer minutosExpiracao = model.getMinutosExpiracao();
    boolean minutosExpiracaoValido =
        minutosExpiracao != null && minutosExpiracao > 0 && minutosExpiracao <= 60;

    Date tempoAcrescentado =
        DateUtil.aumentar(
            agoraDate,
            minutosExpiracaoValido ? minutosExpiracao : TEMPO_EXPIRACAO_TOKEN_CADASTRO_LOGIN,
            DateUtil.MINUTOS);
    tokenRedefinicaoSenha.setDataHoraExpiracaoToken(
        DateUtil.dateToLocalDateTime(tempoAcrescentado));
    save(tokenRedefinicaoSenha);
    return tokenRedefinicaoSenha;
  }

  public List<TokenRedefinicaoSenha>
      findByChaveExternaAndTokenIgnoreCaseAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
          String chaveExterna, String token) {
    return tokenRepository
        .findByChaveExternaAndTokenIgnoreCaseAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            chaveExterna, token);
  }

  public List<TokenRedefinicaoSenha>
      findAllByTokenIgnoreCaseAndIdInstituicaoAndIdProcessadoraAndDocumentoOrderByDataHoraGeracaoDesc(
          String token, Integer idInstituicao, Integer idProcessadora, String documento) {
    return tokenRepository
        .findAllByTokenIgnoreCaseAndIdInstituicaoAndIdProcessadoraAndDocumentoOrderByDataHoraGeracaoDesc(
            token, idInstituicao, idProcessadora, documento);
  }
  ;
}
