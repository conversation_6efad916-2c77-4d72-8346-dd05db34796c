package br.com.sinergico.controller.pix;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.pix.InstituicaoPix;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.pix.InstituicaoPixProdutoContaVO;
import br.com.sinergico.vo.pix.InstituicaoPixVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/pix/instituicao")
public class InstituicaoPixController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  /**
   * Atualiza a Instituição { id:0, idProduto : 0, habilitado : 0, valor_maximo : 0 }
   *
   * <AUTHOR> Oliveira(<EMAIL>)
   */
  @ApiOperation(value = "Atualiza uma Instituição PIX", response = Object.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/update/{id}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Secured({"ROLE_PIX_INSTITUICAO_PUT"})
  public ResponseEntity<?> updateInsituicaoPix(
      @RequestBody InstituicaoPixVO objData, @PathVariable String id) {

    SecurityUser user = getAuthenticatedUser(request, em);
    InstituicaoPix data = instituicaoPixService.updateData(id, objData, user);

    Map<String, Object> map = new HashMap<>();
    map.put("data", data);
    map.put("msg", "Dados Salvos com sucesso!");
    return ResponseEntity.ok(map);
  }

  @ApiOperation(
      value = "Consulta se a Instituição/ContaPagamento está habilitada para transações PIX",
      response = Boolean.class)
  @RequestMapping(
      path = "/produto-instituicao/{idInstituicao}/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_PRODUTO_INSTITUICAO"})
  public ResponseEntity<Boolean> buscarInstituicao(
      HttpServletRequest request,
      @PathVariable("idInstituicao") Integer idInstituicao,
      @PathVariable("idConta") Long idConta) {

    InstituicaoPix data = instituicaoPixService.findFirstByIdInstituicaoOrderById(idInstituicao);
    ContaPagamento contaPagamento =
        contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta);
    boolean habilitado = data.getHabilitado() != null && data.getHabilitado() == 1;

    if (!Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_INSTITUICAO_WIZ.equals(idInstituicao)
        && !Constantes.ID_PRODUCAO_QISTA.equals(idInstituicao)) {

      habilitado =
          habilitado
              && contaPagamento.getPixHabilitado() != null
              && contaPagamento.getPixHabilitado();
    }

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(value = "Busca Instituição PIX pelo ID Produto", response = Object.class)
  @RequestMapping(
      path = "/produto-instituicao/{idProduto}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_PRODUTO_INSTITUICAO"})
  public ResponseEntity<?> buscarInstituicao(
      HttpServletRequest request, @PathVariable("idProduto") Integer idProduto) {

    Map<String, Object> map = new HashMap<>();
    InstituicaoPix data = instituicaoPixService.buscarInstituicaoPixPorProduto(idProduto, null);
    map.put("data", data);

    return ResponseEntity.ok(map);
  }

  /** Reseta as caches com os tokens Rendimento e força a expiração dos tokens no banco de dados */
  @ApiOperation(
      value =
          "Reseta Caches de tokens com o Banco Rendimento, forçando sua expiração no banco de dados.")
  @RequestMapping(
      path = "/reset-token-cache",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_PIX_INSTITUICAO_RESET_CACHES"})
  public void resetarCachesPix() {

    bancoRendimentoClient.resetarTokenCaches();
  }

  /**
   * Salva a Instituição { idProduto : 0, habilitado : 0, valor_maximo : 0 }
   *
   * <AUTHOR> Oliveira(<EMAIL>)
   */
  @ApiOperation(value = "Salva nova Instituição PIX", response = Object.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(path = "/save", method = RequestMethod.POST)
  @Secured({"ROLE_PIX_INSTITUICAO_POST"})
  public ResponseEntity saveInstituicaoPix(@RequestBody InstituicaoPixVO objData) {

    SecurityUser user = getAuthenticatedUser(request, em);
    InstituicaoPix data = instituicaoPixService.saveData(objData, user);

    Map<String, Object> map = new HashMap<>();
    map.put("data", data);
    map.put("msg", "Dados Salvos com sucesso!");
    return ResponseEntity.ok(map);
  }

  @ApiOperation(
      value = "Consulta se a Instituição/ContaPagamento está habilitada para transações PIX",
      response = Boolean.class)
  @RequestMapping(
      path = "/produto-instituicao-pix-cash-in/{idInstituicao}/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_CONTAS_PRODUTO_PIX_CASH_IN_PORTADOR"})
  public ResponseEntity<List<InstituicaoPixProdutoContaVO>> buscarProdutosPixCashIn(
      HttpServletRequest request,
      @PathVariable("idInstituicao") Integer idInstituicao,
      @PathVariable("documento") String documento) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    List<InstituicaoPixProdutoContaVO> contasPixCashInHabilitado = new ArrayList<>();
    try {
      contasPixCashInHabilitado =
          instituicaoPixService.findProdutosAndContasPixCashIn(
              idInstituicao, documento, userPortador);
    } catch (GenericServiceException e) {
      throw new GenericServiceException(e.getMensagem(), HttpStatus.NO_CONTENT);
    }

    return new ResponseEntity<>(contasPixCashInHabilitado, HttpStatus.OK);
  }
}
