package br.com.sinergico.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Data;

@Data
public class InfosChavesPixIssuerVO implements Serializable {

  private String tipoChave;

  private String valorChave;

  private LocalDateTime dataInclusao;

  private Boolean reivindicada;

  private String statusReivindicacao;

  private Boolean deleted;

  public InfosChavesPixIssuerVO(
      String tipoChave,
      String valorChave,
      LocalDateTime dataInclusao,
      Boolean reivindicada,
      Boolean deleted) {
    this.tipoChave = tipoChave;
    this.valorChave = valorChave;
    this.dataInclusao = dataInclusao;
    this.reivindicada = reivindicada;
    this.deleted = deleted;
  }
}
