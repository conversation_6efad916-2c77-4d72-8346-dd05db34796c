package br.com.sinergico.service.ted;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PessoaContaBancaria;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.suporte.ParametroProcessamentoSistema;
import br.com.entity.suporte.RendimentoProperties;
import br.com.entity.transacional.CodigoTransacao;
import br.com.entity.transacional.TED;
import br.com.entity.transacional.TransferenciaDto;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.JcardServiceException;
import br.com.json.bean.transacional.ConsultaTedValue;
import br.com.json.bean.transacional.TEDClientResponse;
import br.com.json.bean.transacional.TEDComprovanteRequest;
import br.com.json.bean.transacional.TEDRendimentoRequest;
import br.com.json.bean.transacional.TEDRequest;
import br.com.json.bean.transacional.TedConsultaStatusRendimentoProcResponse;
import br.com.json.bean.transacional.TedConsultaStatusRendimentoResponse;
import br.com.json.bean.transacional.TedRendimentoResponse;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.enums.MotivoTEDEnum;
import br.com.sinergico.enums.ServicoFornecedorEnum;
import br.com.sinergico.enums.ServicoTipoEnum;
import br.com.sinergico.enums.StatusPagamentoTedRendimentoEnum;
import br.com.sinergico.enums.StatusTransacao;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.facade.transacional.ControleGarantiaFacade;
import br.com.sinergico.repository.gatewaypagto.ContratoGatewayPagtoTituloInsituicaoRepository;
import br.com.sinergico.repository.gatewaypagto.ContratoGatewayPagtoTituloRepository;
import br.com.sinergico.repository.transacional.TEDRepository;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.cadastral.LimitesContaService;
import br.com.sinergico.service.gatewaypagto.GatewayPagtoExternoService;
import br.com.sinergico.service.jcard.LogTransacoesService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.service.suporte.AplicativoMensagemService;
import br.com.sinergico.service.suporte.ParametroProcessamentoSistemaService;
import br.com.sinergico.service.suporte.ServicoChamadaService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.service.transacional.TEDService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.HierarquiaInstituicaoVO;
import br.com.sinergico.vo.LogTransacoesVO;
import br.com.sinergico.vo.TEDResponseVO;
import br.com.sinergico.vo.TEDVO;
import br.com.sinergico.vo.TransacaoTedResponseVO;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.transaction.Transactional;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

@Service
public class TedRendimentoService extends TEDService {

  private static final Integer STATUS_ATIVO = 1;

  private static final Integer OP_TED = 4;

  private static final List<Integer> idsInstituicoesAlertaValorLimiteTED =
      new ArrayList<>(Arrays.asList(Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT));

  private static final BigDecimal VALOR_LIMITE_TED_DISPARO_EMAIL_AUTOMATICO =
      new BigDecimal("15000.00");
  public static final int TIPO_CONTA_FAVORECIDO = 1;
  public static final int TIPO_PESSOA_FAVORECIDO_FISICO = 1;
  public static final int TIPO_PESSOA_FAVORECIDO_JURIDICO = 2;

  public static final String TED_EM_PROCESSAMENTO = "TED em Processamento";
  public static final String SEM_COMPROVANTE = "Erro ao recuperar comprovante";
  public static final String TED_COM_SUCESSO = "TED efetuada com sucesso";
  public static final String TED_ESTORNADA = "TED Estornada";
  private static final String PREVIOUSLY_VOIDED = "previously.voided";

  private final Logger logger = LoggerFactory.getLogger(TedRendimentoService.class);

  @Autowired
  public TedRendimentoService(TEDRepository repository) {
    super(repository);
  }

  public TedRendimentoService() {
    super(null);
  }

  @Value("${service.banco-rendimento.url}")
  private String urlBancoRendimento;

  private RendimentoProperties rendimentoProperties;

  @Autowired private ControleGarantiaFacade controleGarantiaFacade;

  @Autowired private ContratoGatewayPagtoTituloRepository contratoRepository;

  @Autowired private ContratoGatewayPagtoTituloInsituicaoRepository contratoInstRepository;

  @Autowired private ServicoChamadaService servicoChamadaService;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  @Lazy @Autowired public EmailService emailService;

  @Autowired public ParametroProcessamentoSistemaService parametroProcessamentoSistemaService;

  @Autowired LancamentoService lancamentoService;

  @Autowired private RestTemplate restTemplate;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private GatewayPagtoExternoService gatewayPagtoExternoService;

  @Autowired private EventoService eventoService;
  @Autowired private AplicativoMensagemService aplicativoMensagemService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private LogTransacoesService logTransacoesService;

  @Autowired private LimitesContaService limitesContaService;

  public TEDClientResponse efetuarTedPortador(
      TEDRequest tedRequest, SecurityUserPortador userPortador) {

    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            tedRequest.getIdConta(), userPortador);

    travaContasService.travaContas(conta.getIdConta(), Servicos.TED);
    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.TED);

    return efetuarTed(
        tedRequest, Long.valueOf(Constantes.ID_USUARIO_INCLUSAO_PORTADOR), conta, false);
  }

  public TEDClientResponse efetuarTedUser(TEDRequest tedRequest, SecurityUser user) {

    if (!Constantes.ID_USUARIO_TED_AUTOMATIZADA.equals(user.getIdUsuario())) {
      throw new GenericServiceException(
          "Permissão negada para esta operação.", HttpStatus.FORBIDDEN);
    }

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(tedRequest.getIdConta());

    return efetuarTed(tedRequest, Long.valueOf(user.getIdUsuario()), conta, true);
  }

  @Transactional
  public TEDClientResponse efetuarTed(
      TEDRequest tedRequest, Long idUsuario, ContaPagamento contaPagamento, Boolean tedUser) {

    logger.info("Realizando TED Rendimento");

    HierarquiaInstituicaoVO instituicao =
        controleGarantiaFacade.getInstituicao(
            contaPagamento.getIdProcessadora(), contaPagamento.getIdInstituicao());
    if (instituicao.getStatus() == 9) {
      throw new GenericServiceException("Serviço temporariamente indisponível para a instituição");
    }
    ParametroProcessamentoSistema parametroProcessamentoSistema =
        parametroProcessamentoSistemaService.findFirstByIdInstituicaoAndTipoParametro(
            contaPagamento.getIdInstituicao(), Constantes.HR_TED_RENDIMENTO);
    // Verifica se a mesma transação já foi efetuada nos ultimos 10 minutos
    Date horaMenosDezMinutos = DateUtil.localDateTimeToDate(LocalDateTime.now().minusMinutes(10));

    if (getValorTransferenciaAndDocumentoFavorecidoAndDtHrInclusaoAndIdContaAndAgenciaFavorecidoAndBancoFavorecidoAndIdInstituicao(
        new BigDecimal(tedRequest.getValor()).setScale(2, RoundingMode.HALF_EVEN),
        tedRequest.getNumDocumentoFavorecido(),
        horaMenosDezMinutos,
        tedRequest.getIdConta(),
        tedRequest.getAgenciaFavorecido(),
        tedRequest.getBancoFavorecido(),
        contaPagamento.getIdInstituicao())) {
      throw new GenericServiceException(
          "Transação já efetuada com o mesmo valor e conta. Tente novamente mais tarde.");
    }

    if (BigDecimal.valueOf(tedRequest.getValor()).compareTo(BigDecimal.ZERO) <= 0) {
      throw new GenericServiceException("O valor para TED não pode ser negativo ou igual a zero.");
    }

    if (Objects.isNull(parametroProcessamentoSistema)
        || !parametroProcessamentoSistema.getTexto().equals("AUTORIZADO")) {
      if (!verificarHora()) {
        TEDClientResponse tedClientResponse = new TEDClientResponse();
        tedClientResponse.setSuccess(false);
        tedClientResponse.setErrors("Horário limite excedido para essa operação.");
        return tedClientResponse;
      }
    }

    if (!Constantes.CONTA_ATIVA.equals(contaPagamento.getIdStatusConta())) {
      throw new GenericServiceException("A Conta precisa estar Desbloqueada para realizar TED's.");
    }

    Pessoa pessoa = pessoaService.findPessoaTitularConta(tedRequest.getIdConta());

    if (pessoa == null) {
      throw new GenericServiceException("Pessoa não encontrada");
    }

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(contaPagamento.getIdInstituicao())
        && !pessoa.getDocumento().equals(tedRequest.getNumDocumentoFavorecido())
        && !tedUser) {
      throw new GenericServiceException("TED permitida somente para o mesmo CPF.");
    }

    PessoaContaBancaria pessoaConta = null;
    if (tedRequest.getIdPessoaContaBancaria() != null) {
      pessoaConta = pessoaContaBancariaService.findById(tedRequest.getIdPessoaContaBancaria());
    }

    CodigoTransacao codigoTransacao =
        codigoTransacaoService.findOneByCodTransacao(tedRequest.getCodTransacao());
    if (codigoTransacao == null) {
      throw new GenericServiceException("Código da transação não encontrado");
    }

    limitesContaService.validarLimiteContaDiario(
        BigDecimal.valueOf(tedRequest.getValor()), contaPagamento);

    ProdutoInstituicaoConfiguracao produto =
        lancamentoService.getProduto(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdInstituicao(),
            contaPagamento.getIdProdutoInstituicao());
    String idParceiroResgate =
        tedRequest.getIdParceiroResgate() != null
            ? tedRequest.getIdParceiroResgate().toString()
            : "";
    BigDecimal valor =
        new BigDecimal(tedRequest.getValor().toString()).setScale(2, RoundingMode.HALF_EVEN);

    String agenciaFavorecido = null;
    String contaFavorecido = null;
    String idBancoFavorecido = null;
    String nomeFavorecido = null;
    if (pessoaConta != null) {
      agenciaFavorecido = pessoaConta.getIdAgencia().toString();
      contaFavorecido = pessoaConta.getContaBancaria().toString();
      idBancoFavorecido = pessoaConta.getBanco().getIdBanco().toString();
      nomeFavorecido = pessoaConta.getPessoa().getNomeCompleto();
    } else {
      agenciaFavorecido = tedRequest.getAgenciaFavorecido();
      contaFavorecido = tedRequest.getContaFavorecido();
      idBancoFavorecido = tedRequest.getBancoFavorecido();
      nomeFavorecido = tedRequest.getNomeFavorecido();
    }

    String rrn =
        verificacoesESensibilizaSaldo(
            idUsuario,
            idParceiroResgate,
            agenciaFavorecido,
            contaFavorecido,
            idBancoFavorecido,
            nomeFavorecido,
            valor,
            tedRequest.getCodTransacao(),
            tedRequest.getIpOrigem(),
            produto,
            contaPagamento);
    Boolean rrnJaEstornado = Boolean.FALSE;

    TED ted;
    TransferenciaDto transferencia;
    TEDRendimentoRequest request;
    TEDClientResponse tedClientResponse = new TEDClientResponse();
    TransacaoTedResponseVO tedResponseVO = new TransacaoTedResponseVO();

    try {
      ted =
          preencherSalvarTED(
              rrn, contaPagamento, idUsuario, produto, codigoTransacao, tedRequest, pessoaConta);
      transferencia =
          preencherTransferenciaTED(tedRequest, pessoaConta, tedRequest.getIpOrigem(), ted);
      request =
          preencherRequest(
              tedRequest, transferencia, nomeFavorecido, idBancoFavorecido, agenciaFavorecido);
    } catch (Exception e) {
      logger.error("TED RENDIMENTO ERROR: {}", e.getMessage());
      e.printStackTrace();
      String destino =
          String.format(
              "TED para %s banco %s agência %s conta %s",
              tedRequest.getNomeFavorecido(),
              tedRequest.getBancoFavorecido(),
              tedRequest.getAgenciaFavorecido(),
              tedRequest.getContaFavorecido());
      realizarEstorno(rrn, contaPagamento, produto, valor, codigoTransacao, "Sistema Indisponível");
      tedClientResponse.setSuccess(false);
      tedClientResponse.setErrors("Não foi possível realizar a TED");
      return tedClientResponse;
    }

    try {

      logger.info("Iniciando chamada de efetuar TED API do Rendimento" + request.toString());
      this.rendimentoProperties = new RendimentoProperties();
      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_V1_PAGAMENTOS_TED);
      endPoint.setUri(Constantes.URI_RENDIMENTO_INCLUIR_TED);
      this.rendimentoProperties.setEfetuarTed(endPoint);
      this.rendimentoProperties.setUrl(urlBancoRendimento);

      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(
              contaPagamento.getIdInstituicao());
      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.rendimentoProperties.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, rendimentoProperties.getAutenticacao());

      servicoChamadaService.registrarServicoChamado(
          ted.getIdInstituicao().longValue(),
          ted.getIdContaIssuer(),
          pessoa.getDocumento(),
          autenticacaoSensedia != null ? "200" : null,
          ServicoTipoEnum.AUTHORIZATION_TOKEN.getValue(),
          ServicoFornecedorEnum.BANCO_RENDIMENTO.getValue());

      HttpEntity<TEDRendimentoRequest> entity =
          new HttpEntity<TEDRendimentoRequest>(request, headers);
      TedRendimentoResponse response = efetuarTransferencia(entity, chaveInstituicao);
      if (response != null && response.getStatusCode().equals(201)) {
        ted.setNomeRemetente(pessoa.getNomeCompleto());
        tedClientResponse.setSuccess(true);
        ted.setIdTransacaoRendimento(response.getValue());
        ted.setComprovante(gerarComprovante(ted, rrn, instituicao));
        ted.setResponse_code(response.getStatusCode().toString());
        ted.setResponseMessage("Sucesso");

        // Salvar o limite diario
        limitesContaService.salvarLimiteDiario(
            BigDecimal.valueOf(tedRequest.getValor()), contaPagamento.getIdConta());
        logger.info("TED API do Rendimento " + response.getValue());
      } else {
        Integer status = response != null ? response.getStatusCode() : 500;
        String mensagem =
            response != null && response.getErroMessage() != null
                ? response.getErroMessage().getErrors()[0].getMessage()
                : response != null && response.getErroMessage().getMessage() != null
                    ? response.getErroMessage().getMessage()
                    : "Nada informado.";
        logger.warn(
            String.format(
                "Não houve sucesso ao efetuar Rendimento: Status Rendimento: %d Erro: %s",
                status, mensagem));
        ted.setResponse_code(status.toString());
        ted.setResponseMessage(mensagem);
        ted.setStatusTed(StatusTransacao.ERRO.getCodigo());
        rrnJaEstornado =
            realizarEstorno(
                    rrn, contaPagamento, produto, valor, codigoTransacao, "Sistema Indisponível")
                .getSuccess();
        tedClientResponse.setSuccess(false);
      }
      //            registra a chamada tranferencia ted
      servicoChamadaService.registrarServicoChamado(
          ted.getIdInstituicao().longValue(),
          ted.getIdContaIssuer(),
          !pessoa.getDocumento().isEmpty() ? pessoa.getDocumento() : null,
          tedClientResponse.isSuccess() ? "200" : response.getStatusCode().toString(),
          ServicoTipoEnum.ENVIO_TED.getValue(),
          ServicoFornecedorEnum.BANCO_RENDIMENTO.getValue());
      logger.info(
          "Fim chamada de efetuar TED API do Rendimento"
              + this.rendimentoProperties.getUrlTed()
              + request);
    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.error(
          "TED API Rendimento error. Request: {} . Response: {} ",
          request.toString(),
          e.getResponseBodyAsString());
      rrnJaEstornado =
          realizarEstorno(
                  rrn, contaPagamento, produto, valor, codigoTransacao, "Sistema Indisponível")
              .getSuccess();
      tedClientResponse = new TEDClientResponse();
      tedClientResponse.setSuccess(false);
      ted.setResponse_code(e.getStatusCode().getReasonPhrase());
      ted.setStatusTed(StatusTransacao.ERRO.getCodigo());
      TedRendimentoResponse responseError =
          new Gson().fromJson(e.getResponseBodyAsString(), TedRendimentoResponse.class);
      StringBuilder errorMessage = new StringBuilder();
      if (responseError.getErroMessage() != null
          && responseError.getErroMessage().getErrors() != null) {
        for (TedRendimentoResponse.ErroMessage.Error error :
            responseError.getErroMessage().getErrors()) {
          errorMessage.append(error.getMessage() != null ? error.getMessage() : "").append(" ");
        }
      }
      tedClientResponse.setErrors(errorMessage.toString());
      // registra a chamada tranferencia ted
      servicoChamadaService.registrarServicoChamado(
          ted.getIdInstituicao().longValue(),
          ted.getIdContaIssuer(),
          !pessoa.getDocumento().isEmpty() ? pessoa.getDocumento() : null,
          e.getStatusCode().toString(),
          ServicoTipoEnum.ENVIO_TED.getValue(),
          ServicoFornecedorEnum.BANCO_RENDIMENTO.getValue());
      ted.setResponseMessage(errorMessage.toString());
    } catch (Exception e) {
      e.printStackTrace();
      logger.error("Erro ao efetuar TED via Rendimento. Detalhes: {} ", e.getLocalizedMessage());
      tedClientResponse = new TEDClientResponse();
      tedClientResponse.setSuccess(false);
      tedClientResponse.setErrors("Não foi possível realizar a TED");
      ted.setResponse_code(HttpStatus.INTERNAL_SERVER_ERROR.toString());
      ted.setResponseMessage(e.getMessage());
      ted.setStatusTed(StatusTransacao.ERRO.getCodigo());
      rrnJaEstornado =
          realizarEstorno(
                  rrn, contaPagamento, produto, valor, codigoTransacao, "Sistema Indisponível")
              .getSuccess();
      // registra a chamada tranferencia ted
      servicoChamadaService.registrarServicoChamado(
          ted.getIdInstituicao().longValue(),
          ted.getIdContaIssuer(),
          !pessoa.getDocumento().isEmpty() ? pessoa.getDocumento() : null,
          ted.getResponse_code(),
          ServicoTipoEnum.ENVIO_TED.getValue(),
          ServicoFornecedorEnum.BANCO_RENDIMENTO.getValue());
    }
    if (!tedClientResponse.isSuccess()) {
      rrnJaEstornado =
          Boolean.TRUE.equals(rrnJaEstornado)
              ? rrnJaEstornado
              : realizarEstorno(
                      rrn, contaPagamento, produto, valor, codigoTransacao, "Sistema Indisponível")
                  .getSuccess();
      return tedClientResponse;
    } else if (isProdutoMoedaPonto(produto)) {
      atualizarResgateEmPontoControle(rrn, contaPagamento, valor, tedRequest);
    }

    if (isInstituicaoComAlertaDeTED(contaPagamento.getIdInstituicao())
        && isValorTEDMaiorIgualValorLimiteDisparoEmail(
            new BigDecimal(tedRequest.getValor().toString()).setScale(2, RoundingMode.HALF_EVEN))) {
      emailService.sendEmailKreditTEDAcimaValorLimite(pessoa, tedRequest, ted);
    }

    saveAndFlush(ted);

    Long idContaFavorecido = null;
    if (contaFavorecido != null && contaFavorecido.contains("-")) {
      idContaFavorecido =
          Long.parseLong(contaFavorecido.substring(0, contaFavorecido.indexOf("-")));
    } else if (contaFavorecido != null) {
      idContaFavorecido = Long.parseLong(contaFavorecido);
    }
    eventoService.publicarMovimentacaoFinanceiraEvent(
        Servicos.TED.getDescricao() + "_Rendimento",
        contaPagamento.getIdInstituicao(),
        Integer.valueOf(idBancoFavorecido),
        contaPagamento.getIdConta(),
        idContaFavorecido,
        codigoTransacao.getCodTransacao(),
        null,
        valor,
        pessoa != null ? pessoa.getDocumento() : "0");

    //        Montando dados do Destinatario para comprovante
    tedResponseVO.setRrn(ted.getRrn());
    tedResponseVO.setDataTransacao(
        DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, ted.getDataHoraInclusao()));
    tedResponseVO.setDestinatario(ted.getNomeRemetente());
    tedResponseVO.setBanco(ted.getBancoFavorecido());
    tedResponseVO.setAgencia(ted.getAgenciaFavorecido());
    tedResponseVO.setConta(ted.getContaFavorecido());
    tedResponseVO.setValor(Util.currencyFormat(ted.getValorTransferencia()));

    tedClientResponse.setComprovante(ted.getComprovante());
    tedClientResponse.setTed(tedResponseVO);
    return tedClientResponse;
  }

  private boolean verificarHora() {
    Calendar data = Calendar.getInstance();
    int hora = data.get(Calendar.HOUR_OF_DAY);
    int minuto = data.get(Calendar.MINUTE);
    int diaWeek = data.get(Calendar.DAY_OF_WEEK);
    if (diaWeek == 7 || diaWeek == 1) {
      return false;
    } else if (hora == 6 && minuto >= 30) {
      return true;
    } else {
      return hora > 6 && hora < 20;
    }
  }

  TransferenciaDto preencherTransferenciaTED(
      TEDRequest tedRequest, PessoaContaBancaria pessoaContaBancaria, String ipOrigem, TED ted) {
    TransferenciaDto transferenciaDto = new TransferenciaDto();
    if (pessoaContaBancaria != null) {
      transferenciaDto.setAccountCode(pessoaContaBancaria.getContaBancaria().longValue());
      transferenciaDto.setDigitCode(pessoaContaBancaria.getDvContaBancaria().toString());
      transferenciaDto.setBranchCode(pessoaContaBancaria.getIdAgencia().intValue());
      transferenciaDto.setInstitutionCode(pessoaContaBancaria.getBanco().getIdBanco());
      transferenciaDto.setDocument(pessoaContaBancaria.getPessoa().getDocumento());
      transferenciaDto.setName(pessoaContaBancaria.getPessoa().getNomeCompleto());
      transferenciaDto.setBankAccountType(
          1 == pessoaContaBancaria.getTipoContaBancaria().ordinal() ? "CC" : "CP");
    } else {
      String[] parts = tedRequest.getContaFavorecido().split("-");
      transferenciaDto.setAccountCode(Long.parseLong(parts[0]));
      transferenciaDto.setDigitCode(parts[1]);
      transferenciaDto.setBranchCode(Integer.parseInt(tedRequest.getAgenciaFavorecido()));
      transferenciaDto.setInstitutionCode(Integer.parseInt(tedRequest.getBancoFavorecido()));
      transferenciaDto.setDocument(tedRequest.getNumDocumentoFavorecido());
      transferenciaDto.setName(tedRequest.getNomeFavorecido());
      if (tedRequest.getTipoContaFavorecido() != null
          && !tedRequest.getTipoContaFavorecido().isEmpty()) {
        if (Constantes.TIPO_CONTA_BANCARIA_CORRENTE.equals(tedRequest.getTipoContaFavorecido())) {
          transferenciaDto.setBankAccountType(Constantes.DESC_CONTA_CORRENTE);
        } else if (Constantes.TIPO_CONTA_BANCARIA_POUPANCA.equals(
            tedRequest.getTipoContaFavorecido())) {
          transferenciaDto.setBankAccountType(Constantes.DESC_CONTA_POUPANCA);
        }
      }
    }
    transferenciaDto.setValue(tedRequest.getValor());
    transferenciaDto.setExternalNSU(ted.getIdTed().toString());
    transferenciaDto.setExternalTerminal(ipOrigem);
    return transferenciaDto;
  }

  TEDRendimentoRequest preencherRequest(
      TEDRequest tedRequest,
      TransferenciaDto transferenciaDto,
      String nomeFavorecido,
      String idBanco,
      String agencia) {
    String[] partsAgencia = agencia.split("-");
    TEDRendimentoRequest tedRendimentoRequest = new TEDRendimentoRequest();
    tedRendimentoRequest.setBancoDoFavorecido(StringUtils.leftPad(idBanco, 3, "0"));
    tedRendimentoRequest.setAgenciaDoFavorecido(partsAgencia[0]);
    tedRendimentoRequest.setDigitoDaAgenciaDoFavorecido(
        partsAgencia.length > 1 ? partsAgencia[1] : "");
    tedRendimentoRequest.setContaDoFavorecido(transferenciaDto.getAccountCode().toString());
    tedRendimentoRequest.setDigitoDaContaDoFavorecido(transferenciaDto.getDigitCode());
    tedRendimentoRequest.setTipoContaFavorecido(TIPO_CONTA_FAVORECIDO);
    tedRendimentoRequest.setTipoPessoaFavorecido(
        tedRequest.getNumDocumentoFavorecido().length() > 11
            ? TIPO_PESSOA_FAVORECIDO_JURIDICO
            : TIPO_PESSOA_FAVORECIDO_FISICO);
    tedRendimentoRequest.setNomeDoFavorecido(
        nomeFavorecido.length() > 35 ? nomeFavorecido.substring(0, 35) : nomeFavorecido);
    tedRendimentoRequest.setCpfOuCnpjDoFavorecido(tedRequest.getNumDocumentoFavorecido());
    tedRendimentoRequest.setValorDaTransferencia(tedRequest.getValor());
    tedRendimentoRequest.setCodigoDaFinalidade("10");
    tedRendimentoRequest.setCodigoControleTransacao("");
    tedRendimentoRequest.setIdentificacaoDaOperacaoNoExtrato("");
    tedRendimentoRequest.setUrlParceiro("");
    return tedRendimentoRequest;
  }

  private boolean isInstituicaoComAlertaDeTED(Integer idInstituicao) {
    return idsInstituicoesAlertaValorLimiteTED.contains(idInstituicao);
  }

  private boolean isValorTEDMaiorIgualValorLimiteDisparoEmail(BigDecimal valorTED) {
    return valorTED.compareTo(VALOR_LIMITE_TED_DISPARO_EMAIL_AUTOMATICO) >= 0;
  }

  public TedRendimentoResponse efetuarTransferencia(
      HttpEntity<TEDRendimentoRequest> transferencia, InstituicaoPix instituicaoTransferencia) {
    logger.info(
        "Chamada da TED API do Rendimento"
            + this.rendimentoProperties.getUrlTed().toString()
            + transferencia.toString());
    String url =
        this.rendimentoProperties.getUrlTed()
            + "/"
            + instituicaoTransferencia.getAgencia()
            + "/"
            + instituicaoTransferencia.getConta()
            + this.rendimentoProperties.getEfetuarTed().getUri();
    return this.restTemplate
        .postForEntity(url, transferencia, TedRendimentoResponse.class)
        .getBody();
  }

  public TEDResponseVO pegarComprovanteTED(
      TEDComprovanteRequest tedComprovanteRequest, SecurityUserPortador userPortador) {

    if (tedComprovanteRequest.getRrn().isEmpty()) {
      throw new GenericServiceException(
          "ID da Transação não informado", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    if (tedComprovanteRequest.getIdConta() == null) {
      throw new GenericServiceException("idConta não informado", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            (tedComprovanteRequest.getIdConta()), userPortador);

    travaContasService.travaContas(conta.getIdConta(), Servicos.TED);

    TEDResponseVO tedResponse = new TEDResponseVO();

    try {
      tedResponse.setTed(
          tedRepository.findTedByIdTransacaoAndIdConta(
              tedComprovanteRequest.getRrn(), tedComprovanteRequest.getIdConta()));

      if (tedResponse.getTed() != null) {
        if (tedResponse
            .getTed()
            .getStatusTed()
            .equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
          tedResponse.setStatus(HttpStatus.OK.value());
          tedResponse.setStatusTED(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
          tedResponse.setTituloStatusModal(TED_EM_PROCESSAMENTO);
          tedResponse.setDescricaoStatus(StatusTransacao.AGUARDANDO_APROVACAO.getDescricao());
        } else if (tedResponse
            .getTed()
            .getStatusTed()
            .equals(StatusTransacao.TRANSFERIDO.getCodigo())) {
          if (tedResponse.getTed().getIdTransacaoRendimento() != null) {
            if (tedResponse.getTed().getComprovanteTedEfetuado() == null) {
              logger.warn("Comprovante TED Efetuado não foi gerado");
              tedResponse.setStatus(HttpStatus.UNPROCESSABLE_ENTITY.value());
              tedResponse.setTituloStatusModal(SEM_COMPROVANTE);
              tedResponse.setDescricaoStatus(
                  "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
            } else {
              String comprovante = tedResponse.getTed().getComprovanteTedEfetuado();
              tedResponse.setStatus(HttpStatus.OK.value());
              tedResponse.setStatusTED(StatusTransacao.TRANSFERIDO.getCodigo());
              tedResponse.setTituloStatusModal(TED_COM_SUCESSO);
              tedResponse.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());
              tedResponse.setComprovante(comprovante);
            }
          } else if (tedResponse.getTed().getTransactionId() != null) {
            String comprovante = tedResponse.getTed().getComprovante();
            tedResponse.setStatus(HttpStatus.OK.value());
            tedResponse.setStatusTED(StatusTransacao.TRANSFERIDO.getCodigo());
            tedResponse.setTituloStatusModal(TED_COM_SUCESSO);
            tedResponse.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());
            tedResponse.setComprovante(comprovante);
          }
        } else if (tedResponse
            .getTed()
            .getStatusTed()
            .equals(StatusTransacao.REPROVADO.getCodigo())) {
          tedResponse.setStatus(HttpStatus.OK.value());
          tedResponse.setStatusTED(StatusTransacao.REPROVADO.getCodigo());
          tedResponse.setTituloStatusModal(TED_ESTORNADA);
          tedResponse.setDescricaoStatus(StatusTransacao.REPROVADO.getDescricao());
          tedResponse.setMotivo(
              MotivoTEDEnum.valueOfCodigo(tedResponse.getTed().getIdMotivoRecusado())
                  .getDescricao());
        } else if (tedResponse
            .getTed()
            .getStatusTed()
            .equals(StatusTransacao.REJEITADO.getCodigo())) {
          tedResponse.setStatus(HttpStatus.OK.value());
          tedResponse.setStatusTED(StatusTransacao.REJEITADO.getCodigo());
          tedResponse.setTituloStatusModal(TED_ESTORNADA);
          tedResponse.setDescricaoStatus(
              "Não foi possível realizar a transação. A transação será estornada");
          tedResponse.setMotivo(
              MotivoTEDEnum.valueOfCodigo(tedResponse.getTed().getIdMotivoRecusado())
                  .getDescricao());
        }
      } else {
        tedResponse.setStatus(HttpStatus.NOT_FOUND.value());
        tedResponse.setTituloStatusModal(SEM_COMPROVANTE);
        tedResponse.setDescricaoStatus(
            "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        logger.warn("TED não encontrada para o idTransacao informado");
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
      e.printStackTrace();
      throw new GenericServiceException(e.getMessage());
    }

    return tedResponse;
  }

  public TedConsultaStatusRendimentoProcResponse consultarStatusTedAndConfirmarRendimento(TEDVO ted)
      throws IOException {

    ContaPagamento contaPagamento = contaPagamentoService.findByIdNotNull(ted.getIdConta());

    TedConsultaStatusRendimentoResponse consultaStatusTedResponse =
        new TedConsultaStatusRendimentoResponse();
    consultaStatusTedResponse = consultarStatusPagamento(ted);

    return alterarStatusAndGeraComprovanteTed(consultaStatusTedResponse, ted, contaPagamento);
  }

  public TedConsultaStatusRendimentoProcResponse alterarStatusAndGeraComprovanteTed(
      TedConsultaStatusRendimentoResponse consultaStatusTedResponse,
      TEDVO ted,
      ContaPagamento contaPagamento) {

    TED tedCliente = new TED();
    TedConsultaStatusRendimentoProcResponse procResponse =
        new TedConsultaStatusRendimentoProcResponse();

    tedCliente = tedRepository.findTedByIdTransacaoAndIdConta(ted.getRrn(), ted.getIdConta());

    if (Objects.nonNull(consultaStatusTedResponse.getValue())
        && consultaStatusTedResponse.getIsSuccess()) {
      LogTransacoesVO logTransacoes = new LogTransacoesVO();
      ConsultaTedValue tedResponse = new ConsultaTedValue();
      JcardResponse jcardResponse = new JcardResponse();
      tedResponse = consultaStatusTedResponse.getValue();
      String status = tedResponse.getStatus();
      Integer idMotivo = null;

      status = status.toLowerCase();

      logger.info("Status TED Rendimento: " + tedResponse.getStatus());

      String[] statusSplited = status.split("-");

      if (statusSplited.length > 1) {
        try {
          //                    Devido ao erro inesperado no rendimento, é necessário validar essa
          // nova mensagem que apareceu.
          //                    Com isso evita erro em prod e realiza estorno
          if (statusSplited[1].toLowerCase().contains("falha ao incluir transferencia")) {
            status = StatusPagamentoTedRendimentoEnum.REJEITADO.getStatus();
            idMotivo = MotivoTEDEnum.FALHA_AO_INCLUIR_TRANSFERENCIA.getCodigo();
          } else {
            status = statusSplited[0];
            idMotivo = Integer.parseInt(statusSplited[1].replace(" ", ""));
          }
        } catch (NumberFormatException e) {
          status = StatusPagamentoTedRendimentoEnum.REJEITADO.getStatus();
          idMotivo = MotivoTEDEnum.MOTIVO_NAO_INFORMADO.getCodigo();
        }
      } else {
        status = statusSplited[0];
        idMotivo = MotivoTEDEnum.MOTIVO_NAO_INFORMADO.getCodigo();
      }

      status = status.replace(" ", "");

      HierarquiaInstituicaoVO instituicao =
          controleGarantiaFacade.getInstituicao(
              contaPagamento.getIdProcessadora(), contaPagamento.getIdInstituicao());

      ProdutoInstituicaoConfiguracao produto =
          lancamentoService.getProduto(
              contaPagamento.getIdProcessadora(),
              contaPagamento.getIdInstituicao(),
              contaPagamento.getIdProdutoInstituicao());
      CodigoTransacao codigoTransacao =
          codigoTransacaoService.findOneByCodTransacao(tedCliente.getCodTransacao());
      if (codigoTransacao == null) {
        throw new NullPointerException("Código da transação não encontrado");
      }

      if (status.equals(StatusPagamentoTedRendimentoEnum.EFETIVADO.getStatus())) {
        procResponse.setIsSuccess(true);
        procResponse.setIsFailure(false);
        if (tedCliente.getStatusTed().equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
          tedCliente.setStatusTed(StatusTransacao.TRANSFERIDO.getCodigo());
          tedCliente.setDataHoraStatus(new Date());
          tedCliente.setResponseMessage("Sucesso");
          tedCliente.setComprovanteTedEfetuado(
              montarComprovanteTedConfirmada(tedCliente, instituicao));
          procResponse.setMessage("TED foi transferida com sucesso!");
        } else {
          procResponse.setMessage("Status da TED não foi alterado!");
          logger.info("TED já estava Aprovada e o status se manteve.");
        }
      } else if (status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
          || status.equals(StatusPagamentoTedRendimentoEnum.REJEITADO.getStatus())) {

        if (!Objects.equals(idMotivo, MotivoTEDEnum.valueOfCodigo(idMotivo).getCodigo())) {
          idMotivo = MotivoTEDEnum.MOTIVO_NAO_INFORMADO.getCodigo();
        }

        String mensagem =
            status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                ? "TED Recusada - Id Ted: "
                    + tedCliente.getIdTed()
                    + " - Motivo: "
                    + MotivoTEDEnum.valueOfCodigo(idMotivo).getDescricao()
                : "TED Rejeitada pelo rendimento - Transação barrada pela regra de negócio";

        logger.warn(mensagem);

        if (tedCliente.getRrnEstorno() == null) {
          try {
            logger.info("Realizando estorno");
            jcardResponse =
                realizarEstorno(
                    ted.getRrn(),
                    contaPagamento,
                    produto,
                    tedCliente.getValorTransferencia(),
                    codigoTransacao,
                    "TED recusada ou rejeitada");

            if (jcardResponse.getSuccess()) {
              logger.info("TED Estornada com sucesso.");

              aplicativoMensagemService.enviarMensagemPosProcessamentoProc(
                  contaPagamento, produto, true);

              tedCliente.setRrnEstorno(jcardResponse.getRrn());
              tedCliente.setStatusTed(
                  status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                      ? StatusTransacao.REPROVADO.getCodigo()
                      : StatusTransacao.REJEITADO.getCodigo());
              tedCliente.setDataHoraStatus(new Date());
              tedCliente.setResponseMessage("Sucesso");
              tedCliente.setIdMotivoRecusado(MotivoTEDEnum.valueOfCodigo(idMotivo).getCodigo());
              tedCliente.setComprovanteTedEfetuado(null);
              procResponse.setMessage("TED Estornada com sucesso.");
            } else {
              logger.warn("Estorno não foi realizado. Validando motivo.");
              throw new JcardServiceException(jcardResponse.getErrors());
            }
          } catch (JcardServiceException e) {
            if (e.getMensagem().contains(PREVIOUSLY_VOIDED)) {

              logTransacoes = logTransacoesService.findTransacaoByRrn(tedCliente.getRrn());

              tedCliente.setRrnEstorno(logTransacoes.getRrn());
              tedCliente.setStatusTed(
                  status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                      ? StatusTransacao.REPROVADO.getCodigo()
                      : StatusTransacao.REJEITADO.getCodigo());
              tedCliente.setDataHoraStatus(new Date());
              tedCliente.setResponseMessage("Sucesso");
              tedCliente.setIdMotivoRecusado(MotivoTEDEnum.valueOfCodigo(idMotivo).getCodigo());
              tedCliente.setMensagemErroEstorno(e.getMensagem());

              logger.info(
                  "TED ja foi estornada anteriormente. Salvando status e seguindo fluxo normal.");
              procResponse.setMessage("TED ja foi estornada anteriormente");
            } else {
              String erroJcard =
                  "Errors: "
                      + jcardResponse.getErrors()
                      + "\n"
                      + "Mensagem: "
                      + e.getMensagem()
                      + "\n"
                      + "Detalhes: "
                      + e.getDetalhes();

              logger.warn("Estorno TED não realizado!");
              logger.info("Salvando erro Jcard e voltando status ted para 'AGUARDANDO APROVAÇÃO'");
              tedCliente.setMensagemErroEstorno(e.getMensagem());
              tedCliente.setStatusTed(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
              tedCliente.setDataHoraStatus(new Date());

              logger.error(erroJcard);
              procResponse.setMessage("Não foi possível realizar estorno. Valide o log do jcard");
            }
          }
        } else {
          logTransacoes = logTransacoesService.findTransacaoByRrn(tedCliente.getRrn());

          logger.warn("Estorno não foi realizado. TED já foi estornada anteriormente");

          tedCliente.setRrnEstorno(logTransacoes.getRrn());
          tedCliente.setStatusTed(
              status.equals(StatusPagamentoTedRendimentoEnum.RECUSADO.getStatus())
                  ? StatusTransacao.REPROVADO.getCodigo()
                  : StatusTransacao.REJEITADO.getCodigo());
          tedCliente.setDataHoraStatus(new Date());
          tedCliente.setResponseMessage("Sucesso");
          tedCliente.setIdMotivoRecusado(MotivoTEDEnum.valueOfCodigo(idMotivo).getCodigo());
          procResponse.setMessage("TED já foi estornada anteriormente");
        }

        procResponse.setIsSuccess(true);
        procResponse.setIsFailure(false);
      } else if (status.equals(StatusPagamentoTedRendimentoEnum.ERRO_CONSULTA.getStatus())) {
        logger.warn(
            "Erro na consulta. Será realizada uma nova consulta na próxima rodagem da proc.");
        // Determina valores para alteração do registro de TED.
        tedCliente.setDataHoraStatus(new Date());
        tedCliente.setResponseMessage(consultaStatusTedResponse.getErroMessage().getMessage());
        tedCliente.setIdMotivoRecusado(MotivoTEDEnum.valueOfCodigo(idMotivo).getCodigo());
        // Constrói objeto que retornará para a PROC.
        procResponse.setIsSuccess(true);
        procResponse.setIsFailure(false);
        procResponse.setMessage(consultaStatusTedResponse.getErroMessage().getMessage());
      }
      tedRepository.save(tedCliente);
      procResponse.setValue(tedCliente);
    } else {

      Integer status =
          consultaStatusTedResponse != null
                  && consultaStatusTedResponse.getErroMessage() != null
                  && consultaStatusTedResponse.getErroMessage().getStatusCode() != null
                  && consultaStatusTedResponse.getErroMessage().getStatusCode() != 0
              ? consultaStatusTedResponse.getErroMessage().getStatusCode()
              : HttpStatus.INTERNAL_SERVER_ERROR.value();
      String mensagem =
          consultaStatusTedResponse != null && consultaStatusTedResponse.getErroMessage() != null
              ? consultaStatusTedResponse.getErroMessage().getErrors()[0].getMessage()
              : consultaStatusTedResponse != null
                      && consultaStatusTedResponse.getErroMessage().getMessage() != null
                  ? consultaStatusTedResponse.getErroMessage().getMessage()
                  : "Nada informado.";

      tedCliente.setDataHoraStatus(new Date());
      tedCliente.setStatusTed(StatusTransacao.ERRO.getCodigo());
      tedCliente.setResponse_code(status.toString());
      tedCliente.setResponseMessage(mensagem);
      tedRepository.save(tedCliente);

      logger.warn(
          "Objeto Response da consulta nulo ou vazio.\n"
              + consultaStatusTedResponse.getErroMessage());
      procResponse.setIsFailure(true);
      procResponse.setIsSuccess(false);
      procResponse.setValue(tedCliente);
      procResponse.setMessage(
          "Objeto Response da consulta nulo ou vazio.\n"
              + consultaStatusTedResponse.getErroMessage().getMessage());
    }
    return procResponse;
  }

  private TedConsultaStatusRendimentoResponse consultarStatusPagamento(TEDVO ted)
      throws IOException, JsonProcessingException {

    ResponseEntity<TedConsultaStatusRendimentoResponse> response = null;

    try {

      this.rendimentoProperties = new RendimentoProperties();

      RendimentoProperties.EndPoint endPoint = new RendimentoProperties.EndPoint();
      endPoint.setUrl(Constantes.URL_RENDIMENTO_V2_PAGAMENTOS_TED);
      endPoint.setUri(Constantes.URI_RENDIMENTO_CONSULTA_STATUS_TED);
      this.rendimentoProperties.setConsultarStatusTed(endPoint);

      this.rendimentoProperties.setUrl(urlBancoRendimento);

      InstituicaoPix chaveInstituicao =
          instituicaoPixService.findFirstByIdInstituicaoOrderById(
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
      String autenticacaoSensedia =
          bancoRendimentoClient.obterAccessToken(chaveInstituicao.getIdInstituicao());
      this.rendimentoProperties.setAutenticacao(autenticacaoSensedia);

      HttpHeaders headers =
          gatewayPagtoExternoService.getHttpHeaders(
              chaveInstituicao, rendimentoProperties.getAutenticacao());

      HttpEntity<TedRendimentoResponse> entity = new HttpEntity<TedRendimentoResponse>(headers);

      logger.info(
          String.format(
              "Iniciando consulta de TED da conta %d com ID Rendimento %s e status atual %s.",
              ted.getIdConta(), ted.getIdTransacaoRendimento(), ted.getStatusTed()));

      response =
          restTemplate.exchange(
              this.rendimentoProperties.getUrl()
                  + this.rendimentoProperties.getConsultarStatusTed().getUrl()
                  + "/"
                  + chaveInstituicao.getAgencia()
                  + "/"
                  + chaveInstituicao.getConta()
                  + this.rendimentoProperties.getConsultarStatusTed().getUri()
                  + ted.getIdTransacaoRendimento(),
              HttpMethod.GET,
              entity,
              TedConsultaStatusRendimentoResponse.class);

      logger.info(
          response != null
                  && response.getBody() != null
                  && response.getBody().getValue() != null
                  && response.getBody().getValue().getStatus() != null
              ? String.format(
                  "A resposta foi recebida e o status recebido é %s. Partindo para a etapa de atualização do status.",
                  response.getBody().getValue().getStatus())
              : "A resposta não foi recebida como o esperado.");

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      logger.info("Falha ao receber resposta do Banco Rendimento. Detalhes: ");
      logger.info(e.getResponseBodyAsString());
      ObjectMapper objectMapper = new ObjectMapper();
      TedConsultaStatusRendimentoResponse tedRendimentoResponse = null;
      try {
        tedRendimentoResponse =
            objectMapper.readValue(
                e.getResponseBodyAsString(), TedConsultaStatusRendimentoResponse.class);

        if (tedRendimentoResponse != null
            && tedRendimentoResponse.getErroMessage() != null
            && tedRendimentoResponse.getErroMessage().getErrors() != null
            && tedRendimentoResponse.getErroMessage().getErrors().length > 0
            && tedRendimentoResponse.getErroMessage().getErrors()[0].getMessage() != null
            && tedRendimentoResponse
                .getErroMessage()
                .getErrors()[0]
                .getMessage()
                .contains("Transação não encontrada.")) {
          tedRendimentoResponse.setIsSuccess(Boolean.FALSE);
          tedRendimentoResponse.setIsFailure(Boolean.TRUE);
          tedRendimentoResponse.setValue(null);
        }
      } catch (JsonParseException | JsonMappingException ex) {
        tedRendimentoResponse = new TedConsultaStatusRendimentoResponse();
        tedRendimentoResponse.setIsSuccess(Boolean.TRUE);
        tedRendimentoResponse.setIsFailure(Boolean.FALSE);
        tedRendimentoResponse.setValue(new ConsultaTedValue());
        tedRendimentoResponse.getValue().setStatus("Erro Consulta - 997");
        tedRendimentoResponse.setErroMessage(new TedConsultaStatusRendimentoResponse.ErroMessage());
        tedRendimentoResponse.getErroMessage().setStatusCode(500);
        tedRendimentoResponse
            .getErroMessage()
            .setMessage("Não foi possível traduzir o objeto de retorno recebido.");
      }

      return tedRendimentoResponse;
    }

    return response.getBody();
  }
}
