package br.com.entity.cadastral;

import br.com.entity.transacional.CodigoTransacao;
import br.com.sinergico.util.Util;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;

@Entity
@Getter
@Setter
@Table(name = "produto_transacao", schema = "cadastral")
public class ProdutoTransacao implements Serializable {

  private static final long serialVersionUID = 4793582637004604704L;

  @EmbeddedId private ProdutoTransacaoId id;

  @Column(name = "desc_reduzida")
  private String descReduzida;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumns({
    @JoinColumn(
        name = "cod_transacao",
        referencedColumnName = "cod_transacao",
        insertable = false,
        updatable = false),
  })
  private CodigoTransacao codigoTransacao;

  @Type(type = "numeric_boolean")
  @Column(name = "ativa")
  private Boolean ativa;

  @Column(name = "id_usuario_manutencao")
  private Integer idUsuarioManutencao;

  @Column(name = "id_usuario_inclusao")
  private Integer idUsuarioInclusao;

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "dt_hr_inclusao")
  private Date dtHrInclusao;

  @Column(name = "descpushnotification")
  private String descPushNotification;

  @Column(name = "vrminimopushnotification")
  private BigDecimal vrMinimoPushNotification;

  @Column(name = "statusvrminimopushnotification")
  private Short statusVrMinPush;

  @Column(name = "statuspushnotification")
  private Short statusPush;

  @Column(name = "desc_sms_notification")
  private String descricaoSms;

  @Column(name = "status_vr_minimo_sms_notification")
  private Short statusValorMinimoSms;

  @Column(name = "vr_minimo_sms_notification")
  private BigDecimal valorMinimoSms;

  @Column(name = "status_sms_notification")
  private Short statusSms;

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }
}
