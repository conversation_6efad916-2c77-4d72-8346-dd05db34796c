package br.com.sinergico.service.suporte;

import br.com.entity.cadastral.Credencial;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioUnico;
import br.com.entity.suporte.GatewaySMS;
import br.com.entity.suporte.LogEventoConta;
import br.com.entity.suporte.LogSMS;
import br.com.entity.suporte.TokenAcesso;
import br.com.entity.suporte.TokenFuncionalidade;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.TelefoneCelularPessoaResponse;
import br.com.json.bean.enums.MecanismosAutenticacao;
import br.com.json.bean.suporte.ComunicadoConta;
import br.com.json.bean.suporte.ComunicadoContaViaSMS;
import br.com.json.bean.suporte.GerarTokenAcessoRequest;
import br.com.json.bean.suporte.GerarTokenCadastroLogin;
import br.com.json.bean.suporte.GerarTokenFuncionalidade;
import br.com.json.bean.suporte.LoginUnicoResponseDTO;
import br.com.json.bean.suporte.UtilizarTokenAcessoRequest;
import br.com.sinergico.repository.cadastral.ProdutoPlataformaRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.repository.suporte.TokenAcessoRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TokenAcessoService extends GenericService<TokenAcesso, Long> {

  private static final int TEMPO_EXPIRACAO_TOKEN_CADASTRO_LOGIN = 5;

  private static final Integer TAMANHO_TOKEN_CADASTRO_LOGIN = 6;

  private static final Integer TIPO_EVENTO_TOKEN_CADASTRO_LOGIN = 7;
  private static final int MAX_CEL = 999999999;

  private static final int MIN_CEL = 910000000;
  private static final Integer MIN_DDD = 11;
  public static final String SUCESSO = "Token enviado com sucesso.";

  private static final Integer MAX_DDD = 99;
  private TokenAcessoRepository tokenRepository;

  @Autowired private AgenteComunicadorPortadorService agenteComunicadorService;

  @Autowired private LogEventoContaService logEventoContaService;

  @Autowired private GatewaySMSService gatewaySMSService;

  @Autowired private LogSMSService logSMSService;

  @Autowired private HierarquiaInstituicaoRepository instituicaoRepository;

  @Autowired private ProdutoPlataformaRepository prodPlatRepository;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private TokenFuncionalidadeService tokenFuncionalidadeService;

  @Autowired private AcessoTemporarioB2BService acessoTemporarioB2BService;

  @Autowired private AcessoUsuarioUnicoService acessoUsuarioUnicoService;

  @Autowired private AcessoLogService acessoLogService;

  @Autowired private UtilService utilService;

  public static final String TOKEN_SOLICITADO_CURTO_PERIODO_TEMPO =
      "Já foi solicitado um token para o seu usuário. Por favor aguarde alguns instantes para solicitar novamente";

  private static Logger log = LoggerFactory.getLogger(TokenAcessoService.class);

  @Autowired
  public TokenAcessoService(TokenAcessoRepository repo) {
    super(repo);
    tokenRepository = repo;
  }

  @Transactional
  public TokenAcesso gerarTokenCadastroLoginBackoffice(
      GerarTokenCadastroLogin gerarTokenCadastroLogin) {

    // validarCelularPrimeiroAcesso(gerarTokenCadastroLogin);

    GerarTokenAcessoRequest model = new GerarTokenAcessoRequest();
    Integer minutosExpiracao = gerarTokenCadastroLogin.getMinutosExpiracao();
    model.setChaveExterna(gerarTokenCadastroLogin.getChave());
    model.setLetras(false);
    model.setNumeros(true);
    model.setTamanho(TAMANHO_TOKEN_CADASTRO_LOGIN);
    boolean minutosExpiracaoValido =
        minutosExpiracao != null && minutosExpiracao <= 60 && minutosExpiracao > 0;
    model.setPeriodoExpiracao(
        minutosExpiracaoValido ? minutosExpiracao : TEMPO_EXPIRACAO_TOKEN_CADASTRO_LOGIN);
    model.setMedidaTempo(DateUtil.MINUTOS);
    TokenAcesso tokenAcesso = gerarToken(model);
    String tokenGerado = tokenAcesso.getToken();
    montarEEnviarSMS(tokenGerado, gerarTokenCadastroLogin);
    return tokenAcesso;
  }

  @Transactional
  public TokenAcesso gerarTokenCadastroLogin(GerarTokenCadastroLogin model1) {

    validarCelularPrimeiroAcesso(model1);

    GerarTokenAcessoRequest model = new GerarTokenAcessoRequest();
    model.setChaveExterna(model1.getChave());
    model.setLetras(false);
    model.setNumeros(true);
    model.setTamanho(TAMANHO_TOKEN_CADASTRO_LOGIN);
    model.setPeriodoExpiracao(
        model1.getMinutosExpiracao() != null
            ? model1.getMinutosExpiracao()
            : TEMPO_EXPIRACAO_TOKEN_CADASTRO_LOGIN);
    model.setMedidaTempo(DateUtil.MINUTOS);
    TokenAcesso tokenAcesso = gerarToken(model);
    String tokenGerado = tokenAcesso.getToken();
    montarEEnviarSMS(tokenGerado, model1);
    return tokenAcesso;
  }

  /**
   * Método utilizado para fazer a validação do celular fornecido para o 1º acesso, o qual deve ser
   * igual ao celular já cadastrado.
   *
   * @param tokenCadastro
   */
  private void validarCelularPrimeiroAcesso(GerarTokenCadastroLogin tokenCadastro) {
    validarBoaFormacaoNumeroCelular(tokenCadastro.getCelular());
    if (tokenCadastro.isValidarCelularPrimeiroAcesso()) {
      Credencial credencial = credencialService.findById(tokenCadastro.getIdCredencial());
      TelefoneCelularPessoaResponse celularCadastrado =
          pessoaService.findTelefoneCelularByIdPessoa(credencial.getIdPessoa());
      if (isNotCelularCadastrado(tokenCadastro.getCelular(), celularCadastrado)) {
        throw new GenericServiceException(
            "Celular não cadastrado: "
                + tokenCadastro.getCelular()
                + " - O número do celular informado deve ser o mesmo do informado cadastro.");
      }
    }
  }

  private void validarBoaFormacaoNumeroCelular(String celularCompleto) {
    Integer ddd = null;
    Integer numero = null;
    try {
      ddd = new Integer(celularCompleto.substring(0, 2));
      numero = new Integer(celularCompleto.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celularCompleto + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
  }

  private boolean isNotCelularCadastrado(
      String celularEnviado, TelefoneCelularPessoaResponse celularCadastrado) {
    String celularCadastradoAsString =
        celularCadastrado.getDddTelefoneCelular().toString()
            + celularCadastrado.getTelefoneCelular().toString();
    return !celularEnviado.equals(celularCadastradoAsString);
  }

  private void montarEEnviarSMS(String token, GerarTokenCadastroLogin model1) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    if (model1.getIdConta() != null) {
      comunicado.setIdConta(model1.getIdConta());
    }
    GatewaySMS gateway = getGatewaySMS(model1.getIdProcessadora(), model1.getIdInstituicao());

    comunicado.setIdCredencial(model1.getIdCredencial());
    comunicado.setIdGatewaySMS(gateway.getIdGatewaySMS());
    String mensagem = getMensagem(gateway, model1, token);
    comunicado.setMensagem(mensagem);
    comunicado.setTipoEventoConta(TIPO_EVENTO_TOKEN_CADASTRO_LOGIN);

    Long celularNumero = null;
    String celular = model1.getCelular();
    Integer ddd = null;
    Integer restante = null;

    try {
      celularNumero = new Long(celular);

      ddd = new Integer(celular.substring(0, 2));
      restante = new Integer(celular.substring(2));
    } catch (Exception ex) {
      throw new GenericServiceException(
          "Celular invalido." + celular + " Envie DDD+Celular(ex:119xxxxxxxx)");
    }
    if (!isCelularValido(ddd, restante)) {
      throw new GenericServiceException(
          "Celular invalido." + celular + "Envie DDD+Celular(ex:119xxxxxxxx)");
    }

    Date umaHoraAtras = new Date(System.currentTimeMillis() - TimeUnit.HOURS.toMillis(1));

    List<LogSMS> logSMS =
        logSMSService.encontraSMSDeTipoEventoEspecificoEnviadasParaNumeroAposDataHora(
            celularNumero, umaHoraAtras);

    Long quantidadeDeSMSLimite =
        parametroValorService.findValorParametroLong(utilService.getParametroValorLimiteSms());

    if (logSMS != null && !logSMS.isEmpty() && logSMS.size() >= quantidadeDeSMSLimite) {
      throw new GenericServiceException(
          "Quantidade máxima de SMS enviadas na última hora! Aguarde para tentar novamente.");
    }

    comunicado.setNumeroCelular(celularNumero);
    LogEventoConta logEvento =
        getLogEventoConta(comunicado, gateway.getIdProcessadora(), gateway.getIdInstituicao());

    // registro o evento
    logEvento = logEventoContaService.registrarEventoContaImediato(logEvento);

    comunicado.setIdLogEventoConta(logEvento.getIdLogEventoConta());
    LogEventoConta logConta = logEventoContaService.findById(comunicado.getIdLogEventoConta());

    if (logConta == null) {
      throw new GenericServiceException(
          "LogEventoConta não encontrado.",
          "IdLogEventoConta: " + comunicado.getIdLogEventoConta());
    }

    LogSMS logSms = new LogSMS();
    BeanUtils.copyProperties(comunicado, logSms);
    logSms.setNumeroCelular(new Long(celular));

    logSms.setDataHoraRegistro(new Date());
    logSms.setDataHoraAgendamento(new Date());
    logSms.setStatus(ComunicadorPortadorSMSService.PEND_ENVIO);
    logSms.setIdTipoEventoConta(logConta.getIdTipoEventoConta());
    logSms = logSMSService.save(logSms);

    comunicado.setIdLogEventoConta(logSms.getIdLogEventoConta());

    log.debug("IdLogEvento: " + logSms.getIdLogEventoConta());

    enviarSms(comunicado);
  }

  @Transactional
  public TokenAcesso gerarTokenApp(GerarTokenCadastroLogin gerarTokenCadastroLogin) {

    validarCelularPrimeiroAcesso(gerarTokenCadastroLogin);

    GerarTokenAcessoRequest model = new GerarTokenAcessoRequest();
    Integer minutosExpiracao = gerarTokenCadastroLogin.getMinutosExpiracao();
    model.setChaveExterna(gerarTokenCadastroLogin.getChave());
    model.setLetras(false);
    model.setNumeros(true);
    model.setTamanho(TAMANHO_TOKEN_CADASTRO_LOGIN);
    boolean minutosExpiracaoValido =
        minutosExpiracao != null && minutosExpiracao <= 60 && minutosExpiracao > 0;
    model.setPeriodoExpiracao(
        minutosExpiracaoValido ? minutosExpiracao : TEMPO_EXPIRACAO_TOKEN_CADASTRO_LOGIN);
    model.setMedidaTempo(DateUtil.MINUTOS);
    //		String tokenGerado = tokenAcesso.getToken();
    //		montarEEnviarSMS(tokenGerado, gerarTokenCadastroLogin);
    return gerarToken(model);
  }

  private String getMensagem(GatewaySMS gateway, GerarTokenCadastroLogin model1, String token) {
    StringBuilder texto = new StringBuilder();
    String descInstituicao =
        instituicaoRepository.getDescInstituicao(
            gateway.getIdProcessadora(), gateway.getIdInstituicao());
    texto.append(descInstituicao != null ? descInstituicao + ":" : "");
    texto.append(" Prezado cliente, use o código de validação ");
    texto.append(token);
    texto.append(" para prosseguir");
    if (model1.getIdConta() != null
        && (model1.getIdInstituicao() == null
            || !Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(model1.getIdInstituicao()))) {
      String descProdPlat =
          prodPlatRepository.getDescProdutoPlataformaByIdConta(model1.getIdConta());
      texto.append(" ");
      texto.append(descProdPlat);
    }
    texto.append(".");
    return texto.toString();
  }

  private boolean isCelularValido(Integer dddTelefoneCelular, Integer telefoneCelular) {
    return (dddTelefoneCelular >= MIN_DDD && dddTelefoneCelular <= MAX_DDD)
        && (telefoneCelular > MIN_CEL && telefoneCelular < MAX_CEL);
  }

  private LogEventoConta getLogEventoConta(
      ComunicadoContaViaSMS comunicado, Integer idProcessadora, Integer idInstituicao) {
    LogEventoConta evento = getLogEventoConta(comunicado);
    evento.setIdProcessadora(idProcessadora);
    evento.setIdInstituicao(idInstituicao);
    return evento;
  }

  private GatewaySMS getGatewaySMS(Integer idProcessadora, Integer idInstituicao) {
    GatewaySMS gatewaySMS =
        gatewaySMSService.findFirstByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (gatewaySMS == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar gatewaySMS. IdProcessadora: "
              + idProcessadora
              + " , idInstituicao: "
              + idInstituicao);
    }
    return gatewaySMS;
  }

  private <Req extends ComunicadoConta> LogEventoConta getLogEventoConta(Req comunicado) {
    LogEventoConta logEvento = new LogEventoConta();
    logEvento.setIdConta(comunicado.getIdConta());
    logEvento.setIdCredencial(comunicado.getIdCredencial());
    logEvento.setTipoEvento(comunicado.getTipoEventoConta());
    return logEvento;
  }

  private void enviarSms(ComunicadoContaViaSMS comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  public Boolean utilizarToken(UtilizarTokenAcessoRequest model) {
    List<TokenAcesso> tokenAcessoList =
        findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            model.getChaveExterna(), model.getToken());

    // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
    // nao exista mesmo
    if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
      throw new GenericServiceException(
          "Código inválido.",
          "Verifique o código digitado ou solicite um novo",
          HttpStatus.UNAUTHORIZED);
    }

    validarDataValidade(tokenAcessoList.get(0));

    tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());

    return save(tokenAcessoList.get(0)) != null;
  }

  /**
   * Funcao que valida o token para viabilizar o cadastro de um portador inMais
   *
   * @param model
   * @param ig
   * @return
   */
  public Boolean utilizarTokenELiberarLog(UtilizarTokenAcessoRequest model, String ig) {
    List<TokenAcesso> tokenAcessoList =
        findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            model.getChaveExterna(), model.getToken());

    // se nao encontrar pode ser que ja tenha sido cancelado ou utilizado ou
    // nao exista mesmo
    if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
      throw new GenericServiceException(
          "Código inválido.",
          "Verifique o código digitado ou solicite um novo",
          HttpStatus.UNAUTHORIZED);
    }

    validarDataValidade(tokenAcessoList.get(0));

    tokenAcessoList.get(0).setDataHoraUtilizacao(LocalDateTime.now());

    return save(tokenAcessoList.get(0)) != null;
  }

  private void validarDataValidade(TokenAcesso tokenAcesso) {
    if (LocalDateTime.now().isAfter(tokenAcesso.getDataHoraExpiracaoToken())) {
      throw new GenericServiceException(
          "Código expirado. Solicite um novo", "Código expirado solicite um novo");
    }
  }

  public synchronized TokenAcesso gerarToken(GerarTokenAcessoRequest model) {

    String tokenGerado = null;

    // ja existe um token gerado para essa chave que ainda nao foi utilizado
    // e nem cancelado

    // adicionar expirada
    //		List<TokenAcesso> tokenAcessoList =
    // findByChaveExternaAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
    //				model.getChaveExterna());

    // se sim entao devolva me
    //		if (tokenAcessoList != null && !tokenAcessoList.isEmpty()) {
    //			//cancela token e gera nova
    //			cancelarTokensAntigos(tokenAcessoList);
    //		}

    // se nao entao gera um novo pra mim
    tokenGerado = getTokenGerado(model);
    return saveTokenGerado(model, tokenGerado);
  }

  private void cancelarTokensAntigos(List<TokenAcesso> tokenAcessoList) {
    for (TokenAcesso tokenAcesso : tokenAcessoList) {
      tokenAcesso.setDataHoraCancelamentoChave(LocalDateTime.now());
    }
    saveAll(tokenAcessoList);
  }

  private TokenAcesso saveTokenGerado(GerarTokenAcessoRequest model, String tokenGerado) {
    TokenAcesso tokenAcesso = new TokenAcesso();
    tokenAcesso.setChaveExterna(model.getChaveExterna());
    LocalDateTime agora = LocalDateTime.now();
    Date agoraDate = DateUtil.localDateTimeToDate(agora);

    tokenAcesso.setDataHoraGeracao(agora);
    tokenAcesso.setToken(tokenGerado);

    Date tempoAcrescentado =
        DateUtil.aumentar(agoraDate, model.getPeriodoExpiracao(), model.getMedidaTempo());
    tokenAcesso.setDataHoraExpiracaoToken(DateUtil.dateToLocalDateTime(tempoAcrescentado));
    save(tokenAcesso);
    return tokenAcesso;
  }

  private String getTokenGerado(GerarTokenAcessoRequest model) {
    String tokenGerado;
    if (model.isLetras() && model.isNumeros()) {
      tokenGerado = Util.generateRandomLetrasENumerosUpperCase(model.getTamanho());
    } else if (model.isLetras()) {
      tokenGerado = Util.generateRandomLetrasUpperCase(model.getTamanho());
    } else {
      tokenGerado = Util.generateRandomNumeros(model.getTamanho());
    }
    return tokenGerado;
  }

  public List<TokenAcesso>
      findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
          String chaveExterna, String token) {
    return tokenRepository
        .findByChaveExternaAndTokenAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNullOrderByDataHoraGeracaoDesc(
            chaveExterna, token);
  }

  public TokenAcesso findFirstByChaveExternaOrderByDataHoraGeracaoDesc(String chaveExterna) {
    return tokenRepository.findFirstByChaveExternaOrderByDataHoraGeracaoDesc(chaveExterna);
  }

  public List<TokenAcesso>
      findByChaveExternaAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
          String chaveExterna) {
    return tokenRepository
        .findByChaveExternaAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoChaveIsNull(
            chaveExterna);
  }

  public TokenAcesso findByChaveExternaAndDataHoraUtilizacao(
      String chaveExterna, LocalDateTime dataHoraUtilizacao) {
    return tokenRepository.findByChaveExternaAndDataHoraUtilizacao(
        chaveExterna, dataHoraUtilizacao);
  }

  public ResponseEntity<?> gerarTokenAcessoLoginIssuerPorUsuario(
      String login, String senha, HttpServletRequest request, boolean isB2b, Integer tipoB2b)
      throws GenericServiceException {
    HashMap<String, Object> map = new HashMap<>();
    ResponseEntity<?> validacaoUsuario =
        acessoUsuarioService.validarAcessoUsuario(login, senha, request, map, isB2b, tipoB2b);
    if (validacaoUsuario != null) {
      return validacaoUsuario;
    }
    AcessoUsuario acessoUsuario = acessoUsuarioService.findByLoginSimples(login.toUpperCase());
    if (!MecanismosAutenticacao.VALLOO_AUTHENTICATOR
        .getCodigo()
        .equals(acessoUsuario.getIdMecanismoAutenticacao())) {
      ResponseEntity<?> validacrGeracaoToken = validarGeracaoToken(acessoUsuario);
      if (validacrGeracaoToken != null) {
        return validacrGeracaoToken;
      }
      gerarTokenFuncionalidade(acessoUsuario);
    }
    map.put("sucesso", true);
    acessoUsuarioService.saveAcessoLog(acessoUsuario, request, HttpStatus.OK, SUCESSO);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  private ResponseEntity<?> validarGeracaoToken(AcessoUsuario acessoUsuario) {
    if (utilService.isAmbienteHomologacao()) {
      return null;
    }
    HashMap<String, Object> map = new HashMap<>();
    List<TokenFuncionalidade> tokenFuncionalidadeList =
        tokenFuncionalidadeService
            .findTokenGeradosPeriodoTempoNaoUtilizadosPorUsuarioIssuerFuncionalidadesLogin(
                acessoUsuario.getIdUsuario());
    if (tokenFuncionalidadeList != null && !tokenFuncionalidadeList.isEmpty()) {
      map.put("msg", TOKEN_SOLICITADO_CURTO_PERIODO_TEMPO);
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    return null;
  }

  private ResponseEntity<?> validarGeracaoToken(String cpf) {
    if (utilService.isAmbienteHomologacao()) {
      return null;
    }
    HashMap<String, Object> map = new HashMap<>();
    List<TokenFuncionalidade> tokenFuncionalidadeList =
        tokenFuncionalidadeService
            .findTokenGeradosPeriodoTempoNaoUtilizadosPorCpfSuperUsuarioFuncionalidadesLogin(cpf);
    if (tokenFuncionalidadeList != null && !tokenFuncionalidadeList.isEmpty()) {
      map.put("msg", TOKEN_SOLICITADO_CURTO_PERIODO_TEMPO);
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    return null;
  }

  private void gerarTokenFuncionalidade(AcessoUsuario acessoUsuario) {
    GerarTokenFuncionalidade gerarTokenFuncionalidade = new GerarTokenFuncionalidade();
    gerarTokenFuncionalidade.setDocumento(acessoUsuario.getCpf());
    gerarTokenFuncionalidade.setIdInstituicao(
        acessoUsuario.getIdInstituicao() == null ? 2001 : acessoUsuario.getIdInstituicao());
    gerarTokenFuncionalidade.setIdProcessadora(acessoUsuario.getIdProcessadora());
    gerarTokenFuncionalidade.setIdUsuarioIssuer(acessoUsuario.getIdUsuario());
    tokenFuncionalidadeService.gerarTokenLoginIssuer(gerarTokenFuncionalidade, acessoUsuario);
  }

  public ResponseEntity<?> gerarTokenAcessoLoginUnico(
      String login, String senha, HttpServletRequest request) throws GenericServiceException {
    HashMap<String, Object> map = new HashMap<>();
    AcessoUsuarioUnico acessoUsuarioUnico = acessoUsuarioUnicoService.findByLogin(login);
    ResponseEntity<?> validacaoUsuario =
        acessoUsuarioUnicoService.validarAcessoUsuarioUnico(
            acessoUsuarioUnico, login, senha, request, map);
    if (validacaoUsuario != null) {
      return validacaoUsuario;
    }
    ResponseEntity<?> validarGeracaoToken = validarGeracaoToken(acessoUsuarioUnico.getCpf());
    if (validarGeracaoToken != null) {
      return validarGeracaoToken;
    }
    gerarTokenFuncionalidade(acessoUsuarioUnico);
    acessoLogService.saveAcessoLog(acessoUsuarioUnico.getId(), request, HttpStatus.OK, SUCESSO);
    return new ResponseEntity<>(new LoginUnicoResponseDTO(), HttpStatus.OK);
  }

  private void gerarTokenFuncionalidade(AcessoUsuarioUnico acessoUsuarioUnico) {
    GerarTokenFuncionalidade gerarTokenFuncionalidade = new GerarTokenFuncionalidade();
    gerarTokenFuncionalidade.setDocumento(acessoUsuarioUnico.getCpf());
    tokenFuncionalidadeService.gerarTokenLoginUsuarioUnico(
        gerarTokenFuncionalidade, acessoUsuarioUnico);
  }
}
