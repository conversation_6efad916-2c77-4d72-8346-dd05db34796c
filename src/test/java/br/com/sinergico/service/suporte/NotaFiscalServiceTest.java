package br.com.sinergico.service.suporte;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

import br.com.client.rest.totvs.json.bean.response.NfseTotvsApiResponse;
import br.com.entity.totvs.PreRegistroPedidosCarga;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.NFSERequest;
import br.com.sinergico.service.totvs.PreRegistroPedidosCargaService;
import br.com.sinergico.service.totvs.api.TotvsService;
import java.io.InputStream;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.HttpStatus;

class NotaFiscalServiceTest {

  @Mock private TotvsService totvsService;

  @Mock private PreRegistroPedidosCargaService preRegistroPedidosCargaService;

  @InjectMocks private NotaFiscalService notaFiscalService;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  @DisplayName("Retorna InputStream para disponibilizar download após retorno da TOTVS API")
  void testGerarNotaFiscalXMLSucesso() {
    PreRegistroPedidosCarga preRegistro =
        new PreRegistroPedidosCarga(
            1L, 10L, 122L, 1, 1L, new Date(), new Date(), new Date(), new Date());

    NFSERequest nfseRequest = new NFSERequest();
    nfseRequest.setIdPedido(123L);

    String xml =
        "<?xml version='1.0' encoding='UTF-8' ?>\n"
            + "<CompNfse\n"
            + "\txmlns=\\\"http://www.abrasf.org.br/nfse.xsd\\\">\n"
            + "\t<Nfse versao=\\\"2.04\\\">\n"
            + "\t\t<InfNfse>\n"
            + "\t\t\t<Numero></Numero>\n"
            + "\t\t\t<CodigoVerificacao></CodigoVerificacao>\n"
            + "\t\t\t<DataEmissao></DataEmissao>\n"
            + "\t\t\t<OutrasInformacoes></OutrasInformacoes>\n"
            + "\t\t\t<ValoresNfse>\n"
            + "\t\t\t\t<BaseCalculo>2965.97</BaseCalculo>\n"
            + "\t\t\t\t<Aliquota>5</Aliquota>\n"
            + "\t\t\t\t<ValorIss>0.00</ValorIss>\n"
            + "\t\t\t\t<ValorLiquidoNfse>2828.05</ValorLiquidoNfse>\n"
            + "\t\t\t</ValoresNfse>\n"
            + "\t\t\t<ValorCredito>0</ValorCredito>\n"
            + "\t\t\t<PrestadorServico>\n"
            + "\t\t\t\t<RazaoSocial>DATA BENEFICIOS LTDA</RazaoSocial>\n"
            + "\t\t\t\t<NomeFantasia>DATA BENEFICIOS</NomeFantasia>\n"
            + "\t\t\t\t<Endereco>\n"
            + "\t\t\t\t\t<Endereco>99 S/n Parte 2000</Endereco>\n"
            + "\t\t\t\t\t<Complemento />\n"
            + "\t\t\t\t\t<Bairro>Alagoinha</Bairro>\n"
            + "\t\t\t\t\t<CodigoMunicipio>999999</CodigoMunicipio>\n"
            + "\t\t\t\t\t<Uf>DF</Uf>\n"
            + "\t\t\t\t\t<Cep>198724981</Cep>\n"
            + "\t\t\t\t</Endereco>\n"
            + "\t\t\t\t<Contato>\n"
            + "\t\t\t\t\t<Telefone />\n"
            + "\t\t\t\t\t<Email><EMAIL></Email>\n"
            + "\t\t\t\t</Contato>\n"
            + "\t\t\t</PrestadorServico>\n"
            + "\t\t\t<OrgaoGerador>\n"
            + "\t\t\t\t<CodigoMunicipio>1233456</CodigoMunicipio>\n"
            + "\t\t\t\t<Uf>DF</Uf>\n"
            + "\t\t\t</OrgaoGerador>\n"
            + "\t\t\t<DeclaracaoPrestacaoServico>\n"
            + "\t\t\t\t<InfDeclaracaoPrestacaoServico Id=\\\"NSe53250113562076034653473408000000008\\\">\n"
            + "\t\t\t\t\t<Rps>\n"
            + "\t\t\t\t\t\t<IdentificacaoRps>\n"
            + "\t\t\t\t\t\t\t<Numero>8</Numero>\n"
            + "\t\t\t\t\t\t\t<Serie>3</Serie>\n"
            + "\t\t\t\t\t\t\t<Tipo>1</Tipo>\n"
            + "\t\t\t\t\t\t</IdentificacaoRps>\n"
            + "\t\t\t\t\t\t<DataEmissao>2025-01-03</DataEmissao>\n"
            + "\t\t\t\t\t\t<Status>1</Status>\n"
            + "\t\t\t\t\t</Rps>\n"
            + "\t\t\t\t\t<Competencia>2025-01-03</Competencia>\n"
            + "\t\t\t\t\t<Servico>\n"
            + "\t\t\t\t\t\t<Valores>\n"
            + "\t\t\t\t\t\t\t<ValorServicos>2965.97</ValorServicos>\n"
            + "\t\t\t\t\t\t\t<ValorPis>19.28</ValorPis>\n"
            + "\t\t\t\t\t\t\t<ValorCofins>88.98</ValorCofins>\n"
            + "\t\t\t\t\t\t\t<ValorCsll>29.66</ValorCsll>\n"
            + "\t\t\t\t\t\t</Valores>\n"
            + "\t\t\t\t\t\t<IssRetido>2</IssRetido>\n"
            + "\t\t\t\t\t\t<ItemListaServico>17.12</ItemListaServico>\n"
            + "\t\t\t\t\t\t<CodigoCnae>8299702</CodigoCnae>\n"
            + "\t\t\t\t\t\t<CodigoTributacaoMunicipio>1712</CodigoTributacaoMunicipio>\n"
            + "\t\t\t\t\t\t<Discriminacao>PEDIDO: 234235 FATURA: 34342</Discriminacao>\n"
            + "\t\t\t\t\t\t<CodigoMunicipio>265234523</CodigoMunicipio>\n"
            + "\t\t\t\t\t\t<ExigibilidadeISS>2</ExigibilidadeISS>\n"
            + "\t\t\t\t\t\t<MunicipioIncidencia>265234523</MunicipioIncidencia>\n"
            + "\t\t\t\t\t</Servico>\n"
            + "\t\t\t\t\t<Prestador>\n"
            + "\t\t\t\t\t\t<CpfCnpj>\n"
            + "\t\t\t\t\t\t\t<Cnpj>1234123413</Cnpj>\n"
            + "\t\t\t\t\t\t</CpfCnpj>\n"
            + "\t\t\t\t\t\t<InscricaoMunicipal>234123541235</InscricaoMunicipal>\n"
            + "\t\t\t\t\t</Prestador>\n"
            + "\t\t\t\t\t<TomadorServico>\n"
            + "\t\t\t\t\t\t<IdentificacaoTomador>\n"
            + "\t\t\t\t\t\t\t<CpfCnpj>\n"
            + "\t\t\t\t\t\t\t\t<Cnpj>1234123413</Cnpj>\n"
            + "\t\t\t\t\t\t\t</CpfCnpj>\n"
            + "\t\t\t\t\t\t</IdentificacaoTomador>\n"
            + "\t\t\t\t\t\t<RazaoSocial>BIRIMBINHAS LTDA</RazaoSocial>\n"
            + "\t\t\t\t\t\t<Endereco>\n"
            + "\t\t\t\t\t\t\t<Endereco>EST DO DRAGAO</Endereco>\n"
            + "\t\t\t\t\t\t\t<Numero>1000</Numero>\n"
            + "\t\t\t\t\t\t\t<Bairro>DRAGAO</Bairro>\n"
            + "\t\t\t\t\t\t\t<CodigoMunicipio>123124</CodigoMunicipio>\n"
            + "\t\t\t\t\t\t\t<Uf>PE</Uf>\n"
            + "\t\t\t\t\t\t\t<Cep>9768956</Cep>\n"
            + "\t\t\t\t\t\t</Endereco>\n"
            + "\t\t\t\t\t</TomadorServico>\n"
            + "\t\t\t\t\t<OptanteSimplesNacional>2</OptanteSimplesNacional>\n"
            + "\t\t\t\t\t<IncentivoFiscal>2</IncentivoFiscal>\n"
            + "\t\t\t\t\t<InformacoesComplementares>PRESTACAO DE SERVICO CAMPANHA R$  123,64</InformacoesComplementares>\n"
            + "\t\t\t\t</InfDeclaracaoPrestacaoServico>\n"
            + "\t\t\t\t<Signature\n"
            + "\t\t\t\t\txmlns=\\\"http://www.w3.org/2000/09/xmldsig#\\\">\n"
            + "\t\t\t\t\t<SignedInfo>\n"
            + "\t\t\t\t\t\t<CanonicalizationMethod Algorithm=\\\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\\\" />\n"
            + "\t\t\t\t\t\t<SignatureMethod Algorithm=\\\"http://www.w3.org/2000/09/xmldsig#rsa-sha1\\\" />\n"
            + "\t\t\t\t\t\t<Reference URI=\\\"#NSe53250113562076000435634563456008\\\">\n"
            + "\t\t\t\t\t\t\t<Transforms>\n"
            + "\t\t\t\t\t\t\t\t<Transform Algorithm=\\\"http://www.w3.org/2000/09/xmldsig#enveloped-signature\\\" />\n"
            + "\t\t\t\t\t\t\t\t<Transform Algorithm=\\\"http://www.w3.org/TR/2001/REC-xml-c14n-20010315\\\" />\n"
            + "\t\t\t\t\t\t\t</Transforms>\n"
            + "\t\t\t\t\t\t\t<DigestMethod Algorithm=\\\"http://www.w3.org/2000/09/xmldsig#sha1\\\" />\n"
            + "\t\t\t\t\t\t\t<DigestValue>jGuYG876G67VjhV+nU68E=</DigestValue>\n"
            + "\t\t\t\t\t\t</Reference>\n"
            + "\t\t\t\t\t</SignedInfo>\n"
            + "\t\t\t\t\t<SignatureValue>fDV/JjhGJHGJHGjgcd6fqjjCaSK112TLV6WZz7ybM+Afxz8yEAJKDHFA78DFygjp7ojz0z6M+FT5/OX4xKLwD3sirgEnqRSg00/tRstymNBSeV3+zHlO441edlaP+dIO7TNDugVc1+cNq/W/o+n+7KFtFt877xVMtJzyiyddgKE6F2dLpYUY7mnfum2rByo+OMc2Q9AzdinllQnIUeBpvtuu/DbTDHGVFk5la4GpKmu7e1jMBCqWAjsqQnRP6xtG+O1IlX6GEn5aTRFKqIHYGazMl1WKwhf3TzNWFBgbl12pfrfzOdwyOvrNrgQMEw==</SignatureValue>\n"
            + "\t\t\t\t\t<KeyInfo>\n"
            + "\t\t\t\t\t\t<X509Data>\n"
            + "\t\t\t\t\t\t\t<X509Certificate>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</X509Certificate>\n"
            + "\t\t\t\t\t\t</X509Data>\n"
            + "\t\t\t\t\t</KeyInfo>\n"
            + "\t\t\t\t</Signature>\n"
            + "\t\t\t</DeclaracaoPrestacaoServico>\n"
            + "\t\t</InfNfse>\n"
            + "\t</Nfse>\n"
            + "</CompNfse>";

    NfseTotvsApiResponse nfseTotvsApiResponse = new NfseTotvsApiResponse();
    nfseTotvsApiResponse.setSucesso(true);
    nfseTotvsApiResponse.setData(xml.getBytes());

    when(preRegistroPedidosCargaService.buscarPreRegistroCarga(anyLong())).thenReturn(preRegistro);
    when(totvsService.buscarNotaFiscalXML(preRegistro.getIdMovimento()))
        .thenReturn(nfseTotvsApiResponse);

    InputStream result = notaFiscalService.gerarNotaFiscalXML(nfseRequest);

    assertNotNull(result);
  }

  @Test
  @DisplayName("Valida PreRegistroCarga que não foi encontrado na tabela da TOTVS")
  void testGerarNotaFiscalXMLPregistroNaoEncontrado() {
    NFSERequest nfseRequest = new NFSERequest();
    nfseRequest.setIdPedido(123L);

    when(preRegistroPedidosCargaService.buscarPreRegistroCarga(anyLong())).thenReturn(null);

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              notaFiscalService.gerarNotaFiscalXML(nfseRequest);
            });

    assertEquals("Não foi encontrado pedido de carga", exception.getMessage());
    assertEquals(HttpStatus.NOT_FOUND, exception.getHttpStatus());
  }

  @Test
  @DisplayName("Valida excessão gerada por id movimento nulo")
  void testGerarNotaFiscalXMLIdMovimentoNulo() {
    PreRegistroPedidosCarga preRegistroPedidosCarga = new PreRegistroPedidosCarga();
    preRegistroPedidosCarga.setIdMovimento(null);

    NFSERequest nfseRequest = new NFSERequest();
    nfseRequest.setIdPedido(123L);

    when(preRegistroPedidosCargaService.buscarPreRegistroCarga(anyLong()))
        .thenReturn(preRegistroPedidosCarga);

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              notaFiscalService.gerarNotaFiscalXML(nfseRequest);
            });

    assertNull(preRegistroPedidosCarga.getIdMovimento());
    assertEquals("Pedido carga não possui um movimento cadastrado", exception.getMessage());
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getHttpStatus());
  }

  @Test
  @DisplayName("Valida response nulo vindo da API TOTVS")
  void testGerarNotaFiscalXMLTotvsApiResponseNulo() {
    PreRegistroPedidosCarga preRegistroPedidosCarga =
        new PreRegistroPedidosCarga(
            1L, 10L, 122L, 1, 1L, new Date(), new Date(), new Date(), new Date());
    NFSERequest nfseRequest = new NFSERequest();
    nfseRequest.setIdPedido(123L);

    when(preRegistroPedidosCargaService.buscarPreRegistroCarga(anyLong()))
        .thenReturn(preRegistroPedidosCarga);
    when(totvsService.buscarNotaFiscalXML(anyLong())).thenReturn(null);

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              notaFiscalService.gerarNotaFiscalXML(nfseRequest);
            });

    assertEquals("Retorno nulo ao gerar nota fiscal", exception.getMessage());
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getHttpStatus());
  }

  @Test
  @DisplayName("Valida retorno da API TOTVS que teve falha na execução")
  void testGerarNotaFiscalXMLTotvsApiResponseFalha() {
    PreRegistroPedidosCarga preRegistroPedidosCarga =
        new PreRegistroPedidosCarga(
            1L, 10L, 122L, 1, 1L, new Date(), new Date(), new Date(), new Date());
    NFSERequest nfseRequest = new NFSERequest();
    nfseRequest.setIdPedido(123L);

    NfseTotvsApiResponse nfseTotvsApiResponse = new NfseTotvsApiResponse();
    nfseTotvsApiResponse.setSucesso(false);
    nfseTotvsApiResponse.setErros("Erro na geração da NFSE");

    when(preRegistroPedidosCargaService.buscarPreRegistroCarga(anyLong()))
        .thenReturn(preRegistroPedidosCarga);
    when(totvsService.buscarNotaFiscalXML(anyLong())).thenReturn(nfseTotvsApiResponse);

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              notaFiscalService.gerarNotaFiscalXML(nfseRequest);
            });

    assertEquals(
        "Não foi possível gerar nota fiscal. \n" + nfseTotvsApiResponse.getErros(),
        exception.getMessage());
    assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, exception.getHttpStatus());
  }
}
