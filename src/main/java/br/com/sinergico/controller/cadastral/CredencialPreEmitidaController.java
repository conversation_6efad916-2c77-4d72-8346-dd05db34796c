package br.com.sinergico.controller.cadastral;

import br.com.entity.cadastral.CredencialPreEmitida;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.cadastral.CadastrarContaPagPessoaCredPreEmitidaRequest;
import br.com.json.bean.cadastral.ContaPreEmitidaResponse;
import br.com.json.bean.cadastral.HashCodeCredencialRequest;
import br.com.json.bean.cadastral.LiberarCredencialPreEmitidaParaCardHolderRequest;
import br.com.json.bean.cadastral.VincularCredencialPreEmitidaPessoaCardHolderRequest;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPreEmitidaRequest;
import br.com.sinergico.service.cadastral.CredencialPreEmitidaService;
import br.com.sinergico.service.cadastral.PedidoCredenciasPreEmitidasService;
import br.com.sinergico.vo.CredencialPreEmitidaVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/CredencialPreEmitida")
public class CredencialPreEmitidaController extends UtilController {

  private static final String CREDENCIAL_GERADA = "Credencial Gerada";
  private static final String CONTA_CADASTRADA_COM_SUCESSO = "Conta Cadastrada com Sucesso!";
  private static final String CREDENCIAL_PRE_EMITIDA_LIBERADA =
      "Credencial Pré-emitida liberada com sucesso!";
  private static final String OPERACAO_REALIZADA_COM_SUCESSO = "Operação realizada com Sucesso!";

  private static final int TIPO_PESSOA_FISICA = 1;

  @Autowired private PedidoCredenciasPreEmitidasService pedidoCredenciasPreEmitidasService;

  @Autowired private CredencialPreEmitidaService credencialPreEmitidaService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @ApiOperation(
      value = "Criar uma pessoa (ou não) e vincular uma contaPagamento pré-emitida à mesma.",
      response = String.class,
      notes = "Retorna o numero da conta criada")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/criar-pessoa-conta-pre-emitida",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_PESSOA_VINCULAR_CARTAO_PRE_EMITIDO_CONTA_PAGAMENTO"})
  public ResponseEntity<Map<String, Object>> cadastrarContaPreEmitidaPessoa(
      @RequestBody @Validated CadastrarContaPagPessoaCredPreEmitidaRequest model,
      BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.INSTITUICAO);

    CredencialGerada c =
        pedidoCredenciasPreEmitidasService.vincularPessoaEContaCredencialPreEmitida(model, user);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", CONTA_CADASTRADA_COM_SUCESSO);
    map.put(CREDENCIAL_GERADA, c);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Liberar um cartão pré-emitido para que uma credencial possa ser criada.",
      response = Map.class,
      notes = "Retorna um status e uma mensagem de retorno caso sucesso ou falha.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/liberar-cartao-pre-emitido",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, Object>> liberarContaPreEmitidaParaVinculacaoACredencial(
      @RequestBody @Valid LiberarCredencialPreEmitidaParaCardHolderRequest model,
      BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.INSTITUICAO);

    CredencialPreEmitida c =
        pedidoCredenciasPreEmitidasService.liberarContaPreEmitidaParaVinculacaoACredencial(
            model, user);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", CREDENCIAL_PRE_EMITIDA_LIBERADA);
    map.put(CREDENCIAL_GERADA, c);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Verifica se a credencial pré-emitida já está em uso",
      response = Boolean.class,
      notes = "Retorna true para credencial pré-emitida que já está em uso")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/verifica-uso",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_VERIFICAR_EXISTENCIA_USO_CREDENCIAL_PRE_EMITIDA"})
  public Object existeContaPreEmitida(@RequestBody @Valid HashCodeCredencialRequest model) {
    return credencialPreEmitidaService.findByNumeroCredencial(model);
  }

  @ApiOperation(
      value = "Verifica se a credencial pré-emitida já está em uso. Rest para CardHolder",
      response = Boolean.class,
      notes = "Retorna true para credencial pré-emitida que já está em uso")
  @RequestMapping(
      value = "/card-holder/verifica-uso",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public Object existeContaPreEmitidaCardHolder(
      @RequestBody @Valid HashCodeCredencialRequest model) {
    return credencialPreEmitidaService.findByNumeroCredencial(model);
  }

  @ApiOperation(
      value = "vincular uma credencial pré-emitida a uma pessoa controller. Rest para CardHolder",
      response = Map.class,
      notes = "Retorna o numero da conta criada vinculada")
  @RequestMapping(
      value = "/card-holder/vincular-credencial-pre-emitida-pessoa",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<Map<String, Object>> vincularCredencialPreEmitidaPessoaCardHolder(
      @RequestBody @Valid VincularCredencialPreEmitidaPessoaCardHolderRequest model,
      BindingResult result) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    Map<String, Object> map = new HashMap<>();
    map.put(
        CREDENCIAL_GERADA,
        pedidoCredenciasPreEmitidasService.vincularCredencialPreEmitidaPessoaCardHolder(
            model, userPortador));
    map.put("msg", CONTA_CADASTRADA_COM_SUCESSO);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Busca contas não b2b de uma pessoa logada. Rest para CardHolder",
      notes = "Retorna uma lista de contas que não são b2b")
  @RequestMapping(
      value = "/card-holder/contas-not-b2b",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<Map<String, Object>> getContasNotB2b() {

    SecurityUserPortador user = getAuthenticatedPortador(request, em);

    List<Long> contasNotB2b =
        pedidoCredenciasPreEmitidasService.getIdsContasNotB2b(
            user.getIdProcessadora(), user.getIdInstituicao(), TIPO_PESSOA_FISICA, user.getCpf());

    Map<String, Object> map = new HashMap<>();
    map.put("contas", contasNotB2b);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Vincula ou desbloqueia uma credencial pré-emitida a uma pessoa. Rest para CardHolder",
      response = Map.class)
  @RequestMapping(
      value = "/card-holder/vincular-desbloquear-credencial-pessoa",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<Map<String, Object>> vincularDesbloquearCardHolder(
      @RequestBody @Valid CredencialPreEmitidaVO model, BindingResult result) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    credencialPreEmitidaService.vincular(model, userPortador);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", OPERACAO_REALIZADA_COM_SUCESSO);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Vincula ou desbloqueia uma credencial pré-emitida a uma pessoa.",
      response = Map.class)
  @RequestMapping(
      value = "/vincular-desbloquear-credencial-pessoa",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<Map<String, Object>> vincularDesbloquear(
      @RequestBody @Valid CredencialPreEmitidaVO model, BindingResult result) {

    credencialPreEmitidaService.vincular(model);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", OPERACAO_REALIZADA_COM_SUCESSO);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Vincula ou desbloqueia uma credencial pré-emitida a uma pessoa B2B.",
      response = Map.class)
  @RequestMapping(
      value = "/b2b/vincular-desbloquear-credencial-pessoa",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<Map<String, Object>> vincularDesbloquearB2B(
      @RequestBody @Valid CredencialPreEmitidaVO model, BindingResult result) {

    credencialPreEmitidaService.vincularB2b(model);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", OPERACAO_REALIZADA_COM_SUCESSO);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Verifica se a credencial pré-emitida já está em uso. Rest para B2B",
      response = Boolean.class,
      notes = "Retorna true para credencial pré-emitida que já está em uso")
  @RequestMapping(
      value = "/b2b/verifica-uso",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, ContaPreEmitidaResponse>> existeContaPreEmitidaB2B(
      @RequestBody @Valid ContaPreEmitidaRequest model) {

    ContaPreEmitidaResponse contaPreEmitida =
        credencialPreEmitidaService.existeContaPreEmitidaB2B(model);

    Map<String, ContaPreEmitidaResponse> map = new HashMap<>();
    map.put("contaPreEmitida", contaPreEmitida);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }
}
