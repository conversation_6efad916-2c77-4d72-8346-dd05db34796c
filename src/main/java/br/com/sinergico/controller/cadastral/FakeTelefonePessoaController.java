package br.com.sinergico.controller.cadastral;

import br.com.json.bean.cadastral.FakeTelefonePessoaVo;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.FakeTelefonePessoaService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/telefone-pessoa")
public class FakeTelefonePessoaController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private FakeTelefonePessoaService fakeTelefonePessoaService;

  @ApiOperation(
      value = "Consultar registros telefone pessoa",
      response = Boolean.class,
      notes = "Consulta registros de telefone pessoa")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({
    "ROLE_BUSCAR_ENDERECOS_PESSOA_STATUS"
  }) // Utilizando ROLE já disponível em ambiente produtivo para portadores da instituição 2401
  public ResponseEntity<List<FakeTelefonePessoaVo>> consultarTelefonePessoaFake() {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    List<FakeTelefonePessoaVo> lista =
        fakeTelefonePessoaService.consultarTelefonePessoaFake(userPortador);
    return new ResponseEntity<>(lista, HttpStatus.OK);
  }
}
