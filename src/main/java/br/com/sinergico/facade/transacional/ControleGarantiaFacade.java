package br.com.sinergico.facade.transacional;

import br.com.client.rest.jcard.json.bean.BlockBusinessResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ControleGarantiaBloqueioId;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.suporte.ControleGarantia;
import br.com.entity.suporte.ControleGarantiaBloqueio;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.ParametroDefinicao;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.transacional.HistoricoGarantia;
import br.com.entity.transacional.RrnLog;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.EnderecoPessoaRequest;
import br.com.json.bean.cadastral.EnderecosPessoaRequest;
import br.com.json.bean.jcard.BlockBusinessRequest;
import br.com.json.bean.sistema.GenericResponse;
import br.com.json.bean.transacional.CadastroControleGarantia;
import br.com.json.bean.transacional.HistoricoRecomposicaoContaGarantiaRequest;
import br.com.json.bean.transacional.HistoricoRecomposicaoContaGarantiaResponse;
import br.com.json.bean.transacional.PainelControleGarantiaResponse;
import br.com.json.bean.transacional.RecomporSaldoContaGarantiaRequest;
import br.com.json.bean.transacional.SaldoContaGarantiaResponse;
import br.com.sinergico.repository.transacional.ControleGarantiaInstConfigRepository;
import br.com.sinergico.repository.transacional.HistoricoGarantiaRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ControleGarantiaBloqueioService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.jcard.BlockService;
import br.com.sinergico.service.jcard.IssuerService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.transacional.ControleGarantiaService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.service.transacional.RrnLogService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.HierarquiaInstituicaoVO;
import com.google.common.base.Strings;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.StringTokenizer;
import javax.transaction.Transactional;
import org.hibernate.validator.internal.constraintvalidators.hv.EmailValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 14/12/2019
 */
@Service
public class ControleGarantiaFacade {

  private static final int ID_PROC_DEFAULT = 10;

  private static final int STATUS_ATIVO = 1;
  private static final int STATUS_BLOQUEADO = 9;

  private static final int CREDITO = 1;

  @Autowired private HierarquiaInstituicaoService instituicaoService;

  @Autowired private ControleGarantiaService controleGarantiaService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private HierarquiaInstituicaoService instService;

  @Autowired private HistoricoGarantiaRepository historicoGarantiaRepository;

  @Autowired private IssuerService issuerService;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfService;

  @Autowired @Lazy private CredencialService credencialService;
  @Autowired private RrnLogService rrnLogService;

  @Autowired private EmailService emailService;

  @Autowired private ParametroValorService paramValorService;

  @Autowired private BlockService blockService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private ControleGarantiaInstConfigRepository controleGarantiaInstConfRepository;

  @Autowired private ControleGarantiaBloqueioService bloqueioService;

  private static final Logger log = LoggerFactory.getLogger(ControleGarantiaFacade.class);

  private static final String PARAM_EMAIL_FIN_BANK10 = "cgaran.emailfin";

  private static final String PARAM_REC_CONTA_GARANTIA = "cgaran.codtr.rec";

  public List<HierarquiaInstituicaoVO> getAllInstituicoes(SecurityUser user) {

    List<HierarquiaInstituicao> allInst = new ArrayList<>();

    if (user.getIdInstituicao() != null
        && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(user.getIdInstituicao())) {
      allInst = instituicaoService.findByIdProcessadora(ID_PROC_DEFAULT);
    } else {
      allInst = instituicaoService.findAll();
    }
    List<ControleGarantia> controles = controleGarantiaService.findAll();

    List<HierarquiaInstituicaoVO> instsVo = new ArrayList<>();

    if (allInst != null && !allInst.isEmpty()) {

      allInst.forEach(
          inst -> {
            Long idControle = null;
            ControleGarantia garan = null;

            for (ControleGarantia con : controles) {
              if (con.getIdInstituicaoDependente().equals(inst.getIdInstituicao())) {
                idControle = con.getIdControleGarantia();
                garan = con;
                break;
              }
            }

            HierarquiaInstituicaoVO vo = new HierarquiaInstituicaoVO();
            vo.setCnpj(inst.getCnpj());
            vo.setDescInstituicao(inst.getDescInstituicao());
            vo.setIdProcessadora(inst.getIdProcessadora());
            vo.setIdInstituicao(inst.getIdInstituicao());
            vo.setRazaoSocial(inst.getRazaoSocial());
            vo.setIdControleGarantia(idControle);

            if (garan != null) {
              vo.setDescStatus(getStatusContaGarantia().get(garan.getEstado()));
              vo.setValorGarantia(garan.getValorGarantia());
              vo.setValorMinimoGarantia(garan.getValorMinimoGarantia());
              vo.setSaldoInicioDia(garan.getSaldoInicioDia());
              vo.setFaixa1(garan.getPrimeiraFaixa());
              vo.setFaixa2(garan.getSegundaFaixa());
              vo.setFaixa3(garan.getTerceiraFaixa());
              vo.setEmailsAlerta(garan.getEmailsAlertas());
              vo.setAporte1(garan.getAporte1());
              vo.setAporte2(garan.getAporte2());
              vo.setAporte3(garan.getAporte3());
              vo.setAporte4(garan.getAporte4());
              vo.setAporte5(garan.getAporte5());
              LocalDateTime dataHora =
                  garan.getDataHoraManutencao() == null
                      ? garan.getDataHoraInclusao()
                      : garan.getDataHoraManutencao();
              DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
              if (dataHora != null) {
                String agoraFormatado = dataHora.format(formatter);
                vo.setData(agoraFormatado);
              }
            } else {
              vo.setDescStatus(getStatusContaGarantia().get(0));
            }

            instsVo.add(vo);
          });
    }

    return instsVo;
  }

  public HierarquiaInstituicaoVO getInstituicao(SecurityUser user) {
    return getInstituicao(user.getIdProcessadora(), user.getIdInstituicao());
  }

  public HierarquiaInstituicaoVO getInstituicao(SecurityUserPortador userPortador) {
    return getInstituicao(userPortador.getIdProcessadora(), userPortador.getIdInstituicao());
  }

  public HierarquiaInstituicaoVO getInstituicao(Integer idProc, Integer idInst) {
    HierarquiaInstituicao inst;
    if (idProc != null && idInst != null) {
      inst = instituicaoService.findByIdProcessadoraAndIdInstituicao(idProc, idInst);

      if (inst == null) {
        throw new GenericServiceException(
            "Instituição não encontrada.", "IdProc: " + idProc + " idInst: " + idInst);
      }

    } else {
      throw new GenericServiceException(
          "Instituição não encontrada.", "IdProc: " + idProc + " idInst: " + idInst);
    }

    ControleGarantia garan = getControleGarantiaByInst(idProc, idInst);

    HierarquiaInstituicaoVO vo = new HierarquiaInstituicaoVO();
    vo.setCnpj(inst.getCnpj());
    vo.setDescInstituicao(inst.getDescInstituicao());
    vo.setIdProcessadora(inst.getIdProcessadora());
    vo.setIdInstituicao(inst.getIdInstituicao());
    vo.setRazaoSocial(inst.getRazaoSocial());
    vo.setValidaContaAtiva(inst.getValidaContaAtiva());

    if (garan != null) {
      vo.setIdControleGarantia(garan.getIdControleGarantia());
      vo.setDescStatus(getStatusContaGarantia().get(garan.getEstado()));
      vo.setStatus(garan.getEstado());
      // SaldoContaGarantiaResponse saldoCG =
      // getSaldoAtualContaGarantia(garan.getIdControleGarantia());
      // vo.setValorGarantia(saldoCG.getSaldo());

      vo.setValorMinimoGarantia(garan.getValorMinimoGarantia());
      vo.setFaixa1(garan.getPrimeiraFaixa());
      vo.setFaixa2(garan.getSegundaFaixa());
      vo.setFaixa3(garan.getTerceiraFaixa());
      vo.setEmailsAlerta(garan.getEmailsAlertas());
    } else {
      vo.setDescStatus(getStatusContaGarantia().get(0));
      vo.setStatus(0);
    }
    return vo;
  }

  private ControleGarantia getControleGarantiaByInst(Integer idProc, Integer idInst) {
    List<ControleGarantia> controles =
        controleGarantiaService.findByIdProcessadoraAndIdInstituicaoDependente(idProc, idInst);
    ControleGarantia garan = null;
    if (controles != null && !controles.isEmpty()) {
      garan = controles.get(0);
    }
    return garan;
  }

  private ControleGarantia getControleGarantiaByInstNotNull(Integer idProc, Integer idInst) {
    ControleGarantia contr = getControleGarantiaByInst(idProc, idInst);
    if (contr == null) {
      throw new GenericServiceException(
          "Controle de Garantia não encontrado.", "idProc: " + idProc + " idInst: " + idInst);
    }
    return contr;
  }

  private Map<Integer, String> getStatusContaGarantia() {
    /*
     * Ativo = 1; Primeiro Alerta = 2;
     * Segundo Alerta = 3; Terceiro Alerta = 4; Liberacao Forcada = 5;
     * Bloqueado = 9
     */
    Map<Integer, String> stats = new HashMap<Integer, String>();
    stats.put(0, "Sem Cadastro");
    stats.put(1, "Ativo");
    stats.put(2, "Primeiro Alerta");
    stats.put(3, "Segundo Alerta");
    stats.put(4, "Terceiro Alerta");
    stats.put(5, "Liberação Forçada");
    stats.put(9, "Bloqueado");

    return stats;
  }

  public GenericResponse salvarDadosControleGarantia(
      CadastroControleGarantia model, SecurityUser user) {
    ControleGarantia cong = new ControleGarantia();
    GenericResponse resp = new GenericResponse(true);

    try {

      validarEmails(model);

      // verificar se existe conta e se existir apenas atualizar, senao criar conta garantia
      if (model.getIdControleGarantia() != null) {
        cong = controleGarantiaService.findById(model.getIdControleGarantia());

        if (cong == null) {
          throw new GenericServiceException(
              "Controle de Garantia não encontrado.",
              "idControleGarantia: " + model.getIdControleGarantia());
        }
        cong.setDataHoraManutencao(LocalDateTime.now());
        cong = controleGarantiaService.salvarAtualizarDadosControleGarantia(model, cong, user);
      } else {

        cong.setIdUsuarioInclusao(user.getIdUsuario());
        cong.setEstado(STATUS_ATIVO);
        cong.setIdInstituicaoPrincipal(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
        cong.setIdInstituicaoDependente(model.getIdInstituicao());
        cong.setDataHoraInclusao(LocalDateTime.now());

        cong = controleGarantiaService.salvarAtualizarDadosControleGarantia(model, cong, user);
      }

      // caso haja algum erro so loga e devolve o erro
    } catch (GenericServiceException e) {
      resp.setSucesso(false);
      resp.setErros(Arrays.asList(e.getMensagem()));
      log.error(e.getMensagem(), e);
    } catch (Exception e) {
      resp.setSucesso(false);
      resp.setErros(Arrays.asList(e.getMessage()));
      log.error(e.getMessage(), e);
    }

    return resp;
  }

  private void validarEmails(CadastroControleGarantia model) {

    if (model.getEmailsAlerta() != null && !model.getEmailsAlerta().isEmpty()) {
      //			String[] split = model.getEmailsAlerta().split(";");
      String[] tokens = {";"};

      for (String token : tokens) {

        StringTokenizer tokenizer = new StringTokenizer(model.getEmailsAlerta(), token);
        EmailValidator val = new EmailValidator();

        while (tokenizer.hasMoreTokens()) {
          String email = tokenizer.nextToken();

          if (!val.isValid(email, null)) {
            throw new GenericServiceException(
                "E-mail inválido: " + email + " . Verifique o campo de E-mails de Alerta.");
          }
        }
      }
    }
    //		val.isValid(value, null);

  }

  private Integer getIdProdInst(HierarquiaInstituicao inst) {
    StringBuilder sb = new StringBuilder();
    //		sb.append(inst.getIdProcessadora());
    //		sb.append(inst.getIdInstituicao());
    // temp
    //		sb.append(inst.getIdInstituicao());
    //		sb.append("01");

    return new Integer(sb.toString());
  }

  private EnderecosPessoaRequest preencherEndereco(HierarquiaInstituicao inst, Integer iduser) {
    EnderecosPessoaRequest endsReq = new EnderecosPessoaRequest();
    List<EnderecoPessoaRequest> enderecos = new ArrayList<>();
    EnderecoPessoaRequest end = new EnderecoPessoaRequest();
    end.setCep(inst.getCep());
    end.setBairro(inst.getBairro());
    end.setCidade(inst.getCidade());
    end.setUf(inst.getUf());
    end.setLogradouro(inst.getLogradouro());
    end.setNumero(inst.getNumero());
    end.setComplemento(inst.getComplemento());
    end.setIdTipoEndereco(Constantes.ENDERECO_CORRESPONDENCIA);
    end.setIdUsuarioInclusao(iduser);
    end.setNumero(inst.getNumero());
    enderecos.add(end);
    endsReq.setEnderecos(enderecos);
    return endsReq;
  }

  @Transactional
  public GenericResponse desbloquearInstituicao(
      Long idControleGarantia, Integer idProc, Integer idInst, SecurityUser user) {
    GenericResponse response = new GenericResponse(false);
    HierarquiaInstituicao inst = null;
    ControleGarantia controle = null;

    try {

      if (idProc != null && idInst != null) {
        controle = getControleGarantiaByInstNotNull(idProc, idInst);
        inst = instService.findByIdProcessadoraAndIdInstituicao(idProc, idInst);
      } else if (idControleGarantia != null) {
        controle = getControleGarantiaNotNull(idControleGarantia);
        inst =
            instService.findByIdProcessadoraAndIdInstituicao(
                controle.getIdProcessadora(), controle.getIdInstituicaoDependente());
      } else {
        throw new GenericServiceException(
            "Instituicao não encontrada.Dados insuficientes!",
            " idProc " + idProc + "idInst" + idInst + " idControleGarantia " + idControleGarantia);
      }

      if (inst != null) {
        List<ProdutoInstituicao> produtoInstituicao =
            produtoInstituicaoService.findByIntituicao(
                inst.getIdInstituicao(), Constantes.ID_PROCESSADORA_ITS_PAY);
        List<ControleGarantiaBloqueio> controleGarantiaBloqueios =
            bloqueioService.findByIdInstituicao(controle.getIdInstituicaoDependente());

        if (controle.getEstado() == STATUS_ATIVO) {
          if (controleGarantiaBloqueios.isEmpty()) {
            ControleGarantia finalControle = controle;
            produtoInstituicao.forEach(
                prod -> {
                  BlockBusinessRequest block =
                      preparaRequestJcard(prod.getIdInstituicao(), prod.getDescProdInstituicao());
                  BlockBusinessResponse resp = blockService.addBlockBusiness(block);
                  if (resp.getSuccess()) {
                    criarBloqueio(resp, finalControle, prod);
                  } else {
                    throw new GenericServiceException(resp.getErrors());
                  }
                });
          } else {
            controleGarantiaBloqueios.forEach(
                controleGarantiaBloqueio -> {
                  BlockBusinessResponse resp =
                      blockService.blockBusiness(controleGarantiaBloqueio.getId().getIdJcard());
                  if (resp.getSuccess()) {
                    controleGarantiaBloqueio.setStatus(false);
                    bloqueioService.save(controleGarantiaBloqueio);
                  } else {
                    throw new GenericServiceException(resp.getErrors());
                  }
                });
          }

        } else {
          controleGarantiaBloqueios.forEach(
              controleGarantiaBloqueio -> {
                if (controleGarantiaBloqueio.getId() != null) {
                  BlockBusinessResponse resp =
                      blockService.unblockBusiness(controleGarantiaBloqueio.getId().getIdJcard());
                  if (resp.getSuccess()) {
                    controleGarantiaBloqueio.setStatus(true);
                    bloqueioService.save(controleGarantiaBloqueio);
                  } else {
                    throw new GenericServiceException(resp.getErrors());
                  }
                }
              });
        }
        log.info("usuario " + user.getIdUsuario() + " liberando inst: " + inst.getIdInstituicao());
        response.setSucesso(true);

        if (controle.getEstado() == STATUS_ATIVO) {
          controle.setEstado(STATUS_BLOQUEADO);
        } else {
          controle.setEstado(STATUS_ATIVO);
        }
        controle.setDataHoraManutencao(LocalDateTime.now());
        controle.setIdUsuarioManutencao(user.getIdUsuario());
        controleGarantiaService.save(controle);

      } else {
        throw new GenericServiceException(
            "Instituição não encontrada. ", "idProc: " + idProc + " idInst:" + idInst);
      }

    } catch (GenericServiceException e) {
      response.setSucesso(false);
      response.setErros(Arrays.asList(e.getMensagem()));
      log.error(e.getMensagem(), e);
    } catch (Exception e) {
      response.setSucesso(false);
      response.setErros(Arrays.asList(e.getMessage()));
      log.error(e.getMessage(), e);
    }

    return response;
  }

  private void criarBloqueio(
      BlockBusinessResponse resp, ControleGarantia controleGarantia, ProdutoInstituicao prod) {
    ControleGarantiaBloqueio controleGarantiaBloqueio = new ControleGarantiaBloqueio();
    ControleGarantiaBloqueioId id = new ControleGarantiaBloqueioId();
    controleGarantiaBloqueio.setStatus(false);
    controleGarantiaBloqueio.setDescProduto(prod.getDescProdInstituicao());
    controleGarantiaBloqueio.setIdInstituicao(prod.getIdInstituicao());
    controleGarantiaBloqueio.setIdProdInstituicao(prod.getIdProdInstituicao());
    id.setIdControleGarantia(controleGarantia.getIdControleGarantia());
    id.setIdJcard(resp.getId());
    controleGarantiaBloqueio.setId(id);
    bloqueioService.save(controleGarantiaBloqueio);
  }

  private BlockBusinessRequest preparaRequestJcard(Integer idIntituicao, String produto) {

    BlockBusinessRequest block = new BlockBusinessRequest();

    block.setProductname(produto);
    block.setExternallevel2(null);
    block.setExternallevel3(null);
    block.setExternallevel4(null);
    block.setType("CONTA GARANTIA");
    block.setExternallevel1(Strings.padStart(idIntituicao.toString(), 2, Constantes.CHAR_ZERO));
    return block;
  }

  // updateStatusIssuer

  public SaldoContaGarantiaResponse getSaldoAtualContaGarantia(Long idControleGarantia) {
    /*ControleGarantia controle = getControleGarantiaNotNull(idControleGarantia);

    GetSaldoConta saldo = contaPagamentoService.getSaldoConta(controle.getContaGarantia());


    SaldoContaGarantiaResponse resp = new SaldoContaGarantiaResponse();
    resp.setSaldo(saldo.getSaldoDisponivel());
    resp.setDataSaldo(DateUtil.localDateTimeToDate(saldo.getDataLocalDateTime()));
    resp.setData(saldo.getData());
    resp.setHora(saldo.getHora());*/

    return null;
  }

  private ControleGarantia getControleGarantiaNotNull(Long idControleGarantia) {
    ControleGarantia controle = controleGarantiaService.findById(idControleGarantia);

    if (controle == null) {
      throw new GenericServiceException(
          "ControleGarantia não encontrada. ", "idControleGarantia: " + idControleGarantia);
    }
    return controle;
  }

  public PainelControleGarantiaResponse getDadosPainelControle(Long idControleGarantia) {

    ControleGarantia controle = getControleGarantiaNotNull(idControleGarantia);
    // GetSaldoConta saldo = contaPagamentoService.getSaldoConta(controle.getContaGarantia());
    HierarquiaInstituicao inst =
        instituicaoService.findByIdProcessadoraAndIdInstituicao(
            controle.getIdProcessadora(), controle.getIdInstituicaoDependente());
    String descInstituicao = null;

    if (inst != null) {
      descInstituicao = inst.getDescInstituicao();
    }

    HistoricoGarantia hist =
        historicoGarantiaRepository
            .findFirstByIdProcessadoraAndIdInstituicaoPrincipalAndIdInstituicaoDependenteOrderByDataHoraInclusaoDesc(
                controle.getIdProcessadora(),
                controle.getIdInstituicaoPrincipal(),
                controle.getIdInstituicaoDependente());

    PainelControleGarantiaResponse resp = new PainelControleGarantiaResponse();

    if (hist != null && hist.getDataRecomposicaoSaldo() != null) {
      resp.setDataUltRecomposicao(hist.getDataRecomposicaoSaldo());
      resp.setDataUltRecomposicaoStr(
          DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, hist.getDataRecomposicaoSaldo()));
    }
    resp.setDescInstituicao(descInstituicao);
    return resp;
  }

  @Transactional
  public GenericResponse recomporSaldo(RecomporSaldoContaGarantiaRequest model, SecurityUser user) {
    GenericResponse gresp = new GenericResponse(true);

    try {

      ControleGarantia controle = getControleGarantiaNotNull(model.getIdControleGarantia());
      ContaPagamento conta = getContaPagamentoNotNull(controle);

      ProdutoInstituicaoConfiguracao prodInstConf = getProdInstConfNotNull(conta);

      String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN);
      Integer stan = lancamentoService.getStan();

      List<Credencial> creds = credencialService.findByIdContaOrderByCsn(conta.getIdConta());

      if (creds == null || creds.isEmpty()) {
        throw new GenericServiceException(
            "Nenhuma Credencial encontrada para a conta.", "IdConta:" + conta.getIdConta());
      }

      String codtranStr =
          getCodTransacaoRecomporContaGarantia(
              conta.getIdProcessadora(), Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
      Integer codTran = new Integer(codtranStr);

      Credencial cred = creds.get(0);

      JcardResponse response =
          lancamentoService.doLancamentoManual(
              conta.getIdAccountCode(),
              codTran,
              model.getValorGarantia(),
              prodInstConf.getMoeda().getIdMoeda().toString(),
              rrn,
              CREDITO,
              cred.getTokenInterno(),
              conta.getIdConta(),
              "RECOMPOR SALDO CONT. GARAN.",
              "",
              "",
              null,
              stan,
              null);

      if (!response.getSuccess()) {
        log.error("Não foi possível realizar o lancamento:" + ". Motivo: " + response.getErrors());
      }

      // Montar rrn log
      //			RrnLog rrnLog = prepareRrnLog(conta.getIdConta(), codTran, rrn,
      //					response.getSuccess(), response.getErrors(), conta.getIdAccountCode(),
      //					cred.getTokenInterno(), prodInstConf.getMoeda().getIdMoeda().toString(),
      //					model.getValorGarantia());
      //
      //			rrnLogService.salvarLog(null,conta.getIdConta(), codTran, rrn, response.getSuccess(),
      // response.getErrors());

      HistoricoGarantia hist = new HistoricoGarantia();
      hist.setDataHoraInclusao(new Date());
      hist.setConfirmado("Y");
      hist.setIdControleGarantia(controle.getIdControleGarantia());
      hist.setIdInstituicaoDependente(controle.getIdInstituicaoDependente());
      hist.setIdInstituicaoPrincipal(controle.getIdInstituicaoPrincipal());
      hist.setIdProcessadora(controle.getIdProcessadora());
      hist.setIdUsuarioInclusao(user.getIdUsuario());
      hist.setValorDeposito(model.getValorGarantia());
      hist.setDataRecomposicaoSaldo(new Date());
      historicoGarantiaRepository.save(hist);

    } catch (GenericServiceException e) {
      gresp.setSucesso(false);
      List<String> msgs = Arrays.asList(e.getMensagem());
      if (msgs == null || msgs.isEmpty()) {
        msgs = Arrays.asList("Ocorreu um erro ao executar a operação!");
      }
      gresp.setErros(msgs);
      log.error(e.getMensagem(), e);
    } catch (Exception e) {
      gresp.setSucesso(false);
      List<String> msgs = Arrays.asList(e.getMessage());
      if (msgs == null || msgs.isEmpty()) {
        msgs = Arrays.asList("Ocorreu um erro genérico ao executar a operação!");
      }
      gresp.setErros(msgs);
      log.error(e.getMessage(), e);
    }
    return gresp;
  }

  private ContaPagamento getContaPagamentoNotNull(ControleGarantia controle) {
    /*ContaPagamento conta =  contaPagamentoService.findById(controle.getContaGarantia());

    if(conta == null) {
    	throw new GenericServiceException("Conta não encontrada!"," idConta: "+controle.getContaGarantia());
    }*/
    return null;
  }

  private ProdutoInstituicaoConfiguracao getProdInstConfNotNull(ContaPagamento conta) {
    ProdutoInstituicaoConfiguracao prodInstConf =
        prodInstConfService.findByIdConta(conta.getIdConta());

    if (prodInstConf == null) {
      throw new GenericServiceException(
          "ProdutoInstConfig não encontrado.", "idConta:" + conta.getIdConta());
    }
    return prodInstConf;
  }

  private RrnLog prepareRrnLog(
      Long idConta,
      Integer codTransacao,
      String rrn,
      Boolean success,
      String errors,
      String idAccountCode,
      String tokenInterno,
      String idMoeda,
      BigDecimal valorTransacao) {
    RrnLog log = new RrnLog();
    log.setCodTransacao(codTransacao);
    log.setIdConta(idConta);
    log.setRrn(rrn);
    log.setSuccess(success);
    log.setErrors(errors);
    log.setDataHoraInclusao(new Date());
    log.setIdAccountCode(idAccountCode);
    log.setTokenInterno(tokenInterno);
    log.setIdMoeda(idMoeda);
    log.setValorTransacao(valorTransacao);

    return log;
  }

  public List<HistoricoRecomposicaoContaGarantiaResponse> getHistoricoContaGarantia(
      HistoricoRecomposicaoContaGarantiaRequest model, SecurityUser user) {
    ControleGarantia controle = getControleGarantiaNotNull(model.getIdControleGarantia());

    List<HistoricoRecomposicaoContaGarantiaResponse> resp = new ArrayList<>();

    Date dataInicio =
        model.getDataInicio() == null
            ? DateUtil.diminuirDias(new Date(), 30)
            : model.getDataInicio();
    Date dataFim = model.getDataFim() == null ? new Date() : model.getDataFim();
    List<HistoricoGarantia> results =
        historicoGarantiaRepository
            .findByIdProcessadoraAndIdInstituicaoPrincipalAndIdInstituicaoDependenteAndDataRecomposicaoSaldoBetween(
                controle.getIdProcessadora(),
                controle.getIdInstituicaoPrincipal(),
                controle.getIdInstituicaoDependente(),
                dataInicio,
                dataFim);

    if (results != null) {
      results.forEach(
          res -> {
            HistoricoRecomposicaoContaGarantiaResponse tmp =
                new HistoricoRecomposicaoContaGarantiaResponse();
            tmp.setDataRecomposicaoSaldoStr(
                DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, res.getDataRecomposicaoSaldo()));
            tmp.setConfirmado(res.getConfirmado().equals("Y"));
            tmp.setIdHistoricoGarantia(res.getIdHistoricoGarantia());
            tmp.setIdUsuarioInclusao(res.getIdUsuarioInclusao());
            tmp.setValorDeposito(res.getValorDeposito());

            resp.add(tmp);
          });
    }
    return resp;
  }

  public GenericResponse solicitarRecomposicao(
      RecomporSaldoContaGarantiaRequest model, SecurityUser user) {
    GenericResponse gresp = new GenericResponse(true);

    ControleGarantia controle = getControleGarantiaNotNull(model.getIdControleGarantia());

    HierarquiaInstituicao inst =
        instituicaoService.findByIdProcessadoraAndIdInstituicao(
            user.getIdProcessadora(), controle.getIdInstituicaoDependente());

    try {

      String valorForm = Util.currencyFormat(model.getValorGarantia());
      //			String emailFin = getEmailFinBank10(user.getIdProcessadora(),ID_INST_BANK10);
      String emailFin = controle.getEmailsAlertas();

      emailService.dispararEmailSolicitacaoRecomporSaldoContaGarantia(
          user.getIdProcessadora(),
          inst.getDescInstituicao(),
          valorForm,
          emailFin,
          user.getIdUsuario());
    } catch (GenericServiceException e) {
      gresp.setSucesso(false);
      gresp.setErros(Arrays.asList(e.getMensagem()));
      log.error(e.getMensagem(), e);
    } catch (Exception e) {
      gresp.setSucesso(false);
      gresp.setErros(Arrays.asList(e.getMessage()));
      log.error(e.getMessage(), e);
    }
    return gresp;
  }

  private String getEmailFinBank10(Integer idProc, Integer idInstituicao) {
    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(PARAM_EMAIL_FIN_BANK10);

    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(idProc);
    valor.setIdInstituicao(idInstituicao);

    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);
    return params.get(0).getValorParametro();
  }

  private String getCodTransacaoRecomporContaGarantia(Integer idProc, Integer idInstituicao) {
    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(PARAM_REC_CONTA_GARANTIA);

    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(idProc);
    valor.setIdInstituicao(idInstituicao);

    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);
    return params.get(0).getValorParametro();
  }

  public ControleGarantiaService getControleGarantiaService() {
    return this.controleGarantiaService;
  }
}
