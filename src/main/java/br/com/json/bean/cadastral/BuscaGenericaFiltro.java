package br.com.json.bean.cadastral;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BuscaGenericaFiltro implements Serializable {

  private static final long serialVersionUID = 26059394347090628L;

  private Integer idProcessadora;
  private Integer idInstituicao;
  private Integer idRegional;
  private Integer idFilial;
  private Integer idPontoDeRelacionamento;

  private String usuario;
  private String documento;
  private Integer idUsuario;
  private Integer idGrupoAcesso;

  private Integer status;
}
