package br.com.sinergico.service.cadastral;

import static java.lang.Boolean.FALSE;

import br.com.client.rest.jcard.json.bean.CreateAccount;
import br.com.client.rest.jcard.json.bean.CreateAccountBalance;
import br.com.client.rest.jcard.json.bean.CreateAccountResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.client.rest.totvs.json.bean.response.PreRegistroEmpresaContaResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.ContaPessoaId;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituidor;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.MoedaConta;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.repository.cadastral.ContaPessoaRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.jcard.AccountBalanceService;
import br.com.sinergico.service.jcard.AccountService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.totvs.api.PreRegistroContaTotvsService;
import br.com.sinergico.util.ConstantesErro;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ContaPessoaService extends GenericService<ContaPessoa, ContaPessoaId> {

  private static final Logger log = LoggerFactory.getLogger(ContaPessoaService.class);

  private ContaPessoaRepository contaPessoaRepository;
  private static final int BLOQUEIO_CRIACAO = 0;
  private static final Integer ATIVO = 1;
  private static final int TYPE_CREDITO = 1;

  @Autowired private AccountBalanceService accountBalanceService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ContaPagamentoService contaService;

  @Autowired private AccountService accountService;

  @Autowired private PreRegistroContaTotvsService preRegistroContaTotvsService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfService;

  @Autowired private HierarquiaInstituicaoService instituicaoService;

  @Autowired
  public ContaPessoaService(ContaPessoaRepository repo) {
    super(repo);
    contaPessoaRepository = repo;
  }

  public ContaPessoa findOneByIdPessoaAndIdConta(Long idPessoa, Long idConta) {
    return contaPessoaRepository.findOneByIdPessoaAndIdConta(idPessoa, idConta);
  }

  public Boolean isContaPessoaExisteByIdContaAndIdPessoa(Long idConta, Long idPessoa) {
    return contaPessoaRepository.isContaPessoaExisteByIdContaAndIdPessoa(idConta, idPessoa);
  }

  public ContaPessoa findOneByIdPessoaAndIdContaAndStatus(
      Long idPessoa, Long idConta, Integer status) {
    return contaPessoaRepository.findOneByIdPessoaAndIdContaAndStatus(idPessoa, idConta, status);
  }

  public ContaPessoa findOneByIdPessoaAndIdContaAndIdTitularidade(
      Long idPessoa, Long idConta, Integer idTitularidade) {
    return contaPessoaRepository.findOneByIdPessoaAndIdContaAndIdTitularidade(
        idPessoa, idConta, idTitularidade);
  }

  public List<ContaPessoa> findByIdPessoaIdTitularidade(Long idPessoa, Integer idTitularidade) {
    return contaPessoaRepository.findByIdPessoaAndIdTitularidade(idPessoa, idTitularidade);
  }

  public List<ContaPessoa> findByIdPessoaIdTitularidadeAndStatus(
      Long idPessoa, Integer idTitularidade, Integer status) {
    return contaPessoaRepository.findByIdPessoaAndIdTitularidadeAndStatus(
        idPessoa, idTitularidade, status);
  }

  public List<ContaPessoa> findByIdPessoa(Long idPessoa) {
    return contaPessoaRepository.findByIdPessoa(idPessoa);
  }

  public List<ContaPessoa> findByIdConta(Long idConta) {
    return contaPessoaRepository.findByIdConta(idConta);
  }

  public ContaPessoa findOneByIdContaAndIdTitularidade(Long idConta, Integer titularidade) {
    return contaPessoaRepository.findOneByIdContaAndIdTitularidade(idConta, titularidade);
  }

  @Transactional
  public ContaPessoa vincularConta(ContaPessoa contaPessoaReq) {

    Pessoa pessoa = obterPessoa(contaPessoaReq);

    ContaPagamento conta = obterContaPagamento(contaPessoaReq);

    ProdutoInstituicao produtoInstituicao = obterProdutoInstituicao(conta);

    ContaPessoa contaPessoa = new ContaPessoa();
    contaPessoa.setContaPessoaId(new ContaPessoaId(conta, pessoa));
    contaPessoa.setIdConta(contaPessoaReq.getIdConta());
    contaPessoa.setContaPagamento(conta);
    contaPessoa.setIdPessoa(contaPessoaReq.getIdPessoa());
    contaPessoa.setPessoa(pessoa);
    contaPessoa.setIdTitularidade(contaPessoaReq.getIdTitularidade());
    LocalDateTime now = LocalDateTime.now();
    contaPessoa.setDataHoraInclusao(now);
    contaPessoa.setDataHoraStatus(now);
    contaPessoa.setStatus(BLOQUEIO_CRIACAO);
    if (StringUtils.isNotBlank(contaPessoaReq.getNomeCartaoImpresso())) {
      contaPessoa.setNomeCartaoImpresso(contaPessoaReq.getNomeCartaoImpresso());
    }

    if (produtoInstituicao.getB2b()) {
      contaPessoa.setStatus(ATIVO);
    }

    validarContaJaVinculada(contaPessoa);

    createAccount(pessoa, conta);
    ContaPessoa cp = save(contaPessoa);
    contaService.save(conta);

    if (instituicaoService.instituicaoEnviaDadosTotvs(
        conta.getIdProcessadora(), conta.getIdInstituicao())) {
      PreRegistroEmpresaContaResponse preRegistroTotvsResponse =
          preRegistroContaTotvsService.preRegistrarContaTotvs(pessoa, conta);
      if (!preRegistroTotvsResponse.getSucesso()) {
        log.info(
            ConstantesErro.TOT_FALHA_PRE_REGISTRO.format(
                "CONTA", preRegistroTotvsResponse.getErros()));
      }
    }

    // definir limite de contas-pós
    if (Objects.nonNull(conta.getLimiteUnico())) {
      definirLimiteContaPos(conta, produtoInstituicao);
    }

    return cp;
  }

  private Pessoa obterPessoa(ContaPessoa contaPessoaReq) {
    Pessoa pessoa = pessoaService.findById(contaPessoaReq.getIdPessoa());

    if (Objects.isNull(pessoa)) {
      throw new GenericServiceException(
          "Pessoa não encontrada. idPessoa = " + contaPessoaReq.getIdPessoa());
    }
    return pessoa;
  }

  private ContaPagamento obterContaPagamento(ContaPessoa contaPessoaReq) {
    ContaPagamento conta = contaService.findById(contaPessoaReq.getIdConta());
    if (Objects.isNull(conta)) {
      throw new GenericServiceException(
          "ContaPagamento não encontrada. IdConta = " + contaPessoaReq.getIdConta());
    }
    return conta;
  }

  private ProdutoInstituicao obterProdutoInstituicao(ContaPagamento conta) {
    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(conta.getIdProdutoInstituicao());
    if (Objects.isNull(produtoInstituicao)) {
      throw new GenericServiceException("ProdutoInstituicao não encontrado.");
    }
    return produtoInstituicao;
  }

  private void validarContaJaVinculada(ContaPessoa contaPessoa) {
    ContaPessoa contaPessoaTmp = findById(contaPessoa.getContaPessoaId());
    if (Objects.nonNull(contaPessoaTmp)) {
      Map<String, Object> map = new HashMap();
      map.put("created", FALSE);
      map.put("msg", "Essa pessoa já possui uma conta vinculada a essa titularidade!");
      throw new GenericServiceException(map);
    }
  }

  @Transactional
  public void definirLimiteContaPos(ContaPagamento model, ProdutoInstituicao produtoInstituicao) {

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0);

    // account code
    String accountCode = model.getIdAccountCode();

    // layer
    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();
    StringBuilder sbLimite = new StringBuilder();
    sbLimite.append("2" + moeda);
    String moedaLimite = sbLimite.toString();

    // type
    Integer type = TYPE_CREDITO;

    // amount
    Double amount = model.getLimiteUnico();

    // journal
    HierarquiaInstituicaoId id =
        new HierarquiaInstituicaoId(model.getIdProcessadora(), model.getIdInstituicao());
    HierarquiaInstituicao instituicao = instituicaoService.findById(id);
    String journal = instituicao.getJournal();

    // serviço do JCARD
    createAccountBalance(amount, accountCode, type, Integer.parseInt(moedaLimite), journal);
  }

  private void createAccountBalance(
      Double amount, String accountCode, Integer type, Integer layer, String journalName) {
    CreateAccountBalance create =
        AccountBalanceService.prepareCreateAccountBalance(
            amount, accountCode, type, layer, journalName);
    JcardResponse response = accountBalanceService.createAccountBalance(create);

    if (!response.getSuccess()) {
      throw new GenericServiceException("Não foi possível difinir limite: " + response.getErrors());
    }
  }

  @Transactional
  public void createAccount(Pessoa pessoa, ContaPagamento conta) {
    ProdutoInstituicaoConfiguracao produto =
        prodInstConfService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());
    ProdutoInstituidor produtoInstituidor = produto.getProdutoInstituidor();
    MoedaConta moeda = produto.getMoeda();

    HierarquiaInstituicao instituicao =
        instituicaoService.findById(
            new HierarquiaInstituicaoId(pessoa.getIdProcessadora(), pessoa.getIdInstituicao()));

    CreateAccount createAccount =
        AccountService.prepareToCreateAccount(
            pessoa, conta, produto, instituicao, produtoInstituidor, 0.0);

    String journal = instituicao.getJournal();

    GsonBuilder gsonBuilder = new GsonBuilder();
    gsonBuilder.serializeNulls();
    Gson gson = gsonBuilder.create();

    info("CREATEACCOUNT REQUEST->" + gson.toJson(createAccount));

    CreateAccountResponse response = accountService.createAccount(createAccount, journal);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Ocorreu um erro ao executar createAccount. " + response.getErrors());
    }

    conta.setIdAccountCode(createAccount.getCode());
    conta.setIdAcct(response.getId());
  }

  public ContaPessoa vincularPessoaAdicionalConta(
      Pessoa pessoa, ContaPagamento conta, ContaPessoa contaPessoaReq) {

    ContaPessoa contaPessoa = new ContaPessoa();

    ContaPessoaId contaPessoaId = new ContaPessoaId();
    contaPessoaId.setContaPagamento(conta);
    contaPessoaId.setPessoa(pessoa);

    contaPessoa.setContaPessoaId(contaPessoaId);
    contaPessoa.setIdConta(contaPessoaReq.getIdConta());
    contaPessoa.setContaPagamento(conta);
    contaPessoa.setIdPessoa(contaPessoaReq.getIdPessoa());
    contaPessoa.setPessoa(pessoa);
    contaPessoa.setIdTitularidade(contaPessoaReq.getIdTitularidade());
    LocalDateTime now = LocalDateTime.now();
    contaPessoa.setDataHoraInclusao(now);
    contaPessoa.setDataHoraStatus(now);
    contaPessoa.setStatus(BLOQUEIO_CRIACAO);

    ContaPessoa contaPessoaTmp = findById(contaPessoa.getContaPessoaId());

    if (contaPessoaTmp != null) {
      throw new GenericServiceException(
          "Essa pessoa já possui esta conta vinculada com essa titularidade!");
    }

    ContaPessoa cp = save(contaPessoa);
    contaService.save(conta);

    return cp;
  }

  public Integer isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndTitularidade(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      Integer idProdutoInstituicao) {
    return contaPessoaRepository
        .isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndTitularidade(
            idProcessadora, idInstituicao, documento, idProdutoInstituicao);
  }

  public Integer
      isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
          Integer idProcessadora,
          Integer idInstituicao,
          String documento,
          Integer idProdutoInstituicao,
          Integer idPontoRelacionamento) {
    return contaPessoaRepository
        .isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
            idProcessadora, idInstituicao, documento, idProdutoInstituicao, idPontoRelacionamento);
  }
}
