package br.com.sinergico.repository.suporte;

import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface HierarquiaPontoDeRelacionamentoRepository
    extends JpaRepository<HierarquiaPontoDeRelacionamento, HierarquiaPontoDeRelacionamentoId> {

  @Query(
      value =
          "select "
              + " * "
              + "from "
              + " suporte.hierarquia_ponto_de_relacionamento hpdr "
              + "where "
              + " hpdr.id_instituicao = :idInstituicao "
              + " and hpdr.id_processadora = 10 "
              + " and hpdr.id_regional = :idRegional "
              + " and hpdr.id_filial = :idFilial "
              + " and hpdr.id_ponto_de_relacionamento = :idPontoDeRelacionamento",
      nativeQuery = true)
  HierarquiaPontoDeRelacionamento findHierarquiaPontoDeRelacionamentoByIds(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);
}
