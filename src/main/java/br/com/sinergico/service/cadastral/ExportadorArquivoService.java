package br.com.sinergico.service.cadastral;

import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PessoaContaBancaria;
import br.com.entity.cadastral.PreLancamentoLote;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.loyalty.ResgateContaBancaria;
import br.com.entity.loyalty.ResgateGenericoLoyalty;
import br.com.entity.loyalty.TituloCapitalizacao;
import br.com.entity.suporte.ArquivoDownload;
import br.com.entity.suporte.ArquivoDownloadConfiguracao;
import br.com.entity.suporte.CotacaoPontos;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.InterfaceInstituicao;
import br.com.entity.suporte.LogArqPontoRecebido;
import br.com.entity.suporte.LogExportacaoArquivo;
import br.com.entity.transacional.LoyLogSafra;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.ArquivoCSVRequest;
import br.com.json.bean.cadastral.ArquivoCvRequest;
import br.com.json.bean.cadastral.CargaCreditoExportacaoTO;
import br.com.json.bean.cadastral.DetalhePreLancamentoLote;
import br.com.json.bean.cadastral.ParceiroResgateResponse;
import br.com.json.bean.cadastral.PedidoExportacao;
import br.com.json.bean.cadastral.PortadorCartaoPreEmitidoTO;
import br.com.json.bean.cadastral.PortadorVinculacaoTO;
import br.com.json.bean.loyalty.ResgateLoyaltyResponse;
import br.com.json.bean.loyalty.TituloCapitalizacaoDadosPagamentoVO;
import br.com.json.bean.loyalty.TituloCapitalizacaoVO;
import br.com.json.bean.suporte.CadastrarArquivoDownload;
import br.com.json.bean.suporte.CadastrarArquivoDownloadConfiguracao;
import br.com.sinergico.facade.transacional.ResgateLoyaltyFacade;
import br.com.sinergico.repository.suporte.InterfaceInstituicaoRepository;
import br.com.sinergico.security.CriptoUtil;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.loyalty.ExportadorTituloCapitalizaoGanhadoresPdf;
import br.com.sinergico.service.loyalty.ExportadorTituloCapitalizaoTxt;
import br.com.sinergico.service.loyalty.ResgateContaBancariaService;
import br.com.sinergico.service.loyalty.TipoRegistroEnum;
import br.com.sinergico.service.suporte.AgenciaService;
import br.com.sinergico.service.suporte.ArquivoDownloadConfiguracaoService;
import br.com.sinergico.service.suporte.ArquivoDownloadService;
import br.com.sinergico.service.suporte.CotacaoPontosService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.LogArqPontoRecebidoService;
import br.com.sinergico.service.suporte.LogExportacaoArquivoService;
import br.com.sinergico.service.transacional.LoyLogSafraService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.DocumentoUtil;
import br.com.sinergico.util.Util;
import com.itextpdf.text.DocumentException;
import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.NoResultException;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jpos.security.SMException;
import org.jpos.security.SecureKeyStore.SecureKeyStoreException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.DigestUtils;

@Service
public class ExportadorArquivoService {

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ExportadorPortadorCartaoPreEmitidoCargaTxt exportadorPortadorPreEmitidoTxt;

  @Autowired private ArquivoDownloadConfiguracaoService arquivoDownloadConfiguracaoService;

  @Autowired private ArquivoDownloadService arquivoDownloadService;

  @Autowired private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

  @Autowired private CredencialService credencialService;

  @Autowired private PreLancamentoLoteService prelancamentoLoteService;

  @Autowired private CardService cardService;

  @Autowired private DateUtil dateUtil;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private LogExportacaoArquivoService logExportacaoArquivoService;

  @Autowired private AgenciaService agenciaService;

  @Autowired private ResgateContaBancariaService resgateContaBancariaService;

  @Autowired private PessoaContaBancariaService pessoaContaBancariaService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private ProdutoContratadoService produtoContratadoService;

  @Autowired private LoyLogSafraService loyLogSafraService;

  @Autowired private ParceiroResgateService parceiroResgateService;

  @Autowired private ResgateLoyaltyFacade resgateLoyaltyFacade;

  @Autowired private ExportadorTituloCapitalizaoTxt exportadorTituloCapitalizaoTxt;

  @Autowired private InterfaceInstituicaoRepository interfaceInstituicaoRepository;

  @Autowired private CotacaoPontosService cotacaoPontosService;

  @Autowired private LogArqPontoRecebidoService logArqPontoRecebidoService;

  @Autowired
  private ExportadorTituloCapitalizaoGanhadoresPdf exportadorTituloCapitalizaoGanhadoresPdf;

  @Value("${api.dir.emissores}")
  private String apiDirEmissores;

  @Transactional
  public void definirQualCvGerar(SecurityUser user, ArquivoCvRequest model, String idCv)
      throws SecureKeyStoreException, SMException {
    if (Constantes.CV16.equals(idCv)) {
      findPortadoresAptosAdquirirCartaoPreEmitido(user, model, idCv);
    } else if (Constantes.CV14.equals(idCv)) {
      montarPedidoCargaCabal(user, model, idCv);
    } else if (Constantes.CV100.equals(idCv)) {
      montarCv100(model, user);
    } else if (Constantes.CV01.equals(idCv)) {
      montarCv01(model, user);
    }
  }

  @Transactional
  private List<PortadorCartaoPreEmitidoTO> findPortadoresAptosAdquirirCartaoPreEmitido(
      SecurityUser user, ArquivoCvRequest model, String idCv)
      throws SecureKeyStoreException, SMException {

    HierarquiaPontoDeRelacionamento pontorelac =
        buscarEmpresaWithUser(model.getIdPontoDeRelacionamento(), user);

    ProdutoContratado prodUtilizado = localizarProdutoContratado(model, pontorelac);

    ProdutoInstituicaoConfiguracao prodConf =
        localizarProdutoInstituicaoConfiguracao(model, pontorelac);

    Date dataAtual = new Date();

    List<PortadorCartaoPreEmitidoTO> tos =
        contaPagamentoService.findCredencialParaPreEmissao(model);

    if (tos == null || tos.isEmpty()) {
      throw new GenericServiceException(
          "Não há portadores com credenciais pré-emitidas para vinculação.");
    }

    Integer qtd = contaPagamentoService.countCredencialParaPreEmissao(model);

    for (int i = 0; i < tos.size(); i++) {
      tos.get(i)
          .setNumeroCartao(
              CriptoUtil.getConteudoEmClaro(tos.get(i).getNumeroCartao(), Constantes.ZPK_001));
      if (i == 0) {
        tos.get(i).setIdRegHeader(Constantes.ID_REG_HEADER);
      }
      tos.get(i).setCodConvenio("0" + prodConf.getIdProdutoExterno());
      tos.get(i).setCodEmpresa(prodUtilizado.getIdentificadorExterno());
      tos.get(i).setDataGeracaoArquivo(dataAtual);
      tos.get(i).setIdRegDetalhe(Constantes.ID_REG_DETALHE);
      tos.get(i).setTipoDoc(Constantes.COD_DOC_CPF_1);
      if (i == tos.size() - 1) {
        tos.get(i).setIdRegTrailer(Constantes.ID_REG_TRAILER);
      }
      tos.get(i).setTotalVinculacoes(qtd.toString());
    }

    String dataGeracao = dateUtil.dateFormat("ddMMyyyy", dataAtual);
    String nomeEmpresa = pontorelac.getDescricao().toUpperCase();
    String[] primeiroNomeEmpresa = nomeEmpresa.split(" ");
    String nomeArquivo =
        "CV16"
            + "_"
            + "0"
            + prodConf.getIdProdutoExterno()
            + "_"
            + (prodUtilizado.getIdentificadorExterno() != null
                ? prodUtilizado.getIdentificadorExterno()
                : pontorelac.getIdPontoDeRelacionamento())
            + "_"
            + primeiroNomeEmpresa[0]
            + "_"
            + dataGeracao
            + ".txt";

    FileOutputStream in =
        exportadorPortadorPreEmitidoTxt.getExportacaoPortador(
            tos, Integer.valueOf(idCv), nomeArquivo);

    validaOutputStreamExportador(in, user, dataAtual, idCv, pontorelac, nomeArquivo);

    marcarCredencialVinculada(tos);

    return tos;
  }

  private void validaOutputStreamExportador(
      FileOutputStream in,
      SecurityUser user,
      Date dataAtual,
      String idCv,
      HierarquiaPontoDeRelacionamento pontorelac,
      String nomeArquivo) {
    if (in != null) {
      String path = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO + nomeArquivo;

      String md5 = hashCodeArquivo(path);

      registrarArquivosCVConfiguracaoDownload(user, dataAtual, idCv, pontorelac, nomeArquivo, md5);
    } else {
      throw new GenericServiceException("Erro ao gerar arquivo txt posicional.");
    }
  }

  @Transactional
  private void marcarCredencialVinculada(List<PortadorCartaoPreEmitidoTO> tos) {
    Date dataEmissao = new Date();
    for (PortadorCartaoPreEmitidoTO portadorCartaoPreEmitidoTO : tos) {
      Credencial cred = credencialService.findById(portadorCartaoPreEmitidoTO.getIdCredencial());
      cred.setDataHoraEmitido(dateUtil.dateToLocalDateTime(dataEmissao));
      credencialService.save(cred);
      if (cred.getDataHoraEmitido() == null) {
        throw new GenericServiceException("Credencial não confirmada com data de emissão.");
      }
    }
  }

  @Transactional
  private void registrarArquivosCVConfiguracaoDownload(
      SecurityUser user,
      Date dataAtual,
      String idCv,
      HierarquiaPontoDeRelacionamento pontorelac,
      String nomeArquivo,
      String arqHashCode) {

    CadastrarArquivoDownloadConfiguracao modelArqConfig =
        new CadastrarArquivoDownloadConfiguracao();
    CadastrarArquivoDownload modelArq = new CadastrarArquivoDownload();

    ArquivoDownloadConfiguracao arquivoDownloadConfiguracao = new ArquivoDownloadConfiguracao();
    ArquivoDownload arquivoDownload = new ArquivoDownload();

    modelArqConfig.setIdProcessadora(pontorelac.getIdProcessadora());
    modelArqConfig.setIdInstituicao(pontorelac.getIdInstituicao());
    modelArqConfig.setIdRegional(pontorelac.getIdRegional());
    modelArqConfig.setIdFilial(pontorelac.getIdFilial());
    modelArqConfig.setIdPontoDeRelacionamento(pontorelac.getIdPontoDeRelacionamento());
    if (Constantes.CV16.equals(idCv.toString())) {
      modelArqConfig.setPrefixo("CV" + Constantes.CV16);
      modelArqConfig.setDescricao("VINCULAÇÃO DE PORTADORES EM CARTÕES PRÉ-EMITIDOS CABAL");
    } else if (Constantes.CV14.equals(idCv.toString())) {
      modelArqConfig.setPrefixo("CV" + Constantes.CV14);
      modelArqConfig.setDescricao("CARGA DE CRÉDITO EM CARTÕES");
    } else if (Constantes.CV100.equals(idCv.toString())) {
      modelArqConfig.setPrefixo("CV" + Constantes.CV100);
      modelArqConfig.setDescricao("DADOS CADASTRAIS DE EMPRESA PARA VISA");
    } else if (Constantes.CV01.equals(idCv.toString())) {
      modelArqConfig.setPrefixo("CV0" + Constantes.CV01);
      modelArqConfig.setDescricao("DADOS CADASTRAIS DE PORTADORES PARA VISA");
    }
    modelArqConfig.setExtensao("txt");
    modelArqConfig.setNomeArquivo(nomeArquivo);

    arquivoDownloadConfiguracao =
        arquivoDownloadConfiguracaoService.prepareArquivoDownloadConfiguracao(
            modelArqConfig, arquivoDownloadConfiguracao, user);
    if (arquivoDownloadConfiguracao == null) {
      throw new GenericServiceException("Erro ao salvar Configuração de Arquivo de download.");
    }

    modelArq.setIdDownloadConf(arquivoDownloadConfiguracao.getIdDownloadConf());
    modelArq.setDescricao(arquivoDownloadConfiguracao.getDescricao());
    modelArq.setDiretorio(apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO);
    modelArq.setDtDisponibilizacao(DateUtil.dateToLocalDateTime(dataAtual));
    modelArq.setHash(arqHashCode.toString());
    modelArq.setNomeArquivo(arquivoDownloadConfiguracao.getNomeArquivo());

    arquivoDownload =
        arquivoDownloadService.prepareArquivoDownload(
            modelArq, arquivoDownload, arquivoDownloadConfiguracao, user);
    if (arquivoDownload == null) {
      throw new GenericServiceException("Erro ao salvar Arquivo de download.");
    }
  }

  @Transactional
  private ArquivoDownload registrarArquivosCSVConfiguracaoDownload(
      SecurityUser user,
      Date dataAtual,
      Integer idProcessadora,
      Integer idInstituicao,
      String nomeArquivo,
      String arqHashCode) {

    CadastrarArquivoDownloadConfiguracao modelArqConfig =
        new CadastrarArquivoDownloadConfiguracao();
    CadastrarArquivoDownload modelArq = new CadastrarArquivoDownload();

    ArquivoDownloadConfiguracao arquivoDownloadConfiguracao = new ArquivoDownloadConfiguracao();
    ArquivoDownload arquivoDownload = new ArquivoDownload();

    modelArqConfig.setIdProcessadora(idProcessadora);
    modelArqConfig.setIdInstituicao(idInstituicao);
    modelArqConfig.setIdRegional(Constantes.ZERO_INTEGER);
    modelArqConfig.setIdFilial(Constantes.ZERO_INTEGER);
    modelArqConfig.setIdPontoDeRelacionamento(Constantes.ZERO_INTEGER);
    modelArqConfig.setDescricao("COMUNICAR TRANSFERÊNCIAS BANCÁRIAS ENTRE SISTEMAS");
    modelArqConfig.setPrefixo("BENNER");

    modelArqConfig.setExtensao("csv");
    modelArqConfig.setNomeArquivo(nomeArquivo);

    arquivoDownloadConfiguracao =
        arquivoDownloadConfiguracaoService.prepareArquivoDownloadConfiguracao(
            modelArqConfig, arquivoDownloadConfiguracao, user);
    if (arquivoDownloadConfiguracao == null) {
      throw new GenericServiceException("Erro ao salvar Configuração de Arquivo de download.");
    }

    modelArq.setIdDownloadConf(arquivoDownloadConfiguracao.getIdDownloadConf());
    modelArq.setDescricao(arquivoDownloadConfiguracao.getDescricao());
    modelArq.setDiretorio(apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO);
    modelArq.setDtDisponibilizacao(DateUtil.dateToLocalDateTime(dataAtual));
    modelArq.setHash(arqHashCode.toString());
    modelArq.setNomeArquivo(arquivoDownloadConfiguracao.getNomeArquivo());

    arquivoDownload =
        arquivoDownloadService.prepareArquivoDownload(
            modelArq, arquivoDownload, arquivoDownloadConfiguracao, user);
    if (arquivoDownload == null) {
      throw new GenericServiceException("Erro ao salvar Arquivo de download.");
    }
    return arquivoDownload;
  }

  @Transactional
  private void montarPedidoCargaCabal(SecurityUser user, ArquivoCvRequest model, String idCv)
      throws SMException, SecureKeyStoreException {

    HierarquiaPontoDeRelacionamento pontorelac =
        buscarEmpresaWithUser(model.getIdPontoDeRelacionamento(), user);

    ProdutoContratado prodUtilizado = localizarProdutoContratado(model, pontorelac);

    ProdutoInstituicaoConfiguracao prodConf =
        localizarProdutoInstituicaoConfiguracao(model, pontorelac);

    PreLancamentoLote lotePedido =
        prelancamentoLoteService.findPreLancamentoLoteByIdNotNull(model.getIdLote());

    Date dataAtual = new Date();

    BigDecimal vtl =
        new BigDecimal(String.valueOf(lotePedido.getValorTotalLancamento()))
            .setScale(2, BigDecimal.ROUND_FLOOR);

    String totalCargasSemPonto = vtl.toString().replace(".", "");

    List<CargaCreditoExportacaoTO> tos = new ArrayList<CargaCreditoExportacaoTO>();

    for (int i = 0; i < lotePedido.getPreLancamentos().size(); i++) {
      CargaCreditoExportacaoTO to = new CargaCreditoExportacaoTO();
      if (i == 0) {
        to.setIdentificador0(Constantes.ID_REG_HEADER);
        to.setCodConvenio("0" + prodConf.getIdProdutoExterno());
        to.setCodEmpresa(prodUtilizado.getIdentificadorExterno());
        to.setDataGeracao(dataAtual);
      }
      String numeroCartao =
          buscarNumeroCartaoByConta(
              lotePedido.getPreLancamentos().get(i).getIdConta(),
              Constantes.TITULARIDADE_CREDENCIAL);
      to.setIdentificador1(Constantes.ID_REG_DETALHE);
      if (numeroCartao.length() == 16 && numeroCartao.substring(0, 6).equals("000000")) {
        to.setNumeroCartao(numeroCartao);
      } else {
        to.setNumeroCartao(CriptoUtil.getConteudoEmClaro(numeroCartao, Constantes.ZPK_001));
      }
      BigDecimal vti =
          new BigDecimal(String.valueOf(lotePedido.getPreLancamentos().get(i).getValorLancamento()))
              .setScale(2, BigDecimal.ROUND_FLOOR);
      to.setValorCarga(vti.toString().replace(".", ""));
      to.setDescCarga(" ");
      to.setMotivo("0014"); // o que define este motivo?

      if (lotePedido.getPreLancamentos().size() - 1 == i) {
        to.setIdentificador9(Constantes.ID_REG_TRAILER);
        to.setQtdCargas(lotePedido.getQtdLancamento().toString());
        to.setTotoalCargas(totalCargasSemPonto);
      }
      tos.add(to);
    }

    String dataGeracao = dateUtil.dateFormat("ddMMyyyy", dataAtual);
    String nomeEmpresa = pontorelac.getDescricao().toUpperCase();
    String[] primeiroNomeEmpresa = nomeEmpresa.split(" ");
    String nomeArquivo =
        "CV14"
            + "_"
            + "0"
            + prodConf.getIdProdutoExterno()
            + "_"
            + prodUtilizado.getIdentificadorExterno()
            + "_"
            + dataGeracao
            + "_"
            + primeiroNomeEmpresa[0]
            + "_"
            + totalCargasSemPonto
            + ".txt";

    FileOutputStream in =
        exportadorPortadorPreEmitidoTxt.getExportacaoCarga(tos, Integer.valueOf(idCv), nomeArquivo);

    validaOutputStreamExportador(in, user, dataAtual, idCv, pontorelac, nomeArquivo);
  }

  @Transactional
  private String buscarNumeroCartaoByConta(Integer idConta, Integer titularidade) {

    List<Credencial> cards =
        credencialService.findByIdContaAndTitularidadde(idConta.longValue(), titularidade);

    if (cards.get(0).getIdCredencialExterna() == null) {
      GetCardResponse card = cardService.getPan(cards.get(0).getTokenInterno());
      return card.getCard().getPan();
    }
    return cards.get(0).getIdCredencialExterna();
  }

  @Transactional
  private String hashCodeArquivo(String path) {

    FileInputStream fis;
    String md5 = null;

    try {
      fis = new FileInputStream(new File(path));
      md5 = DigestUtils.md5DigestAsHex(fis);

    } catch (FileNotFoundException e) {
      NoResultException noResultException = new NoResultException("Arquivo não encontrado");
      noResultException.initCause(e);
      throw noResultException;
    } catch (IOException e) {
      NoResultException noResultException =
          new NoResultException("Arquivo não pode ser encontrado");
      noResultException.initCause(e);
      throw noResultException;
    }
    return md5;
  }

  @Transactional
  private HierarquiaPontoDeRelacionamento buscarEmpresaWithUser(
      Integer idPontoRelacionamento, SecurityUser user) {

    HierarquiaPontoDeRelacionamento pontorelac =
        hierarquiaPontoRelacionamentoService.getB2BPorId(idPontoRelacionamento, user);
    if (pontorelac == null) {
      throw new GenericServiceException(
          "Não foi localizado Ponto de Relacionamento para prosseguir esta operação.");
    }
    return pontorelac;
  }

  @Transactional
  private HierarquiaPontoDeRelacionamento buscarEmpresaWithHierarquiaId(
      Integer idProc, Integer idInst, Integer idRegional, Integer idFilial, Integer idPntRel) {

    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(idProc, idInst, idRegional, idFilial, idPntRel);
    HierarquiaPontoDeRelacionamento emp = hierarquiaPontoRelacionamentoService.findById(id);
    if (emp == null) {
      throw new GenericServiceException(
          "Não foi localizado Empresa para prosseguir esta operação.");
    }
    return emp;
  }

  @Transactional
  private void montarCv100(ArquivoCvRequest model, SecurityUser user) {

    Date dataAtual = new Date();

    HierarquiaPontoDeRelacionamento emp =
        buscarEmpresaWithUser(model.getIdPontoDeRelacionamento(), user);

    ProdutoInstituicaoConfiguracao prodConf = localizarProdutoInstituicaoConfiguracao(model, emp);

    ProdutoContratado prodUtilizado = localizarProdutoContratado(model, emp);

    String dataGeracao = dateUtil.dateFormat("ddMMyyyy", dataAtual);
    String nomeEmpresa = emp.getDescricao().toUpperCase();
    String[] primeiroNomeEmpresa = nomeEmpresa.split(" ");
    String nomeArquivo =
        "CV100"
            + "_"
            + "0"
            + prodConf.getIdProdutoExterno()
            + "_"
            + (prodUtilizado.getIdentificadorExterno() != null
                ? prodUtilizado.getIdentificadorExterno()
                : emp.getIdPontoDeRelacionamento())
            + "_"
            + primeiroNomeEmpresa[0]
            + "_"
            + dataGeracao
            + ".txt";

    FileOutputStream in =
        exportadorPortadorPreEmitidoTxt.getExportacaoEmpresa(
            prodUtilizado, emp, Integer.valueOf(Constantes.CV100), nomeArquivo);

    if (in != null) {
      String path = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO + nomeArquivo;

      String md5 = hashCodeArquivo(path);

      registrarArquivosCVConfiguracaoDownload(
          user, dataAtual, Constantes.CV100, emp, nomeArquivo, md5);
    } else {
      throw new GenericServiceException("Erro ao gerar arquivo txt posicional.");
    }
  }

  @Transactional
  private void montarCv01(ArquivoCvRequest model, SecurityUser user) {

    Date dataAtual = new Date();

    HierarquiaPontoDeRelacionamento emp =
        buscarEmpresaWithUser(model.getIdPontoDeRelacionamento(), user);

    ProdutoContratado prodUtilizado = localizarProdutoContratado(model, emp);

    ProdutoInstituicaoConfiguracao prodConf = localizarProdutoInstituicaoConfiguracao(model, emp);
    String prodConvenio = "0" + prodConf.getIdProdutoExterno();

    ProdutoInstituicao prod =
        produtoInstituicaoService.findByIdProdInstituicao(model.getIdProdutoInstituicao());

    List<PortadorVinculacaoTO> portadores =
        pessoaService.credencialNaoVinculadoExterno(
            model.getIdProdutoInstituicao(),
            emp.getIdProcessadora(),
            emp.getIdInstituicao(),
            emp.getIdRegional(),
            emp.getIdFilial(),
            emp.getIdPontoDeRelacionamento());
    if (portadores.isEmpty()) {
      throw new GenericServiceException(
          "Nenhum portador disponível para solicitação de cartão visa.");
    }
    String dataGeracao = dateUtil.dateFormat("ddMMyyyy", dataAtual);
    String nomeEmpresa = emp.getDescricao().toUpperCase();
    String[] primeiroNomeEmpresa = nomeEmpresa.split(" ");
    String nomeArquivo =
        "CV01"
            + "_"
            + prodConvenio
            + "_"
            + (prodUtilizado.getIdentificadorExterno() != null
                ? prodUtilizado.getIdentificadorExterno()
                : emp.getIdPontoDeRelacionamento())
            + "_"
            + primeiroNomeEmpresa[0]
            + "_"
            + dataGeracao
            + ".txt";

    FileOutputStream in =
        exportadorPortadorPreEmitidoTxt.getExportacaoPortadorVISA(
            prodConvenio,
            prodUtilizado,
            prod,
            emp,
            portadores,
            Integer.valueOf(Constantes.CV01),
            nomeArquivo);

    if (in != null) {
      String path = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO + nomeArquivo;

      String md5 = hashCodeArquivo(path);

      registrarArquivosCVConfiguracaoDownload(
          user, dataAtual, Constantes.CV01, emp, nomeArquivo, md5);
    } else {
      throw new GenericServiceException("Erro ao gerar arquivo txt posicional.");
    }
  }

  @Transactional
  private ProdutoContratado localizarProdutoContratado(
      ArquivoCvRequest model, HierarquiaPontoDeRelacionamento pontorelac) {

    ProdutoContratado prodUtilizado =
        produtoContratadoService.findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            pontorelac.getIdProcessadora(),
            pontorelac.getIdInstituicao(),
            pontorelac.getIdRegional(),
            pontorelac.getIdFilial(),
            pontorelac.getIdPontoDeRelacionamento(),
            model.getIdProdutoInstituicao());

    if (prodUtilizado == null) {
      throw new GenericServiceException("Não foi possível localizar o produto contratado.");
    }

    if (prodUtilizado.getIdentificadorExterno() == null) {
      throw new GenericServiceException("O produto contratado não contem Identificador Externo.");
    }
    return prodUtilizado;
  }

  @Transactional
  private ProdutoInstituicaoConfiguracao localizarProdutoInstituicaoConfiguracao(
      ArquivoCvRequest model, HierarquiaPontoDeRelacionamento pontorelac) {

    ProdutoInstituicaoConfiguracao prod =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                pontorelac.getIdProcessadora(),
                model.getIdProdutoInstituicao(),
                pontorelac.getIdInstituicao());

    if (prod == null) {
      throw new GenericServiceException(
          "Não foi possível localizar a configuração do prouduto para esta empresa.");
    }

    if (prod.getIdProdutoExterno() == null) {
      throw new GenericServiceException(
          "O 'Produto Instituicao Configuração' utilizado não contem Identificador Produto Externo.");
    }
    return prod;
  }

  @Transactional
  public void montarCSVPedidosTransferencia(SecurityUser user, ArquivoCSVRequest model)
      throws IOException {

    Date dataAtual = new Date();

    String dataGeracao = dateUtil.dateFormat("ddMMyyyy", dataAtual);
    String pathFile = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO;
    String nameFile = "pedidos_transferencia_bancaria_" + dataGeracao + ".csv";

    BufferedWriter fileWriter = null;
    InputStream inputStream = null;
    PreLancamentoLote lotePedido = new PreLancamentoLote();
    List<DetalhePreLancamentoLote> detalhes = new ArrayList<DetalhePreLancamentoLote>();

    try {

      Writer writer = new OutputStreamWriter(new FileOutputStream(pathFile + nameFile), "UTF-8");
      fileWriter = new BufferedWriter(writer);

      StringBuilder sb = new StringBuilder();

      fileWriter = montarCabecalhoCsv(fileWriter);

      for (PedidoExportacao pedido : model.getListaPedidos()) {

        lotePedido = prelancamentoLoteService.findPreLancamentoLoteByIdNotNull(pedido.getIdLote());

        ProdutoInstituicaoConfiguracao prodConfig =
            buscarConfiguracaoProduto(
                lotePedido.getIdProcessadora(),
                lotePedido.getIdInstituicao(),
                lotePedido.getIdProdutoInstituicao());

        if (Constantes.PROD_PLATAFORMA_TRANSFERENCIA_BANCARIA.equals(
            prodConfig.getIdProdutoPlataforma())) {
          if (prodConfig.getCargaIntegracao() != null && prodConfig.getCargaIntegracao()) {
            detalhes.addAll(
                prelancamentoLoteService.findDetalhePreLancamentoLoteById(
                    pedido.getIdLote(), Boolean.TRUE, user));
          } else {
            detalhes =
                prelancamentoLoteService.findDetalhePreLancamentoLoteById(
                    pedido.getIdLote(), Boolean.TRUE, user);
          }
          fileWriter =
              montarConteudoCsvProdTransferenciasBancarias(lotePedido, detalhes, fileWriter);
        } else if (Constantes.PROD_PLATAFORMA_BROKER_INST_EMISSORA_EXTERNA.equals(
            prodConfig.getIdProdutoPlataforma())) {
          fileWriter = montarConteudoCsvCartoes(lotePedido, fileWriter, prodConfig);
          if (prodConfig.getCargaIntegracao() != null && prodConfig.getCargaIntegracao()) {
            detalhes.addAll(
                prelancamentoLoteService.findDetalhePreLancamentoLoteById(
                    pedido.getIdLote(), Boolean.TRUE, user));
          }
        }

        lotePedido.setDataHoraUltimaExportacao(LocalDateTime.now());

        prelancamentoLoteService.save(lotePedido);
      }
      fileWriter.close();
      if (detalhes != null && !detalhes.isEmpty()) {
        for (DetalhePreLancamentoLote detalhePreLancamento : detalhes) {
          if (detalhePreLancamento.getCargaIntegracao() != null
              && detalhePreLancamento.getCargaIntegracao()) {
            inputStream = criaArquivoXlsx(detalhes);
          }
        }
      }
    } catch (IOException e) {
      e.printStackTrace();
    }

    ArquivoDownload arquivoDownload = null;
    ArquivoDownload arquivoDownloadDetalhe = null;

    if (fileWriter != null) {
      String path = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO + nameFile;

      String md5 = hashCodeArquivo(path);

      PreLancamentoLote lotePedidoHierarquia =
          prelancamentoLoteService.findPreLancamentoLoteByIdNotNull(
              model.getListaPedidos().get(0).getIdLote());

      arquivoDownload =
          registrarArquivosCSVConfiguracaoDownload(
              user,
              dataAtual,
              lotePedidoHierarquia.getIdProcessadora(),
              lotePedidoHierarquia.getIdInstituicao(),
              nameFile,
              md5);

      // disponibilza o novo arquivo xlsx na aba de download nesse fluxo somente para produtos da
      // integracao com coluna carga_integracao igual a true.
      if (inputStream != null) {

        LogArqPontoRecebido logArqPontoRecebido =
            logArqPontoRecebidoService.findOneByUltimoSeqArquivoAndIdParceiroAcumuloAndStatus(
                lotePedidoHierarquia.getIdProcessadora(),
                Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL,
                Constantes.PARCEIRO_ACUMULO_IN_MAIS_PREMIOS,
                LogArqPontoRecebidoService.STATUS_REGISTRADO);
        String proximoSequencial = String.valueOf(logArqPontoRecebido.getSeqArquivo() + 1);
        String caminhoArquivo = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO;
        File file =
            new File(
                caminhoArquivo
                    + proximoSequencial
                    + "_carga_pontos_integracao_"
                    + dataGeracao
                    + ".xlsx");
        copyInputStreamToFile(inputStream, file);
        String md5Detalhe = hashCodeArquivo(file.getAbsolutePath());
        arquivoDownloadDetalhe =
            registrarArquivosCSVConfiguracaoDownload(
                user,
                dataAtual,
                lotePedidoHierarquia.getIdProcessadora(),
                Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL,
                file.getName(),
                md5Detalhe);
      }

    } else {
      throw new GenericServiceException("Erro ao gerar arquivo CSV.");
    }

    for (PedidoExportacao pedido : model.getListaPedidos()) {
      LogExportacaoArquivo logExport =
          new LogExportacaoArquivo(
              pedido.getIdLote(),
              arquivoDownload.getId(),
              Constantes.TIPO_LOG_EXPORTACAO_ARQUIVO_PEDIDO_CARGA);
      if (arquivoDownloadDetalhe != null) {
        LogExportacaoArquivo logExportDetalhe =
            new LogExportacaoArquivo(
                pedido.getIdLote(),
                arquivoDownloadDetalhe.getId(),
                Constantes.TIPO_LOG_EXPORTACAO_ARQUIVO_PEDIDO_CARGA);
        logExportacaoArquivoService.save(logExportDetalhe);
      }
      logExportacaoArquivoService.save(logExport);
    }
  }

  private static void copyInputStreamToFile(InputStream inputStream, File file) throws IOException {

    // append = false
    try (FileOutputStream outputStream = new FileOutputStream(file, false)) {
      int read;
      byte[] bytes = new byte[Constantes.DEFAULT_BUFFER_SIZE];
      while ((read = inputStream.read(bytes)) != -1) {
        outputStream.write(bytes, 0, read);
      }
    }
  }

  @Transactional
  public void montarCSVResgatesContaBancaria(SecurityUser user, ArquivoCSVRequest model)
      throws SMException, SecureKeyStoreException {

    Date dataAtual = new Date();
    String dataGeracao = dateUtil.dateFormat("ddMMyyyy", dataAtual);

    String pathFile = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO;

    String nameFile = null;
    if (model.isResgateContaBancaria())
      nameFile = "resgates_inmais_credito_conta_corrente" + dataGeracao + ".csv";
    else nameFile = "resgates_inmais_demais_resgates_" + dataGeracao + ".csv";

    BufferedWriter fileWriter = null;

    try {
      Writer writer = new OutputStreamWriter(new FileOutputStream(pathFile + nameFile), "UTF-8");
      fileWriter = new BufferedWriter(writer);

      StringBuilder sb = new StringBuilder();

      fileWriter = montarCabecalhoCsv(fileWriter);

      if (model.isResgateContaBancaria())
        fileWriter = montarConteudoCsvResgateInmaisTransferenciasBancarias(fileWriter, user, model);
      else fileWriter = montarConteudoCsvResgateInmaisDemaisTipos(fileWriter, user, model);

      fileWriter.close();
    } catch (IOException e) {
      e.printStackTrace();
    }

    ArquivoDownload arquivoDownload = null;

    if (fileWriter != null) {
      String path = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO + nameFile;

      String md5 = hashCodeArquivo(path);

      arquivoDownload =
          registrarArquivosCSVConfiguracaoDownload(
              user, dataAtual, model.getIdProcessadora(), model.getIdInstituicao(), nameFile, md5);
    } else {
      throw new GenericServiceException("Erro ao gerar arquivo CSV.");
    }

    // COMENTAR AQUI PARA RODAR LOCALMENTE
    //		for (ResgateLoyaltyResponse resgate : model.getListaResgates()) {
    //			LogExportacaoArquivo logExport = new
    // LogExportacaoArquivo(resgate.getIdResgate().intValue(), arquivoDownload.getId(),
    // Constantes.TIPO_LOG_EXPORTACAO_ARQUIVO_RESGATE_CONTA_BANCARIA);
    //			logExportacaoArquivoService.save(logExport);
    //		}
  }

  private BufferedWriter montarCabecalhoCsv(BufferedWriter fileWriter) {

    try {
      fileWriter
          .append("NUMERO_PEDIDO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("NOME_CLIENTE")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("CNPJ_CLIENTE")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("COD_BANCO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("COD_AGENCIA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("DV_AGENCIA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("COD_CONTA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("DV_CONTA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("TIPO_CONTA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("FAVORECIDO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("CGCCPF")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("TIPO_FAVORECIDO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("VALOR")
          .append('\n');
    } catch (IOException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    }
    return fileWriter;
  }

  private BufferedWriter montarCabecalhoCsvResgate(BufferedWriter fileWriter) {

    try {
      fileWriter
          .append("NOME_PARCEIRO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("CNPJ_PARCEIRO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("NUM_SOLICITACAO") // corresponde ao RRN
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("DATA_SOLICITACAO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("TIPO_RESGATE")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("FORMA_LIQUIDACAO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("COD_BANCO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("COD_AGENCIA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("DV_AGENCIA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("COD_CONTA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("DV_CONTA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("TIPO_CONTA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("FAVORECIDO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("CGCCPF")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("TIPO_FAVORECIDO")
          .append(Constantes.DEFAULT_SEPARATOR)
          // .append("DESP_RESGATE_PONTOS_BONIFICADOS")
          // .append(Constantes.DEFAULT_SEPARATOR)
          // .append("DESP_RESGATE_PONTOS")
          // .append(Constantes.DEFAULT_SEPARATOR)
          .append("VALOR EM REAIS")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("VALOR RESGATE DE PONTOS")
          .append('\n');

    } catch (IOException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    }
    return fileWriter;
  }

  private BufferedWriter montarConteudoCsvProdTransferenciasBancarias(
      PreLancamentoLote lotePedido,
      List<DetalhePreLancamentoLote> detalhes,
      BufferedWriter fileWriter) {

    HierarquiaPontoDeRelacionamento emp =
        buscarEmpresaWithHierarquiaId(
            lotePedido.getIdProcessadora(),
            lotePedido.getIdInstituicao(),
            lotePedido.getIdRegional(),
            lotePedido.getIdFilial(),
            lotePedido.getIdPontoDeRelacionamento());

    for (DetalhePreLancamentoLote detalhePreLancLote : detalhes) {
      Pessoa pessoa = new Pessoa();
      pessoa = buscarPessoaByConta(detalhePreLancLote.getIdConta().longValue());
      verificaDadosBancarios(pessoa);

      int dvAg = modulo11DV(pessoa.getIdAgencia());

      try {
        fileWriter
            .append(detalhePreLancLote.getIdLote().toString())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(emp.getDescricao())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(mascaraDocumento(emp.getDocumento()))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(pessoa.getIdBanco().toString())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(pessoa.getIdAgencia().toString())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(dvAg == 10 ? "X" : Integer.toString(dvAg))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(contaSemDV(pessoa.getContaBancariaX()))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(digitoVerificadorConta(pessoa.getContaBancariaX()))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(descTipoContaBancaria(pessoa.getTipoContaBancaria(), pessoa.getDocumento()))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(pessoa.getNomeCompleto())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(mascaraDocumento(pessoa.getDocumento()))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(descTipoPessoa(pessoa))
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(detalhePreLancLote.getValorLancamento().toString())
            .append('\n');
      } catch (IOException e) {
        // TODO Auto-generated catch block
        e.printStackTrace();
      }
    }

    return fileWriter;
  }

  private BufferedWriter montarConteudoCsvResgateInmaisTransferenciasBancarias(
      BufferedWriter fileWriter, SecurityUser user, ArquivoCSVRequest model) {

    BufferedWriter fileWriterAux = fileWriter;
    for (ResgateLoyaltyResponse resgate : model.getListaResgates()) {
      Pessoa pessoa = new Pessoa();
      ContaPagamento contaPagamento = new ContaPagamento();
      ResgateContaBancaria resgateContaBancaria = new ResgateContaBancaria();
      //			HierarquiaInstituicao instituicao = new HierarquiaInstituicao();

      resgateContaBancaria = buscarResgateContaBancariaComException(resgate);

      pessoa = buscarPessoaByConta(resgateContaBancaria.getIdConta().longValue());

      PessoaContaBancaria contaBancaria =
          buscarPessoaContaBancariaComException(resgateContaBancaria.getIdContaBancaria());

      contaPagamento = contaPagamentoService.findByIdNotNull(resgateContaBancaria.getIdConta());

      validaHierarquiaDaConta(
          contaPagamento.getIdProcessadora(),
          contaPagamento.getIdInstituicao(),
          model.getIdProcessadora(),
          model.getIdInstituicao());

      //			ProdutoInstituicaoConfiguracao prodConfig =
      // buscarConfiguracaoProduto(contaPagamento.getIdProcessadora(),
      // contaPagamento.getIdInstituicao(), contaPagamento.getIdProdutoInstituicao());

      //			instituicao = buscarinstituicaoComException(contaPagamento.getIdProcessadora(),
      // contaPagamento.getIdInstituicao());

      ParceiroResgateResponse parceiro =
          parceiroResgateService.findByRrn(resgateContaBancaria.getRrn());

      // buscar total de pontos afetados por esse resgate em log safras
      Long totalAcumulo = 0L;
      Long totalBonificado = 0L;
      List<LoyLogSafra> pontosAfetados =
          loyLogSafraService.findSafrasByTransacao(resgateContaBancaria.getRrn());
      for (LoyLogSafra ponto : pontosAfetados) {
        if (ponto.getTipoSafra() == Constantes.TIPO_PONTO_ACUMULO.longValue())
          totalAcumulo += ponto.getPtoTransacao() * -1;
        if (ponto.getTipoSafra() == Constantes.TIPO_PONTO_BONIFICACAO.longValue())
          totalBonificado += ponto.getPtoTransacao() * -1;
      }

      // TODO verificar necessidade de buscar/verificar agencia no banco
      //			Agencia agencia =
      // agenciaService.findByCodAgenciaAndCodBanco(contaBancaria.getIdAgencia().intValue(),
      // contaBancaria.getBanco().getIdBanco());
      //			if(agencia==null){
      //				throw new GenericServiceException("Agência não cadastrada na base de dados");
      //			}
      Integer dvAg = null;

      if (Constantes.COD_BANCO_DO_BRASIL.equals(contaBancaria.getBanco().getIdBanco())
          && contaBancaria.getDvAgencia() == null) {
        dvAg = Util.modulo11DV(contaBancaria.getIdAgencia().intValue());
      } else if (!Constantes.COD_BANCO_DO_BRASIL.equals(contaBancaria.getBanco().getIdBanco())
          && contaBancaria.getDvAgencia() == null) {
        dvAg = null;
      } else {
        dvAg = contaBancaria.getDvAgencia().intValue();
      }

      try {

        if (resgateContaBancaria != null && !resgateContaBancaria.getRrn().isEmpty()) {
          fileWriterAux.append(resgateContaBancaria.getRrn());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        if (parceiro != null && !parceiro.getNomeParceiro().isEmpty()) {
          fileWriterAux.append(parceiro.getNomeParceiro());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        if (parceiro != null && !parceiro.getCnpj().isEmpty()) {
          fileWriterAux.append(mascaraDocumento(parceiro.getCnpj()));
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        /**
         * if(resgate != null && !resgate.getDataHoraInclusaoFormatada().isEmpty()) {
         * fileWriterAux.append(resgate.getDataHoraInclusaoFormatada());
         * fileWriterAux.append(Constantes.DEFAULT_SEPARATOR); }
         *
         * <p>fileWriterAux.append(Constantes.TIPO_RESGATE_CREDITO_CONTA_CORRENTE.toString());
         * fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
         *
         * <p>if(parceiro.getIdFormaLiquidacao() != null) {
         * fileWriterAux.append(parceiro.getIdFormaLiquidacao().toString());
         * fileWriterAux.append(Constantes.DEFAULT_SEPARATOR); } *
         */
        if (contaBancaria != null && contaBancaria.getBanco() != null) {
          fileWriterAux.append(contaBancaria.getBanco().getIdBanco().toString());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }

        if (contaBancaria.getIdAgencia() != null && contaBancaria.getIdAgencia() > 0L) {
          fileWriterAux.append(contaBancaria.getIdAgencia().toString());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        } else {
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }

        if (contaBancaria.getDvAgencia() != null && contaBancaria.getDvAgencia() > 0L) {
          fileWriterAux.append(contaBancaria.getDvAgencia().toString());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        } else {
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }

        if (contaBancaria.getContaBancaria() != null && contaBancaria.getContaBancaria() > 0L) {
          fileWriterAux.append(contaBancaria.getContaBancaria().toString());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        if (contaBancaria.getDvContaBancaria() != null && contaBancaria.getDvContaBancaria() > 0L) {
          fileWriterAux.append(contaBancaria.getDvContaBancaria().toString());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        if (contaBancaria.getTipoContaBancaria() != null) {
          fileWriterAux.append(String.valueOf(contaBancaria.getTipoContaBancaria().getValue()));
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        if (pessoa != null && !pessoa.getNomeCompleto().isEmpty()) {
          fileWriterAux.append(pessoa.getNomeCompleto());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }
        if (pessoa != null && !pessoa.getDocumento().isEmpty()) {
          fileWriterAux.append(pessoa.getDocumento());
          fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        }

        fileWriterAux.append("FISICA"); // sempre pessoa fisica para esse tipo de resgate
        fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
        /**
         * fileWriterAux.append((new BigDecimal(totalBonificado).divide(new BigDecimal(100) ,
         * RoundingMode.FLOOR)).toString()); fileWriterAux.append(Constantes.DEFAULT_SEPARATOR);
         * fileWriterAux.append((new BigDecimal(totalAcumulo).divide(new BigDecimal(100) ,
         * RoundingMode.FLOOR)).toString()); fileWriterAux.append(Constantes.DEFAULT_SEPARATOR); *
         */
        if (resgateContaBancaria != null && resgateContaBancaria.getValorPontos() != null) {
          BigDecimal valorEmReais =
              (resgateContaBancaria
                  .getValorPontos()
                  .divide(new BigDecimal(100), RoundingMode.FLOOR));
          fileWriterAux.append(valorEmReais.setScale(2).toString());
        }

        fileWriterAux.append('\n');

      } catch (IOException e) {
        // TODO Auto-generated catch block
        e.printStackTrace();
      }
      resgateContaBancaria.setDataHoraProc(LocalDateTime.now());
      resgateContaBancaria.setIdUsuarioProc(user.getIdUsuario());
      resgateContaBancaria.setStatusProc(1); // set como processado
      resgateContaBancariaService.save(resgateContaBancaria);
    }
    return fileWriterAux;
  }

  // TODO: demais tipos de resgates serao feitos aqui
  private BufferedWriter montarConteudoCsvResgateInmaisDemaisTipos(
      BufferedWriter fileWriter, SecurityUser user, ArquivoCSVRequest model) {

    for (ResgateLoyaltyResponse resgate : model.getListaResgates()) {
      Pessoa pessoa = new Pessoa();
      ContaPagamento contaPagamento = new ContaPagamento();

      ResgateGenericoLoyalty resgateInfo = new ResgateGenericoLoyalty();

      resgateInfo = buscarResgateComException(resgate);
      resgateInfo.setDataHoraResgate(resgate.getDataHoraInclusaoFormatada());

      pessoa = buscarPessoaByConta(resgateInfo.getIdConta());

      contaPagamento = contaPagamentoService.findByIdNotNull(resgateInfo.getIdConta());
      // contaPagamento = contaPagamentoService.findByIdNotNull(resgate.getIdConta());

      validaHierarquiaDaConta(
          contaPagamento.getIdProcessadora(),
          contaPagamento.getIdInstituicao(),
          model.getIdProcessadora(),
          model.getIdInstituicao());

      ParceiroResgateResponse parceiro = parceiroResgateService.findByRrn(resgateInfo.getRrn());

      // buscar total de pontos afetados por esse resgate em log safras
      Long totalAcumulo = 0L;
      Long totalBonificado = 0L;
      List<LoyLogSafra> pontosAfetados =
          loyLogSafraService.findSafrasByTransacao(resgateInfo.getRrn());
      //			List<LoyLogSafra> pontosAfetados =
      // loyLogSafraService.findSafrasByTransacao(resgate.getRrn());
      for (LoyLogSafra ponto : pontosAfetados) {
        if (ponto.getTipoSafra() == Constantes.TIPO_PONTO_ACUMULO.longValue())
          totalAcumulo += ponto.getPtoTransacao() * -1;
        if (ponto.getTipoSafra() == Constantes.TIPO_PONTO_BONIFICACAO.longValue())
          totalBonificado += ponto.getPtoTransacao() * -1;
      }

      Integer dvAg = null;

      if (parceiro.getCodBanco() != null
          && Constantes.COD_BANCO_DO_BRASIL.equals(parceiro.getCodBanco())
          && parceiro.getDvAgencia() == null) {
        dvAg = Util.modulo11DV(parceiro.getCodAgencia().intValue());
      } else if (parceiro.getCodBanco() != null
          && (!Constantes.COD_BANCO_DO_BRASIL.equals(parceiro.getCodBanco()))
          && parceiro.getDvAgencia() == null) {
        dvAg = null;
      } else if (parceiro.getCodBanco() != null) {
        dvAg = new Integer(parceiro.getDvAgencia());
      }

      try {
        fileWriter
            .append(resgateInfo.getRrn())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getNomeParceiro())
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(mascaraDocumento(parceiro.getCnpj()))
            .append(Constantes.DEFAULT_SEPARATOR)
            // .append(resgateInfo.getDataHoraResgate().toString()) //converter pro formato certo
            // .append(Constantes.DEFAULT_SEPARATOR)
            // .append(resgate.getIdTipoResgate().toString()) //mostrar o nome
            // .append(Constantes.DEFAULT_SEPARATOR)
            // .append(parceiro.getIdFormaLiquidacao().toString())
            // .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getCodBanco() != null ? parceiro.getCodBanco().toString() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getCodAgencia() != null ? parceiro.getCodAgencia().toString() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getDvAgencia() != null ? parceiro.getDvAgencia() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getCodConta() != null ? parceiro.getCodConta().toString() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getDvConta() != null ? parceiro.getDvConta() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(parceiro.getTipoConta() != null ? parceiro.getTipoConta().toString() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(pessoa.getNomeCompleto() != null ? pessoa.getNomeCompleto() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(pessoa.getDocumento() != null ? pessoa.getDocumento() : "")
            .append(Constantes.DEFAULT_SEPARATOR)
            .append(
                pessoa.getTipoPessoa() == null
                    ? ""
                    : (Constantes.PESSOA_JURIDICA.equals(pessoa.getTipoPessoa().getTipoPessoa())
                        ? "JURIDICA"
                        : (Constantes.PESSOA_FISICA.equals(pessoa.getTipoPessoa().getTipoPessoa())
                            ? "FISICA"
                            : "")))
            .append(Constantes.DEFAULT_SEPARATOR)
            // .append((new BigDecimal(totalBonificado).divide(new BigDecimal(100) ,
            // RoundingMode.FLOOR)).toString())
            // .append(Constantes.DEFAULT_SEPARATOR)
            // .append((new BigDecimal(totalAcumulo).divide(new BigDecimal(100) ,
            // RoundingMode.FLOOR)).toString())
            // .append(Constantes.DEFAULT_SEPARATOR)
            .append(
                new BigDecimal(resgateInfo.getValor())
                    .divide(new BigDecimal(100), RoundingMode.FLOOR)
                    .setScale(2)
                    .toString())
            .append('\n');
      } catch (IOException e) {
        e.printStackTrace();
      }
      setDataProcUsuarioProc(resgate, user.getIdUsuario());
    }
    return fileWriter;
  }

  private Boolean validaHierarquiaDaConta(
      Integer idProcessadoraConta,
      Integer idInstituicaoConta,
      Integer idProcessadoraHierarquia,
      Integer idInstituicaoHierarquia) {
    if (!idProcessadoraConta.equals(idProcessadoraHierarquia))
      throw new GenericServiceException(
          "O idProcessadora da conta não corresponde ao idProcessadora da Hierarquia");
    if (!idInstituicaoConta.equals(idInstituicaoHierarquia))
      throw new GenericServiceException(
          "O idInstituicao da conta não corresponde ao idInstituicao da Hierarquia");
    return Boolean.TRUE;
  }

  private BufferedWriter montarConteudoCsvCartoes(
      PreLancamentoLote lotePedido,
      BufferedWriter fileWriter,
      ProdutoInstituicaoConfiguracao prodConfig) {

    HierarquiaPontoDeRelacionamento emp =
        buscarEmpresaWithHierarquiaId(
            lotePedido.getIdProcessadora(),
            lotePedido.getIdInstituicao(),
            lotePedido.getIdRegional(),
            lotePedido.getIdFilial(),
            lotePedido.getIdPontoDeRelacionamento());

    String nomeFavorecido = null;
    String cnpjFavorecido = null;
    if (prodConfig.getBandeiraProduto() != null
        && Constantes.BANDEIRA_PRODUTO_VISA.equals(prodConfig.getBandeiraProduto())) {
      nomeFavorecido = Constantes.NOME_CARTAO_BRB_S_A;
      cnpjFavorecido = Constantes.CNPJ_CARTAO_BRB_S_A;
    } else if (prodConfig.getBandeiraProduto() != null
        && Constantes.BANDEIRA_PRODUTO_CABAL.equals(prodConfig.getBandeiraProduto())) {
      nomeFavorecido = Constantes.NOME_CABAL_BRASIL_LTDA;
      cnpjFavorecido = Constantes.CNPJ_CABAL_BRASIL_LTDA;
    } else if (prodConfig.getBandeiraProduto() != null
        && Constantes.BANDEIRA_PRODUTO_ELO.equals(prodConfig.getBandeiraProduto())) {
      nomeFavorecido = Constantes.NOME_ELO_SEVICOS_S_A;
      cnpjFavorecido = Constantes.CNPJ_ELO_SEVICOS_S_A;
    } else {
      throw new GenericServiceException(
          "Não foi definido a bandeira no produtoInstituicaoConfiguração para o produto: "
              + prodConfig.getIdProdInstituicao());
    }
    try {
      fileWriter
          .append(lotePedido.getIdLote().toString())
          .append(Constantes.DEFAULT_SEPARATOR)
          .append(emp.getDescricao())
          .append(Constantes.DEFAULT_SEPARATOR)
          .append(mascaraDocumento(emp.getDocumento()))
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("CARTAO")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append(nomeFavorecido)
          .append(Constantes.DEFAULT_SEPARATOR)
          .append(cnpjFavorecido)
          .append(Constantes.DEFAULT_SEPARATOR)
          .append("JURIDICA")
          .append(Constantes.DEFAULT_SEPARATOR)
          .append(lotePedido.getValorTotalLancamento().toString())
          .append('\n');
    } catch (IOException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    }

    return fileWriter;
  }

  private int modulo11DV(Integer idAgencia) {
    // String dv = null;
    int base = 9;
    int r = 0;
    int soma = 0;
    int fator = 2;

    String[] numeros, parcial;
    String ag = idAgencia.toString();
    numeros = new String[ag.length() + 1];
    parcial = new String[ag.length() + 1];
    /* Separacao dos numeros */
    for (int i = ag.length(); i > 0; i--) {
      // pega cada numero isoladamente
      numeros[i] = ag.substring(i - 1, i);
      // Efetua multiplicacao do numero pelo fator
      parcial[i] = String.valueOf(Integer.parseInt(numeros[i]) * fator);
      // Soma dos digitos
      soma += Integer.parseInt(parcial[i]);
      if (fator == base) {
        // restaura fator de multiplicacao para 2
        fator = 1;
      }
      fator++;
    }
    /* Calculo do modulo 11 */
    if (r == 0) {
      soma *= 10;
      int dv = soma % 11;
      //	        if (dv == 10) {
      //	        	dv = 0;
      //	        }
      return dv;
    } else {
      int resto = soma % 11;
      return resto;
    }
  }

  private String descTipoContaBancaria(Integer tipoContaBancaria, String documentoPessoa) {
    if (tipoContaBancaria == null) {
      throw new GenericServiceException(
          "Tipo de Conta Bancária não cadastrado para portador de documento: " + documentoPessoa);
    }
    if (tipoContaBancaria.equals(Integer.valueOf(Constantes.TIPO_CONTA_BANCARIA_CORRENTE))) {
      return "CORRENTE";
    } else if (tipoContaBancaria.equals(Integer.valueOf(Constantes.TIPO_CONTA_BANCARIA_POUPANCA))) {
      return "POUPANCA";
    } else if (tipoContaBancaria.equals(Integer.valueOf(Constantes.TIPO_CONTA_BANCARIA_SALARIO))) {
      return "SALARIO";
    } else if (tipoContaBancaria.equals(
        Integer.valueOf(Constantes.TIPO_CONTA_BANCARIA_FACIL_023))) {
      return "FACIL";
    } else {
      throw new GenericServiceException(
          "Tipo de Conta Bancaria"
              + tipoContaBancaria
              + " É inválido ou não existe 'Tipo de Conta Bancária' cadastrado para portador de documento: "
              + documentoPessoa);
    }
  }

  private String descTipoPessoa(Pessoa pessoa) {
    if (pessoa.getIdTipoPessoa() == 1) {
      return "FISICA";
    } else if (pessoa.getIdTipoPessoa() == 2) {
      return "JURIDICA";
    } else {
      throw new GenericServiceException(
          "Não existe 'Tipo Pessoa' para portador de documento: " + pessoa.getDocumento());
    }
  }

  private Pessoa buscarPessoaByConta(Long idConta) {
    Pessoa pessoa = pessoaService.findPessoaByIdConta(idConta);
    if (pessoa == null) {
      throw new GenericServiceException("Nenhum portador encontrado para a conta: " + idConta);
    }
    return pessoa;
  }

  private void verificaDadosBancarios(Pessoa pessoa) {
    if (pessoa.getContaBancariaX() == null) {
      throw new GenericServiceException(
          "Não há 'Conta Bancária' cadastrada para o portador de documento: "
              + pessoa.getDocumento());
    }
    if (pessoa.getIdBanco() == null) {
      throw new GenericServiceException(
          "Não há 'Identificador de Banco' cadastrado para o portador de documento: "
              + pessoa.getDocumento());
    }
    if (pessoa.getIdAgencia() == null) {
      throw new GenericServiceException(
          "Não há 'Agência Bancária' cadastrada para o portador de documento: "
              + pessoa.getDocumento());
    }
  }

  private String digitoVerificadorConta(String conta) {
    String dV = null;
    if (conta != null) {
      conta = conta.trim();
    }
    dV = conta.substring(conta.length() - 1);
    if (dV.equals("0")) {
      return "X";
    }
    return dV;
  }

  private String contaSemDV(String conta) {
    if (conta != null) {
      conta = conta.trim();
    }
    return conta.substring(0, conta.length() - 1);
  }

  private String mascaraDocumento(String doc) {
    if (doc.length() == 11) {
      return DocumentoUtil.imprimeCPF(doc);
    } else if (doc.length() == 14) {
      return DocumentoUtil.imprimeCNPJ(doc);
    } else {
      throw new GenericServiceException("Documento com tamanho impróprio para CPF ou CNPJ: " + doc);
    }
  }

  private ProdutoInstituicaoConfiguracao buscarConfiguracaoProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idProdutoInstituicao) {
    ProdutoInstituicaoConfiguracao prodConfig =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                idProcessadora, idProdutoInstituicao, idInstituicao);
    if (prodConfig == null) {
      throw new GenericServiceException(
          "Não foi possível localizar ProdutoInstituicaoConfiguracao para o produto: "
              + idProdutoInstituicao);
    }
    return prodConfig;
  }

  private ResgateContaBancaria buscarResgateContaBancariaComException(
      ResgateLoyaltyResponse resgate) {
    ResgateContaBancaria resgateContaBancaria =
        resgateContaBancariaService.findByRrn(resgate.getRrn());

    if (resgateContaBancaria == null) {
      throw new GenericServiceException(
          "Não foi possível localizar ResgateContaBancaria de id: " + resgate.getIdResgate());
    }
    return resgateContaBancaria;
  }

  private ResgateGenericoLoyalty buscarResgateComException(ResgateLoyaltyResponse resgate) {
    ResgateGenericoLoyalty resgateGenerico =
        resgateLoyaltyFacade.findResgateByTipoResgateAndRrn(
            resgate.getIdTipoResgate(), resgate.getRrn());

    if (resgateGenerico == null) {
      throw new GenericServiceException(
          "Não foi possível localizar resgate de id: " + resgate.getIdResgate());
    }
    return resgateGenerico;
  }

  private Boolean setDataProcUsuarioProc(ResgateLoyaltyResponse resgate, Integer idUsuario) {
    resgateLoyaltyFacade.setDataProcUsuarioProc(
        resgate.getIdTipoResgate(), resgate.getRrn(), idUsuario);
    return null;
  }

  private PessoaContaBancaria buscarPessoaContaBancariaComException(Long idContaBancaria) {

    PessoaContaBancaria contaBancaria = pessoaContaBancariaService.findById(idContaBancaria);
    if (contaBancaria == null) {
      throw new GenericServiceException(
          "Não foi possível localizar PessoaContaBancaria de id: " + idContaBancaria);
    }
    return contaBancaria;
  }

  private HierarquiaInstituicao buscarinstituicaoComException(Integer idProc, Integer idInst) {
    HierarquiaInstituicao instituicao =
        hierarquiaInstituicaoService.findByIdProcessadoraAndIdInstituicao(idProc, idInst);
    if (instituicao == null) {
      throw new GenericServiceException(
          "Não foi possível localizar a Instituição de id: " + idInst);
    }
    return instituicao;
  }

  @Transactional
  public TituloCapitalizacaoVO montarTXTParticipantesTituloCapitalizacao(
      SecurityUser user,
      List<TituloCapitalizacao> tituloCapitalizacao,
      TipoRegistroEnum acaoMensal,
      TipoRegistroEnum acaoAnual) {

    TituloCapitalizacaoVO tituloCapitalizacaoVO = new TituloCapitalizacaoVO();

    Date dataAtual = new Date();

    InterfaceInstituicao seqArquivoTituloCap =
        interfaceInstituicaoRepository.findByIdProcessadoraAndIdInstituicaoAndIdInterface(
            Constantes.ID_PROCESSADORA_ITS_PAY.longValue(),
            Constantes.PARCEIRO_BONIFICADOR_INMAIS,
            Constantes.INTERFACE_INSTITUICAO_TITULO_CAPITALIZACAO);

    String dataGeracao = dateUtil.dateFormat("yyyyMMdd", dataAtual);
    String nomeArquivo = "INGRUPO.ENTRADA" + "." + dataGeracao + ".txt";

    tituloCapitalizacaoVO.setTituloCapitalizacaoTXT(
        exportadorTituloCapitalizaoTxt.getExportadorTituloCapitalizaoTxt(
            tituloCapitalizacao,
            dataGeracao,
            nomeArquivo,
            seqArquivoTituloCap.getSequencialAtual(),
            acaoMensal,
            acaoAnual));

    if (tituloCapitalizacaoVO.getTituloCapitalizacaoTXT() != null) {
      String path = apiDirEmissores + Constantes.CAMINHO_IN_MAIS_TITULO_CAPITALIZACAO + nomeArquivo;

      //				 	String path = "/home/<USER>/Documentos/titulo/" + nomeArquivo;

      String md5 = hashCodeArquivo(path);

      tituloCapitalizacaoVO.setIdDownload(
          registrarAqruivosTXTInMaisConfiguracaoDownload(user, dataAtual, nomeArquivo, md5));
      tituloCapitalizacaoVO.setNomeArquivo(nomeArquivo);
    } else {
      throw new GenericServiceException("Erro ao gerar arquivo txt posicional. " + nomeArquivo);
    }

    seqArquivoTituloCap.setSequencialAnterior(seqArquivoTituloCap.getSequencialAtual());
    seqArquivoTituloCap.setSequencialAtual(seqArquivoTituloCap.getSequencialAtual() + 1);

    interfaceInstituicaoRepository.save(seqArquivoTituloCap);

    return tituloCapitalizacaoVO;
  }

  private Integer registrarAqruivosTXTInMaisConfiguracaoDownload(
      SecurityUser user, Date dataAtual, String nomeArquivo, String md5) {

    CadastrarArquivoDownloadConfiguracao modelArqConfig =
        new CadastrarArquivoDownloadConfiguracao();
    CadastrarArquivoDownload modelArq = new CadastrarArquivoDownload();

    ArquivoDownloadConfiguracao arquivoDownloadConfiguracao = new ArquivoDownloadConfiguracao();
    ArquivoDownload arquivoDownload = new ArquivoDownload();

    modelArqConfig.setIdProcessadora(Constantes.ID_PROCESSADORA_ITS_PAY);
    modelArqConfig.setIdInstituicao(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL);
    modelArqConfig.setIdRegional(Constantes.ID_PRODUCAO_REGIONAL_IN_MAIS);
    modelArqConfig.setIdFilial(Constantes.ID_PRODUCAO_FILIAL_IN_MAIS);
    modelArqConfig.setIdPontoDeRelacionamento(Constantes.ID_PRODUCAO_PONTO_RELACIONAMENTO_IN_MAIS);
    modelArqConfig.setPrefixo("TIT_CAP");
    modelArqConfig.setDescricao("DADOS DOS PARTICIPANTES DO TITULO DE CAPITALIZAÇÃO");
    modelArqConfig.setExtensao("txt");
    modelArqConfig.setNomeArquivo(nomeArquivo);

    arquivoDownloadConfiguracao =
        arquivoDownloadConfiguracaoService.prepareArquivoDownloadConfiguracao(
            modelArqConfig, arquivoDownloadConfiguracao, user);
    if (arquivoDownloadConfiguracao == null) {
      throw new GenericServiceException("Erro ao salvar Configuração de Arquivo de download.");
    }

    modelArq.setIdDownloadConf(arquivoDownloadConfiguracao.getIdDownloadConf());
    modelArq.setDescricao(arquivoDownloadConfiguracao.getDescricao());
    modelArq.setDiretorio(apiDirEmissores + Constantes.CAMINHO_IN_MAIS_TITULO_CAPITALIZACAO);
    modelArq.setDtDisponibilizacao(DateUtil.dateToLocalDateTime(dataAtual));
    modelArq.setHash(md5.toString());
    modelArq.setNomeArquivo(arquivoDownloadConfiguracao.getNomeArquivo());

    arquivoDownload =
        arquivoDownloadService.prepareArquivoDownload(
            modelArq, arquivoDownload, arquivoDownloadConfiguracao, user);
    if (arquivoDownload == null) {
      throw new GenericServiceException("Erro ao salvar Arquivo de download.");
    }

    return arquivoDownload.getId();
  }

  @Transactional
  public TituloCapitalizacaoVO montarPDFDadosPagamento(
      List<TituloCapitalizacaoDadosPagamentoVO> tituloCapitalizacaoDadosPagamento,
      SecurityUser user) {

    TituloCapitalizacaoVO tit = new TituloCapitalizacaoVO();

    Date dataAtual = new Date();

    InterfaceInstituicao seqArquivoTituloCap =
        interfaceInstituicaoRepository.findByIdProcessadoraAndIdInstituicaoAndIdInterface(
            Constantes.ID_PROCESSADORA_ITS_PAY.longValue(),
            Constantes.PARCEIRO_BONIFICADOR_INMAIS,
            Constantes.INTERFACE_INSTITUICAO_TITULO_CAPITALIZACAO_DADOS_PAGAMENTO);

    String dataGeracao = dateUtil.dateFormat("yyyyMMdd", dataAtual);
    String nomeArquivo =
        "DADOS_PAGAMENTO_" + seqArquivoTituloCap.getSequencialAtual() + "_" + dataGeracao + ".pdf";

    try {
      exportadorTituloCapitalizaoGanhadoresPdf.getPDFTituloCapitalizacao(
          tituloCapitalizacaoDadosPagamento,
          dataGeracao,
          nomeArquivo,
          seqArquivoTituloCap.getSequencialAtual());

      String path = apiDirEmissores + Constantes.CAMINHO_IN_MAIS_TITULO_CAPITALIZACAO + nomeArquivo;

      //		 	String path = "/home/<USER>/Documentos/titulos/" + nomeArquivo;

      String md5 = hashCodeArquivo(path);

      tit.setIdDownload(
          registrarAqruivosPDFInMaisConfiguracaoDownload(user, dataAtual, nomeArquivo, md5));
      tit.setNomeArquivo(nomeArquivo);

      seqArquivoTituloCap.setSequencialAnterior(seqArquivoTituloCap.getSequencialAtual());
      seqArquivoTituloCap.setSequencialAtual(seqArquivoTituloCap.getSequencialAtual() + 1);

      interfaceInstituicaoRepository.save(seqArquivoTituloCap);

    } catch (IOException | DocumentException e) {
      throw new GenericServiceException("Erro ao criar PDF.");
    }

    return tit;
  }

  private Integer registrarAqruivosPDFInMaisConfiguracaoDownload(
      SecurityUser user, Date dataAtual, String nomeArquivo, String md5) {

    CadastrarArquivoDownloadConfiguracao modelArqConfig =
        new CadastrarArquivoDownloadConfiguracao();
    CadastrarArquivoDownload modelArq = new CadastrarArquivoDownload();

    ArquivoDownloadConfiguracao arquivoDownloadConfiguracao = new ArquivoDownloadConfiguracao();
    ArquivoDownload arquivoDownload = new ArquivoDownload();

    modelArqConfig.setIdProcessadora(Constantes.ID_PROCESSADORA_ITS_PAY);
    modelArqConfig.setIdInstituicao(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL);
    modelArqConfig.setIdRegional(Constantes.ID_PRODUCAO_REGIONAL_IN_MAIS);
    modelArqConfig.setIdFilial(Constantes.ID_PRODUCAO_FILIAL_IN_MAIS);
    modelArqConfig.setIdPontoDeRelacionamento(Constantes.ID_PRODUCAO_PONTO_RELACIONAMENTO_IN_MAIS);
    modelArqConfig.setPrefixo("TIT_CAP");
    modelArqConfig.setDescricao("DADOS DE PAGAMENTO DOS GANHADORES");
    modelArqConfig.setExtensao("pdf");
    modelArqConfig.setNomeArquivo(nomeArquivo);

    arquivoDownloadConfiguracao =
        arquivoDownloadConfiguracaoService.prepareArquivoDownloadConfiguracao(
            modelArqConfig, arquivoDownloadConfiguracao, user);
    if (arquivoDownloadConfiguracao == null) {
      throw new GenericServiceException("Erro ao salvar Configuração de Arquivo de download.");
    }

    modelArq.setIdDownloadConf(arquivoDownloadConfiguracao.getIdDownloadConf());
    modelArq.setDescricao(arquivoDownloadConfiguracao.getDescricao());
    modelArq.setDiretorio(apiDirEmissores + Constantes.CAMINHO_IN_MAIS_TITULO_CAPITALIZACAO);
    modelArq.setDtDisponibilizacao(DateUtil.dateToLocalDateTime(dataAtual));
    modelArq.setHash(md5.toString());
    modelArq.setNomeArquivo(arquivoDownloadConfiguracao.getNomeArquivo());

    arquivoDownload =
        arquivoDownloadService.prepareArquivoDownload(
            modelArq, arquivoDownload, arquivoDownloadConfiguracao, user);
    if (arquivoDownload == null) {
      throw new GenericServiceException("Erro ao salvar Arquivo de download.");
    }

    return arquivoDownload.getId();
  }

  public InputStream criaArquivoXlsx(List<DetalhePreLancamentoLote> detalhes) throws IOException {

    String nomeModelo = "carga.xlsx";
    String caminhoModelo = apiDirEmissores + Constantes.CAMINHO_INFINANCAS_INTEGRACAO;

    FileInputStream input_document = new FileInputStream(new File(caminhoModelo + nomeModelo));

    XSSFWorkbook novoBook = new XSSFWorkbook(input_document);

    input_document.close();

    XSSFSheet sheet = novoBook.getSheetAt(0);

    for (int i = 0; i < detalhes.size(); i++) {
      XSSFRow r = sheet.createRow(i);
      r.createCell(0).setCellValue(detalhes.get(i).getDocumento());
      // tranforma reais em pontos
      CotacaoPontos cotacaoPontos =
          cotacaoPontosService.findByIdIntituicao(detalhes.get(i).getIdInstituicao().intValue());
      if (cotacaoPontos != null && cotacaoPontos.getIdCotacaoPontos() > 0) {
        BigDecimal valorConvertido =
            detalhes.get(i).getValorLancamento().multiply(cotacaoPontos.getValorConversao());
        r.createCell(1).setCellValue(valorConvertido.setScale(0, BigDecimal.ROUND_UP).toString());
      } else {
        r.createCell(1).setCellValue(detalhes.get(i).getValorLancamento().toString());
      }
      r.createCell(2).setCellValue(detalhes.get(i).getIdLote().toString());
    }

    ByteArrayOutputStream outStream = new ByteArrayOutputStream();
    novoBook.write(outStream);
    try {
      outStream.close();
    } catch (IOException e) {
      e.printStackTrace();
    }
    ByteArrayInputStream inStream = new ByteArrayInputStream(outStream.toByteArray());
    return inStream;
  }
}
