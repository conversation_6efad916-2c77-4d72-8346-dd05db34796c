package br.com.sinergico.service;

import br.com.entity.cadastral.CartaoQrCode;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.BinaryBitmap;
import com.google.zxing.LuminanceSource;
import com.google.zxing.Result;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.BufferedImageLuminanceSource;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.common.HybridBinarizer;
import com.google.zxing.qrcode.QRCodeReader;
import com.google.zxing.qrcode.QRCodeWriter;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.UUID;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.crypto.Mac;
import org.bouncycastle.crypto.digests.SHA256Digest;
import org.bouncycastle.crypto.macs.HMac;
import org.bouncycastle.crypto.params.KeyParameter;
import org.bouncycastle.util.encoders.Hex;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GeradorQrCodeService {

  private static final String CHAVE_SECRETA_QR_CODE = "QrCodeValloo";
  private static final Integer TAMANHO_PADRAO_PEQUENO = 200;
  private static final Integer TAMANHO_PADRAO_MEDIO = 300;
  private static final Integer TAMANHO_PADRAO_GRANDE = 400;
  private static final Integer TAMANHO_PADRAO_GIGANTE = 500;

  /**
   * Gera a Imagem em Base64 para um cartaoQrCode solicitado.
   *
   * @param cartaoQrCode -> Cartão QR Code para gerar a imagem
   * @return -> String da imagem em Base64
   * @throws Exception
   */
  public String geraQrCodeBase64(CartaoQrCode cartaoQrCode, Boolean gravarEmDisco)
      throws Exception {
    BitMatrix bitMatrix = preparaQrCodeMatrix(cartaoQrCode.getUuid());
    if (gravarEmDisco) {
      gravarImagemQrCode(cartaoQrCode);
    }

    try (ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream()) {
      MatrixToImageWriter.writeToStream(bitMatrix, "PNG", pngOutputStream);
      byte[] pngData = pngOutputStream.toByteArray();
      return Base64.getEncoder().encodeToString(pngData);
    }
  }

  /**
   * Verifica um uuid assinado proveniente de um escaneamento de QR Code feito via aplicativo
   *
   * @param uuidAssinado -> Leitura do QR Code como recebida pelo aplicativo.
   * @return -> Booleano indicando o sucesso (True significa que o QR Code lido é, de fato, um
   *     Cartao QR Code Valloo)
   */
  public Boolean verificaAssinaturaQrCode(String uuidAssinado) {
    String[] segmentos = uuidAssinado.split(":");
    if (segmentos.length != 2) return false;

    String uuid = segmentos[0];
    String assinatura = segmentos[1];

    byte[] resultado = preparaAssinatura(uuid);

    String assinaturaCalculada = Hex.toHexString(resultado);
    return assinatura.equals(assinaturaCalculada);
  }

  /**
   * Resgata o UUID contido em um Cartão QR Code lido pelo aplicativo. Idealmente utilizado após uma
   * verificaAssinaturaQrCode que retornou sucesso
   *
   * @param uuidAssinado -> Leitura do QR Code como recebida pelo aplicativo.
   * @return -> UUID separado da assinatura.
   */
  public String resgataUuidDoToken(String uuidAssinado) {
    String[] segmentos = uuidAssinado.split(":");
    return segmentos[0];
  }

  /**
   * Prepara a matriz para geração de uma imagem QR Code, seja para salvar em disco ou envio na
   * forma de Base64.
   *
   * @param uuid -> Utilizado como conteúdo do Cartão QR Code
   * @return -> Matriz preparada para gerar a imagem QR Code
   * @throws Exception
   */
  protected BitMatrix preparaQrCodeMatrix(UUID uuid) throws Exception {
    String texto = criarStringQrCodeAssinada(uuid.toString());
    Integer tamanho = decideTamanhoDoQrCode(texto);
    return gerarQrCode(texto, tamanho);
  }

  /**
   * Decide o tamanho do QR Code final de acordo com a quantidade de caracteres do conteúdo.
   *
   * @param dados -> Conteúdo para decidir o tamanho.
   * @return
   */
  private Integer decideTamanhoDoQrCode(String dados) {
    int quantidadeDeCaracteres = dados.length();
    if (quantidadeDeCaracteres < 50) return TAMANHO_PADRAO_PEQUENO;
    if (quantidadeDeCaracteres < 200) return TAMANHO_PADRAO_MEDIO;
    if (quantidadeDeCaracteres < 500) return TAMANHO_PADRAO_GRANDE;
    return TAMANHO_PADRAO_GIGANTE;
  }

  /**
   * Cria a assinatura de um conteúdo (UUID) para gerar o QR Code assinado.
   *
   * @param uuid -> Conteúdo (UUID) a ser assinado.
   * @return -> Conteúdo assinado.
   */
  private String criarStringQrCodeAssinada(String uuid) {

    byte[] resultado = preparaAssinatura(uuid);

    String assinatura = Hex.toHexString(resultado);
    return uuid + ":" + assinatura;
  }

  /**
   * Prepara a assinatura de um conteúdo (UUID).
   *
   * @param uuid -> Conteúdo a ser assinado.
   * @return -> Bytes da assinatura referente ao conteúdo inserido.
   */
  private byte[] preparaAssinatura(String uuid) {
    byte[] chaveEmBytes = CHAVE_SECRETA_QR_CODE.getBytes();
    Mac mac = new HMac(new SHA256Digest());
    mac.init(new KeyParameter(chaveEmBytes));
    mac.update(uuid.getBytes(), 0, uuid.getBytes().length);
    byte[] resultado = new byte[mac.getMacSize()];
    mac.doFinal(resultado, 0);
    return resultado;
  }

  /**
   * Gera o QR Code com o conteúdo e tamanho específicados.
   *
   * @param conteudo -> Conteúdo do QR Code.
   * @param tamanho -> Tamanho da imagem QR Code.
   * @return -> Matriz do QR Code gerado.
   * @throws WriterException
   */
  private BitMatrix gerarQrCode(String conteudo, int tamanho) throws WriterException {
    QRCodeWriter qrCodeWriter = new QRCodeWriter();
    return qrCodeWriter.encode(conteudo, BarcodeFormat.QR_CODE, tamanho, tamanho);
  }

  /**
   * A partir de uma Imagem Base64, simula um leitor de QR Code de celular e retorna o conteúdo
   * lido.
   *
   * @param dadosBase64 -> Imagem Base64 para ler.
   * @return -> Conteúdo lido.
   * @throws Exception
   */
  public String extraiDadosDeQrCodeBase64(String dadosBase64) throws Exception {
    byte[] dadosImagem = Base64.getDecoder().decode(dadosBase64);
    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(dadosImagem);
    BufferedImage imagem = ImageIO.read(byteArrayInputStream);
    LuminanceSource fonteLuminancia = new BufferedImageLuminanceSource(imagem);
    BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(fonteLuminancia));
    QRCodeReader leitorQr = new QRCodeReader();
    Result resultado = leitorQr.decode(bitmap);
    return resultado.getText();
  }

  /**
   * Armazena em disco a iamgem de um Cartão QR Code (Utilizado para testes, especialmente)
   *
   * @param cartaoQrCode -> Cartão QR Code a ser salvo.
   * @throws Exception
   */
  public void gravarImagemQrCode(CartaoQrCode cartaoQrCode) throws Exception {
    BitMatrix bitMatrix = preparaQrCodeMatrix(cartaoQrCode.getUuid());

    LocalDateTime agora = LocalDateTime.now();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    String dataFormatada = agora.format(formatter);

    String caminhoPastaEntrada = "/plataforma/backend/qrcode";
    Path path =
        Paths.get(
            caminhoPastaEntrada,
            cartaoQrCode.getApelido() != null
                ? "CartaoQRCode_"
                    + cartaoQrCode.getId()
                    + "_"
                    + cartaoQrCode.getApelido()
                    + "_"
                    + dataFormatada
                : "CartaoQRCode_" + cartaoQrCode.getId() + "_" + dataFormatada);
    MatrixToImageWriter.writeToPath(bitMatrix, "PNG", path);
  }

  /* Para testes abaixo */
  //
  //	public static void main(String[] args) {
  //		try {
  //			CartaoQrCode cartaoQrCode = new CartaoQrCode();
  //			cartaoQrCode.setUuid(null);
  //			String qrCodeTeste = geraQrCodeBase64(cartaoQrCode);
  //			System.out.println(qrCodeTeste);
  //			String tokenAssinado = extraiDadosDeQrCodeBase64(qrCodeTeste);
  //			System.out.println(tokenAssinado);
  //			Boolean teste = verificaAssinaturaQrCode(tokenAssinado);
  //			System.out.println(teste);
  //			String uuid = resgataUuidDoToken(tokenAssinado);
  //			System.out.println(uuid);
  //			cartaoQrCode.setUuid(UUID.fromString(uuid));
  //			gravarImagemQrCode(cartaoQrCode);
  //		} catch (Exception e) {
  //			throw new RuntimeException(e);
  //		}
  //		System.out.println("fim");
  //	}

}
