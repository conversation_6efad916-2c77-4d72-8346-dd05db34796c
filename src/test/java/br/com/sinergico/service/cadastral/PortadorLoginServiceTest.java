package br.com.sinergico.service.cadastral;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

import br.com.exceptions.GenericServiceException;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.PasswordValidatorService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.TipoStatusService;
import br.com.sinergico.util.Constantes;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PortadorLoginServiceTest {

  @Mock private PortadorLoginContaService portadorLoginContaService;
  @Mock private CredencialService credencialService;
  @Mock private ContaPagamentoService contaPagamentoService;
  @Mock private PessoaService pessoaService;
  @Mock private PasswordValidatorService passwordValidatorService;
  @Mock private UtilController utilController;
  @Mock private HierarquiaInstituicaoService hierarquiaInstituicaoService;
  @Mock private TipoStatusService tipoStatusService;
  @Mock private PortadorLoginService spyPortadorLoginService;
  @InjectMocks private PortadorLoginService portadorLoginService;

  @Test
  void deveValidarInstituicaoPermitidaNoPortadorLoginMultibeneficios()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

    Integer instituicaoNaoPermitida = 9999;
    String mensagemErro = "Erro ao validar instituição";

    Method metodoPrivado =
        PortadorLoginService.class.getDeclaredMethod(
            "validarInstituicaoPermitidaPortadorLoginMultibeneficios", Integer.class, String.class);
    metodoPrivado.setAccessible(true);

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              try {
                metodoPrivado.invoke(null, instituicaoNaoPermitida, mensagemErro);
              } catch (InvocationTargetException e) {
                throw (GenericServiceException) e.getTargetException();
              }
            });

    HashMap<String, Object> expectedMap = new HashMap<>();
    expectedMap.put("MSG", mensagemErro);
    expectedMap.put("DETALHE", "Instituição não permite uso de campo grupoAcesso.");
    expectedMap.put("CREATED", false);
    assertEquals(expectedMap.get("MSG"), exception.getErros().get("msg"));
    assertEquals(expectedMap.get("DETALHE"), exception.getDetalhes());
    assertEquals(expectedMap.get("CREATED"), exception.getErros().get("created"));
  }

  @Test
  void naoDeveLancarExcecaoQuandoInstituicaoForPermitidaNoPortadorLoginMultibeneficios()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

    String mensagemErro = "Erro ao validar instituição";
    Integer[] instituicoesPermitidas = {
      Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD,
      Constantes.ID_PRODUCAO_INSTITUICAO_MULVI,
      Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK,
      Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS,
      Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO,
      Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY,
      Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII,
      Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS,
      Constantes.ID_PRODUCAO_INSTITUICAO_WIZ,
      Constantes.ID_PRODUCAO_QISTA,
      Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU
    };

    Method metodoPrivado =
        PortadorLoginService.class.getDeclaredMethod(
            "validarInstituicaoPermitidaPortadorLoginMultibeneficios", Integer.class, String.class);
    metodoPrivado.setAccessible(true);

    for (Integer instituicao : instituicoesPermitidas) {
      assertDoesNotThrow(() -> metodoPrivado.invoke(null, instituicao, mensagemErro));
    }
  }

  @Test
  void deveValidarInstituicaoPermitidaNoPortadorLoginDocumentoAcesso()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    Integer instituicaoNaoPermitida = 9999;
    String mensagemErro = "Erro ao validar instituição";

    Method metodoPrivado =
        PortadorLoginService.class.getDeclaredMethod(
            "validarInstituicaoPermitidaPortadorLoginDocumentoAcesso", Integer.class, String.class);
    metodoPrivado.setAccessible(true);

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              try {
                metodoPrivado.invoke(null, instituicaoNaoPermitida, mensagemErro);
              } catch (InvocationTargetException e) {
                throw (GenericServiceException) e.getTargetException();
              }
            });

    HashMap<String, Object> expectedMap = new HashMap<>();
    expectedMap.put("MSG", mensagemErro);
    expectedMap.put("DETALHE", "Instituição não permite uso de documentoAcesso.");
    expectedMap.put("CREATED", false);
    assertEquals(expectedMap.get("MSG"), exception.getErros().get("msg"));
    assertEquals(expectedMap.get("DETALHE"), exception.getDetalhes());
    assertEquals(expectedMap.get("CREATED"), exception.getErros().get("created"));
  }

  @Test
  void naoDeveLancarExcecaoQuandoInstituicaoForPermitidaNoPortadorLoginDocumentoAcesso()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
    String mensagemErro = "Erro ao validar instituição";

    Integer[] instituicoesPermitidas = {
      Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY,
      Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT,
      Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI,
      Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA,
      Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD,
      Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK,
      Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH,
      Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS,
      Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK,
      Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO,
      Constantes.ID_PRODUCAO_INSTITUICAO_MULVI,
      Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY,
      Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII,
      Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS,
      Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS,
      Constantes.ID_PRODUCAO_INSTITUICAO_WIZ,
      Constantes.ID_PRODUCAO_QISTA,
      Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU
    };

    Method metodoPrivado =
        PortadorLoginService.class.getDeclaredMethod(
            "validarInstituicaoPermitidaPortadorLoginDocumentoAcesso", Integer.class, String.class);
    metodoPrivado.setAccessible(true);

    for (Integer instituicao : instituicoesPermitidas) {
      assertDoesNotThrow(() -> metodoPrivado.invoke(null, instituicao, mensagemErro));
    }
  }
}
