package br.com.sinergico.enums;

import br.com.exceptions.GenericServiceException;

/**
 * Tipos de produto. Inicialmente, possui a intenção de regular o comportamento de transferência
 * entre contas de mesmo CPF (transferência entre mesmo bolso)
 */
public enum TipoProdutoEnum {
  NAO_CLASSIFICADO("Não classificado", 99),
  CONTA_LIVRE("Conta Livre", 1),
  ALIMENTACAO("Alimentação", 2),
  REFEICAO("Refeição", 3),
  MOBILIDADE("Mobilidade", 4),
  MOEDEIRO("Moedeiro", 5),
  LOJISTA("Lojista", 6),
  ALIMENTACAO_REFEICAO("Alimentação e Refeição", 7),
  COMBUSTIVEL("Combustível", 8),
  MOBILIDADE_COMBUSTIVEL("Mobilidade e Combustível", 9),
  SAUDE("Saúde", 10),
  EDUCACAO("Educação", 11),
  CULTURA("Cultura", 12),
  HOME_OFFICE("Home Office", 13),
  PREMIACAO("Premiação", 14),
  PAT_ALIMENTACAO("PAT Alimentação", 15),
  PAT_REFEICAO("PAT Refeição", 16),
  PAT_ALIMENTACAO_REFEICAO("PAT Alimentação e Refeição", 17),
  UTILIDADE("Utilidade", 18),
  COMPRA("Pagamento Compra", 19),
  SALDO_BLOQUEADO("Saldo Bloqueado", 20);

  private final String nomeTela;
  private final int prioridade;

  TipoProdutoEnum(String nomeTela, int prioridade) {
    this.nomeTela = nomeTela;
    this.prioridade = prioridade;
  }

  public String getNomeTela() {
    return nomeTela;
  }

  public int getPrioridade() {
    return prioridade;
  }

  public static int ordenarPorPrioridade(TipoProdutoEnum tipoProduto) {
    return tipoProduto.prioridade;
  }

  public static TipoProdutoEnum findTipoProdutoEnumByIdTipoProduto(Integer idTipoProduto) {
    if (idTipoProduto == null) {
      throw new GenericServiceException("idTipoProduto não pode ser nulo.");
    }
    for (TipoProdutoEnum tipoProdutoEnum : TipoProdutoEnum.values()) {
      if (tipoProdutoEnum.ordinal() == idTipoProduto) {
        return tipoProdutoEnum;
      }
    }
    throw new GenericServiceException(
        String.format("Tipo de produto com idTipoProduto %d não existe.", idTipoProduto));
  }
}
