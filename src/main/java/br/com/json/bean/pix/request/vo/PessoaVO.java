package br.com.json.bean.pix.request.vo;

import br.com.sinergico.util.Abreviador;
import br.com.sinergico.util.Constantes;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class PessoaVO {
  private String inscricaoNacional;
  private String nome;
  private String nomeFantasia;
  private String tipoId; // 1 - Fisica 2 - Juridica

  public void setNome(String nome) {
    this.nome =
        nome != null ? Abreviador.nomeNormalizadoSemAcentos(nome, Constantes.PESSOA_FISICA) : null;
  }

  public void setNomeFantasia(String nomeFantasia) {
    this.nomeFantasia =
        nomeFantasia != null
            ? Abreviador.nomeNormalizadoSemAcentos(nomeFantasia, Constantes.PESSOA_JURIDICA)
            : null;
  }
}
