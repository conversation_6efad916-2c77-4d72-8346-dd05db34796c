package br.com.sinergico.controller;

import br.com.client.rest.jcard.json.bean.CheckCVVRequest;
import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.client.rest.jcard.json.bean.JcardRequestEmbossing;
import br.com.client.rest.jcard.json.bean.JcardResponseEmbossing;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.DesbloqueioCartaoRequest;
import br.com.json.bean.DesbloqueioCartaoRequestCVV;
import br.com.json.bean.DesbloqueioCartaoResponse;
import br.com.json.bean.brb.DesbloqueioCartaoResponseBRB;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.security.CriptoUtil;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.jcard.JcardService;
import br.com.sinergico.util.Constantes;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/portador/desbloqueio")
public class BahamasCardController extends UtilController {

  @Value("${jcard.url}")
  private String jcardUrl;

  private static final Integer STATUS_CARTAO_ATIVO = 1;
  private static final String URL = "/jcard/api/cards/getdataforprinting";
  private static final Integer AUTOR_PORTADOR = 2;

  @Autowired @Lazy private CredencialFacade credencialFacade;

  @Autowired private PessoaService pessoaService;

  @Autowired private CardService cardService;

  @Autowired CredencialRepository credencialRepository;

  @Autowired ContaPagamentoService contaPagamentoService;

  @Autowired ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private HttpServletRequest request;

  @Autowired private JcardService jcardService;

  private static final Logger log = LoggerFactory.getLogger(BahamasCardController.class);

  @ApiOperation(value = "Solicitar desbloqueio de cartão.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/solicitar-desbloqueio-cartao",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESBLOQUEIO_CREDENCIAL_PORTADOR"})
  public ResponseEntity<DesbloqueioCartaoResponse> solicitarDesbloqueioCartao(
      @RequestBody DesbloqueioCartaoRequest dadosCartao) {

    DesbloqueioCartaoResponse retorno = new DesbloqueioCartaoResponse();

    try {
      System.out.println("Desbloqueio do cartão do portador: " + dadosCartao.getIdCredencial());

      // Pegar o token na credencial
      Credencial credencial =
          credencialRepository.findOneByIdCredencial(dadosCartao.getIdCredencial());

      // chamada servico JCARD
      JcardRequestEmbossing jcardRequest = new JcardRequestEmbossing();
      jcardRequest.setToken(credencial.getTokenInterno());
      jcardRequest.setIdconsumer("001");

      JcardResponseEmbossing responseJcard = new JcardResponseEmbossing();

      responseJcard = jcardService.doPost(jcardRequest, responseJcard, jcardUrl + URL);

      Date out = Date.from(credencial.getDataValidade().atZone(ZoneId.systemDefault()).toInstant());

      if (!responseJcard.getSuccess()) {
        log.info("Dados inválidos, Credencial não encontrada");
        retorno.setStatusRetorno(false);
        retorno.setMsg("Dados inválidos, tente novamente.");
        return new ResponseEntity<>(retorno, HttpStatus.UNPROCESSABLE_ENTITY);
      }
      //		    System.out.println("cvv: " +
      // CriptoUtil.descriptografarDados(responseJcard.getCard().getCvv2cripto()).substring(0, 3));
      //		    System.out.println("Numero cartao: " +
      // CriptoUtil.descriptografarDados(responseJcard.getCard().getPancripto()).replace("F", ""));

      String numerosMeioExtraidos =
          CriptoUtil.descriptografarDados(responseJcard.getCard().getPancripto())
              .replace("F", "")
              .substring(4, 12);
      if (numerosMeioExtraidos == null) {
        numerosMeioExtraidos = "";
      }

      //		    System.out.println("teste extracao meio:" + numerosMeioExtraidos);
      //		    System.out.println("Digitos do meio: " +
      // numerosMeioExtraidos.equals(dadosCartao.getNumeroMeioCredencial()));

      Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());

      if (!CriptoUtil.descriptografarDados(responseJcard.getCard().getCvv2cripto())
          .substring(0, 3)
          .equals(dadosCartao.getCodigoCvv())) {
        //					System.out.println("CVV INVALIDO: " +
        // CriptoUtil.descriptografarDados(responseJcard.getCard().getCvv2cripto()).substring(0,
        // 3));
        if (!Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(pessoa.getIdInstituicao())
            || dadosCartao.getForceValidateCvv()) {
          log.info("Dados inválidos, CVV incorreto");
          retorno.setStatusRetorno(false);
          retorno.setMsg("Dados inválidos, tente novamente.");
          return new ResponseEntity<>(retorno, HttpStatus.UNPROCESSABLE_ENTITY);
        }
      } else if (!getDataCredencial(out)
          .equals(dadosCartao.getMes() + "/" + dadosCartao.getAno())) {
        //		    		System.out.println("DATA INVALIDA: " + getDataCredencial(out) + " = " + out + "-->
        // ENVIADA: " + dadosCartao.getMes()+"/"+dadosCartao.getAno());
        log.info("Dados inválidos, Data de validade incorreta");
        retorno.setStatusRetorno(false);
        retorno.setMsg("Dados inválidos, tente novamente.");
        return new ResponseEntity<>(retorno, HttpStatus.UNPROCESSABLE_ENTITY);
      } else if (!numerosMeioExtraidos.equals(dadosCartao.getNumeroMeioCredencial())) {
        //					System.out.println("NUMERO MEIO CREDENCIAL ERRADA");
        log.info("Dados inválidos, Dígitos intermediários incorretos");
        retorno.setStatusRetorno(false);
        retorno.setMsg("Dados inválidos, tente novamente.");
        return new ResponseEntity<>(retorno, HttpStatus.UNPROCESSABLE_ENTITY);
      }

      ContaPagamento contaPagamento =
          contaPagamentoService.buscarPorContaId(credencial.getIdConta());

      Long idValidacao = null;
      if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(contaPagamento)) {
        idValidacao = credencialFacade.checaValidacaoFacialCaf(credencial);
      }

      credencialFacade.alterarStatusCredencial(
          dadosCartao.getIdCredencial(), STATUS_CARTAO_ATIVO, dadosCartao.getIdUsuario(), true);

      if (Objects.equals(pessoa.getIdInstituicao(), Constantes.ID_PRODUCAO_INSTITUICAO_FINANCIAL)) {
        contaPagamentoService.desbloquearConta(
            credencial.getIdConta(), AUTOR_PORTADOR, dadosCartao.getIdUsuario());
      }

      if (idValidacao != null) {
        credencialFacade.efetivaValidacaoFacialCaf(idValidacao);
      }

      retorno.setStatusRetorno(true);
      retorno.setMsg("Cartão desbloqueado");
      return new ResponseEntity<>(retorno, HttpStatus.OK);

    } catch (GenericServiceException e) {
      e.printStackTrace();
      if (HttpStatus.FORBIDDEN.value() == e.getHttpStatus().value()) {
        retorno.setStatusRetorno(false);
        retorno.setMsg(e.getMessage());
        return new ResponseEntity<>(retorno, HttpStatus.FORBIDDEN);
      }
      retorno.setStatusRetorno(false);
      retorno.setMsg("Dados inválidos, tente novamente.");
      return new ResponseEntity<>(retorno, HttpStatus.INTERNAL_SERVER_ERROR);
    } catch (Exception e) {
      e.printStackTrace();
      retorno.setStatusRetorno(false);
      retorno.setMsg("Dados inválidos, tente novamente.");
      return new ResponseEntity<>(retorno, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @ApiOperation(value = "Solicitar desbloqueio de cartão.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/v2/solicitar-desbloqueio-cartao",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESBLOQUEIO_CREDENCIAL_PORTADOR"})
  public ResponseEntity<HashMap<String, DesbloqueioCartaoResponseBRB>> solicitarDesbloqueioCartaoV2(
      @RequestBody DesbloqueioCartaoRequest dadosCartao) {

    DesbloqueioCartaoResponseBRB retorno = new DesbloqueioCartaoResponseBRB();
    HashMap<String, DesbloqueioCartaoResponseBRB> map = new HashMap<>();

    try {
      System.out.println("Desbloqueio do cartão do portador: " + dadosCartao.getIdCredencial());

      // Pegar o token na credencial
      Credencial credencial =
          credencialRepository.findOneByIdCredencial(dadosCartao.getIdCredencial());

      // chamada servico JCARD
      JcardRequestEmbossing jcardRequest = new JcardRequestEmbossing();
      jcardRequest.setToken(credencial.getTokenInterno());
      jcardRequest.setIdconsumer("001");

      JcardResponseEmbossing responseJcard = new JcardResponseEmbossing();

      responseJcard = jcardService.doPost(jcardRequest, responseJcard, jcardUrl + URL);

      Date out = Date.from(credencial.getDataValidade().atZone(ZoneId.systemDefault()).toInstant());

      if (!responseJcard.getSuccess()) {
        log.info("Dados inválidos, Credencial não encontrada");
        //				retorno.setStatusRetorno(false);
        retorno.setMsg("Dados inválidos, tente novamente.");
        map.put("dados", retorno);

        return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
      }

      String numerosMeioExtraidos =
          CriptoUtil.descriptografarDados(responseJcard.getCard().getPancripto())
              .replace("F", "")
              .substring(4, 12);
      if (numerosMeioExtraidos == null) {
        numerosMeioExtraidos = "";
      }

      Pessoa pessoa = pessoaService.findById(credencial.getIdPessoa());

      if (!CriptoUtil.descriptografarDados(responseJcard.getCard().getCvv2cripto())
          .substring(0, 3)
          .equals(dadosCartao.getCodigoCvv())) {
        if (!Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(pessoa.getIdInstituicao())
            || dadosCartao.getForceValidateCvv()) {
          log.info("Dados inválidos, CVV incorreto");

          retorno.setMsg("Dados inválidos, tente novamente.");

          map.put("dados", retorno);
          return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
        }
      } else if (!getDataCredencial(out)
          .equals(dadosCartao.getMes() + "/" + dadosCartao.getAno())) {
        log.info("Dados inválidos, Data de validade incorreta");

        retorno.setMsg("Dados inválidos, tente novamente.");

        map.put("dados", retorno);
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      } else if (!numerosMeioExtraidos.equals(dadosCartao.getNumeroMeioCredencial())) {
        log.info("Dados inválidos, Dígitos intermediários incorretos");
        retorno.setMsg("Dados inválidos, tente novamente.");

        map.put("dados", retorno);
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      } else if (!credencial.getStatus().equals(0)) {
        log.info("Não foi possivel desbloquear cartão. O cartão deve estar com BLOQUEIO DE ORIGEM");
        retorno.setMsg(
            "Não foi possivel desbloquear cartão. O cartão deve estar com BLOQUEIO DE ORIGEM");
        map.put("dados", retorno);

        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      }

      credencialFacade.alterarStatusCredencial(
          dadosCartao.getIdCredencial(), STATUS_CARTAO_ATIVO, dadosCartao.getIdUsuario(), true);

      retorno.setMsg("Cartão desbloqueado com sucesso!");
      map.put("dados", retorno);
      return new ResponseEntity<>(map, HttpStatus.OK);

    } catch (Exception e) {
      e.printStackTrace();
      retorno.setMsg("Dados inválidos, tente novamente.");
      map.put("dados", retorno);
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  public String getDataCredencial(Date data) {
    SimpleDateFormat sdf = new SimpleDateFormat("MM/yy");
    return sdf.format(data);
  }

  @ApiOperation(value = "Solicitar desbloqueio de cartão.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/solicitar-desbloqueio-cartao-cvv",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESBLOQUEIO_CREDENCIAL_PORTADOR"})
  public ResponseEntity<DesbloqueioCartaoResponse> solicitarDesbloqueioCartaoCvv(
      @RequestBody DesbloqueioCartaoRequestCVV dadosCartao) {

    DesbloqueioCartaoResponse retorno = new DesbloqueioCartaoResponse();

    try {
      System.out.println("Desbloqueio do cartão do portador: " + dadosCartao.getIdCredencial());

      // Pegar o token na credencial
      Credencial credencial =
          credencialRepository.findOneByIdCredencial(dadosCartao.getIdCredencial());

      // chamada servico JCARD
      CheckCVVRequest cvvRequest = new CheckCVVRequest();
      cvvRequest.setToken(credencial.getTokenInterno());
      cvvRequest.setHashcvv2(dadosCartao.getCodigoCvv());
      cvvRequest.setSalt(getTokenJWT(request));

      // chamada servico JCARD
      JcardRequestEmbossing jcardRequest = new JcardRequestEmbossing();
      jcardRequest.setToken(credencial.getTokenInterno());
      jcardRequest.setIdconsumer("001");

      GetCardResponse responseJcardData = new GetCardResponse();

      responseJcardData = cardService.getPan(credencial.getTokenInterno());

      Date out = Date.from(credencial.getDataValidade().atZone(ZoneId.systemDefault()).toInstant());

      if (!responseJcardData.getSuccess()) {
        retorno.setStatusRetorno(false);
        retorno.setMsg(
            "Número do cartão não encontrado para a credencial: " + credencial.getIdCredencial());
        return new ResponseEntity<>(retorno, HttpStatus.OK);
      }

      String numerosMeioExtraidos = responseJcardData.getCard().getPan().substring(4, 12);
      if (numerosMeioExtraidos == null) {
        numerosMeioExtraidos = "";
      }

      if (!getDataCredencial(out).equals(dadosCartao.getMes() + "/" + dadosCartao.getAno())) {
        retorno.setStatusRetorno(false);
        retorno.setMsg("Validade do cartão incorreta.");
        return new ResponseEntity<>(retorno, HttpStatus.OK);
      } else if (!numerosMeioExtraidos.equals(dadosCartao.getNumeroMeioCredencial())) {
        retorno.setStatusRetorno(false);
        retorno.setMsg("Número do cartão incorreto.");
        return new ResponseEntity<>(retorno, HttpStatus.OK);
      }

      credencialFacade.alterarStatusCredencial(
          dadosCartao.getIdCredencial(), STATUS_CARTAO_ATIVO, dadosCartao.getIdUsuario(), true);
      System.out.println("Cartão desbloqueado!");
      retorno.setStatusRetorno(true);
      retorno.setMsg("Cartão desbloqueado");
      return new ResponseEntity<>(retorno, HttpStatus.OK);

    } catch (Exception e) {
      e.printStackTrace();
      retorno.setStatusRetorno(false);
      retorno.setMsg("Não foi possível realizar esta ação! " + e.getMessage());
      return new ResponseEntity<>(retorno, HttpStatus.OK);
    }
  }
}
