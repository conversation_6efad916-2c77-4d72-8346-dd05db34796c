package br.com.sinergico.service.suporte;

import br.com.client.rest.totvs.json.bean.response.NfsePdfTotvsApiResponse;
import br.com.client.rest.totvs.json.bean.response.NfseTotvsApiResponse;
import br.com.entity.cadastral.FaturaB2B;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.totvs.PreRegistroPedidosCarga;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.FaturaB2BMin;
import br.com.json.bean.cadastral.NFSERequest;
import br.com.json.bean.nfse.GerarNfseRequest;
import br.com.json.bean.nfse.NfseResponse;
import br.com.json.bean.nfse.NotaFiscalResponse;
import br.com.json.bean.nfse.VisualizarNotaFiscalRequest;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.FaturaB2BService;
import br.com.sinergico.service.cadastral.PreLancamentoLoteService;
import br.com.sinergico.service.nfse.NfseService;
import br.com.sinergico.service.totvs.PreRegistroPedidosCargaService;
import br.com.sinergico.service.totvs.api.TotvsService;
import br.com.sinergico.util.Util;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import javax.transaction.Transactional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class NotaFiscalService {

  @Autowired private NfseService nfseService;

  @Autowired private HierarquiaInstituicaoService instituicaoService;

  @Autowired private HierarquiaPontoRelacionamentoService pontoRelacionamentoService;

  @Autowired private ContatoService contatoService;

  @Autowired private PreLancamentoLoteService preLancamentoLoteService;

  @Autowired private FaturaB2BService faturaService;

  @Autowired private TotvsService totvsService;

  @Autowired private PreRegistroPedidosCargaService preRegistroPedidosCargaService;

  @Transactional
  public <Req, Res extends NfseResponse> NfseResponse gerarNotaFiscal(GerarNfseRequest gerarNfse) {
    return nfseService.gerarNfse(gerarNfse);
  }

  @Transactional
  public InputStream visualizarNotaFiscal(FaturaB2BMin fatura, SecurityUser user)
      throws IOException {

    HierarquiaInstituicao instituicao =
        instituicaoService.findByIdProcessadoraAndIdInstituicao(
            user.getIdProcessadora(), fatura.getIdInstituicao());
    if (instituicao == null) {
      throw new GenericServiceException("Instituição não encontrada para geração de Danfe.");
    }

    FaturaB2B faturaB2b = faturaService.findById(fatura.getIdFatura());
    if (faturaB2b == null) {
      throw new GenericServiceException("Fatura não encontrado para geração de Danfe.");
    }

    VisualizarNotaFiscalRequest nf = new VisualizarNotaFiscalRequest();
    NotaFiscalResponse nota = null;
    Double zero = new Double(0);
    ByteArrayInputStream input = null;

    nf.setIdProcessadora(instituicao.getIdProcessadora());
    nf.setIdInstituicao(instituicao.getIdInstituicao());
    nf.setNumeroNfse(faturaB2b.getNumeroNfse());
    nf.setGerarAntigo(Boolean.FALSE);

    nota = nfseService.gerarDanfe(nf);

    if (nota.getNaoTemRetencoes() != null
        && nota.getNaoTemRetencoes()) { // usado pela API para notas geradas antes de 15/08/2017
      nota = null;

      if (faturaB2b.getValorRetencoes() == null) {
        nf.setTotalRetencoes(zero);
      } else {
        nf.setTotalRetencoes(faturaB2b.getValorRetencoes().doubleValue());
      }
      HierarquiaPontoDeRelacionamento ptRel = findEmpresa(faturaB2b);
      if (ptRel.getRetencaoIss() == null) {
        nf.setValorIssRetido(zero);
      } else {
        nf.setValorIssRetido(ptRel.getRetencaoIss().doubleValue());
      }

      nf.setGerarAntigo(Boolean.TRUE);

      nota = nfseService.gerarDanfe(nf);
    }

    if (nota.getBytesPDF() != null) {
      input = new ByteArrayInputStream(nota.getBytesPDF());
    } else {
      throw new GenericServiceException(
          "Não foi possível encontrar bytes para geração do PDF. Erro: " + nota.getErrors());
    }

    return input;
  }

  @Transactional
  public InputStream gerarNotaFiscalXML(NFSERequest nfseRequest) {

    NfseTotvsApiResponse nfseTotvsApiResponse = null;
    try {
      PreRegistroPedidosCarga preRegistroPedidosCarga =
          preRegistroPedidosCargaService.buscarPreRegistroCarga(nfseRequest.getIdPedido());

      if (preRegistroPedidosCarga == null) {
        throw new GenericServiceException(
            "Não foi encontrado pedido de carga", HttpStatus.NOT_FOUND);
      }

      if (preRegistroPedidosCarga.getIdMovimento() == null) {
        throw new GenericServiceException(
            "Pedido carga não possui um movimento cadastrado", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      nfseTotvsApiResponse =
          totvsService.buscarNotaFiscalXML(preRegistroPedidosCarga.getIdMovimento());

      if (nfseTotvsApiResponse == null) {
        throw new GenericServiceException(
            "Retorno nulo ao gerar nota fiscal", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      if (!nfseTotvsApiResponse.getSucesso()) {
        throw new GenericServiceException(
            "Não foi possível gerar nota fiscal. \n" + nfseTotvsApiResponse.getErros(),
            HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (GenericServiceException e) {
      throw new GenericServiceException(e.getMessage(), e.getHttpStatus());
    }

    return Util.convertObjectToInputStream(nfseTotvsApiResponse.getData());
  }

  private HierarquiaPontoDeRelacionamento findEmpresa(FaturaB2B fatura) {
    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(
            fatura.getIdProcessadora(),
            fatura.getIdInstituicao(),
            fatura.getIdRegional(),
            fatura.getIdFilial(),
            fatura.getIdPontoDeRelacionamento());

    HierarquiaPontoDeRelacionamento pontoRelacionamento = pontoRelacionamentoService.findById(id);
    if (pontoRelacionamento == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar os dados da empresa para gerar Danfe de nota fiscal com data anterior a 16/08/2017");
    }
    return pontoRelacionamento;
  }

  public InputStream gerarNotaFiscalPdf(NFSERequest nfseRequest) {

    NfsePdfTotvsApiResponse nfsePdfTotvsApiResponse;
    try {
      PreRegistroPedidosCarga preRegistroPedidosCarga =
          preRegistroPedidosCargaService.buscarPreRegistroCarga(nfseRequest.getIdPedido());

      if (preRegistroPedidosCarga == null) {
        throw new GenericServiceException(
            "Não foi encontrado pedido de carga", HttpStatus.NOT_FOUND);
      }

      if (preRegistroPedidosCarga.getIdMovimento() == null) {
        throw new GenericServiceException(
            "Pedido carga não possui um movimento cadastrado", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      nfsePdfTotvsApiResponse =
          totvsService.buscarNotaFiscalPdf(preRegistroPedidosCarga.getIdMovimento());

      if (nfsePdfTotvsApiResponse == null) {
        throw new GenericServiceException(
            "Retorno nulo ao gerar nota fiscal", HttpStatus.INTERNAL_SERVER_ERROR);
      }

      if (!nfsePdfTotvsApiResponse.getSucesso()) {
        throw new GenericServiceException(
            "Não foi possível gerar nota fiscal. \n" + nfsePdfTotvsApiResponse.getErros(),
            HttpStatus.INTERNAL_SERVER_ERROR);
      }
    } catch (GenericServiceException e) {
      throw new GenericServiceException(e.getMessage(), e.getHttpStatus());
    }

    return Util.base64ToInputStream(nfsePdfTotvsApiResponse.getData());
  }
}
