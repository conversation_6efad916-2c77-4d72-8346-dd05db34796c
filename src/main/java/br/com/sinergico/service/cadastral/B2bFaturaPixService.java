package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.B2bFaturaPix;
import br.com.json.bean.adq.QrcodeFaturaVO;
import br.com.json.bean.pix.request.webhook.MovimentoForm;
import br.com.sinergico.repository.cadastral.B2bFaturaPixRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class B2bFaturaPixService extends GenericService<B2bFaturaPix, Long> {
  public static final int STATUS_PIX_CARGA_SALVO = 0;
  public static final int STATUS_PIX_CARGA_RECEBIDO = 1;

  private B2bFaturaPixRepository repository;

  @Autowired
  public B2bFaturaPixService(B2bFaturaPixRepository b2bFaturaPixRepository) {
    super(b2bFaturaPixRepository);
    this.repository = b2bFaturaPixRepository;
  }

  public B2bFaturaPix encontraPixPorEndToEnd(String endToEnd) {
    return repository.findByIdEndToEnd(endToEnd);
  }

  public B2bFaturaPix encontraPorIdFatura(Long idFatura) {
    return repository.findByIdFatura(idFatura);
  }

  public void salvar(B2bFaturaPix b2bFaturaPix) {
    repository.save(b2bFaturaPix);
  }

  public Boolean salvarQrcodeFatura(QrcodeFaturaVO model) {

    B2bFaturaPix b2bFaturaPix = null;
    b2bFaturaPix = repository.findByIdFatura(model.getIdFatura());
    if (b2bFaturaPix != null) {
      b2bFaturaPix.setTxPixCopiaECola(model.getTxPixCopiaECola());
      b2bFaturaPix.setIdReferenciaInterna(model.getIdReferenciaInterna());
      b2bFaturaPix.setDtHrStatus(LocalDateTime.now());
    } else {
      b2bFaturaPix = new B2bFaturaPix();
      b2bFaturaPix.setIdReferenciaInterna(model.getIdReferenciaInterna());
      b2bFaturaPix.setTxPixCopiaECola(model.getTxPixCopiaECola());
      b2bFaturaPix.setIdLoteLanc(model.getIdLoteLanc());
      b2bFaturaPix.setIdFatura(model.getIdFatura());
      b2bFaturaPix.setVlFatura(model.getVlFatura());
      b2bFaturaPix.setStatus(model.getStatus());
      b2bFaturaPix.setDtHrStatus(LocalDateTime.now());
      b2bFaturaPix.setDtHrInclusao(LocalDateTime.now());
    }
    repository.save(b2bFaturaPix);
    return true;
  }

  public B2bFaturaPix encontraPixPorReferenciaInternaEStatusSalvo(String referenciaInterna) {
    return repository.findByIdReferenciaInternaAndStatus(referenciaInterna, STATUS_PIX_CARGA_SALVO);
  }

  public String gerarReferenciaInterna(SecurityUser user) {
    String idUsuario = user.getIdUsuario().toString();
    String dataAtual = LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE);
    String uuid = UUID.randomUUID().toString().replaceAll("-", "");
    return dataAtual
        + idUsuario
        + "U"
        + uuid.substring(0, 28 - (dataAtual.length() + idUsuario.length() + 1));
  }

  public void trataPagamentosDeCargasPIX(MovimentoForm movimentoForm, boolean success) {
    B2bFaturaPix b2bFaturaPix = null;
    if (movimentoForm.getReferenciaInterna() != null
        && !movimentoForm.getReferenciaInterna().isEmpty()) {
      b2bFaturaPix =
          encontraPixPorReferenciaInternaEStatusSalvo(movimentoForm.getReferenciaInterna());
    }
    if (success && b2bFaturaPix != null) {
      b2bFaturaPix.setStatus(STATUS_PIX_CARGA_RECEBIDO);
      b2bFaturaPix.setIdEndToEnd(movimentoForm.getEndToEnd());
      b2bFaturaPix.setDtHrStatus(LocalDateTime.now());
      b2bFaturaPix.setDtHrRecebido(LocalDateTime.now());
      salvar(b2bFaturaPix);
    }
  }

  public Integer encontraNumeroPedidoParaExtratoPorRRN(String rrn) {
    return repository.encontraLoteParaExtratoPorRRN(rrn);
  }
}
