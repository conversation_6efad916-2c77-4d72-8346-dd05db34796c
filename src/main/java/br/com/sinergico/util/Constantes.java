package br.com.sinergico.util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/** Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 16/02/17. */
public interface Constantes {

  /* ******************************** */
  /* Constante de decisão do Ambiente */
  /* ******************************** */
  /* ******************************** */
  /* *~*~*~*~*~*~*~*~*~*~* */
  /* Constantes <PERSON> */
  /* *~*~*~*~*~*~*~*~*~*~* */

  /* ~ Processadora ~ */

  Integer ID_PROCESSADORA_ITS_PAY = 10;

  /* ~ Instituições ~ */

  Integer ID_PRODUCAO_INSTITUICAO_TRAVA_TODAS = 0;
  Integer ID_PRODUCAO_INSTITUICAO_HIGH_TECH = 1001;
  Integer ID_PRODUCAO_INSTITUICAO_BAHAMAS = 1201;
  Integer ID_PRODUCAO_INSTITUICAO_FINANCIAL = 1401;
  Integer ID_PRODUCAO_INSTITUICAO_JOY_POINTS = 1601;
  Integer ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS = 1801;
  Integer ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL = 2001;
  Integer ID_PRODUCAO_INSTITUICAO_FACIL = 2201;
  Integer ID_PRODUCAO_INSTITUICAO_BRBCARD = 2401;
  Integer ID_PRODUCAO_INSTITUICAO_CELER = 2601;
  Integer ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS = 2801;
  Integer ID_PRODUCAO_INSTITUICAO_UNIDAS_CLUB = 3001;
  Integer ID_PRODUCAO_INSTITUICAO_BANCO_PAN = 3201;
  Integer ID_PRODUCAO_INSTITUICAO_MONEY_PAG = 3401;
  Integer ID_PRODUCAO_INSTITUICAO_CREDISIS = 3601;
  Integer ID_PRODUCAO_INSTITUICAO_INFINITY = 3801;
  Integer ID_PRODUCAO_INSTITUICAO_KREDIT = 4001;
  Integer ID_PRODUCAO_INSTITUICAO_DAXPAY = 4201;
  Integer ID_PRODUCAO_INSTITUICAO_MATERA = 4401;
  Integer ID_PRODUCAO_INSTITUICAO_SEAC = 4601;
  Integer ID_PRODUCAO_INSTITUICAO_CF_BANK = 4801;
  Integer ID_PRODUCAO_INSTITUICAO_WIP = 5001;
  Integer ID_PRODUCAO_INSTITUICAO_VIVA = 5201;
  Integer ID_PRODUCAO_INSTITUICAO_FANBANK = 5401;
  Integer ID_PRODUCAO_INSTITUICAO_AMAKHA = 5601;
  Integer ID_PRODUCAO_INSTITUICAO_PAXPAY = 5801;
  Integer ID_PRODUCAO_INSTITUICAO_1DBANK = 6001;
  Integer ID_PRODUCAO_INSTITUICAO_BITFY = 6201;
  Integer ID_PRODUCAO_INSTITUICAO_BRBPAY = 6401;
  Integer ID_PRODUCAO_INSTITUICAO_CUSCUZ_PAY = 6601;
  Integer ID_PRODUCAO_INSTITUICAO_VALE_GAS = 6801;
  Integer ID_PRODUCAO_INSTITUICAO_LIBERTO = 7001;
  Integer ID_PRODUCAO_INSTITUICAO_TRADE_SOLUTION = 7201;
  Integer ID_PRODUCAO_INSTITUICAO_DINTECH = 7401;
  Integer ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI = 7601;
  Integer ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA = 8001;
  Integer ID_PRODUCAO_INSTITUICAO_AGRO_CASH = 8201;
  Integer ID_PRODUCAO_INSTITUICAO_MULVI = 8401;
  Integer ID_PRODUCAO_INSTITUICAO_ESCOTEIROS = 8601;
  Integer ID_PRODUCAO_INSTITUICAO_POC_ELO = 1101;
  Integer ID_PRODUCAO_INSTITUICAO_RP3_BANK = 8801;
  Integer ID_PRODUCAO_INSTITUICAO_ENTREPAY = 1102;
  Integer ID_PRODUCAO_INSTITUICAO_DISNUVII = 9401;
  Integer ID_PRODUCAO_INSTITUICAO_CVS_CESTAS = 9001;
  Integer ID_PRODUCAO_INSTITUICAO_WIZ = 9201;
  Integer ID_PRODUCAO_QISTA = 9601;
  Integer ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU = 1301;

  List<Integer> LIST_INSTITUICOES_PORTADOR_DOCUMENTO_ACESSO_ADICIONAL =
      Arrays.asList(ID_PRODUCAO_INSTITUICAO_BRBCARD);

  /* ~ Regional ~ */

  Integer ID_PRODUCAO_REGIONAL_IN_MAIS = 1;

  /* ~ Filial ~ */

  Integer ID_PRODUCAO_FILIAL_IN_MAIS = 1;

  /* ~ Ponto de Relacionamento ~ */

  Integer ID_PRODUCAO_PONTO_RELACIONAMENTO_IN_MAIS = 1;

  /* ~ Produtos ~ */
  int TIPO_POSTAGEM_NAO_APLICAVEL = 0;

  // 1401
  Integer ID_PROD_INST_MEIA_MARATONA_1 = 140116;
  Integer ID_PROD_INST_MEIA_MARATONA_2 = 140127;

  // 1601
  Integer PROD_INSTITUICAO_BASICO_JOYPOINTS = 160101;

  // 1801
  // IDs Produto Instituicao Corporativos
  // 2001
  Integer PROD_INSTITUICAO_BASICO_IN_MAIS = 200101;

  // 2401
  // 2801
  Integer ID_PRODUCAO_PRODUTO_BASICO_INSTITUICAO_BANK_10 = 280103;

  /* ~ Parceiros de Pontos ~ */

  int PARCEIRO_ACUMULO_IN_MAIS_PREMIOS = 302001;
  Long PARCEIRO_BONIFICADOR_JOY_POINTS = 1600L;
  Long PARCEIRO_JOY_POINTS_NO_PROGRAMA_INMAIS = 1602L;
  Long PARCEIRO_BONIFICADOR_INMAIS = 2001L;

  /* ~ Produto Plataforma ~ */

  Integer PROD_PLATAFORMA_PRE_PAGO = 1;
  Integer PROD_PLATAFORMA_VALE_ALIMENTACAO = 2;
  Integer PROD_PLATAFORMA_VALE_REFEICAO = 3;
  Integer PROD_PLATAFORMA_CONVENIO = 4;
  Integer PROD_PLATAFORMA_BROKER_INST_EMISSORA_EXTERNA = 5;
  Integer PROD_PLATAFORMA_TRANSFERENCIA_BANCARIA = 6;
  Integer PROD_PLATAFORMA_TRANSFERENCIA_ORDEM_PAGAMENTO = 7;
  Integer PROD_PLATAFORMA_PROGRAMA_DE_RECOMPENSA = 8;
  Integer PROD_PLATAFORMA_CREDITO = 9;
  Integer PROD_PLATAFORMA_VOUCHER_PAPEL = 10;
  Integer PROD_PLATAFORMA_VALE_CULTURA = 11;
  Integer PROD_PLATAFORMA_BNDES = 91;
  Integer PROD_PLATAFORMA_ATACADISTA = 92;
  Integer PROD_PLATAFORMA_PEF_FRETE = 93;
  Integer PROD_PLATAFORMA_CONTROLE_DE_FROTA = 94;

  /* ~ Arranjo Relacionamento ~ */

  Integer CONTA_DE_PAGAMENTO_PRE_PAGA = 1;
  Integer CONTA_DE_PAGAMENTO_POS_PAGA = 2;
  Integer CONTA_DE_DEPOSITO_A_VISTA = 3;
  Integer RELACIONAMENTO_EVENTUAL = 4;
  Integer INTERNO = 9;

  /* ~ ID de Usuários de Sistema ~ */

  Integer ID_USUARIO_AUTOMATIZA_CADASTROS = 999995;
  Integer ID_USUARIO_TED_AUTOMATIZADA = 999997;
  Integer ID_USUARIO_REPLICADOR = 999998;
  Integer ID_USUARIO_INCLUSAO_PORTADOR = 999999;
  Integer ID_USUARIO_AUTOMATICO = 999900;
  Integer ID_USUARIO_PROC_7010 = 999996;
  Integer ID_USUARIO_URA_BAHAMAS = 42;
  Integer ID_USUARIO_URA_ITSPAY = 43;
  Integer ID_USUARIO_URA_BRB = 4967;
  String patternArquivoCadastros = "CADASTRO_(\\d+)_(\\d+)_(\\d+)_(\\d+)_(\\d{6})(.*)\\.(xls|xlsx)";
  String patternArquivoVinculos = "VINCULO_(\\d+)_(\\d+)_(\\d+)_(\\d+)_(\\d{6})(.*)\\.(xls|xlsx)";
  String patternArquivoPdaf =
      "(CADASTRO|EDICAO|ADICIONAL)_(\\d+)_(\\d+)_(\\d+)_(\\d+)_(\\d{6})(.*)\\.(xls|xlsx)";

  /* ~ Níveis de Hierarquia ~ */

  int NIVEL_PROCESSADORA = 1;
  int NIVEL_INSTITUICAO = 2;
  int NIVEL_REGIONAL = 3;
  int NIVEL_FILIAL = 4;
  int NIVEL_PONTO_DE_RELACIONAMENTO = 5;

  /* ~ Grupos de Usuários ~ */

  int SUPER_GRUPO_2 = 2;
  int GRUPO_3 = 3;
  int GRUPO_PORTADOR = 9;
  int GRUPO_NEGOCIOS = 12;

  /* ~ Status de Usuários ~ */

  Integer USUARIO_INATIVO = 0;
  Integer USUARIO_ATIVO = 1;
  Integer USUARIO_BLOQUEADO_TEMPORARIAMENTE = 2;

  /* ~ Status Usuário Estabelecimento ~ */

  Integer USUARIO_DESABILITADO = 2;
  Integer USUARIO_CANCELADO = 9;

  /* ~ Autor ~ */

  Integer AUTOR_SISTEMA = 1;
  Integer AUTOR_PORTADOR = 2;
  Integer AUTOR_AUTOMATICO = 3;
  Integer AUTOR_URA = 4;

  /* ~ Tipos de Pessoa ~ */

  Integer PESSOA_FISICA = 1;
  Integer PESSOA_JURIDICA = 2;

  /* ~ Titularidade de Conta ~ */

  Integer TITULAR = 1;
  Integer ADICIONAL = 2;

  /* ~ Tipo Endereço ~ */

  Integer CEP_E_NUMEROS = 1;
  Integer OUTROS = 2;
  Integer UF = 3;

  /* ~ Tipo Status Endereço ~ */

  Integer ENDERECO_ATIVO = 1;
  Integer ENDERECO_RESIDENCIAL = 1;
  Integer ENDERECO_COMERCIAL = 2;
  Integer ENDERECO_FISCAL = 3;
  Integer ENDERECO_CORRESPONDENCIA = 5;

  /* ~ Tipos de Contato ~ */

  Integer TIPO_CONTATO_GERAL = 0;
  Integer TIPO_CONTATO_COMERCIAL = 1;
  Integer TIPO_CONTATO_MARKETING = 2;
  Integer TIPO_CONTATO_RH = 3;
  Integer TIPO_CONTATO_FINANCEIRO = 4;
  Integer TIPO_CONTATO_OPERACIONAL = 5;
  Integer CONTATO_ATIVO = 1;

  /* ~ Operadoras de Telefonia Celular ~ */

  Long ID_OPERADORA_CLARO = 1L;
  Long ID_OPERADORA_NEXTEL = 2L;
  Long ID_OPERADORA_OI = 3L;
  Long ID_OPERADORA_TIM = 4L;
  Long ID_OPERADORA_VIVO = 5L;

  /* ~ Parceiros Voucher ~ */

  Long ID_PARCEIRO_VOUCHER_AMERICANAS = 1L;
  Long ID_PARCEIRO_VOUCHER_SUBMARINO = 2L;
  Long ID_PARCEIRO_VOUCHER_SHOPTIME = 3L;
  Long ID_PARCEIRO_VOUCHER_CINEMARK = 4L;
  Long ID_PARCEIRO_VOUCHER_UBER = 5L;
  String PASSWORD_IMKT = "password_";

  /* *~*~*~*~*~*~* */
  /* Configurações */
  /* *~*~*~*~*~*~* */

  /* ~ Headers Comuns ~ */

  String USER_AGENT_ISSUER_BACK = "IssuerBack.Valloo";

  /* ~ Tamanhos e Definições ~ */

  int MIN_LENGHT_ID_PROCESSADORA = 2;
  int MIN_LENGHT_ID_INSTITUICAO = 4;
  Integer TAMANHO_CPF = 11;
  Integer TAMANHO_CNPJ = 14;

  Integer SEIS_MESES_EM_DIAS = 180;

  char DEFAULT_SEPARATOR = ';';
  char DEFAULT_CSV_SEPARATOR = ',';
  char CHAR_ZERO = '0';
  Integer ZERO_INTEGER = 0;
  Double ZERO_DOUBLE = Double.valueOf(0);
  Long ZERO_LONG = 0L;
  Long MENOS_UM_LONG = (long) -1;
  Integer MENOS_UM_INTEGER = -1;

  String ZPK_001 = "zpk.001";
  Integer TAMANH0_NUMERO_CARTAO = 16;
  String MASK_2_PARTES_COMPLETA = "$1-XXXX-XXXX-$2";
  String PADRAO_CREDENCIAL = "([0-9]{4})[0-9]{0,9}([0-9]{4})";
  String REGEX_VALIDA_CELULAR = "^[1-9]{2}[9]{1}[0-9]{8}";

  String FMT_DD_MM_YYYY = "dd/MM/yyyy";

  float PORCENTAGEM_LEVENSHTEIN_DISTANCE =
      (float)
          (55.0 / 100.0); // Valores acima de 0.5 são mais propensos a não falhar de acordo com o
  // teorema da distância de Levenshtein.

  /* ~ Acessos ~ */

  String B2B_ACESSO_VIA_ISSUER = "b2b_via_issuer";
  String B2B_ACESSO_NORMAL = "b2b";
  String ACESSO_ISSUER_NORMAL = "issuer";
  long VALOR_PADRAO_QUANTIDADE_TENTATIVAS_LOGIN = 3l;
  Integer QUANTIDADE_HISTORICO_SENHA = 5;

  /* ~ Credencial ~ */

  Integer TITULARIDADE_CREDENCIAL = 1;
  Boolean CREDENCIAL_NAO_VIRTUAL = Boolean.FALSE;
  Boolean CREDENCIAL_VIRTUAL = Boolean.TRUE;

  /* ~ Configurações PIX ~ */

  Integer STATUS_PIX_HABILITADO = 1;
  Integer STATUS_PIX_DESABILITADO = 2;
  BigDecimal LIMITE_MAXIMO_UNITARIO_BACEN = BigDecimal.valueOf(200.0);
  BigDecimal LIMITE_MAXIMO_PERIODO_BACEN = BigDecimal.valueOf(1000.0);

  /* ~ Parâmetro Valor ~ */

  String DIR_CERT_BOLETO_BRADESCO = "bol.237.certdir";
  String SNH_CERT_BOLETO_BRADESCO = "bol.237.certsnh";
  String DESC_CHAVE_PARAMETRO_CONTA_GARANTIA = "c.garantia.saldo";
  String PARAMETRO_CONSULTAR_LICITACAO_0800 = "consultar.0800";
  String PARAMETRO_VALIDAR_TOKEN = "validar.token";
  String PARAMETRO_VALIDAR_TOKEN_SUPER_USUARIO = "validar.super.token";
  String PARAMETRO_DEFINICAO_TOKEN_API_WHATSAPP = "token.api.whatsapp";
  String PARAMETRO_URL_REDEFINIR_SENHA = "url.redefinir.senha";
  String PARAMETRO_RESTRINGIR_CODIGOS_AJUSTES_MANUAIS = "restringe.cod.ajuste";
  String PARAMETRO_MAXIMO_CARTOES_EMITIDOS = "maximo.emissoes.inst";
  String PARAMETRO_COUNTER_CARTOES_EMITIDOS = "count.emissoes.inst";
  Integer PARAMETRO_TEMPO_VALIDADE_REDIFINICAO_SENHA = 90;
  Integer PARAMETRO_TEMPO_VALIDADE_TOKEN_REDIFINICAO_SENHA = 100;
  Integer PARAMETRO_VALOR_ESAFER = 115;
  Integer PARAMETRO_VALOR_RAKUTEN = 117;
  Integer PARAMETRO_QUANTIDADE_TENTATIVAS_LOGIN_ISSUER = 94;
  Integer PARAMETRO_VALOR_SOLICITACAO_PIX = 124;
  Integer PARAMETRO_VALOR_LIMITE_BOLETO_DIARIO = 125;
  Integer PARAMETRO_ATIVACAO_APLICATIVO = 126;
  String PARAMETRO_TEMPO_TROCA_DISPOSITIVO = "dias.mudanca.disp";
  String PARAMETRO_LIMITE_MAXIMO_DIURNO_CONTA = "lim.max.dia.conta";
  String PARAMETRO_LIMITE_MAXIMO_TRANSACAO_DIURNO_CONTA = "lim.uni.dia.conta";
  String PARAMETRO_LIMITE_MAXIMO_NOTURNO_CONTA = "lim.max.noite.conta";
  String PARAMETRO_LIMITE_MAXIMO_TRANSACAO_NOTURNO_CONTA = "lim.uni.noite.conta";
  String PARAMETRO_EMPRESA_NOTIFICACAO = "ponto.relaci.notifi";
  String PARAMETRO_EMPRESA_MENSAGEM = "noti.port.valloo";

  /* ~ Parâmetro Processamento ~ */

  String HR_TED_RENDIMENTO = "HR_TED_RENDIMENTO";
  String HR_TED_CELCOIN = "HR_TED_CELCOIN";

  /* ~ Tipos de Resgate ~ */

  Integer TIPO_RESGATE_CREDITO_CONTA_CORRENTE = 1;
  Integer TIPO_RESGATE_CREDITO_CARTAO_PRE_PAGO = 2;
  Integer TIPO_RESGATE_CARTAO_PRE_PAGO = 3;
  Integer TIPO_RESGATE_RECARGA_CELULAR = 4;
  Integer TIPO_RESGATE_B2W_VOUCHER = 5;
  Integer TIPO_RESGATE_VIA_VAREJO_PRODUTOS = 6;
  Integer TIPO_RESGATE_ITLOG_PRODUTOS = 7;
  Integer TIPO_RESGATE_GIFT_VOUNCHER = 8;
  Integer TIPO_RESGATE_BRASIL_CT_PASSAGENS_AEREAS = 9;
  Integer TIPO_RESGATE_TED_ABC = 10;
  Integer TIPO_RESGATE_EASYLIVE_VOUCHER = 11;
  Integer TIPO_RESGATE_PAGAMENTO_CELCOIN = 12;
  Integer TIPO_RESGATE_RECARGA_CELULAR_CELCOIN = 13;
  Integer TIPO_RESGATE_QRCODE_VALLOO = 14;
  Integer TIPO_RESGATE_NETSHOES_VOUNCHER = 15;
  Integer TIPO_RESGATE_CONTA_CORRENTE_CELCOIN = 16;

  /* ~ Tipo Log Exportação ~ */

  Integer TIPO_LOG_EXPORTACAO_ARQUIVO_PEDIDO_CARGA = 1;
  Integer TIPO_LOG_EXPORTACAO_ARQUIVO_RESGATE_CONTA_BANCARIA = 2;

  /* ~ Titulo de Capitalização ~ */

  Integer TITULO_DE_CAPITALIZACAO_TIPO_MENSAL = 1;
  Integer TITULO_DE_CAPITALIZACAO_TIPO_ANUAL = 2;

  /* ~ Replicável ~ */

  Integer REPLICAVEL_NAO = 0;
  Integer REPLICAVEL_ELEITO = 1;
  Integer REPLICAVEL_REPLICADO = 2;
  Integer REPLICAVEL_CANCELADO = 9;

  /* ~ Constantes de parceiro resgate 2021322-RF ~ */

  Integer ID_PARCEIRO_RESGATE_INMAIS_VIA_VAREJO = 2;
  Integer ID_PARCEIRO_RESGATE_INMAIS_APP_SITE = 6;
  Integer ID_PARCEIRO_RESGATE_INMAIS_VOUCHER = 8;
  Integer PARCEIRO_RESGATE_CONTA_CORRENTE_CELCOIN = 15;

  /* ~ Importação de Arquivos ~ */

  Integer DIAS_VALIDADE = 90;
  String ID_REG_HEADER = "1";
  String ID_REG_DETALHE = "2";
  String ID_REG_TRAILER = "9";
  String COD_DOC_CPF_1 = "1";
  String COD_DOC_CPF_3 = "3";
  String COD_DOC_CNPJ_2 = "2";
  String CV16 = "16";
  String CV22 = "22";
  String CV14 = "14";
  String CV75 = "75";
  String CV100 = "100";
  String CV01 = "1";
  String CV23 = "23";

  /* ~ Modelo de Arquivos ~ */

  Integer VINCULA_DEPENDETE_AO_RESPONSAVEL = 1;
  Integer CADASTRA_DEPENDETE_E_RESPONSAVEL = 2;

  /* ~ Sequencial conta Inicial ~ */

  Integer SEQ_CONTA_INICIAL_HIERARQUIA_INSTITUICAO = 100;

  /* ~ Caminhos ~ */

  String CAMINHO_INFINANCAS_INTEGRACAO = "infinancas/integracao/";
  String CAMINHO_IN_MAIS_TITULO_CAPITALIZACAO = "inmais/titulos/";

  /* *~*~*~*~*~* */
  /* Financeiros */
  /* *~*~*~*~*~* */

  /* ~ Conversão de pontos ~ */

  Double INMAIS_CONVERSAO_PONTOS_MULTIPLICACAO = new Double(100);
  BigDecimal INMAIS_CONVERSAO_PONTOS_PARA_REAIS = new BigDecimal(100);
  Double JOYPOINTS_CONVERSAO_PONTOS_DIVISAO = 0.025;
  Integer PONTOS_UNIDAS_CONVERTIDO_PARA_IN_MAIS = 100;

  /* ~ Códigos de Moedas ~ */

  String CODIGO_MOEDA_PADRAO = "986";
  Integer CODIGO_MOEDA_DE_PONTO = 999;
  String CODIGO_MOEDA_PONTO = "999";

  /* ~ Bandeiras ~ */

  Integer BANDEIRA_PRODUTO_VISA = 1;
  Integer BANDEIRA_PRODUTO_MASTERCARD = 2;
  Integer BANDEIRA_PRODUTO_CABAL = 3;
  Integer BANDEIRA_PRODUTO_ELO = 4;
  Integer COD_BANDEIRA_VISA_1 = 1;
  Integer COD_BANDEIRA_CABAL_2 = 2;

  /* ~ Tipo Conta Bancária ~ */

  String TIPO_CONTA_BANCARIA_CORRENTE = "1";
  String TIPO_CONTA_BANCARIA_POUPANCA = "2";
  String TIPO_CONTA_BANCARIA_SALARIO = "3";
  String TIPO_CONTA_BANCARIA_FACIL_023 = "4";

  /* ~ País e Entrada ~ */

  String CODIGO_BRASIL = "076";
  String MODO_ENTRADA_PADRAO = "811";

  /* ~ Códigos de Transação ~ */

  int COD_TRANSACAO_CR_A_VISTA_ARRANJO = 200;
  int COD_TRANSACAO_EST_CR_A_VISTA_ARRANJO = 201;
  int COD_TRANSACAO_TAXA_COMPRA_DE_PONTOS = 346;
  int COD_TRANSACAO_ESTORNO_TAXA_COMPRA_DE_PONTOS = 347;
  int COD_TRANSACAO_LANCAMENTO_PLANO_SAUDE = 368;
  int COD_TRANSACAO_EST_LANCAMENTO_PLANO_SAUDE = 369;
  int COD_TRANSACAO_ACUMULO_SAFRA_TRANSF_DE_OUTRO_PROGRAMA = 434;
  int COD_TRANSACAO_ACUMULO_SAFRA = 542;
  int COD_TRANSACAO_ESTORNO_ACUMULO_SAFRA = 543;
  int COD_TRANSACAO_TARIFA_ESTORNO = 546;
  int COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_DEBITO = 556;
  int COD_TRANSACAO_TED = 558;
  int COD_TRANSACAO_ESTORNO_TED = 559;
  int TARIFA_PRIMEIRA_VIA = 560;
  int TARIFA_REPOSICAO = 562;
  int COD_CUSTO_MANUTENCAO_CONTA = 566;
  int COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_CREDITO = 666;
  int COD_TRANSACAO_CR_GENERICO = 668;
  int COD_TRANSACAO_DEBITO_GENERICO = 670;
  int COD_TRANSACAO_ESTORNO_DEBITO_GENERICO = 671;
  int COD_TRANSACAO_LOTE_CARGA_BENEFICIO = 672;
  int COD_TRANSACAO_VOUCHER_RESGATE = 680;
  int COD_TRANSACAO_ESTORNO_VOUCHER_RESGATE = 681;
  int COD_TRANSACAO_BONIFICACAO_SAFRA = 682;
  int COD_TRANSACAO_ESTORNO_BONIFICACAO_SAFRA = 683;
  int COD_TRANSACAO_TRANSFERIR_PARA_OUTRO_PROGRAMA = 684;
  int COD_TRANSACAO_ESTORNO_TRANSFERENCIA_DE_OUTRO_PROGRAMA = 685;
  int COD_TRANSACAO_TRANSFER_B2B_DEBITO = 686;
  int COD_TRANSACAO_TRANSFER_B2B_CREDITO = 688;
  int COD_TRANSACAO_RESGATE_CONTA_BANCARIA = 694;
  int COD_TRANSACAO_ESTORNO_TRANSFERENCIA_BANCARIA = 695;
  int COD_TRANSACAO_TARIFA_TRANSFERENCIA_BANCARIA = 696;
  int COD_TRANSACAO_ESTORNO_TARIFA_TRANSFERENCIA_BANCARIA = 697;
  int COD_TRANSACAO_RESGATE_RECARGA_CELULAR = 698;
  int COD_TRANSACAO_ESTORNO_RECARGA_DE_CELULAR = 699;
  int COD_TRANSACAO_AJUSTE_A_CREDITO_LOYALTY = 701;
  int COD_TRANSACAO_AJUSTE_A_DEBITO_LOYALTY = 703;
  int COD_TRANSACAO_COMPRA_PONTOS = 706;
  int COD_TRANSACAO_DEBITO_PLANO_SAUDE = 726;
  int COD_TRANSACAO_EST_DEBITO_PLANO_SAUDE = 727;
  int COD_TRANSACAO_PAGAMENTO_PLANO_SAUDE = 728;
  int COD_TRANSACAO_EST_PAGAMENTO_PLANO_SAUDE = 729;
  int COD_TRANSACAO_SALDO_MIGRADO = 730;
  int COD_TRANSACAO_CARTAO_PRE_PAGO_RESGATE = 744;
  int COD_TRANSACAO_ESTORNO_SOLICITACAO_CARTAO_PRE_PAGO = 745;
  int COD_TRANSACAO_CARTAO_PRE_PAGO_TRANSFERENCIA = 746;
  int COD_TRANSACAO_TRANSFERENCIA_ENTRE_CONTAS = 746;
  int COD_TRANSACAO_RESGATE_PASSAGEM_AEREA = 760;
  int COD_TRANSACAO_RECARGA_CELULAR = 826;
  int COD_TRANSACAO_LOTE_CARGA_VT = 838;
  int COD_TRANSACAO_RESGATE_GIFFY_VOUCHER = 846;
  int COD_TRANSACAO_LOTE_CARGA_FERIAS = 848;
  int COD_TRANSACAO_LOTE_CARGA_PONTOS = 858;
  int COD_TRANSACAO_LOTE_CARGA_SALARIO = 868;
  int COD_TRANSACAO_RESGATE_SALDO_MOEDEIRO = 872;
  int COD_TRANSACAO_COBRAR_COM_QR_CODE = 876;
  int COD_TRANSACAO_ENVIO_PIX = 880;
  int COD_TRANSACAO_ESTORNO_ENVIO_PIX = 881;
  int COD_TRANSACAO_RECEBIMENTO_PIX = 884;
  int COD_TRANSACAO_DEVOLUCAO_PIX_PARCIAL = 888;
  int COD_TRANSACAO_DEVOLUCAO_PIX_TOTAL = 890;
  int COD_TRANSACAO_PIX_BLOQUEIO_SISTEMA = 922;
  int COD_TRANSACAO_EST_PIX_BLOQUEIO_SISTEMA = 923;
  int COD_TRANSACAO_SOLICITACAO_ENVIO_PIX = 934;
  int COD_TRANSACAO_ESTORNO_SOLICITACAO_ENVIO_PIX = 935;
  int COD_TRANSACAO_SOLICITACAO_DEVOLUCAO_PIX_TOTAL = 948;
  int COD_TRANSACAO_SOLICITACAO_DEVOLUCAO_PIX_PARCIAL = 944;

  /* ~ Sinal de Transação ~ */

  Integer CREDITADO = 1;
  Integer DEBITADO = -1;

  /* ~ Tarifas PIX String ~ */
  String COD_TRANSACAO_ENVIO_PIX_STRING = "880";
  String COD_TRANSACAO_TARIFA_ENVIO_PIX_STRING = "882";
  String COD_TRANSACAO_RECEBIMENTO_PIX_STRING = "884";
  String COD_TRANSACAO_TARIFA_RECEBIMENTO_PIX_STRING = "886";
  List<String> COD_TRANSACAO_PIX_CARGA_AUTOMATIZADA =
      Arrays.asList(
          COD_TRANSACAO_ENVIO_PIX_STRING,
          COD_TRANSACAO_TARIFA_ENVIO_PIX_STRING,
          COD_TRANSACAO_RECEBIMENTO_PIX_STRING,
          COD_TRANSACAO_TARIFA_RECEBIMENTO_PIX_STRING);
  List<String> COD_TRANSACAO_ENVIO_PIX_CARGA_AUTOMATIZADA =
      Arrays.asList(COD_TRANSACAO_ENVIO_PIX_STRING, COD_TRANSACAO_TARIFA_ENVIO_PIX_STRING);
  List<String> COD_TRANSACAO_RECEBIMENTO_PIX_CARGA_AUTOMATIZADA =
      Arrays.asList(
          COD_TRANSACAO_RECEBIMENTO_PIX_STRING, COD_TRANSACAO_TARIFA_RECEBIMENTO_PIX_STRING);

  /* ~ Tarifa ~ */

  Long SEM_TARIFA = 0L;
  Long TARIFA_RESGATE_INMAIS = 350L;
  Long TARIFA_TRANSFERENCIA_CONTAS = 350L;
  Long TARIFA_RESGATE_INMAIS_CELULAR = 0L;
  Long TARIFA_RESGATE_INMAIS_VOUCHER = 0L;
  Long TARIFA_RESGATE_CARTAO = 0L;
  Integer ID_TARIFA_RESGATE_CARTAO = 748;

  /* ~ Prefixos de RRN ~ */

  String PREFIXO_RRN = "USR";
  String PREFIXO_RRN_PORTADOR = "PRT";
  String PREFIXO_RRN_CARGA_AUTO = "CAU";

  /* ~ Layer ~ */

  String LAYER_DEBITO_PLANO_SAUDE = "986";
  String LAYER_PAGAMENTO_PLANO_SAUDE = "368";

  /* ~ Tipos de Pontos ~ */

  Integer TIPO_PONTO_ACUMULO = 1;
  Integer TIPO_PONTO_BONIFICACAO = 2;
  Integer TIPO_PONTO_AJUTES = 3;
  Integer TIPO_PONTO_TRANSFERENCIA = 4;

  /* ~ Tipo de Pedido ~ */

  Integer TIPO_PEDIDO_NORMAL = 0;
  Integer TIPO_PEDIDO_ESPECIAL = 1;
  Integer TIPO_PEDIDO_VOUCHER_PAPEL = 2;

  /* ~ Motivos de Cobrança ~ */

  Integer MOTIVO_COBRANCA_20 = 20;
  Integer MOTIVO_COBRANCA_30 = 30;
  Integer MOTIVO_COBRANCA_32 = 32;
  Integer MOTIVO_COBRANCA_33 = 33;

  /* *~*~*~*~*~*~*~* */
  /* Status Diversos */
  /* *~*~*~*~*~*~*~* */

  /* ~ Status Conta/Cartão ~ */

  Integer TIPO_STATUS_BLOQUEIO_ORIGEM = 0;
  Integer TIPO_STATUS_DESBLOQUEADO = 1;
  Integer TIPO_STATUS_PENDENTE = 2;
  Integer TIPO_STATUS_SUSPENSO_FIM_PROGRAMA = 4;
  Integer TIPO_STATUS_BLOQUEIO_TEMPORARIO = 5;
  Integer TIPO_STATUS_CARTAO_VIRTUAL_UTILIZADO = 6;
  Integer TIPO_STATUS_AUTORIZADO_TRANSACIONAR_PRESENCIAL = 7;
  Integer TIPO_STATUS_BENEFICIARIO_HABILITADO = 8;
  Integer TIPO_STATUS_INATIVADO_PELA_EMPRESA = 9;
  Integer TIPO_STATUS_REPOSTO = 10;
  Integer TIPO_STATUS_AGUARDANDO_APROVACAO = 20;
  Integer TIPO_STATUS_CANCELADO_PELO_USUARIO = 30;
  Integer TIPO_STATUS_CANCELADO_PELA_INSTITUICAO = 31;
  Integer TIPO_STATUS_CANCELADO_POR_PERDA = 32;
  Integer TIPO_STATUS_CANCELADO_POR_ROUBO = 33;
  Integer TIPO_STATUS_CANCELADO_POR_FRAUDE = 34;
  Integer TIPO_STATUS_CANCELADO_POR_COMPROMETIMENTO = 35;
  Integer TIPO_STATUS_CANCELADO_PELA_EMPRESA = 36;
  Integer TIPO_STATUS_CANCELADO_POR_INATIVIDADE = 37;
  Integer TIPO_STATUS_CANCELADO_PARA_EXPIRACAO = 38;
  Integer TIPO_STATUS_VENCIDO = 40;
  Integer TIPO_STATUS_CONTA_INADIMPLENTE = 50;
  Integer TIPO_STATUS_CONTA_CANCELADA_CL = 51;
  Integer TIPO_STATUS_CANCELADO_CARTAO_PRE_PAGO = 52;
  Integer TIPO_STATUS_CANCELADO_DEFINITIVO_MIGRADO = 53;
  Integer TIPO_STATUS_CANCELADO_DEIXADO_LOJA = 54;
  Integer TIPO_STATUS_CANCELADO_DESATIVADO = 55;
  Integer TIPO_STATUS_CANCELADO_DEV_CORREIO = 56;
  Integer TIPO_STATUS_CANCELADO_EMBOSSING = 57;
  Integer TIPO_STATUS_CANCELADO_EXTRAVIADO = 58;
  Integer TIPO_STATUS_CANCELADO_POR_FALECIMENTO = 59;
  Integer TIPO_STATUS_CANCELADO_SUSP_FALSO = 60;
  Integer TIPO_STATUS_CANCELADO_TARJA = 61;
  Integer TIPO_STATUS_PESSOA_FISICA_BLACK_LIST = 62;
  Integer TIPO_STATUS_ACORDO_CRELIQ = 63;
  Integer TIPO_STATUS_ACORDO_PERDA = 64;
  Integer TIPO_STATUS_BLOQUEADA_CADASTRO_INCORRETO = 65;
  Integer TIPO_STATUS_BLOQUEADA_INADIMPLENCIA = 66;
  Integer TIPO_STATUS_CANCELAMENTO_MANUAL = 67;
  Integer TIPO_STATUS_CANCELADO_CRELIQ = 68;
  Integer TIPO_STATUS_LIBERADO = 69;
  Integer TIPO_STATUS_CANCELADO_PERDA = 70;
  Integer TIPO_STATUS_CANCELADO_DANIFICADO = 71;
  Integer TIPO_STATUS_ACORDO_CRELIQ_CONCLUIDO = 72;
  Integer TIPO_STATUS_CANCELADO_ACORDO_PERDA = 73;
  Integer TIPO_STATUS_QUEBRA_ACORDO_CRELIQ = 74;
  Integer TIPO_STATUS_CANCELADO_PERDA_QUITADA = 75;
  Integer TIPO_STATUS_EM_ANALISE = 81;
  Integer TIPO_STATUS_CANCELADO_A_PEDIDO_INST = 89;
  Integer TIPO_STATUS_CANCELADO_INCONSISTENCIA_PROC = 99;

  /* ~ Grupo Status ~ */

  Integer GRUPO_STATUS_BLOQUEIO_ORIGEM = 0;
  Integer GRUPO_STATUS_ATIVO = 1;
  Integer GRUPO_STATUS_BLOQUEIO_TEMP = 5;
  Integer GRUPO_STATUS_CANCELADO = 9;
  Integer GRUPO_STATUS_CANCELADO_PERMITINDO_AJUSTES = 10;
  Integer CONTA_ATIVA = 1;

  /* ~ Status Parceiro Acumulo ~ */

  Integer STATUS_PARCEIRO_ACUMULO_ATIVO = 1;
  Integer STATUS_PARCEIRO_ACUMULO_INATIVO = 9;
  Integer STATUS_PARCEIRO_RESGATE_ATIVO = 1;
  Integer STATUS_PARCEIRO_RESGATE_INATIVO = 9;

  /* ~ Status Solicitação ~ */

  Integer STATUS_SOLICITACAO_LIMITE_PENDENTE = 0;
  Integer STATUS_SOLICITACAO_LIMITE_APROVADO = 1;
  Integer STATUS_SOLICITACAO_LIMITE_NEGADO = 2;

  /* ~ Status Voucher ~ */

  Integer STATUS_LOTE_VOUCHER_PAPEL_SALVO = 0;
  Integer STATUS_LOTE_VOUCHER_PAPEL_NAO_IMPRESSO = 1;
  Integer STATUS_LOTE_VOUCHER_PAPEL_IMPRESSO = 2;
  Integer STATUS_LOTE_VOUCHER_PAPEL_REIMPRESSO = 3;
  Integer STATUS_LOTE_VOUCHER_PAPEL_TODOS = 4;

  /* ~ Status Troca Vencimento ~ */

  Integer STATUS_TROCA_VCTO_SOLICITADO = 1;
  Integer STATUS_TROCA_VCTO_PROCESSADO = 2;
  Integer STATUS_TROCA_VCTO_CANCELADO = 9;

  /* ~ Status Bonificação ~ */

  Integer STATUS_BONIFICACAO_ATIVO = 1;
  Integer STATUS_BONIFICACAO_INATIVO = 9;

  /* ~ Status Parceria Comercial ~ */
  Integer STATUS_PARCERIA_ATIVO = 1;
  Integer STATUS_PARCERIA_INATIVO = 0;

  /* ~ Status Parceria Comercial ~ */
  Integer STATUS_MODALIDADE_COMERCIAL_ATIVO = 1;
  Integer STATUS_MODALIDADE_COMERCIAL_INATIVO = 0;

  /* ~ Status Contrato Comercial ~ */
  Integer STATUS_CONTRATO_COMERCIAL_ATIVO = 1;
  Integer STATUS_CONTRATO_COMERCIAL_INATIVO = 0;

  /* ~ Evios ~ */

  Integer ENVIO_SUSPENSO_FATURA_IMPRESSA_1 = 1;
  Integer ENVIO_ATIVO_FATURA_IMPRESSA_0 = 0;

  Integer ENVIO_SUSPENSO_FATURA_EMAIL_1 = 1;
  Integer ENVIO_ATIVO_FATURA_EMAIL_0 = 0;

  /* ~ Códigos PIX ~ */

  String COD_PIX_CPF = "1";
  String COD_PIX_CNPJ = "2";
  String COD_PIX_TELEFONE = "3";
  String COD_PIX_EMAIL = "4";
  String COD_PIX_ALEATORIA = "5";
  String COD_PIX_TELEFONE_PREFIX = "+55";

  /* *~*~*~*~*~*~* */
  /* Strings fixas */
  /* *~*~*~*~*~*~* */

  /* ~ Pré-cadastro ~ */

  String PARTICIPANTE_PRE_CADASTRADO = "PARTICIPANTE PRÉ CADASTRADO";

  /* ~ Mensagens de Retorno ~ */

  String MSG_BACK_END = "msg";

  String STATUS_SUCESSO = "SUCESSO";
  String STATUS_ERRO = "ERRO";
  String MSG_ERRO_PADRAO = "Erro no servidor. Tente novamente mais tarde.";

  String MSG_ERRO_TIMEOUT_MAILGUN = "Timeout Mailgun.";
  String MSG_ERRO_TIMEOUT_RENDIMENTO = "Timeout Rendimento.";
  String MSG_ERRO_TIMEOUT_PRONTO_PAGUEI = "Timeout Pronto Paguei.";
  String MSG_NAO_ENCONTRADA_PRONTO_PAGUEI =
      "Erro ao buscar informações da sua consulta. Tente novamente mais tarde.";

  /* ~ Tipos de Conta ~ */

  String DESC_CONTA_CORRENTE = "CC";
  String DESC_CONTA_POUPANCA = "CP";

  /* ~ Informações BRB ~ */

  String NOME_CARTAO_BRB_S_A = "Cartão BRB S.A.";
  String CNPJ_CARTAO_BRB_S_A = "01.984.199/0001-00";

  /* ~ Informações Cabal ~ */

  String NOME_CABAL_BRASIL_LTDA = "CABAL BRASIL LTDA.";
  String CNPJ_CABAL_BRASIL_LTDA = "03.766.873/0001-06";

  /* ~ Informações ELO ~ */

  String NOME_ELO_SEVICOS_S_A = "Elo Serviços S.A.";
  String CNPJ_ELO_SEVICOS_S_A = "09.227.084/0001-75";

  /* ~ Info Pontos ~ */

  String MENSAGEM_NAO_EXPIRA = "NÃO EXPIRA";
  String PONTOS_NAO_EXPIRA = "2800-01-01";

  /* *~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~* */
  /* ~ Parceiros Externos - URL/URIs & Credentials ~ */
  /* *~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~*~* */

  String URL_RENDIMENTO_EVOTITULOS = "/baas-evotitulos";
  String URL_RENDIMENTO_V1_PAGAMENTOS_TED = "/pagamentosIB/api/v1/ContasCorrentes";
  String URL_RENDIMENTO_V2_PAGAMENTOS_TED = "/pagamentosIB/api/v2/ContasCorrentes";
  String URL_RENDIMENTO_V1_PAGAMENTO_TITULO = "/pagamentosIB/api/v1/pagamentos/";
  String URL_RENDIMENTO_V1_ESTORNO = "/pagamentosIB/api/v1/Estorno/";
  String URL_RENDIMENTO_V3_PAGAMENTOS_TED = "/pagamentosIB/api/v3/ContasCorrentes/";
  String URL_RENDIMENTO_V5_PAGAMENTOS_TED = "/pagamentosIB/api/v5/ContasCorrentes/";
  String URL_RENDIMENTO_V7_PAGAMENTOS = "/pagamentosIB/api/v7/ContasCorrentes/";
  String URL_RENDIMENTO_CONSULTA_TITULO = "/pagamentos/consultaDadosBoleto";
  String URL_RENDIMENTO_NOVO_PAGAMENTO_TITULO = "/pagamentos/titulo";
  String URI_RENDIMENTO_INCLUIR_TED = "/Transacoes/IncluirTransferencia";
  String URI_RENDIMENTO_CONSULTA_TITULO = "/Transacoes/ConsultarDadosDoBoleto";
  String URI_RENDIMENTO_CONTA_CONSUMO = "/contaconsumo";
  String URI_RENDIMENTO_TRIBUTOS = "/tributos";
  String URI_RENDIMENTO_ESTORNO_TITULO = "/EstornoPagamento/IncluirEstornoPagamento";
  String URI_RENDIMENTO_CONSULTA_STATUS_TITULO = "/pagamentos/transacoes?transacaoId=";
  String URI_RENDIMENTO_CONSULTA_STATUS_TED = "/Transacoes/ObterPorId?transacaoId=";

  /* ~ Demais ~ */

  String PGTO_CTA = "PGTO_CTA";
  String LST_FAV = "LST_FAV";
  String TKN_REDEF_SENHA = "TKN_REDEF_SENHA";

  // tipo meio de pagamento
  Integer TIPO_DEBITO_EM_CONTA = 2;
  String BOLETO_BRADESCO = "BOLETO_BRADESCO";
  String BOLETO_RENDIMENTO = "BOLETO_RENDIMENTO";
  String CODIGO_BANCO_RENDIMENTO = "633";
  String CODIGO_BANCO_BANESE = "047";

  Integer QUANTITY_VALIDATIONS_OCR = 5;
  Integer DEFAULT_BUFFER_SIZE = 8192;

  String BASE_URL_REPORT_RAKUTEN = "https://reportws.linksynergy.com/";
  String URI_REPORT_RAKUTEN = "downloadreport.php?token={tokenRakuten}&reportid=13";

  /* *~*~*~*~*~*~*~*~*~*~*~* */
  /* ~ Ainda Sem Categoria ~ */
  /* *~*~*~*~*~*~*~*~*~*~*~* */

  Integer FOI_UTILIZADO = 1;
  int SIM_ISS_RETIDO = 1;
  int NAO_ISS_RETIDO = 2;

  Integer PORTADOR_VINCULADO = 1;
  Integer PORTADOR_REJEITADO = 2;
  Integer PORTADOR_DOCUMENTO_INVALIDO = 3;
  Integer ATIVAR_BLOQUEIO = 1;
  Integer DESATIVAR_BLOQUEIO = 0;
  Integer CONTA_CANCELADO_PELA_EMPRESA = 9;
  Integer CONTA_DESBLOQUEADA = 1;

  Integer USUARIO_TIPO_INSTITUICAO = 0;
  Integer USUARIO_DE_ESTABELECIMENTO = 1;
  Integer USUARIO_TIPO_B2B = 1;

  Integer COD_PEDIDO_CARGA_GENERICO = 0;

  Integer ORIGEM_CADASTRO_MANUAL = 1;
  Integer ORIGEM_CADASTRO_VIA_ARQUIVO = 2;
  Integer ORIGEM_PRE_CADASTRO_VIA_PROCESSO = 3;
  Integer TIPO_B2B_GENERICO = 0;
  Integer TIPO_B2B_INFINANCAS = 1;

  Integer COD_BANCO_DO_BRASIL = 1;
  String COD_BANCO_CAIXA_ECONOMICA_FEDERAL = "104";

  Integer STATUS_FAIXA_BONIFICACAO_ATIVO = 1;
  Integer STATUS_FAIXA_BONIFICACAO_INATIVO = 9;

  Integer SEQ_PADRAO_TRANSFER_OUTRO_PROGRAMA = -2;

  Integer ID_PROGRAMA_FIDELIDADE_IN_MAIS = 2;

  Integer ID_PROGRAMA_FIDELIDADE_JOYPOINTS = 1;
  static final String PARAMETRO_DEFINICAO_USUARIO_CAF = "usuario.api.caf";
  static final String PARAMETRO_DEFINICAO_SENHA_CAF = "senha.api.caf";

  Integer RESGATE_CONTA_BANCARIA = 1;
  Integer TRANSFERENCIA_OUTRO_PROGRAMA = 2;
  Integer RESGATE_CREDITO_CELULAR = 3;

  Integer TIPO_PAGAMENTO_ANTECIPADO = 0;
  Integer TIPO_PAGAMENTO_A_PRAZO = 1;

  Integer TIPO_BONIFICACAO_FLAT = 1;
  Integer TIPO_BONIFICACAO_PERCENTUAL = 2;

  Long INTERFACE_INSTITUICAO_TITULO_CAPITALIZACAO = 27L;
  Long INTERFACE_INSTITUICAO_TITULO_CAPITALIZACAO_DADOS_PAGAMENTO = 28L;

  Boolean PONTO_RECEBIDO_JA_PROCESSADO = Boolean.TRUE;
  Boolean PONTO_RECEBIDO_PARA_PROCESSAR = Boolean.FALSE;

  int ID_PARCEIRO_EASYLIVE = 0;
  int ID_PARCEIRO_ITLOG = 1;
  int ID_PARCEIRO_CNOVA = 2;

  Integer PRE_LANCAMENTO_VOUCHER_PAPEL_STATUS_NAO_IMPRESSO = 0;
  Integer PRE_LANCAMENTO_VOUCHER_PAPEL_STATUS_IMPRESSO = 1;
  Integer PRE_LANCAMENTO_VOUCHER_PAPEL_SITUACAO_VERIFICADO = 1;
  Integer PRE_LANCAMENTO_VOUCHER_PAPEL_SITUACAO_PROCESSADO = 2;
  Integer LOG_VOUCHER_PAPEL_IMPRESSO = 0;
  Integer LOG_VOUCHER_PAPEL_REIMPRESSO = 1;
  Integer LOG_VOUCHER_PAPEL_PRIMEIRA_VERSAO = 1;

  Double MAXIMO_TRANSFERIVEL_BRB = new Double(5000);

  String COD_EMPRESA_PROVISORIO = "3010357";
  String COD_CONVENIO_0202_CONFORME_LAYOUT = "0202";
  String COD_EXTERNO_CONVENIO_CABAL = "0302";

  String GOOGLE_MAPS_API = "https://maps.googleapis.com/maps/api/";
  String GOOGLE_MAPS_API_KEY = "AIzaSyB0F4zQ8fcUgbfeLl9a_rSEXHuXV7i2xh0";

  String API_TOKEN_CAF =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2NDFjOGU3NmQ3OGI1ZjAwMDg0ZTZkNjQiLCJpYXQiOjE2Nzk1OTMwNzh9.l5KSPrMZKZdZ2QpRKaF1tkt3QMq4O01TQn424Dw9QP4";

  int N_1 = 1;
  int N_2 = 2;
  int N_3 = 3;
  int N_4 = 4;
  int N_5 = 5;
  int N_6 = 6;

  int CONNECTION_TIMEOUT_REST_TEMPLATE_INTEGRACAO_VOXAGE = 15000;
  int CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO = 30000;

  int INSTITUICAO_BLOQUEADA = 9;
}
