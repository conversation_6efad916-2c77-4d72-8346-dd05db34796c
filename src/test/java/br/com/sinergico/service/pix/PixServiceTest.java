package br.com.sinergico.service.pix;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.pix.ContaTransacionalMovimento;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.suporte.MoedaConta;
import br.com.json.bean.enums.pix.StatusValidacaoContaEnum;
import br.com.json.bean.enums.pix.TipoMovimentoEnum;
import br.com.json.bean.pix.request.webhook.MovimentoForm;
import br.com.json.bean.pix.request.webhook.ValidaContaRecebedorForm;
import br.com.json.bean.pix.response.webhook.MovimentoResponse;
import br.com.json.bean.pix.response.webhook.ValidaContaResponse;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.service.cadastral.B2bFaturaPixService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.ProdutoTransacaoService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.util.Constantes;
import br.com.util.MockUtil;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class PixServiceTest {

  @InjectMocks private PixService pixService;

  @Mock private ContaPagamentoService contaPagamentoService;

  @Mock private CredencialService credencialService;

  @Mock private InstituicaoPixService instituicaoPixService;

  @Mock private LancamentoService lancamentoService;

  @Mock private ContaTransacionalMovimentoService contaTransacionalMovimentoService;

  @Mock private ContaTransacionalChavesService contaTransacionalChavesService;

  @Mock private ProdutoTransacaoService produtoTransacaoService;

  @Mock private B2bFaturaPixService b2bFaturaPixService;

  @Mock private CotacaoPontosRepository cotacaoPontosRepository;

  @Mock private EventoService eventoService;

  private MovimentoForm movimentoCreditoForm;
  private MovimentoForm movimentoDebitoForm;

  @BeforeEach
  void setUp() {
    movimentoCreditoForm = new MovimentoForm();
    movimentoCreditoForm.setNroConta("12345");
    movimentoCreditoForm.setTipoMovimento(TipoMovimentoEnum.CREDITO);
    movimentoCreditoForm.setOrigemMovimento("CHAVE");
    movimentoCreditoForm.setEndToEnd("E2E12345");

    movimentoDebitoForm = new MovimentoForm();
    movimentoDebitoForm.setNroConta("12345");
    movimentoDebitoForm.setTipoMovimento(TipoMovimentoEnum.CREDITO);
    movimentoDebitoForm.setOrigemMovimento("CHAVE");
    movimentoDebitoForm.setEndToEnd("E2E12345");
  }

  @Test
  void deveRetornarContaNaoEncontradaQuandoMovimentoCreditoEContaPagamentoForNull() {
    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(1234L)).thenReturn(null);

    ResponseEntity<MovimentoResponse> response =
        pixService.tratarMovimentacaoDeConta(movimentoCreditoForm);

    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("60743", response.getBody().getErro());
    assertEquals("Conta não encontrada", response.getBody().getMensagemErro());
  }

  @Test
  void deveRetornarProdutoPixDesabilitadoQuandoMovimentoDebitoInstituicaoPixNaoEstiverHabilitada() {
    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    contaPagamento.setIdConta(1234L);
    contaPagamento.setIdStatusConta(1);
    contaPagamento.setIdProdutoInstituicao(10);

    InstituicaoPix instituicaoPix = new InstituicaoPix();
    instituicaoPix.setHabilitado(0);

    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(1234L))
        .thenReturn(contaPagamento);
    when(instituicaoPixService.buscarInstituicaoPixPorProduto(
            10, contaPagamento.getIdInstituicao()))
        .thenReturn(instituicaoPix);

    ResponseEntity<MovimentoResponse> response =
        pixService.tratarMovimentacaoDeConta(movimentoDebitoForm);

    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("60215", response.getBody().getErro());
    assertEquals(
        "Situação da conta não permite movimentação", response.getBody().getMensagemErro());
  }

  @Test
  void
      deveRetornarProdutoPixDesabilitadoQuandoMovimentoCreditoInstituicaoPixNaoEstiverHabilitada() {
    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    contaPagamento.setIdConta(1234L);
    contaPagamento.setIdStatusConta(1);
    contaPagamento.setIdProdutoInstituicao(10);

    InstituicaoPix instituicaoPix = new InstituicaoPix();
    instituicaoPix.setHabilitado(0);

    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(1234L))
        .thenReturn(contaPagamento);
    when(instituicaoPixService.buscarInstituicaoPixPorProduto(
            10, contaPagamento.getIdInstituicao()))
        .thenReturn(instituicaoPix);

    ResponseEntity<MovimentoResponse> response =
        pixService.tratarMovimentacaoDeConta(movimentoCreditoForm);

    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("60215", response.getBody().getErro());
    assertEquals(
        "Situação da conta não permite movimentação", response.getBody().getMensagemErro());
  }

  @Test
  void deveRetornarContaInvalidaQuandoStatusContaEstiverNaListaDeNaoPermitidos() {
    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    contaPagamento.setIdConta(1234L);
    contaPagamento.setIdStatusConta(Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO);
    contaPagamento.setIdProdutoInstituicao(10);

    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(1234L))
        .thenReturn(contaPagamento);

    ResponseEntity<MovimentoResponse> response =
        pixService.tratarMovimentacaoDeConta(movimentoCreditoForm);

    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("60605", response.getBody().getErro());
    assertEquals("A CONTA NÃO ESTÁ ATIVA", response.getBody().getMensagemErro());
  }

  @Test
  void deveRetornarEndToEndDuplicadoQuandoJaExistirMovimentoCreditoParaEsseEndToEnd() {
    movimentoCreditoForm.setEndToEnd("END_TO_END_DUP");

    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    contaPagamento.setIdConta(1234L);
    contaPagamento.setIdStatusConta(1);
    contaPagamento.setIdProdutoInstituicao(10);

    InstituicaoPix instituicaoPix = new InstituicaoPix();
    instituicaoPix.setHabilitado(Constantes.STATUS_PIX_HABILITADO);

    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(1234L))
        .thenReturn(contaPagamento);
    when(instituicaoPixService.buscarInstituicaoPixPorProduto(
            10, contaPagamento.getIdInstituicao()))
        .thenReturn(instituicaoPix);

    ContaTransacionalMovimento movimentoJaExistente = new ContaTransacionalMovimento();
    when(contaTransacionalMovimentoService.findFirstByEndToEndAndTipoMovimento(
            "END_TO_END_DUP", TipoMovimentoEnum.CREDITO))
        .thenReturn(Optional.of(movimentoJaExistente));

    ResponseEntity<MovimentoResponse> response =
        pixService.tratarMovimentacaoDeConta(movimentoCreditoForm);

    assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
    assertNotNull(response.getBody());
    assertEquals("AB00018", response.getBody().getErro());
    assertEquals("End To End duplicado", response.getBody().getMensagemErro());
  }

  @Test
  void deveRetornarSucesso_quandoNenhumDosErrosForAtingido() {
    MoedaConta moeda = new MoedaConta();
    moeda.setIdMoeda(986);
    moeda.setSimbolo("R$");
    moeda.setDescMoeda("REAL");

    InstituicaoPix instituicaoPix = new InstituicaoPix();
    instituicaoPix.setHabilitado(Constantes.STATUS_PIX_HABILITADO);

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        new ProdutoInstituicaoConfiguracao();
    produtoInstituicaoConfiguracao.setIdProdInstituicao(10);
    produtoInstituicaoConfiguracao.setMoeda(moeda);

    ProdutoInstituicao produtoInstituicao = new ProdutoInstituicao();
    produtoInstituicao.setIdProdInstituicao(10);
    produtoInstituicao.setProdutoInstituicaoConfiguracao(
        Collections.singletonList(produtoInstituicaoConfiguracao));

    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdInstituicao(MockUtil.getIdInstituicaoAleatoria());
    contaPagamento.setIdConta(1234L);
    contaPagamento.setIdProdutoInstituicao(10);
    contaPagamento.setIdStatusConta(1);
    contaPagamento.setProdutoInstituicao(produtoInstituicao);

    Credencial credencial = new Credencial();
    credencial.setTokenInterno("TOKEN_INTERNO");

    JcardResponse jcardResponse = new JcardResponse();
    jcardResponse.setSuccess(true);

    ContaTransacionalMovimento contaTransacionalMovimento = new ContaTransacionalMovimento();
    contaTransacionalMovimento.setId(1L);

    movimentoCreditoForm.setEndToEnd("END_TO_END_UNICO");
    movimentoCreditoForm.setCodInstituicao(contaPagamento.getIdInstituicao().toString());

    when(cotacaoPontosRepository.findByIdInstituicao(contaPagamento.getIdInstituicao()))
        .thenReturn(null);
    when(credencialService.buscarCredencialParaLancamentoManual(contaPagamento.getIdConta()))
        .thenReturn(credencial);
    when(contaPagamentoService.findOneByIdContaAndIdRelacionamento(1234L))
        .thenReturn(contaPagamento);
    when(instituicaoPixService.buscarInstituicaoPixPorProduto(
            10, contaPagamento.getIdInstituicao()))
        .thenReturn(instituicaoPix);
    when(lancamentoService.doLancamentoManual(
            any(), anyInt(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any(),
            any(), any()))
        .thenReturn(jcardResponse);
    when(contaTransacionalMovimentoService.criarMovimento(movimentoCreditoForm, "RRN_TESTE", null))
        .thenReturn(contaTransacionalMovimento);

    when(contaTransacionalMovimentoService.findFirstByEndToEndAndTipoMovimento(
            "END_TO_END_UNICO", TipoMovimentoEnum.CREDITO))
        .thenReturn(Optional.empty());

    when(lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR)).thenReturn("RRN_TESTE");

    ResponseEntity<MovimentoResponse> response =
        pixService.tratarMovimentacaoDeConta(movimentoCreditoForm);

    assertNotNull(response.getBody());
    assertNotEquals(
        HttpStatus.BAD_REQUEST,
        response.getStatusCode(),
        "Não deve retornar erro pois não caiu nos cenários alterados.");
  }

  @Test
  void deveRetornarChaveNaoPertenceConta_quandoChaveNaoAssociadaAContaInformada() {
    Integer idInstituicao = MockUtil.getIdInstituicaoAleatoria();
    Integer idProduto = MockUtil.getIdProdutoInstituicaoZeroUm(idInstituicao);
    Long idConta =
        MockUtil.getLongAleatorioEntre(MockUtil.getIdContaMin(), MockUtil.getIdContaMax());

    ValidaContaRecebedorForm form = new ValidaContaRecebedorForm();
    form.setCodAgencia(idInstituicao.toString());
    form.setNroConta(idConta.toString() + "0");
    form.setCpfCnpj("11122233344");
    form.setChaveEnderecamento("chave-inexistente-para-essa-conta");
    form.setTipoMovimento("CREDITO");

    ContaPagamento contaMock = new ContaPagamento();
    contaMock.setIdConta(idConta); //
    contaMock.setIdInstituicao(idInstituicao);
    contaMock.setIdStatusConta(1);
    contaMock.setIdProdutoInstituicao(idProduto);

    InstituicaoPix instituicaoPix = new InstituicaoPix();
    instituicaoPix.setIdInstituicao(idInstituicao);
    instituicaoPix.setIdProdutoInstituicao(idProduto);
    instituicaoPix.setHabilitado(1);

    List<Integer> codigosHabilitadosProduto = new ArrayList<>();
    codigosHabilitadosProduto.add(884);

    when(contaPagamentoService.findByIdInstituicaoAndAndIdContaAndDocumento(
            idInstituicao, idConta, "11122233344"))
        .thenReturn(Optional.of(contaMock));

    when(contaTransacionalChavesService.descobreChavePixAssociadaAConta(
            "chave-inexistente-para-essa-conta", idConta))
        .thenReturn(Optional.empty());

    when(instituicaoPixService.buscarInstituicaoPixPorProduto(idProduto, idInstituicao))
        .thenReturn(instituicaoPix);

    when(produtoTransacaoService.getCodTransacoesAdicionados(idProduto))
        .thenReturn(codigosHabilitadosProduto);

    ResponseEntity<ValidaContaResponse> responseEntity = pixService.validarContaRecebedor(form);

    assertNotNull(responseEntity);
    assertEquals(
        HttpStatus.OK,
        responseEntity.getStatusCode(),
        "Mesmo em erro de validação, o método devolve 200");

    ValidaContaResponse body = responseEntity.getBody();
    assertNotNull(body);

    assertEquals(StatusValidacaoContaEnum.CONTA_INVALIDA, body.getStatus());

    assertNotNull(body.getErros());
    assertEquals(1, body.getErros().size());
    assertEquals("60216", body.getErros().get(0).getCodErro());
    assertEquals("CHAVE NÃO PERTENCE A CONTA INFORMADA", body.getErros().get(0).getMensagemErro());
  }
}
