package br.com.sinergico.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SolicitacaoCancelamentoVO {
  private Long idExclusaoConta;
  private Long idConta;
  private String nomeCompleto;
  private BigDecimal saldoExclusao;
  private Integer statusSolicitacao;
  private LocalDateTime dtHrSolicitacao;
  private LocalDateTime dtHrAlteracao;
  private Integer idProdInstituicao;
  private String nomeProduto;
  private Boolean clienteRecusaExclusao;
  private Integer statusSolicitacaoExclusao;
  private Boolean consultaRestrita = Boolean.FALSE;

  public SolicitacaoCancelamentoVO(
      Long idExclusaoConta,
      Long idConta,
      String nomeCompleto,
      BigDecimal saldoExclusao,
      Integer statusSolicitacao,
      LocalDateTime dtHrSolicitacao,
      LocalDateTime dtHrAlteracao,
      Integer idProdInstituicao,
      String nomeProduto,
      Boolean clienteRecusaExclusao,
      Integer statusSolicitacaoExclusao) {
    this.idExclusaoConta = idExclusaoConta;
    this.idConta = idConta;
    this.nomeCompleto = nomeCompleto;
    this.saldoExclusao = saldoExclusao;
    this.statusSolicitacao = statusSolicitacao;
    this.dtHrSolicitacao = dtHrSolicitacao;
    this.dtHrAlteracao = dtHrAlteracao;
    this.idProdInstituicao = idProdInstituicao;
    this.nomeProduto = nomeProduto;
    this.clienteRecusaExclusao = clienteRecusaExclusao;
    this.statusSolicitacaoExclusao = statusSolicitacaoExclusao;
  }

  public SolicitacaoCancelamentoVO() {}
}
