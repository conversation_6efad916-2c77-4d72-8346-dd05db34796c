ambiente=${AMBIENTE}

# Connection database
spring.datasource.hikari.maximum-pool-size=50
spring.datasource.hikari.minimum-idle=50

#ISSUER URL
issuer.url=${ISSUER_URL}

#CRONs
executar.cron.envio.alerta.contagarantia=${EXECUTAR_CRON_ENVIO_ALERTA_CONTAGARANTIA}

#ORDENS DE PAGAMENTOS
executar.atualizar.ordens.pagamentos=${EXECUTAR_ATUALIZAR_ORDENS_PAGAMENTOS}

#BANCO DE DADOS
spring.datasource.url=${DB_URL}
spring.datasource.username=${DB_USERNAME_ISSUER}
spring.datasource.password=${DB_PASSWORD_ISSUER}

#EVENTO JCARD BASIC AUTH
evento.jcard.basic.username=${EVENTO_JCARD_BASIC_USER}
evento.jcard.basic.password=${EVENTO_JCARD_BASIC_PASS}

#VALLOO AUTENTICADOR
username.api.valloo.authenticator=${USERNAME_API_VALLOO_AUTHENTICATOR}
password.api.valloo.authenticator=${PASSWORD_API_VALLOO_AUTHENTICATOR}
url.valloo.autenticador=${URL_VALLOO_AUTENTICADOR}

#TRAVA SERVICOS
servidores.limpar.cache=${SERVIDORES_LIMPAR_CACHE}

#CAF
secret.key.caf=${SECRET_KEY_CAF}
usuario.api.caf=${USUARIO_API_CAF}
senha.api.caf=${SENHA_KEY_CAF}

#COBRANCA BOLETO
boletoregistrado.brbcard.url=${BOLETOREGISTRADO_BRBCARD_URL}
boletoregistrado.bradesco.url=${BOLETOREGISTRADO_BRADESCO_URL}
api.bradesco.url.v1.1=${API_BRADESCO_URL_V1_1}
boletoregistrado.bb.baseurl=${BOLETOREGISTRADO_BB_BASEURL}
boletoregistrado.bb.authurl=${BOLETOREGISTRADO_BB_AUTHURL}
boletoregistrado.bb.devappkey=${BOLETOREGISTRADO_BB_DEVAPPKEY}
boletoregistrado.bb.clientid=${BOLETOREGISTRADO_BB_CLIENTID}
boletoregistrado.bb.clientsecret=${BOLETOREGISTRADO_BB_CLIENTSECRET}
boletoregistrado.bb.gwappkey=${BOLETOREGISTRADO_BB_GWAPPKEY}
boletoregistrado.banese.baseurl=${BOLETOREGISTRADO_BANESE_BASEURL}

#PRONTO PAGUEI
service.pronto-paguei.url=${SERVICE_PRONTOPAGUEI_URL}
service.pronto-paguei.token=${SERVICE_PRONTOPAGUEI_TOKEN}

#ANTIFRAUDE
antifraude.basic.user=${ANTIFRAUDE_BASIC_USER}
antifraude.basic.pass=${ANTIFRAUDE_BASIC_PASS}

#RENDIMENTO
service.banco-rendimento.url=${SERVICE_BANCORENDIMENTO_URL}
service.banco-rendimento.auth.url=${SERVICE_BANCORENDIMENTO_AUTH_URL}

#URL DE TROCA DE SENHA
url.troca.senha=${URL_TROCA_SENHA}
url.troca.senha.b2b=${URL_TROCA_SENHA.B2B}
url.troca.senha.b2b.infinancas=${URL_TROCA_SENHA_B2B_INFINANCAS}
url.troca.senha.b2b.joypoints=${URL_TROCA_SENHA_B2B_JOYPOINTS}
url.troca.senha.acquirer.merchant=${URL_TROCA_SENHA_ACQUIRER_MERCHANT}

#JCARD REST API
jcard.url=${JCARD_URL}
jcard.header.consumerid=${JCARD_HEADER_CONSUMERID}

#TOTVS API
totvs.api.url=${TOTVS_API_URL}
totvs.api.header.token=${TOTVS_API_HEADER_TOKEN}

#ZENVIA ENVIO SMS
gateway.zenvia.auth.password=${GATEWAY_ZENVIA_AUTH_PASSWORD}
gateway.zenvia.auth.user=${GATEWAY_ZENVIA_AUTH_USER}
gateway.zenvia.permiteenviar=${GATEWAY_ZENVIA_PERMITEENVIAR}
token.zenvia.v2=${TOKEN_ZENVIA_V2}

#DIRETORIO SEGURANCA
issuer.dir.security.lmk=${ISSUER_DIR_SECURITY_LMK}
issuer.dir.security.cfg=${ISSUER_DIR_SECURITY_CFG}
issuer.ura.security.alias.101001=${ISSUER_URA_SECURITY_ALIAS_101001}
issuer.ura.security.alias.101201=${ISSUER_URA_SECURITY_ALIAS_101201}
issuer.ura.security.alias.102401=${ISSUER_URA_SECURITY_ALIAS_102401}
issuer.ura.security.alias.102601=${ISSUER_URA_SECURITY_ALIAS_102601}
issuer.ura.security.alias.100101=${ISSUER_URA_SECURITY_ALIAS_100101}

#PAYMENT PROCESS
payment.process.rest.url=${PAYMENT_PROCESS_REST_URL}

#NFSE URL
nfse.url=${NFSE_URL}
nfse.url.instituicao=${NFSE_URL_INSTITUICAO}

#ISSUER ACQUIRER
ip.acquirer=${IP_ACQUIRER}
port.acquirer=${PORT_ACQUIRER}
acquirer.rest.api.url=${ACQUIRER_REST_API_URL}

#MARKETPLACE
mktplace.url=${MKTPLACE_URL}

#URL EMAIL API
url.email.api=${URL_EMAIL_API}

#CRIPTOGRAFIA
cryptography.secret=${CRYPTOGRAPHY_SECRET}

#GATEWAY API
br.com.itspay.gateway.service.antifraudservice.merchantid=${BR_COM_ITSPAY_GATEWAY_SERVICE_ANTIFRAUDSERVICE_MERCHANTID}
br.com.itspay.gateway.service.antifraudservice.clientid=${BR_COM_ITSPAY_GATEWAY_SERVICE_ANTIFRAUDSERVICE_CLIENTID}
br.com.itspay.gateway.service.antifraudservice.clientsecret=${BR_COM_ITSPAY_GATEWAY_SERVICE_ANTIFRAUDSERVICE_CLIENTSECRET}
br.com.itspay.gateway.service.paymentsservice.id=${BR_COM.ITSPAY_GATEWAY_SERVICE_PAYMENTSSERVICE_ID}
br.com.itspay.gateway.service.paymentsservice.key=${BR_COM_ITSPAY_GATEWAY_SERVICE_PAYMENTSSERVICE_KEY}

#QRCODE
elo.qrcode.url=${ELO_QRCODE_URL}

#QRCODE VALLOO
elo.qrcode.keystore.path=${ELO_QRCODE_KEYSTORE_PATH}
elo.qrcode.keystore.password=${ELO_QRCODE_KEYSTORE_PASSWORD}
elo.qrcode.key.password=${ELO_QRCODE_KEY_PASSWORD}
elo.qrcode.truststore.path=${ELO_QRCODE_TRUSTSTORE_PATH}

#QRCODE BRB
elo.brbcard.qrcode.keystore.password=${ELO_BRBCARD_QRCODE_KEYSTORE_PASSWORD}
elo.brbcard.qrcode.key.password=${ELO_BRBCARD_QRCODE_KEY_PASSWORD}
elo.brbcard.qrcode.keystore.path=${ELO_BRBCARD_QRCODE_KEYSTORE_PATH}
elo.brbcard.qrcode.truststore.path=${ELO_BRBCARD_QRCODE_TRUSTSTORE_PATH}

#QRCODE MULVI
elo.mulvi.qrcode.keystore.password=${ELO_MULVI_QRCODE_KEYSTORE_PASSWORD}
elo.mulvi.qrcode.key.password=${ELO_MULVI_QRCODE_KEY_PASSWORD}
elo.mulvi.qrcode.keystore.path=${ELO_MULVI_QRCODE_KEYSTORE_PATH}
elo.mulvi.qrcode.truststore.path=${ELO_MULVI_QRCODE_TRUSTSTORE_PATH}

#INMAIS
url.ambiente.inmais=${URL_AMBIENTE_INMAIS}

#ELO VCN
elo.vcn.url=${ELO_VCN_URL}
elo.vcn.client.id=${ELO_VCN_CLIENT_ID}
elo.vcn.client.auth=${ELO_VCN_CLIENT_AUTH}
elo.vcn.user.key.kid=${ELO_VCN_USER_KEY_KID}
elo.vcn.user.key.x=${ELO_VCN_USER_KEY_X}
elo.vcn.user.key.y=${ELO_VCN_USER_KEY_Y}
elo.vcn.user.key.private=${ELO_VCN_USER_KEY_PRIVATE}
elo.vcn.user.username=${ELO_VCN_USER_USERNAME}
elo.vcn.user.password=${ELO_VCN_USER_PASSWORD}
elo.vcn.bank10.id=${ELO_VCN_BANK10_ID}

#SISTEMA
system.log.payload=${SYSTEM_LOG_PAYLOAD}

#URL SITES VALLLO
url.valloo.b2b.infinancas=${URL_VALLOO_B2B_INFINANCAS}
url.valloo.b2b.client=${URL_VALLOO_B2B_CLIENT}
url.valloo.issuer=${URL_VALLOO_ISSUER}
url.login.unico=${URL_LOGIN_UNICO}

#ENVIO EMAIL
service.mailgun.token=${SERVICE_MAILGUN_TOKEN}

#UNIDAS
site.unidas.url=${SITE_UNIDAS_URL}

#USUARIO DE MONITORACAO
monitoramento.basic.rest.user=${MONITORAMENTO_BASIC_REST_USER}
monitoramento.basic.rest.password=${MONITORAMENTO_BASIC_REST_PASSWORD}

#Propriedades do Redis
spring.session.store-type=redis
spring.redis.cluster.nodes=${SPRING_REDIS_CLUSTER_NODES}

#Celcoin
api.celcoin.clientid=${API_CELCOIN_CLIENTID}
api.celcoin.secretid=${API_CELCOIN_SECRETID}
api.celcoin.url=${API_CELCOIN_URL}
api.celcoin.keystore=${API_CELCOIN_KEYSTORE}
api.celcoin.keystore.password=${API_CELCOIN_KEYSTORE_PASSWORD}
api.celcoin.truststore=${API_CELCOIN_TRUSTSTORE}
api.celcoin.truststore.password=${API_CELCOIN_TRUSTSTORE_PASSWORD}

#Pix BRB
brb.pix.oauth2.url=${BRB_PIX_OAUTH2_URL}
brb.pix.oauth2.client-id=${BRB_PIX_OAUTH2_CLIENT_ID}
brb.pix.oauth2.client-secret=${BRB_PIX_OAUTH2_CLIENT_SECRET}
brb.pix.api.url=${BRB_PIX_API_URL}