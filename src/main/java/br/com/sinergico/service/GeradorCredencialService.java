package br.com.sinergico.service;

import static br.com.sinergico.service.cadastral.CredencialService.*;
import static br.com.sinergico.service.jcard.CardService.ACCOUNT_TYPE_CREDITO;
import static br.com.sinergico.service.jcard.CardService.ACCOUNT_TYPE_DEBITO;

import br.com.client.rest.jcard.json.bean.CardAddAccount;
import br.com.client.rest.jcard.json.bean.ChangePassCardReplacement;
import br.com.client.rest.jcard.json.bean.CreateCard;
import br.com.client.rest.jcard.json.bean.CreateCardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.CredencialConta;
import br.com.entity.cadastral.CredencialContaId;
import br.com.entity.cadastral.CredencialPreEmitida;
import br.com.entity.cadastral.PedidoCredenciaisPreEmitidas;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracaoCredito;
import br.com.entity.cadastral.ProdutoInstituidor;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.MoedaConta;
import br.com.entity.suporte.Plastico;
import br.com.exceptions.GenericRuntimeException;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.cadastral.GerarCredencialRequest;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.PadraoSeqCredencial;
import br.com.sinergico.repository.suporte.PlasticoRepository;
import br.com.sinergico.security.LegacyPasswordEncoder;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.CredencialContaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PedidoCredenciasPreEmitidasService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoCreditoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DVUtil;
import br.com.sinergico.util.DateUtil;
import com.google.common.base.Strings;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import javax.persistence.NoResultException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class GeradorCredencialService {

  private static final String YYYY_MM_DD = "yyyy-MM-dd";

  private static final String CREATED = "CREATED";

  private static final int VERSAO_UM = 1;

  private static final int TAM_DIG_VERIFICADOR = 1;

  private static final int SEQ_PADRAO = 1;

  private static final int MIN_DIG_BIN_ESTENDIDO_VIRTUAL = 6;

  private static final int MIN_DIG_BIN_ESTENDIDO = 6;

  private static final char CHAR_ZERO = '0';

  private static final String SHA_256 = "SHA-256";

  private static final int ATIVO = 1;

  private static final int TAM_MAX_PIN = 6;

  private static final Integer INATIVO = 0;
  private static final boolean HABILITADO = true;

  private static final Integer STATUS_ATIVO = 1;

  private static final Integer PRIMEIRA_EMISSAO = 0;

  private static final Integer REPOSICAO = 1;
  private static final Integer RENOVACAO = 2;
  private static final Integer DEBITO = 2;

  @Autowired private CredencialContaService credencialContaService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private ProdutoInstituicaoConfiguracaoCreditoService prodInstConfigCreditoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private PlasticoRepository plasticoRepo;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private CardService cardService;

  @Autowired private HierarquiaInstituicaoService instituicaoService;

  @Autowired private PedidoCredenciasPreEmitidasService pedidoCredencialService;

  @Autowired private TravaServicosService travaServicosService;

  @Transactional
  public CredencialGerada decideGerarCredencialGrupo(GerarCredencialRequest request) {

    List<Long> idPessoa = pessoaService.findIdPessoasByIdConta(request.getIdConta());
    if (!idPessoa.contains(request.getIdPessoa())) {
      throw new GenericServiceException(
          ConstantesErro.CAR_GERACAO_PESSOA_DIVERGENTE_CONTA.getMensagem());
    }

    Pessoa pessoa = pessoaService.findById(request.getIdPessoa());
    ContaPagamento conta = getContaByIdNotNull(request.getIdConta());

    ProdutoInstituicaoConfiguracao prodInstConf = getProdutoInstConfigNotNull(conta);

    if (prodInstConf.getIdGrupoProduto() != null) {
      Credencial credencialVirtualOuFisicaRecente =
          credencialService.findUltimaCredencialVirtualOuFisica(
              pessoa.getDocumento(),
              prodInstConf.getIdGrupoProduto(),
              conta.getIdInstituicao(),
              conta.getIdProcessadora(),
              conta.getIdRegional(),
              conta.getIdFilial(),
              conta.getIdPontoDeRelacionamento(),
              Boolean.TRUE.equals(request.getVirtual()) ? 1 : 0);

      if (credencialVirtualOuFisicaRecente == null) {
        CredencialGerada credencialGerada = gerarCredencial(request);
        credencialContaService.vincularCredencialContasMesmoGrupo(
            credencialGerada.getCredencial(), pessoa, conta);
        return credencialGerada;
      }

      if (Objects.equals(credencialVirtualOuFisicaRecente.getStatus(), DESBLOQUEADO)
          || Objects.equals(credencialVirtualOuFisicaRecente.getStatus(), BLOQUEIO_DE_ORIGEM)
          || Objects.equals(credencialVirtualOuFisicaRecente.getStatus(), BLOQUEIO_PREVENTIVO)) {
        throw new GenericServiceException(
            ConstantesErro.CAR_GERACAO_CARTAO_VIRTUAL_OU_FISICO_JA_EXISTE.format(
                Boolean.TRUE.equals(request.getVirtual()) ? "virtual" : "físico"));
      }

      CredencialGerada credencialGerada = gerarCredencial(request);
      credencialContaService.vincularCredencialContaSegundaVia(
          credencialVirtualOuFisicaRecente.getIdCredencial(), credencialGerada.getCredencial());
      return credencialGerada;

    } else {
      return gerarCredencial(request);
    }
  }

  @Transactional
  public CredencialGerada validarGeracaoCredencial(
      GerarCredencialRequest gerarCredencialRequest, SecurityUser user) {
    ContaPagamento conta = getContaByIdNotNull(gerarCredencialRequest.getIdConta());
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, conta);
    validarTravaServicoCredencial(gerarCredencialRequest, user.getIdInstituicao());
    return this.decideGerarCredencialGrupo(gerarCredencialRequest);
  }

  @Transactional
  public CredencialGerada validarGeracaoCredencial(
      GerarCredencialRequest gerarCredencialRequest, SecurityUserPortador userPortador) {
    validarTravaServicoCredencial(gerarCredencialRequest, userPortador.getIdInstituicao());
    validaRequestPortador(
        gerarCredencialRequest, userPortador.getCpf(), userPortador.getDocumentoAcesso());
    return this.decideGerarCredencialGrupo(gerarCredencialRequest);
  }

  @Transactional
  public CredencialGerada validarGeracaoCredencial(
      GerarCredencialRequest gerarCredencialRequest, SecurityUserCorporativo userCorporativo) {
    validarTravaServicoCredencial(gerarCredencialRequest, userCorporativo.getIdInstituicao());
    validaRequestPortador(gerarCredencialRequest, userCorporativo.getUsername(), null);
    return this.decideGerarCredencialGrupo(gerarCredencialRequest);
  }

  public void validarTravaServicoCredencial(
      GerarCredencialRequest gerarCredencialRequest, Integer idInstituicao) {
    if (gerarCredencialRequest.getVirtual()) {
      travaServicosService.travaServicos(idInstituicao, Servicos.GERACAO_CREDENCIAL_VIRTUAL);
    } else {
      travaServicosService.travaServicos(idInstituicao, Servicos.GERACAO_CREDENCIAL_FISICO);
    }
  }

  private void validaRequestPortador(
      GerarCredencialRequest request, String documento, String documentoRepresentante) {
    boolean existeDocumentoPessoasContaFisicaJuridica = false;
    if (documentoRepresentante != null) {
      existeDocumentoPessoasContaFisicaJuridica =
          pessoaService.existeDocumentoPessoasConta(documentoRepresentante, request.getIdConta());
    }
    if (!existeDocumentoPessoasContaFisicaJuridica) {
      existeDocumentoPessoasContaFisicaJuridica =
          pessoaService.existeDocumentoPessoasConta(documento, request.getIdConta());
    }
    if (!existeDocumentoPessoasContaFisicaJuridica) {
      throw new GenericServiceException(ConstantesErro.CAR_GERACAO_IMPEDIDA_PORTADOR.getMensagem());
    }
  }

  @Transactional
  public CredencialGerada gerarCredencialAdicional(
      GerarCredencialRequest request, BigDecimal limitePrioritario) {
    return gerarCredencial(request, false, limitePrioritario);
  }

  @Transactional
  public CredencialGerada gerarCredencial(GerarCredencialRequest request) {
    return gerarCredencial(request, false);
  }

  @Transactional
  public CredencialGerada gerarCredencial(
      GerarCredencialRequest request, boolean desbloqueio, BigDecimal limitePrioritario) {

    Pessoa pessoa = getPessoaByIdNotNull(request.getIdPessoa());

    ContaPagamento conta = getContaByIdNotNull(request.getIdConta());

    ProdutoInstituicaoConfiguracao prodInstConf = getProdutoInstConfigNotNull(conta);

    List<Plastico> plastico = getPlasticoNotNull(conta);

    Credencial credencial;

    credencial = getCredencialPreenchida(request, pessoa, conta, prodInstConf, plastico.get(0));

    createCard(credencial, request.getAdicional());

    if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      Credencial credencialExistente =
          credencialService.getCredencialFisicaMaisRecente(conta.getIdConta());
      if (credencialExistente != null) {
        atribuirSenhaCartaoExistenteAoNovo(credencialExistente, credencial);
      }
    }

    atualizarInformacoesCartaoAdicional(request, prodInstConf, credencial, limitePrioritario);

    credencial = atualizarInfo(credencial);

    credencialContaService.vincularCredencialConta(conta, credencial, prodInstConf, pessoa);

    if (desbloqueio) {
      contaPagamentoService.desbloquearCredencialViaPortador(credencial.getIdCredencial());
    } else if (credencial.getVirtual()
        && Constantes.TIPO_STATUS_DESBLOQUEADO.equals(credencial.getIdStatusV2())
        && request.getOnboard() == null) {
      boolean deveEnviarSenhaPorSMS = prodInstConf.getIdGrupoProduto() == null;
      boolean isInstituicaoQueNaoRecebeSenhaPorSMS =
          Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_ENTREPAY.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_WIZ.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_QISTA.equals(conta.getIdInstituicao());

      boolean isInstituicaoQueRecebeSenhaPorSMSDependendoDaConfiguracaoDeGrupos =
          Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(conta.getIdInstituicao())
              || Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                  conta.getIdInstituicao());

      if (!isInstituicaoQueNaoRecebeSenhaPorSMS
          && (!isInstituicaoQueRecebeSenhaPorSMSDependendoDaConfiguracaoDeGrupos
              || deveEnviarSenhaPorSMS)) {
        credencialService.enviarSenhaPorSms(credencial);
      }
    }

    return new CredencialGerada(credencial);
  }

  /**
   * Corpo do metodo adaptado com a mesma logica para: gerarCredencial(GerarCredencialRequest
   * request, boolean desbloqueio, BigDecimal limitePrioritario)
   */
  @Transactional
  public CredencialGerada gerarCredencial(GerarCredencialRequest request, boolean desbloqueio) {
    return gerarCredencial(request, desbloqueio, null);
  }

  @Transactional
  public CredencialGerada gerarCredencialPrimeiraCompra(
      GerarCredencialRequest request, SecurityUser user) {

    Pessoa pessoa = getPessoaByIdNotNull(request.getIdPessoa());

    ContaPagamento conta = getContaByIdNotNull(request.getIdConta());

    ProdutoInstituicaoConfiguracao prodInstConf = getProdutoInstConfigNotNull(conta);

    ProdutoInstituicaoConfiguracaoCredito prodInstConfigCredito =
        prodInstConfigCreditoService.getConfiguracaoProdutoCredito(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    List<Plastico> plastico = getPlasticoNotNull(conta);

    Credencial credencial =
        getCredencialPreenchidaPrimeiraCompra(
            request, pessoa, conta, prodInstConf, plastico.get(0), user, prodInstConfigCredito);

    createCard(credencial, false); // false pois somente o titular possui conta pagamento

    if (produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      Credencial credencialExistente =
          credencialService.getCredencialFisicaMaisRecente(conta.getIdConta());
      if (credencialExistente != null) {
        atribuirSenhaCartaoExistenteAoNovo(credencialExistente, credencial);
      }
    }

    atualizarInformacoesCartaoAdicional(request, prodInstConf, credencial, null);

    atualizarInfoPrimeiraCompra(credencial); // aqui salva a credencial virtual

    credencialContaService.vincularCredencialConta(conta, credencial, prodInstConf, pessoa);

    return new CredencialGerada(credencial);
  }

  @Transactional
  public CredencialGerada gerarPreEmitida(
      GerarCredencialRequest request,
      CredencialPreEmitida credPreEmi,
      Pessoa pessoa,
      ContaPagamento conta) {
    ProdutoInstituicaoConfiguracao prodInstConf = getProdutoInstConfigNotNull(conta);

    List<Plastico> plastico = getPlasticoNotNull(conta);

    Credencial credencial =
        getCredencialPreEmitidaPreenchida(
            request, pessoa, conta, prodInstConf, plastico.get(0), credPreEmi);

    // não precisa criar cartao no jcard
    addAccount(credencial, prodInstConf, pessoa, conta);
    credencial = atualizarInfo(credencial);

    contaPagamentoService.desbloquearCredencialViaPortador(credencial.getIdCredencial());

    credencial = credencialService.save(credencial);

    CredencialConta credencialConta = new CredencialConta();
    CredencialContaId credencialContaId =
        new CredencialContaId(credencial.getIdCredencial(), conta.getIdConta());
    credencialConta.setCredencialContaId(credencialContaId);
    credencialConta.setDtHrInclusao(LocalDateTime.now());

    credencialContaService.save(credencialConta);

    return new CredencialGerada(credencial);
  }

  @Transactional
  public CredencialGerada gerarCredencialPreEmitida(
      GerarCredencialRequest request, CredencialPreEmitida credPreEmi) {

    Pessoa pessoa = getPessoaByIdNotNull(request.getIdPessoa());

    ContaPagamento conta = getContaByIdNotNull(request.getIdConta());

    ProdutoInstituicaoConfiguracao prodInstConf = getProdutoInstConfigNotNull(conta);

    List<Plastico> plastico = getPlasticoNotNull(conta);

    Credencial credencial =
        getCredencialPreEmitidaPreenchida(
            request, pessoa, conta, prodInstConf, plastico.get(0), credPreEmi);

    // não precisa criar cartao no jcard
    addAccount(credencial, prodInstConf, pessoa, conta);
    Credencial credencialSalva = atualizarInfoAndReturn(credencial);

    CredencialConta credencialConta = new CredencialConta();
    CredencialContaId credencialContaId =
        new CredencialContaId(credencialSalva.getIdCredencial(), conta.getIdConta());
    credencialConta.setCredencialContaId(credencialContaId);
    credencialConta.setDtHrInclusao(LocalDateTime.now());

    credencialContaService.save(credencialConta);

    return new CredencialGerada(credencial);
  }

  public void addAccount(
      Credencial c,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Pessoa pessoa,
      ContaPagamento conta) {

    ProdutoInstituidor produtoInstituidor = prodInstConf.getProdutoInstituidor();
    MoedaConta moeda = prodInstConf.getMoeda();

    HierarquiaInstituicao instituicao = null;
    if (conta.getHierarquiaPontoDeRelacionamento() != null
        && conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao() != null) {
      instituicao = conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao();
    }
    if (instituicao == null) {
      HierarquiaInstituicaoId id =
          new HierarquiaInstituicaoId(pessoa.getIdProcessadora(), pessoa.getIdInstituicao());
      instituicao = instituicaoService.findById(id);
    }

    String journal = instituicao.getJournal();
    String accountType = null;

    if (produtoInstituidor != null) {

      // verifico se o produto instituidor da conta tem a funcao de debito ou credito,
      // dependendo dessa funcao tem um campo especifico para pegar o cardHoldAccount
      Integer funcao =
          produtoInstituidor.getFuncao() == null ? DEBITO : produtoInstituidor.getFuncao();

      accountType = funcao.equals(DEBITO) ? ACCOUNT_TYPE_DEBITO : ACCOUNT_TYPE_CREDITO;
    }

    CardAddAccount cardAddAccount = new CardAddAccount();
    cardAddAccount.setAccountCode(conta.getIdAccountCode());
    cardAddAccount.setCurrency(moeda.getIdMoeda().toString());
    cardAddAccount.setJournal(journal);
    cardAddAccount.setAccountType(accountType);
    cardAddAccount.setToken(c.getTokenInterno());
    log.info("Esta sendo adicionado a entidade: " + cardAddAccount);
    addAccount(cardAddAccount);
  }

  private void addAccount(CardAddAccount cardAddAccount) {
    JcardResponse response = cardService.addAccount(cardAddAccount);
    if (response.getSuccess().equals(Boolean.FALSE)) {
      throw new GenericServiceException(
          "Ocorreu um erro ao executar cardAddAccount. " + response.getErrors());
    }
  }

  private Credencial getCredencialPreenchida(
      GerarCredencialRequest request,
      Pessoa pessoa,
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Plastico plastico) {

    // seto o plastico para que tenha o produtoInstituicaoConfiguracao
    // para criar uma credencial com um produto especifico no jcard
    Credencial c =
        montaEntidadeCredencial(new Credencial(), request, pessoa, conta, prodInstConf, plastico);

    if (Objects.isNull(pessoa.getNomeEmbossado())) {
      c.setNomeImpresso(Constantes.PARTICIPANTE_PRE_CADASTRADO);
    } else {
      c.setNomeImpresso(pessoa.getNomeEmbossado());
    }
    c.setPin(encodeSenhaSHA256(gerarSenha(prodInstConf.getTamanhoPin())));

    ContaPessoa contaPesssoa =
        contaPessoaService.findOneByIdPessoaAndIdConta(c.getIdPessoa(), c.getIdConta());

    if (Objects.nonNull(contaPesssoa)) {

      c.setTitularidade(contaPesssoa.getIdTitularidade());
      // verifico a versao da credencial
      Credencial credencial =
          credencialService.buscarCredencialMaisRecente(
              contaPesssoa.getIdConta(),
              contaPesssoa.getIdPessoa(),
              contaPesssoa.getIdTitularidade());

      if (Objects.isNull(credencial)) {
        c.setCsn(VERSAO_UM);
        c.setMotivoEmissao(PRIMEIRA_EMISSAO);
      } else {
        int versao = credencial.getCsn() == null ? VERSAO_UM : credencial.getCsn();
        c.setCsn(versao + VERSAO_UM);

        Credencial credencialFisica =
            credencialService.buscarCredencialMaisRecente(
                contaPesssoa.getIdConta(),
                contaPesssoa.getIdPessoa(),
                Boolean.FALSE,
                contaPesssoa.getIdTitularidade());
        c.setMotivoEmissao(
            Boolean.FALSE.equals(request.getVirtual()) && credencialFisica == null
                ? PRIMEIRA_EMISSAO
                : REPOSICAO);
      }

    } else {
      c.setMotivoEmissao(PRIMEIRA_EMISSAO);
      c.setCsn(VERSAO_UM);
    }

    if ((c.getMotivoEmissao().equals(REPOSICAO)
            && conta.getIdRelacionamento() != null
            && conta.getIdRelacionamento() == 1
            && prodInstConf.getProdutoInstituicao().getB2b().equals(Boolean.FALSE))
        || (request.getIntegracao() != null && request.getIntegracao())
        || Constantes.ID_PRODUCAO_INSTITUICAO_TRADE_SOLUTION.equals(conta.getIdInstituicao())) {
      c.setDtHrLiberacaoEmissao(LocalDateTime.now());
    } else {
      c.setDtHrLiberacaoEmissao(request.getDtHrLiberacaoEmissao());
    }

    if (request.getVirtual() != null && request.getVirtual()) {

      // verifico se a conta esta ativa para emissao de credencial virtual
      if (!Constantes.TIPO_STATUS_DESBLOQUEADO.equals(conta.getIdStatusV2())
          && c.getCsn() > VERSAO_UM) {
        throw new GenericServiceException(
            "A Conta precisa estar Desbloqueada para emissão de Cartão Virtual.");
      }
      if (request.getVirtualMesesValidade() == null || request.getVirtualMesesValidade() <= 0) {
        c.setDataValidade(
            calcularVencimento(
                prodInstConf.getMesesValidadeMaxVirtual() != null
                    ? prodInstConf.getMesesValidadeMaxVirtual()
                    : prodInstConf.getMesesValidadeFisico()));
      } else {
        c.setDataValidade(calcularVencimento(request.getVirtualMesesValidade()));
      }
      c.setApelidoVirtual(request.getVirtualApelido());

      c.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);
    } else {
      request.setVirtual(Constantes.CREDENCIAL_NAO_VIRTUAL);
      c.setDataValidade(calcularVencimento(prodInstConf.getMesesValidadeFisico()));
      c.setIdStatusV2(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);
    }
    setaHabilitadoCredencial(c);

    // TODO: Configuração do cartão como virtual baseado no produto instituição configuração
    c.setVirtual(request.getVirtual());

    return c;
  }

  private Credencial setaHabilitadoCredencial(Credencial c) {
    c.setHabilitaEcommerce(HABILITADO);
    c.setHabilitaExterior(HABILITADO);
    c.setHabilitaSaque(HABILITADO);
    c.setHabilitaUsoPessoa(HABILITADO);
    c.setHabilitaNotificacaoTransacao(HABILITADO);
    return c;
  }

  @Transactional
  public CredencialGerada gerarCredencial(
      GerarCredencialRequest request,
      Credencial credencialRecebida,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Plastico plastico,
      Pessoa pessoa,
      ContaPagamento conta,
      boolean desbloqueio) {

    Credencial credencial =
        getCredencialPreenchida(request, credencialRecebida, pessoa, conta, prodInstConf, plastico);

    createCard(credencial, false);

    if (prodInstConf.getManterPwdSegundaVia() != null && prodInstConf.getManterPwdSegundaVia()
        || produtoInstituicaoService.encontraConfiguracaoRedefinicaoSenhaComCaf(conta)) {
      atribuirSenhaCartaoExistenteAoNovo(credencialRecebida, credencial);
    }

    credencial = atualizarInfo(credencial);

    credencialContaService.vincularCredencialConta(conta, credencial, prodInstConf, pessoa);

    CredencialGerada cred = new CredencialGerada();
    cred.setCredencial(credencial);

    return cred;
  }

  public Credencial getCredencialPreenchida(
      GerarCredencialRequest request,
      Credencial credencialRecebida,
      Pessoa pessoa,
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Plastico plastico) {

    Credencial novaCredencial =
        montaEntidadeCredencial(new Credencial(), request, pessoa, conta, prodInstConf, plastico);

    if (pessoa.getNomeEmbossado() == null) {
      novaCredencial.setNomeImpresso(Constantes.PARTICIPANTE_PRE_CADASTRADO);
    } else {
      novaCredencial.setNomeImpresso(pessoa.getNomeEmbossado());
    }

    novaCredencial.setPin(encodeSenhaSHA256(gerarSenha(prodInstConf.getTamanhoPin())));

    if (request.getVirtual() != null && request.getVirtual()) {
      novaCredencial.setDataValidade(calcularVencimento(request.getVirtualMesesValidade()));
      novaCredencial.setApelidoVirtual(request.getVirtualApelido());
      novaCredencial.setStatus(ATIVO);
    } else {
      request.setVirtual(Constantes.CREDENCIAL_NAO_VIRTUAL);
      novaCredencial.setDataValidade(calcularVencimento(prodInstConf.getMesesValidadeFisico()));
      novaCredencial.setStatus(INATIVO);
    }
    novaCredencial.setHabilitaEcommerce(credencialRecebida.getHabilitaEcommerce());
    novaCredencial.setHabilitaExterior(credencialRecebida.getHabilitaExterior());
    novaCredencial.setHabilitaSaque(credencialRecebida.getHabilitaSaque());
    novaCredencial.setHabilitaUsoPessoa(credencialRecebida.getHabilitaUsoPessoa());
    novaCredencial.setHabilitaNotificacaoTransacao(
        credencialRecebida.getHabilitaNotificacaoTransacao());
    novaCredencial.setTitularidade(credencialRecebida.getTitularidade());

    novaCredencial.setVirtual(request.getVirtual());

    int versao = credencialRecebida.getCsn() == null ? VERSAO_UM : credencialRecebida.getCsn();
    novaCredencial.setCsn(versao + VERSAO_UM);
    novaCredencial.setMotivoEmissao(RENOVACAO);

    return novaCredencial;
  }

  public Credencial getCredencialPreenchidaPrimeiraCompra(
      GerarCredencialRequest request,
      Pessoa pessoa,
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Plastico plastico,
      SecurityUser user,
      ProdutoInstituicaoConfiguracaoCredito prodInstConfigCredito) {

    // seto o plastico para que tenha o produtoInstituicaoConfiguracao
    // para criar uma credencial com um produto especifico no jcard
    Credencial c =
        montaEntidadeCredencial(new Credencial(), request, pessoa, conta, prodInstConf, plastico);

    if (request.getVirtual() != null && request.getVirtual()) {

      // verifico se a conta foi cancelada
      if (Constantes.GRUPO_STATUS_CANCELADO.equals(conta.getTipoStatus().getIdGrupoStatus())) {
        throw new GenericServiceException(
            "Não é possível emitir cartão virtual para uma conta cancelada.");
      }

      // servico de buscar valor primeira compra
      Integer idInst =
          user.getIdInstituicao() != null ? user.getIdInstituicao() : conta.getIdInstituicao();

      HierarquiaInstituicao inst =
          instituicaoService.findByIdProcessadoraAndIdInstituicao(user.getIdProcessadora(), idInst);
      if (Objects.isNull(inst)) {
        throw new GenericServiceException(
            "Não foi possível localizar a instituição para definir o valor de primeira compra");
      }

      GetSaldoConta saldo =
          contaPagamentoService.calcularValorTransacaoMaximo(
              user, conta.getIdConta(), inst, prodInstConfigCredito);

      c.setValorTransacaoMaximo(saldo.getSaldoDisponivel());
      c.setDataValidade(saldo.getDataLocalDateTime());
      c.setApelidoVirtual(request.getVirtualApelido());
      c.setStatus(ATIVO);

    } else {
      throw new GenericServiceException(
          "Este serviço é específico para criação de cartão virtual para primeira compra.");
    }
    setaHabilitadoCredencial(c);

    // TODO: Configuração do cartão como virtual baseado no produto instituição configuração
    c.setVirtual(request.getVirtual());

    ContaPessoa contaPesssoa =
        contaPessoaService.findOneByIdPessoaAndIdConta(c.getIdPessoa(), c.getIdConta());

    if (Objects.nonNull(contaPesssoa)) {

      c.setTitularidade(contaPesssoa.getIdTitularidade());
      // verifico a versao da credencial
      Credencial credencial =
          credencialService.buscarCredencialMaisRecente(
              contaPesssoa.getIdConta(),
              contaPesssoa.getIdPessoa(),
              contaPesssoa.getIdTitularidade());

      int versao = credencial.getCsn() == null ? VERSAO_UM : credencial.getCsn();
      c.setCsn(versao + VERSAO_UM);
      c.setMotivoEmissao(PRIMEIRA_EMISSAO);
    }
    return c;
  }

  private Credencial montaEntidadeCredencial(
      Credencial c,
      GerarCredencialRequest request,
      Pessoa pessoa,
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Plastico plastico) {

    c.setPlastico(plastico);
    c.setBin6(prodInstConf.getBin());
    c.setBinLength(prodInstConf.getBinLength());
    c.setChip(prodInstConf.getChip());
    c.setDataHoraInclusao(LocalDateTime.now());
    c.setDataHoraStatus(LocalDateTime.now());
    c.setIdConta(conta.getIdConta());
    c.setIdContaPagamento(conta.getIdContaPagamento());
    c.setIdPessoa(pessoa.getIdPessoa());
    c.setIdPlastico(plastico.getIdPlastico());
    c.setIdUsuarioInclusao(request.getIdUsuario());
    c.setNomeImpresso(pessoa.getNomeEmbossado());
    c.setPin(encodeSenhaSHA256(gerarSenha(prodInstConf.getTamanhoPin())));
    c.setServiceCode(prodInstConf.getServiceCode());
    c.setIdBandeira(prodInstConf.getIdBandeira());
    c.setIdArranjo(prodInstConf.getIdArranjo());
    c.setMultiConta(
        request.getMultiConta() != null
            ? request.getMultiConta()
            : !prodInstConf
                .getProdutoInstituicao()
                .getProdutosInstituicaoGrupoProdutos()
                .isEmpty());
    return c;
  }

  private Credencial getCredencialPreEmitidaPreenchida(
      GerarCredencialRequest request,
      Pessoa pessoa,
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Plastico plastico,
      CredencialPreEmitida credPreEmi) {

    PedidoCredenciaisPreEmitidas pedido =
        pedidoCredencialService.getPedidoCredsPreEmitidasNotNull(credPreEmi);

    // primeiro preenche da forma padrao
    Credencial credencial = getCredencialPreenchida(request, pessoa, conta, prodInstConf, plastico);

    credencial.setNomeImpresso(pedido.getLinhaImpressa());
    credencial.setBin6(credPreEmi.getBin6());
    credencial.setBinLength(credPreEmi.getBinLength());
    credencial.setBinEstendido(credPreEmi.getBinEstendido());
    credencial.setTokenInterno(credPreEmi.getTokenInterno());
    credencial.setDataValidade(credPreEmi.getDataValidade());

    credencial.setChip(credPreEmi.getChip());
    credencial.setUltimos4Digitos(credPreEmi.getUltimos4Digitos());

    return credencial;
  }

  private List<Plastico> getPlasticoNotNull(ContaPagamento conta) {
    List<Plastico> plastico =
        plasticoRepo
            .findByIdProcessadoraAndIdInstituicaoAndIdProdutoInstituicaoAndDataCancelamentoEmissaoIsNull(
                conta.getIdProcessadora(),
                conta.getIdInstituicao(),
                conta.getIdProdutoInstituicao());

    if (plastico.isEmpty()) {
      throw new NoResultException(
          "Não foi possível iniciar a geração de credencial.Plastico não encontrado. processadora ="
              + conta.getIdProcessadora()
              + ", instituicao = "
              + conta.getIdInstituicao()
              + ", produto instituicao = "
              + conta.getIdProdutoInstituicao());
    }
    return plastico;
  }

  public ProdutoInstituicaoConfiguracao getProdutoInstConfigNotNull(ContaPagamento conta) {
    ProdutoInstituicaoConfiguracao prodInstConf =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                conta.getIdProcessadora(),
                conta.getIdProdutoInstituicao(),
                conta.getIdInstituicao());

    if (Objects.isNull(prodInstConf)) {
      throw new NoResultException(
          "Não foi possível iniciar a geração de credencial.ProdutoInstituicaoConfiguracao não encontrado. processadora ="
              + conta.getIdProcessadora()
              + ", instituicao = "
              + conta.getIdInstituicao()
              + ", produto instituicao = "
              + conta.getIdProdutoInstituicao());
    }
    return prodInstConf;
  }

  public ContaPagamento getContaByIdNotNull(Long idConta) {
    ContaPagamento conta = contaPagamentoService.findById(idConta);

    if (Objects.isNull(conta)) {
      throw new NoResultException(
          "Não foi possível iniciar a geração de credencial. ContaPagamento não encontrada. idConta = "
              + idConta);
    }
    return conta;
  }

  public Pessoa getPessoaByIdNotNull(Long idPessoa) {
    Pessoa pessoa = pessoaService.findById(idPessoa);

    if (Objects.isNull(pessoa)) {
      throw new NoResultException(
          "Não foi possível iniciar a geração de credencial.Pessoa não encontrada. idPessoa = "
              + idPessoa);
    }
    return pessoa;
  }

  public void createCard(Credencial credencial, Boolean adicional) {

    CreateCard createCard = getCreateCard(credencial);
    createCard.setAdicional(adicional);
    createCard.setMultiAccount(credencial.getMultiConta());

    CreateCardResponse response = cardService.createCard(createCard);
    if (response.getSuccess().equals(Boolean.FALSE)) {
      throw new GenericServiceException("Não foi possível criar Card. " + response.getErrors());
    }
    credencial.setBin6(Integer.valueOf(response.getBin()));
    credencial.setBinLength(response.getBinLength());
    credencial.setBinEstendido(Long.valueOf(response.getBinextended()));
    credencial.setUltimos4Digitos(Integer.valueOf(response.getLastfour()));
    credencial.setChip(response.getSmart());
    credencial.setTokenInterno(response.getToken());
  }

  private CreateCard getCreateCard(Credencial credencial) {
    CreateCard card = new CreateCard();

    card.setCardProduct(
        credencial.getPlastico().getProdutoInstituicaoConfiguracao().getIdCardProduct().toString());
    card.setCustomerRealId(credencial.getIdPessoa().toString());
    card.setEndDate(
        DateUtil.dateFormat(
            YYYY_MM_DD, DateUtil.localDateTimeToDate(credencial.getDataValidade())));
    card.setStartDate(
        DateUtil.dateFormat(
            YYYY_MM_DD, DateUtil.localDateTimeToDate(credencial.getDataHoraInclusao())));
    card.setState(CREATED);
    card.setVirtual(credencial.getVirtual());

    return card;
  }

  private synchronized Credencial atualizarInfo(Credencial c) {
    return persistCredencial(c);
  }

  private synchronized Credencial atualizarInfoAndReturn(Credencial c) {
    return persistCredencial(c);
  }

  private synchronized void atualizarInfoPrimeiraCompra(Credencial c) {
    persistCredencial(c);
  }

  private Credencial persistCredencial(Credencial c) {
    return credencialService.saveAndFlush(c);
  }

  /**
   * Método responsavel por gerar a senha do portador do tamanho solicitado
   *
   * @param tamanhoPin
   * @return
   */
  private String gerarSenha(Integer tamanhoPin) {
    return tamanhoPin != null && tamanhoPin.equals(TAM_MAX_PIN) ? "123456" : "1234";
  }

  public String encodeSenhaSHA256(String senha) {
    LegacyPasswordEncoder shaPasswordEncoder = new LegacyPasswordEncoder(SHA_256);
    return shaPasswordEncoder.encodePassword(senha, null);
  }

  public static LocalDateTime calcularVencimento(Integer meses) {
    Calendar c = Calendar.getInstance();
    c.set(Calendar.MONTH, c.get(Calendar.MONTH) + meses);
    c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
    return LocalDateTime.ofInstant(c.toInstant(), ZoneId.systemDefault());
  }

  // TODO Verificar a utilização do metodo, senão houver, remover!
  public static LocalDateTime calcularVencimentoPrimeiraCompra(Integer meses) {
    Calendar c = Calendar.getInstance();
    return LocalDateTime.ofInstant(c.toInstant(), ZoneId.systemDefault());
  }

  // TODO Verificar a utilização do metodo, senão houver, remover!
  private LocalDateTime calcularVencimentoVirtual(Integer meses) {
    Calendar c = Calendar.getInstance();
    c.set(Calendar.MONTH, c.get(Calendar.MONTH) + meses);
    return LocalDateTime.ofInstant(c.toInstant(), ZoneId.systemDefault());
  }

  // TODO Verificar a utilização do metodo, senão houver, remover!
  private ProdutoInstituicaoConfiguracao updateProdutoInstituicaoConfiguracao(
      ProdutoInstituicaoConfiguracao prodInstConf) {
    return produtoInstituicaoConfiguracaoService.save(prodInstConf);
  }

  // TODO Verificar a utilização do metodo, senão houver, remover!
  private String montarCredencialVirtual(ProdutoInstituicaoConfiguracao prodInstConf) {
    StringBuilder credencial = new StringBuilder();

    // padStart preenche com 0s a esquerda e garante que o bin tera pelo
    // menos 6 digitos,mas ele pode ter mais tambem
    credencial.append(
        Strings.padStart(
            prodInstConf.getBinEstendidoVirtual().toString(),
            MIN_DIG_BIN_ESTENDIDO_VIRTUAL,
            CHAR_ZERO));

    prodInstConf.setSequencialVirtual(prodInstConf.getSequencialVirtual() + SEQ_PADRAO);

    int tamanhoSequencial =
        calculaTamSequencial(
            prodInstConf.getPadrao(),
            prodInstConf.getBinEstendidoVirtual().toString().toCharArray().length);

    if (prodInstConf.getSequencialVirtual().toString().toCharArray().length > tamanhoSequencial) {
      throw new GenericRuntimeException(
          "Não foi possível gerar a credencial porque o tamanho do sequencial foi esgotado");
    }

    credencial.append(
        Strings.padStart(
            prodInstConf.getSequencialVirtual().toString(), tamanhoSequencial, CHAR_ZERO));
    credencial.append(DVUtil.montarDVBase10(credencial.toString()));
    return credencial.toString();
  }

  private int calculaTamSequencial(PadraoSeqCredencial padrao, Integer tamBinEstendido) {
    int tamanhoSequencia = padrao.getTamanhoPadrao();

    if (padrao.equals(PadraoSeqCredencial.SEM_PRADRAO)) {
      throw new IllegalArgumentException("padrao sequencia da credencial " + padrao + " invalido");
    }
    return tamanhoSequencia - tamBinEstendido - TAM_DIG_VERIFICADOR;
  }

  /**
   * Realiza o calculo do limite inicial do cartao, conforme percentual e limite credito da conta.
   *
   * @param idConta id da conta a ser verificado o saldo
   * @param percentual percetual a aplica sobre o limite
   * @return o resultado
   */
  // TODO Verificar a utilização do metodo, senão houver, remover!
  private BigDecimal calcularLimiteAdicionalInicialDoCartao(Long idConta, BigDecimal percentual) {
    GetSaldoConta saldoConta = contaPagamentoService.getSaldoConta(idConta);
    return saldoConta.getLimiteCredito().multiply(percentual);
  }

  /** Atualiza as informacoes do cartao adicional */
  public void atualizarInformacoesCartaoAdicional(
      GerarCredencialRequest request,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Credencial credencial,
      BigDecimal limitePrioritario) {

    if ((prodInstConf.getDiferenciacaoLimiteAdicional() != null)
        && (prodInstConf.getDiferenciacaoLimiteAdicional())
        && (request.getAdicional())) {

      log.info("Iniciando configuracao de cartao adicional");

      BigDecimal limitePercentual =
          limitePrioritario == null
              ? prodInstConf.getPercentualLimiteAdicional()
              : limitePrioritario;
      BigDecimal limiteContaPagamento =
          new BigDecimal(0); // sem configuracoes do limite da conta pagamento

      try {
        limiteContaPagamento =
            contaPagamentoService.getSaldoConta(request.getIdConta()).getLimiteCredito();
      } catch (GenericRuntimeException e) {
        throw new GenericRuntimeException(
            "Ocorreu um erro ao recuperar o saldo da conta, erro: " + e);
      }

      log.info(
          "Limite percentual: "
              + limitePercentual.doubleValue()
              + ". Limite da conta pagamento: "
              + limiteContaPagamento.doubleValue());

      JcardResponse response =
          cardService.updateInformacoesLimiteCartao(
              credencial.getTokenInterno(), limiteContaPagamento, limitePercentual);
      if (response.getSuccess().equals(Boolean.FALSE)) {
        throw new GenericServiceException(
            "Não foi possível atualizar dados cartao adicional. " + response.getErrors());
      }

      log.info("Finalizada configuracao de cartao adicional");
    }
  }

  private void atribuirSenhaCartaoExistenteAoNovo(
      Credencial credencialExistente, Credencial credencialNova) {
    ChangePassCardReplacement changePassCardReplacement =
        new ChangePassCardReplacement(
            credencialExistente.getTokenInterno(), credencialNova.getTokenInterno());
    JcardResponse response = cardService.changePasswordCardReplacement(changePassCardReplacement);

    if (response.getSuccess().equals(Boolean.FALSE)) {
      log.info(
          "Não foi possível trocar a senha da credencial nova pela senha da credencial anterior. Conta: "
              + credencialExistente.getIdConta()
              + ", Motivo: "
              + response.getErrors());
    }
  }
}
