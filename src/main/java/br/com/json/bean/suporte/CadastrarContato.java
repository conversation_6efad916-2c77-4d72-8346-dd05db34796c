package br.com.json.bean.suporte;

import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import javax.validation.constraints.Max;
import org.hibernate.validator.constraints.Email;

@ApiModel(value = "contato")
public class CadastrarContato implements Serializable {

  private static final long serialVersionUID = -2011618005084105427L;

  @Email private String email;

  private String nomeContato;

  @Max(99)
  private Integer idProcessadora;

  @Max(9999)
  private Integer idInstituicao;

  @Max(9999)
  private Integer idRegional;

  @Max(9999)
  private Integer idFilial;

  @Max(999999)
  private Integer idPontoDeRelacionamento;

  @Max(99)
  private Integer tipoContato;

  @Max(99)
  private Integer dddTelefoneFixo;

  @Max(999999999)
  private Integer telefoneFixo;

  @Max(99)
  private Integer dddCelular;

  @Max(999999999)
  private Integer celular;

  private Integer idUsuario;

  private Integer idNivelHierarquia;

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public Integer getIdPontoDeRelacionamento() {
    return idPontoDeRelacionamento;
  }

  public void setIdPontoDeRelacionamento(Integer idPontoDeRelacionamento) {
    this.idPontoDeRelacionamento = idPontoDeRelacionamento;
  }

  public Integer getTipoContato() {
    return tipoContato;
  }

  public void setTipoContato(Integer tipoContato) {
    this.tipoContato = tipoContato;
  }

  public String getNomeContato() {
    return nomeContato;
  }

  public void setNomeContato(String nomeContato) {
    this.nomeContato = nomeContato;
  }

  public Integer getDddTelefoneFixo() {
    return dddTelefoneFixo;
  }

  public void setDddTelefoneFixo(Integer dddTelefoneFixo) {
    this.dddTelefoneFixo = dddTelefoneFixo;
  }

  public Integer getTelefoneFixo() {
    return telefoneFixo;
  }

  public void setTelefoneFixo(Integer telefoneFixo) {
    this.telefoneFixo = telefoneFixo;
  }

  public Integer getDddCelular() {
    return dddCelular;
  }

  public void setDddCelular(Integer dddCelular) {
    this.dddCelular = dddCelular;
  }

  public Integer getCelular() {
    return celular;
  }

  public void setCelular(Integer celular) {
    this.celular = celular;
  }

  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public Integer getIdNivelHierarquia() {
    return idNivelHierarquia;
  }

  public void setIdNivelHierarquia(Integer idNivelHierarquia) {
    this.idNivelHierarquia = idNivelHierarquia;
  }
}
