package br.com.json.bean.cadastral;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.hibernate.validator.constraints.NotBlank;
import org.springframework.format.annotation.NumberFormat;

public class EnderecoPessoaRequest implements Serializable {

  private static final long serialVersionUID = 2452017353142556522L;

  private static final int MIN_TAM_CEP = 8;
  private static final int MAX_TAM_CEP = 8;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idUsuarioInclusao;

  private Long idEndereco;

  private Long idPessoa;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idTipoEndereco;

  @NotBlank
  @ApiModelProperty(required = true)
  @NumberFormat
  @Size(min = MIN_TAM_CEP, max = MAX_TAM_CEP)
  private String cep;

  @NotBlank
  @ApiModelProperty(required = true)
  private String logradouro;

  private String numero;
  private String complemento;

  @NotBlank
  @ApiModelProperty(required = true)
  private String bairro;

  @NotBlank
  @ApiModelProperty(required = true)
  private String cidade;

  @NotBlank
  @ApiModelProperty(required = true)
  @Size(min = 0, max = 2)
  private String uf;

  public Long getIdEndereco() {
    return idEndereco;
  }

  public void setIdEndereco(Long idEndereco) {
    this.idEndereco = idEndereco;
  }

  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Long getIdPessoa() {
    return idPessoa;
  }

  public void setIdPessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  public Integer getIdTipoEndereco() {
    return idTipoEndereco;
  }

  public void setIdTipoEndereco(Integer idTipoEndereco) {
    this.idTipoEndereco = idTipoEndereco;
  }

  public String getCep() {
    return cep;
  }

  public void setCep(String cep) {
    this.cep = cep;
  }

  public String getLogradouro() {
    return logradouro;
  }

  public void setLogradouro(String logradouro) {
    this.logradouro = logradouro;
  }

  public String getNumero() {
    return numero;
  }

  public void setNumero(String numero) {
    this.numero = numero;
  }

  public String getComplemento() {
    return complemento;
  }

  public void setComplemento(String complemento) {
    this.complemento = complemento;
  }

  public String getBairro() {
    return bairro;
  }

  public void setBairro(String bairro) {
    this.bairro = bairro;
  }

  public String getCidade() {
    return cidade;
  }

  public void setCidade(String cidade) {
    this.cidade = cidade;
  }

  public String getUf() {
    return uf;
  }

  public void setUf(String uf) {
    this.uf = uf;
  }
}
