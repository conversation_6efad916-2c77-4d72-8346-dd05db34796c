package br.com.json.bean.cadastral;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ParceriaComercialContratoDinamicoDTO implements Serializable {

  private static final long serialVersionUID = 1L;

  private BuscaContratoComercial buscaContratoComercial;

  private ContratoComercial contratoComercial;

  @AllArgsConstructor
  @NoArgsConstructor
  @Getter
  @Setter
  public static class BuscaContratoComercial implements Serializable {

    private static final long serialVersionUID = 1L;

    private Organizacao organizacao;

    private Parceiro parceiro;

    private ModalidadeComercial modalidadeComercial;

    private Integer statusContratoComercial;
  }

  @AllArgsConstructor
  @NoArgsConstructor
  @Getter
  @Setter
  public static class Organizacao implements Serializable {

    private static final long serialVersionUID = 1L;

    private Hierarquia hierarquia;

    private String nomeEmpresa;

    private String cnpj;

    private Integer statusEmpresa;

    public Organizacao trataDocumentoParceiroComercial() {
      if (this.cnpj != null) {
        this.cnpj = this.cnpj.replaceAll("\\D", "");
      }
      return this;
    }

    public Organizacao tratarEmpresaParaConsultaNoBanco() {
      if (this.nomeEmpresa != null) {
        this.nomeEmpresa = "%" + this.nomeEmpresa.toUpperCase() + "%";
      }
      return this;
    }
  }

  @AllArgsConstructor
  @NoArgsConstructor
  @Getter
  @Setter
  public static class Parceiro implements Serializable {

    private static final long serialVersionUID = 1L;

    private Hierarquia hierarquia;

    private String nomeParceiro;

    private String cpf;

    private String cpfVinculado;

    private Integer statusParceiro;

    public Parceiro trataDocumentoParceiroComercial() {
      if (this.cpf != null) {
        this.cpf = this.cpf.replaceAll("\\D", "");
      }
      return this;
    }

    public Parceiro trataDocumentoParceiroComercialViculado() {
      if (this.cpfVinculado != null) {
        this.cpfVinculado = this.cpfVinculado.replaceAll("\\D", "");
      }
      return this;
    }

    public Parceiro tratarParceiroParaConsultaNoBanco() {
      if (this.nomeParceiro != null) {
        this.nomeParceiro = "%" + this.nomeParceiro.toUpperCase() + "%";
      }
      return this;
    }
  }

  @AllArgsConstructor
  @NoArgsConstructor
  @Getter
  @Setter
  public static class ModalidadeComercial implements Serializable {

    private static final long serialVersionUID = 1L;

    private Hierarquia hierarquia;

    private String nomeModalidadeComercial;

    private Integer statusModalidadeComercial;

    public ModalidadeComercial tratarModalidadeParaConsultaNoBanco() {
      if (this.nomeModalidadeComercial != null) {
        this.nomeModalidadeComercial = "%" + this.nomeModalidadeComercial.toUpperCase() + "%";
      }
      return this;
    }
  }

  @AllArgsConstructor
  @NoArgsConstructor
  @Getter
  @Setter
  public static class ContratoComercial implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<ParceriaComercialContratoDTO> parceriaComercialContratoDTOS;
  }

  @AllArgsConstructor
  @NoArgsConstructor
  @Getter
  @Setter
  public static class Hierarquia implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer idProcessadora;
    private Integer idInstituicao;
    private Integer idRegional;
    private Integer idFilial;
    private Integer idPontoDeRelacionamento;
  }
}
