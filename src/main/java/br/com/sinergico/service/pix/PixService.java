package br.com.sinergico.service.pix;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.pix.ContaTransacional;
import br.com.entity.pix.ContaTransacionalBloqueio;
import br.com.entity.pix.ContaTransacionalChaves;
import br.com.entity.pix.ContaTransacionalDevolucao;
import br.com.entity.pix.ContaTransacionalMovimento;
import br.com.entity.pix.ContaTransacionalPagamento;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.pix.NotificacaoDiretorio;
import br.com.entity.pix.NotificacaoReivindicacao;
import br.com.entity.pix.ParticipantesIspb;
import br.com.entity.pix.RepasseInstitucional;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.CotacaoPontos;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.enums.pix.MotivoMEDEnum;
import br.com.json.bean.enums.pix.SituacaoChaveEnum;
import br.com.json.bean.enums.pix.SituacaoReivindicacaoEnum;
import br.com.json.bean.enums.pix.StatusBloqueioEnum;
import br.com.json.bean.enums.pix.StatusValidacaoContaEnum;
import br.com.json.bean.enums.pix.TipoContaEnum;
import br.com.json.bean.enums.pix.TipoMovimentoEnum;
import br.com.json.bean.pix.request.ExtratoRequest;
import br.com.json.bean.pix.request.vo.ContaVO;
import br.com.json.bean.pix.request.webhook.BloqueioForm;
import br.com.json.bean.pix.request.webhook.DesbloqueioForm;
import br.com.json.bean.pix.request.webhook.MovimentoForm;
import br.com.json.bean.pix.request.webhook.NotificacaoDiretorioForm;
import br.com.json.bean.pix.request.webhook.NotificacaoForm;
import br.com.json.bean.pix.request.webhook.NotificacaoInfracaoForm;
import br.com.json.bean.pix.request.webhook.NotificacaoMovimentoForm;
import br.com.json.bean.pix.request.webhook.NotificacaoReivindicacaoForm;
import br.com.json.bean.pix.request.webhook.NotificacaoSolicitacaoDevolucaoForm;
import br.com.json.bean.pix.request.webhook.ValidaContaRecebedorForm;
import br.com.json.bean.pix.response.ExtratoResponse;
import br.com.json.bean.pix.response.vo.ErroValidacaoContaVO;
import br.com.json.bean.pix.response.vo.ExtratoInfoVO;
import br.com.json.bean.pix.response.vo.InstrucoesDevolucao;
import br.com.json.bean.pix.response.vo.MovimentoInfoVO;
import br.com.json.bean.pix.response.vo.OrigemMovimento;
import br.com.json.bean.pix.response.webhook.BloqueioResponse;
import br.com.json.bean.pix.response.webhook.MovimentoResponse;
import br.com.json.bean.pix.response.webhook.NotificacaoResponse;
import br.com.json.bean.pix.response.webhook.SaldoContaResponse;
import br.com.json.bean.pix.response.webhook.ValidaContaResponse;
import br.com.json.bean.suporte.ComunicadoContaViaPush;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.facade.cadastral.ContaPagamentoFacade;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.pix.ContaTransacionalChavesRepository;
import br.com.sinergico.repository.pix.ContaTransacionalDevolucaoRepository;
import br.com.sinergico.repository.pix.ParticipantesIspbRepository;
import br.com.sinergico.repository.pix.RepasseInstitucionalRepository;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.B2bFaturaPixService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.ProdutoTransacaoService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.ComunicadorPortadorPushService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.DadosContaPIXDTO;
import br.com.sinergico.vo.InfoContaTransacionalPixIssuerVO;
import br.com.sinergico.vo.InfosChavesPixIssuerVO;
import br.com.sinergico.vo.InfosPixSolicitacaoLimiteIssuerVO;
import br.com.sinergico.vo.InfosPixVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PixService {

  private static final String NOTIFICACAO_RECEBIDA_MSG =
      "Recebida a mensagem de notificação para a instituição";
  private static final String COD_CONTA_NAO_ENCONTRADA = "60743";
  private static final String COD_CONTA_INVALIDA = "60605";
  private static final String COD_SITUACAO_NAO_PERMITE_CREDITO = "60216";
  private static final String COD_END_TO_END_DUPLICADO = "AB00018";
  private static final String COD_PRODUTO_DESABILITADO = "60215";
  private static final String SEM_ERRO = "0";
  private String mensagemErro = null;
  private static final Integer ID_ONE_SIGNAL = 1;
  private static final Integer ENVIO_PUSH_PORTABILIDADE = 4;
  private static final String DEBITO = "DEBITO";
  private static final String CREDITO = "CREDITO";
  private static final List<SituacaoChaveEnum> statusExcluidos =
      Arrays.asList(
          SituacaoChaveEnum.EXCLUDED,
          SituacaoChaveEnum.EXCLUDED_OWNERSHIP,
          SituacaoChaveEnum.EXCLUDED_PORTABILITY,
          SituacaoChaveEnum.EXCLUDED_SYNC);
  private static final List<Integer> STATUS_NAO_PERMITIDOS_RECEBER_PIX =
      Arrays.asList(
          Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM,
          Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO,
          Constantes.TIPO_STATUS_INATIVADO_PELA_EMPRESA,
          Constantes.TIPO_STATUS_CANCELADO_PELO_USUARIO,
          Constantes.TIPO_STATUS_CANCELADO_PELA_INSTITUICAO,
          Constantes.TIPO_STATUS_CANCELADO_POR_FRAUDE,
          Constantes.TIPO_STATUS_CANCELADO_POR_INATIVIDADE,
          Constantes.TIPO_STATUS_CANCELADO_POR_FALECIMENTO,
          Constantes.TIPO_STATUS_CANCELAMENTO_MANUAL,
          Constantes.TIPO_STATUS_CANCELADO_CRELIQ,
          Constantes.TIPO_STATUS_CANCELADO_PERDA,
          Constantes.TIPO_STATUS_CANCELADO_ACORDO_PERDA,
          Constantes.TIPO_STATUS_CANCELADO_PERDA_QUITADA,
          Constantes.TIPO_STATUS_CANCELADO_A_PEDIDO_INST,
          Constantes.TIPO_STATUS_CANCELADO_INCONSISTENCIA_PROC);

  private static final ObjectMapper objectMapper = new ObjectMapper();
  private static final Gson gson = new Gson();

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private NotificacaoReivindicacaoService notificacaoReivindicacaoService;

  @Autowired private ContaTransacionalService contaTransacionalService;

  @Autowired private B2bFaturaPixService b2bFaturaPixService;

  @Autowired private ContaTransacionalChavesRepository contaTransacionalChavesRepository;

  @Autowired private SolicitarLimitePixService solicitarLimitePixService;

  @Autowired private ContaTransacionalReivindicacaoService contaTransacionalReivindicacaoService;

  @Autowired private ContaTransacionalMovimentoService contaTransacionalMovimentoService;

  @Autowired private ContaTransacionalPagamentoService contaTransacionalPagamentoService;

  @Autowired private ContaTransacionalChavesService contaTransacionalChavesService;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private NotificacaoMovimentoService notificacaoMovimentoService;

  @Autowired private NotificacaoInfracaoService notificacaoInfracaoService;

  @Autowired private NotificacaoSolicitacaoDevolucaoService notificacaoSolicitacaoDevolucaoService;

  @Autowired private NotificacaoDiretorioService notificacaoDiretorioService;

  @Autowired private ContaTransacionalBloqueioService contaTransacionalBloqueioService;

  @Autowired private ContaTransacionalDevolucaoService contaTransacionalDevolucaoService;

  @Autowired private CotacaoPontosRepository cotacaoPontosRepository;

  @Autowired private ParticipantesIspbRepository participantesIspbRepository;

  @Autowired private ContaTransacionalDevolucaoRepository contaTransacionalDevolucaoRepository;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private ComunicadorPortadorPushService comunicadorPushService;

  @Autowired private ProdutoTransacaoService produtoTransacaoService;

  @Autowired private AcessoUsuarioService usuarioService;

  @Autowired private RepasseInstitucionalRepository repasseInstitucionalRepository;

  @Autowired @Lazy private ContaPagamentoFacade contaPagamentoFacade;

  @Autowired private CredencialRepository credencialRepository;

  @Autowired private UtilService utilService;

  @Transactional
  public DadosContaPIXDTO getDadosConta(Long idContaPagamento) {
    ContaPagamento contaPagamento = contaPagamentoService.findByIdNotNull(idContaPagamento);
    return build(contaPagamento);
  }

  @Autowired private EventoService eventoService;

  private DadosContaPIXDTO build(ContaPagamento contaPagamento) {
    Pessoa titular = getTitular(contaPagamento);
    return DadosContaPIXDTO.builder()
        .dtAberturaConta(contaPagamento.getDataHoraInclusao())
        .documento(titular.getDocumento())
        .nome(titular.getNomePorTipoPessoa())
        .build();
  }

  private Pessoa getTitular(ContaPagamento contaPagamento) {
    return contaPagamento.getContasPessoa().stream()
        .filter(cp -> cp.getIdTitularidade() == 1)
        .collect(Collectors.toList())
        .get(0)
        .getPessoa();
  }

  public ResponseEntity<ExtratoResponse> consultarExtrato(
      ExtratoRequest extratoRequest, SecurityUserPortador userPortador) {

    Long idConta = extratoRequest.getConta();
    ContaPagamento contaPagamento =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(idConta, userPortador);

    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());

    ContaTransacional contaTransacional =
        contaTransacionalService
            .findByIdConta(idConta)
            .orElseGet(
                () -> contaTransacionalService.criarContaTransacional(idConta, instituicaoPix));

    return bancoRendimentoClient.consultarExtrato(
        extratoRequest,
        instituicaoPix.getIdInstituicao(),
        contaTransacional.getConta().getIdConta() + contaTransacional.getDvConta(),
        instituicaoPix);
  }

  public ExtratoResponse validarConsultarExtratoLocal(
      ExtratoRequest extratoRequest, SecurityUserPortador userPortador) {
    ContaPagamento contaPagamento =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            extratoRequest.getConta(), userPortador);
    return this.consultarExtratoLocal(extratoRequest, contaPagamento);
  }

  public ExtratoResponse validarConsultarExtratoLocal(
      ExtratoRequest extratoRequest, SecurityUserCorporativo userCorporativo) {
    ContaPagamento contaPagamento =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
            extratoRequest.getConta(), userCorporativo);
    return this.consultarExtratoLocal(extratoRequest, contaPagamento);
  }

  private ExtratoResponse consultarExtratoLocal(
      ExtratoRequest extratoRequest, ContaPagamento contaPagamento) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());
    ContaTransacional contaTransacional =
        contaTransacionalService
            .findByIdConta(extratoRequest.getConta())
            .orElseGet(
                () ->
                    contaTransacionalService.criarContaTransacional(
                        extratoRequest.getConta(), instituicaoPix));

    List<ContaTransacionalMovimento> contaTransacionalMovimento;
    if (extratoRequest.getEndToEnd() == null) {
      contaTransacionalMovimento =
          contaTransacionalMovimentoService.buscarExtratos(
              contaTransacional.getAgencia().toString(),
              contaTransacional.getConta().getIdConta() + contaTransacional.getDvConta(),
              extratoRequest);
    } else {
      contaTransacionalMovimento =
          contaTransacionalMovimentoService.buscarExtratosPorEndToEnd(extratoRequest.getEndToEnd());
    }

    ExtratoResponse response = new ExtratoResponse();
    if (!contaTransacionalMovimento.isEmpty()) {
      response = contaTransacionalMovimentoToExtratoResponse(contaTransacionalMovimento);
    } else {
      response.setFailure(true);
      response.setSuccess(false);
    }

    return response;
  }

  public ResponseEntity<NotificacaoResponse> tratarNotificacao(NotificacaoForm notificacaoForm)
      throws IOException {

    String tmp;
    switch (notificacaoForm.getTipoNotificacao()) {
      case DIRETORIO:
        tmp = gson.toJson(notificacaoForm.getMensagem());
        NotificacaoDiretorioForm notificacaoDiretorioForm =
            objectMapper.readValue(tmp, NotificacaoDiretorioForm.class);
        return new ResponseEntity<>(
            tratarNotificacaoDiretorio(notificacaoDiretorioForm, notificacaoForm.getCpfCnpj()),
            HttpStatus.CREATED);
      case REIVINDICACAO:
        tmp = gson.toJson(notificacaoForm.getMensagem());
        NotificacaoReivindicacaoForm notificacaoReivindicacaoForm =
            objectMapper.readValue(tmp, NotificacaoReivindicacaoForm.class);
        return new ResponseEntity<>(
            tratarNotificacaoReivindicacao(
                notificacaoReivindicacaoForm, notificacaoForm.getCpfCnpj()),
            HttpStatus.CREATED);
      case MOVIMENTO:
        tmp = gson.toJson(notificacaoForm.getMensagem());
        NotificacaoMovimentoForm notificacaoMovimentoForm =
            objectMapper.readValue(tmp, NotificacaoMovimentoForm.class);
        return new ResponseEntity<>(
            tratarNotificacaoMovimento(notificacaoMovimentoForm, notificacaoForm.getCpfCnpj()),
            HttpStatus.CREATED);
      case SOLICITACAO_DEVOLUCAO:
        tmp = gson.toJson(notificacaoForm.getMensagem());
        NotificacaoSolicitacaoDevolucaoForm notificacaoSolicitacaoDevolucaoForm =
            objectMapper.readValue(tmp, NotificacaoSolicitacaoDevolucaoForm.class);
        return new ResponseEntity<>(
            tratarNotificacaoSolicitacaoDevolucaoForm(
                notificacaoSolicitacaoDevolucaoForm, notificacaoForm.getCpfCnpj()),
            HttpStatus.CREATED);
      case RELATO_INFRACAO:
        tmp = gson.toJson(notificacaoForm.getMensagem());
        NotificacaoInfracaoForm notificacaoInfracaoForm =
            objectMapper.readValue(tmp, NotificacaoInfracaoForm.class);
        return new ResponseEntity<>(
            tratarNotificacaoInfracao(notificacaoInfracaoForm, notificacaoForm.getCpfCnpj()),
            HttpStatus.CREATED);
      default:
        log.error("Mensagem de notificação não reconhecida. notificacaoForm: " + notificacaoForm);
        return new ResponseEntity<>(
            new NotificacaoResponse(false, "Tipo de notificação não reconhecida"),
            HttpStatus.BAD_REQUEST);
    }
  }

  private NotificacaoResponse tratarNotificacaoMovimento(
      NotificacaoMovimentoForm notificacaoMovimentoForm, String cpfCnpj) {
    log.info(
        "Recebida notificação de Movimento. CPF/CNPJ: "
            + cpfCnpj
            + " notificacaoMovimentoForm: "
            + notificacaoMovimentoForm);
    notificacaoMovimentoService.salvarNotificacaoWebhook(notificacaoMovimentoForm);
    return new NotificacaoResponse(true, NOTIFICACAO_RECEBIDA_MSG);
  }

  private NotificacaoResponse tratarNotificacaoInfracao(
      NotificacaoInfracaoForm notificacaoInfracaoForm, String cpfCnpj) {
    log.info(
        "Recebida notificação de Infração. CPF/CNPJ: "
            + cpfCnpj
            + " notificacaoInfracaoForm: "
            + notificacaoInfracaoForm);
    notificacaoInfracaoService.salvarNotificacaoWebhook(notificacaoInfracaoForm);
    return new NotificacaoResponse(true, NOTIFICACAO_RECEBIDA_MSG);
  }

  private NotificacaoResponse tratarNotificacaoReivindicacao(
      NotificacaoReivindicacaoForm notificacaoReivindicacaoForm, String cpfCnpj) {
    log.info("Recebida notificação de Reivindicacao: CPF/CNPJ " + cpfCnpj);
    log.info(notificacaoReivindicacaoForm.toString());

    LocalDateTime pushDate = LocalDateTime.now();
    if (notificacaoReivindicacaoForm.getStatus().equals(SituacaoReivindicacaoEnum.OPEN)) {
      Collection<Integer> gruposNaoVemNaPesquisa = new ArrayList<>();
      gruposNaoVemNaPesquisa.add(Constantes.GRUPO_STATUS_CANCELADO);
      gruposNaoVemNaPesquisa.add(Constantes.GRUPO_STATUS_BLOQUEIO_ORIGEM);
      Credencial credencial =
          credencialService.buscarCredencialFisicaMaisRecenteContaTitular(
              Long.valueOf(
                  notificacaoReivindicacaoForm
                      .getDonorAccount()
                      .getAccountNumber()
                      .substring(
                          0,
                          notificacaoReivindicacaoForm.getDonorAccount().getAccountNumber().length()
                              - 1)),
              Constantes.TITULARIDADE_CREDENCIAL,
              Constantes.CREDENCIAL_NAO_VIRTUAL,
              gruposNaoVemNaPesquisa);

      ComunicadoContaViaPush comunicado =
          getComunicadoPushPortabilidade(
              credencial, credencial.getIdCredencial(), notificacaoReivindicacaoForm);
      enviarNotificacaoPush(comunicado);
    }

    if (!notificacaoReivindicacaoForm
            .getStatus()
            .equals(SituacaoReivindicacaoEnum.WAITING_RESOLUTION)
        && !notificacaoReivindicacaoForm.getStatus().equals(SituacaoReivindicacaoEnum.OPEN)) {
      contaTransacionalReivindicacaoService.atualizarReivindicacaoStatus(
          notificacaoReivindicacaoForm.getStatus(), notificacaoReivindicacaoForm.getId());
    }

    DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
    NotificacaoReivindicacao notificacaoReivindicacao = new NotificacaoReivindicacao();
    notificacaoReivindicacao.setIdReivindicacao(notificacaoReivindicacaoForm.getId());
    notificacaoReivindicacao.setType(notificacaoReivindicacaoForm.getType());
    notificacaoReivindicacao.setIsClaimerPerson(
        notificacaoReivindicacaoForm.getIsClaimerPerson() != null
            && notificacaoReivindicacaoForm.getIsClaimerPerson());
    notificacaoReivindicacao.setIsDonorPerson(
        notificacaoReivindicacaoForm.getIsDonorPerson() != null
            && notificacaoReivindicacaoForm.getIsDonorPerson());
    notificacaoReivindicacao.setKey(notificacaoReivindicacaoForm.getKey());
    notificacaoReivindicacao.setKeyType(notificacaoReivindicacaoForm.getKeyType());
    notificacaoReivindicacao.setParticipant(notificacaoReivindicacaoForm.getParticipant());
    notificacaoReivindicacao.setDonorParticipant(
        notificacaoReivindicacaoForm.getDonorParticipant());

    if (Objects.nonNull(notificacaoReivindicacaoForm.getClaimerAccount())
        && Objects.nonNull(notificacaoReivindicacaoForm.getClaimer())) {
      notificacaoReivindicacao.setClaimerAccountNumber(
          notificacaoReivindicacaoForm.getClaimerAccount().getAccountNumber());
      notificacaoReivindicacao.setClaimerAccountType(
          notificacaoReivindicacaoForm.getClaimerAccount().getAccountType());
      notificacaoReivindicacao.setClaimerTaxIdNumber(
          notificacaoReivindicacaoForm.getClaimer().getTaxIdNumber());
      notificacaoReivindicacao.setClaimerName(notificacaoReivindicacaoForm.getClaimer().getName());
      notificacaoReivindicacao.setClaimerTradeName(
          notificacaoReivindicacaoForm.getClaimer().getTradeName());
    }

    if (Objects.nonNull(notificacaoReivindicacaoForm.getDonorAccount())
        && Objects.nonNull(notificacaoReivindicacaoForm.getDonor())) {
      notificacaoReivindicacao.setDonorAccountNumber(
          notificacaoReivindicacaoForm.getDonorAccount().getAccountNumber());
      notificacaoReivindicacao.setDonorAccountType(
          notificacaoReivindicacaoForm.getDonorAccount().getAccountType());
      notificacaoReivindicacao.setDonorTaxIdNumber(
          notificacaoReivindicacaoForm.getDonor().getTaxIdNumber());
      notificacaoReivindicacao.setDonorName(notificacaoReivindicacaoForm.getDonor().getName());
      notificacaoReivindicacao.setDonorTradeName(
          notificacaoReivindicacaoForm.getDonor().getTradeName());
    }

    if (notificacaoReivindicacaoForm.getCompletionPeriodEnd() != null) {
      notificacaoReivindicacao.setCompletionPeriodEnd(
          LocalDateTime.parse(notificacaoReivindicacaoForm.getCompletionPeriodEnd(), formatter));
    }
    notificacaoReivindicacao.setLastModified(
        LocalDateTime.parse(notificacaoReivindicacaoForm.getLastModified(), formatter));
    notificacaoReivindicacao.setResolutionPeriodEnd(
        LocalDateTime.parse(notificacaoReivindicacaoForm.getResolutionPeriodEnd(), formatter));
    notificacaoReivindicacao.setStatus(notificacaoReivindicacaoForm.getStatus());
    notificacaoReivindicacao.setCancelledBy(notificacaoReivindicacaoForm.getCancelledBy());
    notificacaoReivindicacao.setCancelReason(notificacaoReivindicacaoForm.getCancelReason());
    notificacaoReivindicacao.setPushDate(pushDate);
    notificacaoReivindicacaoService.salvarNotificacao(notificacaoReivindicacao);
    contaTransacionalChavesService.atualizarStatusDeReivindicacao(notificacaoReivindicacaoForm);

    return new NotificacaoResponse(true, NOTIFICACAO_RECEBIDA_MSG);
  }

  private NotificacaoResponse tratarNotificacaoDiretorio(
      NotificacaoDiretorioForm notificacaoDiretorioForm, String cpfCnpj) {

    log.info(
        "Recebida notificação de Diretorio. CPF/CNPJ: "
            + cpfCnpj
            + "  notificacaoDiretorioForm: "
            + notificacaoDiretorioForm);
    NotificacaoDiretorio notificacaoDiretorio =
        this.notificacaoDiretorioService.salvarNotificacaoWebhook(notificacaoDiretorioForm);
    if (statusExcluidos.contains(notificacaoDiretorio.getStatus())) {
      List<ContaTransacionalChaves> chaves =
          contaTransacionalChavesService.findByValorChaveAndInscricaoNacionalAndDeletedFalse(
              notificacaoDiretorio.getKey(), cpfCnpj);
      if (chaves != null && !chaves.isEmpty()) {
        chaves.stream()
            .filter(c -> Boolean.FALSE.equals(c.getDeleted()))
            .forEach(c -> c.setDeleted(Boolean.TRUE));
        contaTransacionalChavesRepository.saveAll(chaves);
      }
    }
    return new NotificacaoResponse(true, NOTIFICACAO_RECEBIDA_MSG);
  }

  private NotificacaoResponse tratarNotificacaoSolicitacaoDevolucaoForm(
      NotificacaoSolicitacaoDevolucaoForm notificacaoSolicitacaoDevolucaoForm, String cpfCnpj) {

    log.info(
        "Recebida notificação de Movimento. CPF/CNPJ: "
            + cpfCnpj
            + "  notificacaoSolicitacaoDevolucaoForm: "
            + notificacaoSolicitacaoDevolucaoForm);
    this.notificacaoSolicitacaoDevolucaoService.salvarNotificacaoWebhook(
        notificacaoSolicitacaoDevolucaoForm);
    return new NotificacaoResponse(true, NOTIFICACAO_RECEBIDA_MSG);
  }

  public ResponseEntity<SaldoContaResponse> consultaSaldoDeContaParaOperacoesGerais(
      String codIspb, String codAgencia, String nroConta, String cpfCnpj, String uuid) {

    try {
      Boolean inmais = false;
      Long numeroConta = getNumeroContaSemDV(nroConta);
      ContaTransacional contaTransacional =
          contaTransacionalService
              .findByIdConta(numeroConta)
              .orElseThrow(
                  () ->
                      new GenericServiceException(
                          String.format(
                              "Configuração PIX para o conta %s não encontrada", nroConta)));
      GetSaldoConta saldoConta =
          contaPagamentoService.getSaldoConta(contaTransacional.getConta().getIdConta());
      ContaTransacionalBloqueio contaTransacionalBloqueio;

      inmais =
          Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(contaTransacional.getAgencia());

      if (uuid == null) {
        contaTransacionalBloqueio =
            contaTransacionalBloqueioService.findFirstByContaTransacionalOrderByDtHrInclusaoDesc(
                contaTransacional);
      } else {
        contaTransacionalBloqueio =
            contaTransacionalBloqueioService.findByUuidBloqueioDevolucaoEspecial(uuid);
      }

      SaldoContaResponse saldoContaResponse = new SaldoContaResponse();
      boolean possuiValorBloqueado =
          contaTransacionalBloqueio != null
              && contaTransacionalBloqueio.getDesbloqueado() != null
              && !contaTransacionalBloqueio.getDesbloqueado();
      BigDecimal valorBloqueado =
          possuiValorBloqueado && contaTransacionalBloqueio.getValor() != null
              ? contaTransacionalBloqueio.getValor()
              : BigDecimal.ZERO;

      saldoContaResponse.setSaldoBloq(valorBloqueado);
      saldoContaResponse.setSaldoTotal(trataValorInMais(saldoConta.getSaldoDisponivel(), inmais));
      saldoContaResponse.setSaldoDisp(
          possuiValorBloqueado
              ? contaTransacionalBloqueio
                          .getValor()
                          .compareTo(contaTransacionalBloqueio.getValorTotalBloqueado())
                      == 0
                  ? (contaTransacionalBloqueio.getValorInicialRetido().subtract(valorBloqueado))
                  : (trataValorInMais(saldoConta.getSaldoDisponivel(), inmais)
                      .subtract(valorBloqueado))
              : (trataValorInMais(saldoConta.getSaldoDisponivel(), inmais)));

      return new ResponseEntity<>(saldoContaResponse, HttpStatus.OK);
    } catch (Exception e) {
      log.error("Erro ao consultar saldo" + e.getMessage());
      e.printStackTrace();
      return new ResponseEntity<>(
          new SaldoContaResponse(
              "Erro ao consultar saldo", 99999, BigDecimal.ZERO, BigDecimal.ZERO, BigDecimal.ZERO),
          HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  public ResponseEntity<ValidaContaResponse> validarContaRecebedor(
      ValidaContaRecebedorForm validaContaRecebedorForm) {
    log.info(validaContaRecebedorForm.toString());

    String idInstituicao = String.valueOf(validaContaRecebedorForm.getCodAgencia()).substring(0, 4);
    Long numeroConta =
        Long.valueOf(
            validaContaRecebedorForm
                .getNroConta()
                .substring(0, validaContaRecebedorForm.getNroConta().length() - 1));

    Optional<ContaPagamento> contaPagamentoOptional =
        contaPagamentoService.findByIdInstituicaoAndAndIdContaAndDocumento(
            Integer.valueOf(idInstituicao), numeroConta, validaContaRecebedorForm.getCpfCnpj());

    if (!contaPagamentoOptional.isPresent()) {
      return criarResposta(
          "00000",
          "000000",
          StatusValidacaoContaEnum.CONTA_INVALIDA,
          COD_CONTA_NAO_ENCONTRADA,
          "CONTA NÃO ENCONTRADA");
    }
    log.info(
        "valida-conta-recebedor -> Conta pagamento encontrada {}.", contaPagamentoOptional.get());

    ContaPagamento contaPagamento = contaPagamentoOptional.get();
    String codigoAgencia = contaPagamento.getIdInstituicao().toString();

    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());

    if (instituicaoPix != null) {
      if (!Constantes.STATUS_PIX_HABILITADO.equals(instituicaoPix.getHabilitado())) {
        log.info(
            "valida-conta-recebedor -> Conta produto pix desabilitado. {}",
            contaPagamento.getIdProdutoInstituicao());
        return criarResposta(
            codigoAgencia,
            validaContaRecebedorForm.getNroConta(),
            StatusValidacaoContaEnum.CONTA_INVALIDA,
            COD_PRODUTO_DESABILITADO,
            "PRODUTO PIX DESABILITADO");
      }

      if (CREDITO.equals(validaContaRecebedorForm.getTipoMovimento())) {
        List<Integer> codigosHabilitadosProduto =
            produtoTransacaoService.getCodTransacoesAdicionados(
                instituicaoPix.getIdProdutoInstituicao());
        if (codigosHabilitadosProduto.isEmpty()
            || !codigosHabilitadosProduto.contains(Constantes.COD_TRANSACAO_RECEBIMENTO_PIX)) {
          log.info(
              "valida-conta-recebedor -> Códigos de Transação não configurados para o Produto. {}",
              contaPagamento.getIdProdutoInstituicao());
          return criarResposta(
              codigoAgencia,
              validaContaRecebedorForm.getNroConta(),
              StatusValidacaoContaEnum.CONTA_INVALIDA,
              COD_PRODUTO_DESABILITADO,
              "PRODUTO PIX DESABILITADO");
        }
      }
    }

    if (STATUS_NAO_PERMITIDOS_RECEBER_PIX.contains(contaPagamento.getIdStatusConta())) {
      log.info(
          "valida-conta-recebedor -> Conta pagamento em status inválido. {}",
          contaPagamento.getIdStatusConta());
      return criarResposta(
          codigoAgencia,
          validaContaRecebedorForm.getNroConta(),
          StatusValidacaoContaEnum.CONTA_INVALIDA,
          COD_CONTA_INVALIDA,
          "CONTA BLOQUEADA");
    }

    if (validaContaRecebedorForm.getChaveEnderecamento() != null
        && !validaContaRecebedorForm.getChaveEnderecamento().isEmpty()
        && contaTransacionalChavesService
            .descobreChavePixAssociadaAConta(
                validaContaRecebedorForm.getChaveEnderecamento(), contaPagamento.getIdConta())
            .isEmpty()) {
      log.info(
          "valida-conta-recebedor -> Conta pagamento não associada à Chave PIX informada. {}",
          validaContaRecebedorForm.getChaveEnderecamento());
      return criarResposta(
          codigoAgencia,
          validaContaRecebedorForm.getNroConta(),
          StatusValidacaoContaEnum.CONTA_INVALIDA,
          COD_SITUACAO_NAO_PERMITE_CREDITO,
          "CHAVE NÃO PERTENCE A CONTA INFORMADA");
    }

    return criarResposta(
        codigoAgencia,
        validaContaRecebedorForm.getNroConta(),
        StatusValidacaoContaEnum.CONTA_VALIDA,
        null,
        null);
  }

  private ResponseEntity<ValidaContaResponse> criarResposta(
      String codAgencia,
      String nroConta,
      StatusValidacaoContaEnum status,
      String erroCodigo,
      String erroDescricao) {
    ValidaContaResponse validaContaResponse = new ValidaContaResponse();
    validaContaResponse.setCodAgencia(codAgencia);
    validaContaResponse.setNroConta(nroConta);
    validaContaResponse.setStatus(status);
    validaContaResponse.setTipoConta(TipoContaEnum.CACC);

    if (erroCodigo != null && erroDescricao != null) {
      validaContaResponse.setErros(
          Collections.singletonList(new ErroValidacaoContaVO(erroCodigo, erroDescricao)));
    }

    return new ResponseEntity<>(validaContaResponse, HttpStatus.OK);
  }

  public ResponseEntity<MovimentoResponse> tratarMovimentacaoDeConta(MovimentoForm movimentoForm) {

    log.info(movimentoForm.toString());
    JcardResponse jcardResponse;
    MovimentoResponse movimentoResponse;
    String codErro = verificarContaParaMovimentacao(movimentoForm);
    String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);

    try {
      // se movimento for do tipo debito, entao conta ja foi debitada
      if (movimentoForm.getTipoMovimento().equals(TipoMovimentoEnum.DEBITO)) {

        ContaTransacionalMovimento contaTransacionalMovimento;
        // para bloqueio devolucao especial
        if ((movimentoForm.getOrigemMovimento().equals("DEVOLUCAO")
                || movimentoForm.getOrigemMovimento().equals("DEVOLUCAO_ESPECIAL"))
            && movimentoForm.getUuidSolicitacaoDevolucaoEspecial() != null) {
          trataDesbloqueio(movimentoForm);
          jcardResponse =
              tratarMovimentoDeDebitoPIX(
                  movimentoForm, rrn, Constantes.COD_TRANSACAO_DEVOLUCAO_PIX_TOTAL, true);
          boolean success = jcardResponse.getSuccess().equals(Boolean.TRUE);
          MovimentoResponse movimentoFromJcardResponse =
              getMovimentoFromJcardResponse(success, codErro, movimentoForm, rrn);

          return new ResponseEntity<>(movimentoFromJcardResponse, HttpStatus.OK);
        } else if (movimentoForm.getOrigemMovimento().equals("DEVOLUCAO_PARCIAL")
            && movimentoForm.getUuidSolicitacaoDevolucaoEspecial() != null) {
          trataDesbloqueio(movimentoForm);
          jcardResponse =
              tratarMovimentoDeDebitoPIX(
                  movimentoForm, rrn, Constantes.COD_TRANSACAO_DEVOLUCAO_PIX_PARCIAL, true);
          boolean success = jcardResponse.getSuccess().equals(Boolean.TRUE);
          MovimentoResponse movimentoFromJcardResponse =
              getMovimentoFromJcardResponse(success, codErro, movimentoForm, rrn);

          return new ResponseEntity<>(movimentoFromJcardResponse, HttpStatus.OK);
        } else {

          if (movimentoForm.getOrigemMovimento().equals("DEVOLUCAO")
              || movimentoForm.getOrigemMovimento().equals("DEVOLUCAO_PARCIAL")) {
            Optional<ContaTransacionalDevolucao> devolucaoOptional =
                contaTransacionalDevolucaoService.findFirstByEndToEndDevolucao(
                    movimentoForm.getEndToEnd());
            if (!devolucaoOptional.isPresent()) {
              movimentoResponse =
                  new MovimentoResponse("99999", "Não foi possível realizar a operação", null);
              return new ResponseEntity<>(movimentoResponse, HttpStatus.BAD_REQUEST);
            }
            contaTransacionalMovimento =
                contaTransacionalMovimentoService.criarMovimento(
                    movimentoForm,
                    devolucaoOptional.get().getRrn(),
                    devolucaoOptional.get().getDataDevolucao());
          } else {
            Optional<ContaTransacionalPagamento> pagamentoOptional =
                contaTransacionalPagamentoService.findFirstByEndToEnd(movimentoForm.getEndToEnd());
            if (!pagamentoOptional.isPresent()) {
              movimentoResponse =
                  new MovimentoResponse("99999", "Não foi possível realizar a operação", null);
              return new ResponseEntity<>(movimentoResponse, HttpStatus.BAD_REQUEST);
            }
            contaTransacionalMovimento =
                contaTransacionalMovimentoService.criarMovimento(
                    movimentoForm,
                    pagamentoOptional.get().getRrn(),
                    pagamentoOptional.get().getDataPagamento());
          }
        }

        movimentoResponse =
            new MovimentoResponse(SEM_ERRO, null, contaTransacionalMovimento.getId());
        return new ResponseEntity<>(movimentoResponse, HttpStatus.OK);
      } else {

        if (!codErro.equals(SEM_ERRO)) {
          return new ResponseEntity<>(
              new MovimentoResponse(codErro, mensagemErro, null), HttpStatus.BAD_REQUEST);
        }

        // se movimento for do tipo credito, entao eh necessario creditar na conta do portador
        jcardResponse = tratarMovimentoDeCreditoPIX(movimentoForm, rrn);
        boolean success = jcardResponse.getSuccess().equals(Boolean.TRUE);

        if (success
            && movimentoForm.getOrigemMovimento().equals("DEVOLUCAO")
            && movimentoForm
                .getCodAgencia()
                .equals(Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.toString())) {

          RepasseInstitucional repasseInstitucional =
              repasseInstitucionalRepository.findByEndToEnd(movimentoForm.getEndToEndOriginal());
          Credencial credencialOrigem =
              credencialRepository.findUltimaCredencialTitularConta(
                  utilService.getIdContaRepasseRp3());
          Credencial credencialDestino =
              credencialRepository.findUltimaCredencialTitularConta(
                  repasseInstitucional.getIdContaOrigem());

          JcardResponse sucesso =
              realizarTransferenciaInterna(
                  repasseInstitucional,
                  movimentoForm.getValor(),
                  credencialOrigem,
                  credencialDestino,
                  movimentoForm.getOrigemMovimento().equals("DEVOLUCAO")
                          || movimentoForm.getOrigemMovimento().equals("DEVOLUCAO_ESPECIAL")
                      ? Constantes.COD_TRANSACAO_SOLICITACAO_DEVOLUCAO_PIX_TOTAL
                      : Constantes.COD_TRANSACAO_SOLICITACAO_DEVOLUCAO_PIX_PARCIAL);

          if (!sucesso.getSuccess()) {
            movimentoResponse = movimentarDevolucao(repasseInstitucional);
            repasseInstitucionalRepository.save(repasseInstitucional);
            return new ResponseEntity<>(movimentoResponse, HttpStatus.BAD_REQUEST);
          } else {
            saveRepasseInstitucional(movimentoForm, repasseInstitucional, sucesso);
          }
        }

        // Verifica se o crédito se trata de uma carga PIX
        b2bFaturaPixService.trataPagamentosDeCargasPIX(movimentoForm, success);

        int codigoTransacao =
            (movimentoForm.getOrigemMovimento().equals("DEVOLUCAO")
                    || movimentoForm.getOrigemMovimento().equals("DEVOLUCAO_ESPECIAL"))
                ? Constantes.COD_TRANSACAO_DEVOLUCAO_PIX_TOTAL
                : Constantes.COD_TRANSACAO_DEVOLUCAO_PIX_PARCIAL;
        Long numeroConta = getNumeroContaSemDV(movimentoForm.getNroConta());
        eventoService.publicarMovimentacaoFinanceiraEvent(
            Servicos.PIX.getDescricao(),
            null,
            Integer.valueOf(movimentoForm.getCodInstituicao()),
            numeroConta,
            null,
            null,
            codigoTransacao,
            movimentoForm.getValor(),
            "0");

        return new ResponseEntity<>(
            getMovimentoFromJcardResponse(success, codErro, movimentoForm, rrn),
            success ? HttpStatus.OK : HttpStatus.BAD_REQUEST);
      }
    } catch (GenericServiceException exception) {
      movimentoResponse =
          new MovimentoResponse("Ocorreu um erro inesperado", exception.getMensagem(), null);
      return new ResponseEntity<>(movimentoResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  private MovimentoResponse getMovimentoFromJcardResponse(
      boolean success, String codErro, MovimentoForm movimentoForm, String rrn) {

    if (success) {
      ContaTransacionalMovimento contaTransacionalMovimento =
          contaTransacionalMovimentoService.criarMovimento(movimentoForm, rrn, null);
      return new MovimentoResponse(codErro, null, contaTransacionalMovimento.getId());
    } else {
      MovimentoResponse movimentoResponse = new MovimentoResponse();
      movimentoResponse.setErro("99999");
      movimentoResponse.setMensagemErro("Não foi possível realizar operação");
      movimentoResponse.setNroMovimento(null);
      return movimentoResponse;
    }
  }

  private String verificarContaParaMovimentacao(MovimentoForm movimentoForm) {
    String numeroContaAsString = movimentoForm.getNroConta();
    Long numeroConta = getNumeroContaSemDV(numeroContaAsString);
    ContaPagamento contaPagamento =
        contaPagamentoService.findOneByIdContaAndIdRelacionamento(numeroConta);

    if (Objects.isNull(contaPagamento)) {
      mensagemErro = "Conta não encontrada";
      return COD_CONTA_NAO_ENCONTRADA;
    }

    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());
    if (!Objects.isNull(instituicaoPix)
        && !Constantes.STATUS_PIX_HABILITADO.equals(instituicaoPix.getHabilitado())) {
      mensagemErro = "Situação da conta não permite movimentação";
      return COD_PRODUTO_DESABILITADO;
    }

    if (STATUS_NAO_PERMITIDOS_RECEBER_PIX.contains(contaPagamento.getIdStatusConta())) {
      mensagemErro = "A CONTA NÃO ESTÁ ATIVA";
      return COD_CONTA_INVALIDA;
    }

    if (movimentoForm.getTipoMovimento().equals(TipoMovimentoEnum.CREDITO)) {
      Optional<ContaTransacionalMovimento> contaTransacionalMovimentoOptional;
      contaTransacionalMovimentoOptional =
          contaTransacionalMovimentoService.findFirstByEndToEndAndTipoMovimento(
              movimentoForm.getEndToEnd(), movimentoForm.getTipoMovimento());
      if (contaTransacionalMovimentoOptional.isPresent()) {
        mensagemErro = "End To End duplicado";
        return COD_END_TO_END_DUPLICADO;
      }
    }

    return SEM_ERRO;
  }

  @SuppressWarnings("SameParameterValue")
  private JcardResponse tratarMovimentoDeDebitoPIX(
      MovimentoForm movimentoForm, String rrn, int codTransacao, boolean isDevolucaoEspecial) {
    String textoExtrato =
        isDevolucaoEspecial
            ? "Devolução Especial - Bloqueio"
            : String.format("PIX enviado para %s", movimentoForm.getNomeContraParte());
    return movimentarContaPix(
        movimentoForm.getNroConta(), movimentoForm.getValor(), textoExtrato, rrn, codTransacao, -1);
  }

  private JcardResponse tratarMovimentoDeCreditoPIX(MovimentoForm movimentoForm, String rrn) {
    String textoExtrato = String.format("PIX recebido de %s", movimentoForm.getNomeContraParte());
    return movimentarContaPix(
        movimentoForm.getNroConta(),
        movimentoForm.getValor(),
        textoExtrato,
        rrn,
        Constantes.COD_TRANSACAO_RECEBIMENTO_PIX,
        1);
  }

  private JcardResponse movimentarContaPix(
      String nroContaComDV,
      BigDecimal valor,
      String textoExtrato,
      String rrn,
      int codTransacao,
      int sinal) {
    Long numeroConta = getNumeroContaSemDV(nroContaComDV);
    ContaPagamento contaPagamento =
        contaPagamentoService.findOneByIdContaAndIdRelacionamento(numeroConta);
    ProdutoInstituicao produtoInstituicao = contaPagamento.getProdutoInstituicao();
    ProdutoInstituicaoConfiguracao produto =
        produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0);

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaPagamento, produto);

    boolean isProdutoMoeda =
        Constantes.CODIGO_MOEDA_DE_PONTO.equals(produto.getMoeda().getIdMoeda());
    Optional<CotacaoPontos> cotacaoPontosOptional =
        Optional.ofNullable(
            cotacaoPontosRepository.findByIdInstituicao(contaPagamento.getIdInstituicao()));
    CotacaoPontos cotacaoPontos = cotacaoPontosOptional.orElseGet(CotacaoPontos::new);
    Integer stan = lancamentoService.getStan();
    Credencial credencial =
        credencialService.buscarCredencialParaLancamentoManual(contaPagamento.getIdConta());
    JcardResponse response =
        lancamentoService.doLancamentoManual(
            accountCode,
            codTransacao,
            isProdutoMoeda ? valor.multiply(cotacaoPontos.getValorConversao()) : valor,
            produto.getMoeda().getIdMoeda().toString(),
            rrn,
            sinal,
            credencial.getTokenInterno(),
            contaPagamento.getIdConta(),
            textoExtrato,
            null,
            null,
            null,
            stan,
            null);

    eventoService.publicarMovimentacaoFinanceiraEvent(
        Servicos.PIX.getDescricao(),
        contaPagamento.getIdInstituicao(),
        null,
        contaPagamento.getIdConta(),
        null,
        null,
        codTransacao,
        valor,
        "0");

    return response;
  }

  private Long getNumeroContaSemDV(String nroConta) {
    return Long.valueOf(nroConta.substring(0, nroConta.length() - 1));
  }

  public ResponseEntity<BloqueioResponse> bloqueioConta(
      BloqueioForm bloqueioForm, Boolean isDevolucaoEspecial)
      throws InvocationTargetException, IllegalAccessException {

    Long idContaPagamento = getNumeroContaSemDV(bloqueioForm.getNroConta());
    Optional<ContaTransacional> contaTransacionalOptional =
        contaTransacionalService.findByIdConta(idContaPagamento);
    ContaTransacionalBloqueio contaTransacionalBloqueio = null;

    GetSaldoConta saldoConta = contaPagamentoService.getSaldoConta(idContaPagamento);

    if (!contaTransacionalOptional.isPresent()) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              60015, "", StatusBloqueioEnum.FALHA_BLOQUEIO.name(), "CONTA INVÁLIDA"),
          HttpStatus.CONFLICT);
    }
    ContaTransacional contaTransacional =
        contaTransacionalOptional.orElseThrow(
            () -> new GenericServiceException("Conta não encontrada"));
    Boolean inMais =
        Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(contaTransacional.getAgencia());

    if (saldoConta.getSaldoDisponivel().compareTo(BigDecimal.ZERO) <= 0) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              60168,
              "",
              StatusBloqueioEnum.FALHA_BLOQUEIO.name(),
              "SALDO DO CLIENTE EM CONTA INSUFICIENTE"),
          HttpStatus.CONFLICT);
    }

    if (bloqueioForm.getCodAgencia() == null) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              60062, "", StatusBloqueioEnum.FALHA_BLOQUEIO.name(), "AGÊNCIA NÃO CADASTRADA"),
          HttpStatus.CONFLICT);
    }

    if (bloqueioForm.getMotivoMED() != null
        && (!bloqueioForm.getMotivoMED().equals(MotivoMEDEnum.FRAUD)
            && !bloqueioForm.getMotivoMED().equals(MotivoMEDEnum.OPERATIONAL_FLAW)
            && !bloqueioForm.getMotivoMED().equals(MotivoMEDEnum.REFUND_CANCELLED))) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              61308,
              "",
              StatusBloqueioEnum.FALHA_BLOQUEIO.name(),
              "MOTIVO DE BLOQUEIO NÃO CADASTRADO"),
          HttpStatus.CONFLICT);
    }

    if (bloqueioForm.getCodUsuario() == null) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              61309, "", StatusBloqueioEnum.FALHA_BLOQUEIO.name(), "USUÁRIO NÃO INFORMADO"),
          HttpStatus.CONFLICT);
    }

    if (bloqueioForm.getEndToEnd() != null) {
      contaTransacionalBloqueio =
          contaTransacionalBloqueioService.findByEndToEnd(bloqueioForm.getEndToEnd());
    }

    if (contaTransacionalBloqueio == null && bloqueioForm.getEndToEndOriginal() != null) {
      contaTransacionalBloqueio =
          contaTransacionalBloqueioService.findByEndToEndOriginal(
              bloqueioForm.getEndToEndOriginal());
    }

    if (contaTransacionalBloqueio == null
        && bloqueioForm.getUuidBloqueioDevolucaoEspecial() != null) {
      contaTransacionalBloqueio =
          contaTransacionalBloqueioService.findByUuidBloqueioDevolucaoEspecial(
              bloqueioForm.getUuidBloqueioDevolucaoEspecial());
    }

    if (contaTransacionalBloqueio != null) {
      if (!contaTransacionalBloqueio.getDesbloqueado()) {
        return new ResponseEntity<>(
            new BloqueioResponse(
                0, contaTransacionalBloqueio.getId(), StatusBloqueioEnum.BLOQUEADO.name(), ""),
            HttpStatus.OK);
      } else {
        return new ResponseEntity<>(
            new BloqueioResponse(
                999999,
                contaTransacionalBloqueio.getId(),
                StatusBloqueioEnum.DESBLOQUEADO.name(),
                "ESTA TRANSACAO JÁ FOI BLOQUEADA E DESBLOQUEADA ANTERIORMENTE"),
            HttpStatus.CONFLICT);
      }
    }

    contaTransacionalBloqueio = new ContaTransacionalBloqueio();
    BigDecimal valorABloquear = bloqueioForm.getValor();
    if (trataValorInMais(saldoConta.getSaldoDisponivel(), inMais)
            .subtract(valorABloquear)
            .compareTo(BigDecimal.ZERO)
        < 0) {
      valorABloquear = trataValorInMais(saldoConta.getSaldoDisponivel(), inMais);
    }

    // bloqueia o valor na conta por suspeita de fraude
    String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);
    String textoExtrato = "Bloqueio para prevenção de fraude";
    JcardResponse jcardResponse =
        movimentarContaPix(
            bloqueioForm.getNroConta(),
            valorABloquear,
            textoExtrato,
            rrn,
            Constantes.COD_TRANSACAO_PIX_BLOQUEIO_SISTEMA,
            -1);
    if (jcardResponse.getSuccess().equals(Boolean.FALSE)) {
      throw new GenericServiceException("Erro ao bloquear valor");
    }

    BeanUtils.copyProperties(contaTransacionalBloqueio, bloqueioForm);
    contaTransacionalBloqueio.setContaTransacional(contaTransacional);
    contaTransacionalBloqueio.setValorTotalBloqueado(bloqueioForm.getValor());
    contaTransacionalBloqueio.setValorInicialRetido(valorABloquear);
    contaTransacionalBloqueio.setRrnBloqueio(rrn);
    contaTransacionalBloqueio.setDtHrInclusao(LocalDateTime.now());
    contaTransacionalBloqueio.setDevolucaoEspecial(isDevolucaoEspecial);
    contaTransacionalBloqueioService.save(contaTransacionalBloqueio);

    return new ResponseEntity<>(
        new BloqueioResponse(
            0, contaTransacionalBloqueio.getId(), StatusBloqueioEnum.BLOQUEADO.name(), ""),
        HttpStatus.OK);
  }

  public ResponseEntity<BloqueioResponse> desbloqueioConta(DesbloqueioForm desbloqueioForm) {

    Long idConta = getNumeroContaSemDV(desbloqueioForm.getNroConta());
    Optional<ContaTransacional> contaTransacionalOptional =
        contaTransacionalService.findByIdConta(idConta);

    if (!contaTransacionalOptional.isPresent()) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              60211,
              "",
              StatusBloqueioEnum.FALHA_DESBLOQUEIO.name(),
              "CONTA CORRENTE NÃO CADASTRADA"),
          HttpStatus.CONFLICT);
    }

    if (desbloqueioForm.getCodUsuario() == null) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              61309, "", StatusBloqueioEnum.FALHA_DESBLOQUEIO.name(), "USUÁRIO NÃO INFORMADO"),
          HttpStatus.CONFLICT);
    }

    ContaTransacionalBloqueio contaTransacionalBloqueio =
        buscaContaTransacionalBloqueio(
            desbloqueioForm.getEndToEnd(),
            desbloqueioForm.getEndToEndOriginal(),
            desbloqueioForm.getUuidBloqueioDevolucaoEspecial(),
            desbloqueioForm.getUuidSolicitacaoDevolucaoEspecial(),
            desbloqueioForm.getChaveBloqueio());

    if (contaTransacionalBloqueio == null) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              60859, "", StatusBloqueioEnum.FALHA_DESBLOQUEIO.name(), "NÃO EXISTE VALOR BLOQUEADO"),
          HttpStatus.CONFLICT);
    }

    if (contaTransacionalBloqueio.getDesbloqueado().equals(Boolean.TRUE)) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              0, contaTransacionalBloqueio.getId(), StatusBloqueioEnum.DESBLOQUEADO.name(), ""),
          HttpStatus.OK);
    }

    if (contaTransacionalBloqueio.getValor().compareTo(desbloqueioForm.getValor()) < 0) {
      return new ResponseEntity<>(
          new BloqueioResponse(
              60858,
              "",
              StatusBloqueioEnum.FALHA_DESBLOQUEIO.name(),
              "VALOR A DESBLOQUEAR NÃO PODE SER MAIOR QUE O VALOR TOTAL BLOQUEADO PARA ESTE MOTIVO"),
          HttpStatus.CONFLICT);
    }

    // desbloqueia valor em conta
    desbloqueiaValorEmConta(
        desbloqueioForm.getNroConta(), desbloqueioForm.getValor(), contaTransacionalBloqueio);

    return new ResponseEntity<>(
        new BloqueioResponse(
            0, contaTransacionalBloqueio.getId(), StatusBloqueioEnum.DESBLOQUEADO.name(), ""),
        HttpStatus.OK);
  }

  private void trataDesbloqueio(MovimentoForm movimentoForm) {
    if (movimentoForm.getMotivoMED() != null
        && movimentoForm.getMotivoMED().equals(MotivoMEDEnum.FRAUD)) {
      ContaTransacionalBloqueio contaTransacionalBloqueio =
          buscaContaTransacionalBloqueio(
              movimentoForm.getEndToEnd(),
              movimentoForm.getEndToEndOriginal(),
              movimentoForm.getUuidBloqueioDevolucaoEspecial(),
              movimentoForm.getUuidSolicitacaoDevolucaoEspecial(),
              movimentoForm.getChaveBloqueio());
      if (contaTransacionalBloqueio != null && !contaTransacionalBloqueio.getDesbloqueado()) {
        desbloqueiaValorEmConta(
            movimentoForm.getNroConta(), movimentoForm.getValor(), contaTransacionalBloqueio);
      }
    }
  }

  private ContaTransacionalBloqueio buscaContaTransacionalBloqueio(
      String endToEnd,
      String endToEndOriginal,
      String uuidBloqueioDevolucaoEspecial,
      String uuidSolicitacaoDevolucaoEspecial,
      String chaveBloqueio) {
    ContaTransacionalBloqueio contaTransacionalBloqueio = null;

    if (uuidBloqueioDevolucaoEspecial != null) {
      contaTransacionalBloqueio =
          contaTransacionalBloqueioService.findByUuidBloqueioDevolucaoEspecial(
              uuidBloqueioDevolucaoEspecial);
    }

    if (contaTransacionalBloqueio == null && uuidSolicitacaoDevolucaoEspecial != null) {
      contaTransacionalBloqueio =
          contaTransacionalBloqueioService.findByUuidBloqueioDevolucaoEspecial(
              uuidSolicitacaoDevolucaoEspecial);
    }

    if (contaTransacionalBloqueio == null && chaveBloqueio != null) {
      contaTransacionalBloqueio = contaTransacionalBloqueioService.findById(chaveBloqueio);
    }

    if (contaTransacionalBloqueio == null && endToEndOriginal != null) {
      contaTransacionalBloqueio =
          contaTransacionalBloqueioService.findByEndToEndOriginal(endToEndOriginal);
    }

    if (contaTransacionalBloqueio == null && endToEnd != null) {
      contaTransacionalBloqueio = contaTransacionalBloqueioService.findByEndToEnd(endToEnd);
    }
    return contaTransacionalBloqueio;
  }

  private void desbloqueiaValorEmConta(
      String nroConta, BigDecimal valor, ContaTransacionalBloqueio contaTransacionalBloqueio) {

    BigDecimal restante = contaTransacionalBloqueio.getValor().subtract(valor);

    if (restante.compareTo(BigDecimal.ZERO) < 0) {
      ResponseEntity<BloqueioResponse> response =
          new ResponseEntity<>(
              new BloqueioResponse(
                  60858,
                  "",
                  StatusBloqueioEnum.FALHA_DESBLOQUEIO.name(),
                  "VALOR A DESBLOQUEAR NÃO PODE SER MAIOR QUE O VALOR TOTAL BLOQUEADO PARA ESTE MOTIVO"),
              HttpStatus.CONFLICT);
      throw new GenericServiceException(
          "VALOR A DESBLOQUEAR NÃO PODE SER MAIOR QUE O VALOR TOTAL BLOQUEADO PARA ESTE MOTIVO",
          response,
          HttpStatus.CONFLICT);
    }

    if (contaTransacionalBloqueio
            .getValorTotalBloqueado()
            .compareTo(contaTransacionalBloqueio.getValor())
        == 0) {
      String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);
      JcardResponse jcardResponse =
          movimentarContaPix(
              nroConta,
              contaTransacionalBloqueio.getValorInicialRetido(),
              "Desbloqueio de valor Pix",
              rrn,
              Constantes.COD_TRANSACAO_EST_PIX_BLOQUEIO_SISTEMA,
              1);

      if (jcardResponse.getSuccess().equals(Boolean.FALSE)) {
        throw new GenericServiceException("Erro ao realizar desbloqueio");
      }
      contaTransacionalBloqueio.setRrnDesbloqueio(rrn);
    }

    contaTransacionalBloqueio.setValor(restante);
    contaTransacionalBloqueio.setDesbloqueado(restante.compareTo(BigDecimal.ZERO) == 0);
    contaTransacionalBloqueio.setDtHrManutencao(LocalDateTime.now());
    contaTransacionalBloqueioService.save(contaTransacionalBloqueio);
  }

  private BigDecimal trataValorInMais(BigDecimal valor, Boolean inMais) {
    return inMais ? valor.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) : valor;
  }

  public ExtratoResponse contaTransacionalMovimentoToExtratoResponse(
      List<ContaTransacionalMovimento> contaTransacionalMovimento) {
    List<MovimentoInfoVO> movimentos = new ArrayList<>();
    for (ContaTransacionalMovimento obj :
        contaTransacionalMovimento.stream()
            .sorted(Comparator.comparing(ContaTransacionalMovimento::getDtHrInclusao).reversed())
            .collect(Collectors.groupingBy(ContaTransacionalMovimento::getEndToEnd))
            .values()
            .stream()
            .flatMap(group -> group.stream().limit(1))
            .collect(Collectors.toList())) {
      MovimentoInfoVO temp = new MovimentoInfoVO();
      temp.setInstrucoesDevolucao(new InstrucoesDevolucao());
      temp.setOrigemMovimento(new OrigemMovimento());

      temp.setCampoLivre(obj.getCampoLivre());
      temp.setData(obj.getDtHrInclusao());
      temp.setDescricao(obj.getCodOperacao());
      temp.setDevolvido(obj.getCodigoMotivoDevolucao() != null);
      temp.setEndToEnd(obj.getEndToEnd());
      temp.setEndToEndOriginal(obj.getEndToEndOriginal());
      temp.setNatureza(obj.getTipoMovimento().toString().substring(0, 1));
      temp.setNumeroEbank(obj.getIdIdempotenteEbank());
      temp.setOrigem(obj.getOrigemMovimento());
      temp.setReferenciaInterna(obj.getReferenciaInterna());
      temp.setValor(obj.getValor());

      if (temp.getNatureza().equals("C")) {
        List<ContaTransacionalDevolucao> contaTransacionalDevolucao =
            contaTransacionalDevolucaoRepository.findByEndToEndRecebimentoAndEstornado(
                obj.getEndToEnd(), false);
        BigDecimal valorDevolucaoTotal = new BigDecimal(0);

        for (ContaTransacionalDevolucao devolucao : contaTransacionalDevolucao) {
          valorDevolucaoTotal = valorDevolucaoTotal.add(devolucao.getValor());
        }
        if (valorDevolucaoTotal.compareTo(obj.getValor()) == 0
            || temp.getOrigem().equalsIgnoreCase("DEVOLUCAO")) {
          temp.getInstrucoesDevolucao().setPermiteDevolucao(false);
          temp.getInstrucoesDevolucao()
              .setValorPermitido(obj.getValor().subtract(valorDevolucaoTotal));
        } else {
          temp.getInstrucoesDevolucao().setPermiteDevolucao(true);
          temp.getInstrucoesDevolucao()
              .setValorPermitido(obj.getValor().subtract(valorDevolucaoTotal));
        }
      } else {
        temp.getInstrucoesDevolucao().setPermiteDevolucao(false);
        temp.getInstrucoesDevolucao().setValorPermitido(obj.getValor());
      }

      ParticipantesIspb participantesIspb =
          participantesIspbRepository.findByCodIspb(Long.parseLong(obj.getCodIspbContraParte()));
      temp.getOrigemMovimento()
          .setNomeInstituicao(participantesIspb != null ? participantesIspb.getRazaoSocial() : "");

      temp.getOrigemMovimento().setAgencia(obj.getCodAgenciaContraParte());
      temp.getOrigemMovimento().setInscricaoNacional(obj.getDocumentoContraParte());
      temp.getOrigemMovimento().setIspbInstituicao(obj.getCodIspbContraParte());
      temp.getOrigemMovimento().setNome(obj.getNomeContraParte());
      temp.getOrigemMovimento().setNumero(obj.getNroContaContraParte());

      temp.setConta(new ContaVO());
      temp.getConta().setAgencia(obj.getCodAgencia());
      temp.getConta().setIdConta(obj.getNroConta());
      temp.getConta().setNome(obj.getNome());
      temp.getConta().setInscricaoNacional(obj.getDocumento());
      ParticipantesIspb participanteContaOrigem =
          participantesIspbRepository.findByCodIspb(Long.parseLong(obj.getCodInstituicao()));
      temp.getConta()
          .setNomeInstituicao(
              participanteContaOrigem != null ? participanteContaOrigem.getRazaoSocial() : "");

      movimentos.add(temp);
    }

    movimentos.sort(
        new Comparator<MovimentoInfoVO>() {
          @Override
          public int compare(MovimentoInfoVO extrato1, MovimentoInfoVO extrato2) {
            return extrato2.getData().compareTo(extrato1.getData());
          }
        });

    ExtratoResponse response = new ExtratoResponse();
    response.setValue(new ExtratoInfoVO());
    response.getValue().setConta(new ContaVO());
    response.getValue().setMovimentos(movimentos);

    ParticipantesIspb participantesIspb =
        participantesIspbRepository.findByCodIspb(
            Long.parseLong(contaTransacionalMovimento.get(0).getCodInstituicao()));
    response
        .getValue()
        .getConta()
        .setNomeInstituicao(participantesIspb != null ? participantesIspb.getRazaoSocial() : "");
    response.getValue().getConta().setAgencia(contaTransacionalMovimento.get(0).getCodAgencia());
    response
        .getValue()
        .getConta()
        .setInscricaoNacional(contaTransacionalMovimento.get(0).getChaveEnderecamento());
    response
        .getValue()
        .getConta()
        .setIspbParticipante(contaTransacionalMovimento.get(0).getCodIspbContraParte());
    response.getValue().getConta().setIdConta(contaTransacionalMovimento.get(0).getNroConta());

    response.setFailure(false);
    response.setSuccess(true);

    return response;
  }

  public ComunicadoContaViaPush getComunicadoPushPortabilidade(
      Credencial item, Long idCredencial, NotificacaoReivindicacaoForm req) {

    ComunicadoContaViaPush comunicado = new ComunicadoContaViaPush();
    comunicado.setIdConta(item.getIdConta());
    comunicado.setIdCredencial(idCredencial);
    comunicado.setIdGateway(ID_ONE_SIGNAL);
    comunicado.setMensagem(
        "Existe solicitação de portabilidade de chave PIX do tipo "
            + req.getKeyType()
            + " para outra conta. "
            + "Acesse sua área Pix para aceitar ou negar este pedido.");
    comunicado.setTipoEventoConta(ENVIO_PUSH_PORTABILIDADE);

    return comunicado;
  }

  @Async
  public void enviarNotificacaoPush(ComunicadoContaViaPush comunicado) {
    comunicadorPushService.prepararComunicacao(comunicado);
    comunicadorPushService.comunicar(comunicado.getIdLogEventoConta());
  }

  public InfosPixVO getInformacoesPix(Long idConta, SecurityUser user) {

    ContaPagamento contaPagamento = contaPagamentoService.findByIdNotNull(idConta);
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, contaPagamento);

    InfoContaTransacionalPixIssuerVO contaTransacional =
        contaTransacionalService.findOneByIdConta(idConta);
    if (contaTransacional == null) {
      throw new GenericServiceException("Conta Transacional não encontrada ou inexistente.");
    }

    List<InfosChavesPixIssuerVO> chaves =
        contaTransacionalChavesRepository.findContaTransacionalChavesByIdContaTransacional(
            contaTransacional.getId());
    for (InfosChavesPixIssuerVO chave : chaves) {
      if (Boolean.TRUE.equals(chave.getReivindicada())) {
        String statusReivindicacao =
            contaTransacionalReivindicacaoService.getStatusReivindicacaoByIdContaTransacional(
                contaTransacional.getId(), chave.getValorChave());
        chave.setStatusReivindicacao(statusReivindicacao);
      }
    }

    List<InfosPixSolicitacaoLimiteIssuerVO> solicitacaoLimitePix =
        solicitarLimitePixService.findByIdConta(idConta);
    for (InfosPixSolicitacaoLimiteIssuerVO limitePix : solicitacaoLimitePix) {
      AcessoUsuario usuario = usuarioService.findByIdUsuario(limitePix.getIdUsuarioAutorizacao());
      limitePix.setUsuarioAutorizacao(usuario == null ? "Sem autorizador" : usuario.getNome());
    }

    InfosPixVO infosPixVO = new InfosPixVO();
    infosPixVO.setContaTransacionalPixIssuerVO(contaTransacional);
    infosPixVO.setChaves(chaves);
    infosPixVO.setSolicitacaoLimitePix(solicitacaoLimitePix);
    return infosPixVO;
  }

  public ContaTransacionalMovimento findTransacaoPixByRrn(String rrn) {
    return contaTransacionalMovimentoService.findTransacaoPixByRrn(rrn);
  }

  private JcardResponse realizarTransferenciaInterna(
      RepasseInstitucional repasseInstitucional,
      BigDecimal valor,
      Credencial credencialOrigem,
      Credencial credencialDestino,
      Integer codTransacao) {
    JcardResponse sucesso =
        contaPagamentoFacade.realizarTransferencia(
            repasseInstitucional.getIdInstituicao(),
            credencialOrigem.getIdCredencial(),
            null,
            valor,
            null,
            false,
            false,
            credencialDestino,
            codTransacao,
            null,
            null,
            null);
    return sucesso;
  }

  private static MovimentoResponse movimentarDevolucao(RepasseInstitucional repasseInstitucional) {
    MovimentoResponse movimentoResponse;
    if (repasseInstitucional != null) {
      repasseInstitucional.setTransferenciaInternaSucesso(false);
      repasseInstitucional.setTransferenciaInternaMensagem("Não foi possível realizar operação");
    }
    movimentoResponse =
        new MovimentoResponse("99999", "Não foi possível realizar a operação", null);
    return movimentoResponse;
  }

  private void saveRepasseInstitucional(
      MovimentoForm movimentoForm,
      RepasseInstitucional repasseInstitucional,
      JcardResponse sucesso) {
    Optional<ContaTransacionalDevolucao> contaTransacionalDevolucao =
        contaTransacionalDevolucaoService.findFirstByEndToEndDevolucao(movimentoForm.getEndToEnd());
    repasseInstitucional.setTransferenciaInternaSucesso(true);
    repasseInstitucional.setIdContaTransacionalDevolucao(contaTransacionalDevolucao.get().getId());
    repasseInstitucionalRepository.save(repasseInstitucional);
  }
}
