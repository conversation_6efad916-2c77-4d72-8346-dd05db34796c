package br.com.sinergico.service.jcard;

import static br.com.sinergico.service.cadastral.PortadorLoginService.USUARIO_PORTADOR;
import static br.com.sinergico.util.Constantes.ID_PROCESSADORA_ITS_PAY;

import br.com.entity.gatewaypagto.LogPagtoTituloTransacao;
import br.com.entity.jcard.LogTransacoes;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.ControleGarantia;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.transacional.CodigoTransacao;
import br.com.entity.transacional.LancamentoAuto;
import br.com.entity.transacional.LancamentoManual;
import br.com.entity.transacional.TED;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.DebConvenioNaoReestabelecidoResponse;
import br.com.json.bean.jcard.AutorizacaoPendenteResponse;
import br.com.json.bean.jcard.TransacaoDePontoResponse;
import br.com.json.bean.loyalty.JoySaldoDisponivelResponse;
import br.com.json.bean.suporte.RelatorioTransacoesNaoReestabelecidasRequest;
import br.com.json.bean.transacional.DetalhesMotivoEstornoRequest;
import br.com.json.bean.transacional.DetalhesMotivoEstornoResponse;
import br.com.json.bean.transacional.TransacaoEstornadaResponse;
import br.com.json.bean.transacional.TransacoesByPeriodoResponse;
import br.com.json.bean.transacional.TransacoesPJB2BByPeriodoResponse;
import br.com.sinergico.enums.MotivoTEDEnum;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloTransacaoRepository;
import br.com.sinergico.repository.jcard.LogTransacoesRepository;
import br.com.sinergico.repository.jcard.impl.LogTransacoesRepositoryImpl;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.DebFolhaConvEmpresaService;
import br.com.sinergico.service.pix.PixService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.transacional.CodigoTransacaoService;
import br.com.sinergico.service.transacional.ControleGarantiaService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.service.transacional.TEDService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.vo.LogTransacoesVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class LogTransacoesService extends GenericService<LogTransacoes, Long> {

  private static final Logger log = LoggerFactory.getLogger(LogTransacoesService.class);

  private LogTransacoesRepository repository;

  @Autowired LogTransacoesRepositoryImpl logTransacoesRepositoryImpl;

  @Autowired ParametroValorService parametroValorService;

  @Autowired ControleGarantiaService controleGarantiaService;

  @Autowired private DebFolhaConvEmpresaService debFolhaConvEmpresaService;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private LogPagtoTituloTransacaoRepository logPagtoTransacaoRepository;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private CodigoTransacaoService codigoTransacaoService;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private PixService pixService;

  @Autowired private TEDService tedService;

  @Autowired
  public LogTransacoesService(LogTransacoesRepository repo) {
    super(repo);
    this.repository = repo;
  }

  public List<AutorizacaoPendenteResponse> buscarAutorizacoesPendentes(
      SecurityUser user, Long idConta) {

    consultaRestritaService.checaPrivilegio(idConta, user);

    return repository.findByLogTransacoesAutorizacoesPendentes(idConta);
  }

  public JoySaldoDisponivelResponse getPontosDaJoyDisponiveisParaTransferencia(Long idConta) {
    return new JoySaldoDisponivelResponse(
        repository.findPontosDaJoyDisponiveisParaTransferencia(idConta));
  }

  public Long findIdLogTransacoesByRrn(String rrn) {
    return repository.findIdLogTransacoesByRrn(rrn);
  }

  public Integer findFunctionCodeByRrn(String rrn) {
    return repository.findFunctionCodeByRrn(rrn);
  }

  public LogTransacoes findByRrn(String rrn) {
    return repository.findByRrn(rrn);
  }

  public LogTransacoes findByIdTranlogOrQrCodeTransactionIdIsQrCodeElo(
      Long id, String transactionId) {
    return repository.findByIdTranlogOrQrCodeTransactionIdIsQrCodeElo(id, transactionId);
  }

  public LogTransacoesVO findTransacaoByRrn(String rrn) {
    return repository.findTransacaoByRrn(rrn);
  }

  public LogTransacoes findByRrnAndSsAndDia(String rrn, String ss, String dia) {
    return repository.findByRrnAndSsAndDia(rrn, ss, dia);
  }

  public List<TransacoesByPeriodoResponse> findTransacoesNaoReestabelecidasByDebConvenio(
      SecurityUser user, RelatorioTransacoesNaoReestabelecidasRequest model) {

    List<TransacoesByPeriodoResponse> response = new ArrayList<TransacoesByPeriodoResponse>();

    List<DebConvenioNaoReestabelecidoResponse> debs =
        debFolhaConvEmpresaService.findDebsNaoReestabelecidos(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento(),
            model.getIdProdInstituicao());

    debs.forEach(
        c -> {
          response.addAll(
              repository.findTransacoesByHierarquiaAndProdutoAndPeriodo(
                  c.getIdProcessadora(),
                  c.getIdInstituicao(),
                  c.getIdRegional(),
                  c.getIdFilial(),
                  c.getIdPontoDeRelacionamento(),
                  model.getIdProdInstituicao(),
                  model.getDocumento(),
                  c.getDataInicio(),
                  c.getDataFim()));
        });

    return response;
  }

  public Long findIdLogTransacoesQueEstornouRrn(String rrn) {
    return repository.findIdLogTransacoesQueEstornouRrn(rrn);
  }

  public TransacaoDePontoResponse getTransacaoEstornoDePontoByRrn(String rrn) {

    TransacaoDePontoResponse transacao = null;
    Long idEstorno = 0L;
    idEstorno = repository.findIdLogTransacoesQueEstornouRrn(rrn);

    if (idEstorno != 0L) {
      transacao = repository.findTransacaoDePontoById(idEstorno);
    }
    if (idEstorno == 0L || transacao != null) {
      transacao = lancamentoService.getTransacaoDePontoEstornado(rrn);
    }

    return transacao;
  }

  public TransacaoEstornadaResponse getTransacaoEstornoadaByIdTranlog(Long idTranlog) {

    TransacaoEstornadaResponse transacao = repository.findTransacaoEstornadaByIdTranlog(idTranlog);

    if (transacao == null) {
      throw new GenericServiceException(
          "Não foi possível localizar a transação original deste estorno...");
    } else {
      transacao.setDataTransacaoStr();
    }

    return transacao;
  }

  public List<TransacoesPJB2BByPeriodoResponse> findTransacoesPJB2BByHierarquiaAndPeriodo(
      SecurityUser user, Date dataInicio, Date dataFim) {

    return repository.findTransacoesPJB2BByHierarquiaAndPeriodo(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdRegional(),
        user.getIdFilial(),
        user.getIdPontoDeRelacionamento(),
        dataInicio,
        dataFim);
  }

  public Boolean atualizarSaldoInicioDiaDeContasGarantia(
      String dateTimeInicio, String dateTimeFim) {
    log.info("INÍCIO atualização por ENDPOINT de saldo início dia das Contas Garantia");
    log.info("Data/Horário: " + LocalDateTime.now() + "\n");
    List<ParametroValor> listParametros =
        parametroValorService.getParametrosByDescricaoChaveParametro(
            Constantes.DESC_CHAVE_PARAMETRO_CONTA_GARANTIA);
    List<String> functionCodes = getFunctionCodesFromParametro(listParametros.get(0));
    List<Integer> idsInstituicoes = getIdsInstituicoesFromParametro(listParametros.get(1));
    List<ControleGarantia> listControleGarantia =
        controleGarantiaService.findByIdProcessadoraAndIdInstituicaoDependenteIn(
            ID_PROCESSADORA_ITS_PAY, idsInstituicoes);
    for (ControleGarantia controleGarantia : listControleGarantia) {
      logInfoControleGarantiaAntesAtualizacao(controleGarantia);
      BigDecimal saldoCalculado = controleGarantia.getSaldoInicioDia();
      saldoCalculado = somarAportes(saldoCalculado, controleGarantia);
      zerarAportes(controleGarantia);
      saldoCalculado =
          processarTransacoesPorFunctionCode(
              controleGarantia.getIdInstituicaoDependente(),
              functionCodes,
              saldoCalculado,
              dateTimeInicio,
              dateTimeFim);
      controleGarantia.setSaldoInicioDia(saldoCalculado);
      controleGarantia.setDataHoraManutencao(LocalDateTime.now());
      log.info("Saldo DEPOIS dos cálculos: R$" + controleGarantia.getSaldoInicioDia() + "\n");
    }
    controleGarantiaService.salvarControlesGarantia(listControleGarantia);
    log.info("FIM atualização por ENDPOINT de saldo início dia das Contas Garantia");
    return true;
  }

  private void logInfoControleGarantiaAntesAtualizacao(ControleGarantia controleGarantia) {
    log.info("Instituicao: " + controleGarantia.getIdInstituicaoDependente());
    log.info("Última modificação: " + controleGarantia.getDataHoraManutencao().toString());
    log.info("Saldo início dia antes dos cálculos: R$" + controleGarantia.getSaldoInicioDia());
    log.info(
        "Aporte 1 R$"
            + (controleGarantia.getAporte1() == null
                ? "0.00"
                : controleGarantia.getAporte1().toString()));
    log.info(
        "Aporte 2 R$"
            + (controleGarantia.getAporte2() == null
                ? "0.00"
                : controleGarantia.getAporte2().toString()));
    log.info(
        "Aporte 3 R$"
            + (controleGarantia.getAporte3() == null
                ? "0.00"
                : controleGarantia.getAporte3().toString()));
    log.info(
        "Aporte 4 R$"
            + (controleGarantia.getAporte4() == null
                ? "0.00"
                : controleGarantia.getAporte4().toString()));
    log.info(
        "Aporte 5 R$"
            + (controleGarantia.getAporte5() == null
                ? "0.00"
                : controleGarantia.getAporte5().toString()));
  }

  private List<Integer> getIdsInstituicoesFromParametro(ParametroValor parametroValor) {
    List<Integer> listToReturn = new ArrayList<>();
    for (String item : parametroValor.getValorParametro().split(",")) {
      listToReturn.add(Integer.valueOf(item));
    }
    return listToReturn;
  }

  private List<String> getFunctionCodesFromParametro(ParametroValor parametroValor) {
    return Arrays.asList(parametroValor.getValorParametro().split(","));
  }

  private BigDecimal processarTransacoesPorFunctionCode(
      Integer idInstituicao,
      List<String> functionCodes,
      BigDecimal saldoCalculado,
      String dateTimeInicio,
      String dateTimeFim) {
    Date diaAnteriorInicioDia =
        DateUtil.parseDate(DateUtil.DATE_TIME_FORMAT_ISO_8601_SPACE_VARIANT, dateTimeInicio);
    Date diaAnteriorFimDia =
        DateUtil.parseDate(DateUtil.DATE_TIME_FORMAT_ISO_8601_SPACE_VARIANT, dateTimeFim);
    log.info("Buscando transações entre " + diaAnteriorInicioDia + " e " + diaAnteriorFimDia);
    List<LogTransacoes> listaTransacoesDoPeriodo =
        logTransacoesRepositoryImpl.findByIdInstituicaoPeriodo(
            idInstituicao, functionCodes, diaAnteriorInicioDia, diaAnteriorFimDia);
    BigDecimal totalFunctionCodePAR = new BigDecimal("0.00");
    BigDecimal totalFunctionCodeIMPAR = new BigDecimal("0.00");
    for (LogTransacoes lt : listaTransacoesDoPeriodo) {
      if (isFunctionCodePar(lt)) {
        totalFunctionCodePAR = totalFunctionCodePAR.add(lt.getAmount());
      } else {
        totalFunctionCodeIMPAR = totalFunctionCodeIMPAR.add(lt.getAmount());
      }
    }
    saldoCalculado = saldoCalculado.subtract(totalFunctionCodePAR);
    saldoCalculado = saldoCalculado.add(totalFunctionCodeIMPAR);
    log.info("Total de transações com functioncode PAR:   - R$" + totalFunctionCodePAR);
    log.info("Total de transações com functioncode IMPAR: + R$" + totalFunctionCodeIMPAR);
    return saldoCalculado;
  }

  private boolean isFunctionCodePar(LogTransacoes lt) {
    return Integer.parseInt(lt.getFunctionCode()) % 2 == 0;
  }

  private BigDecimal somarAportes(BigDecimal saldoCalculado, ControleGarantia controleGarantia) {
    if (controleGarantia.getAporte1() != null) {
      saldoCalculado = saldoCalculado.add(controleGarantia.getAporte1());
    }

    if (controleGarantia.getAporte2() != null) {
      saldoCalculado = saldoCalculado.add(controleGarantia.getAporte2());
    }

    if (controleGarantia.getAporte3() != null) {
      saldoCalculado = saldoCalculado.add(controleGarantia.getAporte3());
    }

    if (controleGarantia.getAporte4() != null) {
      saldoCalculado = saldoCalculado.add(controleGarantia.getAporte4());
    }

    if (controleGarantia.getAporte5() != null) {
      saldoCalculado = saldoCalculado.add(controleGarantia.getAporte5());
    }
    return saldoCalculado;
  }

  private void zerarAportes(ControleGarantia controleGarantia) {
    BigDecimal zero = new BigDecimal("0.00");
    controleGarantia.setAporte1(zero);
    controleGarantia.setAporte2(zero);
    controleGarantia.setAporte3(zero);
    controleGarantia.setAporte4(zero);
    controleGarantia.setAporte5(zero);
  }

  /**
   * Alguns pagamentos (Ex: pix, credito) não tem coluna que informa motivo de estorno, logo o
   * response apresenta apenas uma mensagem genérica
   *
   * @param motivoEstornoRequest
   * @return
   */
  public DetalhesMotivoEstornoResponse getDetalhesMotivoEstorno(
      DetalhesMotivoEstornoRequest motivoEstornoRequest) {

    DetalhesMotivoEstornoResponse motivoEstornoResponse = new DetalhesMotivoEstornoResponse();

    try {
      AcessoUsuario user = new AcessoUsuario();
      LancamentoManual lancamentoManual = lancamentoService.getByRrn(motivoEstornoRequest.getRrn());
      String telefoneComDDD;

      if (lancamentoManual == null) {

        LancamentoAuto lancamentoAuto =
            lancamentoService.buscarLancamentoAutoByRrn(motivoEstornoRequest.getRrn());
        String motivoEstornoMessage;
        LogTransacoes logTransacoes;

        if (lancamentoAuto == null) {

          logTransacoes = repository.findLogTransacoesByVoidId(motivoEstornoRequest.getIdTranlog());

          if (logTransacoes == null) {
            logTransacoes = repository.findLogTransacoesByRrn(motivoEstornoRequest.getRrn());

            if (logTransacoes == null) {
              throw new GenericServiceException(
                  "Não foi possível buscar motivo estorno. Transação "
                      + motivoEstornoRequest.getRrn()
                      + " não encontrada, contate o suporte.",
                  HttpStatus.NOT_FOUND);
            }
          }

          CodigoTransacao codigoTransacao =
              codigoTransacaoService.findOneByCodTransacao(
                  Integer.valueOf(motivoEstornoRequest.getCodigoTransacao()));

          if (codigoTransacao == null) {
            throw new GenericServiceException(
                "Não foi possível buscar motivo estorno. Código de transação não encontrado",
                HttpStatus.NOT_FOUND);
          }
          user = acessoUsuarioService.findByIdUsuario(USUARIO_PORTADOR);
          telefoneComDDD =
              String.valueOf(user.getDddNroCelular()) + String.valueOf(user.getNroCelular());

          if (codigoTransacao.getDescTransacaoEstendida().toLowerCase().contains("boleto")
              || codigoTransacao
                  .getDescTransacaoEstendida()
                  .toLowerCase()
                  .contains("débito genérico")) {
            LogPagtoTituloTransacao logPagtoTituloTransacao =
                logPagtoTransacaoRepository.findOneByRrn(logTransacoes.getRrn());

            if (logPagtoTituloTransacao == null) {
              motivoEstornoMessage = "Motivo de estorno não registrado";
            } else {
              motivoEstornoMessage =
                  logPagtoTituloTransacao.getTxMotivoRecusRend() != null
                      ? logPagtoTituloTransacao.getTxMotivoRecusRend()
                      : "Motivo de estorno não registrado";
            }

            motivoEstornoResponse =
                montarMotivoEstorno(
                    user.getIdUsuario(),
                    user.getNome(),
                    user.getEmail(),
                    telefoneComDDD,
                    motivoEstornoMessage);
          } else if (codigoTransacao.getDescTransacaoEstendida().toLowerCase().contains("pix")) {
            motivoEstornoResponse =
                montarMotivoEstorno(
                    user.getIdUsuario(),
                    user.getNome(),
                    user.getEmail(),
                    telefoneComDDD,
                    "Motivo de estorno não registrado");
          } else if (codigoTransacao.getDescTransacaoEstendida().toLowerCase().contains("ted")) {
            TED ted = tedService.findTedByRrn(logTransacoes.getRrn());

            if (ted == null) {
              motivoEstornoMessage = "Motivo de estorno não registrado";
            } else {
              motivoEstornoMessage =
                  ted.getIdMotivoRecusado() != null
                      ? !Objects.equals(
                              ted.getIdMotivoRecusado(),
                              MotivoTEDEnum.REJEITADA_POR_REGRA_DE_NEGOCIO.getCodigo())
                          ? MotivoTEDEnum.valueOfCodigo(ted.getIdMotivoRecusado()).getDescricao()
                          : "Motivo de estorno não registrado"
                      : "Motivo de estorno não registrado";
            }

            motivoEstornoResponse =
                montarMotivoEstorno(
                    user.getIdUsuario(),
                    user.getNome(),
                    user.getEmail(),
                    telefoneComDDD,
                    motivoEstornoMessage);
          } else if (codigoTransacao.getDescTransacaoEstendida().toLowerCase().contains("credito")
              || codigoTransacao.getDescTransacaoEstendida().toLowerCase().contains("crédito")) {
            motivoEstornoResponse =
                montarMotivoEstorno(
                    user.getIdUsuario(),
                    user.getNome(),
                    user.getEmail(),
                    telefoneComDDD,
                    "Motivo de estorno não registrado");
          } else {
            log.warn(
                "Não foi possível buscar motivo estorno. Mapeamento do tipo de transação não permitiu encontrar a transação solicitada.");
            throw new GenericServiceException(
                "Não foi possível buscar motivo estorno. Mapeamento do tipo de transação não permitiu encontrar a transação solicitada.",
                HttpStatus.INTERNAL_SERVER_ERROR);
          }
        } else {
          user = acessoUsuarioService.findByIdUsuario(lancamentoAuto.getIdUsuario());
          telefoneComDDD =
              String.valueOf(user.getDddNroCelular()) + String.valueOf(user.getNroCelular());
          motivoEstornoResponse =
              montarMotivoEstorno(
                  lancamentoAuto.getIdUsuario(),
                  user.getNome(),
                  user.getEmail(),
                  telefoneComDDD,
                  lancamentoAuto.getIdTransacaoApresentada() != null
                      ? "Estornada por liquidação"
                      : "Motivo de estorno não registrado");
        }

      } else {
        user = acessoUsuarioService.findByIdUsuario(lancamentoManual.getIdUsuario());
        telefoneComDDD =
            String.valueOf(user.getDddNroCelular()) + String.valueOf(user.getNroCelular());
        motivoEstornoResponse =
            montarMotivoEstorno(
                lancamentoManual.getIdUsuario(),
                user.getNome(),
                user.getEmail(),
                telefoneComDDD,
                lancamentoManual.getTextoComentario() != null
                    ? lancamentoManual.getTextoComentario()
                    : lancamentoManual.getTextoExtrato());
      }
    } catch (GenericServiceException e) {
      throw new GenericServiceException(
          e.getMensagem() == null
              ? "Ocorreu um erro ao buscar informações do motivo estorno da transação = "
                  + motivoEstornoRequest.getRrn()
              : e.getMensagem(),
          e.getHttpStatus() == null ? HttpStatus.INTERNAL_SERVER_ERROR : e.getHttpStatus());
    }

    return motivoEstornoResponse;
  }

  public DetalhesMotivoEstornoResponse montarMotivoEstorno(
      Integer idUsuario, String nome, String email, String telefone, String motivoEstorno) {
    DetalhesMotivoEstornoResponse detalhesMotivoEstornoResponse =
        new DetalhesMotivoEstornoResponse();
    detalhesMotivoEstornoResponse.setIdUsuario(idUsuario);
    detalhesMotivoEstornoResponse.setNome(nome);
    detalhesMotivoEstornoResponse.setEmail(email);
    detalhesMotivoEstornoResponse.setTelefone(telefone);
    detalhesMotivoEstornoResponse.setMotivoEstorno(motivoEstorno);

    return detalhesMotivoEstornoResponse;
  }
}
