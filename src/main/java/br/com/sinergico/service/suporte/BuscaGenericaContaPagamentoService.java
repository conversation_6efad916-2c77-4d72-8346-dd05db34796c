package br.com.sinergico.service.suporte;

import static br.com.sinergico.util.MyHibernateUtils.listAndCast;

import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.suporte.HierarquiaFilial;
import br.com.entity.suporte.HierarquiaFilialId;
import br.com.entity.suporte.TipoPessoa;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.BuscaContaPagamentoPessoaRetorno;
import br.com.json.bean.cadastral.BuscaContaPagamentoRetorno;
import br.com.sinergico.criteria.BuscaContaPagamentoCriteria;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.util.MyHibernateUtils;
import br.com.sinergico.util.Util;
import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import org.apache.commons.lang3.math.NumberUtils;
import org.hibernate.Criteria;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class BuscaGenericaContaPagamentoService {

  @Autowired private BuscaContaPagamentoCriteria buscaContaCriteria;

  @Autowired private ProdutoInstituicaoService produtoService;

  @Autowired private FilialService filialService;

  @Autowired private CardService cardService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  private static final String DESC_FISICA = "Fisica";
  private static final String DESC_JURIDICA = "Juridica";
  private static final Integer ID_FISICA = 1;
  private static final Integer ID_JURIDICA = 2;

  public List<BuscaContaPagamentoRetorno> buscarContaPagamento(
      String busca, Boolean isBuscaNroCartao, SecurityUser user, Integer first, Integer max) {

    if (isBuscaNroCartao != null && isBuscaNroCartao) {
      GetCardResponse card = buscarCredencialPeloHash512Hex(busca.toUpperCase());

      if (!card.getSuccess()) {
        throw new GenericServiceException("Não foi possível encontrar o cartão pelo número.");
      }

      return contaPagamentoService.findContaPagamentoByFiltros(
          card.getCard().getToken(), isBuscaNroCartao, user, first, max);

    } else {
      return contaPagamentoService.findContaPagamentoByFiltros(
          busca, isBuscaNroCartao, user, first, max);
    }
  }

  public Long contarContasPagamento(String busca, Boolean isBuscaNroCartao, SecurityUser user) {
    if (isBuscaNroCartao != null && isBuscaNroCartao) {
      GetCardResponse card = buscarCredencialPeloHash512Hex(busca.toUpperCase());

      if (!card.getSuccess()) {
        throw new GenericServiceException("Não foi possível encontrar o cartão pelo número.");
      }

      return contaPagamentoService.countContaPagamentoByFiltros(
          card.getCard().getToken(), isBuscaNroCartao, user);

    } else {
      return contaPagamentoService.countContaPagamentoByFiltros(busca, isBuscaNroCartao, user);
    }
  }

  public List<BuscaContaPagamentoRetorno> buscaPorFiltro(
      String res,
      Boolean isBuscaNroCartao,
      SecurityUser user,
      Integer first,
      Integer max,
      Integer idInstituicao) {
    if (isBuscaNroCartao != null && isBuscaNroCartao) {
      GetCardResponse card = buscarCredencialPeloHash512Hex(res.toUpperCase());

      if (!card.getSuccess()) {
        throw new GenericServiceException("Não foi possível encontrar o cartão pelo número.");
      }

      return contaPagamentoService.filtroByContaPagamento(
          card.getCard().getToken(), isBuscaNroCartao, user, first, max, idInstituicao);

    } else {
      return contaPagamentoService.filtroByContaPagamento(
          res, isBuscaNroCartao, user, first, max, idInstituicao);
    }
  }

  public Long contarContasPagamentoFiltro(
      String busca, Boolean isBuscaNroCartao, SecurityUser user, Integer idInstituicao) {
    if (isBuscaNroCartao != null && isBuscaNroCartao) {
      GetCardResponse card = buscarCredencialPeloHash512Hex(busca.toUpperCase());

      if (!card.getSuccess()) {
        throw new GenericServiceException("Não foi possível encontrar o cartão pelo número.");
      }

      return contaPagamentoService.countContaPagamentoFiltros(
          card.getCard().getToken(), isBuscaNroCartao, user, idInstituicao);

    } else {
      return contaPagamentoService.countContaPagamentoFiltros(
          busca, isBuscaNroCartao, user, idInstituicao);
    }
  }

  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorNumeroConta(
      String contaBusca, SecurityUser user, Integer first, Integer max) {
    Criteria criteriaConta = getContaCriteria(user, first, max);
    buscaContaCriteria.setIdContaPagamento(contaBusca);
    List<ContaPagamento> contas =
        new ArrayList<ContaPagamento>(
            new LinkedHashSet<ContaPagamento>(listAndCast(criteriaConta)));
    return prepareRetornoCP(contas);
  }

  public int contarContaPagamentoPorNumeroConta(String contaBusca, SecurityUser user) {
    Criteria criteriaConta = getContaCriteria(user, null, null);
    buscaContaCriteria.setIdContaPagamento(contaBusca);
    buscaContaCriteria.setCount(Boolean.TRUE);
    int count = MyHibernateUtils.countRegisters(criteriaConta);
    return count;
  }

  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorIdConta(
      Long idConta, SecurityUser user, Integer first, Integer max) {
    Criteria criteriaConta = getContaCriteria(user, first, max);
    buscaContaCriteria.setIdConta(idConta);
    List<ContaPagamento> contas =
        new ArrayList<ContaPagamento>(
            new LinkedHashSet<ContaPagamento>(listAndCast(criteriaConta)));
    return prepareRetorno(contas, null, null);
  }

  public int contarContaPagamentoPorIdConta(Long idConta, SecurityUser user) {
    Criteria criteriaConta = getContaCriteria(user, null, null);
    buscaContaCriteria.setIdConta(idConta);
    buscaContaCriteria.setCount(Boolean.TRUE);
    int count = MyHibernateUtils.countRegisters(criteriaConta);
    return count;
  }

  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorCredencial(
      String credencialBusca, SecurityUser user, Integer first, Integer max) {
    GetCardResponse card = buscarCredencialPeloHash512Hex(credencialBusca.toUpperCase());
    Criteria criteriaConta = getContaCriteria(user, first, max);
    buscaContaCriteria.setTokenInterno(card.getCard().getToken());
    List<ContaPagamento> contas =
        new ArrayList<ContaPagamento>(
            new LinkedHashSet<ContaPagamento>(listAndCast(criteriaConta)));
    return prepareRetorno(contas, null, null);
  }

  public int contarContaPagamentoPorCredencial(String credencialBusca, SecurityUser user) {
    GetCardResponse card = buscarCredencialPeloHash512Hex(credencialBusca.toUpperCase());
    Criteria criteriaConta = getContaCriteria(user, null, null);
    buscaContaCriteria.setTokenInterno(card.getCard().getToken());
    buscaContaCriteria.setCount(Boolean.TRUE);
    int count = MyHibernateUtils.countRegisters(criteriaConta);
    return count;
  }

  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorNome(
      String nomeBusca, SecurityUser user, Integer first, Integer max) {
    Criteria criteriaConta = getContaCriteria(user, first, max);
    buscaContaCriteria.setNome(nomeBusca);
    List<ContaPagamento> contas =
        new ArrayList<ContaPagamento>(
            new LinkedHashSet<ContaPagamento>(listAndCast(criteriaConta)));
    return prepareRetorno(contas, null, nomeBusca);
  }

  public int contarContaPagamentoPorNome(String nomeBusca, SecurityUser user) {
    Criteria criteriaConta = getContaCriteria(user, null, null);
    buscaContaCriteria.setNome(nomeBusca);
    buscaContaCriteria.setCount(Boolean.TRUE);
    int count = MyHibernateUtils.countRegisters(criteriaConta);
    return count;
  }

  private GetCardResponse buscarCredencialPeloHash512Hex(String hashHexCredencial) {
    GetCardResponse card = cardService.getToken(hashHexCredencial);

    return card;
  }

  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorDocumento(
      String documentoBusca, SecurityUser user, Integer first, Integer max) {
    Criteria criteriaConta = getContaCriteria(user, first, max);
    TipoPessoa tipoPessoa = setTipopessoaByDocumentoBusca(documentoBusca);

    buscaContaCriteria.setTipoPessoa(tipoPessoa);
    buscaContaCriteria.setDocumento(documentoBusca);
    List<ContaPagamento> contas =
        new ArrayList<ContaPagamento>(
            new LinkedHashSet<ContaPagamento>(listAndCast(criteriaConta)));
    return prepareRetorno(contas, documentoBusca, null);
  }

  private TipoPessoa setTipopessoaByDocumentoBusca(String documentoBusca) {
    TipoPessoa tipoPessoa = new TipoPessoa();
    if (documentoBusca.length() == 11) {
      tipoPessoa.setId(ID_FISICA);
      tipoPessoa.setTipoPessoa(ID_FISICA);
      tipoPessoa.setDescTipoPessoa(DESC_FISICA);

    } else {
      tipoPessoa.setId(ID_JURIDICA);
      tipoPessoa.setTipoPessoa(ID_JURIDICA);
      tipoPessoa.setDescTipoPessoa(DESC_JURIDICA);
    }

    return tipoPessoa;
  }

  public int contarContaPagamentoPorDocumento(String documentoBusca, SecurityUser user) {
    Criteria criteriaConta = getContaCriteria(user, null, null);
    TipoPessoa tipoPessoa = setTipopessoaByDocumentoBusca(documentoBusca);

    buscaContaCriteria.setTipoPessoa(tipoPessoa);
    buscaContaCriteria.setDocumento(documentoBusca);
    buscaContaCriteria.setCount(Boolean.TRUE);
    int count = MyHibernateUtils.countRegisters(criteriaConta);
    return count;
  }

  private Criteria getContaCriteria(SecurityUser user, Integer first, Integer max) {
    Criteria criteria =
        buscaContaCriteria.getContaPagamentoCriteria(ContaPagamento.class, first, max);

    buscaContaCriteria.setProcessadora(user.getIdProcessadora());
    buscaContaCriteria.setInstituicao(user.getIdInstituicao());
    buscaContaCriteria.setRegional(user.getIdRegional());
    buscaContaCriteria.setFilial(user.getIdFilial());
    buscaContaCriteria.setPontoDeRelacionamento(user.getIdPontoDeRelacionamento());

    return criteria;
  }

  private List<BuscaContaPagamentoRetorno> prepareRetorno(
      List<ContaPagamento> contas, String documento, String nome) {

    List<BuscaContaPagamentoRetorno> retorno = new ArrayList<BuscaContaPagamentoRetorno>();

    for (ContaPagamento conta : contas) {
      BuscaContaPagamentoRetorno busca = new BuscaContaPagamentoRetorno();
      ProdutoInstituicao prodInst = buscaProdInst(conta);
      HierarquiaFilial hierarquiaFilial = buscaFilial(conta);

      busca = preencheBuscaByContaHierarquiaProduto(conta, hierarquiaFilial, prodInst);

      if (!Util.isEmpty(documento) || !Util.isEmpty(nome)) {

        if (conta.getContasPessoa() == null) {
          conta.setContasPessoa(contaPessoaService.findByIdConta(conta.getIdConta()));
        }

        busca.setInfoPessoa(preparePessoaInfo(conta.getContasPessoa(), documento, nome));
      }

      retorno.add(busca);
    }

    return retorno;
  }

  private BuscaContaPagamentoRetorno preencheBuscaByContaHierarquiaProduto(
      ContaPagamento conta,
      HierarquiaFilial hierarquiaFilial,
      ProdutoInstituicao produtoInstituicao) {
    BuscaContaPagamentoRetorno busca = new BuscaContaPagamentoRetorno();
    busca.setIdConta(conta.getIdConta());
    busca.setIdContaPagamento(conta.getIdContaPagamento());
    busca.setDescFilial(hierarquiaFilial.getDescFilial().trim());
    busca.setDescProdInstituicao(produtoInstituicao.getDescProdInstituicao());
    busca.setIdStatusConta(conta.getIdStatusConta());
    busca.setDescStatusConta(conta.getTipoStatus().getDescStatus());
    busca.setDescRelacionamento(conta.getArranjoRelacionamento().getDescAbrev());
    return busca;
  }

  private List<BuscaContaPagamentoRetorno> prepareRetornoCP(List<ContaPagamento> contas) {

    List<BuscaContaPagamentoRetorno> retorno = new ArrayList<BuscaContaPagamentoRetorno>();

    for (ContaPagamento conta : contas) {
      BuscaContaPagamentoRetorno busca = new BuscaContaPagamentoRetorno();
      ProdutoInstituicao prodInst = buscaProdInst(conta);
      HierarquiaFilial hierarquiaFilial = buscaFilial(conta);
      busca = preencheBuscaByContaHierarquiaProduto(conta, hierarquiaFilial, prodInst);

      if (conta.getContasPessoa() != null) {

        ContaPessoa cp =
            conta.getContasPessoa().stream()
                .filter(
                    contaPessoa -> contaPessoa.getIdTitularidade().equals(NumberUtils.INTEGER_ONE))
                .findFirst()
                .get();

        Pessoa pessoa = cp.getPessoa();

        BuscaContaPagamentoPessoaRetorno pessoaInfo = new BuscaContaPagamentoPessoaRetorno();
        pessoaInfo.setDocumento(pessoa.getDocumento());
        pessoaInfo.setNomeCompleto(pessoa.getNomeCompleto());
        pessoaInfo.setRazaoSocial(pessoa.getRazaoSocial());
        pessoaInfo.setTipoTitularidade(cp.getTipoTitularidade());

        busca.setInfoPessoa(pessoaInfo);
      }

      retorno.add(busca);
    }

    return retorno;
  }

  private ProdutoInstituicao buscaProdInst(ContaPagamento conta) {
    return produtoService.findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
        conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());
  }

  private HierarquiaFilial buscaFilial(ContaPagamento conta) {
    HierarquiaFilialId id = new HierarquiaFilialId();
    id.setIdProcessadora(conta.getIdProcessadora());
    id.setIdInstituicao(conta.getIdInstituicao());
    id.setIdRegional(conta.getIdRegional());
    id.setIdFilial(conta.getIdFilial());

    return filialService.findById(id);
  }

  private BuscaContaPagamentoPessoaRetorno preparePessoaInfo(
      List<ContaPessoa> contasPessoa, String documento, String nome) {

    BuscaContaPagamentoPessoaRetorno pessoaInfo = null;

    for (ContaPessoa cp : contasPessoa) {
      Pessoa pessoa = cp.getPessoa();

      if (!Util.isEmpty(documento)) {
        if (pessoa.getDocumento().equals(documento)) {
          pessoaInfo = new BuscaContaPagamentoPessoaRetorno();
          pessoaInfo.setDocumento(pessoa.getDocumento());
          pessoaInfo.setNomeCompleto(pessoa.getNomeCompleto());
          pessoaInfo.setRazaoSocial(pessoa.getRazaoSocial());
          pessoaInfo.setTipoTitularidade(cp.getTipoTitularidade());
          break;
        }
      }

      if (!Util.isEmpty(nome)) {
        if (pessoa.getNomeCompleto() != null) {
          if (pessoa.getNomeCompleto().toUpperCase().startsWith(nome.toUpperCase())) {
            pessoaInfo = new BuscaContaPagamentoPessoaRetorno();
            pessoaInfo.setDocumento(pessoa.getDocumento());
            pessoaInfo.setNomeCompleto(pessoa.getNomeCompleto());
            pessoaInfo.setTipoTitularidade(cp.getTipoTitularidade());
            break;
          }
        } else if (pessoa.getRazaoSocial() != null) {
          if (pessoa.getRazaoSocial().toUpperCase().startsWith(nome.toUpperCase())) {
            pessoaInfo = new BuscaContaPagamentoPessoaRetorno();
            pessoaInfo.setDocumento(pessoa.getDocumento());
            pessoaInfo.setRazaoSocial(pessoa.getRazaoSocial());
            pessoaInfo.setTipoTitularidade(cp.getTipoTitularidade());
            break;
          }
        }
      }
    }

    return pessoaInfo;
  }
}
