package br.com.json.bean.transacional;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class CadastroCargaBRBApi implements Serializable {

  private static final long serialVersionUID = 4465547895868874172L;
  private static final String MENSAGEM_FALHA =
      "Falha ao executar API. Parâmetros vazios ou inconsistentes.";

  @NotNull(message = MENSAGEM_FALHA)
  @Min(value = 0, message = MENSAGEM_FALHA)
  private Long idConta;

  @NotNull(message = MENSAGEM_FALHA)
  private BigDecimal valor;

  @NotNull(message = MENSAGEM_FALHA)
  private Integer idProduto;

  @Length(max = 100, message = MENSAGEM_FALHA)
  private String textoExtrato;

  @NotNull(message = MENSAGEM_FALHA)
  @Min(value = 30010000000L, message = MENSAGEM_FALHA)
  private Long transactionUid;
}
