package br.com.sinergico.controller.cadastral.portador;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.suporte.TokenFuncionalidade;
import br.com.enumVO.TipoTokenFuncionalidadeEnum;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.antifraude.AlterarDispositivo;
import br.com.json.bean.cadastral.CadastrarPortadorLogin;
import br.com.json.bean.cadastral.CadastrarPortadorLoginPreCadastradoRequest;
import br.com.json.bean.cadastral.DadosPortador;
import br.com.json.bean.cadastral.DetalhesAcessoPortador;
import br.com.json.bean.cadastral.DetalhesPortadorLogin;
import br.com.json.bean.cadastral.FazerLoginPortador;
import br.com.json.bean.cadastral.FazerLoginPortadorResponse;
import br.com.json.bean.cadastral.LoginTokenValloMotiva;
import br.com.json.bean.cadastral.PessoaPreCadastradaVO;
import br.com.json.bean.cadastral.PortadorLoginCredencialVO;
import br.com.json.bean.cadastral.RecuperarSenhaEmailPortador;
import br.com.json.bean.cadastral.RecuperarSenhaPortador;
import br.com.json.bean.cadastral.SenhaRequest;
import br.com.json.bean.cadastral.TrocarEmail;
import br.com.json.bean.cadastral.ValidarDadosOnboardVO;
import br.com.json.bean.cadastral.ValidarDispositivo;
import br.com.json.bean.suporte.RedefinirSenhaPortador;
import br.com.json.bean.suporte.RedefinirSenhaPortadorV2;
import br.com.json.bean.suporte.TrocarSenhaEmailPortador;
import br.com.json.bean.suporte.TrocarSenhaPortador;
import br.com.json.bean.suporte.TrocarSenhaPortadorV2;
import br.com.json.bean.suporte.ValidarCadastroOnboardVO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import br.com.sinergico.security.PasswordValidatorService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.suporte.TokenFuncionalidadeService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.validator.UtilValidator;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/portador/login")
public class PortadorLoginController extends UtilController {

  @Autowired private EntityManager em;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private TokenFuncionalidadeService tokenFuncionalidadeService;

  @Autowired private PasswordValidatorService passwordValidatorService;

  @Autowired private UtilService utilService;

  public static final int TITULARIDADE = 1;
  private static final int TODAS_INSTITUICOES = 0;

  @ApiOperation(value = "Serviço responsável por cadastrar um login de um portador")
  @RequestMapping(
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, Object>> create(
      @RequestBody @Valid CadastrarPortadorLogin model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados inválidos.", result);
    }

    travaServicosService.travaServicos(
        model.getIdInstituicao() == null ? TODAS_INSTITUICOES : model.getIdInstituicao(),
        Servicos.CRIAR_PORTADOR_LOGIN);

    portadorLoginService.create(model);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Login cadastrado com sucesso");
    map.put("created", true);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por cadastrar um login de um portador pre cadastrado")
  @RequestMapping(
      value = "/criar-login/pre-cadastro",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, Object>> criarPortadorLoginPreCadastro(
      @RequestBody @Valid CadastrarPortadorLoginPreCadastradoRequest model, BindingResult result) {

    HashMap<String, Object> map = new HashMap<>();
    if (result.hasErrors()) {
      result.getFieldError().toString();
      log.info(result.getFieldError().toString());
      throw new InvalidRequestException("Dados inválidos.", result);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(model.getSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }

    if (!passwordValidatorService.validate(model.getSenha(), Arrays.asList(model.getDocumento()))) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números"
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.",
          result);
    }

    portadorLoginService.criarPortadorLoginPreCadastro(model);

    map.put("msg", "Login cadastrado com sucesso");
    map.put("created", true);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @RequestMapping(
      value = "/auth",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(
      notes = "",
      value = "Login do portador no sistema",
      response = FazerLoginPortadorResponse.class)
  public ResponseEntity<FazerLoginPortadorResponse> doLogin(
      @RequestBody @Valid FazerLoginPortador login,
      HttpServletRequest request,
      BindingResult result)
      throws IOException {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na Validação de login do portador.", result);
    }

    travaServicosService.travaServicos(login.getIdInstituicao(), Servicos.LOGIN_PORTADOR);

    login.setSenha(encodeSenhaSHA256(login.getSenha()));
    return portadorLoginService.doLogin(request, login);
  }

  /**
   * Login do Portador para BFF Multibeneficios
   *
   * @param request
   * @param login
   * @param result
   * @return
   */
  @RequestMapping(
      value = "/v2/auth",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(
      notes = "",
      value = "Login do portador no sistema",
      response = FazerLoginPortadorResponse.class)
  public ResponseEntity<Map<String, Object>> doLoginPortadorV2(
      @RequestBody @Valid FazerLoginPortador login,
      HttpServletRequest request,
      BindingResult result)
      throws IOException {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na Validação de login do portador.", result);
    }

    travaServicosService.travaServicos(login.getIdInstituicao(), Servicos.LOGIN_PORTADOR);

    login.setSenha(encodeSenhaSHA256(login.getSenha()));
    ResponseEntity<FazerLoginPortadorResponse> resp = portadorLoginService.doLogin(request, login);

    return ResponseEntity.ok(Collections.singletonMap("dados", resp.getBody()));
  }

  @RequestMapping(
      value = "/trocar-senha",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por trocar a senha do portador")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_TROCAR_SENHA_PORTADOR"})
  public ResponseEntity<?> trocarSenha(
      @RequestBody @Valid TrocarSenhaPortador trocarSenha,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(
        userPortador.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    if (UtilValidator.existeCaracteresNaoAceitos(trocarSenha.getNovaSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }

    if (!passwordValidatorService.validate(
        trocarSenha.getNovaSenha(),
        Arrays.asList(userPortador.getDocumentoAcesso(), userPortador.getUsername()))) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números."
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.",
          result);
    }

    //		if(UtilValidator.isSenhaFraca(trocarSenha.getNovaSenha())){
    //			throw new InvalidRequestException("Para a sua segurança sua nova senha deverá conter no
    // mínimo 8 caracteres, contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter
    // especial e números",result);
    //		}

    trocarSenha.setSenha(encodeSenhaSHA256(trocarSenha.getSenha()));

    trocarSenha.setNovaSenha(encodeSenhaSHA256(trocarSenha.getNovaSenha()));
    portadorLoginService.trocarSenhaPortador(trocarSenha);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Senha alterada com sucesso");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  /**
   * Trocar senha para BFF multibeneficios
   *
   * @param trocarSenha
   * @param result
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(
      value = "/v2/trocar-senha",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por trocar a senha do portador")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_TROCAR_SENHA_PORTADOR"})
  public ResponseEntity<?> trocarSenhaPortadorV2(
      @RequestBody @Valid TrocarSenhaPortadorV2 trocarSenha,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(
        userPortador.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    if (UtilValidator.existeCaracteresNaoAceitos(trocarSenha.getNovaSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }
    if (UtilValidator.isSenhaFraca(trocarSenha.getNovaSenha())) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres, contendo pelo menos 1 letra maiúscula, 1 letra minúscula e números",
          result);
    }

    TrocarSenhaPortador trocarSenhaPortador = new TrocarSenhaPortador();

    trocarSenhaPortador.setSenha(encodeSenhaSHA256(trocarSenha.getSenha()));
    trocarSenhaPortador.setNovaSenha(encodeSenhaSHA256(trocarSenha.getNovaSenha()));
    trocarSenhaPortador.setCpf(userPortador.getCpf());
    trocarSenhaPortador.setIdProcessadora(userPortador.getIdProcessadora());
    trocarSenhaPortador.setIdInstituicao(userPortador.getIdInstituicao());
    trocarSenhaPortador.setGrupoAcesso(userPortador.getGrupoAcesso());

    portadorLoginService.trocarSenhaPortador(trocarSenhaPortador);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Senha alterada com sucesso");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @RequestMapping(
      value = "/trocar-email-senha",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(
      value =
          "Serviço responsável por trocar a senha e o email do portador sem confirmaçao para os logins registrando OCR")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_TROCAR_SENHA_EMAIL_PORTADOR"})
  public ResponseEntity<?> trocarEmailESenhaPrimeiroAcesso(
      @RequestBody @Valid TrocarSenhaEmailPortador trocarSenhaEmail,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(trocarSenhaEmail.getNovaSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }
    if (UtilValidator.isSenhaFraca(trocarSenhaEmail.getNovaSenha())) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres, um caractere especial, contendo pelo menos 1 letra maiúscula, 1 letra minúscula e números",
          result);
    }

    Map<String, Object> map = new HashMap<>();

    SecurityUserPortador portadorLogin = getAuthenticatedPortador(request, em);

    if (Objects.equals(portadorLogin.getCpf(), trocarSenhaEmail.getCpf())
        && Objects.equals(portadorLogin.getIdProcessadora(), trocarSenhaEmail.getIdProcessadora())
        && Objects.equals(portadorLogin.getIdInstituicao(), trocarSenhaEmail.getIdInstituicao())) {

      trocarSenhaEmail.setNovaSenha(encodeSenhaSHA256(trocarSenhaEmail.getNovaSenha()));

      portadorLoginService.trocarEmailESenhaPrimeiroAcesso(trocarSenhaEmail);

      map.put("msg", "Senha e emails alterados com sucesso");
    } else {
      map.put(
          "msg",
          "Não foi possível alterar senha e email: usuário incompatível com usuário logado atualmente");
    }

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @RequestMapping(
      value = "/trocar-email",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por trocar o email do portador")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_TROCAR_EMAIL_PORTADOR"})
  public ResponseEntity<HashMap<String, String>> trocarEmail(
      @RequestBody @Valid TrocarEmail trocarEmail,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.TROCAR_EMAIL);

    portadorLoginService.trocarEmailPortador(trocarEmail, userPortador);

    HashMap<String, String> map = new HashMap<>();
    map.put("msg", "E-mail alterado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por buscar o email do portador")
  @RequestMapping(
      value = "/{idProcessadora}/{idInstituicao}/buscar-email/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_BUSCAR_EMAIL_PORTADOR"})
  public ResponseEntity<HashMap<String, String>> buscarEmail(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable String documento,
      HttpServletRequest request,
      HttpServletResponse response) {

    HashMap<String, String> map =
        portadorLoginService.buscarEmailPortador(idProcessadora, idInstituicao, documento);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @RequestMapping(value = "/logout", method = RequestMethod.GET)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public void logout(HttpServletRequest request) {
    request.getSession().invalidate();
  }

  @ApiOperation(
      value = "Serviço responsável por listar os logins cadastrados de um portador",
      response = DetalhesPortadorLogin.class,
      notes = "Lista os logins de um portador",
      responseContainer = "List")
  @RequestMapping(
      value = "/get-logins/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_BUSCAR_LOGINS_CADASTRADOS_PORTADOR"})
  public ResponseEntity<List<DetalhesPortadorLogin>> buscarLogins(
      @PathVariable Long idConta, HttpServletRequest request, HttpServletResponse response) {

    List<DetalhesPortadorLogin> lista = portadorLoginService.buscarLogins(idConta);

    return new ResponseEntity<>(lista, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por validar cpf e telefone pre-cadastrado")
  @RequestMapping(
      value = "/pre-cadastro",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> preCadastro(@RequestBody PessoaPreCadastradaVO pessoaPreCadastradaVO) {

    Map<String, Boolean> map = new HashMap<>();

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumento(
            pessoaPreCadastradaVO.getIdProcessadora(),
            pessoaPreCadastradaVO.getIdInstituicao(),
            pessoaPreCadastradaVO.getDocumento());
    PortadorLogin portador =
        portadorLoginService
            .findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
                pessoaPreCadastradaVO.getIdProcessadora(),
                pessoaPreCadastradaVO.getIdInstituicao(),
                pessoaPreCadastradaVO.getDocumento(),
                TipoPortadorLoginEnum.LEGADO_SIMPLES);

    if (pessoa != null
        && !Objects.equals(
            pessoaPreCadastradaVO.getTelefone(),
            pessoa.getDddTelefoneCelular().toString() + pessoa.getTelefoneCelular().toString())) {
      map.put("telefoneValidado", false);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }

    if (pessoa != null && portador == null) {
      map.put("sucesso", true);
      return new ResponseEntity<>(map, HttpStatus.OK);
    } else {
      map.put("sucesso", false);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
  }

  @ApiOperation(
      value =
          "Serviço responsável por buscar se o CPF está vinculado à alguma conta com produto InFinanças ou Pontos InMais.")
  @RequestMapping(
      value = "/get-conta/infinancas/pontos-inmais/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public boolean buscarContasInfinancasOuPontoInMais(@PathVariable String documento) {
    Integer existente = 0;
    Collection<Integer> instituicoes =
        Arrays.asList(
            Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS,
            Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL,
            Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS);
    List<Pessoa> pessoa =
        pessoaService.findByIdProcessadoraAndDocumentoAndIdInstituicaoIn(
            Constantes.ID_PROCESSADORA_ITS_PAY, documento, instituicoes);

    if (pessoa != null) {
      for (Pessoa pessoaSelecionada : pessoa) {
        List<ContaPagamento> contaPagamento =
            contaPagamentoService.findByIdPessoa(pessoaSelecionada.getIdPessoa());
        if (contaPagamento != null) {
          for (ContaPagamento pagamento : contaPagamento) {
            if (!Objects.equals(
                pagamento.getIdInstituicao(), Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL)) {
              if (Objects.equals(
                      pagamento.getIdProdutoInstituicao(), utilService.getProdutoInsCampanha())
                  || Objects.equals(
                      pagamento.getIdProdutoInstituicao(),
                      utilService.getProdutoInsPontosInMais())) {
                existente++;
              }
            } else {
              return false;
            }
          }
        }
      }
    }
    return existente > 0;
  }

  @ApiOperation(
      value =
          "Serviço responsável por buscar se um documento possui portador login legado simples cadastrado")
  @RequestMapping(
      value = "/get-login/{idProcessadora}/{idInstituicao}/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public boolean buscarLoginPorDocumento(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable String documento) {
    PortadorLogin portador =
        portadorLoginService
            .findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
                idProcessadora, idInstituicao, documento, TipoPortadorLoginEnum.LEGADO_SIMPLES);
    if (portador == null) {
      return false;
    } else {
      return true;
    }
  }

  @ApiOperation(
      value =
          "Serviço responsável por listar os acessos dos últimos 6 meses de um login de um portador",
      response = DetalhesAcessoPortador.class,
      notes = "Lista os acessos dos últimos 6 meses de um login de um portador",
      responseContainer = "List")
  @RequestMapping(
      value = "/get-acessos/{idLogin}/{first}/{max}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_BUSCAR_ACESSOS_PORTADOR_ULTIMOS_SEIS_MESES"})
  public ResponseEntity<List<DetalhesAcessoPortador>> getAcessosLogin(
      @PathVariable Long idLogin,
      HttpServletRequest request,
      HttpServletResponse response,
      @PathVariable Integer first,
      @PathVariable Integer max) {

    List<DetalhesAcessoPortador> lista =
        portadorLoginService.buscarAcessosLogin(idLogin, first, max);

    return new ResponseEntity<>(lista, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por contar os acessos dos últimos 6 meses de um login de um portador",
      response = DetalhesAcessoPortador.class,
      notes = "Conta os acessos dos últimos 6 meses de um login de um portador",
      responseContainer = "List")
  @RequestMapping(
      value = "/get-acessos/count/{idLogin}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_BUSCAR_ACESSOS_PORTADOR_ULTIMOS_SEIS_MESES"})
  public Integer countAcessosLogin(
      @PathVariable Long idLogin, HttpServletRequest request, HttpServletResponse response) {

    return portadorLoginService.countAcessosLogin(idLogin);
  }

  @ApiOperation(value = "Serviço responsável por recuperar senha de um login de um portador")
  @RequestMapping(
      value = "/recuperar-senha",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_BUSCAR_ACESSOS_PORTADOR_ULTIMOS_SEIS_MESES"})
  public ResponseEntity<?> recuperarSenha(
      @RequestBody @Valid RecuperarSenhaPortador recuperarSenha,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Verifique os dados de recuperação de Senha. ", result);
    }

    travaServicosService.travaServicos(
        recuperarSenha.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    return portadorLoginService.recuperarSenha(recuperarSenha);
  }

  @ApiOperation(value = "Serviço responsável por recuperar senha de um login de um portador")
  @RequestMapping(
      value = "/recuperar-senha/email",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_BUSCAR_ACESSOS_PORTADOR_ULTIMOS_SEIS_MESES"})
  public ResponseEntity<?> recuperarSenhaByEmail(
      @RequestBody @Valid RecuperarSenhaEmailPortador recuperarSenha,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Verifique os dados de recuperação de Senha. ", result);
    }

    travaServicosService.travaServicos(
        recuperarSenha.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    return portadorLoginService.recuperarSenha(recuperarSenha);
  }

  // servico acessado por qualquer usuario para recuperar senha(nao logado)
  @ApiOperation(
      value = "Serviço responsável por recadastrar senha de um login de um portador",
      response = Map.class)
  @RequestMapping(
      value = "/recadastrar-senha",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> recadastrarSenha(
      @RequestBody @Valid TrocarSenhaPortador trocarSenhaPortador,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados inválidos.", result);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(trocarSenhaPortador.getNovaSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }
    if (UtilValidator.isSenhaFraca(trocarSenhaPortador.getNovaSenha())) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres, contendo pelo menos 1 letra maiúscula, 1 letra minúscula 1 caracter especial e números",
          result);
    }

    travaServicosService.travaServicos(
        trocarSenhaPortador.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    trocarSenhaPortador.setSenha(encodeSenhaSHA256(trocarSenhaPortador.getSenha()));
    trocarSenhaPortador.setNovaSenha(encodeSenhaSHA256(trocarSenhaPortador.getNovaSenha()));

    return new ResponseEntity<>(
        portadorLoginService.recadastrarSenha(trocarSenhaPortador), HttpStatus.OK);
  }

  @RequestMapping(
      value = "/redefinir-senha",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por definir a senha do portador")
  public ResponseEntity<?> redefinirSenha(
      @RequestBody @Valid RedefinirSenhaPortador redefinirSenha,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(redefinirSenha.getNovaSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }

    if (!passwordValidatorService.validate(
        redefinirSenha.getNovaSenha(), Arrays.asList(redefinirSenha.getCpf()))) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números"
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.",
          result);
    }

    travaServicosService.travaServicos(
        redefinirSenha.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    redefinirSenha.setNovaSenha(encodeSenhaSHA256(redefinirSenha.getNovaSenha()));
    portadorLoginService.redefinirSenhaPortador(redefinirSenha, request);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Senha alterada com sucesso");
    map.put("sucesso", true);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  /**
   * Redefinir Senha para BFF Multibeneficios
   *
   * @param redefinirSenha
   * @param result
   * @param request
   * @param response
   * @return
   */
  @RequestMapping(
      value = "/v2/redefinir-senha",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por definir a senha do portador")
  public ResponseEntity<?> redefinirSenhaPortadorV2(
      @RequestBody @Valid RedefinirSenhaPortadorV2 redefinirSenha,
      BindingResult result,
      HttpServletRequest request,
      HttpServletResponse response) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(redefinirSenha.getNovaSenha())) {
      throw new InvalidRequestException(
          "Apenas os caracteres especiais !@#$%&*+= são aceitos.", result);
    }
    if (UtilValidator.isSenhaFraca(redefinirSenha.getNovaSenha())) {
      throw new InvalidRequestException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres, contendo pelo menos 1 letra maiúscula, 1 letra minúscula e números",
          result);
    }

    travaServicosService.travaServicos(
        redefinirSenha.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);

    RedefinirSenhaPortador redefinirSenhaPortador = new RedefinirSenhaPortador();
    redefinirSenhaPortador.setCpf(redefinirSenha.getCpf());
    redefinirSenhaPortador.setNovaSenha(encodeSenhaSHA256(redefinirSenha.getNovaSenha()));
    redefinirSenhaPortador.setChaveExterna(redefinirSenha.getChaveExterna());
    redefinirSenhaPortador.setGrupoAcesso(redefinirSenha.getGrupoAcesso());
    redefinirSenhaPortador.setToken(redefinirSenha.getToken());
    redefinirSenhaPortador.setIdInstituicao(redefinirSenha.getIdInstituicao());
    redefinirSenhaPortador.setIdProcessadora(redefinirSenha.getIdProcessadora());
    redefinirSenhaPortador.setIsFluxoOCR(redefinirSenha.isFluxoOCR());

    portadorLoginService.redefinirSenhaPortador(redefinirSenhaPortador, request);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Senha alterada com sucesso");
    map.put("sucesso", true);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por buscar contrato migração")
  @RequestMapping(
      value = "/contrato/{idInstituicao}/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_BUSCAR_EMAIL_PORTADOR"}) // criar role
  public ResponseEntity<?> validarContrato(
      @PathVariable Integer idInstituicao, @PathVariable String documento) {
    Boolean contratoAceito =
        portadorLoginService.validarContratoMigracaoAceito(idInstituicao, documento);
    Map<String, Boolean> map = new HashMap<>();
    map.put("contrato", contratoAceito);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por salvar data hora contrato migração")
  @RequestMapping(
      value = "/salvar-contrato",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @Secured({"ROLE_BUSCAR_EMAIL_PORTADOR"}) // criar role
  public ResponseEntity<?> salvarContrato(@RequestBody Map<String, Object> contratoRequest) {
    Boolean ok =
        portadorLoginService.salvarContratoMigracao(
            new Integer(contratoRequest.get("idInstituicao").toString()),
            contratoRequest.get("documento").toString());
    Map<String, Boolean> map = new HashMap<>();
    map.put("contrato", ok);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por verificar se a opção de Abrir Conta está habilitada.")
  @RequestMapping(
      value = "/verifica-abertura-conta/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public boolean verificaAberturaConta(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    return portadorLoginService.verificaOpcaoAberturaConta(idInstituicao, idProcessadora);
  }

  @RequestMapping(
      value = "/verifica-necessidade/ocr",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(
      notes = "",
      value = "Verifica se usuário do aplicativo Valloo precisará passar pelo OCR")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public boolean needValidationOCR(
      HttpServletRequest request, @RequestBody ValidarDispositivo login) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    //		travaServicosService.travaServicos(userPortador.getIdInstituicao(),
    // Servicos.VALIDAR_CRIAR_OCR);

    return portadorLoginService.verificarNecessidadeOCR(userPortador, login);
  }

  @ApiOperation(value = "Serviço responsável por cadastrar portador login")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/cadastrar-portador",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_PORTADOR_LOGIN"})
  public ResponseEntity<?> cadastrarPortadorLogin(@RequestBody DadosPortador dadosPortador) {

    portadorLoginService.cadastrarPortadorLogin(dadosPortador);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Cadastro Portador Login realizado com sucesso!");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por excluir portador login")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/excluir-portador/{id_login}",
      method = RequestMethod.DELETE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MANIPULAR_PORTADOR_LOGIN"})
  public ResponseEntity<?> excluirPortadorLogin(@PathVariable("id_login") String idLogin) {

    portadorLoginService.excluirPortadorLogin(Long.valueOf(idLogin));
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Exclusão de Portador Login realizada com sucesso!");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por resetar senha do portador login")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/resetar-senha-portador/{id_login}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MANIPULAR_PORTADOR_LOGIN"})
  public ResponseEntity<?> resetarSenhaPortadorLogin(
      @PathVariable("id_login") String idLogin, @RequestBody DadosPortador dadosPortador) {

    travaServicosService.travaServicos(
        dadosPortador.getIdInstituicao(), Servicos.TROCAR_SENHA_PORTADOR);
    portadorLoginService.recuperarSenhaPortadorLogin(Integer.valueOf(idLogin));

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Reset de senha realizado com sucesso!");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por reativar (alterar expiracao senha para false) senha do portador login")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/reativar-senha-portador/{id_login}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MANIPULAR_PORTADOR_LOGIN"})
  public ResponseEntity<?> reativarSenhaPortadorLogin(@PathVariable("id_login") String idLogin) {

    portadorLoginService.reativarSenhaPortadorLogin(Integer.valueOf(idLogin));

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Redefinição de senha realizado com sucesso!");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @RequestMapping(
      value = "/auth-motiva",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(
      notes = "",
      value = "Login do portador para o motiva vallo redirecionado pelo Mobile",
      response = FazerLoginPortadorResponse.class)
  public ResponseEntity<FazerLoginPortadorResponse> doLogin(
      @RequestBody @Valid LoginTokenValloMotiva loginTokenValloMotiva,
      HttpServletRequest request,
      BindingResult result)
      throws IOException {
    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na Validação de login do portador.", result);
    }

    if (!Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(
        loginTokenValloMotiva.getIdInstituicao())) {
      throw new GenericServiceException(
          "Caminho indisponível para a instituicao.", HttpStatus.FORBIDDEN);
    }

    travaServicosService.travaServicos(
        loginTokenValloMotiva.getIdInstituicao(), Servicos.LOGIN_PORTADOR);
    TokenFuncionalidade tokenFuncionalidade =
        this.tokenFuncionalidadeService.utilizarTokenByFuncionalidade(
            loginTokenValloMotiva.getToken(), TipoTokenFuncionalidadeEnum.TOKEN_CAMPANHA);
    if (tokenFuncionalidade == null) {
      throw new GenericServiceException("Token inválido.", HttpStatus.UNAUTHORIZED);
    }

    if (!tokenFuncionalidade.getDocumento().equalsIgnoreCase(loginTokenValloMotiva.getCpf())) {
      throw new GenericServiceException(
          "Token informado não pertence ao CPF informado.", HttpStatus.UNAUTHORIZED);
    }
    PortadorLogin portadorLogin =
        portadorLoginService.buscarLogin(
            loginTokenValloMotiva.getIdProcessadora(),
            loginTokenValloMotiva.getIdInstituicao(),
            tokenFuncionalidade.getDocumento(),
            null,
            null,
            null,
            null);
    FazerLoginPortador fazerLoginPortador = new FazerLoginPortador();
    fazerLoginPortador.setCpf(tokenFuncionalidade.getDocumento());
    fazerLoginPortador.setIdInstituicao(loginTokenValloMotiva.getIdInstituicao());
    fazerLoginPortador.setIdProcessadora(loginTokenValloMotiva.getIdProcessadora());
    fazerLoginPortador.setSenha(portadorLogin.getSenha());

    return portadorLoginService.doLogin(request, fazerLoginPortador);
  }

  @ApiOperation(
      value = "Serviço responsável por validar cadastro para onboard",
      response = DetalhesPortadorLogin.class,
      notes = "Validar cadastro para onboard")
  @RequestMapping(
      value = "/validar-cadastro-onboard",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<ValidarCadastroOnboardVO> validarCadastroOnboard(
      @RequestBody ValidarDadosOnboardVO validarDadosOnboardVO) throws IOException {
    ValidarCadastroOnboardVO validacao =
        portadorLoginService.validarCadastroOnboard(validarDadosOnboardVO);
    return new ResponseEntity<>(validacao, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por validar cadastro para onboard sem validação pela CAF",
      response = DetalhesPortadorLogin.class,
      notes = "Validar cadastro para onboard sem validação pela CAF")
  @RequestMapping(
      value = "/validar-cadastro-onboard-sem-caf",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<ValidarCadastroOnboardVO> validarCadastroOnboard2(
      @RequestBody ValidarDadosOnboardVO validarDadosOnboardVO) throws IOException {
    ValidarCadastroOnboardVO validacao =
        portadorLoginService.validarCadastroOnboardSemCaf(validarDadosOnboardVO);
    return new ResponseEntity<>(validacao, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por devolver configuração atual de segurança para redefinição de senha",
      response = Boolean.class,
      notes = "Requer CAF para alterar senha")
  @RequestMapping(
      value = "/encontra-caf-necessario/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<Boolean> encontraCafNecessarioPorIdConta(
      @PathVariable("idConta") Long idConta, HttpServletRequest request) throws IOException {
    SecurityUserPortador portador = getAuthenticatedPortador(request, em);
    Boolean configuracao =
        portadorLoginService.encontraConfiguracaoCafPorIdConta(idConta, portador);
    return new ResponseEntity<>(configuracao, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por devolver configuração atual de segurança para redefinição de senha",
      response = Boolean.class,
      notes = "Requer CAF para alterar senha")
  @RequestMapping(
      value = {
        "/encontra-caf-necessario/instituicao/{idInstituicao}/documento/{documento}",
        "/encontra-caf-necessario/instituicao/{idInstituicao}/documento/{documento}/documentoRepresentante/{documentoRepresentante}"
      },
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Boolean> encontraCafNecessario(
      @PathVariable("documento") String documento,
      @PathVariable Optional<String> documentoRepresentante,
      @PathVariable("idInstituicao") Integer idInstituicao)
      throws IOException {
    Boolean configuracao =
        portadorLoginService.encontraConfiguracaoCaf(
            idInstituicao, documento, documentoRepresentante);
    return new ResponseEntity<>(configuracao, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por devolver tipo login a partir de nome do aplicativo e documento que está acessando",
      response = List.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/resgata-tipos-login/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_PORTADOR_LOGIN"})
  public ResponseEntity<List<String>> resgataTiposLogin(
      @PathVariable("idProdInstituicao") Integer idProdInstituicao) {
    List<String> tipoPortadorLogin = portadorLoginService.resgataTodosTipoLogin(idProdInstituicao);
    return new ResponseEntity<>(tipoPortadorLogin, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por devolver tipo login a partir de nome do aplicativo e documento que está acessando",
      response = List.class)
  @RequestMapping(
      value = "/descobre-tipo-login/{idApp}/documento/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<String>> descobreTipoLoginPorAplicativoEDocumento(
      @PathVariable("idApp") Integer idApp, @PathVariable("documento") String documento) {
    List<String> tipoPortadorLogin =
        portadorLoginService.determinaTipoLoginPorAplicativoEDocumento(idApp, documento);
    return new ResponseEntity<>(tipoPortadorLogin, HttpStatus.OK);
  }

  @ApiOperation(value = "Altera o dispositivo do portador verificando o token CAF gerado da selfie")
  @RequestMapping(
      value = "/alterar-dispositivo-validacao-facial",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<?> alterarDispositivo(
      HttpServletRequest request, @RequestBody AlterarDispositivo alterarDispositivo) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    portadorLoginService.alterarDispositivo(userPortador, alterarDispositivo);
    return new ResponseEntity<>(null, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável validar os dados para criação portador login corporativo",
      response = PortadorLoginCredencialVO.class,
      notes = "Validar cadastro para onboard")
  @RequestMapping(
      value = "/validar-cadastro-corporativo",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<PortadorLoginCredencialVO> validarDadosCadastroCorporativo(
      @RequestBody PortadorLoginCredencialVO portadorLoginCredencialVO) {
    PortadorLoginCredencialVO response =
        portadorLoginService.validarDadosCadastroCorporativo(portadorLoginCredencialVO);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  /**
   * Endpoint para cancelar um PortadorLogin e invalidar suas credenciais.
   *
   * @param idConta id da conta.
   * @return ResponseEntity com mensagem de sucesso ou erro.
   */
  @ApiOperation(value = "Cancela o PortadorLogin e invalida suas credenciais")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cancelar-portador-credencial-virtual/{id_conta}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CANCELAR_PORTADOR_CREDENCIAL"})
  public ResponseEntity<?> cancelarPortadorLogin(
      @PathVariable("id_conta") Long idConta,
      @RequestBody SenhaRequest senhaRequest,
      HttpServletRequest request) {
    try {
      String token = getTokenJWT(request);
      SecurityUser user = getAuthenticatedUser(request, em);

      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
          user, Constantes.ID_PROCESSADORA_ITS_PAY, user.getIdInstituicao(), null, null, null);

      portadorLoginService.cancelarPortadorLogin(
          idConta, user, token, senhaRequest.getSenha(), request);

      Map<String, Object> map = new HashMap<>();
      map.put("msg", "Operação realizada com sucesso!");
      return new ResponseEntity<>(map, HttpStatus.OK);
    } catch (GenericServiceException e) {
      Map<String, Object> map = new HashMap<>();
      map.put("msg", e.getMessage());
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }
  }
}
