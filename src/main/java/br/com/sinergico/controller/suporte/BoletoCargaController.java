package br.com.sinergico.controller.suporte;

import br.com.caelum.stella.boleto.Boleto;
import br.com.entity.suporte.MotivoCobranca;
import br.com.entity.transacional.CobrancaBancaria;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.suporte.BoletoCarga;
import br.com.json.bean.suporte.GerarBoletoCarga;
import br.com.json.bean.transacional.DownloadBoletoCobBancariaPortador;
import br.com.json.bean.transacional.EnviarBoletoCobBancariaPortador;
import br.com.json.bean.transacional.GerarBoletoCobBancariaPortador;
import br.com.json.bean.transacional.GetCobrancasBancarias;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.suporte.BoletoB2BService;
import br.com.sinergico.service.suporte.BoletoCargaService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping(value = "/api/boleto/carga")
public class BoletoCargaController extends UtilController {

  private static final String FMT_D_MMM = "d MMM";

  @Autowired private BoletoCargaService boletoService;

  @Autowired private BoletoB2BService boletoB2BService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private TravaContasService travaContasService;

  @ApiOperation(
      value = "Gera boleto de carga",
      notes = "Retorna a linha digitavel de um boleto e também o código de barras.",
      response = BoletoCarga.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/gerar-linha-digitavel",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<String> gerarBoletoCarga(
      @RequestBody @Valid GerarBoletoCarga model, BindingResult result) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.BOLETO);
    travaContasService.travaContas(Long.valueOf(model.getContaPagamento()), Servicos.BOLETO);

    HashMap<String, String> resp = new HashMap<String, String>();
    resp.put(Constantes.MSG_BACK_END, "Erro Favor Atualizar o Aplicativo");

    return new ResponseEntity<String>(resp.toString(), HttpStatus.MOVED_PERMANENTLY);
  }

  @ApiOperation(value = "Gera boleto de carga", notes = "Envia o boleto por email.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/enviar-boleto-email",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, String>> enviarBoletoCargaPorEmail(
      @RequestBody @Valid GerarBoletoCarga model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Erro na validação da Geração de Boleto para Carga.", result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.BOLETO);
    travaContasService.travaContas(Long.valueOf(model.getContaPagamento()), Servicos.BOLETO);

    Boleto boleto = boletoService.gerarBoletoEEnviarPorEmail(model);

    BoletoCarga boletoCarga = new BoletoCarga();
    BeanUtils.copyProperties(boleto, boletoCarga);
    String dataValidadeFmtMes =
        DateUtil.dateFormat(FMT_D_MMM, boleto.getDatas().getVencimento().getTime());

    boletoCarga.setDataVencimentoFmtMes(dataValidadeFmtMes);

    Map<String, String> map = new HashMap<>();
    map.put("msg", "E-mail enviado com sucesso!");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Gera boleto de carga e fazer download",
      notes = "Envia o boleto por email.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/download-boleto",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public ResponseEntity<InputStreamResource> downloadBoletoCarga(
      @RequestBody @Valid GerarBoletoCarga model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Erro na validação da Geração de Boleto para Carga.", result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.BOLETO);
    travaContasService.travaContas(Long.valueOf(model.getContaPagamento()), Servicos.BOLETO);

    InputStream input = boletoService.gerarBoletoParaDownload(model);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", Arrays.asList("Download finalizado com sucesso!"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Listar cobranças bancárias de um conta",
      notes = "Lista cobranças bancárias de um conta",
      response = GetCobrancasBancarias.class,
      responseContainer = "list")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/listar-cobrancas/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_COBRANCA_CONTA"})
  public ResponseEntity<?> findCobrancasByConta(@PathVariable Long idConta) {

    SecurityUser user = null;
    SecurityUserEstabelecimento userEstabelecimento = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userEstabelecimento = getAuthenticatedUserEstabelecimento(request, em);
    }

    List<GetCobrancasBancarias> cobrancas = boletoService.findCobrancasByConta(idConta, user);

    return new ResponseEntity<>(cobrancas, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Listar motivos do mesmo produto da conta",
      notes = "Lista motivos do mesmo produto da conta",
      response = MotivoCobranca.class,
      responseContainer = "list")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/listar-motivos/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_MOTIVO_CONTA"})
  public ResponseEntity<?> findMotivosByProdConta(@PathVariable Long idConta) {

    List<MotivoCobranca> cobrancas = boletoService.findMotivosByProdConta(idConta);

    return new ResponseEntity<>(cobrancas, HttpStatus.OK);
  }

  @ApiOperation(
      value = "gerar cobrança ",
      notes = "Cobranca bancaria",
      response = CobrancaBancaria.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/gerar-cobranca-boleto/portador",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Secured({"ROLE_GERAR_COBRANCA_CONTA"})
  public ResponseEntity<?> gerarboletoCobrancaBancariaPortador(
      @RequestBody @Valid GerarBoletoCobBancariaPortador model, BindingResult result) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.BOLETO);
    travaContasService.travaContas(model.getIdConta(), Servicos.BOLETO);

    Calendar vencimento = Calendar.getInstance();
    vencimento.setTime(model.getVencimento());
    CobrancaBancaria cobranca;
    log.info("Valor informado para geracao de boleto de carga: " + model.getValor());
    if (model.isOrigemPortador()) {
      cobranca =
          boletoService.gerarBoletoCobrancaBancaria(
              model.getIdConta(),
              model.getValor(),
              vencimento,
              model.getMotivo(),
              model.isConfirmacaoProc());
    } else {
      user = getAuthenticatedUser(request, em);
      cobranca =
          boletoService.gerarBoletoCobrancaBancaria(
              model.getIdConta(), model.getValor(), vencimento, model.getMotivo(), user, false);
    }

    return new ResponseEntity<>(cobranca, HttpStatus.OK);
  }

  @ApiOperation(
      value = "enviar boleto de cobrança por email ",
      notes = "Cobranca bancaria",
      response = Boolean.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/gerar-cobranca-boleto/portador/enviar-boleto-email",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ENVIAR_COBRANCA_EMAIL"})
  public ResponseEntity<?> gerarBoletoCobrancaBancariaEEnviarPorEmail(
      @RequestBody @Valid EnviarBoletoCobBancariaPortador model, BindingResult result) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.BOLETO);

    boletoService.gerarBoletoCobrancaBancariaEEnviarPorEmail(
        model.getIdCobrancaBancaria(), model.getEmailDestino());

    return new ResponseEntity<>(Boolean.TRUE, HttpStatus.OK);
  }

  @ApiOperation(
      value = "download boleto de cobrança ao portador",
      notes = "Download do boleto Cobranca bancaria")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/gerar-cobranca-boleto/portador/download-boleto",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({ "ROLE_DOWNLOAD_BOLETO_COBRANCA" })
  // TEMPORARIAMENTE SEM USO DE PERMISSÃO - 13-03-17
  public ResponseEntity<?> downloadBoletoCobrancaBancaria(
      @RequestBody @Valid DownloadBoletoCobBancariaPortador model, BindingResult result) {

    InputStream input = boletoService.downloadBoletoCobrancaBancaria(model.getIdCobrancaBancaria());

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", Arrays.asList("Download finalizado com sucesso!"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  //	@ApiOperation(value = "gerar cobranças de teste ", notes = "Cobranca bancaria de teste")
  //	@ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //		@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")})
  //	@RequestMapping(value = "/gerar-cobranca-boleto/portador/teste", method = RequestMethod.GET,
  // produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	public ResponseEntity<?> gerarboletoCobrancaBancariaPortadorCorrida() {
  //		SecurityUser user = null;
  //		SecurityUserPortador userPortador = null;
  //		try {
  //			user = getAuthenticatedUser(request, em);
  //		} catch (Exception e) {
  //			userPortador = getAuthenticatedPortador(request, em);
  //		}
  //		travaServicosService.travaServicos(user != null ? user.getIdInstituicao() :
  //				Objects.requireNonNull(userPortador).getIdInstituicao(),Servicos.BOLETO);
  //
  //		boletoService.gerarCobrancasCorrida(user);
  //		return new ResponseEntity<>(true, HttpStatus.OK);
  //	}

  // @ApiOperation(value = "gerar cobranças de teste ", notes = "Cobranca bancaria de teste")
  // @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //	@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")})
  // @RequestMapping(value = "/gerar-cobranca-boleto/portador/teste/{teste}", method =
  // RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	public ResponseEntity<?> enviarCobrancasCorridaPorEmail(@PathVariable boolean teste) throws
  // MessagingException {
  //
  //		boletoService.enviarCobrancasCorridaPorEmail(teste);
  //		return new ResponseEntity<>(true, HttpStatus.OK);
  //	}

  @ApiOperation(value = "Enviar linha digitável do boleto por SMS", response = Boolean.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/gerar-cobranca-boleto/portador/enviar-boleto-sms/{idCobrancaBancaria}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> gerarBoletoAndSendLinhaDigitavelPorSMS(
      @PathVariable Long idCobrancaBancaria) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.BOLETO);

    boletoService.gerarBoletoAndSendLinhaDigitavelPorSMS(idCobrancaBancaria);

    return new ResponseEntity<>(Boolean.TRUE, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Validar se um boleto gerado existe",response = Boolean.class)
  //	@RequestMapping(value = "/validar-boleto-gerado", method = RequestMethod.GET, produces =
  // MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	public ResponseEntity<?> validarExistenciaBoleto(@RequestParam(value = "token",required =
  // false) Long token) {
  //
  //		Boolean existe = boletoService.validarExistenciaBoleto(token);
  //		if(existe)
  //			return new ResponseEntity<>(existe, HttpStatus.OK);
  //		return new ResponseEntity<>(existe, HttpStatus.UNPROCESSABLE_ENTITY);
  //	}

}
