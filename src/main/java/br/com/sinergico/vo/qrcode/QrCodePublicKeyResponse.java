package br.com.sinergico.vo.qrcode;

import com.fasterxml.jackson.annotation.JsonProperty;

public class QrCodeP<PERSON>licKeyResponse {

  @JsonProperty("keyId")
  private String keyId;

  @JsonProperty("keyData")
  private String keyData;

  @JsonProperty("keyExpiration")
  private Integer keyExpiration;

  public QrCodePublicKeyResponse() {
    super();
  }

  public QrCodePublicKeyResponse(String keyId, String keyData, Integer keyExpiration) {
    super();
    this.keyId = keyId;
    this.keyData = keyData;
    this.keyExpiration = keyExpiration;
  }

  public String getKeyId() {
    return keyId;
  }

  public void setKeyId(String keyId) {
    this.keyId = keyId;
  }

  public String getKeyData() {
    return keyData;
  }

  public void setKeyData(String keyData) {
    this.keyData = keyData;
  }

  public Integer getKeyExpiration() {
    return keyExpiration;
  }

  public void setKeyExpiration(Integer keyExpiration) {
    this.keyExpiration = keyExpiration;
  }
}
