package br.com.sinergico.controller.suporte;

import br.com.json.bean.cadastral.BuscaContaPagamentoRetorno;
import br.com.json.bean.suporte.BuscaContaPagamento;
import br.com.json.bean.suporte.BuscaContaPagamentoVO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.BuscaGenericaContaPagamentoService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/busca")
public class BuscaGenericaContaPagamentoController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private BuscaGenericaContaPagamentoService buscaService;

  @ApiOperation(value = "Serviço responsável por buscar um portador a partir de qualquer parâmetro")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento/{first}/{max}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_PORTADOR_BY_FILTROS"})
  public List<BuscaContaPagamentoRetorno> buscarContaPagamento(
      @RequestBody @Valid BuscaContaPagamento busca,
      @PathVariable Integer first,
      @PathVariable Integer max) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<BuscaContaPagamentoRetorno> list =
        buscaService.buscarContaPagamento(
            busca.getStringBusca(), busca.getIsBuscaNroCartao(), user, first, max);
    return list;
  }

  @ApiOperation(
      value =
          "Serviço responsável por contar a busca de um portador a partir de qualquer parâmetro")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento/count",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_PORTADOR_BY_FILTROS"})
  public Long contarContaPagamento(@RequestBody @Valid BuscaContaPagamento busca) {

    SecurityUser user = getAuthenticatedUser(request, em);
    return buscaService.contarContasPagamento(
        busca.getStringBusca(), busca.getIsBuscaNroCartao(), user);
  }

  @ApiOperation(
      value = "Serviço responsável por buscar uma conta a partir de um número de credencial",
      response = BuscaContaPagamentoRetorno.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento-por-credencial",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_CONTA_BY_CREDENCIAL"})
  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorCredencial(
      @RequestBody @Valid BuscaContaPagamento busca) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<BuscaContaPagamentoRetorno> list =
        buscaService.buscarContaPagamentoPorCredencial(busca.getStringBusca(), user, null, null);
    return list;
  }

  @ApiOperation(
      value = "Serviço responsável por buscar uma conta a partir de um número de conta",
      response = BuscaContaPagamentoRetorno.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento-por-numero-conta",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_CONTA_BY_NUMERO"})
  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorNumeroConta(
      @RequestBody @Valid BuscaContaPagamento busca) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<BuscaContaPagamentoRetorno> list =
        buscaService.buscarContaPagamentoPorNumeroConta(busca.getStringBusca(), user, null, null);
    return list;
  }

  @ApiOperation(
      value = "Serviço responsável por buscar uma conta a partir de um id de conta",
      response = BuscaContaPagamentoRetorno.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento-por-id-conta",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_CONTA_BY_ID"})
  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorIdConta(
      @RequestBody @Valid BuscaContaPagamento busca) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<BuscaContaPagamentoRetorno> list =
        buscaService.buscarContaPagamentoPorIdConta(
            Long.parseLong(busca.getStringBusca()), user, null, null);
    return list;
  }

  @ApiOperation(
      value = "Serviço responsável por buscar uma conta a partir de um documento (CPF ou CNPJ)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento-por-documento",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_CONTA_BY_DOCUMENTO"})
  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorDocumento(
      @RequestBody @Valid BuscaContaPagamento busca) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<BuscaContaPagamentoRetorno> list =
        buscaService.buscarContaPagamentoPorDocumento(busca.getStringBusca(), user, null, null);
    return list;
  }

  @ApiOperation(value = "Serviço responsável por buscar uma conta a partir de um nome")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento-por-nome",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_CONTA_BY_NOME"})
  public List<BuscaContaPagamentoRetorno> buscarContaPagamentoPorNome(
      @RequestBody @Valid BuscaContaPagamento busca) {

    SecurityUser user = getAuthenticatedUser(request, em);
    return buscaService.buscarContaPagamentoPorNome(busca.getStringBusca(), user, null, null);
  }

  @ApiOperation(
      value =
          "Serviço responsável por filtrar a busca de um portador a partir de qualquer parâmetro")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento/filtro/{first}/{max}/{idInstituicao}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_PORTADOR_BY_FILTROS"})
  public List<BuscaContaPagamentoRetorno> filtroContaPagamento(
      @RequestBody @Valid BuscaContaPagamentoVO res,
      @PathVariable Integer first,
      @PathVariable Integer max,
      @PathVariable Integer idInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<BuscaContaPagamentoRetorno> listaFilto =
        buscaService.buscaPorFiltro(
            res.getStringBusca(), res.getIsBuscaNroCartao(), user, first, max, idInstituicao);
    return listaFilto;
  }

  @ApiOperation(
      value =
          "Serviço responsável por contar a busca de um portador a partir do parâmetro filtrado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pagamento/count-filtro/{idInstituicao}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_PORTADOR_BY_FILTROS"})
  public Long contarContaPagamentoFiltro(
      @RequestBody @Valid BuscaContaPagamentoVO res, @PathVariable Integer idInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);
    return buscaService.contarContasPagamentoFiltro(
        res.getStringBusca(), res.getIsBuscaNroCartao(), user, idInstituicao);
  }
}
