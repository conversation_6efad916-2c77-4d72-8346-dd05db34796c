package br.com.sinergico.service.suporte;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import br.com.entity.suporte.AcessoUsuario;
import br.com.json.bean.cadastral.BuscaGenericaFiltro;
import br.com.json.bean.suporte.AcessoUsuarioDTO;
import br.com.sinergico.repository.suporte.AcessoUsuarioRepository;
import br.com.sinergico.util.Constantes;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
public class AcessoUsuarioServiceTest {

  @Mock private AcessoUsuarioRepository usuarioRepo;

  @InjectMocks private AcessoUsuarioService acessoUsuarioService;

  @Test
  public void testFindByDynamicFiltersQualquerValor() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY, 1801, 1, 1, 1, "Gabriel", "04912788104", 1, 1, 1);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Add instances of AcessoUsuario as needed
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    when(usuarioRepo.buscarPessoasComFiltros(
            anyInt(),
            anyInt(),
            anyInt(),
            anyInt(),
            anyString(),
            anyString(),
            anyInt(),
            anyInt(),
            anyInt(),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicFiltersTodosCamposNulos() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(null, null, null, null, null, null, null, null, null, null);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Add instances of AcessoUsuario as needed
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    when(usuarioRepo.buscarPessoasComFiltros(
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicFiltersComIdInstituicao() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY,
            1801,
            null,
            null,
            null,
            null,
            null,
            null,
            null,
            null);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Add instances of AcessoUsuario as needed
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    when(usuarioRepo.buscarPessoasComFiltros(
            eq(1801),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(null),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicFiltersComIdInstituicaoNomeEDocumento() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY,
            1801,
            null,
            null,
            null,
            "Gabriel",
            "04912788104",
            null,
            null,
            null);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Add instances of AcessoUsuario as needed
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    when(usuarioRepo.buscarPessoasComFiltros(
            eq(1801),
            eq(null),
            eq(null),
            eq(null),
            eq("%Gabriel%".toUpperCase()),
            eq("04912788104"),
            eq(null),
            eq(null),
            eq(null),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicFiltersUsuarioNulo() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY, 1801, 1, 1, 1, null, "04912788104", 1, 1, 1);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Add instances of AcessoUsuario as needed
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    when(usuarioRepo.buscarPessoasComFiltros(
            eq(1801),
            eq(1),
            eq(1),
            eq(1),
            isNull(),
            eq("04912788104"),
            eq(1),
            eq(1),
            eq(1),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicFiltersCPFNulo() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY, 1801, 1, 1, 1, "Gabriel Barros", null, 1, 1, 1);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Adicione instâncias de AcessoUsuario conforme necessário
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    // Corrigir a configuração do mock para corresponder aos argumentos reais
    when(usuarioRepo.buscarPessoasComFiltros(
            eq(1801),
            eq(1),
            eq(1),
            eq(1),
            eq("%Gabriel Barros%".toUpperCase()),
            isNull(),
            eq(1),
            eq(1),
            eq(1),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicFiltersInstituicaoEDocumentoEIdUsuarioEIdGrupoEStatus() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY,
            1801,
            null,
            null,
            null,
            null,
            "04912788104",
            1,
            1,
            1);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Add instances of AcessoUsuario as needed
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    when(usuarioRepo.buscarPessoasComFiltros(
            eq(1801),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            eq("04912788104"),
            eq(1),
            eq(1),
            eq(1),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }

  @Test
  public void testFindByDynamicInstituicaoERegional() {
    // Setup mocks
    BuscaGenericaFiltro filtro =
        new BuscaGenericaFiltro(
            Constantes.ID_PROCESSADORA_ITS_PAY, 1801, 1, null, null, null, null, null, null, null);
    Pageable pageable = PageRequest.of(0, 10);
    List<AcessoUsuario> usuarios = new ArrayList<>();
    usuarios.add(new AcessoUsuario()); // Adicione instâncias de AcessoUsuario conforme necessário
    Page<AcessoUsuario> page = new PageImpl<>(usuarios, pageable, usuarios.size());

    // Corrigir a configuração do mock para corresponder aos argumentos reais
    when(usuarioRepo.buscarPessoasComFiltros(
            eq(1801),
            eq(1),
            eq(null),
            eq(null),
            eq(null),
            isNull(),
            eq(null),
            eq(null),
            eq(null),
            eq(pageable)))
        .thenReturn(page);

    // Call the method to be tested
    Page<AcessoUsuarioDTO> result = acessoUsuarioService.findByDynamicFilters(0, 10, filtro);

    // Verify the results
    assertEquals(1, result.getTotalElements());
    assertEquals(1, result.getContent().size());
  }
}
