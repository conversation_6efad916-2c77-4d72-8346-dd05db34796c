package br.com.sinergico.service.totvs.api;

import br.com.client.rest.totvs.json.bean.request.PreRegistroContatoRequest;
import br.com.client.rest.totvs.json.bean.response.PreRegistroEmpresaContaResponse;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.suporte.Contato;
import org.springframework.stereotype.Service;

@Service
public class PreRegistroContatoTotvsService extends TotvsService {

  private static final String URL_ADICIONAR_CONTATO = "/api/pre-registro/adicionar-contato";
  private static final String URL_ATUALIZAR_CONTATO = "/api/pre-registro/atualizar-contato";

  public void preRegistrarContatoEmpresaTotvs(Contato contato) {
    if (enviarDadosParaTotvs(contato.getIdProcessadora(), contato.getIdInstituicao())) {
      preRegistroContato(preparaPreRegistroRequestContatoEmpresa(contato));
    }
  }

  public void atualizarContatoEmpresaTotvs(Contato contato) {
    if (enviarDadosParaTotvs(contato.getIdProcessadora(), contato.getIdInstituicao())
        && contatoPodeSerAtualizado(null, contato.getIdContato())) {
      alteracaoPreRegistroContato(preparaPreRegistroRequestContatoEmpresa(contato));
    }
  }

  public void atualizarContatoPessoaTotvs(Pessoa pessoa) {
    if (enviarDadosParaTotvs(pessoa.getIdProcessadora(), pessoa.getIdInstituicao())
        && contatoPodeSerAtualizado(pessoa.getIdPessoa(), null)) {
      alteracaoPreRegistroContato(preparaPreRegistroRequestContatoPessoa(pessoa));
    }
  }

  protected PreRegistroEmpresaContaResponse preRegistroContato(
      PreRegistroContatoRequest preRegistroContatoRequest) {
    return doPost(
        preRegistroContatoRequest,
        new PreRegistroEmpresaContaResponse(),
        getUrlBaseTotvs() + URL_ADICIONAR_CONTATO);
  }

  protected PreRegistroEmpresaContaResponse alteracaoPreRegistroContato(
      PreRegistroContatoRequest preRegistroContatoRequest) {
    return doPost(
        preRegistroContatoRequest,
        new PreRegistroEmpresaContaResponse(),
        getUrlBaseTotvs() + URL_ATUALIZAR_CONTATO);
  }

  private static PreRegistroContatoRequest preparaPreRegistroRequestContatoEmpresa(
      Contato contato) {

    PreRegistroContatoRequest preRegistroContatoRequest = new PreRegistroContatoRequest();
    preencheDadosEmComum(
        preRegistroContatoRequest,
        contato.getIdProcessadora(),
        contato.getIdInstituicao(),
        contato.getIdRegional(),
        contato.getIdFilial(),
        contato.getIdPontoDeRelacionamento());
    preRegistroContatoRequest.setIdContato(contato.getIdContato());
    preRegistroContatoRequest.setNomeContato(contato.getNomeContato());
    preRegistroContatoRequest.setEmail(contato.getEmail());
    preRegistroContatoRequest.setDddTelefone(contato.getDddTelefoneFixo());
    preRegistroContatoRequest.setTelefone(contato.getTelefoneFixo());
    return preRegistroContatoRequest;
  }

  private static PreRegistroContatoRequest preparaPreRegistroRequestContatoPessoa(Pessoa pessoa) {

    String nome =
        pessoa.getNomeCompleto() != null && !pessoa.getNomeCompleto().isEmpty()
            ? pessoa.getNomeCompleto()
            : pessoa.getRazaoSocial();

    PreRegistroContatoRequest preRegistroContatoRequest = new PreRegistroContatoRequest();
    preencheDadosEmComum(
        preRegistroContatoRequest,
        pessoa.getIdProcessadora(),
        pessoa.getIdInstituicao(),
        null,
        null,
        null);
    preRegistroContatoRequest.setIdPessoa(pessoa.getIdPessoa());
    preRegistroContatoRequest.setNomeContato(nome);
    preRegistroContatoRequest.setEmail(pessoa.getEmail());
    preRegistroContatoRequest.setDddTelefone(pessoa.getDddTelefoneCelular());
    preRegistroContatoRequest.setTelefone(pessoa.getTelefoneCelular());
    return preRegistroContatoRequest;
  }

  private static void preencheDadosEmComum(
      PreRegistroContatoRequest preRegistroContatoRequest,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    preRegistroContatoRequest.setIdProcessadora(idProcessadora);
    preRegistroContatoRequest.setIdInstituicao(idInstituicao);
    preRegistroContatoRequest.setIdRegional(idRegional);
    preRegistroContatoRequest.setIdFilial(idFilial);
    preRegistroContatoRequest.setIdPontoRelacionamento(idPontoDeRelacionamento);
  }
}
