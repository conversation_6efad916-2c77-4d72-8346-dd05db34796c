package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.LimiteTransacaoConta;
import br.com.entity.cadastral.LimiteTransacaoProduto;
import br.com.entity.cadastral.LimiteTransacaoSolicitacao;
import br.com.entity.cadastral.LimiteTransacaoTipo;
import br.com.entity.cadastral.LimiteTransacaoTipoFunctioncode;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.transacional.TED;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.LimiteTransacaoSolicitacaoVo;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.LimiteTransacaoTipoEnum;
import br.com.sinergico.enums.NomeCampoLimiteEnum;
import br.com.sinergico.enums.StatusTransacao;
import br.com.sinergico.repository.cadastral.ContaPagamentoRepository;
import br.com.sinergico.repository.cadastral.LimiteTransacaoContaRepository;
import br.com.sinergico.repository.cadastral.LimiteTransacaoProdutoRepository;
import br.com.sinergico.repository.cadastral.LimiteTransacaoTipoFunctionCodeRepository;
import br.com.sinergico.repository.cadastral.LimiteTransacaoTipoRepository;
import br.com.sinergico.repository.cadastral.PessoaRepository;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoConfiguracaoRepository;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoRepository;
import br.com.sinergico.repository.cadastral.SolicitarLimiteContaRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.repository.transacional.TEDRepository;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.AplicativoMensagemService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.vo.BuscaLimiteVO;
import br.com.sinergico.vo.ConsultaLimiteAtualDTO;
import br.com.sinergico.vo.LimiteDiarioVO;
import br.com.sinergico.vo.LimiteTransacaoContaProdutoVO;
import br.com.sinergico.vo.LimiteTransacaoContaVO;
import br.com.sinergico.vo.LimiteTransacaoProdutoVO;
import br.com.sinergico.vo.LimiteTransacaoSolicitacaoContaVO;
import br.com.sinergico.vo.SolicitarNovoLimiteVO;
import br.com.sinergico.vo.TransacoesPorFcDiaVO;
import br.com.sinergico.vo.ValidaLimitePortadorVO;
import java.math.BigDecimal;
import java.math.MathContext;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class LimitesContaService {

  @Autowired private SolicitarLimiteContaRepository solicitarLimiteContaRepository;

  @Autowired private LimiteTransacaoContaRepository limiteTransacaoContaRepository;

  @Autowired
  private LimiteTransacaoTipoFunctionCodeRepository limiteTransacaoTipoFunctionCodeRepository;

  @Autowired private ContaPagamentoRepository contaPagamentoRepository;

  @Autowired private LimiteTransacaoProdutoRepository limiteTransacaoProdutoRepository;

  @Autowired private LimiteTransacaoTipoRepository limiteTransacaoTipoRepository;

  @Autowired private ProdutoInstituicaoRepository produtoInstituicaoRepository;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired PortadorLoginService portadorLoginService;

  @Autowired AcessoUsuarioService acessoUsuarioService;

  @Autowired ContaPagamentoService contaPagamentoService;

  @Autowired private PessoaRepository pessoaRepository;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private AplicativoMensagemService aplicativoMensagemService;

  @Autowired
  private ProdutoInstituicaoConfiguracaoRepository produtoInstituicaoConfiguracaoRepository;

  @Autowired private LogPagtoTituloValidacaoRepository logPagtoValidacaoRepository;

  @Autowired private TEDRepository tedRepository;

  private static final int STATUS_ATIVO = 1;
  private static final int STATUS_INATIVO = 0;

  private static final Logger log = LoggerFactory.getLogger(LimitesContaService.class);
  @Autowired private UtilService utilService;

  public LimiteTransacaoConta consultarLimiteAtual(
      Long idConta, LimiteTransacaoTipoEnum idTipoTransacao) {
    ContaPagamento contaPagamento = this.contaPagamentoRepository.findByIdConta(idConta);
    LimiteTransacaoTipo limiteTransacaoTipo =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(idTipoTransacao);
    return this.limiteTransacaoContaRepository
        .findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
            contaPagamento.getIdConta(), STATUS_ATIVO, limiteTransacaoTipo.getId());
  }

  public List<String> solicitarLimites(
      List<SolicitarNovoLimiteVO> solicitarLimitesVO, SecurityUserPortador userPortador) {
    List<String> mensagem = new ArrayList<>();

    Long idConta = solicitarLimitesVO.get(0).getIdConta();
    if (!solicitarLimitesVO.stream().allMatch(s -> s.getIdConta().equals(idConta))) {
      throw new GenericServiceException(
          "Contas diferentes na lista de solicitações de alteração de limite",
          HttpStatus.FORBIDDEN);
    }

    for (SolicitarNovoLimiteVO novoLimiteVO : solicitarLimitesVO) {
      contaPagamentoService.validaIdContaPeloRequestEPortador(
          novoLimiteVO.getIdConta(), userPortador);
      mensagem.add(this.solicitarNovoLimite(novoLimiteVO));
    }
    return mensagem;
  }

  public List<String> solicitarLimites(
      List<SolicitarNovoLimiteVO> solicitarLimitesVO, SecurityUserCorporativo userCorporativo) {
    List<String> mensagem = new ArrayList<>();

    Long idConta = solicitarLimitesVO.get(0).getIdConta();
    if (!solicitarLimitesVO.stream().allMatch(s -> s.getIdConta().equals(idConta))) {
      throw new GenericServiceException(
          "Contas diferentes na lista de solicitações de alteração de limite",
          HttpStatus.FORBIDDEN);
    }

    for (SolicitarNovoLimiteVO novoLimiteVO : solicitarLimitesVO) {
      contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
          novoLimiteVO.getIdConta(), userCorporativo);
      mensagem.add(this.solicitarNovoLimite(novoLimiteVO));
    }
    return mensagem;
  }

  private String solicitarNovoLimite(SolicitarNovoLimiteVO solicitarNovoLimiteVO) {
    String mensagem;

    try {
      LimiteTransacaoSolicitacao limiteTransacaoSolicitacao =
          getSituacaoLimiteSolicitacao(
              solicitarNovoLimiteVO.getIdConta(),
              solicitarNovoLimiteVO.getIdTipoTransacao(),
              solicitarNovoLimiteVO.getNomeCampoLimite(),
              "PENDENTE");

      if (limiteTransacaoSolicitacao == null) {
        ContaPagamento contaPagamento =
            contaPagamentoRepository.findByIdConta(solicitarNovoLimiteVO.getIdConta());
        LimiteTransacaoTipo limiteTransacaoTipo =
            limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
                solicitarNovoLimiteVO.getIdTipoTransacao());

        LimiteTransacaoSolicitacao novaSolicitacao = new LimiteTransacaoSolicitacao();
        novaSolicitacao.setIdUsuarioSolicitacao(999999);
        novaSolicitacao.setContaPagamento(contaPagamento);
        novaSolicitacao.setIdConta(contaPagamento.getIdConta());
        novaSolicitacao.setValorAtual(solicitarNovoLimiteVO.getValorAtual());
        novaSolicitacao.setValorSolicitado(solicitarNovoLimiteVO.getValorSolicitado());
        novaSolicitacao.setSituacaoStatusSolicitacao("PENDENTE");
        novaSolicitacao.setIdProdInstituicao(contaPagamento.getIdProdutoInstituicao());
        novaSolicitacao.setLimiteTransacaoTipo(limiteTransacaoTipo);
        novaSolicitacao.setNomeCampoLimite(solicitarNovoLimiteVO.getNomeCampoLimite());
        novaSolicitacao.setDataHoraSolicitacao(LocalDateTime.now());

        solicitarLimiteContaRepository.save(novaSolicitacao);

        mensagem = "Solicitação realizada com sucesso!";
      } else {
        log.warn(
            "Solicitação para o campo limite "
                + solicitarNovoLimiteVO.getNomeCampoLimite()
                + " ja existe.");
        mensagem =
            "Solicitação para o campo limite "
                + solicitarNovoLimiteVO.getNomeCampoLimite()
                + " ja existe.";
      }
    } catch (Exception e) {
      log.error("Não foi possivel realizar solicitação", e);
      throw new GenericServiceException(
          "Não foi possivel realizar solicitação", HttpStatus.UNPROCESSABLE_ENTITY);
    }
    return mensagem;
  }

  public List<LimiteTransacaoSolicitacao> getSituacaoSolicitacao(
      Long idConta, LimiteTransacaoTipoEnum idTipoTransacao) {
    return solicitarLimiteContaRepository.findByIdContaAndLimiteTransacaoTipo_Id(
        idConta, idTipoTransacao);
  }

  public List<LimiteTransacaoSolicitacao> getSituacaoSolicitacaoByStatusList(
      Long idConta, LimiteTransacaoTipoEnum idTipoTransacao, String statusSolicitacao) {
    return solicitarLimiteContaRepository
        .findByIdContaAndSituacaoStatusSolicitacaoAndLimiteTransacaoTipo_Id(
            idConta, statusSolicitacao, idTipoTransacao);
  }

  public LimiteTransacaoSolicitacao getSituacaoLimiteSolicitacao(
      Long idConta,
      LimiteTransacaoTipoEnum idTipoTransacao,
      NomeCampoLimiteEnum nomeCampoLimite,
      String statusSolicitacao) {
    return solicitarLimiteContaRepository
        .findByIdContaAndNomeCampoLimiteAndSituacaoStatusSolicitacaoAndLimiteTransacaoTipo_id(
            idConta, nomeCampoLimite, statusSolicitacao, idTipoTransacao);
  }

  public LimiteTransacaoSolicitacaoContaVO buscaLimiteContaAndSolicitacao(
      BuscaLimiteVO model, SecurityUser user) {

    consultaRestritaService.checaPrivilegio(model.getIdConta(), user);

    LimiteTransacaoConta limiteTransacaoConta =
        consultarLimiteAtual(model.getIdConta(), model.getIdTipoTransacao());
    List<LimiteTransacaoSolicitacao> limiteTransacaoSolicitacao =
        getSituacaoSolicitacao(model.getIdConta(), model.getIdTipoTransacao());

    LimiteTransacaoSolicitacaoContaVO limiteTransacaoSolicitacaoContaVO =
        new LimiteTransacaoSolicitacaoContaVO();

    if (limiteTransacaoConta != null) {
      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
          user, limiteTransacaoConta.getContaPagamento());

      limiteTransacaoSolicitacaoContaVO.setIdConta(
          limiteTransacaoConta.getContaPagamento().getIdConta());
      limiteTransacaoSolicitacaoContaVO.setLimiteTransacaoTipo(
          limiteTransacaoConta.getLimiteTransacaoTipo().getId());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteUnitarioDia(
          limiteTransacaoConta.getValorLimiteUnitarioDia());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteUnitarioNoite(
          limiteTransacaoConta.getValorLimiteUnitarioNoite());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteSomaDia(
          limiteTransacaoConta.getValorLimiteSomaDia());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteSomaNoite(
          limiteTransacaoConta.getValorLimiteSomaNoite());
      limiteTransacaoSolicitacaoContaVO.setQtLimiteSomaDia(
          limiteTransacaoConta.getQtLimiteSomaDia());
      limiteTransacaoSolicitacaoContaVO.setQtLimiteSomaNoite(
          limiteTransacaoConta.getQtLimiteSomaNoite());
      limiteTransacaoSolicitacaoContaVO.setSituacaoStatus(limiteTransacaoConta.getSituacaoStatus());
      limiteTransacaoSolicitacaoContaVO.setDtHrStatus(limiteTransacaoConta.getDtHrStatus());
      limiteTransacaoSolicitacaoContaVO.setDtHrInclusao(limiteTransacaoConta.getDtHrInclusao());

      for (int i = 0; i < limiteTransacaoSolicitacao.size(); i++) {
        if (limiteTransacaoSolicitacao
                .get(i)
                .getNomeCampoLimite()
                .equals(NomeCampoLimiteEnum.VALOR_LIMITE_UNITARIO_DIA)
            && limiteTransacaoSolicitacao
                .get(i)
                .getSituacaoStatusSolicitacao()
                .equals("PENDENTE")) {
          limiteTransacaoSolicitacaoContaVO.setStatusLimiteUnitarioDia(true);
        } else if (limiteTransacaoSolicitacao
                .get(i)
                .getNomeCampoLimite()
                .equals(NomeCampoLimiteEnum.VALOR_LIMITE_SOMA_DIA)
            && limiteTransacaoSolicitacao
                .get(i)
                .getSituacaoStatusSolicitacao()
                .equals("PENDENTE")) {
          limiteTransacaoSolicitacaoContaVO.setStatusLimiteSomaDia(true);
        }
      }
    }

    return limiteTransacaoSolicitacaoContaVO;
  }

  public LimiteTransacaoSolicitacaoContaVO buscaLimiteContaAndSolicitacaoPortador(
      BuscaLimiteVO model, SecurityUserPortador userPortador) {
    LimiteTransacaoConta limiteTransacaoConta =
        consultarLimiteAtual(model.getIdConta(), model.getIdTipoTransacao());
    List<LimiteTransacaoSolicitacao> limiteTransacaoSolicitacao =
        getSituacaoSolicitacao(model.getIdConta(), model.getIdTipoTransacao());

    contaPagamentoService.validaIdContaPeloRequestEPortador(model.getIdConta(), userPortador);

    LimiteTransacaoSolicitacaoContaVO limiteTransacaoSolicitacaoContaVO =
        new LimiteTransacaoSolicitacaoContaVO();
    limiteTransacaoSolicitacaoContaVO.setIdConta(
        limiteTransacaoConta.getContaPagamento().getIdConta());
    limiteTransacaoSolicitacaoContaVO.setLimiteTransacaoTipo(
        limiteTransacaoConta.getLimiteTransacaoTipo().getId());
    limiteTransacaoSolicitacaoContaVO.setValorLimiteUnitarioDia(
        limiteTransacaoConta.getValorLimiteUnitarioDia());
    limiteTransacaoSolicitacaoContaVO.setValorLimiteUnitarioNoite(
        limiteTransacaoConta.getValorLimiteUnitarioNoite());
    limiteTransacaoSolicitacaoContaVO.setValorLimiteSomaDia(
        limiteTransacaoConta.getValorLimiteSomaDia());
    limiteTransacaoSolicitacaoContaVO.setValorLimiteSomaNoite(
        limiteTransacaoConta.getValorLimiteSomaNoite());
    limiteTransacaoSolicitacaoContaVO.setQtLimiteSomaDia(limiteTransacaoConta.getQtLimiteSomaDia());
    limiteTransacaoSolicitacaoContaVO.setQtLimiteSomaNoite(
        limiteTransacaoConta.getQtLimiteSomaNoite());
    limiteTransacaoSolicitacaoContaVO.setSituacaoStatus(limiteTransacaoConta.getSituacaoStatus());
    limiteTransacaoSolicitacaoContaVO.setDtHrStatus(limiteTransacaoConta.getDtHrStatus());
    limiteTransacaoSolicitacaoContaVO.setDtHrInclusao(limiteTransacaoConta.getDtHrInclusao());

    for (int i = 0; i < limiteTransacaoSolicitacao.size(); i++) {
      if (limiteTransacaoSolicitacao
              .get(i)
              .getNomeCampoLimite()
              .equals(NomeCampoLimiteEnum.VALOR_LIMITE_UNITARIO_DIA)
          && limiteTransacaoSolicitacao.get(i).getSituacaoStatusSolicitacao().equals("PENDENTE")) {
        limiteTransacaoSolicitacaoContaVO.setStatusLimiteUnitarioDia(true);
      } else if (limiteTransacaoSolicitacao
              .get(i)
              .getNomeCampoLimite()
              .equals(NomeCampoLimiteEnum.VALOR_LIMITE_SOMA_DIA)
          && limiteTransacaoSolicitacao.get(i).getSituacaoStatusSolicitacao().equals("PENDENTE")) {
        limiteTransacaoSolicitacaoContaVO.setStatusLimiteSomaDia(true);
      }
    }

    return limiteTransacaoSolicitacaoContaVO;
  }

  public LimiteTransacaoProduto consultarLimiteAtualProduto(
      Integer idProdutoInstituicao, LimiteTransacaoTipoEnum idTipoTransacao) {
    LimiteTransacaoTipo limiteTransacaoTipo =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(idTipoTransacao);
    ProdutoInstituicao produtoInstituicao =
        this.produtoInstituicaoRepository.findByIdProdInstituicao(idProdutoInstituicao);

    return this.limiteTransacaoProdutoRepository
        .findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
            produtoInstituicao.getIdProdInstituicao(), limiteTransacaoTipo, 1);
  }

  public List<LimiteTransacaoSolicitacao> consultarAlteracaoLimite(
      Integer idInstituicao, LimiteTransacaoTipoEnum idTipoTransacao) {
    LimiteTransacaoTipo limiteTransacaoTipo =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(idTipoTransacao);
    ProdutoInstituicao produtoInstituicao =
        this.produtoInstituicaoRepository.findByIdProdInstituicao(idInstituicao);

    List<LimiteTransacaoSolicitacao> limitesSolicitados =
        this.solicitarLimiteContaRepository
            .findByIdProdInstituicaoAndLimiteTransacaoTipoAndSituacaoStatusSolicitacao(
                produtoInstituicao.getIdProdInstituicao(), limiteTransacaoTipo, "PENDENTE");

    Pessoa pessoa;

    for (LimiteTransacaoSolicitacao limiteSolicitado : limitesSolicitados) {
      limiteSolicitado.setNumeroConta(limiteSolicitado.getContaPagamento().getIdConta());
      pessoa =
          this.pessoaRepository.findPessoaTitularConta(
              limiteSolicitado.getContaPagamento().getIdConta());
      limiteSolicitado.setPortador(pessoa.getNomeCompleto());
    }
    return limitesSolicitados;
  }

  @Transactional
  public LimiteTransacaoProduto cadastrarLimiteProduto(
      LimiteTransacaoProduto limiteTransacaoProduto) {
    LimiteTransacaoProduto limiteAtual =
        limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
            limiteTransacaoProduto.getIdProdInstituicao(),
            limiteTransacaoProduto.getLimiteTransacaoTipo(),
            1);
    if (limiteAtual != null) {
      limiteAtual.setStatus(0);
      this.limiteTransacaoProdutoRepository.save(limiteAtual);
    }
    LimiteTransacaoProduto novoLimite = new LimiteTransacaoProduto();
    novoLimite.setValorLimiteUnitarioDia(limiteTransacaoProduto.getValorLimiteUnitarioDia());
    novoLimite.setValorLimiteSomaDia(limiteTransacaoProduto.getValorLimiteSomaDia());
    novoLimite.setIdProdInstituicao(limiteTransacaoProduto.getIdProdInstituicao());
    novoLimite.setStatus(1);
    novoLimite.setLimiteTransacaoTipo(limiteTransacaoProduto.getLimiteTransacaoTipo());

    this.limiteTransacaoProdutoRepository.save(novoLimite);

    return novoLimite;
  }

  @Transactional
  public LimiteTransacaoSolicitacao analisarLimite(
      LimiteTransacaoSolicitacao limiteTransacaoSolicitacao, SecurityUser securityUser) {
    AcessoUsuario acessoUsuario =
        this.acessoUsuarioService.findByIdUsuario(securityUser.getIdUsuario());
    LimiteTransacaoSolicitacao solicitacao =
        solicitarLimiteContaRepository.findById(limiteTransacaoSolicitacao.getId());

    if (Objects.equals(limiteTransacaoSolicitacao.getSituacaoStatusSolicitacao(), "APROVADO")) {
      LimiteTransacaoConta limiteTransacaoConta =
          limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
              limiteTransacaoSolicitacao.getNumeroConta(),
              STATUS_ATIVO,
              limiteTransacaoSolicitacao.getLimiteTransacaoTipo().getId());

      if (limiteTransacaoConta == null) {
        limiteTransacaoConta = new LimiteTransacaoConta();
        limiteTransacaoConta.setContaPagamento(limiteTransacaoSolicitacao.getContaPagamento());
        limiteTransacaoConta.setIdConta(
            limiteTransacaoSolicitacao.getContaPagamento().getIdConta());
        limiteTransacaoConta.setLimiteTransacaoTipo(
            limiteTransacaoSolicitacao.getLimiteTransacaoTipo());
        limiteTransacaoConta.setDtHrInclusao(LocalDateTime.now());
        limiteTransacaoConta.setSituacaoStatus(STATUS_ATIVO);
        limiteTransacaoConta.setDtHrStatus(LocalDateTime.now());
      }

      switch (limiteTransacaoSolicitacao.getNomeCampoLimite()) {
        case VALOR_LIMITE_UNITARIO_DIA:
          limiteTransacaoConta.setValorLimiteUnitarioDia(solicitacao.getValorSolicitado());
          break;
        case VALOR_LIMITE_SOMA_DIA:
          limiteTransacaoConta.setValorLimiteSomaDia(solicitacao.getValorSolicitado());
          break;
        case VALOR_LIMITE_UNITARIO_NOITE:
          limiteTransacaoConta.setValorLimiteUnitarioNoite(solicitacao.getValorSolicitado());
          break;
        case VALOR_LIMITE_SOMA_NOITE:
          limiteTransacaoConta.setValorLimiteSomaNoite(solicitacao.getValorSolicitado());
          break;
        default:
          break;
      }

      this.limiteTransacaoContaRepository.save(limiteTransacaoConta);
    }

    solicitacao.setSituacaoStatusSolicitacao(
        limiteTransacaoSolicitacao.getSituacaoStatusSolicitacao());
    solicitacao.setUsuarioAutorizacao(acessoUsuario);
    solicitacao.setDataHoraAutorizacao(new Date());
    solicitacao.setTxMotivo(limiteTransacaoSolicitacao.getTxMotivo());

    this.aplicativoMensagemService.enviarMensagem(solicitacao, acessoUsuario);

    this.solicitarLimiteContaRepository.save(solicitacao);
    return solicitacao;
  }

  public Boolean verificaLimitePortadorPorTransacao(ValidaLimitePortadorVO validaLimitePortadorVO) {

    //        Apenas testes locais
    //        int ano = 2022;
    //        int dia = 8;
    //        Date dataHoje = new Date(ano - 1900, Calendar.APRIL, dia);

    //        Date dataHoje = new Date();

    LimiteTransacaoContaProdutoVO limiteTransacaoContaProdutoVO =
        new LimiteTransacaoContaProdutoVO();

    ContaPagamento contaPagamento =
        this.contaPagamentoRepository.findByIdConta(validaLimitePortadorVO.getIdConta());

    LimiteTransacaoTipo ltp =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
            validaLimitePortadorVO.getIdTipoTransacao());

    LimiteTransacaoConta limitesTransacaoConta =
        this.limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
            contaPagamento.getIdConta(), STATUS_ATIVO, ltp.getId());

    LimiteTransacaoProduto limiteTransacaoProduto =
        this.limiteTransacaoProdutoRepository
            .findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
                validaLimitePortadorVO.getIdProdInstituicao(), ltp, 1);

    if (limitesTransacaoConta != null || limiteTransacaoProduto != null) {
      limiteTransacaoContaProdutoVO.setValorLimiteSomaDia(
          limitesTransacaoConta != null && limitesTransacaoConta.getValorLimiteSomaDia() != null
              ? limitesTransacaoConta.getValorLimiteSomaDia()
              : limiteTransacaoProduto.getValorLimiteSomaDia());
      limiteTransacaoContaProdutoVO.setValorLimiteUnitarioDia(
          limitesTransacaoConta != null && limitesTransacaoConta.getValorLimiteUnitarioDia() != null
              ? limitesTransacaoConta.getValorLimiteUnitarioDia()
              : limiteTransacaoProduto.getValorLimiteUnitarioDia());
      limiteTransacaoContaProdutoVO.setValorLimiteSomaNoite(
          limitesTransacaoConta != null && limitesTransacaoConta.getValorLimiteSomaNoite() != null
              ? limitesTransacaoConta.getValorLimiteSomaNoite()
              : limiteTransacaoProduto.getValorLimiteSomaNoite());
      limiteTransacaoContaProdutoVO.setValorLimiteUnitarioNoite(
          limitesTransacaoConta != null
                  && limitesTransacaoConta.getValorLimiteUnitarioNoite() != null
              ? limitesTransacaoConta.getValorLimiteUnitarioNoite()
              : limiteTransacaoProduto.getValorLimiteUnitarioNoite());
      limiteTransacaoContaProdutoVO.setNomeTipoTransacao(ltp.getId());
    } else {
      return true;
    }

    List<LimiteTransacaoTipoFunctioncode> limiteTransacaoTipoFunctioncode =
        this.limiteTransacaoTipoFunctionCodeRepository.findByLimiteTransacaoTipo_Id(ltp.getId());

    return this.somarTransacoesPorPeriodo(
        limiteTransacaoTipoFunctioncode,
        validaLimitePortadorVO,
        limiteTransacaoContaProdutoVO,
        ltp);
  }

  public Boolean somarTransacoesPorPeriodo(
      List<LimiteTransacaoTipoFunctioncode> limiteTransacaoTipoFunctioncode,
      ValidaLimitePortadorVO validaLimitePortadorVO,
      LimiteTransacaoContaProdutoVO limiteTransacaoContaProdutoVO,
      LimiteTransacaoTipo ltp) {
    Boolean permitido;
    if (limiteTransacaoTipoFunctioncode != null && limiteTransacaoTipoFunctioncode.size() > 0) {
      LocalDateTime now = LocalDateTime.now();
      String periodoPorTipoTransacao = this.verificaPeriodoPorTipoTransacao(ltp);

      if (periodoPorTipoTransacao.equals("DIURNO")) {
        LocalDateTime inicio = now.with(LocalTime.of(6, 0));
        LocalDateTime fim = now.with(LocalTime.of(20, 0));

        Date inicioDate = Date.from(inicio.atZone(ZoneId.systemDefault()).toInstant());
        Date fimDate = Date.from(fim.atZone(ZoneId.systemDefault()).toInstant());

        List<TransacoesPorFcDiaVO> transacoesPorFcNoDia =
            this.contaPagamentoRepository.findTransacoesPorFcNoDia(
                validaLimitePortadorVO.getIdConta(),
                limiteTransacaoTipoFunctioncode.stream()
                    .map(
                        l ->
                            l.getLimiteTransacaoTipoFunctionCodeId()
                                .getCdFunctioncode()
                                .longValue())
                    .collect(Collectors.toList()),
                inicioDate,
                fimDate,
                validaLimitePortadorVO.getIdInstituicao(),
                validaLimitePortadorVO.getIdProdInstituicao().longValue());

        permitido =
            this.validaNomeCampoELimites(
                validaLimitePortadorVO, limiteTransacaoContaProdutoVO, transacoesPorFcNoDia);
      } else if (periodoPorTipoTransacao.equals("NOTURNO")) {
        LocalDateTime inicio;
        LocalDateTime fim;

        if (now.getHour() >= 20 && !now.isAfter(LocalDate.now().atTime(LocalTime.MAX))) {
          inicio = now.with(LocalTime.of(20, 0, 0));
          fim = now.with(LocalTime.of(23, 59, 59));
        } else {
          inicio = now.minusDays(1).with(LocalTime.of(20, 0));
          fim = now.with(LocalTime.of(6, 0));
        }

        Date inicioDate = Date.from(inicio.atZone(ZoneId.systemDefault()).toInstant());
        Date fimDate = Date.from(fim.atZone(ZoneId.systemDefault()).toInstant());

        List<TransacoesPorFcDiaVO> transacoesPorFcNoDia =
            this.contaPagamentoRepository.findTransacoesPorFcNoDia(
                validaLimitePortadorVO.getIdConta(),
                limiteTransacaoTipoFunctioncode.stream()
                    .map(
                        l ->
                            l.getLimiteTransacaoTipoFunctionCodeId()
                                .getCdFunctioncode()
                                .longValue())
                    .collect(Collectors.toList()),
                inicioDate,
                fimDate,
                validaLimitePortadorVO.getIdInstituicao(),
                validaLimitePortadorVO.getIdProdInstituicao().longValue());

        permitido =
            this.validaNomeCampoELimites(
                validaLimitePortadorVO, limiteTransacaoContaProdutoVO, transacoesPorFcNoDia);
      } else {
        Date dataHoje = new Date();

        List<TransacoesPorFcDiaVO> transacoesPorFcNoDia =
            this.contaPagamentoRepository.findTransacoesPorFcNoDia(
                validaLimitePortadorVO.getIdConta(),
                limiteTransacaoTipoFunctioncode.stream()
                    .map(
                        l ->
                            l.getLimiteTransacaoTipoFunctionCodeId()
                                .getCdFunctioncode()
                                .longValue())
                    .collect(Collectors.toList()),
                dataHoje,
                dataHoje,
                validaLimitePortadorVO.getIdInstituicao(),
                validaLimitePortadorVO.getIdProdInstituicao().longValue());

        permitido =
            this.validaNomeCampoELimites(
                validaLimitePortadorVO, limiteTransacaoContaProdutoVO, transacoesPorFcNoDia);
      }
      return permitido;
    } else {
      throw new GenericServiceException(
          "Não há o Tipo de transação "
              + ltp.getNmTipoTransacao()
              + " vinculado a nenhum FunctionCode");
    }
  }

  public Boolean validaNomeCampoELimites(
      ValidaLimitePortadorVO validaLimitePortadorVO,
      LimiteTransacaoContaProdutoVO limitesTransacao,
      List<TransacoesPorFcDiaVO> transacoesPorFcDiaVOList) {

    BigDecimal valorTotalTransacoes = BigDecimal.ZERO;

    LimiteTransacaoTipoEnum limiteTransacaoEnum = limitesTransacao.getNomeTipoTransacao();
    if (limiteTransacaoEnum == null) {
      throw new GenericServiceException("Tipo de transação indefinido");
    }
    switch (limiteTransacaoEnum) {
      case BOLETO:
        List<LogPagtoTituloValidacao> pagamentosRealizados =
            logPagtoValidacaoRepository.findByIdContaAndStatusTransacaoAndDataInicio(
                validaLimitePortadorVO.getIdConta(), "SUCESSO", new Date());
        for (LogPagtoTituloValidacao pagamentoRealizado : pagamentosRealizados) {
          valorTotalTransacoes =
              valorTotalTransacoes.add(
                  new BigDecimal(pagamentoRealizado.getValor(), MathContext.DECIMAL64));
        }
        break;
      case TED:
        List<TED> tedRealizados =
            tedRepository.findByIdContaIssuerAndStatusTedInAndDataHoraInclusao(
                validaLimitePortadorVO.getIdConta(),
                Arrays.asList(
                    StatusTransacao.AGUARDANDO_APROVACAO.getCodigo(),
                    StatusTransacao.TRANSFERIDO.getCodigo(),
                    StatusTransacao.AGUARDANDO_APROVACAO_DE_FAVORECIDO.getCodigo(),
                    StatusTransacao.AGUARDANDO_CHECAGEM.getCodigo()),
                new Date());
        for (TED tedRealizado : tedRealizados) {
          valorTotalTransacoes = valorTotalTransacoes.add(tedRealizado.getValorTransferencia());
        }
        break;
      case PIX:
        break;
      default:
        throw new GenericServiceException("Tipo de transação inválido.");
    }

    if (limitesTransacao.getNomeTipoTransacao().equals(LimiteTransacaoTipoEnum.BOLETO)
        || limitesTransacao.getNomeTipoTransacao().equals(LimiteTransacaoTipoEnum.TED)
        || limitesTransacao.getNomeTipoTransacao().equals(LimiteTransacaoTipoEnum.PIX)) {

      if (limitesTransacao.getValorLimiteSomaDia() != null) {
        if (valorTotalTransacoes.doubleValue()
            > limitesTransacao.getValorLimiteSomaDia().doubleValue()) {
          throw new GenericServiceException("Valor total de transações por dia excedida.");
        } else if (valorTotalTransacoes
                .add(validaLimitePortadorVO.getValorTransferencia())
                .doubleValue()
            > limitesTransacao.getValorLimiteSomaDia().doubleValue()) {
          throw new GenericServiceException("Valor da transação excede o limite diurno.");
        } else if (validaLimitePortadorVO.getValorTransferencia().longValue()
            > limitesTransacao.getValorLimiteSomaDia().longValue()) {
          throw new GenericServiceException("Valor da transação excede o limite diurno.");
        }
      } else {
        return true;
      }

      if (limitesTransacao.getValorLimiteUnitarioDia() != null) {
        if (validaLimitePortadorVO.getValorTransferencia().longValue()
            > limitesTransacao.getValorLimiteUnitarioDia().longValue()) {
          throw new GenericServiceException(
              "Valor da transação excede o limite para transação única diária.");
        }
      } else {
        return true;
      }
    }

    if (limitesTransacao.getNomeTipoTransacao().equals(LimiteTransacaoTipoEnum.PIX)) {
      if (limitesTransacao.getValorLimiteSomaNoite() != null) {
        if (valorTotalTransacoes.doubleValue()
            > limitesTransacao.getValorLimiteSomaNoite().doubleValue()) {
          throw new GenericServiceException("Valor total de transações por dia excedida.");
        } else if (valorTotalTransacoes
                .add(validaLimitePortadorVO.getValorTransferencia())
                .doubleValue()
            > limitesTransacao.getValorLimiteSomaNoite().doubleValue()) {
          throw new GenericServiceException("Valor da transação excede o limite noturno.");
        } else if (validaLimitePortadorVO.getValorTransferencia().longValue()
            > limitesTransacao.getValorLimiteSomaNoite().longValue()) {
          throw new GenericServiceException("Valor da transação excede o limite noturno.");
        }
      } else {
        return true;
      }

      if (limitesTransacao.getValorLimiteUnitarioNoite() != null) {
        if (validaLimitePortadorVO.getValorTransferencia().longValue()
            > limitesTransacao.getValorLimiteUnitarioNoite().longValue()) {
          throw new GenericServiceException(
              "Valor da transação excede o limite para transação unica noturna.");
        }
      } else {
        return true;
      }
    }

    //        LIBERAR QUANDO PODER AVALIAR A QUANTIDADE DE TRANSAÇÕES
    //        if(limitesTransacao.getQtLimiteSomaDia() != null){
    //            if(validaLimitePortadorVO.getQtLimiteSomaDia() >
    // limitesTransacao.getQtLimiteSomaDia()) {
    //                throw new GenericServiceException("Quantidade de transações por dia
    // excedida.");
    //            }
    //        } else if(limitesTransacao.getQtLimiteSomaNoite() != null){
    //            if(validaLimitePortadorVO.getQtLimiteSomaNoite() >
    // limitesTransacaoConta.getQtLimiteSomaNoite()) {
    //                throw new GenericServiceException("Quantidade de transações por noite
    // excedida.");
    //            }
    //        }
    return true;
  }

  public LimiteTransacaoSolicitacaoContaVO validarBuscaLimiteContaOrProduto(
      ConsultaLimiteAtualDTO consultaLimiteAtualDTO, SecurityUserPortador userPortador) {
    LimiteTransacaoTipoEnum limiteTransacaoTipoEnum =
        LimiteTransacaoTipoEnum.values()[consultaLimiteAtualDTO.getIdTipoTransacao().intValue()];
    contaPagamentoService.validaIdContaPeloRequestEPortador(
        consultaLimiteAtualDTO.getIdConta(), userPortador);

    return this.buscaLimiteContaOrProduto(
        consultaLimiteAtualDTO.getIdConta(),
        limiteTransacaoTipoEnum,
        consultaLimiteAtualDTO.getIdProdInstituicao());
  }

  public LimiteTransacaoSolicitacaoContaVO validarBuscaLimiteContaOrProduto(
      ConsultaLimiteAtualDTO consultaLimiteAtualDTO, SecurityUserCorporativo userCorporativo) {
    LimiteTransacaoTipoEnum limiteTransacaoTipoEnum =
        LimiteTransacaoTipoEnum.values()[consultaLimiteAtualDTO.getIdTipoTransacao().intValue()];
    contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
        consultaLimiteAtualDTO.getIdConta(), userCorporativo);

    return this.buscaLimiteContaOrProduto(
        consultaLimiteAtualDTO.getIdConta(),
        limiteTransacaoTipoEnum,
        consultaLimiteAtualDTO.getIdProdInstituicao());
  }

  private LimiteTransacaoSolicitacaoContaVO buscaLimiteContaOrProduto(
      Long idConta, LimiteTransacaoTipoEnum idTipoTransacao, Integer idProdInstituicao) {
    LimiteTransacaoConta limiteTransacaoConta = consultarLimiteAtual(idConta, idTipoTransacao);
    LimiteTransacaoProduto limiteTransacaoProduto =
        consultarLimiteAtualProduto(idProdInstituicao, idTipoTransacao);

    LimiteTransacaoSolicitacaoContaVO limiteTransacaoSolicitacaoContaVO =
        new LimiteTransacaoSolicitacaoContaVO();

    if (limiteTransacaoConta != null || limiteTransacaoProduto != null) {

      List<LimiteTransacaoSolicitacao> limiteTransacaoSolicitacao =
          getSituacaoSolicitacaoByStatusList(idConta, idTipoTransacao, "PENDENTE");

      limiteTransacaoSolicitacaoContaVO.setIdConta(
          limiteTransacaoConta != null
              ? limiteTransacaoConta.getContaPagamento().getIdConta()
              : idConta);
      limiteTransacaoSolicitacaoContaVO.setLimiteTransacaoTipo(
          limiteTransacaoConta != null
              ? limiteTransacaoConta.getLimiteTransacaoTipo().getId()
              : limiteTransacaoProduto.getLimiteTransacaoTipo().getId());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteUnitarioDia(
          limiteTransacaoConta != null && limiteTransacaoConta.getValorLimiteUnitarioDia() != null
              ? limiteTransacaoConta.getValorLimiteUnitarioDia()
              : limiteTransacaoProduto.getValorLimiteUnitarioDia());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteUnitarioNoite(
          limiteTransacaoConta != null && limiteTransacaoConta.getValorLimiteUnitarioNoite() != null
              ? limiteTransacaoConta.getValorLimiteUnitarioNoite()
              : limiteTransacaoProduto.getValorLimiteUnitarioNoite());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteSomaDia(
          limiteTransacaoConta != null && limiteTransacaoConta.getValorLimiteSomaDia() != null
              ? limiteTransacaoConta.getValorLimiteSomaDia()
              : limiteTransacaoProduto.getValorLimiteSomaDia());
      limiteTransacaoSolicitacaoContaVO.setValorLimiteSomaNoite(
          limiteTransacaoConta != null && limiteTransacaoConta.getValorLimiteSomaNoite() != null
              ? limiteTransacaoConta.getValorLimiteSomaNoite()
              : limiteTransacaoProduto.getValorLimiteSomaNoite());
      limiteTransacaoSolicitacaoContaVO.setQtLimiteSomaDia(
          limiteTransacaoConta != null && limiteTransacaoConta.getQtLimiteSomaDia() != null
              ? limiteTransacaoConta.getQtLimiteSomaDia()
              : limiteTransacaoProduto.getQtLimiteSomaDia());
      limiteTransacaoSolicitacaoContaVO.setQtLimiteSomaNoite(
          limiteTransacaoConta != null && limiteTransacaoConta.getQtLimiteSomaNoite() != null
              ? limiteTransacaoConta.getQtLimiteSomaNoite()
              : limiteTransacaoProduto.getQtLimiteSomaNoite());
      limiteTransacaoSolicitacaoContaVO.setSituacaoStatus(
          limiteTransacaoConta != null && limiteTransacaoConta.getSituacaoStatus() != null
              ? limiteTransacaoConta.getSituacaoStatus()
              : limiteTransacaoProduto.getStatus());
      limiteTransacaoSolicitacaoContaVO.setDtHrStatus(
          limiteTransacaoConta != null && limiteTransacaoConta.getDtHrStatus() != null
              ? limiteTransacaoConta.getDtHrStatus()
              : limiteTransacaoProduto.getDtHrStatus());
      limiteTransacaoSolicitacaoContaVO.setDtHrInclusao(
          limiteTransacaoConta != null
              ? limiteTransacaoConta.getDtHrInclusao()
              : limiteTransacaoProduto.getDtHrInclusao());

      for (int i = 0; i < limiteTransacaoSolicitacao.size(); i++) {
        if (limiteTransacaoSolicitacao
                .get(i)
                .getNomeCampoLimite()
                .equals(NomeCampoLimiteEnum.VALOR_LIMITE_UNITARIO_DIA)
            && limiteTransacaoSolicitacao
                .get(i)
                .getSituacaoStatusSolicitacao()
                .equals("PENDENTE")) {
          limiteTransacaoSolicitacaoContaVO.setStatusLimiteUnitarioDia(true);
        } else if (limiteTransacaoSolicitacao
                .get(i)
                .getNomeCampoLimite()
                .equals(NomeCampoLimiteEnum.VALOR_LIMITE_SOMA_DIA)
            && limiteTransacaoSolicitacao
                .get(i)
                .getSituacaoStatusSolicitacao()
                .equals("PENDENTE")) {
          limiteTransacaoSolicitacaoContaVO.setStatusLimiteSomaDia(true);
        } else {
          limiteTransacaoSolicitacaoContaVO.setStatusLimiteUnitarioDia(false);
          limiteTransacaoSolicitacaoContaVO.setStatusLimiteSomaDia(false);
        }
      }
    } else {
      limiteTransacaoSolicitacaoContaVO = null;
    }

    return limiteTransacaoSolicitacaoContaVO;
  }

  public List<LimiteTransacaoSolicitacaoVo> buscarLimitesConta(
      Long idConta,
      Integer idProdInstituicao,
      LimiteTransacaoTipoEnum idTipoTransacao,
      SecurityUser user) {
    consultaRestritaService.checaPrivilegio(idConta, user);
    return solicitarLimiteContaRepository
        .findByIdContaAndIdProdInstituicaoAndLimiteTransacaoTipo_id(
            idConta, idProdInstituicao, idTipoTransacao);
  }

  public LimiteTransacaoProduto buscarLimiteProduto(
      Integer idProdInstituicao, LimiteTransacaoTipoEnum idTipoTransacao) {
    LimiteTransacaoTipo ltp =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(idTipoTransacao);
    return limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
        idProdInstituicao, ltp, STATUS_ATIVO);
  }

  public String verificaPeriodoPorTipoTransacao(LimiteTransacaoTipo limiteTransacaoTipo) {
    LocalDateTime now = LocalDateTime.now();
    String periodo = "";

    if (limiteTransacaoTipo.getNmTipoTransacao().equals("PIX")) {
      if (now.getHour() >= 6 && now.getHour() <= 20) {
        periodo = "DIURNO";
      } else if (now.getHour() > 20) {
        periodo = "NOTURNO";
      }
    }

    return periodo;
  }

  /*
   *
   * Início validação limites.
   *
   * */

  public void validaLimitesConta(
      ContaPagamento conta,
      BigDecimal valorTransacao,
      BigDecimal valorSomaDiaria,
      Integer quantidadeDiaria,
      LimiteTransacaoTipoEnum tipoTransacao) {
    LimiteTransacaoTipo ltp =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(tipoTransacao);

    if (ltp == null) {
      log.info("Não há o Tipo de transação [{}] cadastrado.", tipoTransacao);
      return;
    }

    LimiteTransacaoConta limiteTransacaoConta =
        this.limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
            conta.getIdConta(), STATUS_ATIVO, ltp.getId());
    LimiteTransacaoProduto limiteTransacaoProduto =
        this.limiteTransacaoProdutoRepository
            .findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
                conta.getIdProdutoInstituicao(), ltp, 1);

    if (limiteTransacaoConta == null && limiteTransacaoProduto == null) {
      log.info(
          "Não há o Tipo de transação "
              + tipoTransacao
              + " cadastrado para o produto "
              + conta.getIdProdutoInstituicao()
              + " e conta "
              + conta.getIdConta());
      return;
    }

    LimiteTransacaoConta limiteTransacaoContaFinal = new LimiteTransacaoConta();

    BiFunction<Supplier<BigDecimal>, Supplier<BigDecimal>, BigDecimal> escolherValor =
        (valorConta, valorProduto) ->
            valorConta.get() != null ? valorConta.get() : valorProduto.get();

    BiFunction<Supplier<Integer>, Supplier<Integer>, Integer> escolherQuantidade =
        (quantidadeConta, quantidadeProduto) ->
            quantidadeConta.get() != null ? quantidadeConta.get() : quantidadeProduto.get();

    limiteTransacaoContaFinal.setValorLimiteSomaDia(
        escolherValor.apply(
            () ->
                limiteTransacaoConta != null ? limiteTransacaoConta.getValorLimiteSomaDia() : null,
            () ->
                limiteTransacaoProduto != null
                    ? limiteTransacaoProduto.getValorLimiteSomaDia()
                    : null));
    limiteTransacaoContaFinal.setQtLimiteSomaDia(
        escolherQuantidade.apply(
            () -> limiteTransacaoConta != null ? limiteTransacaoConta.getQtLimiteSomaDia() : null,
            () ->
                limiteTransacaoProduto != null
                    ? limiteTransacaoProduto.getQtLimiteSomaDia()
                    : null));
    limiteTransacaoContaFinal.setValorLimiteUnitarioDia(
        escolherValor.apply(
            () ->
                limiteTransacaoConta != null
                    ? limiteTransacaoConta.getValorLimiteUnitarioDia()
                    : null,
            () ->
                limiteTransacaoProduto != null
                    ? limiteTransacaoProduto.getValorLimiteUnitarioDia()
                    : null));
    limiteTransacaoContaFinal.setValorLimiteSomaNoite(
        escolherValor.apply(
            () ->
                limiteTransacaoConta != null
                    ? limiteTransacaoConta.getValorLimiteSomaNoite()
                    : null,
            () ->
                limiteTransacaoProduto != null
                    ? limiteTransacaoProduto.getValorLimiteSomaNoite()
                    : null));
    limiteTransacaoContaFinal.setQtLimiteSomaNoite(
        escolherQuantidade.apply(
            () -> limiteTransacaoConta != null ? limiteTransacaoConta.getQtLimiteSomaNoite() : null,
            () ->
                limiteTransacaoProduto != null
                    ? limiteTransacaoProduto.getQtLimiteSomaNoite()
                    : null));
    limiteTransacaoContaFinal.setValorLimiteUnitarioNoite(
        escolherValor.apply(
            () ->
                limiteTransacaoConta != null
                    ? limiteTransacaoConta.getValorLimiteUnitarioNoite()
                    : null,
            () ->
                limiteTransacaoProduto != null
                    ? limiteTransacaoProduto.getValorLimiteUnitarioNoite()
                    : null));
    limiteTransacaoContaFinal.setQtdMaxPagamentoDia(
        limiteTransacaoConta != null ? limiteTransacaoConta.getQtdMaxPagamentoDia() : null);

    Integer limiteQuantidadeDiaria =
        obterLimiteQuantidadeDiaria(limiteTransacaoContaFinal, conta, tipoTransacao);

    if (limiteQuantidadeDiaria != null) {
      validarLimiteDiarioDeQuantidadeDePagamentos(
          quantidadeDiaria, limiteQuantidadeDiaria, tipoTransacao);
      validarLimitesDeTransacao(
          valorTransacao, limiteTransacaoContaFinal, valorSomaDiaria, tipoTransacao);
    }
  }

  private Integer obterLimiteQuantidadeDiaria(
      LimiteTransacaoConta limiteTransacaoConta,
      ContaPagamento conta,
      LimiteTransacaoTipoEnum tipoTransacao) {
    if (limiteTransacaoConta != null && limiteTransacaoConta.getQtdMaxPagamentoDia() != null) {
      return limiteTransacaoConta.getQtdMaxPagamentoDia();
    }
    Long limiteDiarioParametroValor = null;
    if (LimiteTransacaoTipoEnum.BOLETO.equals(tipoTransacao)) {
      limiteDiarioParametroValor =
          parametroValorService.findByParametroDefinicaoAndIdInstituicao(
              utilService.getParametroDefinicaoLimiteBoletoDiario(), conta.getIdInstituicao());
      if (limiteDiarioParametroValor == null) {
        limiteDiarioParametroValor =
            parametroValorService.findValorParametroLong(
                Constantes.PARAMETRO_VALOR_LIMITE_BOLETO_DIARIO);
      }
    }
    return limiteDiarioParametroValor == null ? -1 : limiteDiarioParametroValor.intValue();
  }

  private void validarLimiteDiarioDeQuantidadeDePagamentos(
      Integer quantidadeUsadaNoDia,
      Integer limiteQuantidadeDiaria,
      LimiteTransacaoTipoEnum tipoTransacao) {
    if (quantidadeUsadaNoDia != null
        && quantidadeUsadaNoDia > 0
        && limiteQuantidadeDiaria > -1
        && quantidadeUsadaNoDia >= limiteQuantidadeDiaria) {
      throw new GenericServiceException(
          "Não é possível efetuar mais pagamentos de " + tipoTransacao.name() + " hoje.");
    }
  }

  private void validarLimitesDeTransacao(
      BigDecimal valor,
      LimiteTransacaoConta limiteTransacaoConta,
      BigDecimal somaDiaria,
      LimiteTransacaoTipoEnum tipoTransacao) {
    LocalDateTime now = LocalDateTime.now();
    boolean isPeriodoDiurno = now.getHour() >= 6 && now.getHour() < 20;
    if (isPeriodoDiurno) {
      validarLimiteSomaDiurna(valor, somaDiaria, limiteTransacaoConta);
      validarLimiteUnitarioDiurno(valor, limiteTransacaoConta, tipoTransacao);
    } else {
      validarLimiteSomaNoturna(valor, somaDiaria, limiteTransacaoConta);
      validarLimiteUnitarioNoturno(valor, limiteTransacaoConta, tipoTransacao);
    }
  }

  private void validarLimiteUnitarioDiurno(
      BigDecimal valor,
      LimiteTransacaoConta limiteTransacaoConta,
      LimiteTransacaoTipoEnum tipoTransacao) {
    if (limiteTransacaoConta.getValorLimiteUnitarioDia() != null
        && valor.compareTo(limiteTransacaoConta.getValorLimiteUnitarioDia()) > 0) {
      throw new GenericServiceException(
          "O valor a ser pago ultrapassa o seu Limite de "
              + tipoTransacao.name()
              + " R$ "
              + limiteTransacaoConta.getValorLimiteUnitarioDia()
              + ". Para realizar maiores, solicite a alteração do seu limite");
    }
  }

  private void validarLimiteSomaDiurna(
      BigDecimal valor, BigDecimal somaDiaria, LimiteTransacaoConta limiteTransacaoConta) {
    if (limiteTransacaoConta.getValorLimiteSomaDia() != null
        && (somaDiaria.add(valor)).compareTo(limiteTransacaoConta.getValorLimiteSomaDia()) > 0) {
      BigDecimal valorDisponivel =
          (limiteTransacaoConta.getValorLimiteSomaDia().subtract(somaDiaria));
      throw new GenericServiceException(
          "Valor ultrapassaria o limite total diário. O máximo disponível para hoje é "
              + valorDisponivel);
    }
  }

  private void validarLimiteUnitarioNoturno(
      BigDecimal valor,
      LimiteTransacaoConta limiteTransacaoConta,
      LimiteTransacaoTipoEnum tipoTransacao) {
    if (limiteTransacaoConta.getValorLimiteUnitarioNoite() != null
        && valor.compareTo(limiteTransacaoConta.getValorLimiteUnitarioNoite()) > 0) {
      throw new GenericServiceException(
          "O valor a ser pago ultrapassa o seu Limite de "
              + tipoTransacao.name()
              + " R$ "
              + limiteTransacaoConta.getValorLimiteUnitarioNoite()
              + ". Para realizar maiores, solicite a alteração do seu limite");
    }
  }

  private void validarLimiteSomaNoturna(
      BigDecimal valor, BigDecimal somaDiaria, LimiteTransacaoConta limiteTransacaoConta) {
    if (limiteTransacaoConta.getValorLimiteSomaNoite() != null
        && (somaDiaria.add(valor)).compareTo(limiteTransacaoConta.getValorLimiteSomaNoite()) > 0) {
      BigDecimal valorDisponivel =
          (limiteTransacaoConta.getValorLimiteSomaNoite().subtract(somaDiaria));
      throw new GenericServiceException(
          "Valor ultrapassaria o limite total noturno. O máximo disponível para hoje é "
              + valorDisponivel);
    }
  }

  public void validarLimiteContaDiario(BigDecimal valorTransacao, ContaPagamento conta) {

    LocalDate hoje = LocalDate.now();

    LimiteTransacaoConta limiteConta =
        limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
            conta.getIdConta(), STATUS_ATIVO, LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA);

    if (limiteConta == null) {
      limiteConta = criarNovoLimiteContaBaseadoNoProduto(conta);
    }

    if (limiteConta == null) {
      return;
    }

    validarDiaAtualLimiteConta(limiteConta, hoje);
    BigDecimal limiteDisponivel = calcularLimiteDisponivel(limiteConta);
    validarLimiteDiarioDisponivelParaValorTransacao(valorTransacao, limiteDisponivel);
  }

  private LimiteTransacaoConta criarNovoLimiteContaBaseadoNoProduto(ContaPagamento conta) {
    LimiteTransacaoTipo limiteTransacaoTipo =
        limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
            LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA);

    LimiteTransacaoProduto limiteProduto =
        limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
            conta.getIdProdutoInstituicao(), limiteTransacaoTipo, STATUS_ATIVO);

    if (limiteProduto == null) {
      return null;
    }

    LimiteTransacaoConta novoLimite = new LimiteTransacaoConta();
    novoLimite.setDtHrTransacaoDia(LocalDateTime.now());
    novoLimite.setLimiteTransacaoTipo(limiteProduto.getLimiteTransacaoTipo());
    novoLimite.setValorLimiteGlobal(limiteProduto.getValorLimiteGlobal());
    novoLimite.setValorLimiteSomaDia(BigDecimal.ZERO);
    novoLimite.setIdConta(conta.getIdConta());
    novoLimite.setSituacaoStatus(STATUS_ATIVO);
    novoLimite.setDtHrInclusao(LocalDateTime.now());
    novoLimite.setIdUsuarioInclusao(limiteProduto.getIdUsuarioInclusao());

    return limiteTransacaoContaRepository.save(novoLimite);
  }

  private BigDecimal calcularLimiteDisponivel(LimiteTransacaoConta limiteConta) {
    return limiteConta.getValorLimiteGlobal().subtract(limiteConta.getValorLimiteSomaDia());
  }

  public void salvarLimiteDiario(BigDecimal valorOperacao, Long idConta) {
    LimiteTransacaoConta limiteConta =
        limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
            idConta, STATUS_ATIVO, LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA);

    if (limiteConta == null) {
      return;
    }
    limiteConta.setValorLimiteSomaDia(
        limiteConta.getValorLimiteSomaDia() == null
            ? BigDecimal.ZERO
            : limiteConta.getValorLimiteSomaDia().add(valorOperacao));
    limiteConta.setDtHrTransacaoDia(LocalDateTime.now());
    limiteTransacaoContaRepository.save(limiteConta);
  }

  private void validarDiaAtualLimiteConta(LimiteTransacaoConta limiteConta, LocalDate hoje) {
    if (limiteConta.getDtHrTransacaoDia() == null
        || !limiteConta.getDtHrTransacaoDia().toLocalDate().equals(hoje)) {
      limiteConta.setValorLimiteSomaDia(BigDecimal.ZERO);
      limiteConta.setDtHrTransacaoDia(LocalDateTime.now());
    }
  }

  private static void validarLimiteDiarioDisponivelParaValorTransacao(
      BigDecimal valorTransacao, BigDecimal limiteDisponivel) {
    if (valorTransacao.compareTo(limiteDisponivel) > 0) {
      throw new GenericServiceException(
          "Essa Transação excede o limite diário de transações da conta. "
              + "Limite restante de hoje: R$ "
              + limiteDisponivel);
    }
  }

  public LimiteTransacaoConta buscarLimiteDiarioConta(BuscaLimiteVO buscaLimiteVO) {
    return limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
        buscaLimiteVO.getIdConta(), STATUS_ATIVO, buscaLimiteVO.getIdTipoTransacao());
  }

  public void cadastrarLimiteDiario(
      LimiteDiarioVO limiteDiarioVO, SecurityUser user, BuscaLimiteVO buscaLimiteVO) {

    LimiteTransacaoTipo limiteTransacaoTipo =
        limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
            buscaLimiteVO.getIdTipoTransacao());

    if (limiteDiarioVO.getMotivoAlteracao() == null) {
      throw new GenericServiceException(ConstantesErro.MOTIVO_ALTERACAO.getMensagem());
    }

    if (Objects.equals(limiteDiarioVO.getValorLimiteGlobal(), BigDecimal.ZERO)) {
      throw new GenericServiceException(ConstantesErro.VALOR_LIMITE_DIARIO.getMensagem());
    }

    if (limiteDiarioVO.getConta()) {
      LimiteTransacaoConta limiteTransacaoConta =
          limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
              limiteDiarioVO.getIdConta(), STATUS_ATIVO, buscaLimiteVO.getIdTipoTransacao());

      if (limiteTransacaoConta != null) {
        // Inativa o limite atual
        limiteTransacaoConta.setSituacaoStatus(STATUS_INATIVO);
        limiteTransacaoConta.setDtHrAlteracao(LocalDateTime.now());
        limiteTransacaoConta.setIdUsuarioManutencao(user.getIdUsuario());
        limiteTransacaoConta.setMotivoAlteracao(limiteDiarioVO.getMotivoAlteracao());
        limiteTransacaoConta.setValorLimiteGlobal(limiteDiarioVO.getValorLimiteGlobal());
        limiteTransacaoContaRepository.save(limiteTransacaoConta);
      }
      // Cria um novo limite
      LimiteTransacaoConta novoLimiteTransacaoConta = new LimiteTransacaoConta();
      novoLimiteTransacaoConta.setIdConta(limiteDiarioVO.getIdConta());
      novoLimiteTransacaoConta.setSituacaoStatus(STATUS_ATIVO);
      novoLimiteTransacaoConta.setDtHrInclusao(LocalDateTime.now());
      novoLimiteTransacaoConta.setIdUsuarioInclusao(user.getIdUsuario());
      novoLimiteTransacaoConta.setMotivoAlteracao(limiteDiarioVO.getMotivoAlteracao());
      novoLimiteTransacaoConta.setValorLimiteGlobal(limiteDiarioVO.getValorLimiteGlobal());
      novoLimiteTransacaoConta.setLimiteTransacaoTipo(limiteTransacaoTipo);

      novoLimiteTransacaoConta.setValorLimiteSomaDia(
          Optional.ofNullable(limiteTransacaoConta)
              .map(LimiteTransacaoConta::getValorLimiteSomaDia)
              .orElse(null));
      novoLimiteTransacaoConta.setDtHrTransacaoDia(
          Optional.ofNullable(limiteTransacaoConta)
              .map(LimiteTransacaoConta::getDtHrTransacaoDia)
              .orElse(null));

      limiteTransacaoContaRepository.save(novoLimiteTransacaoConta);
    } else {
      LimiteTransacaoProduto limiteTransacaoProduto =
          limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
              limiteDiarioVO.getIdProdInstituicao(), limiteTransacaoTipo, STATUS_ATIVO);

      if (limiteTransacaoProduto != null) {
        // Atualiza o limite existente
        limiteTransacaoProduto.setDtHrAlteracao(LocalDateTime.now());
        limiteTransacaoProduto.setIdUsuarioManutencao(user.getIdUsuario());
        limiteTransacaoProduto.setStatus(STATUS_INATIVO);
        limiteTransacaoProduto.setMotivoAlteracao(limiteDiarioVO.getMotivoAlteracao());
        limiteTransacaoProduto.setValorLimiteGlobal(limiteDiarioVO.getValorLimiteGlobal());
        limiteTransacaoProdutoRepository.save(limiteTransacaoProduto);
      }

      LimiteTransacaoProduto novoLimiteTransacaoProduto = new LimiteTransacaoProduto();
      novoLimiteTransacaoProduto.setIdProdInstituicao(limiteDiarioVO.getIdProdInstituicao());
      novoLimiteTransacaoProduto.setLimiteTransacaoTipo(limiteTransacaoTipo);
      novoLimiteTransacaoProduto.setStatus(STATUS_ATIVO);
      novoLimiteTransacaoProduto.setDtHrStatus(LocalDateTime.now());
      novoLimiteTransacaoProduto.setDtHrInclusao(LocalDateTime.now());
      novoLimiteTransacaoProduto.setValorLimiteGlobal(limiteDiarioVO.getValorLimiteGlobal());
      novoLimiteTransacaoProduto.setIdUsuarioInclusao(user.getIdUsuario());
      novoLimiteTransacaoProduto.setMotivoAlteracao(limiteDiarioVO.getMotivoAlteracao());
      limiteTransacaoProdutoRepository.save(novoLimiteTransacaoProduto);
    }
  }

  public List<LimiteTransacaoContaVO> listarLimitesDiarioConta(
      Long idConta, Integer idTipoTransacao, SecurityUser user) {
    List<LimiteTransacaoConta> listaLimiteTransacaoConta =
        limiteTransacaoContaRepository.listaLimiteTransacaoConta(idConta, idTipoTransacao);

    return listaLimiteTransacaoConta.stream()
        .map(
            limite -> {
              LimiteTransacaoContaVO vo = new LimiteTransacaoContaVO();
              String nomeUsuario = buscarNomeUsuarioPorId(limite.getIdUsuarioInclusao());
              vo.setNomeUsuario(nomeUsuario);
              vo.setSituacaoStatus(limite.getSituacaoStatus());
              vo.setDtHrInclusao(limite.getDtHrInclusao());
              vo.setDtHrAlteracao(limite.getDtHrAlteracao());
              vo.setDtHrStatus(limite.getDtHrStatus());
              vo.setId(limite.getId());
              vo.setIdConta(limite.getIdConta());
              vo.setIdUsuarioInclusao(limite.getIdUsuarioInclusao());
              vo.setIdUsuarioManutencao(limite.getIdUsuarioManutencao());
              vo.setValorLimiteGlobal(limite.getValorLimiteGlobal());
              return vo;
            })
        .collect(Collectors.toList());
  }

  public List<LimiteTransacaoProdutoVO> listarLimitesDiarioProduto(
      Integer idProdInstituicao, Integer idTipoTransacao, SecurityUser user) {
    List<LimiteTransacaoProduto> listaLimiteTransacaoProduto =
        limiteTransacaoProdutoRepository.listaLimiteTransacaoProduto(
            idProdInstituicao, idTipoTransacao);

    return listaLimiteTransacaoProduto.stream()
        .map(
            limite -> {
              LimiteTransacaoProdutoVO vo = new LimiteTransacaoProdutoVO();
              String nomeUsuario = buscarNomeUsuarioPorId(limite.getIdUsuarioInclusao());
              vo.setValorLimiteGlobal(limite.getValorLimiteGlobal());
              vo.setDtHrAlteracao(limite.getDtHrAlteracao());
              vo.setDtHrInclusao(limite.getDtHrInclusao());
              vo.setStatus(limite.getStatus());
              vo.setIdLimite(limite.getId());
              vo.setIdUsuarioInclusao(limite.getIdUsuarioInclusao());
              vo.setNomeUsuario(nomeUsuario);
              return vo;
            })
        .collect(Collectors.toList());
  }

  private String buscarNomeUsuarioPorId(Integer idUsuarioInclusao) {
    return acessoUsuarioService.findNomeById(idUsuarioInclusao);
  }

  public LimiteTransacaoProduto buscarLimiteDiarioProduto(
      BuscaLimiteVO buscaLimiteVO, Integer idProdInstituicao) {
    LimiteTransacaoTipo ltp =
        this.limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
            buscaLimiteVO.getIdTipoTransacao());
    return limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
        idProdInstituicao, ltp, STATUS_ATIVO);
  }

  public void cancelarLimiteDiario(
      LimiteDiarioVO limiteDiarioVO, SecurityUser user, BuscaLimiteVO buscaLimiteVO) {
    if (limiteDiarioVO.getConta()) {
      Optional<LimiteTransacaoConta> limiteTransacaoConta =
          limiteTransacaoContaRepository.findById(Long.valueOf(limiteDiarioVO.getIdLimite()));
      if (limiteTransacaoConta.isEmpty()) {
        throw new GenericServiceException("Limite da conta não encontrada");
      } else {
        limiteTransacaoConta.get().setSituacaoStatus(STATUS_INATIVO);
        limiteTransacaoConta.get().setIdUsuarioManutencao(user.getIdUsuario());
        limiteTransacaoConta.get().setDtHrAlteracao(LocalDateTime.now());
        limiteTransacaoContaRepository.save(limiteTransacaoConta.get());
      }
    } else {
      Optional<LimiteTransacaoProduto> limiteTransacaoProduto =
          limiteTransacaoProdutoRepository.findById(limiteDiarioVO.getIdLimite());
      if (limiteTransacaoProduto.isEmpty()) {
        throw new GenericServiceException("Limite do produto não encontrado");
      } else {
        limiteTransacaoProduto.get().setStatus(STATUS_INATIVO);
        limiteTransacaoProduto.get().setIdUsuarioManutencao(user.getIdUsuario());
        limiteTransacaoProduto.get().setDtHrAlteracao(LocalDateTime.now());
        limiteTransacaoProdutoRepository.save(limiteTransacaoProduto.get());
      }
    }
  }
}
