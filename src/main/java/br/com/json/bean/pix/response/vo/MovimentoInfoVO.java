package br.com.json.bean.pix.response.vo;

import br.com.json.bean.pix.request.vo.ContaVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class MovimentoInfoVO {
  private String origem;
  private String endToEnd;
  private String endToEndOriginal;
  private String referenciaInterna;

  @Getter(AccessLevel.NONE)
  @Setter(AccessLevel.NONE)
  private LocalDateTime data;

  private BigDecimal valor;
  private String numeroEbank;
  private String natureza;
  private String descricao;
  private String campoLivre;
  private Boolean devolvido;
  private OrigemMovimento origemMovimento;
  private InstrucoesDevolucao instrucoesDevolucao;
  private ContaVO conta;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'",
      timezone = "America/Sao_Paulo")
  public LocalDateTime getData() {
    return data;
  }

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS'Z'][.SS'Z'][.S'Z']['Z'][]",
      timezone = "America/Sao_Paulo")
  public void setData(LocalDateTime data) {
    this.data = data;
  }
}
