package br.com.sinergico.controller.transacional;

import br.com.json.bean.transacional.CadastroCargaBRBApi;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.CargaBRBApiService;
import br.com.sinergico.vo.CargaBRBApiResponse;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/carga-brb")
public class CargaBRBApiController extends UtilController {

  @Autowired private CargaBRBApiService service;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private TravaContasService travaContasService;

  @ApiOperation(value = "Realiza carga de um valor - Uso exclusivo do BRB")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/lancar",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<CargaBRBApiResponse> cargaBRBApi(
      @RequestBody @Valid CadastroCargaBRBApi model, BindingResult result) {

    if (result.hasErrors()) {
      throw new br.com.exceptions.InvalidRequestException("Erro de validacao", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.LANCAMENTO);
    travaContasService.travaContas(model.getIdConta(), Servicos.LANCAMENTO);

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    return service.cargaBRBApi(model, user, ipOrigem);
  }

  @ApiOperation(value = "Realiza Estorno em uma Transação - Uso exclusivo do BRB")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/estornar/{transactionUid}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<CargaBRBApiResponse> estornaCargaBRBApi(
      @PathVariable @Valid Long transactionUid) {

    SecurityUser user = getAuthenticatedUser(request, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.LANCAMENTO);

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    return service.estornaCargaBRBApi(transactionUid, user, ipOrigem);
  }
}
