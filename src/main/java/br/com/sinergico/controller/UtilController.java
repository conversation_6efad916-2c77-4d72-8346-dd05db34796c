package br.com.sinergico.controller;

import br.com.entity.adq.Estabelecimento;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.CorporativoLogin;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.SetorFilial;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioEstabelecimento;
import br.com.entity.suporte.HierarquiaProcessadora;
import br.com.entity.suporte.SwaggerAcesso;
import br.com.exceptions.GenericRuntimeException;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.enums.RangeHierarquia;
import br.com.sinergico.repository.suporte.AcessoServicoRepository;
import br.com.sinergico.security.LegacyPasswordEncoder;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.security.SecurityUserSwagger;
import br.com.sinergico.security.TokenHandler;
import br.com.sinergico.security.TokenHandlerCorporativo;
import br.com.sinergico.security.TokenHandlerEstabelecimento;
import br.com.sinergico.security.TokenHandlerPortador;
import br.com.sinergico.security.TokenHandlerSwagger;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.SpringContextUtils;
import java.beans.PropertyDescriptor;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.BiFunction;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.util.DigestUtils;

public class UtilController {

  private static final String SHA_256 = "SHA-256";
  private static final String SHA_512 = "SHA-512";
  public static final int OBJETOS_CONTA_PAGAMENTO = 1;
  public static final int OBJETOS_ACESSO_USUARIO = 2;
  public static final int OBJETO_INVALIDO_OU_DISTINTOS_ENTRE_SI = -1;
  public static final int OBJETOS_CONTA_E_USUARIO = 3;
  public static final int OBJETOS_USUARIO_E_CONTA = 4;

  protected Logger log = LoggerFactory.getLogger(this.getClass());

  public static final String AUTH_HEADER_NAME = "Authorization";
  public static final String AUTH_HEADER_LOGIN_UNICO = "LoginUnicoAuthorization";
  public static final String AUTH_HEADER_PORTADOR = "AuthorizationPortador";
  public static final String AUTH_HEADER_CORPORATIVO = "AuthorizationCorporativo";
  public static final String AUTH_HEADER_ESTABELECIMENTO = "AuthorizationEstabelecimento";
  public static final String AUTH_HEADER_SWAGGER = "AuthorizationSwagger";

  @Autowired private AcessoServicoRepository acessoServicoRepository;

  protected <Req, Res> Res callFunctionUserOrFunctionPortador(
      HttpServletRequest httpServletRequest,
      Req request,
      BiFunction<Req, SecurityUser, Res> functionUser,
      BiFunction<Req, SecurityUserPortador, Res> functionPortador,
      EntityManager em) {
    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
      return functionPortador.apply(request, userPortador);
    }

    return functionUser.apply(request, user);
  }

  protected <Req, Res> Res callFunctionUserOrFunctionPortadorOrFunctionCorporativo(
      HttpServletRequest httpServletRequest,
      Req request,
      BiFunction<Req, SecurityUser, Res> functionUser,
      BiFunction<Req, SecurityUserPortador, Res> functionPortador,
      BiFunction<Req, SecurityUserCorporativo, Res> functionCorporativo,
      EntityManager em) {
    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    SecurityUserCorporativo userCorporativo = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
      return functionUser.apply(request, user);
    } catch (Exception e) {
      try {
        userPortador = getAuthenticatedPortador(httpServletRequest, em);
        return functionPortador.apply(request, userPortador);
      } catch (Exception e2) {
        userCorporativo = getAuthenticatedCorporativo(httpServletRequest, em);
        return functionCorporativo.apply(request, userCorporativo);
      }
    }
  }

  protected <Req, Res> Res callFunctionPortadorOrFunctionCorporativo(
      HttpServletRequest httpServletRequest,
      Req request,
      BiFunction<Req, SecurityUserPortador, Res> functionPortador,
      BiFunction<Req, SecurityUserCorporativo, Res> functionCorporativo,
      EntityManager em) {
    SecurityUserPortador userPortador = null;
    SecurityUserCorporativo userCorporativo = null;
    try {
      userPortador = getAuthenticatedPortador(httpServletRequest, em);
    } catch (Exception e) {
      userCorporativo = getAuthenticatedCorporativo(httpServletRequest, em);
      return functionCorporativo.apply(request, userCorporativo);
    }

    return functionPortador.apply(request, userPortador);
  }

  protected <Req, Res> Res callFunctionUserOrFunctionPortadorOrFunctionEstabelecimento(
      HttpServletRequest httpServletRequest,
      Req request,
      BiFunction<Req, SecurityUser, Res> functionUser,
      BiFunction<Req, SecurityUserPortador, Res> functionPortador,
      BiFunction<Req, SecurityUserEstabelecimento, Res> functionEstabelecimento,
      EntityManager em) {
    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    SecurityUserEstabelecimento userEstabelecimento = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
    } catch (Exception e) {
      try {
        userPortador = getAuthenticatedPortador(httpServletRequest, em);
      } catch (Exception e2) {
        userEstabelecimento = getAuthenticatedUserEstabelecimento(httpServletRequest, em);
        return functionEstabelecimento.apply(request, userEstabelecimento);
      }

      return functionPortador.apply(request, userPortador);
    }

    return functionUser.apply(request, user);
  }

  protected <Req, Res>
      Res callFunctionUserOrFunctionPortadorOrFunctionEstabelecimentoOrFunctionCorporativo(
          HttpServletRequest httpServletRequest,
          Req request,
          BiFunction<Req, SecurityUser, Res> functionUser,
          BiFunction<Req, SecurityUserPortador, Res> functionPortador,
          BiFunction<Req, SecurityUserEstabelecimento, Res> functionEstabelecimento,
          BiFunction<Req, SecurityUserCorporativo, Res> functionCorporativo,
          EntityManager em) {
    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    SecurityUserEstabelecimento userEstabelecimento = null;
    SecurityUserCorporativo userCorporativo = null;
    try {
      user = getAuthenticatedUser(httpServletRequest, em);
    } catch (Exception e) {
      try {
        userPortador = getAuthenticatedPortador(httpServletRequest, em);
      } catch (Exception e2) {
        try {
          userEstabelecimento = getAuthenticatedUserEstabelecimento(httpServletRequest, em);
        } catch (Exception e3) {
          userCorporativo = getAuthenticatedCorporativo(httpServletRequest, em);
          return functionCorporativo.apply(request, userCorporativo);
        }
        return functionEstabelecimento.apply(request, userEstabelecimento);
      }
      return functionPortador.apply(request, userPortador);
    }
    return functionUser.apply(request, user);
  }

  public SecurityUserPortador getAuthenticatedPortadorValidando(
      HttpServletRequest request, EntityManager em) {
    Object obj = request.getSession().getAttribute("sec-portador");
    if (obj instanceof SecurityUser) {
      throw new GenericServiceException(
          "Token Invalido", "Não pertecente ao portador e sim ao issuer");
    }
    return getAuthenticatedPortador(request, em);
  }

  public SecurityUserPortador getAuthenticatedPortador(
      HttpServletRequest request, EntityManager em) {
    Object obj = request.getSession().getAttribute("sec-portador");
    if (obj instanceof SecurityUserPortador) {
      return (SecurityUserPortador) obj;
    }

    Long idLogin =
        new TokenHandlerPortador().getIdLoginFromToken(getToken(request, AUTH_HEADER_PORTADOR));
    PortadorLogin portador = em.find(PortadorLogin.class, idLogin);
    List<String> distinctRoles =
        acessoServicoRepository.retornaRolesDistintasDoGrupoPortadorDaInstituicao(
            portador.getIdProcessadora(), portador.getIdInstituicao());
    return new SecurityUserPortador(portador, distinctRoles);
  }

  public SecurityUserCorporativo getAuthenticatedCorporativo(
      HttpServletRequest request, EntityManager em) {
    Object obj = request.getSession().getAttribute("sec-corporativo");
    if (obj instanceof SecurityUserCorporativo) {
      return (SecurityUserCorporativo) obj;
    }

    Long id =
        new TokenHandlerCorporativo().getIdFromToken(getToken(request, AUTH_HEADER_CORPORATIVO));
    CorporativoLogin corporativo = em.find(CorporativoLogin.class, id);
    List<String> distinctRoles =
        acessoServicoRepository.retornaRolesDistintasDoGrupoPortadorDaInstituicao(
            corporativo.getIdProcessadora(), corporativo.getIdInstituicao());
    return new SecurityUserCorporativo(corporativo, distinctRoles);
  }

  public SecurityUserSwagger getAuthenticatedSwagger(HttpServletRequest request, EntityManager em) {
    Object obj = request.getSession().getAttribute("sec-user-swagger");
    if (obj instanceof SecurityUserSwagger) {
      return (SecurityUserSwagger) obj;
    }

    Integer idUser =
        new TokenHandlerSwagger().getUserIdFromToken(getToken(request, AUTH_HEADER_SWAGGER));
    SwaggerAcesso swaggerAcesso = em.find(SwaggerAcesso.class, idUser);
    return new SecurityUserSwagger(swaggerAcesso);
  }

  public SecurityUser getAuthenticatedUser(HttpServletRequest request, EntityManager em) {
    Object obj = request.getSession().getAttribute("sec-user");
    if (obj instanceof SecurityUser) {
      return (SecurityUser) obj;
    }

    Integer userId = new TokenHandler().getUserIdFromToken(getToken(request, AUTH_HEADER_NAME));
    AcessoUsuario usuario = em.find(AcessoUsuario.class, userId);
    return new SecurityUser(usuario);
  }

  public SecurityUserEstabelecimento getAuthenticatedUserEstabelecimento(
      HttpServletRequest request, EntityManager em) {
    Object obj = request.getSession().getAttribute("sec-user-estabelecimento");
    if (obj instanceof SecurityUserEstabelecimento) {
      return (SecurityUserEstabelecimento) obj;
    }

    Integer userId =
        new TokenHandlerEstabelecimento()
            .getUserIdFromToken(getToken(request, AUTH_HEADER_ESTABELECIMENTO));
    AcessoUsuarioEstabelecimento usuario = em.find(AcessoUsuarioEstabelecimento.class, userId);
    return new SecurityUserEstabelecimento(usuario);
  }

  private String getToken(HttpServletRequest request, String authorizationHeader) {
    String token = request.getHeader(authorizationHeader);
    if (token == null) {
      throw new GenericRuntimeException(authorizationHeader + " não encontrado.");
    }
    return token;
  }

  public static void checkHierarquiaPermission(AcessoUsuario usuario, Integer idNivelHierarquia) {
    if (idNivelHierarquia == null) {
      throw new NoResultException("Nível da Hierarquia não encontrada");
    } else if (idNivelHierarquia < RangeHierarquia.HIERARQUIA_MINIMA.value()
        || idNivelHierarquia > RangeHierarquia.HIERARQUIA_MAXIMA.value()) {
      throw new NoResultException("Hierarquia informada inexistente");
    } else if (!usuario.isNivelHierarquiaAllowed(idNivelHierarquia)) {
      throw new AccessDeniedException("Você não possui privilégios suficientes para essa operação");
    }
  }

  public static void checkHierarquiaUsuarioEstabelecimentoLogado(
      AcessoUsuarioEstabelecimento usuarioEstabelecimento, ContaPagamento contaPagamento) {
    Estabelecimento estabelecimento = usuarioEstabelecimento.getEstabelecimento();
    checkHierarquiaUsuarioEstabelecimentoLogado(
        estabelecimento,
        contaPagamento.getIdProcessadora(),
        contaPagamento.getIdInstituicao(),
        contaPagamento.getIdRegional(),
        contaPagamento.getIdFilial(),
        contaPagamento.getIdPontoDeRelacionamento());
  }

  public static void checkHierarquiaUsuarioEstabelecimentoLogado(
      AcessoUsuarioEstabelecimento usuarioEstabelecimento, Pessoa pessoa) {
    Estabelecimento estabelecimento = usuarioEstabelecimento.getEstabelecimento();
    SetorFilial setorFilial = pessoa.getSetorFilial();
    checkHierarquiaUsuarioEstabelecimentoLogado(
        estabelecimento,
        pessoa.getIdProcessadora(),
        pessoa.getIdInstituicao(),
        setorFilial != null ? setorFilial.getIdRegional() : null,
        setorFilial != null ? setorFilial.getIdFilial() : null,
        setorFilial != null ? setorFilial.getIdPontoRelacionamento() : null);
  }

  public static void checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
      AcessoUsuario usuario, Pessoa pessoa) {
    SetorFilial setorFilial = pessoa.getSetorFilial();
    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario,
        pessoa.getIdProcessadora(),
        pessoa.getIdInstituicao(),
        setorFilial != null ? setorFilial.getIdRegional() : null,
        setorFilial != null ? setorFilial.getIdFilial() : null,
        setorFilial != null ? setorFilial.getIdPontoRelacionamento() : null);
  }

  public static void checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
      AcessoUsuario usuario, ContaPagamento contaPagamento) {
    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario,
        contaPagamento.getIdProcessadora(),
        contaPagamento.getIdInstituicao(),
        contaPagamento.getIdRegional(),
        contaPagamento.getIdFilial(),
        contaPagamento.getIdPontoDeRelacionamento());
  }

  public static void checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
      AcessoUsuario usuario, AcessoUsuario usuarioAModificar) {

    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario,
        usuarioAModificar.getIdProcessadora(),
        usuarioAModificar.getIdInstituicao(),
        usuarioAModificar.getIdRegional(),
        usuarioAModificar.getIdFilial(),
        usuarioAModificar.getIdPontoDeRelacionamento());
  }

  public static void checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
      AcessoUsuario usuario,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    if ((usuario.getIdProcessadora() != null && !usuario.getIdProcessadora().equals(idProcessadora))
        || (usuario.getIdInstituicao() != null && !usuario.getIdInstituicao().equals(idInstituicao))
        || (usuario.getIdRegional() != null && !usuario.getIdRegional().equals(idRegional))
        || (usuario.getIdFilial() != null && !usuario.getIdFilial().equals(idFilial))
        || (usuario.getIdPontoDeRelacionamento() != null
            && !usuario.getIdPontoDeRelacionamento().equals(idPontoDeRelacionamento))) {

      throw new AccessDeniedException("Usuário logado não possui permissão.");
    }
  }

  public static void checkHierarquiaUsuarioLogadoEmParidadeOuSuperiorInstituicao(
      AcessoUsuario usuario, Integer idInstituicao) {
    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario, Constantes.ID_PROCESSADORA_ITS_PAY, idInstituicao, null, null, null);
  }

  public static void checkHierarquiaUsuarioLogadoEmSuperioridade(
      AcessoUsuario usuario, AcessoUsuario usuarioAModificar) {

    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario,
        usuarioAModificar.getIdProcessadora(),
        usuarioAModificar.getIdInstituicao(),
        usuarioAModificar.getIdRegional(),
        usuarioAModificar.getIdFilial(),
        usuarioAModificar.getIdPontoDeRelacionamento());

    checkNivelHierarquiaUsuarioLogadoEmSuperioridade(
        usuario, usuarioAModificar.getIdHierarquiaNivel());
  }

  public static void checkHierarquiaUsuarioLogadoEmSuperioridade(
      AcessoUsuario usuario,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario, idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);

    checkNivelHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
        usuario, idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  public static void checkHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
      AcessoUsuario usuario, AcessoUsuario usuarioAModificar) {

    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario,
        usuarioAModificar.getIdProcessadora(),
        usuarioAModificar.getIdInstituicao(),
        usuarioAModificar.getIdRegional(),
        usuarioAModificar.getIdFilial(),
        usuarioAModificar.getIdPontoDeRelacionamento());

    checkNivelHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
        usuario, usuarioAModificar.getIdHierarquiaNivel());
  }

  public static void checkHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
      AcessoUsuario usuario,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        usuario, idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);

    checkNivelHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
        usuario, idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  public static void checkNivelHierarquiaUsuarioLogadoEmSuperioridade(
      AcessoUsuario usuario,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    if ((usuario.getIdPontoDeRelacionamento() != null)
        || (idFilial != null && idPontoDeRelacionamento == null && usuario.getIdFilial() != null)
        || (idRegional != null && idFilial == null && usuario.getIdRegional() != null)
        || (idInstituicao != null && idRegional == null && usuario.getIdInstituicao() != null)
        || (idProcessadora != null
            && idInstituicao == null
            && usuario.getIdProcessadora() != null)) {

      throw new AccessDeniedException("Usuário logado não possui permissão.");
    }
  }

  public static void checkNivelHierarquiaUsuarioLogadoEmSuperioridade(
      AcessoUsuario usuario, Integer idNivelHierarquia) {

    if ((idNivelHierarquia == null) || (usuario.getIdHierarquiaNivel() >= idNivelHierarquia)) {

      throw new AccessDeniedException("Usuário logado não possui permissão.");
    }
  }

  public static void checkNivelHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
      AcessoUsuario usuario,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    if ((usuario.getIdPontoDeRelacionamento() != null)
        || (idFilial != null && idPontoDeRelacionamento == null && usuario.getIdFilial() != null)
        || (idRegional != null && idFilial == null && usuario.getIdRegional() != null)
        || (idInstituicao != null && idRegional == null && usuario.getIdInstituicao() != null)) {

      throw new AccessDeniedException("Usuário logado não possui permissão.");
    }
  }

  public static void checkNivelHierarquiaUsuarioLogadoEmSuperioridadeExcetoProcessadora(
      AcessoUsuario usuario, Integer idNivelHierarquia) {

    if ((idNivelHierarquia == null)
        || (usuario.getIdHierarquiaNivel() >= idNivelHierarquia
            && !usuario.getIdHierarquiaNivel().equals(HierarquiaType.PROCESSADORA.value()))) {

      throw new AccessDeniedException("Usuário logado não possui permissão.");
    }
  }

  public static void checkHierarquiaUsuarioEstabelecimentoLogado(
      Estabelecimento estabelecimento,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    if ((estabelecimento.getIdProcessadora() != null
            && !estabelecimento.getIdProcessadora().equals(idProcessadora))
        || (estabelecimento.getIdInstituicao() != null
            && !estabelecimento.getIdInstituicao().equals(idInstituicao))
        || (estabelecimento.getIdRegional() != null
            && !estabelecimento.getIdRegional().equals(idRegional))
        || (estabelecimento.getIdFilial() != null
            && !estabelecimento.getIdFilial().equals(idFilial))
        || (estabelecimento.getIdPontoDeRelacionamento() != null
            && !estabelecimento.getIdPontoDeRelacionamento().equals(idPontoDeRelacionamento))) {

      throw new AccessDeniedException("Usuário logado não possui permissão.");
    }
  }

  /**
   * Checa, a partir de dois objetos, se a hierarquia entre eles é a mesma. Retorna true sempre que
   * uma distinção for encontrada entre as hierarquias dos objetos.
   *
   * @param primeiroObjeto -> ContaPagamento ou AcessoUsuario
   * @param segundoObjeto -> ContaPagamento ou AcessoUsuario
   * @return -> Boolean: true: existe disparidade entre as hierarquias dos objetos. false: objetos
   *     pertencem à mesma hierarquia.
   */
  public static Boolean checkHierarquiaObjetosIguaisEmParidadeOuSuperior(
      Object primeiroObjeto, Object segundoObjeto) {
    Integer tipoObjeto = descobreTipoDeObjetos(primeiroObjeto, segundoObjeto);
    switch (tipoObjeto) {
      case OBJETOS_CONTA_PAGAMENTO:
        return checaObjetosContaPagamentoEmParidade(
            (ContaPagamento) primeiroObjeto, (ContaPagamento) segundoObjeto);
      case OBJETOS_ACESSO_USUARIO:
        return checaObjetosAcessoUsuarioEmParidade(
            (AcessoUsuario) primeiroObjeto, (AcessoUsuario) segundoObjeto);
      case OBJETOS_CONTA_E_USUARIO:
        return checaUsuarioContaEmParidade(
            (AcessoUsuario) segundoObjeto, (ContaPagamento) primeiroObjeto);
      case OBJETOS_USUARIO_E_CONTA:
        return checaUsuarioContaEmParidade(
            (AcessoUsuario) primeiroObjeto, (ContaPagamento) segundoObjeto);
      case OBJETO_INVALIDO_OU_DISTINTOS_ENTRE_SI:
      default:
        throw new GenericServiceException("Não foi possível fazer a comparação entre objetos.");
    }
  }

  private static Integer descobreTipoDeObjetos(Object primeiroObjeto, Object segundoObjeto) {
    if (primeiroObjeto instanceof ContaPagamento && segundoObjeto instanceof ContaPagamento) {
      return OBJETOS_CONTA_PAGAMENTO;
    }
    if (primeiroObjeto instanceof AcessoUsuario && segundoObjeto instanceof AcessoUsuario) {
      return OBJETOS_ACESSO_USUARIO;
    }
    if (primeiroObjeto instanceof ContaPagamento && segundoObjeto instanceof AcessoUsuario) {
      return OBJETOS_CONTA_E_USUARIO;
    }
    if (primeiroObjeto instanceof AcessoUsuario && segundoObjeto instanceof ContaPagamento) {
      return OBJETOS_USUARIO_E_CONTA;
    }
    return OBJETO_INVALIDO_OU_DISTINTOS_ENTRE_SI;
  }

  private static Boolean checaObjetosContaPagamentoEmParidade(
      ContaPagamento primeiraConta, ContaPagamento segundaConta) {
    return checaHierarquia(
        primeiraConta.getIdProcessadora(),
        segundaConta.getIdProcessadora(),
        primeiraConta.getIdInstituicao(),
        segundaConta.getIdInstituicao(),
        primeiraConta.getIdRegional(),
        segundaConta.getIdRegional(),
        primeiraConta.getIdFilial(),
        segundaConta.getIdFilial(),
        primeiraConta.getIdPontoDeRelacionamento(),
        segundaConta.getIdPontoDeRelacionamento());
  }

  private static Boolean checaObjetosAcessoUsuarioEmParidade(
      AcessoUsuario primeiroUsuario, AcessoUsuario segundoUsuario) {
    return checaHierarquia(
        primeiroUsuario.getIdProcessadora(),
        segundoUsuario.getIdProcessadora(),
        primeiroUsuario.getIdInstituicao(),
        segundoUsuario.getIdInstituicao(),
        primeiroUsuario.getIdRegional(),
        segundoUsuario.getIdRegional(),
        primeiroUsuario.getIdFilial(),
        segundoUsuario.getIdFilial(),
        primeiroUsuario.getIdPontoDeRelacionamento(),
        segundoUsuario.getIdPontoDeRelacionamento());
  }

  private static Boolean checaUsuarioContaEmParidade(AcessoUsuario usuario, ContaPagamento conta) {
    return checaHierarquia(
        usuario.getIdProcessadora(),
        conta.getIdProcessadora(),
        usuario.getIdInstituicao(),
        conta.getIdInstituicao(),
        usuario.getIdRegional(),
        conta.getIdRegional(),
        usuario.getIdFilial(),
        conta.getIdFilial(),
        usuario.getIdPontoDeRelacionamento(),
        conta.getIdPontoDeRelacionamento());
  }

  private static Boolean checaHierarquia(
      Integer idProcessadoraA,
      Integer idProcessadoraB,
      Integer idInstituicaoA,
      Integer idInstituicaoB,
      Integer idRegionalA,
      Integer idRegionalB,
      Integer idFilialA,
      Integer idFilialB,
      Integer idPontoDeRelacionamentoA,
      Integer idPontoDeRelacionamentoB) {
    return (idProcessadoraA != null
            && idProcessadoraB != null
            && !idProcessadoraA.equals(idProcessadoraB))
        || (idInstituicaoA != null
            && idInstituicaoB != null
            && !idInstituicaoA.equals(idInstituicaoB))
        || (idRegionalA != null && idRegionalB != null && !idRegionalA.equals(idRegionalB))
        || (idFilialA != null && idFilialB != null && !idFilialA.equals(idFilialB))
        || (idPontoDeRelacionamentoA != null
            && idPontoDeRelacionamentoB != null
            && !idPontoDeRelacionamentoA.equals(idPontoDeRelacionamentoB));
  }

  public static void checkProcessadora(HierarquiaProcessadora processadora) {
    if (processadora == null) {
      throw new NoResultException("Processadora não encontrada");
    }
  }

  public static void copyNonNullProperties(Object src, Object target) {
    BeanUtils.copyProperties(src, target, getNullPropertyNames(src));
  }

  public static String[] getNullPropertyNames(Object source) {
    final BeanWrapper src = new BeanWrapperImpl(source);
    PropertyDescriptor[] pds = src.getPropertyDescriptors();

    Set<String> emptyNames = new HashSet();
    for (PropertyDescriptor pd : pds) {
      Object srcValue = src.getPropertyValue(pd.getName());
      if (srcValue == null) {
        emptyNames.add(pd.getName());
      }
    }
    String[] result = new String[emptyNames.size()];
    return emptyNames.toArray(result);
  }

  public static String hashFile(String path) {
    FileInputStream fis;
    String md5 = null;

    try {
      fis = new FileInputStream(new File(path));
      md5 = DigestUtils.md5DigestAsHex(fis);
    } catch (FileNotFoundException e) {
      throwNoResultException(e, "Arquivo não encontrado");
    } catch (IOException e) {
      throwNoResultException(e, "Arquivo não pode ser encontrado");
    }

    return md5;
  }

  private static void throwNoResultException(Throwable e, String message) {
    NoResultException noResultException = new NoResultException(message);
    noResultException.initCause(e);
    throw noResultException;
  }

  public static String encodeSenhaSHA256(String senha) {
    LegacyPasswordEncoder shaPasswordEncoder = new LegacyPasswordEncoder(SHA_256);
    return shaPasswordEncoder.encodePassword(senha, null);
  }

  public static String encodeSenhaSHA512(String senha) {
    LegacyPasswordEncoder shaPasswordEncoder = new LegacyPasswordEncoder(SHA_512);
    return shaPasswordEncoder.encodePassword(senha, null);
  }

  public static String getTokenJWT(HttpServletRequest request) {
    String tokenJWT = request.getHeader(AUTH_HEADER_NAME);

    if (tokenJWT == null) {
      tokenJWT = request.getHeader(AUTH_HEADER_PORTADOR);
    }

    if (tokenJWT == null) {
      tokenJWT = request.getHeader(AUTH_HEADER_CORPORATIVO);
    }

    if (tokenJWT == null) {
      tokenJWT = request.getHeader(AUTH_HEADER_ESTABELECIMENTO);
    }

    if (tokenJWT != null) {
      tokenJWT = tokenJWT.replace("Bearer ", "");
    }

    return tokenJWT;
  }

  public String getIpOrigem(HttpServletRequest request) {
    return (request.getHeader("X-Forwarded-For")) == null
        ? request.getRemoteAddr()
        : request.getHeader("X-Forwarded-For");
  }

  protected CredencialService getCredencialService() {
    return SpringContextUtils.getBean(CredencialService.class);
  }

  protected void error(String message, Throwable t) {
    log.error(message, t);
  }

  protected void info(String message) {
    log.info(message);
  }
}
