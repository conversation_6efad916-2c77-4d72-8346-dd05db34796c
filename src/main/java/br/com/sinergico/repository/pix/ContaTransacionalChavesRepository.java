package br.com.sinergico.repository.pix;

import br.com.entity.pix.ContaTransacional;
import br.com.entity.pix.ContaTransacionalChaves;
import br.com.sinergico.vo.InfosChavesPixIssuerVO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface ContaTransacionalChavesRepository
    extends JpaRepository<ContaTransacionalChaves, Long> {

  List<ContaTransacionalChaves> findByValorChaveAndInscricaoNacionalAndDeletedFalse(
      String valorChave, String cpf);

  Optional<ContaTransacionalChaves>
      findFirstByValorChaveAndInscricaoNacionalAndReivindicadaFalseOrderByIdDesc(
          String valorChave, String cpf);

  @Query(
      "select new br.com.sinergico.vo.InfosChavesPixIssuerVO(tc.tipo, ctc.valorChave, ctc.dataInclusao, ctc.reivindicada, ctc.deleted) "
          + "from ContaTransacionalChaves as ctc "
          + "inner join ctc.contaTransacional as ct "
          + "inner join ctc.tipoChave as tc "
          + "where ct.id = :idContaTransacional ")
  List<InfosChavesPixIssuerVO> findContaTransacionalChavesByIdContaTransacional(
      @Param("idContaTransacional") Long idContaTransacional);

  ContaTransacionalChaves findByContaTransacionalAndValorChave(
      ContaTransacional conta, String valor);

  @Query(
      value =
          " select \n "
              + " ctc.* \n "
              + " from pix.conta_transacional_chaves ctc \n "
              + " \t inner join pix.conta_transacional ct on ct.id_conta_transacional = ctc.id_conta_transacional \n "
              + " where true \n "
              + " \t and ct.conta = :idConta \n "
              + " \t and ctc.valor_chave = :valorChave \n "
              + " \t and ctc.deleted = 0 \n "
              + " \t and ctc.reivindicada = 0 ",
      nativeQuery = true)
  Optional<ContaTransacionalChaves> descobreChavePixAssociadaAConta(
      @Param("valorChave") String valorChave, @Param("idConta") Long idConta);
}
