package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.CargaAutoProdutoParametro;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.adq.AtualizarParametrosCargaVO;
import br.com.json.bean.adq.ParametrosCargaProdutosVO;
import br.com.json.bean.adq.ProdutosCargaVO;
import br.com.sinergico.repository.cadastral.CargaAutoProdutoParametroRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CargaAutoProdutoParametroService
    extends GenericService<CargaAutoProdutoParametro, Long> {

  private CargaAutoProdutoParametroRepository repository;

  @Autowired
  public CargaAutoProdutoParametroService(
      CargaAutoProdutoParametroRepository cargaAutoProdutoParametroRepository) {
    super(cargaAutoProdutoParametroRepository);
    this.repository = cargaAutoProdutoParametroRepository;
  }

  public CargaAutoProdutoParametro encontrarParametroDoProduto(
      Integer idProduto, Integer idParametro, Integer status) {
    return repository.findByIdProdInstituicaoAndIdParametroCargaAndIdStatus(
        idProduto, idParametro, status);
  }

  public void salvar(CargaAutoProdutoParametro cargaAutoProdutoParametro) {
    repository.save(cargaAutoProdutoParametro);
  }

  public void salvarLista(List<CargaAutoProdutoParametro> cargaAutoProdutoParametroList) {
    repository.saveAll(cargaAutoProdutoParametroList);
  }

  public Boolean salvarProdutosCarga(
      ParametrosCargaProdutosVO parametrosCargaProdutosVO, SecurityUser user) {

    for (Integer cargaProdutosVO : parametrosCargaProdutosVO.getIdProdInstituicao()) {

      for (ProdutosCargaVO listProdutos : parametrosCargaProdutosVO.getProdutos()) {
        CargaAutoProdutoParametro produto = new CargaAutoProdutoParametro();
        produto.setIdProdInstituicao(cargaProdutosVO);
        produto.setIdParametroCarga(listProdutos.getIdParametroCarga());
        produto.setVlParametro(listProdutos.getVlParametro());
        produto.setIdStatus(1);
        produto.setDtHrStatus(LocalDateTime.now());
        produto.setIdUsuarioInclusao(user.getIdUsuario());
        produto.setDtHrInclusao(LocalDateTime.now());
        repository.save(produto);
      }
    }
    return true;
  }

  public List<CargaAutoProdutoParametro> findIdProdutoInstituicao(Integer idProdInstituicao) {
    return repository.findByIdProdInstituicao(idProdInstituicao);
  }

  public Boolean existsByProduto(Integer idProduto) {
    return repository.existsByProduto(idProduto);
  }

  public Boolean existsLiberacaoManualByProduto(Integer idProdInstituicao) {
    return repository.existsLiberacaoManualByProduto(idProdInstituicao);
  }

  public Boolean atualizarProdutosCarga(AtualizarParametrosCargaVO model, SecurityUser user) {

    for (Integer obj : model.getIdProdInstituicao()) {
      for (ProdutosCargaVO produtosCargaVO : model.getProdutos()) {
        CargaAutoProdutoParametro cargaAutoProdutoParametro =
            repository.findById(produtosCargaVO.getId()).orElse(null);

        if (cargaAutoProdutoParametro == null) {
          throw new GenericServiceException("Parâmetro não encontrado!");
        }
        cargaAutoProdutoParametro.setIdProdInstituicao(obj);
        cargaAutoProdutoParametro.setIdParametroCarga(produtosCargaVO.getIdParametroCarga());
        cargaAutoProdutoParametro.setVlParametro(produtosCargaVO.getVlParametro());
        cargaAutoProdutoParametro.setIdStatus(1);
        cargaAutoProdutoParametro.setDtHrStatus(LocalDateTime.now());
        cargaAutoProdutoParametro.setIdUsuarioManutencao(user.getIdUsuario());
        cargaAutoProdutoParametro.setDtHrManutencao(LocalDateTime.now());
        repository.save(cargaAutoProdutoParametro);
      }
    }

    return true;
  }

  public String buscarFaturasIntegracao(Long idFaura) {
    return repository.buscarFaturasIntegracao(idFaura);
  }
}
