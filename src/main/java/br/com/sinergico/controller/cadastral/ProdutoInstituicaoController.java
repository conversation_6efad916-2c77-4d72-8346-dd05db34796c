package br.com.sinergico.controller.cadastral;

import br.com.entity.cadastral.ParcelamentoFaturaProduto;
import br.com.entity.cadastral.PlanoSaudeProduto;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracaoCredito;
import br.com.entity.cadastral.RenegociacaoDividaProduto;
import br.com.entity.cadastral.TaxasRenegociacaoDividaProduto;
import br.com.entity.suporte.EventoFaturavelB2B;
import br.com.entity.suporte.EventoNotificavel;
import br.com.entity.suporte.EventoNotificavelProduto;
import br.com.entity.suporte.ItemFaturaProdutoB2B;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.AddItemFaturavelProduto;
import br.com.json.bean.cadastral.AddRenegociacaoDividaProdutoVO;
import br.com.json.bean.cadastral.AlteraStatusRequest;
import br.com.json.bean.cadastral.BuscaProdutoNaoB2b;
import br.com.json.bean.cadastral.BuscarProdutoByFilter;
import br.com.json.bean.cadastral.CadastrarProdutoInstituicao;
import br.com.json.bean.cadastral.CadastroProdutoModel;
import br.com.json.bean.cadastral.ContaProdutoLoginResponseVO;
import br.com.json.bean.cadastral.CreatePerfilTarifario;
import br.com.json.bean.cadastral.DadosProdutoInstituicao;
import br.com.json.bean.cadastral.DeglosePlanoSaudeVO;
import br.com.json.bean.cadastral.DetalhesProduto;
import br.com.json.bean.cadastral.GetDetalhesProdInstituicao;
import br.com.json.bean.cadastral.GetPerfisTarifarios;
import br.com.json.bean.cadastral.ParcelamentoFaturaProdutoVO;
import br.com.json.bean.cadastral.PlanoSaudeVO;
import br.com.json.bean.cadastral.ProdInstConfigCreditoRequest;
import br.com.json.bean.cadastral.ProdutoInstituicaoParametrizadoVO;
import br.com.json.bean.cadastral.ProdutoInstituicaoResponse;
import br.com.json.bean.cadastral.ResponseProdutosInstituicao;
import br.com.json.bean.cadastral.TaxasRenegociacaoDividaProdutoVO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/produtoinstituicao")
public class ProdutoInstituicaoController extends UtilController {

  @Autowired private ProdutoInstituicaoService service;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @ApiOperation(value = "Serviço responsável por cadastrar o produto instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_PROD_INSTITUICAO"})
  public ResponseEntity<HashMap<String, Object>> create(
      @RequestBody @Valid CadastrarProdutoInstituicao model, BindingResult result) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cadastro de produto instituição", result);
    }

    ProdutoInstituicao produtoInstituicao = new ProdutoInstituicao();
    produtoInstituicao = service.prepareProdutoInstituicao(model, produtoInstituicao, user);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Produto instituicao cadastrado com sucesso");
    map.put("id", produtoInstituicao.getId());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Buscar os produtos instituição",
      response = ProdutoInstituicao.class,
      notes = "Lista os produtos instituição cadastrados.",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PROD_INSTITUICAO"})
  public List<ProdutoInstituicao> getAll() {

    return service.findAll();
  }

  @ApiOperation(
      value =
          "Buscar os produtos instituição para pontos de relacionamentos que não sejam B2B e de idRelacionamento 1 para PF",
      response = ResponseProdutosInstituicao.class,
      notes = "Lista os produtos instituição cadastrados que não sejam B2B e de idRelacionamento 1",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-not-b2b/pessoa-fisica/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_NOT_B2B_AND_PF"})
  public List<ResponseProdutosInstituicao> getAllNotB2b(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.findAllNotB2b(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value =
          "Buscar os produtos instituição para pontos de relacionamentos que não sejam B2B e de idRelacionamento 1 para PJ",
      response = ResponseProdutosInstituicao.class,
      notes = "Lista os produtos instituição cadastrados que não sejam B2B e de idRelacionamento 1",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-not-b2b/pessoa-juridica/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_NOT_B2B_AND_PF"})
  public List<ResponseProdutosInstituicao> getAllNotB2bJuridico(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.findAllNotB2bJuridico(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value =
          "Buscar os produtos instituição para pontos de relacionamentos que não sejam B2B e de idRelacionamento 1 para PF",
      response = ResponseProdutosInstituicao.class,
      notes = "Lista os produtos instituição cadastrados que não sejam B2B e de idRelacionamento 1",
      responseContainer = "List")
  @RequestMapping(
      value = "/buscar/pessoa-fisica/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> getProdutosPessoaFisica(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.findAllNotB2b(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value =
          "Buscar os produtos instituição para pontos de relacionamentos que não sejam B2B e de idRelacionamento 1 para PJ",
      response = ResponseProdutosInstituicao.class,
      notes = "Lista os produtos instituição cadastrados que não sejam B2B e de idRelacionamento 1",
      responseContainer = "List")
  @RequestMapping(
      value = "/buscar/pessoa-juridica/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> getProdutosPessoaJuridica(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.findAllNotB2bJuridico(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value = "Buscar os produtos instituição por Instituicao",
      response = ProdutoInstituicao.class,
      notes = "Lista os produtos instituição cadastrados.",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({
    "ROLE_BUSCAR_PROD_INSTITUICAO_BY_INSTITUICAO",
    "ROLE_BUSCAR_PRODS_INSTITUICAO_NOT_B2B_AND_PF"
  })
  public List<ResponseProdutosInstituicao> findByProcessadoraInstituicao(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {

    return service.findAllByInstituicao(idProcessadora, idInstituicao);
  }

  @ApiOperation(
      value = "Buscar os produtos instituição por Instituicao com condições cadastradas",
      response = ProdutoInstituicao.class,
      notes = "Lista os produtos instituição cadastrados.",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/{idInstituicao}/{idProcessadora}",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PROD_INSTITUICAO_BY_INSTITUICAO"})
  public List<ProdutoInstituicao> findByInstituicaoProcessadoraComCondicoes(
      @PathVariable Integer idInstituicao, @PathVariable Integer idProcessadora) {

    return service.findByIntituicao(idInstituicao, idProcessadora);
  }

  @ApiOperation(
      value = "Buscar os produtos instituição por Instituicao que sejam B2B",
      response = ProdutoInstituicaoResponse.class,
      notes = "Lista os produtos instituição cadastrados que sejam B2B",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/find/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_B2B"})
  public List<ProdutoInstituicaoResponse> findByInstituicaoProcessadoraAndB2b(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {

    return service.findByIntituicaoAndB2b(idProcessadora, idInstituicao);
  }

  @ApiOperation(
      value = "Buscar os produtos pela hierarquia do usuario logado",
      response = GetDetalhesProdInstituicao.class,
      notes = "Lista os produtos que o usuario logado pode acessar",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar/{idInstituicao}/{first}/{max}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_BY_USUARIO_LOGADO"})
  public List<GetDetalhesProdInstituicao> findByUser(
      @PathVariable Integer idInstituicao,
      @PathVariable Integer first,
      @PathVariable Integer max,
      @RequestParam(required = false) String termo) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (user.getIdInstituicao() == null) {
      if (idInstituicao == 0) {
        return service.findByIdProcessadora(user.getIdProcessadora(), termo, first, max);
      }
      return service.findByIdProcessadoraAndIdInstituicao(
          user.getIdProcessadora(), idInstituicao, termo, first, max);
    } else {
      return service.findByIdProcessadoraAndIdInstituicao(
          user.getIdProcessadora(), user.getIdInstituicao(), termo, first, max);
    }
  }

  @ApiOperation(
      value = "Buscar os produtos parametrizados",
      response = GetDetalhesProdInstituicao.class,
      notes = "Lista os produtos que o usuario logado pode acessar",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-parametrizados/{idInstituicao}/{first}/{max}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_BY_USUARIO_LOGADO"})
  public List<ProdutoInstituicaoParametrizadoVO> buscarProdutosParametrizados(
      @PathVariable Integer idInstituicao, @PathVariable Integer first, @PathVariable Integer max) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (user.getIdInstituicao() == null) {
      return service.buscarProdutosParametrizados(
          user.getIdProcessadora(), idInstituicao, first, max);
    } else {
      return service.buscarProdutosParametrizados(
          user.getIdProcessadora(), user.getIdInstituicao(), first, max);
    }
  }

  @ApiOperation(
      value = "Contar os produtos pela hierarquia do usuario logado",
      response = GetDetalhesProdInstituicao.class,
      notes = "Conta os produtos que o usuario logado pode acessar",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/count/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_BY_USUARIO_LOGADO"})
  public Integer countByUser(
      @PathVariable Integer idInstituicao, @RequestParam(required = false) String termo) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (user.getIdInstituicao() == null) {
      if (idInstituicao == 0) {
        return service.countByIdProcessadora(user.getIdProcessadora(), termo);
      }
      return service.countByIdProcessadoraAndIdInstituicao(
          user.getIdProcessadora(), idInstituicao, termo);
    } else {
      return service.countByIdProcessadoraAndIdInstituicao(
          user.getIdProcessadora(), user.getIdInstituicao(), termo);
    }
  }

  @ApiOperation(
      value = "Buscar detalhes de um produto instituição por id",
      response = DetalhesProduto.class,
      notes = "Lista os detalhes de um produtos instituição cadastrado.",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/detalhes/{id}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DETALHES_PROD_INSTITUICAO"})
  public DetalhesProduto getDetalhesProduto(@PathVariable Integer id) {

    DetalhesProduto detalhes = service.buscarDetalhesProduto(id);
    return detalhes;
  }

  @ApiOperation(
      value = "Buscar detalhes de um produto instituição por id",
      response = DetalhesProduto.class,
      notes = "Lista os detalhes de um produtos instituição cadastrado.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/get/{id}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DETALHES_LOTE"})
  public ProdutoInstituicao getProdutoInstituicao(@PathVariable Integer id) {

    ProdutoInstituicao detalhes = service.findByIdProdInstituicao(id);
    return detalhes;
  }

  @ApiOperation(
      value = "Gerar sequencia de id do Produto Instituicao",
      response = ProdutoInstituicao.class,
      notes = "Gera a sequencia de id do Produto Instituicao",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/generate/prodInst/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GERAR_SEQUENCIA_ID_PROD_INSTITUICAO"})
  public Integer generateIdProdInst(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    return service.gerarIdProdutoInstituicao(idProcessadora, idInstituicao);
  }

  @ApiOperation(
      value =
          "Serviço responsável por cadastrar o produto instituição e produto instituição configuração")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cadastrarCompleto",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_PROD_INSTITUICAO_AND_CONFIG"})
  public ResponseEntity<HashMap<String, Object>> cadastrarCompleto(
      @RequestBody @Valid CadastroProdutoModel model, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();

    SecurityUser user = getAuthenticatedUser(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    service.salvarProdutoInstAndConfig(model, user);

    map.put("msg", "Produto Instituição cadastrado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value =
          "Serviço responsável por atualizar os dados de um produto instituição e produto instituição configuração")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizarDados",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATUALIZAR_DADOS_PROD_INSTITUICAO_AND_CONFIG"})
  public ResponseEntity<Map<String, Object>> atualizarDados(
      @RequestBody @Valid DadosProdutoInstituicao model, BindingResult result) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    Map<String, Object> map = service.atualizarDadosProduto(model, user);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Buscar o perfis tarifarios do produto",
      response = GetPerfisTarifarios.class,
      notes = "Retorna os perfis tarifarios do produto",
      responseContainer = "list")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/perfil-tarifario/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PERFIS_PRODUTO"})
  public ResponseEntity<List<GetPerfisTarifarios>> getPerfisTarifariosProduto(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    List<GetPerfisTarifarios> response =
        service.getPerfisTarifariosProduto(idProcessadora, idInstituicao, idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Criar um perfil tarifario para um produto",
      response = Boolean.class,
      notes = "Cria o perfil tarifário de um produto")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/perfil-tarifario/create",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CRIAR_PERFIL_PRODUTO"})
  public ResponseEntity<?> createPerfisTarifariosProduto(
      @RequestBody @Valid CreatePerfilTarifario model) {
    SecurityUser user = getAuthenticatedUser(request, em);
    Boolean response = service.createPerfilTarifarioProduto(model, user) != null;
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Listar os itens faturaveis do produto",
      response = ItemFaturaProdutoB2B.class,
      responseContainer = "list")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/itens-faturaveis/list/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_ITENS_FATURAVEIS"})
  public ResponseEntity<?> getItensFaturaveisProduto(@PathVariable Integer idProdInstituicao) {
    List<ItemFaturaProdutoB2B> response = service.getItensFaturaveisProduto(idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Listar os eventos faturaveis disponíveis para associar ao produto",
      response = EventoFaturavelB2B.class,
      responseContainer = "list")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/eventos-faturaveis-disponiveis/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_EVENTOS_FATURAVEIS"})
  public ResponseEntity<?> getEventosFaturaveisDisponiveisProduto(
      @PathVariable Integer idProdInstituicao) {
    List<EventoFaturavelB2B> response =
        service.getEventosFaturaveisDisponiveisProduto(idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Adicionar um item faturável ao produto",
      response = ItemFaturaProdutoB2B.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/itens-faturaveis/create",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ADD_ITENS_FATURAVEIS"})
  public ResponseEntity<?> addItemFaturavelProduto(
      @RequestBody @Valid AddItemFaturavelProduto model) {
    ItemFaturaProdutoB2B response = service.addItemFaturavelProduto(model);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Editar a descrição de um item faturável ao produto",
      response = ItemFaturaProdutoB2B.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/itens-faturaveis/update",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_EDIT_ITENS_FATURAVEIS"})
  public ResponseEntity<?> updateItemFaturavelProduto(
      @RequestBody @Valid AddItemFaturavelProduto model) {
    ItemFaturaProdutoB2B response = service.updateItemFaturavelProduto(model);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os produtos instituição pos-pagos",
      response = ResponseProdutosInstituicao.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-all-pos/pf/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> getAllPos(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.findAllPos(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value = "Buscar os produtos instituição pos-pagos",
      response = ResponseProdutosInstituicao.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-all-pos/pj/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> getAllPosPJ(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.findAllPosPJ(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value = "Buscar resumo de um produto instituição pos-pagos para propostas",
      response = ResponseProdutosInstituicao.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-resumo-pos/pf/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseProdutosInstituicao getResumoProdPos(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    try {
      return service.findResumoProdutoById(idProcessadora, idInstituicao, idProdInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível recuperar o resumo do produto instituição");
    }
  }

  @ApiOperation(
      value = "Buscar configuracoes de um produto de credito",
      response = ProdutoInstituicaoConfiguracaoCredito.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-config-credito/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> getConfiguracaoProdutoCredito(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    ProdutoInstituicaoConfiguracaoCredito response =
        service.getConfiguracaoProdutoCredito(idProcessadora, idInstituicao, idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Salvar configuracoes de um produto de credito",
      response = ProdutoInstituicaoConfiguracaoCredito.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/save-config-credito",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> saveConfiguracaoProdutoCredito(
      @RequestBody @Valid ProdInstConfigCreditoRequest model) {
    ProdutoInstituicaoConfiguracaoCredito response = service.saveConfiguracaoProdutoCredito(model);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os eventos notificaveis de um produto",
      response = EventoNotificavelProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/buscar-eventos-notificaveis-produto/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> getEventosNotificaveisProduto(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    List<EventoNotificavelProduto> response =
        service.getEventosNotificaveisProduto(idProcessadora, idInstituicao, idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os eventos notificaveis disponiveis para associar à um produto",
      response = EventoNotificavel.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-eventos-notificaveis/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> getEventosNotificaveisDisponiveisToProduto(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    List<EventoNotificavel> response =
        service.getEventosNotificaveisDisponiveisToProduto(
            idProcessadora, idInstituicao, idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Adicionar um evento notificavel à um produto",
      response = EventoNotificavelProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/save/evento-notificavel-produto",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> saveEventoNotificavelProduto(
      @RequestBody @Valid EventoNotificavelProduto model) {
    service.saveEventoNotificavelProduto(model);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(
      value = "Alterar um evento notificavel de um produto",
      response = EventoNotificavelProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/update/evento-notificavel-produto",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> updateEventoNotificavelProduto(
      @RequestBody @Valid EventoNotificavelProduto model) {
    service.updateEventoNotificavelProduto(model);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(
      value = "Excluir um evento notificavel de um produto",
      response = EventoNotificavelProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/delete/evento-notificavel-produto/{id}",
      method = RequestMethod.DELETE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> deleteEventoNotificavelProduto(@PathVariable Integer id) {
    service.deleteEventoNotificavelProduto(id);
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar configuracoes de parcelamento de fatura de um produto de credito",
      response = ParcelamentoFaturaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/get-config-credito/parc-fatura/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CONGIF_PARCELAM_FAT_PROD"})
  public ResponseEntity<?> getConfigParcFatProdutoCredito(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    List<ParcelamentoFaturaProduto> response =
        service.getConfigParcFatProdutoCredito(idProcessadora, idInstituicao, idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Editar uma configuracao de parcelamento de fatura de um produto de credito",
      response = ParcelamentoFaturaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/edit-config-credito/parc-fatura",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_EDIT_CONGIF_PARCELAM_FAT_PROD"})
  public ResponseEntity<?> editConfigParcFatProdutoCredito(
      @RequestBody ParcelamentoFaturaProdutoVO model) {
    SecurityUser user = getAuthenticatedUser(request, em);
    ParcelamentoFaturaProduto response =
        service.editConfigParcFatProdutoCredito(model, user.getIdUsuario());
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Adicionar uma configuracao de parcelamento de fatura de um produto de credito",
      response = ParcelamentoFaturaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/add-config-credito/parc-fatura",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ADD_CONGIF_PARCELAM_FAT_PROD"})
  public ResponseEntity<?> addConfigParcFatProdutoCredito(
      @RequestBody ParcelamentoFaturaProdutoVO model) {
    SecurityUser user = getAuthenticatedUser(request, em);
    ParcelamentoFaturaProduto response =
        service.addConfigParcFatProdutoCredito(model, user.getIdUsuario());
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar configuracoes de renegociacao de divida de um produto de credito",
      response = RenegociacaoDividaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/get-config/renegociacao-divida/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CONGIF_RENEGOC_DIV_PROD"})
  public ResponseEntity<?> getConfigRenegocDividaProduto(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    List<RenegociacaoDividaProduto> response =
        service.getConfigRenegocDividaProduto(idProcessadora, idInstituicao, idProdInstituicao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar planos de saúde de um produto de credito",
      response = PlanoSaudeVO.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{idProcessadora}/{idInstituicao}/{idProdInstituicao}/plano-saude",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PLANO_SAUDE"})
  public ResponseEntity<?> getPlanoSaude(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    return new ResponseEntity<>(
        service.getPlanoSaude(idProcessadora, idInstituicao, idProdInstituicao), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar degloses de um plano de saúde",
      response = DeglosePlanoSaudeVO.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/{idProcessadora}/{idInstituicao}/{idProdInstituicao}/plano-saude/{idPlanoSaude}/degloses",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PLANO_SAUDE"})
  public ResponseEntity<?> getDeglosesPlanoSaude(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao,
      @PathVariable Long idPlanoSaude) {

    PlanoSaudeProduto planoSaudeProduto = new PlanoSaudeProduto();

    planoSaudeProduto.setId(idPlanoSaude);
    planoSaudeProduto.setIdProcessadora(idProcessadora);
    planoSaudeProduto.setIdInstituicao(idInstituicao);
    planoSaudeProduto.setIdProdInstituicao(idProdInstituicao);

    return new ResponseEntity<>(service.getDeglosesPlanoSaude(planoSaudeProduto), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Edita as desgloses de um plano de saúde",
      response = DeglosePlanoSaudeVO.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "desglose/edit",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PLANO_SAUDE"})
  public ResponseEntity<?> editDeglosesPlanoSaudeCodigoContabil(
      @RequestBody DeglosePlanoSaudeVO model) {
    return new ResponseEntity<>(service.editDesglosePlanoPlanoSaude(model), HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Buscar proximo dia inicial do periodo de renegociacao de divida de um produto de credito")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/get-next-dia-inicial/renegociacao-divida/{idProcessadora}/{idInstituicao}/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ADD_CONGIF_RENEGOC_DIV_PROD"})
  public Integer getNextDiaInicialPeriodoRenegocDivida(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao) {
    return service.getNextDiaInicialPeriodoRenegocDivida(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  @ApiOperation(value = "Adicionar configuracao de renegociacao de divida de um produto de credito")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/add-config/renegociacao-divida",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ADD_CONGIF_RENEGOC_DIV_PROD"})
  public void addConfigRenegocDividaProduto(@RequestBody AddRenegociacaoDividaProdutoVO model) {
    SecurityUser user = getAuthenticatedUser(request, em);
    service.addConfigRenegocDividaProduto(model, user.getIdUsuario());
  }

  @ApiOperation(value = "Adicionar plano de saúde um produto de credito")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{idProcessadora}/{idInstituicao}/{idProdInstituicao}/plano-saude",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PLANO_SAUDE"})
  public ResponseEntity<String> addPlanoSaude(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao,
      @RequestBody PlanoSaudeVO model) {
    SecurityUser user = getAuthenticatedUser(request, em);
    return service.addPlanoSaude(
        idProcessadora, idInstituicao, idProdInstituicao, model, user.getIdUsuario());
  }

  @ApiOperation(
      value = "Buscar taxas de renegociacao de divida de um produto de credito",
      response = TaxasRenegociacaoDividaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/get-config/taxa-renegociacao-divida/{idRenegociacao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CONGIF_RENEGOC_DIV_PROD"})
  public ResponseEntity<?> getTaxasRenegocDividaProduto(@PathVariable Integer idRenegociacao) {
    List<TaxasRenegociacaoDividaProduto> response =
        service.getTaxasRenegocDividaProduto(idRenegociacao);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Adicionar taxa de renegociacao de divida de um produto de credito",
      response = TaxasRenegociacaoDividaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/add-config/taxa-renegociacao-divida",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ADD_CONGIF_RENEGOC_DIV_PROD"})
  public ResponseEntity<?> addTaxaRenegocDividaProduto(
      @RequestBody TaxasRenegociacaoDividaProdutoVO model) {
    TaxasRenegociacaoDividaProduto response = service.addTaxaRenegocDividaProduto(model);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Editar taxa de renegociacao de divida de um produto de credito",
      response = TaxasRenegociacaoDividaProduto.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/edit-config/taxa-renegociacao-divida",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_EDIT_CONGIF_RENEGOC_DIV_PROD"})
  public ResponseEntity<?> editTaxaRenegocDividaProduto(
      @RequestBody TaxasRenegociacaoDividaProdutoVO model) {
    TaxasRenegociacaoDividaProduto response = service.editTaxaRenegocDividaProduto(model);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os produtos instituição pos-pagos",
      response = ResponseProdutosInstituicao.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-all-pos/pf-and-pj/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> getAllPosPfAndPj(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {
    try {
      return service.getAllPosPfAndPj(idProcessadora, idInstituicao);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value = "Buscar os produtos convênios b2b pela instituição do usuário logado",
      response = ProdutoInstituicaoResponse.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "buscar-convenios/usuario-logado",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ProdutoInstituicaoResponse> getProdInstCOnfigByIdProdInstituicao() {

    SecurityUser user = getAuthenticatedUser(request, em);
    return service.findConvenioByUsuarioLogado(user);
  }

  @ApiOperation(
      value = "Buscar todos produtos da instituição que não são b2b",
      response = ResponseProdutosInstituicao.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-all-not-b2b/by-hierarquia-tipo-pessoa-filtro",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> findAllNotB2bByFiltros(
      @RequestBody BuscaProdutoNaoB2b model) {
    try {
      return service.findAllNotB2bByFiltros(model, false);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value = "Buscar os produtos pela hierarquia do usuario logado e filtros",
      response = GetDetalhesProdInstituicao.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/find-by-filter/{first}/{max}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_BY_USUARIO_LOGADO"})
  public List<GetDetalhesProdInstituicao> findByUserAndFilter(
      @RequestBody BuscarProdutoByFilter model,
      @PathVariable Integer first,
      @PathVariable Integer max) {

    SecurityUser user = getAuthenticatedUser(request, em);

    return service.findByFilterPaginate(user, model, first, max);
  }

  @ApiOperation(
      value = "Contar os produtos pela hierarquia do usuario logado e filtros",
      response = Integer.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/count-by-filter",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODS_INSTITUICAO_BY_USUARIO_LOGADO"})
  public Integer countByUserAndFilter(@RequestBody BuscarProdutoByFilter model) {

    SecurityUser user = getAuthenticatedUser(request, em);

    return service.countByFilterPaginate(user, model);
  }

  @ApiOperation(
      value = "Buscar todos produtos da instituição que não são b2b conta e vincular conta",
      response = ResponseProdutosInstituicao.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-all-not-b2b/by-hierarquia-tipo-pessoa-conta-filtro",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponseProdutosInstituicao> findAllNotB2bByContaFiltros(
      @RequestBody BuscaProdutoNaoB2b model) {
    try {
      return service.findAllNotB2bFiltroObj(model);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível recuperar os produtos instituição.");
    }
  }

  @ApiOperation(
      value = "Buscar todos produtos e as quantidades de solicitacoes de limite pagamento",
      response = Object.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-produtos-solicitacoes/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ProdutoInstituicao> buscarProdutosSolicitacoes(@PathVariable Integer idInstituicao) {
    try {
      SecurityUser user = getAuthenticatedUser(request, em);
      return service.listarProdutosSolicitacoes(idInstituicao, user);
    } catch (Exception e) {
      throw new GenericServiceException(
          ConstantesErro.NAO_FOI_POSSIVEL_RECUPERAR_DADOS_INSTITUICAO.getMensagem(),
          HttpStatus.NOT_FOUND);
    }
  }

  @ApiOperation(
      value = "Buscar as contas cadastradas com seus produtos agrupados por tipo_login",
      response = Object.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-produtos-login-cadastrados/{idInstituicao}/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ContaProdutoLoginResponseVO> buscarProdutosELoginsCadastrados(
      @PathVariable Integer idInstituicao, @PathVariable String documento) {
    try {
      SecurityUser user = getAuthenticatedUser(request, em);
      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
          user, Constantes.ID_PROCESSADORA_ITS_PAY, idInstituicao, null, null, null);
      return service.listarProdutosELoginsCadastrados(idInstituicao, documento, user);
    } catch (Exception e) {
      throw new GenericServiceException(
          ConstantesErro.PRD_INFORMACOES_LOGIN_CONTAS_NAO_ENCONTRADAS.format(documento),
          HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }

  @ApiOperation(value = "alterar status de produto instituicao")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/altera-status-produto",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_PRODUTO"})
  public ResponseEntity<?> alterarStatusProduto(@RequestBody AlteraStatusRequest model) {

    SecurityUser user = getAuthenticatedUser(request, em);

    service.alteraStatusProduto(user, model);

    return ResponseEntity.ok().build();
  }
}
