package br.com.json.bean.cadastral;

import br.com.json.bean.suporte.ValorCargaProdutoInstituicao;
import br.com.sinergico.annotation.Documento;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotBlank;
import org.hibernate.validator.constraints.NotEmpty;

@ApiModel(value = "CadastrarContaPagamentoPessoa")
@Getter
@Setter
public class CadastrarContaPagamentoPessoaRequest implements Serializable {

  private static final long serialVersionUID = -2757347860710635312L;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer tipoPessoa;

  private Integer idInstituicao;

  private Integer idProcessadora;

  private Boolean isIntegracao;

  private Long idPessoaOrigem;
  private Long idContaOrigem;

  @Documento
  @NotEmpty
  @ApiModelProperty(required = true)
  private String documento;

  private String documentoAcesso;

  private Long grupoAcesso;

  private TipoPortadorLoginEnum tipoLogin;

  private Integer idUsuarioInclusao;

  private Integer idRegional;

  private Integer idFilial;

  private Integer idPontoDeRelacionamento;

  private String nomeCompleto;

  private String naturalidade;

  private String nacionalidade;

  private String parcelamento;

  private String rg;

  private String rgOrgaoEmissor;

  private String rgUfOrgaoEmissor;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private Date rgDataEmissao;

  private Boolean estrangeiro;

  private String passaporte;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private Date dataNascimento;

  private String nomePai;

  private String nomeMae;

  @Email private String email;

  private String emailProfissional;

  private Integer dddTelefoneResidencial;

  private Integer telefoneResidencial;

  private Integer dddTelefoneComercial;

  private Integer telefoneComercial;

  private Integer dddTelefoneCelular;

  private Integer telefoneCelular;

  private String razaoSocial;

  @Size(max = 80)
  private String nomeFantasia;

  private String inscricaoEstadual;

  private String inscricaoMunicipal;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private Date dataFundacao;

  private String atividadePrincipal;

  private String formaDeConstituicao;

  private Integer idSetorFilial;

  private String matricula;

  private Integer idSexo;

  private Integer idEstadoCivil;

  private Integer enderecoEntrega;

  private Integer idBanco;

  private Integer idAgencia;

  private String contaBancariaX;

  private Integer contaBancaria;

  private Integer tipoContaBancaria;

  private Integer tipoChavePix;

  private String chavePix;

  private Integer numeroProposta;

  private Integer cadastroOrigem;

  private Integer idParceiroAcumulo;

  private Boolean semEmail;

  // Infos de PJ
  private BigDecimal faturamentoMedioMensal;
  private Integer quantidadeFuncionarios;
  private String nomeRepresentanteLegal;
  private String cpfRepresentanteLegal;
  private String rgRepresentanteLegal;
  private String logradouroRepresentanteLegal;
  private String numeroRepresentanteLegal;
  private String bairroRepresentanteLegal;
  private String cidadeRepresentanteLegal;
  private String ufRepresentanteLegal;
  private String cepRepresentanteLegal;
  private Integer dddCelularRepresentanteLegal;
  private Integer telefonteCelularRepresentanteLegal;
  private Integer dddFixoRepresentanteLegal;
  private Integer telefoneFixoRepresentanteLegal;
  private String nomeMaeRepresentanteLegal;
  private String dtNascimentoRepresentanteLegal;
  private String emailRepresentanteLegal;

  @NotBlank
  @Size(max = 24)
  private String nomeEmbossado;

  private EnderecosPessoaRequest enderecosPessoaRequest;

  @NotEmpty private List<ValorCargaProdutoInstituicao> valoresCargasProdutos;

  private String nomeCartaoImpresso;

  private Boolean deficienteVisual;

  private String numeroAgenciaBRB;

  private String nomeAgenciaBRB;

  private String cpfResponsavel;

  private String nomeResponsavel;

  private String nomeEscola;

  private String cidadeEmbossing;

  private String ufEmbossing;

  // IMEI
  private String deviceId;
  private Integer sistemaOperacional;
  private String architectureInfo;
  private String model;
  private String platformName;
  private String plataformVersion;

  public CadastrarContaPagamentoPessoaRequest() {
    super();
  }
}
