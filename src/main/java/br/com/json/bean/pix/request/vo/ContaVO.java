package br.com.json.bean.pix.request.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Getter
@Setter
@ToString
public class ContaVO {

  private String ispbParticipante;
  private String agencia;

  @NotNull
  @Min(value = 0)
  @JsonProperty("numero")
  private String idConta;

  private String tipoId; // 1 - Corrente 2 - Salario 3 - Poupança 4 - Transacional
  private String inscricaoNacional;
  private String nomeInstituicao;
  private String nome;

  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  private String dataAbertura;
}
