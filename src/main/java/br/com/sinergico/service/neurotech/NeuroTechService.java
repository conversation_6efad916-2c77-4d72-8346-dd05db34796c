package br.com.sinergico.service.neurotech;

import br.com.exceptions.GenericServiceException;
import br.com.itspay.neurotech.api.ClienteNeurotechAPI;
import br.com.itspay.neurotech.api.enums.AmbienteNeurotech;
import br.com.itspay.neurotech.api.model.RequestPolitica1;
import br.com.itspay.neurotech.api.model.RequestPolitica2;
import br.com.itspay.neurotech.api.util.ItsPayNeurotechConstantes;
import br.com.itspay.neurotech.cliente.ambiente.unificado.RetornoFluxoComParametros;
import br.com.sinergico.service.UtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NeuroTechService {

  @Autowired UtilService utilService;

  public ClienteNeurotechAPI initClientNeuroTech() {
    ClienteNeurotechAPI clienteNeurotechAPI;
    try {
      if (utilService.isAmbienteHomologacao()) {
        clienteNeurotechAPI =
            new ClienteNeurotechAPI(
                ItsPayNeurotechConstantes.CODIGO_ASSOCIADO,
                ItsPayNeurotechConstantes.CODIGO_FILIAL,
                ItsPayNeurotechConstantes.SENHA_HOMOLOG,
                AmbienteNeurotech.HOMOLOGACAO);
        return clienteNeurotechAPI;
      } else {
        if (utilService.isAmbienteProducao()) {
          clienteNeurotechAPI =
              new ClienteNeurotechAPI(
                  ItsPayNeurotechConstantes.CODIGO_ASSOCIADO,
                  ItsPayNeurotechConstantes.CODIGO_FILIAL,
                  ItsPayNeurotechConstantes.SENHA_PROD,
                  AmbienteNeurotech.PRODUCAO);
          return clienteNeurotechAPI;
        } else {
          System.out.println("Variável de ambiente não encontrada");
          throw new GenericServiceException("Variável de ambiente não encontrada");
        }
      }
    } catch (Exception e) {
      System.out.println("Não foi possível inicializar o cliente NeuroTech: " + e.getMessage());
      throw new GenericServiceException(
          "Não foi possível inicializar o cliente NeuroTech: " + e.getMessage());
    }
  }

  public RetornoFluxoComParametros executePolitica1(RequestPolitica1 requestPolitica1) {

    ClienteNeurotechAPI api = initClientNeuroTech();

    return api.executaFluxo1(requestPolitica1);
  }

  public RetornoFluxoComParametros executePolitica2(RequestPolitica2 requestPolitica2) {

    ClienteNeurotechAPI api = initClientNeuroTech();

    return api.executaFluxo2(requestPolitica2);
  }
}
