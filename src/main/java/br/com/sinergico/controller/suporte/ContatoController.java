package br.com.sinergico.controller.suporte;

import br.com.entity.suporte.Contato;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.suporte.CadastrarContato;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.ContatoService;
import br.com.sinergico.vo.CentralAtendimentoVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/contato")
public class ContatoController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private ContatoService contatoService;

  @ApiOperation(value = "Serviço responsável por Inativar um contato")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/inativar/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESATIVAR_CONTATO"})
  public ResponseEntity<Map<String, Object>> inativarContato(@PathVariable Long id) {

    SecurityUser user = getAuthenticatedUser(request, em);

    contatoService.inativarContato(id, user.getIdUsuario());
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Contato inativado com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por ativar um contato")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/ativar/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATIVAR_CONTATO"})
  public ResponseEntity<Map<String, Object>> ativar(@PathVariable Long id) {

    SecurityUser user = getAuthenticatedUser(request, em);

    contatoService.ativarContato(id, user.getIdUsuario());
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Contato ativado com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por alterar um contato")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{id}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_CONTATO"})
  public ResponseEntity<Map<String, Object>> update(
      @RequestBody @Valid CadastrarContato model, @PathVariable Long id, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na validação da Alteração de contato.", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);

    contatoService.update(user, model, id);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Contato alterado com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por criar um contato")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_CONTATO"})
  public ResponseEntity<Map<String, Object>> create(
      @RequestBody @Valid CadastrarContato model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na validação da Criação de contato.", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);

    contatoService.cadastrarContato(user, model);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Contato cadastrado com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Buscar contatos de uma instituição", response = Contato.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-contatos-instituicao/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<Contato> getContatosInstituicao(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {

    List<Contato> result = contatoService.getContatosInstituicao(idProcessadora, idInstituicao);

    return result;
  }

  @ApiOperation(value = "Buscar fale conosco de uma instituição", response = Contato.class)
  @RequestMapping(
      value = "/buscar-fale-conosco/{idProcessadora}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public CentralAtendimentoVO buscarFaleConosco(
      @PathVariable Integer idProcessadora, @PathVariable Integer idInstituicao) {

    return contatoService.buscarFaleConoscoInstituicao(idProcessadora, idInstituicao);
  }
}
