package br.com.sinergico.controller.cadastral;

import static org.springframework.http.ResponseEntity.ok;

import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.LogAlteracaoPinCredencial;
import br.com.entity.suporte.LogRegistroAlterarStatus;
import br.com.entity.suporte.TipoStatus;
import br.com.exceptions.BusinessException;
import br.com.exceptions.GenericRuntimeException;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.AvisarPerdaOuRouboRequest;
import br.com.json.bean.cadastral.CredenciaisDisponiveisDTO;
import br.com.json.bean.cadastral.CredencialExternaTO;
import br.com.json.bean.cadastral.CredencialJcard;
import br.com.json.bean.cadastral.CredencialLoteResponse;
import br.com.json.bean.cadastral.CredencialParaRecadastramentoPin;
import br.com.json.bean.cadastral.CredencialStatus;
import br.com.json.bean.cadastral.CredencialTransferencia;
import br.com.json.bean.cadastral.DetalheCredencialLoteVo;
import br.com.json.bean.cadastral.GetContadorSenhaCard;
import br.com.json.bean.cadastral.GetCredenciaisDisponiveisVO;
import br.com.json.bean.cadastral.GetCredenciaisResponse;
import br.com.json.bean.cadastral.GetCredencial;
import br.com.json.bean.cadastral.GetInfoPortadorCredencialRequest;
import br.com.json.bean.cadastral.HistoricoCartaoRequest;
import br.com.json.bean.cadastral.PortadorCorporativo;
import br.com.json.bean.cadastral.PortadorCorporativoRequest;
import br.com.json.bean.cadastral.PortadorCredencial;
import br.com.json.bean.cadastral.RecadastrarPinRequest;
import br.com.json.bean.cadastral.SegundaViaCredencialRequest;
import br.com.json.bean.cadastral.SegundaViaCredencialResponse;
import br.com.json.bean.cadastral.SenhaCartaoAppRequest;
import br.com.json.bean.cadastral.SenhaCartaoViaSmsRequest;
import br.com.json.bean.cadastral.TrocarEstadoCredencialJcard;
import br.com.json.bean.cadastral.TrocarEstadoCredencialRequest;
import br.com.json.bean.cadastral.TrocarEstadoNFCCredencialRequest;
import br.com.json.bean.cadastral.TrocarPinRequest;
import br.com.json.bean.cadastral.ValidarPinRequest;
import br.com.json.bean.suporte.AlterarStatusCredencialResponseVO;
import br.com.json.bean.suporte.GetExtratoCredencial;
import br.com.json.bean.suporte.NovoAcessoLogPan;
import br.com.json.bean.transacional.GetExtratoCredencialInmais;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.security.ResgateSenhaService;
import br.com.sinergico.security.SecurityResgateSenha;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.security.TokenHandlerResgateSenha;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.LogAlteracaoPinCredencialService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.CredencialUtil;
import br.com.sinergico.vo.SegundaViaCredencialRequestVO;
import br.com.sinergico.vo.vcn.DadosSensiveisCredencial;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.InputStream;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/portador/credencial")
public class CredencialController extends UtilController {

  @Autowired private CredencialService credencialService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private LogAlteracaoPinCredencialService logAlteracaoPinCredencialService;

  @Autowired private CredencialFacade credencialFacade;

  @Autowired private CredencialUtil credencialUtil;

  @Autowired private PessoaService pessoaService;

  @Autowired private EmailService mail;
  @Autowired private ResgateSenhaService resgateSenhaService;
  private TokenHandlerResgateSenha tokenHandler;

  @Autowired private EntityManager em;

  @Autowired private HttpServletRequest request;

  @Autowired private TravaServicosService travaServicosService;

  private static Logger log = LoggerFactory.getLogger(CredencialController.class);

  @ApiOperation(
      value = "Buscar todas as credenciais de um Portador",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista com as credenciais do portador.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/{documento}/pessoa/{tipoPessoa}/processadora/{idProc}/instituicao/{idInst}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_PORTADOR"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciais(
      @PathVariable String documento,
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer tipoPessoa) {

    GetCredenciaisResponse response =
        credencialService.getCredenciais(documento, idProc, idInst, tipoPessoa);
    credencialUtil.setSaldoLimiteNasCredenciais(response);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar todas as credenciais de um portador em um grupo de produtos")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/grupo/listar",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, GetCredenciaisResponse>> getCredenciaisPortadorGrupo() {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    HashMap<String, GetCredenciaisResponse> map = new HashMap<>();
    GetCredenciaisResponse response =
        credencialService.getCredenciaisGrupo(
            userPortador.getCpf(),
            userPortador.getIdProcessadora(),
            userPortador.getIdInstituicao(),
            userPortador.getIdTipoPessoa(),
            userPortador);
    map.put("dados", response);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar todas as credencialConta de um portador em um grupo de produtos")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = {
        "/grupo/credencial-conta/listar",
        "/grupo/credencial-conta/listar/{virtualOuFisica}"
      },
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, Object>> getCredenciaisContaPortadorGrupo(
      @PathVariable Optional<Boolean> virtualOuFisica) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    HashMap<String, Object> map = new HashMap<>();
    map.put(
        "credenciaisConta",
        credencialService.getCredenciaisContaGrupo(userPortador, virtualOuFisica));

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar todas as credenciais desbloqueadas de um Portador",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista com as credenciais do portador.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value =
          "/{documento}/pessoa/{tipoPessoa}/processadora/{idProc}/instituicao/{idInst}/desbloqueadas",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_DESBLOQUEADAS_PORTADOR"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciaisDesbloqueadas(
      @PathVariable String documento,
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer tipoPessoa) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    GetCredenciaisResponse response =
        credencialService.getCredenciaisDesbloqueadas(
            documento, idProc, idInst, tipoPessoa, userPortador);
    credencialUtil.setSaldoLimiteNasCredenciais(response);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar todas as credenciais desbloqueadas de um Portador",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista com as credenciais do portador.",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value =
          "/{documento}/pessoa/{tipoPessoa}/processadora/{idProc}/instituicao/{idInst}/disponiveis",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_DESBLOQUEADAS_PORTADOR"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciaisDisponiveis(
      @PathVariable String documento,
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer tipoPessoa) {

    GetCredenciaisResponse response =
        callFunctionPortadorOrFunctionCorporativo(
            request,
            new CredenciaisDisponiveisDTO(documento, idProc, idInst, tipoPessoa),
            credencialService::getCredenciaisDisponiveis,
            credencialService::getCorporativoCredenciaisDisponiveis,
            em);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Buscar todas as credenciais desbloqueadas de um Portador, mais as credenciais bloqueadas por"
              + " perda e roubo",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista " + "com as credenciais do portador.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value =
          "/{documento}/pessoa/{tipoPessoa}/processadora/{idProc}/instituicao/{idInst}/disponiveis-perda-roubo",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_DESBLOQUEADAS_PORTADOR"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciaisDisponiveisEPerdaRoubo(
      @PathVariable String documento,
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer tipoPessoa) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    GetCredenciaisResponse response =
        credencialService.getCredenciaisDisponiveisEPerdaRoubo(
            documento, idProc, idInst, tipoPessoa, userPortador);
    credencialUtil.setSaldoLimiteNasCredenciais(response);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar quais produtos devem ser exibidos por instituicao",
      response = Object.class,
      notes = "Retorna uma lista com os produtos a serem exibidas.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/exibir/{idInst}/{subType}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Object> getCredenciaisExibir(
      @PathVariable Integer idInst, @PathVariable boolean subType) {

    List<Integer> response = new ArrayList<>();

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar todas as credenciais disponíveis de um Portador",
      response = GetCredenciaisResponse.class,
      notes =
          "Serviço responsável "
              + "por buscar as credenciais disponíveis de um portador pelo documento, ID da Processadora, ID da Instituição, TipoPessoa e pelo Site Acessado."
              + " Os Tokens de autorização devem ser inseridos de acordo com seu tipo de login.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar/disponiveis",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_DESBLOQUEADAS_PORTADOR"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciaisDisponiveisParametrizado(
      @RequestBody GetCredenciaisDisponiveisVO model) {

    GetCredenciaisResponse response =
        credencialService.getCredenciaisDisponiveisParametrizado(
            model.getDocumento(),
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getTipoPessoa(),
            model.getAcessoSite());
    credencialUtil.setSaldoLimiteNasCredenciais(response);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar todas as credenciais virtuais de uma Conta",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista com as credenciais virtuais de uma conta.",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/virtual/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_VIRTUAIS_CONTA"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciaisVirtuaisConta(
      @PathVariable Long idConta) {

    GetCredenciaisResponse response = credencialService.getCredenciaisVituaisConta(idConta);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar informações do Portador de uma Credencial",
      response = PortadorCredencial.class,
      notes = "Retorna informacoes do Portador de uma credencial.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/info-portador",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PORTADOR_DA_CREDENCIAL"})
  public ResponseEntity<PortadorCredencial> getInfoPortadorCredencial(
      @RequestBody @Valid GetInfoPortadorCredencialRequest infoPortador,
      HttpServletRequest httpServletRequest) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(httpServletRequest, em);

    PortadorCredencial pc =
        credencialService.getInfoPortadorCredencialPortador(
            infoPortador.getPanHash(), userPortador);

    return new ResponseEntity<>(pc, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os detalhes da credencial de um Portador",
      response = GetCredencial.class,
      notes = "Retorna os detalhes da credencial do portador.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/{idCredencial}/detalhes",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DETALHES_CREDENCIAL_PORTADOR"})
  public ResponseEntity<GetCredencial> getDetalhesCredencial(@PathVariable Long idCredencial) {

    GetCredencial c = credencialService.detalhe(idCredencial);
    credencialUtil.setSaldoLimiteNaCredencial(c);
    return new ResponseEntity<>(c, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Buscar o extrato da credencial de um Portador por periodos dos últimos
  // dias", response = GetExtratoCredencial.class, notes = "Buscar o extrato da credencial de um
  // Portador", responseContainer = "List")
  //	@ApiImplicitParams({ @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //			@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key") })
  //	@RequestMapping(value = "/{idCredencial}/extrato/periodo/{periodo}", method =
  // RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_BUSCAR_EXTRATO_CREDENCIAL_PORTADOR_ULTIMOS_DIAS"})
  //	public ResponseEntity<List<GetExtratoCredencial>> getExtratoCredencialPorPeriodo(@PathVariable
  // Long idCredencial,
  //			@PathVariable Integer periodo) {
  //
  //		List<GetExtratoCredencial> extrato = credencialService.getExtrato(idCredencial, periodo);
  //
  //		return new ResponseEntity<>(extrato, HttpStatus.OK);
  //	}

  @ApiOperation(
      value = "Buscar o extrato da conta por data",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da conta",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "inmais/{idCredencial}/extrato/acumulo/ano_mes/{anoMes}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_ANO_MES"})
  public ResponseEntity<List<GetExtratoCredencialInmais>> getInmaisExtratoAcumuloAndContaPorAnoMes(
      @PathVariable Long idCredencial,
      @PathVariable Integer anoMes,
      @RequestParam("maisRecente") Boolean maisRecente) {

    SecurityUserPortador user = getAuthenticatedPortador(request, em);
    if (maisRecente == null) {
      maisRecente = false;
    }
    List<GetExtratoCredencialInmais> extrato =
        credencialFacade.getExtratoInmaisAcumuloByAnoMesAndStatusFixo(
            user, idCredencial, anoMes, maisRecente);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o extrato da conta por data",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da conta",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "inmais/{idCredencial}/extrato/ano_mes/{anoMes}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_ANO_MES"})
  public ResponseEntity<List<GetExtratoCredencialInmais>> getInmaisExtratoContaPorAnoMes(
      @PathVariable Long idCredencial,
      @PathVariable Integer anoMes,
      @RequestParam(value = "maisRecente", required = false) Boolean maisRecente) {

    SecurityUserPortador user = getAuthenticatedPortador(request, em);
    if (maisRecente == null) {
      maisRecente = false;
    }
    List<GetExtratoCredencialInmais> extrato =
        credencialFacade.getExtratoInmaisByAnoMesAndStatusFixo(
            user, idCredencial, anoMes, maisRecente);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o extrato da conta por data",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da conta",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/{idCredencial}/extrato/ano_mes/{anoMes}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_ANO_MES"})
  public ResponseEntity<List<GetExtratoCredencialInmais>> getExtratoContaPorAnoMes(
      @PathVariable Long idCredencial,
      @PathVariable Integer anoMes,
      @RequestParam(value = "maisRecente", required = false) Boolean maisRecente) {

    SecurityUserPortador user = getAuthenticatedPortador(request, em);
    maisRecente = maisRecente == null ? false : maisRecente;
    List<GetExtratoCredencialInmais> extrato =
        credencialFacade.getExtratoInmaisByAnoMes(user, idCredencial, anoMes, maisRecente);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o extrato da credencial de um Portador por data",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da credencial de um Portador",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/{idCredencial}/extrato/data_inicial/{dataInicial}/data_final/{dataFinal}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CREDENCIAL_PORTADOR_DATA"})
  public ResponseEntity<List<GetExtratoCredencial>> getExtratoCredencialPorDatas(
      @PathVariable Long idCredencial,
      @PathVariable Date dataInicial,
      @PathVariable Date dataFinal) {

    List<GetExtratoCredencial> extrato =
        credencialService.getExtrato(idCredencial, dataInicial, dataFinal, null);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Validar a senha da credencial de um Portador", response = Boolean.class,
  // notes = "Validar a senha da credencial de um Portador")
  //	@ApiImplicitParams({ @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //			@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key") })
  //	@RequestMapping(value = "/validar-pin", method = RequestMethod.POST, produces =
  // MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_VALIDAR_SENHA_CREDENCIAL_PORTADOR"})
  //	public ResponseEntity<Boolean> validarPin(@RequestBody @Valid ValidarPinRequest req,
  // BindingResult result) {
  //
  //		if (result.hasErrors()) {
  //			throw new InvalidRequestException("Validação na validação de pin.", result);
  //
  //		}
  //
  //		Boolean valido = credencialService.validarPin(req.getPin(),
  // req.getIdCredencial(),getTokenJWT(request));
  //
  //		return new ResponseEntity<>(valido, HttpStatus.OK);
  //	}

  @ApiOperation(
      value = "Validar a senha da credencial de um Portador",
      response = Boolean.class,
      notes = "Validar a senha da credencial de um Portador")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/validar-senha-cartao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_VALIDAR_SENHA_CREDENCIAL_PORTADOR"})
  public ResponseEntity<Map<String, Object>> validarPinBloqueado(
      @RequestBody @Valid ValidarPinRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação na validação de pin.", result);
    }

    Map<String, Object> map =
        credencialService.validarSenha(req.getPin(), req.getIdCredencial(), getTokenJWT(request));

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Gerar uma segunda via da credencial de um Portador",
      response = SegundaViaCredencialResponse.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/segunda-via",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GERAR_SEGUNDA_VIA_CREDENCIAL_PORTADOR"})
  public ResponseEntity<HashMap<String, SegundaViaCredencialResponse>> pedirSegundaVia(
      @RequestBody @Valid SegundaViaCredencialRequest req,
      BindingResult result,
      HttpServletRequest httpServletRequest) {

    HashMap<String, SegundaViaCredencialResponse> map = new HashMap<>();
    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação na entrada para solicitar segunda via.", result);
    }

    SegundaViaCredencialResponse response = new SegundaViaCredencialResponse();
    response.setValido(false);

    SegundaViaCredencialRequestVO segundaViaCredencialRequestVO =
        new SegundaViaCredencialRequestVO();

    segundaViaCredencialRequestVO.setIdCredencial(req.getIdCredencial());
    segundaViaCredencialRequestVO.setCobrarTarifa(req.getCobrarTarifa());
    segundaViaCredencialRequestVO.setDeveForcarCobranca(req.getDeveForcarCobranca());
    segundaViaCredencialRequestVO.setIpOrigem(ipOrigem);
    segundaViaCredencialRequestVO.setIdConta(req.getIdConta());

    try {
      Boolean valido =
          callFunctionUserOrFunctionPortador(
              httpServletRequest,
              segundaViaCredencialRequestVO,
              credencialService::solicitarSegundaViaUser,
              credencialService::solicitarSegundaViaPortador,
              em);

      response.setValido(valido);
      response.setCodErro(0);
      response.setMensagem("OK!");

      map.put("dados", response);
      return new ResponseEntity<>(map, HttpStatus.OK);
    } catch (BusinessException e) {

      response.setMensagem(e.getMessage());
      response.setCodErro(e.getCodErro());
      map.put("dados", response);

      return new ResponseEntity<>(map, HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }

  @ApiOperation(
      value = "Solicita credencial para o portador",
      response = SegundaViaCredencialRequest.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/solicitar-credencial",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GERAR_SEGUNDA_VIA_CREDENCIAL_PORTADOR"})
  public ResponseEntity<SegundaViaCredencialResponse> solicitarCredencial(
      @RequestBody @Valid SegundaViaCredencialRequest req,
      BindingResult result,
      HttpServletRequest httpServletRequest) {
    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação na entrada para solicitar segunda via.", result);
    }

    SecurityUserPortador user = getAuthenticatedPortador(request, em);

    SegundaViaCredencialResponse response = new SegundaViaCredencialResponse();
    response.setValido(false);

    if (user != null) {
      try {
        response.setValido(
            credencialService.solicitarCredencial(req, user, ipOrigem, httpServletRequest));
        response.setCodErro(0);
        response.setMensagem("OK!");
      } catch (BusinessException e) {
        response.setMensagem(e.getMessage());
        response.setCodErro(e.getCodErro());
        return new ResponseEntity<>(response, HttpStatus.UNPROCESSABLE_ENTITY);
      }
      try {
        Pessoa pessoa =
            pessoaService.findById(credencialService.findById(req.getIdCredencial()).getIdPessoa());
        mail.sendEmailInmaisSegundaVia(pessoa.getEmail(), pessoa.getNomeCompleto());
      } catch (Exception e) {
        throw new GenericServiceException("Falha ao tentar enviar o email" + e);
      }
    }
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Trocar a senha da credencial de um Portador",
      response = Boolean.class,
      notes = "Trocar a senha da credencial de um Portador")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/trocar-pin",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TROCAR_SENHA_CREDENCIAL_PORTADOR"})
  public ResponseEntity<Boolean> trocarPin(
      @RequestBody @Valid TrocarPinRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação de Troca de pin.", result);
    }

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (GenericRuntimeException e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.TROCAR_SENHA_CARTAO);

    Boolean alterado =
        credencialService.trocarPin(
            req.getSenha(),
            req.getNovaSenha(),
            req.getIdCredencial(),
            user != null
                ? user.getIdUsuario()
                : Objects.requireNonNull(userPortador).getIdLogin().intValue(),
            getTokenJWT(request));

    return new ResponseEntity<>(alterado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Vindo do Portador Recadastrar a senha da credencial de um Portador",
      response = Boolean.class,
      notes =
          "Vindo do Portador Recadastrar a senha da credencial de um Portador. Suporta validação por PIN (senhaUsuario/senhaCartao) ou por token (chaveExterna + token)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/portador/recadastrar-pin",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_RECADASTRAR_SENHA_CREDENCIAL_ORIGEM_PORTADOR"})
  public ResponseEntity<HashMap<String, Object>> recadastrarPinOrigemPortador(
      @RequestBody @Valid RecadastrarPinRequest req, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação de Troca de pin.", result);
    }

    SecurityUserPortador user = getAuthenticatedPortador(request, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_SENHA_CARTAO);

    Boolean alterado;
    if (req.getSenhaUsuario() != null && !req.getSenhaUsuario().isEmpty()) {
      alterado = credencialFacade.recadastraPin(req, user, request);
    } else if (req.getChaveExterna() != null && req.getToken() != null) {
      alterado = credencialFacade.recadastraPin(req, user, request);
    }  else {
      alterado = credencialFacade.recadastraPinComPinCredencial(req, user, request);
    }

    if (alterado) {
      map.put("msg", "Senha recadastrada com sucesso!");
      return new ResponseEntity<>(map, HttpStatus.OK);
    } else {
      map.put("msg", "A senha não pôde ser recadastrada!");
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
  }

  @ApiOperation(
      value = "Buscar quantidade de credenciais por lote de emissão. Para definição de senha",
      response = Boolean.class,
      notes = "Para enviar a quantidade de credenciais por lote, para definição de senha.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/contar-quantidade-lote/{idProcessadora}/{idInstituicao}/{idRegional}/{idFilial}/{idPontoRelacionamento}/{loteEmissao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_RECADASTRAR_SENHA_EM_LOTE"})
  public ResponseEntity<HashMap<String, Object>> contarQuantidadePorLoteEmissao(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idRegional,
      @PathVariable Integer idFilial,
      @PathVariable Integer idPontoRelacionamento,
      @PathVariable Integer loteEmissao) {
    HashMap<String, Object> map = new HashMap<>();

    Long contador =
        credencialService.getCountParaDefinicaoSenha(
            loteEmissao,
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoRelacionamento);

    map.put("contador", contador);
    map.put("loteEmissao", loteEmissao);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Redefinição de Senha por lote de Emissão",
      response = Boolean.class,
      notes = "Envia-se o lote e o seviço recadastra os pins de todas as credenciais encontradas.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/recadastrar-pin-lote/{idProcessadora}/{idInstituicao}/{idRegional}/{idFilial}/{idPontoRelacionamento}/{loteEmissao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_RECADASTRAR_SENHA_EM_LOTE"})
  public ResponseEntity<HashMap<String, Object>> recadastrarPinsPorLote(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idRegional,
      @PathVariable Integer idFilial,
      @PathVariable Integer idPontoRelacionamento,
      @PathVariable Integer loteEmissao) {
    HashMap<String, Object> map = new HashMap<>();
    SecurityUser user = getAuthenticatedUser(request, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_SENHA_CARTAO);

    List<CredencialParaRecadastramentoPin> lista =
        credencialService.getCredenciaisParaRedefinicaoSenha(
            loteEmissao,
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoRelacionamento);

    String listaCredenciaisNaoProcessadas = "";
    long processadosComSucesso = 0;
    for (CredencialParaRecadastramentoPin cred : lista) {
      Long idCredencial = cred.getIdCredencial();
      String documento = cred.getDocumento();

      String senha = documento.substring(0, 4);
      String novaSenha = "";
      try {
        novaSenha = credencialService.getSha(senha + getTokenJWT(request));
      } catch (Exception e) {
        e.printStackTrace();
      }

      boolean processada =
          credencialService.recadastraPinParaLote(
              novaSenha, idCredencial, user.getIdUsuario(), getTokenJWT(request));

      if (!processada) {
        listaCredenciaisNaoProcessadas += idCredencial + ",";
      } else {
        processadosComSucesso++;
      }
    }

    if (listaCredenciaisNaoProcessadas.length() > 1) {
      listaCredenciaisNaoProcessadas.substring(0, listaCredenciaisNaoProcessadas.length() - 2);
    }

    String mensagemRetorno =
        processadosComSucesso
            + " de "
            + lista.size()
            + " processados com sucesso para o lote "
            + loteEmissao
            + ".";
    if (listaCredenciaisNaoProcessadas.length() > 0) {
      mensagemRetorno += "Credenciais não processadas: " + listaCredenciaisNaoProcessadas;
    }

    map.put("msg", mensagemRetorno);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Recadastrar a senha da credencial de um Portador",
      response = Boolean.class,
      notes =
          "Recadastra a senha da credencial de um Portador. Suporta validação por PIN (senhaUsuario) ou por token (chaveExterna + token)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/recadastrar-pin",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_RECADASTRAR_SENHA_CREDENCIAL_PORTADOR"})
  public ResponseEntity<HashMap<String, Object>> recadastrarPin(
      @RequestBody @Valid RecadastrarPinRequest req, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação de Troca de pin.", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_SENHA_CARTAO);

    Boolean alterado;

    // Detectar se é validação por token ou PIN
    if (req.getChaveExterna() != null && req.getToken() != null) {
      // Validação por token - usar método unificado do service
      Credencial credencial =
          credencialService.recadastraPin(
              req.getNovaSenha(),
              req.getConfirmacaoSenha(),
              req.getIdCredencial(),
              null, // senha não é necessária para validação por token
              user.getIdUsuario(),
              getTokenJWT(request),
              false, // origemPortador = false para usuário admin
              false, // isCardPin = false
              req.getChaveExterna(),
              req.getToken());
      alterado = credencial != null;
    } else {
      // Validação por PIN - lógica original
      alterado =
          credencialService.recadastraPin(
              req.getNovaSenha(),
              req.getConfirmacaoSenha(),
              req.getIdCredencial(),
              req.getSenhaUsuario(),
              user.getIdUsuario(),
              getTokenJWT(request));
    }

    if (alterado) {
      map.put("msg", "Senha recadastrada com sucesso!");
      return new ResponseEntity<>(map, HttpStatus.OK);
    } else {
      map.put("msg", "A senha não pôde ser recadastrada!");
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @ApiOperation(
      value = "Habilitar Credencial",
      response = Boolean.class,
      notes = "Habilitar Credencial de um Portador")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/habilitar",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_HABILITAR_CREDENCIAL"})
  public ResponseEntity<Boolean> habilitarCredencial(
      @RequestBody @Valid TrocarEstadoCredencialRequest req,
      BindingResult result,
      HttpServletRequest httpServletRequest) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação de Habilitação de credencial.", result);
    }

    Boolean habilitado =
        callFunctionUserOrFunctionPortador(
            httpServletRequest,
            req,
            credencialService::habilitarUsoCredencialUser,
            credencialService::habilitarUsoCredencialPortador,
            em);

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Bloquear Credencial Temporariamente",
      response = Boolean.class,
      notes = "Bloqueia temporariamente a Credencial de um Portador")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/bloquear-temporariamente",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BLOQUEAR_CREDENCIAL_TEMPORARIAMENTE"})
  public ResponseEntity<Boolean> bloquearTemporariamente(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação de Bloqueio temporário de credencial.", result);
    }
    Boolean bloqueado = credencialService.bloquearSenhaResgatePontos(req.getIdCredencial());

    return new ResponseEntity<>(bloqueado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Desbloquear Credencial Temporariamente",
      response = Boolean.class,
      notes = "Desbloqueia temporariamente a Credencial de um Portador")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/desbloquear-resgate-pontos",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESBLOQUEAR_CREDENCIAL_RESGATE_PONTOS"})
  public ResponseEntity<Boolean> desbloquearTemporariamente(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação de Bloqueio temporário de credencial.", result);
    }
    Boolean desbloqueado = credencialService.desbloquearSenhaResgatePontos(req.getIdCredencial());

    Credencial credencial = credencialService.findById(req.getIdCredencial());
    Pessoa pessoa = credencialService.getPessoaByIdNotNull(credencial);

    SecurityResgateSenha security = new SecurityResgateSenha(credencial, "cadastro");

    tokenHandler = new TokenHandlerResgateSenha(resgateSenhaService);

    String tokenCadastroSenha = tokenHandler.createTokenForResgateSenha(security);

    HierarquiaInstituicao instituicao =
        hierarquiaInstituicaoService.findByIdProcessadoraAndIdInstituicao(
            pessoa.getIdProcessadora(), pessoa.getIdInstituicao());

    credencialService.resetContadorSenha(req.getIdCredencial());

    String emailDeResposta;
    if (instituicao.getEmailReply() != null) {
      emailDeResposta = instituicao.getEmailReply();
    } else {
      emailDeResposta = "<EMAIL>";
    }
    // logar token de cadastro de senha
    mail.disparoEmailCadastroSenha(tokenCadastroSenha, pessoa.getEmail(), emailDeResposta);

    return new ResponseEntity<>(desbloqueado, HttpStatus.OK);
  }

  // Deixar só autenticação do AuthorizationPortador caso seja necessário
  @ApiOperation(
      value = "Alterar Status da credencial feita pelo portador",
      response = Boolean.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/bloqueio-desbloqueio-portador",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Boolean> bloquearDesbloquearPortador(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }
    if (req.getTipoEstado() != 1 && req.getTipoEstado() != 5) {
      throw new GenericServiceException("Operação inválida: ");
    }

    Boolean response =
        credencialFacade.alterarStatusCredencial(
            req.getIdCredencial(),
            req.getTipoEstado(),
            Constantes.ID_USUARIO_INCLUSAO_PORTADOR,
            true);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Desabilitar uso da Credencial no exterior",
      response = Boolean.class,
      notes = "Desabilita uso da Credencial no exterior")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/desabilitar-exterior",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESABILITAR_CREDENCIAL_EXTERIOR"})
  public ResponseEntity<Boolean> desabilitarUsoExterior(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação para Desabilitar uso de credencial no Exterior.", result);
    }
    Boolean habilitado =
        credencialService.desabilitarUsoExterior(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Habilitar uso da Credencial no exterior",
      response = Boolean.class,
      notes = "Habilita uso da Credencial no exterior")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/habilitar-exterior",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_HABILITAR_CREDENCIAL_EXTERIOR"})
  public ResponseEntity<Boolean> habilitarUsoExterior(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação para Habilitar uso de credencial no Exterior.", result);
    }
    Boolean habilitado =
        credencialService.habilitarUsoExterior(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Desabilitar uso da Credencial em Ecommcerce",
      response = Boolean.class,
      notes = "Desabilita1 uso da Credencial em Ecommcerce")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/desabilitar-ecommerce",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESABILITAR_CREDENCIAL_ECOMMERCE"})
  public ResponseEntity<Boolean> desabilitarEcommerce(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação para Desabilitar uso de credencial no E-Commerce.", result);
    }
    Boolean desabilitado =
        credencialService.desabilitarEcommerce(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(desabilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Habilitar uso da Credencial em Ecommcerce",
      response = Boolean.class,
      notes = "Desabilita1 uso da Credencial em Ecommcerce")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/habilitar-ecommerce",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_HABILITAR_CREDENCIAL_ECOMMERCE"})
  public ResponseEntity<Boolean> habilitarEcommerce(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação para Habilitar uso de credencial no E-Commerce.", result);
    }
    Boolean habilitado =
        credencialService.habilitarEcommerce(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Desabilitar saque para Credencial",
      response = Boolean.class,
      notes = "Desabilita saque para Credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/desabilitar-saque",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESABILITAR_SAQUE_CREDENCIAL"})
  public ResponseEntity<Boolean> desabilitarSaque(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação para desabilitar saque para Credencial.", result);
    }
    Boolean habilitado =
        credencialService.desabilitarSaque(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Habilitar saque para Credencial",
      response = Boolean.class,
      notes = "Habilitar saque para Credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/habilitar-saque",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_HABILITAR_SAQUE_CREDENCIAL"})
  public ResponseEntity<Boolean> habilitarSaque(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação para habilitar saque para Credencial.", result);
    }
    Boolean habilitado =
        credencialService.habilitarSaque(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(habilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Desabilitar notificação  de transação para credencial",
      response = Boolean.class,
      notes = "desabilitar notificação  de transação para credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/desabilitar-notificacao-transacao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESABILITAR_NOTIFICACAO_CREDENCIAL"})
  public ResponseEntity<Boolean> desabilitarNotificacaoTransacao(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação para desabilitar saque para Credencial.", result);
    }
    Boolean desabilitado =
        credencialService.desabilitarNotificacaoTransacao(
            req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(desabilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Habilitar notificação  de transação para credencial",
      response = Boolean.class,
      notes = "habilitar notificação  de transação para credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/habilitar-notificacao-transacao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_HABILITAR_NOTIFICACAO_CREDENCIAL"})
  public ResponseEntity<Boolean> habilitarNotificacaoTransacao(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação Habilitar notificação  de transação Credencial.", result);
    }
    Boolean desabilitado =
        credencialService.habilitarNotificacaoTransacao(req.getIdCredencial(), req.getIdUsuario());

    return new ResponseEntity<>(desabilitado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Cancelar credencial",
      response = Boolean.class,
      notes = "Cancelar credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/cancelar-credencial",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CANCELAR_CREDENCIAL_PORTADOR"})
  public ResponseEntity<Map<String, Object>> cancelarCredencial(
      @RequestBody @Valid AvisarPerdaOuRouboRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação Cancelar Credencial.", result);
    }
    Map<String, Object> map = new HashMap<>();
    Boolean avisado =
        credencialFacade.cancelarCredencial(req.getIdCredencial(), req.getIdUsuario(), true);
    String mensagem =
        avisado
            ? "Cancelamento de credencial realizado com Sucesso."
            : "Não foi possível cancelar credencial.";
    map.put("msg", mensagem);
    map.put("avisado", avisado);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Avisar perda  de credencial",
      response = Boolean.class,
      notes = "Avisar perda de credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/avisar-perda",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_AVISAR_PERDA_CREDENCIAL"})
  public ResponseEntity<Map<String, Object>> avisarPerda(
      @RequestBody @Valid AvisarPerdaOuRouboRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação Avisar perda  Credencial.", result);
    }
    Map<String, Object> map = new HashMap<>();
    Boolean avisado = credencialFacade.avisarPerda(req.getIdCredencial(), req.getIdUsuario(), true);
    String mensagem =
        avisado ? "Aviso de Perda realizado com Sucesso." : "Não foi Possível avisar Perda.";
    map.put("msg", mensagem);
    map.put("avisado", avisado);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Avisar roubo  de credencial",
      response = Boolean.class,
      notes = "Avisar roubo de credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/avisar-roubo",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_AVISAR_ROUBO_CREDENCIAL"})
  public ResponseEntity<Map<String, Object>> avisarRoubo(
      @RequestBody @Valid AvisarPerdaOuRouboRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação Avisar roubo Credencial.", result);
    }
    Map<String, Object> map = new HashMap<>();
    Boolean avisado = credencialFacade.avisarRoubo(req.getIdCredencial(), req.getIdUsuario(), true);
    String mensagem =
        avisado ? "Aviso de Roubo realizado com Sucesso." : "Não foi Possível avisar Roubo.";
    map.put("msg", mensagem);
    map.put("avisado", avisado);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Avisar perda ou roubo de credencial", notes = "Avisar perda ou roubo de
  // credencial")
  //	@ApiImplicitParams({ @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //			@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key") })
  //	@RequestMapping(value = "/avisar-perda-roubo", method = RequestMethod.POST, produces =
  // MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({"ROLE_AVISAR_PERDA_OU_ROUBO_CREDENCIAL"})
  //	public ResponseEntity<Map<String, Object>> avisarPerdaOuRoubo(@RequestBody @Valid
  // AvisarPerdaOuRouboRequest req,
  //			BindingResult result) {
  //
  //		if (result.hasErrors()) {
  //			throw new InvalidRequestException("Validação Avisar perda ou roubo Credencial.", result);
  //		}
  //		Map<String, Object> map = new HashMap<>();
  //		Boolean avisado = credencialFacade.alterarStatusCredencial(req.getIdCredencial(),
  // req.getStatus(),
  //				req.getIdUsuario(), false);
  //		String mensagem = avisado ? "Aviso de Perda ou Roubo realizado com Sucesso."
  //				: "Não foi Possível avisar Perda ou Roubo.";
  //		map.put("msg", mensagem);
  //		map.put("avisado", avisado);
  //		return new ResponseEntity<>(map, HttpStatus.OK);
  //	}

  @ApiOperation(
      value = "Troca estados temporarios da credencial conforme parametros",
      response = Boolean.class,
      notes =
          "troca estado da credencial conforme parametros. "
              + "Tipo estados: 1-trocar status exterior,2-trocar status ecommerce,3-trocar status saque,4-trocar status uso pessoa,5-trocar status notificacao transacao")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/trocar-estado",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TROCAR_STATUS_TEMPORARIO_CREDENCIAL"})
  public ResponseEntity<Boolean> trocarEstado(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }
    Boolean avisado =
        credencialService.trocarEstado(
            req.getIdCredencial(), req.getIdUsuario(), req.getTipoEstado());

    return new ResponseEntity<>(avisado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os status de habilitação de uma credencial",
      response = CredencialStatus.class,
      notes = "Retorna os status de habilitação de uma credencial.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/status-habilitacao/{idCredencial}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_STATUS_HABILITACAO_CREDENCIAL"})
  public ResponseEntity<CredencialStatus> getStatusHabilitacao(@PathVariable Long idCredencial) {

    CredencialStatus response = credencialService.getStatusHabilitacao(idCredencial);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar os cartões de um lote", response = CredencialLoteResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/lote/{idLote}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CARTOES_LOTE"})
  public ResponseEntity<List<CredencialLoteResponse>> findCredenciaisByLote(
      @PathVariable Integer idLote) {

    List<CredencialLoteResponse> response =
        credencialService.findCredencialLoteResponseByIdLoteEmissao(idLote);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os status destinos possíveis para uma credencial",
      response = TipoStatus.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/tipo-status-destino/{idCredencial}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_STATUS_DESTINO_POSSIVEIS_CREDENCIAL"})
  public ResponseEntity<List<TipoStatus>> getTipoStatusDisponiveis(
      @PathVariable Long idCredencial, HttpServletRequest request) {

    SecurityUser user = getAuthenticatedUser(request, em);

    List<TipoStatus> response = credencialService.getTipoStatusDisponiveis(idCredencial, user);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o status da credencial no Jcard",
      response = TipoStatus.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/status-jcard/{idCredencial}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_JCARD"})
  public ResponseEntity<CredencialJcard> getStatusJcard(
      @PathVariable Long idCredencial, HttpServletRequest request) {

    SecurityUser user = getAuthenticatedUser(request, em);

    CredencialJcard response = credencialService.getStatusJcard(idCredencial, user);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar Status da credencial somente no Jcard", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-status-credencial-jcard",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_JCARD"})
  public ResponseEntity<Boolean> alterarStatusCredencialJcard(
      @RequestBody @Valid TrocarEstadoCredencialJcard req, BindingResult result) {
    SecurityUser user = getAuthenticatedUser(request, em);
    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }
    if (req.getIdUsuario() == null) {
      req.setIdUsuario(user.getIdUsuario());
    }

    Boolean response = credencialService.alteraStatusJcard(req, user);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar Status da credencial", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-status-credencial",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL"})
  public ResponseEntity<Boolean> alterarStatusCredencial(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {
    SecurityUser user = getAuthenticatedUser(request, em);
    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }
    if (req.getIdUsuario() == null) {
      req.setIdUsuario(user.getIdUsuario());
    }
    Boolean response =
        credencialFacade.alterarStatusCredencial(
            req.getIdCredencial(), req.getTipoEstado(), req.getIdUsuario(), false);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar Status da credencial Multibeneficios", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/v2/alterar-status-credencial",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL"})
  public ResponseEntity<Map<String, Boolean>> alterarStatusCredencialV2(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }
    if (req.getIdCredencial() == null) {
      throw new GenericRuntimeException("Cartão não informado!");
    }
    Boolean response =
        credencialFacade.alterarStatusCredencial(
            req.getIdCredencial(), req.getTipoEstado(), req.getIdUsuario(), true);

    return new ResponseEntity<>(Collections.singletonMap("dados", response), HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar Status da credencial no B2B", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-status-credencial/b2b",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL"})
  public ResponseEntity<Boolean> alterarStatusCredencialB2b(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {
    SecurityUser user = getAuthenticatedUser(request, em);
    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }
    if (req.getIdUsuario() == null) {
      req.setIdUsuario(user.getIdUsuario());
    }
    Boolean response =
        credencialFacade.alterarStatusCredencialB2b(
            req.getIdCredencial(), req.getTipoEstado(), req.getIdUsuario());
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por mostrar o numero completo de um cartao")
  @RequestMapping(
      value = "/show-credencial-number",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_MOSTRAR_NUMERO_COMPLETO_CREDENCIAL"})
  public ResponseEntity<HashMap<String, Object>> showCredencialNumber(
      @RequestBody @Valid NovoAcessoLogPan model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Validação de recuperação do número da credencial.", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);

    // Verifica se o usuário logado tem permissão para esta ação.
    if (!user.getAuthorities()
        .contains(new SimpleGrantedAuthority("ROLE_MOSTRAR_NUMERO_COMPLETO_CREDENCIAL"))) {
      throw new GenericServiceException("O usuário logado não tem permissão para esta ação!");
    }

    String numeroCompleto = new String();
    try {
      numeroCompleto = credencialService.showNumeroCompleto(model, user);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível recuperar o número completo do cartão! Erro: " + e.getMessage(), e);
    }

    HashMap<String, Object> map = new HashMap<>();
    map.put("numero", numeroCompleto);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por mostrar o numero criptografado de uma credencial(cartao)")
  @RequestMapping(
      value = "/numero-credencial-criptografrado/{idCredencial}",
      method = RequestMethod.GET)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_MOSTRAR_NUMERO_CRIPTOGRAFADO_CREDENCIAL"})
  public ResponseEntity<HashMap<String, Object>> getNumeroCredencialCriptografado(
      @PathVariable Long idCredencial) {

    SecurityUser user = getAuthenticatedUser(request, em);

    // Verifica se o usuário logado tem permissão para esta ação.
    if (!user.getAuthorities()
        .contains(new SimpleGrantedAuthority("ROLE_MOSTRAR_NUMERO_CRIPTOGRAFADO_CREDENCIAL"))) {
      throw new GenericServiceException("O usuário logado não tem permissão para esta ação!");
    }

    String numeroCripto = null;

    try {
      numeroCripto = credencialService.getNumeroCredencialCriptografado(idCredencial);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível recuperar o número criptografado do cartão! ", e);
    }

    if (numeroCripto == null) {
      throw new GenericServiceException(
          "Não foi possível recuperar o número criptografado do cartão! ");
    }

    HashMap<String, Object> map = new HashMap<>();
    map.put("numero", numeroCripto);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por mostrar a senha criptografada de uma credencial(cartao)")
  @RequestMapping(
      value = "/pin-credencial-criptografrada/{idCredencial}",
      method = RequestMethod.GET)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_MOSTRAR_PIN_CRIPTOGRAFADA_CREDENCIAL"})
  public ResponseEntity<HashMap<String, Object>> getSenhaCriptografrada(
      @PathVariable Long idCredencial) {

    SecurityUser user = getAuthenticatedUser(request, em);

    // Verifica se o usuário logado tem permissão para esta ação.
    if (!user.getAuthorities()
        .contains(new SimpleGrantedAuthority("ROLE_MOSTRAR_PIN_CRIPTOGRAFADA_CREDENCIAL"))) {
      throw new GenericServiceException("O usuário logado não tem permissão para esta ação!");
    }

    String numeroCripto = null;

    try {
      numeroCripto = credencialService.getSenhaCredencialCriptografada(idCredencial);

    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível recuperar a senha criptografada do cartão! ", e);
    }

    if (numeroCripto == null) {
      throw new GenericServiceException(
          "Não foi possível recuperar a senha criptografada do cartão! ");
    }
    HashMap<String, Object> map = new HashMap<>();
    map.put("pin", numeroCripto);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Serviço responsável por mostrar a senha criptografada de uma
  // credencial(cartao)")
  //	@RequestMapping(value="/pin-credencial-criptografada/{idCredencial}", method =
  // RequestMethod.GET)
  //	@ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //		@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")})
  //	@Secured({"ROLE_MOSTRAR_PIN_CRIPTOGRAFADA_CREDENCIAL"})
  //	public ResponseEntity<HashMap<String, Object>> getSenhaCriptografada(@PathVariable Long
  // idCredencial){
  //		return getSenhaCriptografrada(idCredencial);
  //	}

  @ApiOperation(
      value = "Enviar senha de cartão via SMS",
      response = Boolean.class,
      notes = "Enviar senha de cartão via SMS.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/senha-sms",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ENVIAR_SENHA_CREDENCIAL_SMS"})
  public ResponseEntity<Boolean> enviarSenhaSms(
      @RequestBody @Valid SenhaCartaoViaSmsRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.RECEBER_SENHA_CARTAO);

    Boolean torpedo = credencialService.enviarSenhaPorSmsFindCredencialById(req.getIdCredencial());

    return new ResponseEntity<>(torpedo, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Enviar senha de cartão via Whatsapp",
      response = Boolean.class,
      notes = "Enviar senha de cartão via Whatsapp.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/senha-whatsapp",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ENVIAR_SENHA_CREDENCIAL_SMS"})
  public ResponseEntity<Boolean> enviarSenhaWhatsapp(
      @RequestBody @Valid SenhaCartaoViaSmsRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.RECEBER_SENHA_CARTAO);

    Boolean torpedo =
        credencialService.enviarSenhaPorWhatsappFindCredencialById(req.getIdCredencial());

    return new ResponseEntity<>(torpedo, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Enviar senha de cartão via SMS",
      response = Boolean.class,
      notes = "Enviar URL que redireciona para redefinição de senha de cartão via SMS.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/url-senha-sms",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ENVIAR_URL_REDEFINIR_SENHA_SMS"})
  public ResponseEntity<Boolean> enviarUrlParaRedefinirSenhaSms(
      @RequestBody @Valid SenhaCartaoViaSmsRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estado Credencial.", result);
    }

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.TROCAR_SENHA_CARTAO);

    Boolean torpedo =
        credencialService.enviarURLRedefinirSenhaPorSmsFindCredencialById(req.getIdCredencial());

    return new ResponseEntity<>(torpedo, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Enviar senha de cartão decriptografada",
      response = String.class,
      notes = "Enviar senha de cartão decriptografada.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/senha-app",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ENVIAR_SENHA_CARTAO_APP"})
  public ResponseEntity<String> enviarSenhaCartaoDecriptografada(
      @RequestBody @Valid SenhaCartaoAppRequest req, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação trocar estadoCredencial.", result);
    }

    SecurityUserPortador user = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.RECEBER_SENHA_CARTAO);

    String senhaDecriptografada = credencialService.getSenhaDecriptografadaById(req);
    return new ResponseEntity<>(senhaDecriptografada, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Integrar número de credencial externa com credencial de conta pagamento existente ",
      response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "externa/integrar-conta", method = RequestMethod.POST)
  @Secured({"ROLE_INTEGRAR_CONTA_CARTAO_EM_CREDENCIAL_EXISTENTE"})
  public ResponseEntity<Boolean> IntegrarCredencialExterna(
      @RequestBody @Valid CredencialExternaTO req, BindingResult result) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro ao integrar número de credencial externa.", result);
    }
    Boolean ok = credencialService.integrarCredencialExterna(req, user);

    return new ResponseEntity<>(ok, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar as credenciais habilitadas para telecompra/televenda",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista com as credenciais que a empresa usa em telecompra.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/televendas",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CREDENCIAIS_TELE_COMPRAS"})
  public ResponseEntity<List<GetCredencial>> getCredenciaisTeleCompras() {

    SecurityUser user = getAuthenticatedUser(request, em);

    List<GetCredencial> response = credencialService.getCredenciaisPjB2b(user);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o extrato da conta do INMAIS por anoMes no Issuer",
      response = GetExtratoCredencial.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{idConta}/{documento}/extrato/ano_mes/{anoMes}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_DATA"})
  public ResponseEntity<List<GetExtratoCredencialInmais>> getExtratoContaPorAnoMesAdm(
      @PathVariable Long idConta,
      @PathVariable String documento,
      @PathVariable Integer anoMes,
      @RequestParam(value = "maisRecente", required = false) Boolean maisRecente) {

    SecurityUser user = getAuthenticatedUser(request, em);

    List<GetExtratoCredencialInmais> extrato =
        credencialFacade.getExtratoInmaisByAnoMesADM(documento, idConta, anoMes, user, maisRecente);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar contador de senha",
      response = GetContadorSenhaCard.class,
      notes = "Retonar o contador de senhas, se está bloqueada e idCredencial")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/contador-senha/{idCredencial}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CONTADOR_SENHA"})
  public ResponseEntity<GetContadorSenhaCard> getContadorSenhaCredencial(
      @PathVariable Long idCredencial) {

    GetContadorSenhaCard response = credencialService.getContadorSenha(idCredencial);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Reseta o contador de senha do Jcard", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/reset-contador", method = RequestMethod.PUT)
  @Secured({"ROLE_RESETAR_CONTADOR_SENHA"})
  public ResponseEntity<Boolean> resetContadorSenha(
      @RequestBody @Valid TrocarEstadoCredencialRequest req, BindingResult result) {

    Boolean ok = credencialService.resetContadorSenha(req.getIdCredencial());

    return new ResponseEntity<>(ok, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Retorna detalhes do cartão / lote",
      response = DetalheCredencialLoteVo.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/consultar-detalhe-lote/{idCredencial}", method = RequestMethod.GET)
  public ResponseEntity<DetalheCredencialLoteVo> consultaDetalheLoteCartao(
      @PathVariable Long idCredencial) {

    return new ResponseEntity<>(
        credencialService.consultaDetalheLoteCartao(idCredencial), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Liberar cartão para emissão manualmente. Marca data hora liberação emissão.",
      response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/liberar-emissao-manualmente/{idCredencial}", method = RequestMethod.GET)
  @Secured({"ROLE_DT_HR_LIBERACAO_MANUAL"})
  public ResponseEntity<HashMap<String, Object>> liberarEmissaoManualmente(
      @PathVariable Long idCredencial) {

    SecurityUser user = getAuthenticatedUser(request, em);

    return credencialService.liberarEmissaoManualmente(idCredencial, user.getIdUsuario());
  }

  @ApiOperation(
      value = "Buscar todas as credenciais desbloqueadas de um Portador por produto",
      response = GetCredenciaisResponse.class,
      notes = "Retorna uma lista com as credenciais do portador que possui o prouto informado.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{documento}/produto/{idProduto}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_POR_PRODUTO"})
  public ResponseEntity<GetCredenciaisResponse> getCredenciaisByDocumentoAndProduto(
      @PathVariable String documento, @PathVariable Integer idProduto) {

    GetCredenciaisResponse response =
        credencialService.getCredenciaisByDocumentoAndProduto(documento, idProduto);

    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca credencias desbloqueadas por documento,instituição e tipo produto plataforma",
      response = CredencialTransferencia.class,
      notes = "Busca credencias desbloqueadas por documento,instituição e tipo produto plataforma.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value =
          "/{doc}/instituicao/{idInst}/produtoPlataforma/{prodPlataforma}/conta-origem/{idContaOrigem}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_DESBLOQUEADAS_PORTADOR"})
  public ResponseEntity<List<CredencialTransferencia>>
      buscarCredenciaisPorDocumentoInstituicaoETipoPlataforma(
          @PathVariable String doc,
          @PathVariable Integer idInst,
          @PathVariable Integer prodPlataforma,
          @PathVariable Long idContaOrigem) {

    List<CredencialTransferencia> response =
        credencialService.buscarCredenciaisPorDocumentoInstituicaoETipoPlataforma(
            doc, idInst, prodPlataforma, idContaOrigem);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca informações corporativas de um Portador",
      response = PortadorCorporativo.class,
      notes = "Busca informações corporativas de um Portador")
  @RequestMapping(
      value = "/corporativo",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<PortadorCorporativo> buscarInformacoesCorporativas(
      @RequestBody @Valid PortadorCorporativoRequest request) {
    return ok(
        credencialService.buscarInformacoesCorporativasPorCredencialESenha(
            request.getCredencial(), request.getSenha()));
  }

  @ApiOperation(
      value = "Envia dados sensiveis da credencial ativa para a Conta.",
      response = String.class,
      notes = "Envia dados sensiveis da credencial ativa para a Conta.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/consultar-credencial/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_SENSIVEIS_CREDENCIAL"})
  public ResponseEntity<DadosSensiveisCredencial> buscarDadosCredencialConta(
      @PathVariable Long idConta, HttpServletRequest request) {
    SecurityUser user = getAuthenticatedUser(request, em);
    return new ResponseEntity<>(
        credencialService.obterDadosSensiveisCredencialPelaConta(idConta, user), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Envia dados sensiveis de todas credenciais da conta.",
      response = String.class,
      notes = "Envia dados sensiveis de todas credenciais da conta.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/consultar-credenciais/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONSULTAR_SENSIVEIS_CREDENCIAL"})
  public ResponseEntity<List<DadosSensiveisCredencial>> buscarDadosCredencialBitfy(
      @PathVariable Long idConta, HttpServletRequest request) {
    SecurityUser user = getAuthenticatedUser(request, em);
    return new ResponseEntity<>(
        credencialService.obterDadosSensiveisCredenciaisPelaConta(idConta, user), HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar estado de NFC da credencial", response = Boolean.class)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/alterar-nfc-credencial",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL"})
  public ResponseEntity<Boolean> alterarNFCCredencial(
      @RequestBody @Valid TrocarEstadoNFCCredencialRequest trocarEstadoNFCCredencialRequest,
      BindingResult result) {
    if (result.hasErrors()) {
      throw new InvalidRequestException(
          ConstantesErro.CAR_VALIDAR_TROCA_ESTADO_NFC.getMensagem(), result);
    }

    boolean response =
        callFunctionPortadorOrFunctionCorporativo(
            this.request,
            trocarEstadoNFCCredencialRequest,
            credencialFacade::alterarEstadoNFCCredencial,
            credencialFacade::alterarEstadoNFCCredencial,
            em);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar estado de NFC da credencial pelo issuer", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-nfc-credencial-issuer",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL_NFC_ISSUER"})
  public ResponseEntity<Boolean> alterarNFCCredencialIssuer(
      @RequestBody @Valid TrocarEstadoNFCCredencialRequest trocarEstadoNFCCredencialRequest,
      BindingResult result) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          ConstantesErro.CAR_VALIDAR_TROCA_ESTADO_NFC.getMensagem(), result);
    }

    Boolean response =
        credencialFacade.alterarEstadoNFCCredencialIssuer(trocarEstadoNFCCredencialRequest, user);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Alterar estado de NFC da credencial Multibenefícios",
      response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/v2/alterar-nfc-credencial",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL"})
  public ResponseEntity<Map<String, Boolean>> alterarNFCCredencialBff(
      @RequestBody @Valid TrocarEstadoNFCCredencialRequest trocarEstadoNFCCredencialRequest,
      BindingResult result) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          ConstantesErro.CAR_VALIDAR_TROCA_ESTADO_NFC.getMensagem(), result);
    }

    Boolean response =
        credencialFacade.alterarEstadoNFCCredencial(trocarEstadoNFCCredencialRequest, userPortador);
    return new ResponseEntity<>(Collections.singletonMap("dados", response), HttpStatus.OK);
  }

  @ApiOperation(value = "lista historico de status de cartão jcard e plataforma")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/historio-cartao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CHAMADO_CONTA"})
  public ResponseEntity<HashMap<String, Object>> historicoCredencial(
      @RequestBody @Valid HistoricoCartaoRequest req, BindingResult result) {
    SecurityUser user = getAuthenticatedUser(request, em);
    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação lista historico Credencial", result);
    }

    if (req.getIdUsuario() == null) {
      req.setIdUsuario(user.getIdUsuario());
    }

    HashMap<String, Object> historicoAlteracaoCredencialAudit;

    historicoAlteracaoCredencialAudit =
        credencialService.historicoCredencial(req.getIdCredencial());

    return new ResponseEntity<>(historicoAlteracaoCredencialAudit, HttpStatus.OK);
  }

  @ApiOperation(value = "Altera status de multiplas credenciais de uma vez")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/alterar-credenciais-arquivo",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_STATUS_CREDENCIAL"})
  public ResponseEntity<AlterarStatusCredencialResponseVO> alterarStatusMultiplasCredenciais(
      @RequestBody List<LogRegistroAlterarStatus> body, HttpServletRequest request) {
    SecurityUser user = getAuthenticatedUser(request, em);
    AlterarStatusCredencialResponseVO responseVO = new AlterarStatusCredencialResponseVO();
    responseVO.setList(
        credencialService.alterarStatusMultiplasCredenciais(body, user.getIdUsuario()));
    return new ResponseEntity<>(responseVO, HttpStatus.OK);
  }

  @ApiOperation(value = "Busca informação de pagamento de boleto de segunda via")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-boleto-segunda-via/credencial/{idCredencial}/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_BOLETO_SEGUNDA_VIA"})
  public ResponseEntity<HashMap<String, Object>> buscarInfoBoletoSegundaVia(
      @PathVariable("idCredencial") Long idCredencial,
      @PathVariable("idConta") Long idConta,
      HttpServletRequest request) {
    HashMap<String, Object> map = new HashMap<>();
    map.put("config", credencialService.findConfigSegundaViaBoleto(idConta));
    map.put("boleto", credencialService.findPagamentoCredencial(idCredencial));
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Confere situação atual dos pagamentos e gera novo boleto")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  })
  @RequestMapping(
      value = "/novo-boleto-segunda-via/credencial/{idCredencial}/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GERAR_NOVO_BOLETO"})
  public ResponseEntity<HashMap<String, Object>> gerarNovoBoletoSegundaVia(
      @PathVariable("idCredencial") Long idCredencial,
      @PathVariable("idConta") Long idConta,
      HttpServletRequest request) {
    HashMap<String, Object> map =
        credencialService.regerarBoletoPorConfig(idConta, 999999, idCredencial);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Gera segunda via do cartão após verificação de pagamento de boleto")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/gerar-segunda-via-boleto/{idCredencialAnterior}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GERAR_SEGUNDA_VIA"})
  public ResponseEntity<Credencial> gerarSegundaViaPosPagamento(
      @PathVariable Long idCredencialAnterior) {
    SecurityUser user = getAuthenticatedUser(request, em);
    Credencial credencial = credencialService.findByIdCredencial(idCredencialAnterior);
    Credencial credencialNova =
        credencialService.gerarSegundaViaPosPagamentoBoleto(credencial, user.getIdUsuario());
    return new ResponseEntity<>(credencialNova, HttpStatus.OK);
  }

  @ApiOperation(value = "Baixa boleto de segunda via em PDF")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  })
  @RequestMapping(
      value = "/download-boleto-segunda-via/{idCobrancaBancaria}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DOWNLOAD_BOLETO_SEGUNDA_VIA"})
  public ResponseEntity<?> downloadBoletoSegundaVia(@PathVariable Long idCobrancaBancaria) {
    InputStream input = credencialService.downloadBoletoCobrancaBancaria(idCobrancaBancaria);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", Arrays.asList("Download finalizado com sucesso!"));

    return new ResponseEntity<>(new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(value = "Busca ultima data hora alteracao da credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar-alteracao-senha/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<LogAlteracaoPinCredencial>> buscarDadosCredencialConta(
      @PathVariable Long idConta) {
    return new ResponseEntity<>(
        logAlteracaoPinCredencialService.buscarAlteracaoPinCredencial(idConta), HttpStatus.OK);
  }
}
