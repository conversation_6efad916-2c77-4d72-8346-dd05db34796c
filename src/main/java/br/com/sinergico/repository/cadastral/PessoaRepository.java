package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.json.bean.cadastral.PortadorVinculacaoTO;
import br.com.json.bean.cadastral.TelefoneCelularPessoaResponse;
import br.com.sinergico.vo.PessoaPortadorCafVO;
import br.com.sinergico.vo.PessoaVO;
import java.util.Collection;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface PessoaRepository extends JpaRepository<Pessoa, Long>, PessoaRepositoryCustom {

  String PESSOA_FISICA = "1";
  String PESSOA_JURIDICA = "2";
  String PESSOA_TITULAR = "1";
  String PESSOA_ADICIONAL = "2";

  //	@EntityGraph(value = "Pessoa.contas", type = EntityGraphType.LOAD)
  //	@Query(value = "From Pessoa p " +
  //			"where p.idProcessadora = :idProcessadora " +
  //			"and p.idInstituicao = :idInstituicao " +
  //			"and p.documento = :documento " +
  //			"and p.idTipoPessoa = :idTipoPessoa ")
  //	@QueryHints(@QueryHint(name = org.hibernate.annotations.QueryHints.FETCH_SIZE, value = "1"))
  //	Pessoa findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoa(
  //			@Param("idProcessadora") Integer idProcessadora,
  //			@Param("idInstituicao") Integer idInstituicao,
  //			@Param("documento") String documento,
  //			@Param("idTipoPessoa") Integer idTipoPessoa);

  @Query(
      "SELECT p FROM Pessoa p WHERE p.idProcessadora = :idProcessadora AND p.idInstituicao = :idInstituicao AND p.documento = :documento "
          + "and p.idTipoPessoa = :idTipoPessoa and p.setorFilial.idProcessadora = :idProcessadora and p.setorFilial.idInstituicao = :idInstituicao"
          + " and p.setorFilial.idRegional = :idRegional and p.setorFilial.idFilial = :idFilial and p.setorFilial.idPontoRelacionamento = :idPontoRelacionamento")
  Pessoa findOneByHierarquia(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("idTipoPessoa") Integer idTipoPessoa,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "SELECT p FROM Pessoa p WHERE p.idProcessadora = :idProcessadora AND p.idInstituicao = :idInstituicao AND p.documento = :documento "
          + " and p.setorFilial.idProcessadora = :idProcessadora and p.setorFilial.idInstituicao = :idInstituicao"
          + " and p.setorFilial.idRegional = :idRegional and p.setorFilial.idFilial = :idFilial and p.setorFilial.idPontoRelacionamento = :idPontoRelacionamento")
  List<Pessoa> findByHierarquiaPFouPJ(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "SELECT p FROM Pessoa p WHERE p.idProcessadora = :idProcessadora AND p.idInstituicao = :idInstituicao AND p.matricula = :matricula "
          + "and p.idTipoPessoa = :idTipoPessoa and p.setorFilial.idProcessadora = :idProcessadora and p.setorFilial.idInstituicao = :idInstituicao"
          + " and p.setorFilial.idRegional = :idRegional and p.setorFilial.idFilial = :idFilial and p.setorFilial.idPontoRelacionamento = :idPontoRelacionamento")
  Pessoa findOneByHierarquiaAndMatricula(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("matricula") String matricula,
      @Param("idTipoPessoa") Integer idTipoPessoa,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  Pessoa findOneByIdPessoa(Long idPessoa);

  @Query(
      "SELECT new Pessoa(p.idPessoa) FROM Pessoa p WHERE p.idProcessadora = :idProcessadora AND p.idInstituicao = :idInstituicao AND p.documento = :documento AND p.tipoPessoa.id=:tipoPessoa ")
  List<Pessoa> findByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("tipoPessoa") Integer tipoPessoa);

  @Query(
      "SELECT p FROM Pessoa p WHERE p.idProcessadora = :idProcessadora AND p.idInstituicao = :idInstituicao AND p.documento = :documento")
  Pessoa findOneByIdProcessadoraAndIdInstituicaoAndDocumento(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento);

  Pessoa findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoOrderByIdPessoaDesc(
      Integer idProcessadora, Integer idInstituicao, String documento);

  @Query(
      "SELECT p FROM Pessoa p "
          + " inner join p.contasPessoa cp "
          + " inner join cp.contaPagamento cpag "
          + " where p.documento = :documento "
          + " and cpag.idProcessadora = 10 "
          + " and cpag.idInstituicao = 2001 "
          + " and cpag.produtoInstituicao = 200101 ")
  Pessoa findPessoaProdutoInMaisByDocumento(@Param("documento") String documento);

  @Query(
      value =
          "select p.* from cadastral.pessoa p \n"
              + "inner join cadastral.conta_pessoa cp on p.id_pessoa = cp.id_pessoa and cp.id_titularidade = 1\n"
              + "inner join cadastral.conta_pagamento cpag on cp.id_conta = cpag.id_conta \n"
              + "inner join cadastral.produto_instituicao_correspondente pic on cpag.id_prod_instituicao = pic.id_prod_instituicao_orig \n"
              + "inner join cadastral.produto_instituicao_configuracao pic2  on pic.id_prod_instituicao_dest = pic2.id_prod_instituicao \n"
              + "where  p.documento = :documento and p.id_instituicao = 1801 and pic2.carga_integracao",
      nativeQuery = true)
  List<Pessoa> findPessoaInMaisPremiosIntegracaoPorDocumento(@Param("documento") String documento);

  List<Pessoa> findByIdProcessadoraAndIdInstituicaoAndDocumento(
      Integer idProcessadora, Integer idInstituicao, String documento);

  List<Pessoa> findByIdProcessadoraAndDocumentoAndIdInstituicaoIn(
      Integer idProcessadora, String documento, Collection<Integer> idInstituicao);

  @Query(
      "SELECT p FROM Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento c "
          + "where c.idConta = :idConta "
          + "and cp.idTitularidade = 2")
  List<Pessoa> buscarPessoasAdicionaisConta(@Param("idConta") Long idConta);

  @Query(
      "SELECT new br.com.sinergico.vo.PessoaVO(p.nomeCompleto, p.documento) FROM Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento cpg "
          + "WHERE cpg.idConta = :idConta "
          + "and cp.idTitularidade = 1")
  PessoaVO findByIdConta(@Param("idConta") Long idConta);

  @Query(
      "SELECT new br.com.sinergico.vo.PessoaVO(p.nomeCompleto, p.documento) FROM Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento cpg "
          + "WHERE cpg.idConta = :idConta")
  List<PessoaVO> findListByIdConta(@Param("idConta") Long idConta, Pageable page);

  @Query(
      "SELECT p FROM Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento cpg "
          + "WHERE cpg.idConta = :idConta "
          + "and cp.idTitularidade = 1 ")
  Pessoa findPessoaByIdConta(@Param("idConta") Long idConta);

  @Query(
      "SELECT p.idPessoa FROM Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento cpg "
          + "WHERE cpg.idConta = :idConta")
  List<Long> findIdPessoaByIdConta(@Param("idConta") Long idConta);

  @Query(
      "SELECT count(p.idPessoa) FROM Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento c "
          + "where c.idConta = :idConta "
          + "and p.documento = :documento")
  Integer existeDocumentoPessoasConta(
      @Param("documento") String documento, @Param("idConta") Long idConta);

  @Query(
      "select new br.com.json.bean.cadastral.PortadorVinculacaoTO(p.idPessoa, cp.idConta, cp.nomeCartaoImpresso, p.documento, p.nomeCompleto, p.sexo.sexo, p.estadoCivil.id, p.dataNascimento, endp.logradouro, endp.bairro, endp.cidade, endp.uf, endp.cep, p.dddTelefoneResidencial, p.telefoneResidencial, p.email, p.nomeMae, p.nomePai, p.nacionalidade,p.naturalidade, p.rg, p.rgDataEmissao, p.rgOrgaoEmissor, p.dddTelefoneCelular, p.telefoneCelular, p.cnpjVinculante) from Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento cpag "
          + "inner join p.enderecosPessoa endp "
          + "inner join cpag.credenciais c "
          + "inner join cpag.produtoInstituicao pi "
          + "where pi.idProdInstituicao = :idProdInstituicao and c.idConta = cpag.idConta and c.idCredencialExterna is null and c.dataHoraEmitido is null "
          + "and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoRelacionamento "
          + "and endp.status=1 and endp.idTipoEndereco = 1 and cpag.idProdutoPlataforma = 5 and c.status=0")
  List<PortadorVinculacaoTO> findByCredencialNaoVinculadoExterno(
      @Param("idProdInstituicao") Integer idProdInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "update Pessoa pess set pess.email = :email where pess.idInstituicao = :idInstituicao and pess.idProcessadora= :idProcessadora and pess.documento like :documento")
  @Modifying
  @Transactional
  void updateByIdInstituicaoAndIdProcessadoraAndDocumento(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("email") String email,
      @Param("documento") String documento);

  @Query(
      value =
          "select p.* from cadastral.pessoa p "
              + "inner join cadastral.conta_pessoa cp on cp.id_pessoa = p.id_pessoa "
              + "inner join cadastral.conta_pagamento c on c.id_conta = cp.id_conta "
              + "where c.id_conta = :idConta "
              + "and cp.id_titularidade = 1 ",
      nativeQuery = true)
  Pessoa findPessoaTitularConta(@Param("idConta") Long idConta);

  @Query(
      "select new br.com.json.bean.cadastral.TelefoneCelularPessoaResponse(p.dddTelefoneCelular, p.telefoneCelular) from Pessoa p "
          + "where p.idPessoa = :idPessoa ")
  TelefoneCelularPessoaResponse getTelefonePessoa(@Param("idPessoa") Long idPessoa);

  @Query(
      "select new br.com.json.bean.cadastral.TelefoneCelularPessoaResponse(p.dddTelefoneCelular, p.telefoneCelular) from Pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento c "
          + "where c.idConta = :idConta "
          + "and cp.idTitularidade = 1 ")
  TelefoneCelularPessoaResponse getTelefonePessoaTitularConta(@Param("idConta") Long idConta);

  @Query(
      "SELECT p.idTipoPessoa FROM Pessoa p inner join p.contasPessoa cp where cp.idConta = :idConta and cp.idTitularidade = 1")
  Long findTipoConta(@Param("idConta") Long idConta);

  Pessoa findByDocumentoAndMatriculaAndIdSetorFilial(
      String documento, String matricula, Integer idSetorFilial);

  List<Pessoa> findByIdProcessadoraAndIdInstituicaoAndEmailIgnoreCase(
      Integer idProcessadora, Integer idInstituicao, String email);

  List<Pessoa> findByIdProcessadoraAndIdInstituicaoAndDddTelefoneCelularAndTelefoneCelular(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer dddTelefoneCelular,
      Integer telefoneCelular);

  Pessoa
      findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataNascimentoNotNullOrderByIdPessoaDesc(
          Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa);

  @Query(
      value =
          "SELECT cpag.id_conta, ddd_tel_celular , tel_celular , pes.* "
              + "from cadastral.portador_login pl "
              + "inner join cadastral.pessoa pes "
              + "on (pes.id_processadora , pes.id_instituicao , pes.documento) = (pl.id_processadora , pl.id_instituicao , pl.cpf) "
              + "inner join cadastral.conta_pessoa cpes on cpes.id_pessoa = pes.id_pessoa and cpes.id_titularidade = 1 "
              + "inner join cadastral.conta_pagamento cpag on cpag.id_conta = cpes.id_conta "
              + "inner join cadastral.produto_instituicao_configuracao pic on pic.id_prod_instituicao = cpag.id_prod_instituicao and  pic.id_grupo_produto = pl.grupo_acesso "
              + "where pl.cpf = :documento and pl.id_processadora = :idProcessadora and pl.id_instituicao = :idInstituicao"
              + " and grupo_acesso = :grupoAcesso "
              + "order by pes.id_pessoa desc",
      nativeQuery = true)
  Pessoa
      findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndGrupoAcessoAndDataNascimentoNotNull(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("grupoAcesso") Long grupoAcesso);

  @Query(
      value =
          "select "
              + "    p.* "
              + "from cadastral.pessoa p "
              + "    inner join cadastral.conta_pessoa cp on cp.id_pessoa = p.id_pessoa and cp.id_titularidade = 1 "
              + "    inner join cadastral.conta_pagamento cpag on cpag.id_conta = cp.id_conta "
              + "    inner join suporte.tipo_status ts on cpag.id_status_conta = ts.id_status and ts.id_grupo_status <> 9 "
              + "    inner join cadastral.portador_login pl on (pl.cpf, pl.id_processadora, pl.id_instituicao) = (p.documento, p.id_processadora, p.id_instituicao) "
              + "where true "
              + "and pl.cpf = :documento "
              + "and pl.id_processadora = :idProcessadora "
              + "and pl.id_instituicao = :idInstituicao "
              + "and p.tipo_pessoa = :idTipoPessoa "
              + "and p.data_nascimento is not null "
              + "limit 1 ",
      nativeQuery = true)
  Pessoa encontraPessoaContaAtivaPorDocumentoProcessadoraEInstituicaoDataDeNascimentoNotNull(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("idTipoPessoa") Integer idTipoPessoa);

  Pessoa
      findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataFundacaoNotNullOrderByIdPessoaDesc(
          Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa);

  @Query(
      value =
          " select case when (rl.cpf = pl.documento_acesso) then rl.cpf else p.documento end as documento "
              + " from cadastral.portador_login pl "
              + " left join cadastral.pessoa p on pl.cpf = p.documento "
              + " left join cadastral.representante_legal rl on pl.documento_acesso = rl.cpf "
              + " where ((p.rg is not null and p.rg = :rg) or (rl.rg is not null and rl.rg = :rg)) "
              + " order by p.dt_hr_inclusao desc ",
      nativeQuery = true)
  List<String> findAllByRg(@Param("rg") String rg);

  @Query(
      value =
          "select exists ( "
              + " select 1 "
              + " from cadastral.pessoa p "
              + " inner join cadastral.conta_pessoa cp on cp.id_pessoa = p.id_pessoa and cp.id_titularidade = 2 "
              + " where p.id_processadora = :idProcessadora and p.id_instituicao = :idInstituicao and p.documento = :documento "
              + ")",
      nativeQuery = true)
  Boolean existePessoaAdicional(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento);

  @Query(
      value =
          " SELECT p.* "
              + " from cadastral.pessoa p "
              + " inner join cadastral.conta_pessoa cp on cp.id_pessoa = p.id_pessoa "
              + " inner join cadastral.pessoa ptit on ptit.id_processadora = p.id_processadora and ptit.id_instituicao = p.id_instituicao "
              + " inner join cadastral.conta_pessoa cptit on cptit.id_pessoa = ptit.id_pessoa and cp.id_conta = cptit.id_conta"
              + " where p.id_processadora = :idProcessadora and p.id_instituicao = :idInstituicao "
              + " and p.documento = :documentoAdicional and ptit.documento = :documentoTitular"
              + " and p.tipo_pessoa = "
              + PESSOA_FISICA
              + " and ptit.tipo_pessoa = "
              + PESSOA_JURIDICA
              + " and cp.id_titularidade = "
              + PESSOA_ADICIONAL
              + " and cptit.id_titularidade = "
              + PESSOA_TITULAR
              + " order by p.id_pessoa desc limit 1",
      nativeQuery = true)
  Pessoa buscarPessoaFisicaAdicionalPorProcessadoraInstituicaoDocumentoTitularDocumentoAdicional(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoTitular") String documentoTitular,
      @Param("documentoAdicional") String documentoAdicional);

  @SuppressWarnings("unnused")
  @Query(
      value =
          "select p.* "
              + "from cadastral.pessoa p "
              + "inner join cadastral.portador_login pl on p.id_processadora = pl.id_processadora "
              + "and p.id_instituicao = pl.id_instituicao "
              + "and pl.cpf = p.documento "
              + "where pl.id_processadora = :idProcessadora "
              + "and pl.id_instituicao = :idInstituicao "
              + "and pl.cpf = :documento "
              + "and pl.grupo_acesso = :grupoAcesso "
              + "and pl.documento_acesso is null "
              + "and pl.dt_hr_cancelamento is null "
              + "order by p.id_pessoa desc ",
      nativeQuery = true)
  List<Pessoa> findPessoasWithLoginAtivoAndIdProcessadoraAndIdInstituicaoAndDocumentoAndGrupoAcesso(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("grupoAcesso") String grupoAcesso);

  @SuppressWarnings("unnused")
  @Query(
      value =
          "select p.*"
              + "from cadastral.pessoa p "
              + "inner join cadastral.portador_login pl on p.id_processadora = pl.id_processadora "
              + "and p.id_instituicao = pl.id_instituicao "
              + "and ( "
              + "(pl.tipo_pessoa = 1 and pl.documento_acesso = p.documento) "
              + "or (pl.tipo_pessoa = 2 and length(p.documento) = 14 and length(pl.documento_acesso) = 11 and pl.cpf = p.documento) "
              +
              // "--\t\tor (pl.tipo_pessoa = 2 and length(p.documento) = 14 and
              // length(pl.documento_acesso) = 14 and pl.documento_acesso = p.documento) -- Quando
              // houverem CNPJs adicionais\n" +
              ") "
              + "where pl.id_processadora = :idProcessadora "
              + "and pl.id_instituicao = :idInstituicao "
              + "and pl.cpf = :documento "
              + "and pl.documento_acesso = :documentoAcesso "
              + "and pl.grupo_acesso is null "
              + "and pl.dt_hr_cancelamento is null "
              + "order by p.id_pessoa desc ",
      nativeQuery = true)
  List<Pessoa>
      findPessoasWithLoginAtivoAndIdProcessadoraAndIdInstituicaoAndDocumentoAndDocumentoAcesso(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("documentoAcesso") String documentoAcesso);

  @SuppressWarnings("unnused")
  @Query(
      value =
          "select p.* "
              + "from cadastral.pessoa p  "
              + "inner join cadastral.portador_login pl on p.id_processadora = pl.id_processadora "
              + "and p.id_instituicao = pl.id_instituicao "
              + "and pl.cpf = p.documento "
              + "where pl.id_processadora = :idProcessadora "
              + "and pl.id_instituicao = :idInstituicao "
              + "and pl.cpf = :documento "
              + "and pl.documento_acesso is null "
              + "and pl.grupo_acesso is null "
              + "and pl.dt_hr_cancelamento is null "
              + "order by p.id_pessoa desc ",
      nativeQuery = true)
  List<Pessoa> findPessoasWithLoginAtivoAndIdProcessadoraAndIdInstituicaoAndDocumento(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento);

  @Query(
      value =
          "select p.* from cadastral.pessoa p "
              + "left join audit.pessoa_audit pa on p.id_pessoa = pa.id_pessoa "
              + "where p.id_processadora = :idProcessadora "
              + "and p.id_instituicao = :idInstituicao "
              + "and p.documento = :documento "
              + "order by pa.dt_hr_manutencao desc, p.dt_hr_inclusao desc limit 1 ",
      nativeQuery = true)
  Pessoa findPessoaAtualizadaRecente(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento);

  @Query(
      value =
          " select p.* from cadastral.pessoa p  \n "
              + " \t inner join cadastral.conta_pessoa cp on cp.id_pessoa = p.id_pessoa and cp.id_titularidade = 1 \n "
              + " \t inner join cadastral.conta_pagamento cpag on cpag.id_conta = cp.id_conta  \n "
              + " \t inner join cadastral.produto_instituicao_configuracao pic on pic.id_prod_instituicao = cpag.id_prod_instituicao  \n "
              + " \t left join audit.pessoa_audit pa on p.id_pessoa = pa.id_pessoa  \n "
              + " where p.id_processadora = :idProcessadora  \n "
              + " \t and p.id_instituicao = :idInstituicao  \n "
              + " \t and p.documento = :documento  \n "
              + " \t and pic.id_grupo_produto = :grupoAcesso \n "
              + " order by pa.dt_hr_manutencao desc, p.dt_hr_inclusao desc limit 1 ",
      nativeQuery = true)
  Pessoa findPessoaAtualizadaRecenteComGrupoAcesso(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("grupoAcesso") Long grupoAcesso);

  @Query(
      value =
          "select p.* from cadastral.pessoa p "
              + " where true "
              + " and p.documento = :documento "
              + " order by p.consulta_restrita desc "
              + " limit 1 ",
      nativeQuery = true)
  Pessoa encontraPessoaConsultaRestritaPorDocumento(@Param("documento") String documento);

  @Query(
      value =
          "select new br.com.sinergico.vo.PessoaPortadorCafVO(p.nomeCompleto ,p.documento, p.nomeMae, p.nomePai, p.rg) "
              + " from Pessoa p "
              + " where p.documento = :documento "
              + " and p.idInstituicao = :idInstituicao ")
  PessoaPortadorCafVO buscarDadosPessoaCafByInstituicao(
      @Param("documento") String documento, @Param("idInstituicao") Integer idInstituicao);

  @Query(
      " select p from Pessoa p "
          + " where p.documento = :documento "
          + " and p.idInstituicao = :idInstituicao ")
  Pessoa findPessoaByDocumentoAndIdInstituicao(
      @Param("documento") String documento, @Param("idInstituicao") Integer idInstituicao);

  @Query(
      value =
          " select p.* FROM cadastral.pessoa p \n "
              + " \t inner join cadastral.conta_pessoa cp  on p.id_pessoa = cp.id_pessoa \n "
              + " \t inner join cadastral.conta_pagamento cpag on cp.id_conta = cpag.id_conta \n "
              + " where true \n "
              + " \t and p.tipo_pessoa = :tipoPessoa \n "
              + " \t and p.documento = :documento \n "
              + " \t and cpag.id_instituicao  = :idInstituicao \n "
              + " \t and cpag.id_regional = :idRegional \n "
              + " \t and cpag.id_filial = :idFilial \n "
              + " \t and cpag.id_ponto_de_relacionamento = :idPontoDeRelacionamento \n "
              + " \t and cpag.id_prod_instituicao = :idProdutoInstituicao ",
      nativeQuery = true)
  Pessoa findPessoaProdutoPDAFByDocumento(
      @Param("documento") String documento,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa);

  @Query(
      value =
          " select cp.* FROM cadastral.pessoa p \n "
              + " \t inner join cadastral.conta_pessoa cp  on p.id_pessoa = cp.id_pessoa \n "
              + " \t inner join cadastral.conta_pagamento cpag on cp.id_conta = cpag.id_conta \n "
              + " where true \n "
              + " \t and p.tipo_pessoa = :tipoPessoa \n "
              + " \t and p.documento = :documento \n "
              + " \t and cpag.id_instituicao  = :idInstituicao \n "
              + " \t and cpag.id_regional = :idRegional \n "
              + " \t and cpag.id_filial = :idFilial \n "
              + " \t and cpag.id_ponto_de_relacionamento = :idPontoDeRelacionamento \n "
              + " \t and cpag.id_prod_instituicao = :idProdutoInstituicao ",
      nativeQuery = true)
  ContaPessoa findContaPagamentoProdutoPDAFByDocumento(
      @Param("documento") String documento,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa);

  @Query(
      value =
          " select p.* FROM cadastral.conta_pessoa cp \n "
              + " \t inner join cadastral.pessoa p on cp.id_pessoa = p.id_pessoa \n "
              + " where true \n "
              + " \t and cp.id_titularidade = 2 \n "
              + " \t and cp.id_conta = :idConta ",
      nativeQuery = true)
  List<Pessoa> findContasPessoasAdicionaisPDAFByIdConta(@Param("idConta") Long idConta);

  @Query(
      value =
          " select p.* FROM cadastral.pessoa p \n "
              + " where true \n "
              + " \t and ddd_tel_celular is not null \n "
              + " \t and tel_celular is not null \n "
              + " \t and p.documento = :documento \n "
              + " \t and p.id_instituicao = :idInstituicao limit 1",
      nativeQuery = true)
  Pessoa findPessoaPortadorLogin(
      @Param("idInstituicao") Integer idInstituicao, @Param("documento") String documento);
}
