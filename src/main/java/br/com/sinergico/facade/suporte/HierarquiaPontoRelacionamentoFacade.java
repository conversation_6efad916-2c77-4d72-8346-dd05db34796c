package br.com.sinergico.facade.suporte;

import br.com.client.rest.jcard.json.bean.AlteraLimiteEmpresa;
import br.com.client.rest.jcard.json.bean.PontoRelacionamentoResponse;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.CadastrarPontoRelacionamento;
import br.com.sinergico.repository.cadastral.ContaPagamentoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.jcard.AlteraLimiteEmpresaService;
import br.com.sinergico.service.jcard.PontoRelacionamentoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.DateUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import javax.persistence.NoResultException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class HierarquiaPontoRelacionamentoFacade {

  @Lazy @Autowired
  private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

  @Autowired private PontoRelacionamentoService pontoRelacionamentoService;

  @Autowired private AlteraLimiteEmpresaService alteraLimiteEmpresaService;

  @Autowired private ContaPagamentoRepository contaPagRepository;

  @Transactional
  public HierarquiaPontoDeRelacionamento update(
      CadastrarPontoRelacionamento model, SecurityUser user, Integer id) {
    HierarquiaPontoDeRelacionamento hpr = new HierarquiaPontoDeRelacionamento(model);
    hpr.getId().setIdProcessadora(user.getIdProcessadora());
    hpr.getId().setIdPontoDeRelacionamento(id);

    HierarquiaPontoDeRelacionamento hprBuscada =
        hierarquiaPontoRelacionamentoService.findById(hpr.getId());
    if (hprBuscada == null) {
      throw new NoResultException(
          "Ponto de Relacionamento não existe.idInstituicao = "
              + model.getIdInstituicao()
              + " , idRegional = "
              + model.getIdRegional()
              + ", idFilial = "
              + model.getIdFilial()
              + " idPontoRelacionamento= "
              + id
              + " ");
    }
    if (model.getLimiteMaxCredito() != null) {
      validarLimiteGlobal(model.getLimiteMaxCredito(), hprBuscada);
    }

    BeanUtils.copyProperties(model, hprBuscada, GenericService.getNullPropertyNames(model));
    // verifica se eh B2B
    if (model.getB2b() != null && model.getB2b()) {

      hprBuscada.setDataHoraUltimaAtualizacao(LocalDateTime.now());
      hprBuscada.setIdUsuarioManutencao(model.getIdUsuario());

      if (model.getGrauDeRisco() != null) {
        hprBuscada.setGrauDeRisco(model.getGrauDeRisco());
      }
      if (model.getDataFundacao() != null) {
        hprBuscada.setDataFundacao(DateUtil.dateToLocalDateTime(model.getDataFundacao()));
      }
    }

    hprBuscada =
        hierarquiaPontoRelacionamentoService.preparePontoDeRelacionamento(hprBuscada, user);
    hprBuscada.setIdUsuarioManutencao(user.getIdUsuario());

    // realiza o update do limiteempresa no jcard nas contas pertencentes ao ponto de
    // relacionamento.
    AlteraLimiteEmpresa alteraLimiteEmpresa = new AlteraLimiteEmpresa();
    alteraLimiteEmpresa.setIdInstituicao(hprBuscada.getIdInstituicao());
    alteraLimiteEmpresa.setIdRegional(hprBuscada.getIdRegional());
    alteraLimiteEmpresa.setIdFilial(hprBuscada.getIdFilial());
    alteraLimiteEmpresa.setIdPontoDeRelacionamento(hprBuscada.getIdPontoDeRelacionamento());
    alteraLimiteEmpresa.setValorLimiteEmpresa(hprBuscada.getLimiteMaxCredito());
    alteraLimiteEmpresaService.alteraLimiteEmpresa(alteraLimiteEmpresa);

    hprBuscada = hierarquiaPontoRelacionamentoService.save(hprBuscada);

    // salvar no jcard
    PontoRelacionamentoResponse pontoRelacionamentoResponse =
        pontoRelacionamentoService.updatePontoRelacionamento(hprBuscada);
    if (!pontoRelacionamentoResponse.getSuccess()) {
      throw new GenericServiceException("Erro ao salvar a configuração.");
    }
    return hprBuscada;
  }

  @Transactional
  public void validarLimiteGlobal(
      BigDecimal limiteGlobal, HierarquiaPontoDeRelacionamento pontoRelacionamento) {

    BigDecimal valorTodosLimites =
        contaPagRepository.findSomaLimiteGlobalEmpresaConvenio(
            pontoRelacionamento.getIdProcessadora(),
            pontoRelacionamento.getIdInstituicao(),
            pontoRelacionamento.getIdRegional(),
            pontoRelacionamento.getIdFilial(),
            pontoRelacionamento.getIdPontoDeRelacionamento());
    valorTodosLimites = valorTodosLimites == null ? BigDecimal.ZERO : valorTodosLimites;

    if (limiteGlobal.compareTo(valorTodosLimites) < 0
        && ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("msg", "O Limite Global não pode ser menor que a soma dos limites de convênios.");
      map.put("DTL", "Valor total de limites em convênios: " + valorTodosLimites);
      map.put("permitirForcado", 1);
      map.put("inadimplente", "Empresa encontra-se inadimplente.");
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    } else if (limiteGlobal.compareTo(valorTodosLimites) < 0
        && !ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("msg", "O Limite Global não pode ser menor que a soma dos limites de convênios.");
      map.put("DTL", "Valor total de limites em convênios: " + valorTodosLimites);
      map.put("permitirForcado", 1);
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    } else if (limiteGlobal.compareTo(valorTodosLimites) > 0
        && ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("inadimplente", "1");
      map.put("msg", "Empresa encontra-se inadimplente.");
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    }
  }
}
