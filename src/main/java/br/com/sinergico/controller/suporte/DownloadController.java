package br.com.sinergico.controller.suporte;

import br.com.entity.suporte.ArquivoDownload;
import br.com.json.bean.transacional.GetExtratoCredencialInmais;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.service.suporte.ArquivoDownloadService;
import br.com.sinergico.vo.DadosExtratoVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.core.io.PathResource;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.format.annotation.DateTimeFormat.ISO;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/download")
public class DownloadController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private ArquivoDownloadService service;

  @Autowired private CredencialFacade credencialFacade;

  @Value("${api.dir.b2bclient.relatorios.modelos}")
  private String apiDirB2BclientRelatoriosModelos;

  @Value("${issuer.dir.modelos.cadastro}")
  private String issuerDirModelosCadastro;

  List<String> msg = new ArrayList<>();

  @ApiOperation(
      value = "Realiza o download de arquivo(s)",
      notes = "Retorna o arquivo solictados ou o zip dos arquivos solicitados")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{ids}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFile(
      @PathVariable Integer[] ids, HttpServletResponse response) {

    SecurityUser user = getAuthenticatedUser(request, em);

    if (ids.length > 10) {
      throw new NoResultException(
          "O sistema permite no máximo downloads de 10 arquivos simultaneos.");
    }

    return service.downloadArquivos(ids, user.getIdUsuario());
  }

  @ApiOperation(
      value = "Buscar todos os arquivos disponíveis para download",
      response = ArquivoDownload.class,
      notes =
          "Retorna todos os arquivos disponíveis (que a data de expiração seja menor que a atual) para download cadastrados do sistema",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCA_DISPONIVEL_DOWNLOAD"})
  public List<ArquivoDownload> getArquivoDownloadValidos(
      @RequestParam(name = "dateInicio", required = false) @DateTimeFormat(iso = ISO.DATE_TIME)
          LocalDateTime dateInicio,
      @RequestParam(name = "dateFim", required = false) @DateTimeFormat(iso = ISO.DATE_TIME)
          LocalDateTime dateFim) {

    SecurityUser user = getAuthenticatedUser(request, em);

    return service.allArquivoDownloadValidos(dateInicio, dateFim, user);
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para cadastro de portador.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/infinancas/modelo/portador",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFileB2BInFinancas(
      HttpServletResponse response) {

    PathResource file = null;

    String path =
        apiDirB2BclientRelatoriosModelos
            + "modelo_cadastro_portador_infinancas.xlsx"; // arquivo.getDiretorio()

    file = new PathResource(path);
    if (!file.exists()) {
      throw new NoResultException(
          "O arquivo modelo_cadastro_portador_infinancas.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para alterar status.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/issuer/modelo/alterar-status/{tipoArq}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFileAlterarStatus(
      @PathVariable("tipoArq") Long tipoArq, HttpServletResponse response) {

    PathResource file = null;
    if (tipoArq == 1) {
      String path =
          apiDirB2BclientRelatoriosModelos
              + "001_ALTERAR_STATUS_CONTA_[INSERIR_A_DATA_ATUAL]_[INSERIR_O_PRODUTO].xlsx"; // arquivo.getDiretorio()
      file = new PathResource(path);
    } else {
      String path =
          apiDirB2BclientRelatoriosModelos
              + "000_ALTERAR_STATUS_CREDENCIAL_[INSERIR_A_DATA_ATUAL]_[INSERIR_O_PRODUTO].xlsx"; // arquivo.getDiretorio()
      file = new PathResource(path);
    }
    if (!file.exists()) {
      throw new NoResultException("O arquivo modelo.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para carga de crédito.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/infinancas/modelo/pedido",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFilePedidoCargaB2BInFinancas(
      HttpServletResponse response) {

    PathResource file = null;

    String path = apiDirB2BclientRelatoriosModelos + "modelo_pedido_carga_infinancas.xlsx";

    file = new PathResource(path);
    if (!file.exists()) {
      throw new NoResultException(
          "O arquivo modelo_pedido_carga_infinancas.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para carga de crédito.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/b2b/modelo/carga",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFilePedidoCargaB2B(
      HttpServletResponse response) {

    PathResource file = null;

    String path = apiDirB2BclientRelatoriosModelos + "modelo_pedido_carga_b2b.xlsx";

    file = new PathResource(path);
    if (!file.exists()) {
      throw new NoResultException(
          "O arquivo modelo_pedido_carga_b2b.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para cadastro de funcionário.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/b2b/modelo/funcionario",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFileCadastroFuncionarioB2B(
      HttpServletResponse response) {

    PathResource file = null;

    String path = apiDirB2BclientRelatoriosModelos + "modelo_cadastro_funcionario_b2b.xlsx";

    file = new PathResource(path);
    if (!file.exists()) {
      throw new NoResultException(
          "O arquivo modelo_cadastro_funcionario_b2b.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(value = "Realiza o download de arquivo(s) do acquirer")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  @RequestMapping(
      value = "acquirer/{ids}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Secured({"ROLE_DOWNLOAD_ARQUIVO_MERCHANT"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFileAcquirer(
      @PathVariable Integer[] ids, HttpServletResponse response) {

    SecurityUserEstabelecimento user = getAuthenticatedUserEstabelecimento(request, em);

    if (ids.length > 10) {
      throw new NoResultException(
          "O sistema permite no máximo downloads de 10 arquivos simultaneos.");
    }

    return service.downloadArquivos(ids, user.getIdUsuario());
  }

  @ApiOperation(
      value = "Realiza download modelo de arquivo para produtos integração.",
      notes = "Retorna o arquivo de modelo cadastro portador para produtos integração.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/infinancas/modelo/cadastro/portador",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public @ResponseBody ResponseEntity<InputStreamResource> getFileCadastroPortadorInfinancas(
      HttpServletResponse response) {

    PathResource file = null;

    String path = apiDirB2BclientRelatoriosModelos + "modelo_cadastro_portador_b2b.xlsx";

    file = new PathResource(path);
    if (!file.exists()) {
      throw new NoResultException(
          "O arquivo modelo_cadastro_portador_b2b.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo de extrato.",
      notes = "Retorna o arquivo de extrato.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/extrato",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Secured({"ROLE_DOWNLOAD_EXTRATO"})
  public ResponseEntity<InputStreamResource> downloadExtrato(
      @RequestBody DadosExtratoVO dadosExtrato) {

    SecurityUser user = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception ignored) {
    }

    List<GetExtratoCredencialInmais> extrato =
        credencialFacade.getExtratoInmaisByAnoMesADM(
            dadosExtrato.getDocumento(),
            dadosExtrato.getIdConta(),
            dadosExtrato.getAnoMes(),
            user,
            Boolean.FALSE);

    try {
      InputStream input = service.criarArquivoExtrato(dadosExtrato, extrato);
      HttpHeaders headers = new HttpHeaders();
      headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
      return new ResponseEntity<>(new InputStreamResource(input), headers, HttpStatus.OK);

    } catch (IOException | InvalidFormatException e) {
      NoResultException error = new NoResultException("Falha ao realizar download do arquivo");
      error.initCause(e);
      throw error;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para vincular contas responsavel e dependente.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/issuer/modelo/cadastro",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public @ResponseBody ResponseEntity<InputStreamResource> getFileCadastro(
      HttpServletResponse response) {

    PathResource file = null;

    String caminho = issuerDirModelosCadastro;
    String nomeArquivo = "ARQUIVO_DE_CADASTRO_ESCOTEIROS.xlsx";
    String path = caminho + nomeArquivo;
    file = new PathResource(path);

    if (!file.exists()) {
      throw new NoResultException("O arquivo modelo.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo.",
      notes = "Retorna o arquivo de modelo para vincular contas responsavel e dependente.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/issuer/modelo/vinculo",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public @ResponseBody ResponseEntity<InputStreamResource> getFileVinculo(
      HttpServletResponse response) {

    PathResource file = null;
    String caminho = issuerDirModelosCadastro;
    String nomeArquivo = "ARQUIVO_DE_VINCULAÇÃO_ESCOTEIROS.xlsx";
    String path = caminho + nomeArquivo;
    file = new PathResource(path);

    if (!file.exists()) {
      throw new NoResultException("O arquivo modelo.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<InputStreamResource>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar download do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }

  @ApiOperation(
      value = "Realiza download de arquivo com template de cadastro PJ.",
      notes = "Retorna o arquivo de modelo para cadastro PJ.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/modelo/cadastro/pessoa-juridica",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  // @Secured({"ROLE_DOWNLOAD_ARQUIVO"})
  public @ResponseBody ResponseEntity<InputStreamResource> getFileCadastroPessoaJuridica() {

    PathResource file;
    String path = issuerDirModelosCadastro + "modelo_cadastro_pj.xlsx";
    file = new PathResource(path);
    if (!file.exists()) {
      throw new NoResultException(
          "O arquivo modelo_cadastro_pj.xlsx não foi encontrado para download.");
    }

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", msg);

    try {
      return new ResponseEntity<>(
          new InputStreamResource(file.getInputStream()), headers, HttpStatus.OK);
    } catch (IOException e) {
      NoResultException nre = new NoResultException("Falha ao realizar dowload do(s) arquivo(s)");
      nre.initCause(e);
      throw nre;
    }
  }
}
