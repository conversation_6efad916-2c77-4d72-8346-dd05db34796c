package br.com.sinergico.service.suporte;

import static br.com.sinergico.util.ConstantesErro.INS_INSTITUICAO_NAO_ENCONTRADA;
import static br.com.sinergico.util.MyHibernateUtils.listAndCast;

import br.com.client.rest.jcard.json.bean.CreateIssuer;
import br.com.client.rest.jcard.json.bean.CreateIssuerResponse;
import br.com.entity.adq.Estabelecimento;
import br.com.entity.suporte.AcessoGrupo;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AntifraudeCafInstituicaoConfig;
import br.com.entity.suporte.Contato;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.HierarquiaProcessadora;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.nfse.InformacoesInstituicaoRequest;
import br.com.json.bean.nfse.InformacoesInstituicaoResponse;
import br.com.json.bean.suporte.CadastrarInstituicao;
import br.com.json.bean.suporte.ConfiguracaoInstituicaoDTO;
import br.com.json.bean.suporte.ContatoResponse;
import br.com.json.bean.suporte.Instituicao;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.criteria.UsuarioCriteria;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.repository.suporte.AntifraudeCafPortadorRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.adq.EstabelecimentoService;
import br.com.sinergico.service.jcard.IssuerService;
import br.com.sinergico.service.nfse.NfseService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.Hierarquia;
import br.com.sinergico.util.MyHibernateUtils;
import br.com.sinergico.vo.HierarquiaInstituicaoUsuarioVO;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.persistence.NoResultException;
import org.apache.poi.util.IOUtils;
import org.hibernate.Criteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.Errors;

@Service
public class HierarquiaInstituicaoService
    extends GenericService<HierarquiaInstituicao, HierarquiaInstituicaoId> {

  private static final int ATIVO = 1;

  private static final int INATIVO = 0;

  private static final int NIVEL_INSTITUICAO = 2;

  @Lazy @Autowired private UsuarioCriteria usuarioCriteria;

  private HierarquiaInstituicaoRepository repository;

  @Autowired private IssuerService issuerService;

  @Autowired private HierarquiaProcessadoraService processadoraService;

  @Autowired private GrupoService grupoService;

  @Autowired private ContatoService contatoService;

  @Autowired private EstabelecimentoService estabelecimentoService;

  @Autowired private NfseService nfseService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private AntifraudeCafPortadorRepository antifraudeCafPortadorRepository;

  @Value("${issuer.dir.emissor.logos}")
  private String issuerDirEmissorLogos;

  @Value("${issuer.dir.processadora.logos}")
  private String issuerDirProcessadoraLogos;

  @Autowired
  public HierarquiaInstituicaoService(HierarquiaInstituicaoRepository repo) {
    super(repo);
    this.repository = repo;
  }

  public HierarquiaInstituicao findByIdInstituicaoOrElseThrow(Integer idInstituicao) {
    return findByIdOrElseThrow(
        new HierarquiaInstituicaoId(Constantes.ID_PROCESSADORA_ITS_PAY, idInstituicao));
  }

  public HierarquiaInstituicao findByIdOrElseThrow(
      HierarquiaInstituicaoId hierarquiaInstituicaoId) {
    HierarquiaInstituicao hierarquiaInstituicao = findById(hierarquiaInstituicaoId);
    if (hierarquiaInstituicao == null) {
      throw new GenericServiceException(
          INS_INSTITUICAO_NAO_ENCONTRADA.format(hierarquiaInstituicaoId.getIdInstituicao()));
    }
    return hierarquiaInstituicao;
  }

  @Transactional
  public HierarquiaInstituicao create(
      SecurityUser user, CadastrarInstituicao model, Errors result) {

    HierarquiaInstituicao inst = new HierarquiaInstituicao();
    inst.setId(new HierarquiaInstituicaoId(user.getIdProcessadora(), model.getIdInstituicao()));
    inst.setIdProcessadora(user.getIdProcessadora());

    if (findById(inst.getId()) != null) {
      throw new InvalidRequestException(
          "Já existe uma Instituição cadastrada com os dados inseridos. IdInstituicao: "
              + model.getIdInstituicao(),
          result);
    }
    prepareInstituicao(model, inst, user);

    // Inserir valor inicial para sequencial da conta
    inst.setSeqConta(Constantes.SEQ_CONTA_INICIAL_HIERARQUIA_INSTITUICAO);

    for (Contato contato : model.getContatos()) {
      contato.setIdProcessadora(inst.getIdProcessadora());
      contato.setIdInstituicao(inst.getIdInstituicao());
      contato.setIdNivelHierarquia(NIVEL_INSTITUICAO);
      contato.setIdUsuarioInclusao(user.getIdUsuario());
      contato.setStatus(ATIVO);
      contato.setDataHoraStatus(LocalDateTime.now());

      contatoService.save(contato);
    }

    inst.setIdUsuarioManutencao(user.getIdUsuario());
    inst = save(inst);
    criarGrupoInstituicao(inst);

    createIssuer(inst);
    inst = save(inst);

    return inst;
  }

  @Transactional
  public void criarGrupoInstituicao(HierarquiaInstituicao inst) {

    AcessoGrupo acessoGrupo = new AcessoGrupo();
    acessoGrupo.setIdProcessadora(inst.getIdProcessadora());
    acessoGrupo.setIdInstituicao(inst.getIdInstituicao());
    acessoGrupo.setIdNivelHierarquia(NIVEL_INSTITUICAO);
    acessoGrupo.setDescGrupo("GRUPO ADM INST. - " + inst.getIdInstituicao().toString());

    acessoGrupo.setIdGrupoPai(
        grupoService.findGrupoAdmHierarquiaAnterior(
            acessoGrupo.getIdNivelHierarquia(),
            acessoGrupo.getIdProcessadora(),
            acessoGrupo.getIdInstituicao(),
            acessoGrupo.getIdRegional(),
            acessoGrupo.getIdFilial(),
            acessoGrupo.getIdPontoDeRelacionamento()));

    acessoGrupo = grupoService.save(acessoGrupo);
  }

  public void createIssuer(HierarquiaInstituicao inst) {
    CreateIssuer createIssuer = getCreateIssuer(inst);

    CreateIssuerResponse createIssuerResponse = issuerService.createIssuer(createIssuer);

    if (!createIssuerResponse.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar Issuer. " + createIssuerResponse.getErrors());
    }

    inst.setAccount(createIssuerResponse.getIssuer().getAccount());
    inst.setCardholderCreditAccount(createIssuerResponse.getIssuer().getCardholderCreditAccount());
    inst.setCardholderDebitAccount(createIssuerResponse.getIssuer().getCardholderDebitAccount());
    inst.setFeeAccount(createIssuerResponse.getIssuer().getFeeAccount());
    inst.setInstitutionId(createIssuerResponse.getIssuer().getInstitutionId());
    inst.setJournal(createIssuerResponse.getIssuer().getJournal());
  }

  public CreateIssuer getCreateIssuer(HierarquiaInstituicao inst) {
    CreateIssuer createIssuer = new CreateIssuer();
    createIssuer.setInstitutionId(
        IssuerService.getValidInstituicionId(
            inst.getId().getIdProcessadora(), inst.getId().getIdInstituicao()));
    createIssuer.setName(inst.getDescInstituicao());
    return createIssuer;
  }

  public HierarquiaInstituicao update(SecurityUser user, Integer id, CadastrarInstituicao model) {

    HierarquiaInstituicaoId idEntidade = new HierarquiaInstituicaoId(user.getIdProcessadora(), id);

    HierarquiaInstituicao inst = findById(idEntidade);

    if (inst == null) {
      throw new NoResultException("Instituição inserida não existe na base");
    }

    prepareInstituicao(model, inst, user);
    inst.setIdUsuarioInclusao(user.getIdUsuario());
    inst.setDtHrInclusao(new Date());
    return save(inst);
  }

  public List<HierarquiaInstituicao> findAll() {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaInstituicao.class, null, null);
    criteria.addOrder(Order.asc("idInstituicao"));
    List<HierarquiaInstituicao> result = listAndCast(criteria);
    return result;
  }

  public HierarquiaInstituicao findOne() {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaInstituicao.class, null, null);
    HierarquiaInstituicao result = (HierarquiaInstituicao) criteria.uniqueResult();
    return result;
  }

  public List<HierarquiaInstituicao> findByIdProcessadora(Integer idProcessadora) {
    return repository.findByIdProcessadora(idProcessadora);
  }

  public Instituicao findByIdProcessadoraAndIdInstituicaoWithContatos(
      Integer idProcessadora, Integer idInstituicao) {

    List<ContatoResponse> contatosRes = new ArrayList<>();
    Instituicao model = new Instituicao();

    HierarquiaInstituicao instituicao =
        repository.findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    List<Contato> contatos = contatoService.getContatosInstituicao(idProcessadora, idInstituicao);

    for (Contato conts : contatos) {
      ContatoResponse contato = new ContatoResponse();
      BeanUtils.copyProperties(conts, contato, getNullPropertyNames(contatos));
      contatosRes.add(contato);
    }

    BeanUtils.copyProperties(instituicao, model, getNullPropertyNames(instituicao));

    if (contatos.size() > 0) {
      model.setContatos(contatosRes);
    }

    return model;
  }

  public HierarquiaInstituicao findByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao) {
    return repository.findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);
  }

  public Instituicao findInstituicaoByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao) {
    HierarquiaInstituicao hierarquiaInstituicao =
        repository.findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);
    if (hierarquiaInstituicao != null) {
      return new Instituicao(hierarquiaInstituicao);
    }
    return null;
  }

  public String findPlasticoImgArqPadraoByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao) {
    return repository.findPlasticoImgArqPadraoByIdProcessadoraAndIdInstituicao(
        idProcessadora, idInstituicao);
  }

  public String findPlaticoImgMobileArqPadraoByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao) {
    return repository.findPlaticoImgMobileArqPadraoByIdProcessadoraAndIdInstituicao(
        idProcessadora, idInstituicao);
  }

  private HierarquiaInstituicao prepareInstituicao(
      CadastrarInstituicao model, HierarquiaInstituicao inst, SecurityUser user) {

    new Hierarquia.Builder()
        .usuario(user)
        .nivel(HierarquiaType.PROCESSADORA.value())
        .checkHierarquiaPermission();
    BeanUtils.copyProperties(model, inst, getNullPropertyNames(model));
    if (inst.getEmiteNotaFiscal()) {
      inst.setItemListaServico("10.02");
      inst.setExigibilidadeIss(1);
      inst.setStatusRps(1);
      inst.setTipoRps(1);
      inst.setSerieRps("UNICO");
      inst.setIdTagRps(inst.getIdInstituicao().toString());
    } else {
      inst.setItemListaServico(null);
      inst.setExigibilidadeIss(null);
      inst.setStatusRps(null);
      inst.setTipoRps(null);
      inst.setSerieRps(null);
      inst.setIdTagRps(null);
    }
    return inst;
  }

  @Transactional
  public void salvarLogo(
      InputStream is,
      String originalFilename,
      Integer idProcessadora,
      Integer idInstituicao,
      SecurityUser user) {

    int posicao = originalFilename.lastIndexOf('.');
    String extensao = originalFilename.substring(posicao);

    if (extensao.equals(".png") || extensao.equals(".jpeg")) {
      String pasta = issuerDirEmissorLogos;

      String caminhoBanco = idProcessadora.toString() + idInstituicao.toString() + extensao;
      String caminhoFinal = pasta + caminhoBanco;

      try {
        FileOutputStream fout = new FileOutputStream(caminhoFinal);
        while (is.available() != 0) {
          fout.write(is.read());
        }
        is.close();
        fout.close();
      } catch (Exception e) {
        throw new GenericServiceException(
            "Não foi possível gravar a imagem no caminho indicado: " + caminhoFinal, e);
      }

      HierarquiaInstituicao inst =
          findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);
      inst.setUrlLogo(caminhoBanco);
      save(inst);

    } else {
      throw new GenericServiceException(
          "A imagem com o formato "
              + extensao
              + " não é suportada. Faça upload apenas de imagens com formato .png ou .jpeg");
    }
  }

  public InputStream mostrarImagem(Integer idProcessadora, Integer idInstituicao) {

    HierarquiaInstituicao inst =
        findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    String caminhoFinal = issuerDirEmissorLogos + inst.getUrlLogo();

    try {

      if (caminhoFinal != null && !caminhoFinal.trim().isEmpty()) {

        BufferedInputStream in = new BufferedInputStream(new FileInputStream(caminhoFinal));

        // Get image contents.
        byte[] bytes = new byte[in.available()];
        int count = 0;

        do {
          count = in.read(bytes);
        } while (count > 0);
        in.close();

        return new ByteArrayInputStream(bytes);
      }
    } catch (IOException e) {

      throw new GenericServiceException(
          "Não foi possível encontrar a imagem. Caminho: " + caminhoFinal, e);
    }

    return null;
  }

  public InputStream recuperarImagemLogoByUser(SecurityUser user) {

    HierarquiaInstituicao inst = new HierarquiaInstituicao();
    HierarquiaProcessadora processadora = new HierarquiaProcessadora();
    String caminhoFinal = new String();

    if (user.getIdInstituicao() == null) {
      processadora = processadoraService.findByIdProcessadora(user.getIdProcessadora());
      caminhoFinal = issuerDirProcessadoraLogos + processadora.getUrlLogo();
    } else {
      inst =
          findByIdProcessadoraAndIdInstituicao(user.getIdProcessadora(), user.getIdInstituicao());
      caminhoFinal = issuerDirEmissorLogos + inst.getUrlLogo();
    }

    try {

      if (caminhoFinal != null && !caminhoFinal.trim().isEmpty()) {

        BufferedInputStream in = new BufferedInputStream(new FileInputStream(caminhoFinal));

        // Get image contents.
        byte[] bytes = new byte[in.available()];
        int count = 0;

        do {
          count = in.read(bytes);
        } while (count > 0);
        in.close();

        return new ByteArrayInputStream(bytes);
      }
    } catch (IOException e) {

      throw new GenericServiceException(
          "Não foi possível encontrar a imagem. Caminho: " + caminhoFinal, e);
    }

    return null;
  }

  public List<HierarquiaInstituicao> findAll(String termo, Integer first, Integer max) {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaInstituicao.class, max, first);
    criteria.addOrder(Order.asc("idInstituicao"));
    if (termo != null) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        criteria.add(Restrictions.in("idInstituicao", termoListInteiros));
      } else {
        criteria.add(Restrictions.ilike("descInstituicao", "%" + termo + "%"));
      }
    }
    List<HierarquiaInstituicao> result = listAndCast(criteria);
    return result;
  }

  public Integer countBy(String termo) {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaInstituicao.class, null, null);
    if (termo != null) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        criteria.add(Restrictions.in("idInstituicao", termoListInteiros));
      } else {
        criteria.add(Restrictions.ilike("descInstituicao", "%" + termo + "%"));
      }
    }
    criteria.setProjection(Projections.rowCount());
    return MyHibernateUtils.countRegisters(criteria);
  }

  public List<HierarquiaInstituicao> findByIdProcessadoraAndIdInstituicaoNotIn(
      Integer idProcessadora, Collection<Integer> exclusao) {
    return repository.findByIdProcessadoraAndIdInstituicaoNotIn(idProcessadora, exclusao);
  }

  public String findUrlLogoByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao) {
    return repository.findUrlLogoByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);
  }

  public InputStream recuperarImagemLogoByUserEstabelecimento(SecurityUserEstabelecimento user) {
    HierarquiaInstituicao inst = new HierarquiaInstituicao();

    String caminhoFinal = new String();

    Estabelecimento estabelecimento =
        estabelecimentoService.findEstabelecimento(user.getIdEstabelecimento());

    inst =
        findByIdProcessadoraAndIdInstituicao(
            estabelecimento.getIdProcessadora(), estabelecimento.getIdInstituicao());
    caminhoFinal = issuerDirEmissorLogos + inst.getUrlLogo();

    try {

      if (caminhoFinal != null && !caminhoFinal.trim().isEmpty()) {

        BufferedInputStream in = new BufferedInputStream(new FileInputStream(caminhoFinal));

        // Get image contents.
        byte[] bytes = new byte[in.available()];
        int count = 0;

        do {
          count = in.read(bytes);
        } while (count > 0);
        in.close();

        return new ByteArrayInputStream(bytes);
      }
    } catch (IOException e) {

      throw new GenericServiceException(
          "Não foi possível encontrar a imagem. Caminho: " + caminhoFinal, e);
    }

    return null;
  }

  public InformacoesInstituicaoResponse inserirlogoInsituicaoAPINotaFiscal(
      Integer idProcessadora, Integer idInstituicao) {

    String nomeArquivo = new String();
    String caminho = new String();
    InformacoesInstituicaoRequest request = new InformacoesInstituicaoRequest();

    nomeArquivo = findUrlLogoByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    caminho = issuerDirEmissorLogos;

    if (nomeArquivo == null)
      throw new GenericServiceException("Nome de arquivo para logo de Instituição não encontrado.");

    if (caminho == null)
      throw new GenericServiceException(
          "Caminho do arquivo para logo de Instituição não encontrado.");

    String caminhoFinal = caminho + nomeArquivo;

    BufferedInputStream in;
    try {
      in = new BufferedInputStream(new FileInputStream(caminhoFinal));
      byte[] bytes = IOUtils.toByteArray(in);
      request.setIdProcessadora(idProcessadora);
      request.setIdInstituicao(idInstituicao);
      request.setLogo(bytes);
    } catch (FileNotFoundException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    } catch (IOException e) {
      // TODO Auto-generated catch block
      e.printStackTrace();
    }

    return nfseService.enviarDadosLogoInstituicao(request);
  }

  public Long getIdLayoutPadrao(Integer idProcessadora, Integer idInstituicao) {
    return repository.getLayoutPadrao(idProcessadora, idInstituicao);
  }

  public Boolean instituicaoPermiteEnvioSMS(Integer idProcessadora, Integer idInstituicao) {

    HierarquiaInstituicaoId id = new HierarquiaInstituicaoId(idProcessadora, idInstituicao);
    HierarquiaInstituicao inst = findById(id);

    if (inst == null) {
      throw new GenericServiceException("A instituição não foi encontrada");
    }

    return inst.getPermiteEnvioSMS();
  }

  public List<HierarquiaInstituicao> findAllByNotificacaoHabilitada() {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaInstituicao.class, null, null);
    criteria.addOrder(Order.asc("idInstituicao"));
    List<HierarquiaInstituicao> result = listAndCast(criteria);
    result =
        result.stream()
            .filter(inst -> inst.getBlNotificacao() && inst.getIdStatus() == 1)
            .collect(Collectors.toList());
    return result;
  }

  public HierarquiaInstituicaoUsuarioVO getDadosInstituicaoUsuarioByHierarquia(Integer idUsuario) {

    AcessoUsuario userIssuer = acessoUsuarioService.findByIdUsuario(idUsuario);

    if (userIssuer == null) {
      throw new GenericServiceException("Usuário não encontrado", HttpStatus.NOT_FOUND);
    }

    HierarquiaInstituicao hierarquiaInstituicao =
        this.findByIdProcessadoraAndIdInstituicao(
            Constantes.ID_PROCESSADORA_ITS_PAY, userIssuer.getIdInstituicao());

    if (hierarquiaInstituicao == null) {
      throw new GenericServiceException("Instituição não encontrada", HttpStatus.NOT_FOUND);
    }

    HierarquiaInstituicaoUsuarioVO hierarquiaInstituicaoUsuarioVO =
        new HierarquiaInstituicaoUsuarioVO();
    hierarquiaInstituicaoUsuarioVO.setIdInstituicao(hierarquiaInstituicao.getIdInstituicao());
    hierarquiaInstituicaoUsuarioVO.setDescInstituicao(hierarquiaInstituicao.getDescInstituicao());

    return hierarquiaInstituicaoUsuarioVO;
  }

  public List<HierarquiaInstituicaoUsuarioVO> buscarDadosInstituicaoCafAtivo() {

    List<HierarquiaInstituicaoUsuarioVO> listaHierarquiaInstituicaoCaf = new ArrayList<>();

    List<AntifraudeCafInstituicaoConfig> antifraudeCafInstituicaoConfigList =
        antifraudeCafPortadorRepository.findAntifraudeCafInstituicaoConfig();

    for (AntifraudeCafInstituicaoConfig acic : antifraudeCafInstituicaoConfigList) {
      HierarquiaInstituicao hierarquiaInstituicao =
          this.findByIdProcessadoraAndIdInstituicao(
              Constantes.ID_PROCESSADORA_ITS_PAY, acic.getIdInstituicao());

      HierarquiaInstituicaoUsuarioVO hierarquiaInstituicaoUsuarioVO =
          new HierarquiaInstituicaoUsuarioVO();
      hierarquiaInstituicaoUsuarioVO.setIdInstituicao(hierarquiaInstituicao.getIdInstituicao());
      hierarquiaInstituicaoUsuarioVO.setDescInstituicao(hierarquiaInstituicao.getDescInstituicao());

      listaHierarquiaInstituicaoCaf.add(hierarquiaInstituicaoUsuarioVO);
    }

    listaHierarquiaInstituicaoCaf =
        listaHierarquiaInstituicaoCaf.stream()
            .sorted(Comparator.comparing(HierarquiaInstituicaoUsuarioVO::getDescInstituicao))
            .collect(Collectors.toList());

    return listaHierarquiaInstituicaoCaf;
  }

  public HierarquiaInstituicao alterarStatus(SecurityUser user, Integer id) {

    HierarquiaInstituicaoId idEntidade = new HierarquiaInstituicaoId(user.getIdProcessadora(), id);
    HierarquiaInstituicao inst = findById(idEntidade);

    if (inst == null) {
      throw new NoResultException("Instituição buscada não existe na base");
    }

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user,
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdRegional(),
        user.getIdFilial(),
        user.getIdPontoDeRelacionamento());

    inst.setIdStatus(inst.getIdStatus() == INATIVO ? ATIVO : INATIVO);
    inst.setIdUsuarioManutencao(user.getIdUsuario());
    inst.setDtHrStatus(LocalDateTime.now());
    return save(inst);
  }

  public HierarquiaInstituicao updatePermiteProcessamentoLancamentos(
      SecurityUser user, Integer idInstituicao) {

    HierarquiaInstituicao inst =
        repository.findByIdProcessadoraAndIdInstituicao(user.getIdProcessadora(), idInstituicao);

    if (inst == null) {
      throw new NoResultException("Instituição buscada não existe na base");
    }

    if (inst.getIdInstituicao().equals(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS)) {
      throw new GenericServiceException(
          "Esta instituição não pode realizar processamentos de cargas.");
    }

    inst.setIdUsuarioManutencao(user.getIdUsuario());
    inst.setPermiteProcessamentoLancamentos(
        inst.getPermiteProcessamentoLancamentos() != null
                && inst.getPermiteProcessamentoLancamentos()
            ? null
            : true);
    inst.setDtHrStatus(LocalDateTime.now());
    return save(inst);
  }

  public Boolean instituicaoEnviaDadosTotvs(Integer idProcessadora, Integer idInstituicao) {
    return repository.enviaDadosTotvs(idProcessadora, idInstituicao);
  }

  public HierarquiaInstituicao configuracaoInstituicaoAtualizar(
      ConfiguracaoInstituicaoDTO configuracaoInstituicaoDTO) {

    HierarquiaInstituicao inst =
        repository.findByIdInstituicao(configuracaoInstituicaoDTO.getIdHierarquiaInstitucao());

    if (inst != null) {
      inst.setHabilitacaoGrupo(configuracaoInstituicaoDTO.getHabilitacaoGrupo());
      save(inst);
    }

    return inst;
  }

  public HierarquiaInstituicao configuracaoInstituicaoBuscar(
      ConfiguracaoInstituicaoDTO configuracaoInstituicaoDTO) {

    HierarquiaInstituicao inst =
        repository.findByIdInstituicao(configuracaoInstituicaoDTO.getIdHierarquiaInstitucao());

    return inst;
  }
}
