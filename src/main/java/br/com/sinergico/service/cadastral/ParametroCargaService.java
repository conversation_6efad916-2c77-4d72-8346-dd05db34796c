package br.com.sinergico.service.cadastral;

import static br.com.sinergico.service.cadastral.PreLancamentoLoteService.*;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.B2bFaturaPix;
import br.com.entity.cadastral.CargaAutoProdutoParametro;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.FaturaB2B;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PreLancamento;
import br.com.entity.cadastral.PreLancamentoLote;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.pix.ContaTransacionalMovimento;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.CargaAutoParametros;
import br.com.entity.suporte.Contato;
import br.com.entity.suporte.CotacaoPontos;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.transacional.CobrancaBancaria;
import br.com.entity.transacional.PontosRecebidos;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.adq.CargasVO;
import br.com.json.bean.enums.pix.TipoMovimentoEnum;
import br.com.json.bean.pix.request.IncluirOrdemDevolucaoRequest;
import br.com.json.bean.pix.request.MotivoDevolucao;
import br.com.json.bean.pix.response.IncluirOrdemPagamentoResponse;
import br.com.sinergico.repository.cadastral.ParametroCargaRepository;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.pix.ContaTransacionalDevolucaoService;
import br.com.sinergico.service.pix.ContaTransacionalMovimentoService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.CargaAutoParametrosService;
import br.com.sinergico.service.suporte.ContatoService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.transacional.CobrancaBancariaService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.service.transacional.PontosRecebidosService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.vo.FaturasEPedidosFiltroVO;
import br.com.sinergico.vo.LancamentoPedidosVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ParametroCargaService extends GenericService<PreLancamentoLote, Integer> {

  private static final Logger log = LoggerFactory.getLogger(ParametroCargaService.class);

  private static final boolean PGTO_MANUAL = true;
  private static final int STATUS_CONFIRMADO = 1;
  private static final int STATUS_LIBERADO_CARGA_PRODUTO_TRANSFERENCIA = 5;
  public static final int PRODUTO_PLATAFORMA = 6;
  public static final int STATUS_ATIVO = 1;
  public static final String TEXTO_EXTRATO_PRE = "Carga referente ao pedido ";
  public static final String TEXTO_EXTRATO_POS = " recebido via TED";
  public static final int SINAL_CREDITO = 1;
  public static final int COD_TRANSACAO_CARGA_PRE = 516;

  @Autowired private LoteVoucherPapelService loteVoucherPapelService;

  @Autowired private FaturaB2BService faturaB2BService;

  @Autowired private B2bFaturaPixService b2bFaturaPixService;

  @Autowired private HierarquiaPontoRelacionamentoService pontoRelacionamentoService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private ContatoService contatoService;

  @Autowired ProdutoInstituicaoService produtoInstuicaoService;

  @Autowired private PreLancamentoLoteService preLancamentoLoteService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private PessoaService pessoaService;

  @Autowired private PontosRecebidosService pontosRecebidosService;

  @Autowired private EmailService emailService;

  @Autowired private CobrancaBancariaService cobrancaBancariaService;

  @Autowired private CargaAutoParametrosService cargaAutoParametrosService;

  @Autowired private CargaAutoProdutoParametroService cargaAutoProdutoParametroService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ContaTransacionalMovimentoService contaTransacionalMovimentoService;

  @Autowired private ContaTransacionalDevolucaoService contaTransacionalDevolucaoService;

  @Autowired private CotacaoPontosRepository cotacaoPontosRepository;

  @Autowired private LancamentoService lancamentoService;

  private ParametroCargaRepository repository;

  @PersistenceContext private EntityManager em;

  @Autowired
  public ParametroCargaService(ParametroCargaRepository repo) {
    super(repo);
    repository = repo;
  }

  public List<LancamentoPedidosVO> buscaPedidosIntegracao(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Boolean carga,
      SecurityUser user) {
    List<LancamentoPedidosVO> lista = new ArrayList<>();

    if (carga) {
      model.setIsCount(Boolean.FALSE);
      lista =
          repository.buscarListaPedidosEFaturasIntegracao(
              model, idProcessadora, idInstituicao, List.class);
    } else {
      model.setIsCount(Boolean.FALSE);
      lista =
          repository.buscarListaPedidosEFaturasReplicacao(
              model, idProcessadora, idInstituicao, List.class);
    }
    return lista;
  }

  public Long countPedidosEFaturas(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Boolean carga,
      SecurityUser user) {

    if (carga) {
      model.setIsCount(Boolean.TRUE);
      return repository.buscarListaPedidosEFaturasIntegracao(
          model, idProcessadora, idInstituicao, Long.class);
    } else {
      model.setIsCount(Boolean.TRUE);
      return repository.buscarListaPedidosEFaturasReplicacao(
          model, idProcessadora, idInstituicao, Long.class);
    }
  }

  public List<LancamentoPedidosVO> buscarCargas(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Boolean carga,
      SecurityUser user) {
    List<LancamentoPedidosVO> lista;

    if (carga) {
      model.setIsCount(Boolean.FALSE);
      lista = repository.buscarCargasIntegracao(model, idProcessadora, idInstituicao, List.class);
      return lista;
    } else {
      model.setIsCount(Boolean.FALSE);
      lista = repository.buscarCargasReplicacao(model, idProcessadora, idInstituicao, List.class);
      return lista;
    }
  }

  public Long countCargas(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Boolean carga,
      SecurityUser user) {

    if (carga) {
      model.setIsCount(Boolean.TRUE);
      return repository.buscarCargasIntegracao(model, idProcessadora, idInstituicao, Long.class);
    } else {
      model.setIsCount(Boolean.TRUE);
      return repository.buscarCargasReplicacao(model, idProcessadora, idInstituicao, Long.class);
    }
  }

  public List<LancamentoPedidosVO> buscarCargasPagasAgendadasTransferencia(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Boolean carga,
      SecurityUser user) {
    List<LancamentoPedidosVO> lista = new ArrayList<>();

    if (!carga) {
      model.setIsCount(Boolean.FALSE);
      lista =
          repository.buscarCargasTransferencia(model, idProcessadora, idInstituicao, List.class);
    }
    return lista;
  }

  public Long countPedidosTransferenciaPagas(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      SecurityUser user) {

    model.setIsCount(Boolean.TRUE);
    return repository.buscarCargasTransferencia(model, idProcessadora, idInstituicao, Long.class);
  }

  public List<LancamentoPedidosVO> buscarPedidosTransferencia(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Boolean carga,
      SecurityUser user) {
    List<LancamentoPedidosVO> lista = new ArrayList<>();

    if (!carga) {
      model.setIsCount(Boolean.FALSE);
      lista =
          repository.buscarPedidosTransferencia(model, idProcessadora, idInstituicao, List.class);
    }
    return lista;
  }

  public Long countPedidosTransferencia(
      FaturasEPedidosFiltroVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      SecurityUser user) {

    model.setIsCount(Boolean.TRUE);
    return repository.buscarPedidosTransferencia(model, idProcessadora, idInstituicao, Long.class);
  }

  public List<PreLancamentoLote> reagendarCarga(CargasVO model, SecurityUser user) {

    List<PreLancamentoLote> listaRetorno = new ArrayList<>();

    for (Integer obg : model.getIdLote()) {
      PreLancamentoLote preLancamentoLote = repository.findByIdLote(obg);

      preLancamentoLote.setDataAgendamento(model.getDataAgendamento());
      preLancamentoLote.setIdUsuarioManutencao(user.getIdUsuario());
      preLancamentoLote.setDataHoraManutencao(new Date());
      listaRetorno.add(preLancamentoLote);
      repository.save(preLancamentoLote);
      tratarReagendamentoDePontosRecebidos(preLancamentoLote);
    }

    return listaRetorno;
  }

  private void tratarReagendamentoDePontosRecebidos(PreLancamentoLote pedido) {
    if (pontosRecebidosService.existsByNumeroPedido(pedido.getIdLote().longValue())) {
      List<PontosRecebidos> pontosRecebidos =
          pontosRecebidosService.resgataPontosRecebidosPorNumeroPedido(
              pedido.getIdLote().longValue());
      pontosRecebidos.forEach(ponto -> ponto.setDataMovimento(pedido.getDataAgendamento()));
      pontosRecebidosService.saveAll(pontosRecebidos);
    }
  }

  @Transactional
  public List<PreLancamentoLote> alterarStatusLote(
      CargasVO model, Integer status, Integer idUsuario) {

    List<PreLancamentoLote> list = new ArrayList<>();

    for (Integer obj : model.getIdLote()) {

      PreLancamentoLote lote = findPreLancamentoLoteByIdNotNull(obj);

      if (lote.getTipoPedido() != null) {
        if (Constantes.TIPO_PEDIDO_VOUCHER_PAPEL.equals(lote.getTipoPedido())
            && ConstantesB2B.STATUS_PROCESSADO.equals(lote.getStatus())) {
          return list;
        }
      }

      String dtAgendamentolote = DateUtil.dateFormat("dd-MM-yyyy", lote.getDataAgendamento());
      String dtHoje = DateUtil.dateFormat("dd-MM-yyyy", new Date());

      List<Integer> possiveisStatus = getMapasPossiveis().get(lote.getStatus());

      if (!possiveisStatus.contains(status)) {
        throw new GenericServiceException(
            "Não é possível mudar status de " + lote.getStatus() + " para " + status);
      }

      if (status.equals(STATUS_CANCELADO)
          && lote.getTipoPedido() != null
          && Constantes.TIPO_PEDIDO_VOUCHER_PAPEL.equals(lote.getTipoPedido())) {

        loteVoucherPapelService.cancelarLotesVoucherPapel(obj);
      }

      if (!status.equals(STATUS_CONFIRMADO)
          || !cargaAutoProdutoParametroService.existsLiberacaoManualByProduto(
              lote.getIdProdutoInstituicao())) {
        lote.setStatus(status);
      }
      lote.setIdUsuarioManutencao(idUsuario);
      lote.setDataHoraManutencao(new Date());
      lote.setDataHoraStatus(new Date());
      lote.setMotivoCancelamento(model.getMotivoCancelamento());
      FaturaB2B fatura = null;
      // verificar se o status é cancelado e se existe fatura para cancelar.
      Boolean sucesso = Boolean.TRUE;
      if (status.equals(STATUS_CANCELADO)
          && lote.getIdFatura() != null
          && lote.getIdFatura() != 0) {
        fatura = faturaB2BService.findById(lote.getIdFatura());

        if (fatura == null) {
          throw new GenericServiceException(
              "Não é possível cancelar o pedido. Motivo = não foi encontrada a fatura de número: "
                  + lote.getIdFatura());
        }

        B2bFaturaPix b2bFaturaPix = b2bFaturaPixService.encontraPorIdFatura(fatura.getIdFatura());
        if (b2bFaturaPix != null) {
          sucesso = verificarNecessidadeDeDevolucaoPIX(b2bFaturaPix);
        }

        if (Boolean.TRUE.equals(sucesso)) {
          fatura.setDataCancelamento(LocalDateTime.now());
          fatura.setIdUsuarioManutencao(idUsuario);
          faturaB2BService.save(fatura);
        }
      }

      if (Boolean.TRUE.equals(sucesso)) {
        lote = saveAndFlush(lote);

        HierarquiaPontoDeRelacionamento pontoRelacionamento =
            buscarPontoRelacionamento(
                lote.getIdProcessadora(),
                lote.getIdInstituicao(),
                lote.getIdRegional(),
                lote.getIdFilial(),
                lote.getIdPontoDeRelacionamento());

        if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
                pontoRelacionamento.getIdInstituicao())
            && status == STATUS_CANCELADO
            && lote.getIdFatura() != null) {

          HierarquiaInstituicao instituicao =
              buscarInstituicao(lote.getIdProcessadora(), lote.getIdInstituicao());
          AcessoUsuario user = acessoUsuarioService.findByIdUsuario(idUsuario);
          List<Contato> nomesContato =
              contatoService.getContatosInstituicao(
                  pontoRelacionamento.getIdProcessadora(), pontoRelacionamento.getIdInstituicao());
          String descProduto = buscaDescricaoProdutoParaEmailConfirmacaoCancelamento(lote);
          emailService.sendPedidoCargaCancelarEmail(
              user, nomesContato, fatura, lote, pontoRelacionamento, instituicao, descProduto);
        }
      }
    }
    return list;
  }

  public PreLancamentoLote findPreLancamentoLoteByIdNotNull(Integer idLote) {
    PreLancamentoLote lote = findById(idLote);

    if (lote == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar PreLancamentoLote. IdLote: " + idLote);
    }
    return lote;
  }

  /**
   * @return
   */
  @Transactional
  public Map<Integer, List<Integer>> getMapasPossiveis() {
    Map<Integer, List<Integer>> mapaLote = new HashMap<>();

    List<Integer> destinoStatusSalvo = new ArrayList<>();
    destinoStatusSalvo.add(PreLancamentoLoteService.STATUS_CONFIRMADO);
    destinoStatusSalvo.add(STATUS_CANCELADO);

    mapaLote.put(STATUS_SALVO, destinoStatusSalvo);

    List<Integer> destinoStatusConfirmado = new ArrayList<>();
    destinoStatusConfirmado.add(STATUS_PROCESSADO);
    destinoStatusConfirmado.add(STATUS_CANCELADO);

    mapaLote.put(PreLancamentoLoteService.STATUS_CONFIRMADO, destinoStatusConfirmado);

    List<Integer> destinoStatusProcessado = new ArrayList<>();
    mapaLote.put(STATUS_PROCESSADO, destinoStatusProcessado);

    List<Integer> destinoStatusCancelado = new ArrayList<>();
    mapaLote.put(STATUS_CANCELADO, destinoStatusCancelado);

    return mapaLote;
  }

  private HierarquiaPontoDeRelacionamento buscarPontoRelacionamento(
      Integer idProc, Integer idInst, Integer idRegional, Integer idFilial, Integer idPR) {

    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(idProc, idInst, idRegional, idFilial, idPR);

    HierarquiaPontoDeRelacionamento pontoRelacionamento = pontoRelacionamentoService.findById(id);

    if (pontoRelacionamento == null) {
      throw new GenericServiceException("Empresa não encontrada. ", "Parâmetros: " + id);
    }
    return pontoRelacionamento;
  }

  private HierarquiaInstituicao buscarInstituicao(Integer idProc, Integer idInst) {

    HierarquiaInstituicaoId idHierarquiaInstituicao = new HierarquiaInstituicaoId(idProc, idInst);

    HierarquiaInstituicao instituicao =
        hierarquiaInstituicaoService.findById(idHierarquiaInstituicao);

    if (instituicao == null) {
      throw new GenericServiceException(
          "Instituição emissora não encontrada. ", " Parâmetros: " + idHierarquiaInstituicao);
    }
    return instituicao;
  }

  @Transactional
  public String buscaDescricaoProdutoParaEmailConfirmacaoCancelamento(PreLancamentoLote preLanca) {
    return produtoInstuicaoService
        .findByIdProdInstituicao(preLanca.getIdProdutoInstituicao())
        .getDescProdInstituicao();
    // tratamento de excessão no local do uso desta descrição
  }

  @Transactional
  public void confirmarPagamento(CargasVO model, SecurityUser user) throws GenericServiceException {

    for (Integer obg : model.getIdLote()) {

      PreLancamentoLote lote = findPreLancamentoLoteByIdNotNull(obg);
      ProdutoInstituicaoConfiguracao produtoConfiguracao =
          produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(
              lote.getIdProdutoInstituicao());
      FaturaB2B fatura = faturaB2BService.findById(lote.getIdFatura());

      if (fatura == null) {
        throw new GenericServiceException("Não foi possível encontrar a fatura selecionada!");
      }

      PreLancamentoLote pedido =
          preLancamentoLoteService.findByIdFaturaSemException(fatura.getIdFatura());

      if (pedido != null) {
        if (produtoConfiguracao.getIdProdutoPlataforma() == PRODUTO_PLATAFORMA
            && fatura.getDataPagamento() == null) {
          fazerCargaContaParametrizada(pedido, fatura);
          pedido.setIdUsuarioManutencao(user.getIdUsuario());
          pedido.setPgtoManual(PGTO_MANUAL);
          pedido.setStatus(STATUS_LIBERADO_CARGA_PRODUTO_TRANSFERENCIA);
          preLancamentoLoteService.save(pedido);
        } else {
          pedido.setIdUsuarioManutencao(user.getIdUsuario());
          pedido.setPgtoManual(PGTO_MANUAL);
          pedido.setStatus(STATUS_CONFIRMADO);
          preLancamentoLoteService.save(pedido);
        }
      }

      if (fatura.getDataPagamento() == null) {
        fatura.setDataPagamento(LocalDateTime.now());
        fatura.setDataHoraPgtoManual(LocalDateTime.now());
      }
      fatura.setIdUsuarioManutencao(user.getIdUsuario());
      fatura = faturaB2BService.save(fatura);

      // enviar e-mail caso for instituição infincancas
      if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(fatura.getIdInstituicao())) {
        AcessoUsuario usuario = new AcessoUsuario();

        if (pedido == null
            || pedido.getIdUsuarioInclusao() == null
            || (pedido.getTipoPedido() != null
                && Constantes.TIPO_PEDIDO_ESPECIAL.equals(pedido.getTipoPedido()))) {
          usuario.setEmail(user.getEmail());
          usuario.setNome(user.getNome());
        } else {
          usuario = acessoUsuarioService.findByIdUsuario(pedido.getIdUsuarioInclusao());
        }

        if (usuario.getNome() != null && usuario.getEmail() != null) {
          emailService.sendAvisoPgtoManualEmail(usuario.getEmail(), usuario.getNome(), fatura);
        } else {
          throw new GenericServiceException(
              "O Usuário que gerou o pedido não foi carregado.O pagamento deste pedido não será confirmado.");
        }
      } else {
        // registrar em cobrança bancária para outras instituições
        Date dtPgto = new Date();
        confirmaPgtoManualParteCobranca(fatura, dtPgto, user.getIdUsuario());
      }

      if (pedido != null && pedido.getProdutoInstituicao() != null) {
        ProdutoInstituicaoConfiguracao prodConfig =
            produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(
                pedido.getIdProdutoInstituicao());
        if (prodConfig != null
            && prodConfig.getCargaIntegracao() != null
            && prodConfig.getCargaIntegracao()) {
          if (pontosRecebidosService.existsByNumeroPedido(pedido.getIdLote().longValue())) {
            throw new GenericServiceException(
                "Já foi confirmada a carga de pontos para o pedido de número "
                    + pedido.getIdLote()
                    + ".",
                HttpStatus.UNPROCESSABLE_ENTITY);
          }
          tratarPontosRecebidos(pedido, user);
        }
      }
    }
  }

  private void tratarPontosRecebidos(PreLancamentoLote pedido, SecurityUser user) {
    List<PontosRecebidos> pontosRecebidosList = new ArrayList<>();
    for (PreLancamento preLancamento : pedido.getPreLancamentos()) {
      Pessoa pessoa = pessoaService.findPessoaByIdConta(preLancamento.getIdConta().longValue());
      PontosRecebidos pontosRecebidos = new PontosRecebidos();
      pontosRecebidos.setIdJobInclusao(1);
      pontosRecebidos.setDataHoraJobInclusao(LocalDateTime.now());
      pontosRecebidos.setIdUsuarioInclusao(user.getIdUsuario());
      pontosRecebidos.setSeqArquivo(null);
      pontosRecebidos.setSeqLinhaArquivo(null);
      pontosRecebidos.setIdParcAcumulo(Constantes.PARCEIRO_ACUMULO_IN_MAIS_PREMIOS);
      pontosRecebidos.setDocumento(pessoa.getDocumento());
      // Buscando uma lista pois podem haver duplicações na base
      // caso haja, permitir passar, pois o proc32 tratará o caso no momento do processamento
      List<Pessoa> pessoas =
          pessoaService.findByIdProcessadoraAndIdInstituicaoAndDocumento(
              Constantes.ID_PROCESSADORA_ITS_PAY,
              Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL,
              pessoa.getDocumento());
      if (pessoas != null && pessoas.size() > 0) {
        pontosRecebidos.setNomeParticipante(pessoas.get(0).getNomeCompleto());
      } else {
        pontosRecebidos.setNomeParticipante("FUTURO PARTICIPANTE");
      }
      pontosRecebidos.setDataMovimento(pedido.getDataAgendamento());
      pontosRecebidos.setVlrReais(preLancamento.getValorLancamento().doubleValue());
      BigDecimal pontos =
          preLancamento
              .getValorLancamento()
              .multiply(new BigDecimal(100))
              .setScale(0, RoundingMode.HALF_EVEN);
      pontosRecebidos.setQtdPontos(pontos.longValue());
      pontosRecebidos.setBonificado(null);
      pontosRecebidos.setTipoPagamento(pedido.getTipoPagamento());
      pontosRecebidos.setDataHoraAutorizacao(LocalDateTime.now());
      pontosRecebidos.setNumeroPedido(pedido.getIdLote().longValue());
      pontosRecebidosList.add(pontosRecebidos);
    }
    pontosRecebidosService.saveAll(pontosRecebidosList);
  }

  protected Boolean verificarNecessidadeDeDevolucaoPIX(B2bFaturaPix b2bFaturaPix) {
    if (b2bFaturaPix.getDtHrRecebido() != null) {
      log.info("Iniciando devolução do PIX de EndToEnd " + b2bFaturaPix.getIdEndToEnd());
      Optional<ContaTransacionalMovimento> movimento =
          contaTransacionalMovimentoService.findFirstByEndToEndAndTipoMovimento(
              b2bFaturaPix.getIdEndToEnd(), TipoMovimentoEnum.CREDITO);
      if (!movimento.isPresent()) {
        return Boolean.FALSE;
      }
      ContaTransacionalMovimento mov = movimento.get();
      IncluirOrdemDevolucaoRequest incluirOrdemDevolucaoRequest =
          new IncluirOrdemDevolucaoRequest();
      incluirOrdemDevolucaoRequest.setMotivosDevolucao(
          Collections.singletonList(new MotivoDevolucao()));
      incluirOrdemDevolucaoRequest.setEndToEnd(b2bFaturaPix.getIdEndToEnd());
      incluirOrdemDevolucaoRequest.setValor(b2bFaturaPix.getVlFatura());
      incluirOrdemDevolucaoRequest.setInscricaoNacional(mov.getDocumento());
      ResponseEntity<IncluirOrdemPagamentoResponse> incluirDevolucao = null;
      try {
        incluirDevolucao =
            contaTransacionalDevolucaoService.incluirDevolucaoDeOrdemUser(
                getNumeroContaSemDV(mov.getNroConta()), incluirOrdemDevolucaoRequest);
      } catch (Exception e) {
        String mensagemDevolucao =
            "Ocorreu um erro ao tentar devolver o PIX de EndToEnd "
                + b2bFaturaPix.getIdEndToEnd()
                + ". A fatura "
                + b2bFaturaPix.getIdFatura()
                + " e o Lote "
                + b2bFaturaPix.getIdLoteLanc()
                + " não serão cancelados ainda."
                + " Informação adicional: "
                + e.getMessage()
                + "; "
                + e.getLocalizedMessage();
        log.info(mensagemDevolucao);
        return Boolean.FALSE;
      }
      if (incluirDevolucao.getBody() != null
          && incluirDevolucao.getStatusCode().equals(HttpStatus.OK)) {
        String mensagemDevolucao = "Devolvido o PIX de EndToEnd " + b2bFaturaPix.getIdEndToEnd();
        log.info(mensagemDevolucao);
        b2bFaturaPix.setStatus(STATUS_CANCELADO);
        b2bFaturaPix.setDtHrStatus(LocalDateTime.now());
        b2bFaturaPixService.salvar(b2bFaturaPix);
        return Boolean.TRUE;
      } else {
        String mensagemDevolucao =
            "Não foi possível devolver o PIX de EndToEnd "
                + b2bFaturaPix.getIdEndToEnd()
                + ". A fatura "
                + b2bFaturaPix.getIdFatura()
                + " e o Lote "
                + b2bFaturaPix.getIdLoteLanc()
                + " não serão cancelados ainda.";
        log.info(mensagemDevolucao);
        return Boolean.FALSE;
      }
    }
    return Boolean.TRUE;
  }

  private Long getNumeroContaSemDV(String nroConta) {
    return Long.valueOf(nroConta.substring(0, nroConta.length() - 1));
  }

  public void confirmaPgtoManualParteCobranca(FaturaB2B fatura, Date dtPgto, Integer idUsuario) {
    CobrancaBancaria cobranca = cobrancaBancariaService.findById(fatura.getIdCobrancaBancaria());

    if (cobranca == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar a cobrança bancária da fatura selecionada!");
    }

    cobranca.setValorPago(
        fatura.getValorFaturaNova() != null && fatura.getValorFaturaNova() != BigDecimal.ZERO
            ? fatura.getValorFaturaNova()
            : (fatura.getValorLiquido() != null
                ? fatura.getValorLiquido()
                : fatura.getValorFatura()));

    Date dateAtual = new Date();
    dateAtual.setHours(23);
    dateAtual.setMinutes(59);
    dateAtual.setSeconds(59);

    if (dtPgto.after(dateAtual)) {
      throw new GenericServiceException(
          "A data de crédito na conta da empresa deve ser anterior ou igual a data de hoje");
    }

    Calendar dataAtualCalendar = Calendar.getInstance();
    cobranca.setDataPagamento(dataAtualCalendar);
    cobranca.setDataCreditoContaCorrente(dataAtualCalendar);
    cobranca.setDataCreditoContaCorrenteBaixaManual(dtPgto);
    cobranca.setIdUsuarioBaixaManual(idUsuario);
    cobrancaBancariaService.save(cobranca);
  }

  public void fazerCargaContaParametrizada(PreLancamentoLote pedido, FaturaB2B fatura) {

    try {
      CargaAutoParametros cargaAutoParametros =
          cargaAutoParametrosService.encontrarParametroPorNome("ID_CONTA");
      CargaAutoProdutoParametro cargaAutoProdutoParametro =
          cargaAutoProdutoParametroService.encontrarParametroDoProduto(
              pedido.getIdProdutoInstituicao(), cargaAutoParametros.getId(), STATUS_ATIVO);

      Long idConta = Long.valueOf(cargaAutoProdutoParametro.getVlParametro());
      String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_CARGA_AUTO);

      ContaPagamento contaPagamento =
          contaPagamentoService.findOneByIdContaAndIdRelacionamento(idConta);
      ProdutoInstituicao produtoInstituicao = contaPagamento.getProdutoInstituicao();
      ProdutoInstituicaoConfiguracao produto =
          produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0);

      String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaPagamento, produto);

      boolean isProdutoMoeda =
          Constantes.CODIGO_MOEDA_DE_PONTO.equals(produto.getMoeda().getIdMoeda());
      Optional<CotacaoPontos> cotacaoPontosOptional =
          Optional.ofNullable(
              cotacaoPontosRepository.findByIdInstituicao(contaPagamento.getIdInstituicao()));
      CotacaoPontos cotacaoPontos = cotacaoPontosOptional.orElseGet(CotacaoPontos::new);
      Integer stan = lancamentoService.getStan();
      Credencial credencial =
          getCredencialService().buscarCredencialParaLancamentoManual(contaPagamento.getIdConta());
      JcardResponse response =
          lancamentoService.doLancamentoManual(
              accountCode,
              COD_TRANSACAO_CARGA_PRE,
              isProdutoMoeda
                  ? fatura.getValorFaturaNova() != null
                      ? fatura.getValorFaturaNova()
                      : fatura.getValorFatura().multiply(cotacaoPontos.getValorConversao())
                  : fatura.getValorFaturaNova() != null
                      ? fatura.getValorFaturaNova()
                      : fatura.getValorFatura(),
              produto.getMoeda().getIdMoeda().toString(),
              rrn,
              SINAL_CREDITO,
              credencial.getTokenInterno(),
              contaPagamento.getIdConta(),
              TEXTO_EXTRATO_PRE + pedido.getIdLote() + TEXTO_EXTRATO_POS,
              null,
              null,
              null,
              stan,
              null);

      if (!response.getSuccess()) {
        throw new GenericServiceException(
            "Não foi possivel realizar a carga na conta parametrizada.");
      }
    } catch (Exception e) {
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar realizar a carga na conta parametrizada.");
    }
  }

  public List<LancamentoPedidosVO> listarConfirmacaoPedido(
      FaturasEPedidosFiltroVO model, Integer idProcessadora, Integer idInstituicao) {

    model.setIsCount(Boolean.FALSE);
    List<LancamentoPedidosVO> lista = new ArrayList<>();
    lista =
        repository.listarConfirmacaoPedidosEFaturas(
            model, idProcessadora, idInstituicao, List.class);

    if (!lista.isEmpty()) {
      for (LancamentoPedidosVO lancamentoPedidosVO : lista) {
        List<CargaAutoProdutoParametro> cargaAutoProdutoParametroList =
            cargaAutoProdutoParametroService.findIdProdutoInstituicao(
                lancamentoPedidosVO.getIdProdutoInstituicao());
        lancamentoPedidosVO.setParametrosCarga(cargaAutoProdutoParametroList);
      }
    }

    return lista;
  }

  public Long contarFaturasEPedidosConfirmacao(
      FaturasEPedidosFiltroVO model, Integer idProcessadora, Integer idInstituicao) {

    model.setIsCount(Boolean.TRUE);
    return repository.listarConfirmacaoPedidosEFaturas(
        model, idProcessadora, idInstituicao, Long.class);
  }

  public List<LancamentoPedidosVO> listarFaturasEPedidosCargas(
      FaturasEPedidosFiltroVO model, Integer idProcessadora, Integer idInstituicao) {
    List<LancamentoPedidosVO> lista;

    model.setIsCount(Boolean.FALSE);
    lista =
        repository.listarFaturasEPedidosCargas(model, idProcessadora, idInstituicao, List.class);
    return lista;
  }

  public Long contarlistarFaturasEPedidosCargas(
      FaturasEPedidosFiltroVO model, Integer idProcessadora, Integer idInstituicao) {

    model.setIsCount(Boolean.TRUE);
    return repository.listarFaturasEPedidosCargas(model, idProcessadora, idInstituicao, Long.class);
  }
}
