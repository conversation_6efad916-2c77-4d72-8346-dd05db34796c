package br.com.sinergico.enums;

import br.com.exceptions.GenericServiceException;
import com.fasterxml.jackson.annotation.JsonFormat;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum TipoFraudeEloEnum implements TipoFraudeBandeiraEnum {
  CARTAO_PERDIDO("00", "Cartão perdido pelo portador"),
  CARTAO_ROUBADO("01", "Cartão roubado do portador"),
  CARTAO_EXTRAVIADO("02", "Cartão extraviado"),
  FALSIDADE_IDEOLOGICA("03", "Falsidade Ideológica"),
  CARTAO_CLONADO("04", "Cartão clonado/falsificado"),
  SEM_ENVOLVIMENTO_PORTADOR("05", "Transação autorizada sem o envolvimento do portador"),
  MARKETING_DIRETO_OU_COMERICIO_ELETRONICO(
      "06",
      "Número do cartão foi usado em transação de marketing direto ou no comércio eletrônico"),
  FRAUDE_FAMILIAR("07", "Fraude Familiar"),
  ENGENHARIA_SOCIAL("08", "Engenharia Social"),
  AUTO_FRAUDE("09", "Auto Fraude"),
  FRAUDE_INTERNA("10", "Fraude Interna");

  private final String codigo;
  private final String descricao;

  TipoFraudeEloEnum(String codigo, String descricao) {
    this.codigo = codigo;
    this.descricao = descricao;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getDescricao() {
    return descricao;
  }

  public static TipoFraudeEloEnum getEnumFromCodigo(String codigo) {
    if (codigo != null) {
      for (TipoFraudeEloEnum tf : values()) {
        if (tf.codigo.equalsIgnoreCase(codigo)) {
          return tf;
        }
      }
    }
    throw new GenericServiceException(
        "Tipo Fraude ELO de código " + (codigo != null ? codigo : "null") + " não existe.");
  }
}
