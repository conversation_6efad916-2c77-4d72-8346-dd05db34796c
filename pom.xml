<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>br.com.itspay.devops</groupId>
        <artifactId>pom-master</artifactId>
        <version>1.1.2</version>
    </parent>

    <groupId>br.com.itspay</groupId>
    <artifactId>issuerBack</artifactId>
    <version>2.83.1</version>
    <name>issuerBack</name>
    <description>issuerBack</description>


    <properties>
        <maven.compiler.target>17</maven.compiler.target>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.release>17</maven.compiler.release>
        <main.class.start.spring>br.com.sinergico.IssuerApplication</main.class.start.spring>
        <skip.rewrite.plugin>true</skip.rewrite.plugin>
        <skip.enforce.rules>false</skip.enforce.rules>

        <!-- ITP -->
        <acquirer-iso-client.version>1.1.10</acquirer-iso-client.version>
        <lib-integracao-celcoin.version>3.1.0</lib-integracao-celcoin.version>
        <gateway-api.version>1.2.0</gateway-api.version>
        <qrcode-api.version>1.2.0</qrcode-api.version>
        <lib-integracao-giftty.version>2.1.0</lib-integracao-giftty.version>
        <b2e-client-api.version>2.0.0</b2e-client-api.version>
        <cred-defense-api.version>2.0.0</cred-defense-api.version>
        <neurotec-client.version>2.0.0</neurotec-client.version>
        <lib-integracao-abc.version>2.1.0</lib-integracao-abc.version>
        <!-- ITP -->

        <!-- TERCEIROS -->
        <bcmail-jdk15on.version>1.52</bcmail-jdk15on.version>
        <bucket4j-core.version>4.10.0</bucket4j-core.version>
        <caelum-stella.version>2.1.6</caelum-stella.version>
        <dynamicreports-core.version>3.1.6</dynamicreports-core.version>
        <itext.version>2.1.7</itext.version>
        <itextpdf.version>5.5.12</itextpdf.version>
        <javatuples.version>1.2</javatuples.version>
        <jasperreports.version>6.4.1</jasperreports.version>
        <jjwt.version>0.6.0</jjwt.version>
        <jpos.version>2.1.7</jpos.version>
        <lombok.version>1.18.36</lombok.version>
        <nimbus-jose-jwt.version>9.1.2</nimbus-jose-jwt.version>
        <olap4j.version>1.2.0</olap4j.version>
        <pdfbox.version>1.1.0</pdfbox.version>
        <poi.version>3.14</poi.version>
        <net.javacrumbs.shedlock.version>4.0.3</net.javacrumbs.shedlock.version>
        <com.google.zxing.version>3.4.1</com.google.zxing.version>
        <commons-beanutils.version>1.9.4</commons-beanutils.version>
        <passay.version>1.6.3</passay.version>
        <nekohtml.version>1.9.22</nekohtml.version>
        <jedis.version>2.5.1</jedis.version>
        <gson.version>2.11.0</gson.version>
        <rt.version>2.3.3</rt.version>
        <jakarta.xml.rpc-api.version>1.1.4</jakarta.xml.rpc-api.version>
        <commons-io.version>2.16.1</commons-io.version>
        <json.version>20231013</json.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>
        <spring-dotenv.version>4.0.0</spring-dotenv.version>
        <!-- TERCEIROS -->

    </properties>

    <dependencies>
        <!-- SPRING STARTER FOR REDIS -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- SPRING STARTER FOR REDIS SESSSION -->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>
        <!-- ITP -->
        <dependency>
            <groupId>br.com.itspay.gateway</groupId>
            <artifactId>gateway-api</artifactId>
            <version>${gateway-api.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.httpcomponents</groupId>
                    <artifactId>httpclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay</groupId>
            <artifactId>acquirer-iso-client</artifactId>
            <version>${acquirer-iso-client.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay.qrcodeapi</groupId>
            <artifactId>qrcode-api</artifactId>
            <version>${qrcode-api.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay.giftty.client</groupId>
            <artifactId>lib-integracao-giftty</artifactId>
            <version>${lib-integracao-giftty.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay</groupId>
            <artifactId>b2e-client-api</artifactId>
            <version>${b2e-client-api.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay</groupId>
            <artifactId>cred-defense-api</artifactId>
            <version>${cred-defense-api.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay</groupId>
            <artifactId>neurotec-client</artifactId>
            <version>${neurotec-client.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.itspay.abc.client</groupId>
            <artifactId>lib-integracao-abc</artifactId>
            <version>${lib-integracao-abc.version}</version>
        </dependency>

        <!-- ITP -->

        <dependency>
            <groupId>net.sourceforge.nekohtml</groupId>
            <artifactId>nekohtml</artifactId>
            <version>${nekohtml.version}</version>
        </dependency>
        <!-- TERCEIROS -->
        <dependency>
            <groupId>com.github.vladimir-bukhtoyarov</groupId>
            <artifactId>bucket4j-core</artifactId>
            <version>${bucket4j-core.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>${jjwt.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${com.google.zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>${com.google.zxing.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>${pdfbox.version}</version>
        </dependency>
        <dependency>
            <groupId>org.olap4j</groupId>
            <artifactId>olap4j</artifactId>
            <version>${olap4j.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>xerces</groupId>
                    <artifactId>xercesImpl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcmail-jdk15on</artifactId>
            <version>${bcmail-jdk15on.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>${itext.version}</version>
        </dependency>
        <!-- Stella gerar boleto -->
        <dependency>
            <groupId>br.com.caelum.stella</groupId>
            <artifactId>caelum-stella-core</artifactId>
            <version>${caelum-stella.version}</version>
        </dependency>
        <dependency>
            <groupId>br.com.caelum.stella</groupId>
            <artifactId>caelum-stella-boleto</artifactId>
            <version>${caelum-stella.version}</version>
        </dependency>
        <!-- fim stella gerar boleto -->

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${poi.version}</version>
        </dependency>

        <!-- DynamicReports-Core -->
        <dependency>
            <groupId>net.sourceforge.dynamicreports</groupId>
            <artifactId>dynamicreports-core</artifactId>
            <version>${dynamicreports-core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lowagie</groupId>
                    <artifactId>itext</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--Inicio JasperReports -->
        <!-- https://mvnrepository.com/artifact/net.sf.jasperreports/jasperreports -->
        <dependency>
            <groupId>net.sf.jasperreports</groupId>
            <artifactId>jasperreports</artifactId>
            <version>${jasperreports.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.lowagie</groupId>
                    <artifactId>itext</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.olap4j</groupId>
                    <artifactId>olap4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version>
        </dependency>
        <!-- fim JasperReports -->

        <dependency>
            <groupId>org.jpos</groupId>
            <artifactId>jpos</artifactId>
            <version>${jpos.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-nop</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.javatuples/javatuples -->
        <dependency>
            <groupId>org.javatuples</groupId>
            <artifactId>javatuples</artifactId>
            <version>${javatuples.version}</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>${itextpdf.version}</version>
        </dependency>
        <dependency>
            <groupId>com.nimbusds</groupId>
            <artifactId>nimbus-jose-jwt</artifactId>
            <version>${nimbus-jose-jwt.version}</version>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
            <version>${net.javacrumbs.shedlock.version}</version>
        </dependency>

        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-jdbc-template</artifactId>
            <version>${net.javacrumbs.shedlock.version}</version>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.rpc</groupId>
            <artifactId>jakarta.xml.rpc-api</artifactId>
            <version>${jakarta.xml.rpc-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>jaxws-rt</artifactId>
            <version>${rt.version}</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.sun.xml.ws</groupId>
            <artifactId>rt</artifactId>
            <version>${rt.version}</version>
        </dependency>
        <!--APACHE COMMONS -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>${commons-beanutils.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>${commons-io.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-httpclient/commons-httpclient -->
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>${commons-httpclient.version}</version>
        </dependency>


        <dependency>
            <groupId>org.passay</groupId>
            <artifactId>passay</artifactId>
            <version>${passay.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.json</groupId>
            <artifactId>json</artifactId>
            <version>${json.version}</version>
        </dependency>
    </dependencies>

</project>
