package br.com.sinergico.controller.suporte;

import br.com.json.bean.cadastral.FaturaB2BMin;
import br.com.json.bean.cadastral.NFSERequest;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.NotaFiscalService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/nota-fiscal")
public class NotaFiscalController extends UtilController {

  @Autowired private EntityManager em;

  @Autowired private HttpServletRequest request;

  @Autowired private NotaFiscalService notaFiscalService;

  @ApiOperation(value = "Abre uma janela com dada nota solicitada")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/gerar-danfe",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Secured({"ROLE_GERAR_PDF_DANFE_NOTA_FISCAL"})
  public ResponseEntity<?> visualizarNotaFiscalPdf(
      @RequestBody @Valid FaturaB2BMin model, BindingResult result) throws IOException {

    SecurityUser user = getAuthenticatedUser(request, em);

    InputStream input = notaFiscalService.visualizarNotaFiscal(model, user);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));
    headers.put("msg", Arrays.asList("Download finalizado com sucesso!"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(value = "Realiza download de nota fiscal via totvs")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @PostMapping(value = "/totvs/download", produces = MediaType.APPLICATION_XML_VALUE)
  public ResponseEntity<?> baixarNotaFiscalTotvsPdf(
      @RequestBody @Valid NFSERequest nfseRequest, BindingResult result) throws IOException {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());

    InputStream input = notaFiscalService.gerarNotaFiscalXML(nfseRequest);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/xml"));

    return new ResponseEntity<>(new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(value = "Abre uma janela com a nota solicitada em PDF")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/gerar-danfe-pdf",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  @Secured({"ROLE_GERAR_PDF_DANFE_NOTA_FISCAL"})
  public ResponseEntity<?> gerarNotaFiscalPdf(@RequestBody @Valid NFSERequest nfseRequest) {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());

    InputStream input = notaFiscalService.gerarNotaFiscalPdf(nfseRequest);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/pdf"));
    headers.put("msg", List.of("Download finalizado com sucesso!"));

    return new ResponseEntity<>(new InputStreamResource(input), headers, HttpStatus.OK);
  }
}
