package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.CredencialConta;
import br.com.entity.cadastral.CredencialContaId;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.sinergico.repository.cadastral.CredencialContaRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.GeradorCredencialService;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CredencialContaService extends GenericService<CredencialConta, CredencialContaId> {

  @Autowired CredencialContaRepository credencialContaRepository;

  @Autowired GeradorCredencialService geradorCredencialService;

  @Autowired
  public CredencialContaService(CredencialContaRepository repo) {
    super(repo);
    credencialContaRepository = repo;
  }

  public CredencialConta vincularCredencialConta(
      ContaPagamento conta,
      Credencial credencial,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Pessoa pessoa) {
    CredencialConta credencialConta = new CredencialConta();
    CredencialContaId credencialContaId =
        new CredencialContaId(credencial.getIdCredencial(), conta.getIdConta());

    CredencialConta jaAssociado =
        credencialContaRepository.findById(credencialContaId).orElse(null);

    if (jaAssociado == null) {
      credencialConta.setCredencialContaId(credencialContaId);
      credencialConta.setDtHrInclusao(LocalDateTime.now());

      credencialContaRepository.saveAndFlush(credencialConta);

      geradorCredencialService.addAccount(credencial, prodInstConf, pessoa, conta);
    }

    return credencialConta;
  }

  public void vincularCredencialContaSegundaVia(
      Long idCredencialAnterior, Credencial credencialNova) {
    // TODO: verificar se é preciso checar o status da conta antes de vincular a nova credencial,
    // mudar a query caso preciso
    List<CredencialConta> credencialContas =
        credencialContaRepository.findByCredencial(idCredencialAnterior);
    credencialContas =
        credencialContas.stream()
            .filter(
                cc -> !cc.getCredencialContaId().getIdConta().equals(credencialNova.getIdConta()))
            .collect(Collectors.toList());
    for (CredencialConta cc : credencialContas) {
      vincularCredencialConta(
          cc.getContaPagamento(),
          credencialNova,
          cc.getContaPagamento().getProdutoInstituicao().getProdutoInstituicaoConfiguracao().get(0),
          cc.getContaPagamento().getContasPessoa().get(0).getPessoa());
    }
  }

  public void vincularCredencialContaComIdConta(Long idConta, Credencial credencialNova) {
    // Buscar todos os registros de CredencialConta ligados ao idConta
    List<CredencialConta> credencialContas =
        credencialContaRepository.findByContaPagamento_IdConta(idConta);

    // Conjunto para armazenar todas as Contas Pagamento distintas
    Set<ContaPagamento> contasDistintas = new HashSet<>();

    // Para cada CredencialConta, buscar novamente todos os registros de CredencialConta usando os
    // idCredencial retornados
    for (CredencialConta credencialConta : credencialContas) {
      Long idCredencial = credencialConta.getCredencialContaId().getIdCredencial();
      List<CredencialConta> credencialContasRelacionadas =
          credencialContaRepository.findByCredencial(idCredencial);

      // Coletar todas as contas pagamento distintas encontradas
      for (CredencialConta credencialContaRelacionada : credencialContasRelacionadas) {
        if (credencialContaRelacionada.getContaPagamento() != null) {
          contasDistintas.add(credencialContaRelacionada.getContaPagamento());
        }
      }
    }

    contasDistintas =
        contasDistintas.stream()
            .filter(conta -> !conta.getIdConta().equals(credencialNova.getIdConta()))
            .collect(Collectors.toSet());

    // Vincular a nova credencial a todas as contas pagamento distintas
    for (ContaPagamento contaPagamento : contasDistintas) {
      vincularCredencialConta(
          contaPagamento,
          credencialNova,
          contaPagamento.getProdutoInstituicao().getProdutoInstituicaoConfiguracao().get(0),
          contaPagamento.getContasPessoa().get(0).getPessoa());
    }
  }

  public void vincularCredencialContasMesmoGrupo(
      Credencial credencialNova, Pessoa pessoa, ContaPagamento conta) {
    List<CredencialConta> listaContasMesmoGrupo =
        findCredencialContaByDocumentoAndGrupoProdutosAndHierarquia(
            pessoa.getDocumento(),
            conta
                .getProdutoInstituicao()
                .getProdutoInstituicaoConfiguracao()
                .get(0)
                .getIdGrupoProduto(),
            conta.getIdInstituicao(),
            conta.getIdProcessadora(),
            conta.getIdFilial(),
            conta.getIdRegional(),
            conta.getIdPontoDeRelacionamento());

    Set<ContaPagamento> setContas =
        listaContasMesmoGrupo.stream()
            .map(CredencialConta::getContaPagamento)
            .filter(cp -> !cp.getIdConta().equals(credencialNova.getIdConta()))
            .collect(Collectors.toSet());

    for (ContaPagamento cp : setContas) {
      vincularCredencialConta(
          cp,
          credencialNova,
          cp.getProdutoInstituicao().getProdutoInstituicaoConfiguracao().get(0),
          pessoa);
    }
  }

  public List<CredencialConta> findCredencialContaByDocumentoAndGrupoProdutosAndHierarquia(
      String documento,
      Long idGrupo,
      Integer idInstituicao,
      Integer idProcessadora,
      Integer idFilial,
      Integer idRegional,
      Integer idPontoRelacionamento) {
    List<CredencialConta> credenciaisContaList =
        credencialContaRepository.findCredencialContaByDocumentoAndGrupoProdutosAndHierarquia(
            documento,
            idGrupo,
            idInstituicao,
            idProcessadora,
            idPontoRelacionamento,
            idRegional,
            idFilial);

    credenciaisContaList =
        credenciaisContaList.stream()
            .peek(
                cc -> {
                  if (cc.getCredencialContaId().getIdCredencial() != null
                      && cc.getCredencial() == null) {
                    cc.setCredencial(
                        getCredencialFacade()
                            .findById(cc.getCredencialContaId().getIdCredencial()));
                  }
                  if (cc.getCredencialContaId().getIdConta() != null
                      && cc.getContaPagamento() == null) {
                    cc.setContaPagamento(
                        getContaPagamentoFacade()
                            .findContaByIdConta(cc.getCredencialContaId().getIdConta()));
                  }
                })
            .collect(Collectors.toList());
    return credenciaisContaList;
  }

  public List<CredencialConta> findCredencialContaByListaContas(List<Long> idsContas) {
    return credencialContaRepository.findByListaContas(idsContas);
  }
}
