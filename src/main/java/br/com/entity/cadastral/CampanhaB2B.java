package br.com.entity.cadastral;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@Entity
@Table(name = "campanha_b2b", schema = "cadastral")
@SequenceGenerator(
    name = "campanha_b2b_id_campanha_seq",
    sequenceName = "cadastral.campanha_b2b_id_campanha_seq",
    initialValue = 1,
    allocationSize = 1)
@ApiModel(value = "CampanhaB2B")
public class CampanhaB2B implements Serializable {

  //  Representa Campanha B2B. A classe Campanha do mktplace2 não se aplica para essa usagem

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id_campanha", nullable = false)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "campanha_b2b_id_campanha_seq")
  private Long idCampanha;

  @Column(name = "id_prod_instituicao", nullable = false)
  private Integer idProdInstituicao;

  @Column(name = "id_instituicao", nullable = false)
  private Integer idInstituicao;

  @Column(name = "nome_campanha", nullable = false)
  private String nomeCampanha;

  @Column(name = "data_inicio_campanha", nullable = false)
  @Temporal(TemporalType.DATE)
  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private Date dataInicioCampanha;

  @Column(name = "data_fim_campanha", nullable = false)
  @Temporal(TemporalType.DATE)
  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  private Date dataFimCampanha;

  @Column(name = "regulamento_campanha")
  private String regulamentoCampanha;

  @Column(name = "situacao_campanha", nullable = false)
  private String situacaoCampanha;

  @ManyToOne
  @JoinColumn(
      name = "id_prod_instituicao",
      referencedColumnName = "id_prod_instituicao",
      insertable = false,
      updatable = false)
  private ProdutoInstituicao produtoInstituicao;

  @Column(name = "motivo_cancelamento")
  private String motivoCancelamento;

  @Column(name = "id_regional", nullable = false)
  private Integer idRegional;

  @Column(name = "id_filial", nullable = false)
  private Integer idFilial;

  @Column(name = "id_ponto_de_relacionamento", nullable = false)
  private Integer idPontoRelacionamento;

  @Column(name = "produto_corresp_multicontas")
  private Integer idProdutoCorrespMulticontas;

  @Column(name = "id_instituicao_corresp", nullable = false)
  private Integer idInstituicaoCorresp;

  @ManyToOne
  @JoinColumn(
      name = "produto_corresp_multicontas",
      referencedColumnName = "id_prod_instituicao",
      insertable = false,
      updatable = false)
  private ProdutoInstituicao produtoCorrespMulticontas;

  @Column(name = "id_regional_corresp", nullable = false)
  private Integer idRegionalCorresp;

  @Column(name = "id_filial_corresp", nullable = false)
  private Integer idFilialCorresp;

  @Column(name = "id_ponto_de_relacionamento_corresp", nullable = false)
  private Integer idPontoRelacionamentoCorresp;
}
