package br.com.sinergico.facade.cadastral;

import static br.com.sinergico.util.Constantes.GRUPO_STATUS_ATIVO;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ArranjoPagamento;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.CredencialConta;
import br.com.entity.cadastral.CredencialPreEmitida;
import br.com.entity.cadastral.GrupoEmpresarial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracaoCredito;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.suporte.Agencia;
import br.com.entity.suporte.Banco;
import br.com.entity.suporte.CotacaoPontos;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.TipoStatus;
import br.com.entity.suporte.TokenAcesso;
import br.com.entity.transacional.CodigoTransacao;
import br.com.entity.transacional.FaturaMovimento;
import br.com.entity.transacional.LancamentoManual;
import br.com.entity.transacional.LogTransferenciaB2b;
import br.com.entity.transacional.PontoControle;
import br.com.entity.transacional.TED;
import br.com.entity.transacional.TransacaoCartaoQrCode;
import br.com.entity.transacional.TransferenciaContaBancaria;
import br.com.exceptions.GenericServiceException;
import br.com.itspay.acquirer.iso.client.AcquirerClientException;
import br.com.itspay.acquirer.iso.client.TipoTransacao;
import br.com.itspay.acquirer.iso.client.TransacaoAcquirer;
import br.com.itspay.acquirer.iso.client.Utils;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.cadastral.AgendarReplicacaoContaB2BRequest;
import br.com.json.bean.cadastral.CadastrarContaDigital;
import br.com.json.bean.cadastral.CadastrarContaPagamentoPessoaRequest;
import br.com.json.bean.cadastral.CadastrarPessoaFisica;
import br.com.json.bean.cadastral.CadastrarPessoaJuridica;
import br.com.json.bean.cadastral.ContaPagamentoRequest;
import br.com.json.bean.cadastral.ContasCredenciaisReplicaveisReponse;
import br.com.json.bean.cadastral.CriarContaPagamento;
import br.com.json.bean.cadastral.DadosConta;
import br.com.json.bean.cadastral.DadosPortador;
import br.com.json.bean.cadastral.DetalhesConta;
import br.com.json.bean.cadastral.GerarCredencialRequest;
import br.com.json.bean.cadastral.GetDadosConsumo;
import br.com.json.bean.cadastral.GetPerfilTariafarioResponse;
import br.com.json.bean.cadastral.InfoMigracaoConta;
import br.com.json.bean.cadastral.LancamentoContaEstabelecimentoVo;
import br.com.json.bean.cadastral.SolicitacaoReplicacaoCredencialResumoResponse;
import br.com.json.bean.cadastral.TermoAdesaoContaPDF;
import br.com.json.bean.cadastral.TermoAdesaoContaVO;
import br.com.json.bean.cadastral.TransMesmaInstituicaoIdCredencial;
import br.com.json.bean.cadastral.TransferenciaCobrarComQrCode;
import br.com.json.bean.cadastral.TransferenciaMesmaInstituicao;
import br.com.json.bean.cadastral.TransferenciaMesmaInstituicaoViaWeb;
import br.com.json.bean.cadastral.TransferenciaMesmoBolso;
import br.com.json.bean.cadastral.ValidarCobrarTranferenciaQrCodeDTO;
import br.com.json.bean.cadastral.ValidarTransferenciaMesmaInstituicaoDTO;
import br.com.json.bean.suporte.ComunicadoContaViaPush;
import br.com.json.bean.suporte.GetDadosCredito;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.json.bean.suporte.ValorCargaProdutoInstituicao;
import br.com.json.bean.transacional.CadastroLancamentoManual;
import br.com.json.bean.transacional.TransferenciaEntreConta;
import br.com.json.bean.transacional.TransferenciaEntreContasB2b;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.LimiteTransacaoTipoEnum;
import br.com.sinergico.enums.MetodosSegurancaTransacaoEnum;
import br.com.sinergico.enums.StatusTransacaoCartaoQrCodeEnum;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.repository.cadastral.GrupoEmpresarialRepository;
import br.com.sinergico.repository.cadastral.RepresentanteLegalRepository;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.repository.suporte.MigracaoMulticontasVallooRepository;
import br.com.sinergico.repository.suporte.impl.AntifraudeCafPortadorRepositoryImpl;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GeradorContaService;
import br.com.sinergico.service.GeradorCredencialService;
import br.com.sinergico.service.adq.EstabelecimentoUnidadeService;
import br.com.sinergico.service.cadastral.ArranjoPagamentoService;
import br.com.sinergico.service.cadastral.ContaPagamentoFaturaService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.CredencialContaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.LimitesContaService;
import br.com.sinergico.service.cadastral.PerfilTarifarioService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoContratadoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoCreditoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoCorrespondenteService;
import br.com.sinergico.service.jcard.AccountService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.loyalty.ResgateContaBancariaService;
import br.com.sinergico.service.suporte.AcquirerClientService;
import br.com.sinergico.service.suporte.AgenciaService;
import br.com.sinergico.service.suporte.BancoService;
import br.com.sinergico.service.suporte.ComunicadorPortadorPushService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.SolicitacaoPreCadastroService;
import br.com.sinergico.service.suporte.SolicitacaoReplicacaoCredencialResumoService;
import br.com.sinergico.service.suporte.TipoStatusService;
import br.com.sinergico.service.suporte.TokenAcessoService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.CodigoTransacaoService;
import br.com.sinergico.service.transacional.FaturaMovimentoService;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.service.transacional.LogTransferenciaB2bService;
import br.com.sinergico.service.transacional.RrnLogService;
import br.com.sinergico.service.transacional.SaldoContaCreditoService;
import br.com.sinergico.service.transacional.TEDService;
import br.com.sinergico.service.transacional.TransacaoCartaoQrCodeService;
import br.com.sinergico.service.transacional.TransferenciaContaBancariaService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.util.UtilManejoPontos;
import br.com.sinergico.vo.MigracaoMulticontasVallooVO;
import br.com.sinergico.vo.PessoaPoliticamenteExpostaVO;
import com.itextpdf.text.DocumentException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ContaPagamentoFacade {

  public static final Integer REPRESENTANTE_LEGAL_ATIVO = 1;
  private static final int TIPO_CONTA_CORRENTE = 1;
  public static final String TRANSFER_NOT_ALLOWED_BETWEEN_PRODUCTS =
      "Transfer not allowed between products";
  public static final String TRANSACTION_NOT_ENABLED = "transaction.not.enabled";

  @Autowired private LancamentoService lancamentoService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private CardService cardService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private TipoStatusService tipoStatusService;

  @Autowired private GeradorContaService geradorContaService;

  @Autowired private PessoaService pessoaService;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private GeradorCredencialService geradorCredencialService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private ComunicadorPortadorPushService comunicadorPushService;

  @Autowired private LogTransferenciaB2bService logTransferenciaB2bService;

  @Autowired private RrnLogService rrnLogService;

  @Autowired private EstabelecimentoUnidadeService estabelecimentoUnidadeService;

  @Autowired private AcquirerClientService acquirerClientService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private ProdutoInstituicaoConfiguracaoCreditoService prodInstConfigCreditoService;

  @Autowired
  private ProdutoInstituicaoCorrespondenteService produtoInstituicaoCorrespondenteService;

  @Lazy @Autowired private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacioService;

  @Autowired private ProdutoContratadoService produtoContratadoService;

  @Autowired private ContaPagamentoFaturaService contaPagamentoFaturaService;

  @Autowired private FaturaMovimentoService faturaMovimentoService;

  @Autowired private SaldoContaCreditoService contaCreditoService;

  @Autowired private TransferenciaContaBancariaService transferenciaContaBancariaService;

  @Autowired private SolicitacaoReplicacaoCredencialResumoService solicitaReplicacaoService;

  @Autowired private AccountService accountService;

  @Autowired private RepresentanteLegalRepository representanteRepository;

  @Autowired private AgenciaService agenciaService;

  @Autowired private BancoService bancoService;

  @Autowired private UtilManejoPontos utilManejoPontos;

  @Autowired private ArranjoPagamentoService arranjoPagamentoService;

  @Autowired private CodigoTransacaoService codigoTransacaoService;

  @Autowired private ResgateContaBancariaService resgateContaBancariaService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfigService;

  @Autowired private SolicitacaoPreCadastroService preCadastroService;

  private static final Logger log = LoggerFactory.getLogger(ContaPagamentoFacade.class);

  @Autowired private TokenAcessoService tokenAcessoService;

  @Autowired private EventoService eventoService;

  @Autowired private CredencialContaService credencialContaService;

  @Autowired private TransacaoCartaoQrCodeService transacaoCartaoQrCodeService;

  @Autowired private CotacaoPontosRepository cotacaoPontosRepository;

  @Autowired private LimitesContaService limitesContaService;

  @Autowired private TEDService tedService;

  @Autowired private GrupoEmpresarialRepository grupoEmpresarialRepository;

  @Autowired private AntifraudeCafPortadorRepositoryImpl antifraudeCafPortadorRepository;

  @Autowired private MigracaoMulticontasVallooRepository migracaoMulticontasVallooRepository;

  private static final String FMT_DD_MM_YYYY = "dd/MM/yyyy";

  /**
   * Metodo responsável por efetuar transferencias para portadores da mesma instituição.
   *
   * @param model
   * @param tokenJWT
   * @param comSenha
   * @return boolean
   */
  @Transactional
  public boolean transferenciaMesmaInstituicao(
      TransferenciaMesmaInstituicao model,
      String tokenJWT,
      Boolean comSenha,
      String documentoRepresentante) {
    Credencial credencialDestino = null;

    GetCardResponse card = cardService.getToken(model.getCredencialDestino().toUpperCase());
    String tokenInterno = null;

    if (card.getSuccess()) {
      tokenInterno = card.getCard().getToken();
      credencialDestino = credencialService.findOneByTokenInterno(tokenInterno);
    }

    if (Objects.isNull(credencialDestino)) {
      throw new GenericServiceException(
          "Credencial Destino Não encontrada.", "TokenInterno: " + tokenInterno);
    }

    return realizarTransferencia(
            model.getIdInstituicaoOrigem(),
            model.getIdCredencialOrigem(),
            model.getPinCredencialOrigem(),
            model.getValorTransferencia(),
            tokenJWT,
            comSenha,
            FALSE,
            credencialDestino,
            null,
            documentoRepresentante,
            null,
            null)
        .getSuccess();
  }

  public JcardResponse realizarTransferencia(
      Integer idInstituicaoOrigem,
      Long idCredencialOrigem,
      String pinCredencialOrigem,
      BigDecimal valorTransferencia,
      String tokenJWT,
      Boolean ComSenha,
      Boolean validadoComMetodoDeSeguranca,
      Credencial credencialDestino,
      Integer codTransacao,
      String documentoRepresentante,
      ContaPagamento contaOrigem,
      ContaPagamento contaDestino) {
    if (contaDestino == null) {
      contaDestino = contaPagamentoService.findByIdNotNull(credencialDestino.getIdConta());
    }
    travaContasService.travaContas(
        contaDestino.getIdConta(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);

    if (!contaDestino.getIdInstituicao().equals(idInstituicaoOrigem)) {
      throw new GenericServiceException("Instituição destino diferente da instituição de origem.");
    }

    Credencial credencialOrigem = credencialService.findById(idCredencialOrigem);
    if (contaOrigem == null) {
      contaOrigem = contaPagamentoService.findByIdNotNull(credencialOrigem.getIdConta());
    }
    travaContasService.travaContas(
        contaOrigem.getIdConta(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);

    Boolean validadoComTokenOuMetodoDeSeguranca = validadoComMetodoDeSeguranca;

    Pessoa pessoa = null;
    if (ComSenha) {
      if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(idInstituicaoOrigem)
          || Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(idInstituicaoOrigem)
          || Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI.equals(idInstituicaoOrigem)
          || Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA.equals(idInstituicaoOrigem)
          || Constantes.ID_PRODUCAO_INSTITUICAO_AGRO_CASH.equals(idInstituicaoOrigem)) {
        pessoa = pessoaService.findPessoaTitularConta(contaOrigem.getIdConta());

        if (pessoa.getIdTipoPessoa() == 1) {
          TokenAcesso tokenAcesso =
              tokenAcessoService.findFirstByChaveExternaOrderByDataHoraGeracaoDesc(
                  pessoa
                      .getDddTelefoneCelular()
                      .toString()
                      .concat(
                          pessoa
                              .getTelefoneCelular()
                              .toString()
                              .concat(
                                  LocalDate.now()
                                      .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))));

          if (tokenAcesso != null
              && tokenAcesso.getDataHoraUtilizacao() != null
              && tokenAcesso.getDataHoraUtilizacao().isAfter(LocalDateTime.now().minusMinutes(5L))
              && tokenAcesso.getDataHoraExpiracaoToken().isAfter(LocalDateTime.now())
              && tokenAcesso.getDataHoraCancelamentoChave() == null) {

            validadoComTokenOuMetodoDeSeguranca = true;

          } else if (tokenAcesso != null
              && tokenAcesso.getDataHoraUtilizacao() == null
              && tokenAcesso.getDataHoraExpiracaoToken().isBefore(LocalDateTime.now())
              && tokenAcesso.getDataHoraCancelamentoChave() == null) {

            throw new GenericServiceException("Token Expirado.");

          } else if (tokenAcesso == null
              || tokenAcesso.getDataHoraCancelamentoChave() != null
              || tokenAcesso.getDataHoraUtilizacao() == null
              || tokenAcesso.getDataHoraExpiracaoToken().isBefore(LocalDateTime.now())
              || tokenAcesso
                  .getDataHoraUtilizacao()
                  .isBefore(LocalDateTime.now().minusMinutes(5L))) {

            Map<String, Object> map =
                credencialService.validarSenha(pinCredencialOrigem, idCredencialOrigem, tokenJWT);
            Boolean valida = (Boolean) map.get("sucesso");
            String msg = (String) map.get("erro");
            if (!valida) {
              throw new GenericServiceException(
                  idInstituicaoOrigem == 4001 ? msg : "Pin Inválido para a credencial.");
            }
          }
        } else if (pessoa.getIdTipoPessoa() == 2 && documentoRepresentante != null) {
          RepresentanteLegal representanteLegal =
              representanteRepository.findOneByIdContaAndCpfAndStatus(
                  contaOrigem.getIdConta(), documentoRepresentante, REPRESENTANTE_LEGAL_ATIVO);

          if (Util.isEmpty(representanteLegal)) {
            throw new GenericServiceException("Dados do Representante não encontrados.");
          }

          TokenAcesso tokenAcesso =
              tokenAcessoService.findFirstByChaveExternaOrderByDataHoraGeracaoDesc(
                  representanteLegal
                      .getDddCelular()
                      .toString()
                      .concat(
                          representanteLegal
                              .getTelefoneCelular()
                              .toString()
                              .concat(
                                  LocalDate.now()
                                      .format(DateTimeFormatter.ofPattern("yyyy-MM-dd")))));

          if (tokenAcesso != null
              && tokenAcesso.getDataHoraUtilizacao() != null
              && tokenAcesso.getDataHoraUtilizacao().isAfter(LocalDateTime.now().minusMinutes(5L))
              && tokenAcesso.getDataHoraExpiracaoToken().isAfter(LocalDateTime.now())
              && tokenAcesso.getDataHoraCancelamentoChave() == null) {

            validadoComTokenOuMetodoDeSeguranca = true;

          } else if (tokenAcesso != null
              && tokenAcesso.getDataHoraUtilizacao() == null
              && tokenAcesso.getDataHoraExpiracaoToken().isBefore(LocalDateTime.now())
              && tokenAcesso.getDataHoraCancelamentoChave() == null) {

            throw new GenericServiceException("Token Expirado.");

          } else if (tokenAcesso == null
              || tokenAcesso.getDataHoraCancelamentoChave() != null
              || tokenAcesso.getDataHoraUtilizacao() == null
              || tokenAcesso.getDataHoraExpiracaoToken().isBefore(LocalDateTime.now())
              || tokenAcesso
                  .getDataHoraUtilizacao()
                  .isBefore(LocalDateTime.now().minusMinutes(5L))) {

            Map<String, Object> map =
                credencialService.validarSenha(pinCredencialOrigem, idCredencialOrigem, tokenJWT);
            Boolean valida = (Boolean) map.get("sucesso");
            String msg = (String) map.get("erro");
            if (!valida) {
              throw new GenericServiceException(
                  idInstituicaoOrigem == 4001 ? msg : "Pin Inválido para a credencial.");
            }
          }
        } else {
          Map<String, Object> map =
              credencialService.validarSenha(pinCredencialOrigem, idCredencialOrigem, tokenJWT);
          Boolean valida = (Boolean) map.get("sucesso");
          String msg = (String) map.get("erro");
          if (!valida) {
            throw new GenericServiceException(
                idInstituicaoOrigem == 4001 ? msg : "Pin Inválido para a credencial.");
          }
        }
      } else {
        Map<String, Object> map =
            credencialService.validarSenha(pinCredencialOrigem, idCredencialOrigem, tokenJWT);
        Boolean valida = (Boolean) map.get("sucesso");
        String msg = (String) map.get("erro");
        if (!valida) {
          throw new GenericServiceException(
              idInstituicaoOrigem == 4001 ? msg : "Pin Inválido para a credencial.");
        }
      }
    }

    TipoStatus tipoStatusOrigem =
        contaOrigem.getTipoStatus() == null
            ? tipoStatusService.findById(contaOrigem.getIdStatusConta())
            : contaOrigem.getTipoStatus();
    TipoStatus tipoStatusDestino =
        contaDestino.getTipoStatus() == null
            ? tipoStatusService.findById(contaDestino.getIdStatusConta())
            : contaDestino.getTipoStatus();

    if (tipoStatusOrigem == null || tipoStatusDestino == null) {
      throw new GenericServiceException(
          "Não é possível realizar transferência .tipoStatus Não encontrado.");
    }
    Integer idGrupoStatusContaOrigem = tipoStatusOrigem.getIdGrupoStatus();
    Integer idGrupoStatusContaDestino = tipoStatusDestino.getIdGrupoStatus();

    if (!GRUPO_STATUS_ATIVO.equals(idGrupoStatusContaOrigem)) {

      throw new GenericServiceException(
          "Conta Origem não permite efetuar Tranferências. GrupoStatus= "
              + idGrupoStatusContaOrigem);
    }

    if (Constantes.GRUPO_STATUS_CANCELADO.equals(idGrupoStatusContaDestino)) {

      throw new GenericServiceException(
          "Conta Destino não permite receber Tranferências. GrupoStatus= "
              + idGrupoStatusContaDestino);
    }

    TransferenciaEntreConta transf =
        contaPagamentoService.prepareTranferenciaEntreContas(
            pinCredencialOrigem,
            valorTransferencia,
            credencialOrigem.getTokenInterno(),
            credencialDestino);

    JcardResponse sucesso =
        lancamentoService.saveTransferencia(
            contaOrigem,
            contaDestino,
            credencialOrigem,
            credencialDestino,
            valorTransferencia,
            tokenJWT,
            pinCredencialOrigem,
            codTransacao,
            validadoComTokenOuMetodoDeSeguranca);

    if (sucesso.getSuccess()) {
      ComunicadoContaViaPush comunicado =
          contaPagamentoService.getComunicadoPushTransferenciaCredencialOrigem(
              credencialOrigem, transf.getValor().doubleValue());
      enviarNotificacaoPush(comunicado);

      if (!credencialDestino.getIdCredencial().equals(credencialOrigem.getIdCredencial())) {
        ComunicadoContaViaPush comunicado2 =
            contaPagamentoService.getComunicadoPushTransferenciaCredencialDestino(
                credencialOrigem, transf.getValor().doubleValue());
        enviarNotificacaoPush(comunicado2);
      }

      eventoService.publicarMovimentacaoFinanceiraEvent(
          Servicos.TRANSFERENCIA_INTERNA_PORTADOR.getDescricao(),
          contaOrigem.getIdInstituicao(),
          contaDestino.getIdInstituicao(),
          contaOrigem.getIdConta(),
          contaDestino.getIdConta(),
          codTransacao,
          null,
          valorTransferencia,
          pessoa != null ? pessoa.getDocumento() : "0");
    }

    return sucesso;
  }

  public ContaPessoa findOneByIdContaAndTitularidade(Long idConta, Integer idTitularidade) {
    return contaPessoaService.findOneByIdContaAndIdTitularidade(idConta, idTitularidade);
  }

  /**
   * Metodo responsável por efetuar transferencias para portadores da mesma instituicao via
   * web/mobile instituição.
   *
   * @param model Request contendo daados para transferencia
   * @param tid
   */
  @Transactional
  public JcardResponse transferenciaMesmaInstituicaoViaWebMob(
      TransferenciaMesmaInstituicaoViaWeb model, String tid, String servico) {

    try {

      ContaPagamento contaOrigem = contaPagamentoService.findByIdNotNull(model.getIdContaOrigem());

      ContaPagamento contaDestino =
          contaPagamentoService.findByIdNotNull(model.getIdContaDestino());

      if (!contaDestino.getIdInstituicao().equals(contaOrigem.getIdInstituicao())) {
        throw new GenericServiceException(
            "Instituição destino diferente da instituição de origem.");
      }

      log.info(
          "Realizando transf. mesma instituicao. Conta origem: "
              + contaOrigem.getIdConta()
              + " Conta destino: "
              + contaDestino.getIdConta());

      TipoStatus tipoStatusOrigem =
          contaOrigem.getTipoStatus() == null
              ? tipoStatusService.findById(contaOrigem.getIdStatusConta())
              : contaOrigem.getTipoStatus();
      TipoStatus tipoStatusDestino =
          contaDestino.getTipoStatus() == null
              ? tipoStatusService.findById(contaDestino.getIdStatusConta())
              : contaDestino.getTipoStatus();

      if (tipoStatusOrigem == null || tipoStatusDestino == null) {
        throw new GenericServiceException(
            "Não é possível realizar transferência.tipoStatus Não encontrado.");
      }

      Integer idGrupoStatusContaOrigem = tipoStatusOrigem.getIdGrupoStatus();

      if (!GRUPO_STATUS_ATIVO.equals(idGrupoStatusContaOrigem)) {
        throw new GenericServiceException(
            "Conta Origem não permite efetuar Tranferências. GrupoStatus= "
                + idGrupoStatusContaOrigem);
      }

      Integer idGrupoStatusContaDestino = tipoStatusDestino.getIdGrupoStatus();

      if (Constantes.GRUPO_STATUS_CANCELADO.equals(idGrupoStatusContaDestino)) {

        throw new GenericServiceException(
            "Conta Destino não permite receber Tranferências. GrupoStatus= "
                + idGrupoStatusContaDestino);
      }

      Credencial credencialDestino =
          getCredencialFisicaMaisRecenteNotNull(model.getIdContaDestino());
      Credencial credencialOrigem = getCredencialFisicaMaisRecenteNotNull(model.getIdContaOrigem());

      Pessoa pessoa = pessoaService.findPessoaTitularConta(contaDestino.getIdConta());
      eventoService.publicarMovimentacaoFinanceiraEvent(
          servico,
          contaOrigem.getIdInstituicao(),
          contaDestino.getIdInstituicao(),
          contaOrigem.getIdConta(),
          contaDestino.getIdConta(),
          null,
          null,
          model.getValorTransferencia(),
          pessoa != null ? pessoa.getDocumento() : "0");

      return lancamentoService.saveTransferenciaViaWebMob(
          contaOrigem,
          contaDestino,
          credencialOrigem,
          credencialDestino,
          model.getValorTransferencia());

    } catch (GenericServiceException e) {
      return new JcardResponse(FALSE, e.getErros().toString());
    } catch (Exception e) {
      return new JcardResponse(FALSE, e.getMessage());
    }
  }

  private Credencial getCredencialFisicaMaisRecenteNotNull(Long idConta) {
    Collection<Integer> gruposNaoVemNaPesquisa = new ArrayList<>();
    gruposNaoVemNaPesquisa.add(Constantes.GRUPO_STATUS_CANCELADO);
    gruposNaoVemNaPesquisa.add(Constantes.GRUPO_STATUS_BLOQUEIO_ORIGEM);

    Credencial credencialDestino =
        credencialService.buscarCredencialFisicaMaisRecenteContaTitular(
            idConta,
            Constantes.TITULARIDADE_CREDENCIAL,
            Constantes.CREDENCIAL_NAO_VIRTUAL,
            gruposNaoVemNaPesquisa);
    if (Objects.isNull(credencialDestino)) {
      throw new GenericServiceException(
          "Credencial não encontrada para a conta idConta: " + idConta);
    }
    return credencialDestino;
  }

  @Transactional
  public Boolean transferenciaEntreContasB2b(
      TransferenciaEntreContasB2b model, String tokenJWT, SecurityUser user, String tid) {

    Credencial credencialDestino = null;
    Credencial credencialOrigem = null;

    credencialDestino =
        credencialService.buscarCredencialMaisRecente(
            model.getIdContaDestino().longValue(),
            model.getIdPessoaDestino().longValue(),
            Constantes.TITULARIDADE_CREDENCIAL);
    if (credencialDestino == null) {
      throw new GenericServiceException("Credencial Destino não encontrada.");
    }

    credencialOrigem =
        credencialService.buscarCredencialMaisRecente(
            model.getIdContaOrigem().longValue(),
            model.getIdPessoaOrigem().longValue(),
            Constantes.TITULARIDADE_CREDENCIAL);
    if (credencialOrigem == null) {
      throw new GenericServiceException("Credencial Origem não encontrada.");
    }

    ContaPagamento contaDestino =
        contaPagamentoService.findByIdNotNull(credencialDestino.getIdConta());

    ContaPagamento contaOrigem =
        contaPagamentoService.findByIdNotNull(model.getIdContaOrigem().longValue());

    TipoStatus tipoStatusOrigem =
        contaOrigem.getTipoStatus() == null
            ? tipoStatusService.findById(contaOrigem.getIdStatusConta())
            : contaOrigem.getTipoStatus();

    TipoStatus tipoStatusDestino =
        contaDestino.getTipoStatus() == null
            ? tipoStatusService.findById(contaDestino.getIdStatusConta())
            : contaDestino.getTipoStatus();

    if (tipoStatusOrigem == null || tipoStatusDestino == null) {
      throw new GenericServiceException(
          "Não é possível realizar transferência .tipoStatus Não encontrado.");
    }

    Integer idGrupoStatusContaOrigem = tipoStatusOrigem.getIdGrupoStatus();

    Integer idGrupoStatusContaDestino = tipoStatusDestino.getIdGrupoStatus();

    if (!GRUPO_STATUS_ATIVO.equals(idGrupoStatusContaOrigem)) {
      throw new GenericServiceException(
          "Conta Origem não permite efetuar Tranferências. GrupoStatus= "
              + idGrupoStatusContaOrigem);
    }

    if (Constantes.GRUPO_STATUS_CANCELADO.equals(idGrupoStatusContaDestino)) {
      throw new GenericServiceException(
          "Conta Destino não permite receber Tranferências. GrupoStatus= "
              + idGrupoStatusContaDestino);
    }

    LogTransferenciaB2b log =
        debitarValorTransferB2b(
            contaOrigem, credencialOrigem, model.getValor(), tid, user, contaDestino);

    if (log.getSucessoDebito()) {
      log =
          creditarValorTransferB2b(
              contaDestino, credencialDestino, model.getValor(), tid, user, contaOrigem, log);
    }

    log.setDataHoraInclusao(LocalDateTime.now());
    log.setIdUsuarioInclusao(user.getIdUsuario());
    logTransferenciaB2bService.save(log);

    Boolean status = log.getSucessoDebito() && log.getSucessoCredito();
    if (status) {
      Pessoa pessoa = pessoaService.findPessoaTitularConta(contaDestino.getIdConta());
      eventoService.publicarMovimentacaoFinanceiraEvent(
          Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE.getDescricao(),
          contaOrigem.getIdProdutoInstituicao(),
          contaDestino.getIdInstituicao(),
          contaOrigem.getIdConta(),
          contaDestino.getIdConta(),
          log.getCodTransacaoCredito(),
          log.getCodTransacaoDebito(),
          model.getValor(),
          pessoa != null ? pessoa.getDocumento() : "0");
    }

    return status;
  }

  @Transactional
  public LogTransferenciaB2b debitarValorTransferB2b(
      ContaPagamento contaOrigem,
      Credencial credencialOrigem,
      BigDecimal valor,
      String tid,
      SecurityUser user,
      ContaPagamento contaDestino) {
    Integer codTransacao = Constantes.COD_TRANSACAO_TRANSFER_B2B_DEBITO;
    String idMoeda = "986";
    Integer sinal = Constantes.DEBITADO; // debitar
    String freedata = "Tranf. contas via usuário B2B"; // texto extrato
    String rrn = null;
    Integer stan = null;

    ProdutoInstituicaoConfiguracao produto =
        contaPagamentoService.getProdutoConfig(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaOrigem, produto);

    stan = lancamentoService.getStan();
    rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN);

    JcardResponse jcard =
        lancamentoService.doTransferenciaB2b(
            accountCode,
            codTransacao,
            valor,
            idMoeda,
            rrn,
            sinal,
            credencialOrigem.getTokenInterno(),
            contaOrigem.getIdConta(),
            freedata,
            null,
            tid,
            null,
            stan,
            null);

    LogTransferenciaB2b log =
        montarParteDebito(rrn, stan, contaOrigem.getIdConta(), valor, sinal, codTransacao, jcard);

    // lancamentoService.checaJcardExcecao(jcard);

    return log;
  }

  @Transactional
  public Boolean debitarValorTransferByIdsConta(
      ContaPagamento contaOrigem,
      Credencial credencialOrigem,
      BigDecimal valor,
      String tid,
      ContaPagamento contaDestino) {
    Integer codTransacao = Constantes.COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_DEBITO;
    String idMoeda = "986";
    Integer sinal = Constantes.DEBITADO; // debitar
    String freedata = "Transf. entre contas via Web/Mob"; // texto extrato
    String rrn = null;
    Integer stan = null;

    ProdutoInstituicaoConfiguracao produto =
        contaPagamentoService.getProdutoConfig(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaOrigem, produto);

    stan = lancamentoService.getStan();
    rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN);

    JcardResponse jcard =
        lancamentoService.doTransferenciaSimples(
            accountCode,
            codTransacao,
            valor,
            idMoeda,
            rrn,
            sinal,
            credencialOrigem.getTokenInterno(),
            contaOrigem.getIdConta(),
            freedata,
            null,
            tid,
            null,
            stan,
            null);

    lancamentoService.checaJcardExcecao(jcard);

    return true;
  }

  private LogTransferenciaB2b montarParteDebito(
      String rrnDebito,
      Integer stanDebito,
      Long idContaDebito,
      BigDecimal valorDebito,
      Integer sinal,
      Integer codTransacaoDebito,
      JcardResponse jcard) {

    LogTransferenciaB2b log = new LogTransferenciaB2b();
    log.setRrnDebito(rrnDebito);
    log.setStanDebito(stanDebito);
    log.setIdContaDebito(idContaDebito);
    log.setValorDebito(valorDebito);
    log.setSinalDebito(sinal);
    log.setCodTransacaoDebito(codTransacaoDebito);
    if (jcard.getSuccess()) {
      log.setSucessoDebito(TRUE);
    } else {
      log.setErroDebito(jcard.getErrors());
      log.setSucessoDebito(Boolean.FALSE);
    }
    logTransferenciaB2bService.save(log);
    rrnLogService.salvarLog(
        idContaDebito, null, codTransacaoDebito, rrnDebito, jcard.getSuccess(), jcard.getErrors());
    return log;
  }

  @Transactional
  public Boolean creditarValorTransferByIdsConta(
      ContaPagamento contaDestino,
      Credencial credencialDestino,
      BigDecimal valor,
      String tid,
      ContaPagamento contaOrigem) {
    Integer codTransacao = Constantes.COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_CREDITO;
    String idMoeda = Constantes.CODIGO_MOEDA_PADRAO;
    Integer sinal = Constantes.CREDITADO; // creditar
    String freedata = "Transf. entre contas via Web/Mob"; // texto extrato
    String rrn = null;
    Integer stan = null;

    ProdutoInstituicaoConfiguracao produto =
        contaPagamentoService.getProdutoConfig(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaDestino, produto);

    boolean isProdutoMoeda =
        Constantes.CODIGO_MOEDA_DE_PONTO.equals(produto.getMoeda().getIdMoeda());
    Optional<CotacaoPontos> cotacaoPontosOptional =
        Optional.ofNullable(
            cotacaoPontosRepository.findByIdInstituicao(contaDestino.getIdInstituicao()));
    CotacaoPontos cotacaoPontos = cotacaoPontosOptional.orElseGet(CotacaoPontos::new);
    Credencial credencial =
        credencialService.buscarCredencialParaLancamentoManual(contaDestino.getIdConta());

    JcardResponse jcard =
        lancamentoService.doLancamentoManual(
            accountCode,
            codTransacao,
            isProdutoMoeda ? valor.multiply(cotacaoPontos.getValorConversao()) : valor,
            produto.getMoeda().getIdMoeda().toString(),
            rrn,
            sinal,
            credencial.getTokenInterno(),
            contaDestino.getIdConta(),
            freedata,
            null,
            null,
            null,
            stan,
            null);

    return jcard.getSuccess();
  }

  @Transactional
  public LogTransferenciaB2b creditarValorTransferB2b(
      ContaPagamento contaDestino,
      Credencial credencialDestino,
      BigDecimal valor,
      String tid,
      SecurityUser user,
      ContaPagamento contaOrigem,
      LogTransferenciaB2b log) {
    Integer codTransacao = Constantes.COD_TRANSACAO_TRANSFER_B2B_CREDITO;
    String idMoeda = "986";
    Integer sinal = Constantes.CREDITADO; // creditar
    String freedata = "Tranf. contas via usuário B2B"; // texto extrato
    String rrn = null;
    Integer stan = null;

    ProdutoInstituicaoConfiguracao produto =
        contaPagamentoService.getProdutoConfig(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaDestino, produto);

    stan = lancamentoService.getStan();
    rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN);

    JcardResponse jcard =
        lancamentoService.doTransferenciaB2b(
            accountCode,
            codTransacao,
            valor,
            idMoeda,
            rrn,
            sinal,
            credencialDestino.getTokenInterno(),
            contaDestino.getIdConta(),
            freedata,
            null,
            tid,
            null,
            stan,
            null);

    log =
        montarParteCredito(
            rrn, stan, contaDestino.getIdConta(), valor, sinal, codTransacao, jcard, log);

    return log;
  }

  private LogTransferenciaB2b montarParteCredito(
      String rrnCredito,
      Integer stanCredito,
      Long idContaCredito,
      BigDecimal valorCredito,
      Integer sinal,
      Integer codTransacaoCredito,
      JcardResponse jcard,
      LogTransferenciaB2b log) {

    log.setRrnCredito(rrnCredito);
    log.setStanCredito(stanCredito);
    log.setIdContaCredito(idContaCredito);
    log.setValorCredito(valorCredito);
    log.setSinalCredito(sinal);
    log.setCodTransacaoCredito(codTransacaoCredito);
    if (jcard.getSuccess()) {
      log.setSucessoCredito(TRUE);
    } else {
      log.setErroCredito(jcard.getErrors());
      log.setSucessoCredito(Boolean.FALSE);
    }
    rrnLogService.salvarLog(
        null,
        idContaCredito,
        codTransacaoCredito,
        rrnCredito,
        jcard.getSuccess(),
        jcard.getErrors());
    return log;
  }

  /**
   * Metodo responsável por efetuar transferencias para portadores da mesma instituição.
   *
   * @param idBancoDestino
   * @param idAgenciaDestino
   * @param contaCorrenteDestino
   * @param idInstituicaoOrigem
   * @param pinCredencialOrigem
   * @param idCredencialOrigem
   * @param valorTransferencia
   * @param tokenJWT
   * @param idLoginUser
   * @param ipOrigem
   * @param comSenha
   * @param tipoContaDestino
   * @return boolean
   */
  @Transactional
  public boolean transferenciaContaCorrente(
      Integer idBancoDestino,
      Integer idAgenciaDestino,
      Long contaCorrenteDestino,
      Integer idInstituicaoOrigem,
      String pinCredencialOrigem,
      Long idCredencialOrigem,
      Double valorTransferencia,
      String tokenJWT,
      Integer idLoginUser,
      String ipOrigem,
      boolean comSenha,
      Integer tipoContaDestino) {

    validarBanco(idBancoDestino);
    validarAgencia(idAgenciaDestino, idBancoDestino);

    Credencial credencial = obterCredencial(idCredencialOrigem);

    validarInstituicaoBRB(idInstituicaoOrigem, valorTransferencia, credencial);

    ContaPagamento contaOrigem = obterContaOrigemPagamento(credencial);

    travaContasService.travaContas(contaOrigem.getIdConta(), Servicos.TED);
    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(contaOrigem, Servicos.TED);

    validarPin(pinCredencialOrigem, idCredencialOrigem, tokenJWT, comSenha);

    String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);

    LancamentoManual lancamento =
        realizarLancamentoManual(
            idBancoDestino,
            idAgenciaDestino,
            contaCorrenteDestino,
            valorTransferencia,
            contaOrigem,
            rrn);

    TransferenciaContaBancaria transfer =
        realizarTransferenciaBancaria(
            idBancoDestino,
            idAgenciaDestino,
            contaCorrenteDestino,
            valorTransferencia,
            idLoginUser,
            contaOrigem,
            rrn,
            tipoContaDestino);

    realizarLancamentoDebitoJcard(ipOrigem, credencial, contaOrigem, lancamento, rrn);

    //	     Comentando pois ainda não foram realizados todos os testes.
    //	     if(jcardResponse.getSuccess() &&
    //	     !Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(idInstituicaoOrigem)){
    //	     ComunicadoContaViaPush comunicado =
    //
    // contaPagamentoService.getComunicadoPushTransferenciaCredencialOrigem(credencial,transfContaCorrente.getValor().doubleValue());
    //	     enviarNotificacaoPush(comunicado);
    //	     }

    boolean status = Objects.nonNull(transfer.getIdTransferenciaContaBancaria());

    if (status) {
      eventoService.publicarMovimentacaoFinanceiraEvent(
          "TED",
          contaOrigem.getIdInstituicao(),
          idBancoDestino,
          contaOrigem.getIdConta(),
          contaCorrenteDestino,
          null,
          null,
          new BigDecimal(valorTransferencia),
          "");
    }

    return status;
  }

  private TransferenciaContaBancaria realizarTransferenciaBancaria(
      Integer idBancoDestino,
      Integer idAgenciaDestino,
      Long contaCorrenteDestino,
      Double valorTransferencia,
      Integer idLoginUser,
      ContaPagamento contaOrigem,
      String rrn,
      Integer tipoContaDestino) {
    TransferenciaContaBancaria transfer = new TransferenciaContaBancaria();
    transfer.setIdContaOrigem(contaOrigem.getIdConta());
    transfer.setDataHoraInclusao(LocalDateTime.now());
    transfer.setIdUsuarioInclusao(idLoginUser.longValue());
    transfer.setValorTransferencia(BigDecimal.valueOf(valorTransferencia));
    transfer.setRrn(rrn);
    transfer.setTipoContaDestino(
        Objects.isNull(tipoContaDestino) ? TIPO_CONTA_CORRENTE : tipoContaDestino);
    transfer.setIdContaOrigem(contaOrigem.getIdConta());
    transfer.setIdBancoDestino(idBancoDestino);
    transfer.setIdAgenciaDestino(idAgenciaDestino);
    transfer.setContaBancariaDestino(contaCorrenteDestino);

    transfer = transferenciaContaBancariaService.saveAndFlush(transfer);

    if (Objects.isNull(transfer.getIdTransferenciaContaBancaria())) {
      throw new GenericServiceException(
          "Transferência realizada. Mas não foi possível gravar o log da transferência");
    }
    return transfer;
  }

  private LancamentoManual realizarLancamentoManual(
      Integer idBancoDestino,
      Integer idAgenciaDestino,
      Long contaCorrenteDestino,
      Double valorTransferencia,
      ContaPagamento contaOrigem,
      String rrn) {
    LancamentoManual lancamento = new LancamentoManual();

    lancamento.setIdUsuario(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);
    lancamento.setDataHoraLancamento(LocalDateTime.now());
    lancamento.setIdProcessadora(contaOrigem.getIdProcessadora());
    lancamento.setIdInstituicao(contaOrigem.getIdInstituicao());
    lancamento.setRrn(rrn);
    lancamento.setCodTransacao(Constantes.COD_TRANSACAO_TED);
    lancamento.setIdConta(contaOrigem.getIdConta());
    lancamento.setValor(BigDecimal.valueOf(valorTransferencia));
    lancamento.setTextoExtrato(
        "BC: "
            + idBancoDestino
            + " - AG: "
            + idAgenciaDestino
            + " - C/C: "
            + Util.formataContaCorrente(contaCorrenteDestino));
    lancamento = lancamentoService.saveAndFlush(lancamento);
    return lancamento;
  }

  private void realizarLancamentoDebitoJcard(
      String ipOrigem,
      Credencial credencial,
      ContaPagamento contaOrigem,
      LancamentoManual lancamento,
      String rrn) {

    ProdutoInstituicaoConfiguracao produtoConfig =
        lancamentoService.getProduto(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaOrigem, produtoConfig);

    Integer stan = lancamentoService.getStan();

    JcardResponse jcardResponse =
        lancamentoService.doLancamentoManual(
            accountCode,
            lancamento.getCodTransacao(),
            lancamento.getValor(),
            produtoConfig.getMoeda().getIdMoeda().toString(),
            rrn,
            -1,
            credencial.getTokenInterno(),
            lancamento.getIdConta(),
            lancamento.getTextoExtrato(),
            null,
            ipOrigem,
            null,
            stan,
            null);

    if (!jcardResponse.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível realizar a transferência solicitada na contaOrigem:"
              + contaOrigem.getIdConta());
    }
  }

  private ContaPagamento obterContaOrigemPagamento(Credencial credencial) {
    ContaPagamento contaOrigem = contaPagamentoService.findById(credencial.getIdConta());

    validarContaOrigem(contaOrigem);

    return contaOrigem;
  }

  private void validarContaOrigem(ContaPagamento contaOrigem) {
    if (Objects.isNull(contaOrigem)) {
      throw new GenericServiceException("Conta Não encontrada.");
    }

    TipoStatus tipoStatusOrigem =
        Objects.isNull(contaOrigem.getTipoStatus())
            ? tipoStatusService.findById(contaOrigem.getIdStatusConta())
            : contaOrigem.getTipoStatus();

    if (Objects.isNull(tipoStatusOrigem)) {
      throw new GenericServiceException(
          "Não é possível realizar transferência. TipoStatus Não encontrado.");
    }

    Integer idGrupoStatusContaOrigem = tipoStatusOrigem.getIdGrupoStatus();
    if (!GRUPO_STATUS_ATIVO.equals(idGrupoStatusContaOrigem)) {
      throw new GenericServiceException(
          "Conta Origem não permite efetuar Tranferências. GrupoStatus= "
              + idGrupoStatusContaOrigem);
    }
  }

  private void validarPin(
      String pinCredencialOrigem, Long idCredencialOrigem, String tokenJWT, boolean comSenha) {
    if (comSenha
        && !credencialService.validarPin(pinCredencialOrigem, idCredencialOrigem, tokenJWT)) {
      throw new GenericServiceException("Pin Inválido para a credencial.");
    }
  }

  private void validarInstituicaoBRB(
      Integer idInstituicaoOrigem, Double valorTransferencia, Credencial credencial) {
    if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(idInstituicaoOrigem)) {

      if (valorTransferencia.compareTo(Constantes.MAXIMO_TRANSFERIVEL_BRB) > 0) {
        throw new GenericServiceException(
            "O valor máximo permitido para transferência é de R$5.000,00 (cinco mil reais).");
      } else if (transferenciaContaBancariaService.existeTransferenciaNoMesAtual(
          credencial.getIdConta())) {
        throw new GenericServiceException(
            "A transferência mensal já foi efetuada. Aguarde até o próximo mês para realizar sua transferência de até R$5.000,00 (cinco mil reais).");
      }
    }
  }

  private Credencial obterCredencial(Long idCredencialOrigem) {
    Credencial credencial = credencialService.findById(idCredencialOrigem);
    if (Objects.isNull(credencial)) {
      throw new GenericServiceException("Credencial Não encontrada.");
    }
    return credencial;
  }

  private void validarAgencia(Integer idAgenciaDestino, Integer idBancoDestino) {
    Agencia agencia = agenciaService.findOneByIdBancoAndIdAgencia(idBancoDestino, idAgenciaDestino);

    if (Objects.isNull(agencia)) {
      throw new GenericServiceException(
          "Agência não encontrada para as informações enviadas!",
          "idAgencia: " + idAgenciaDestino + " idBanco: " + idBancoDestino);
    }
  }

  private void validarBanco(Integer idBancoDestino) {
    Banco banco = bancoService.findByIdBanco(idBancoDestino);

    if (Objects.isNull(banco)) {
      throw new GenericServiceException(
          "Banco não encontrado para as informações enviadas!", " idBanco: " + idBancoDestino);
    }
  }

  @Transactional
  public ContaPagamento createContaPagamento(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      Integer idProdutoInstituicao,
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao) {

    ContaPagamento conta = new ContaPagamento();
    ContaPagamentoRequest createConta = new ContaPagamentoRequest();

    contaPagamentoService.prepareCadastrarConta(
        cadastrarContaPagPessoa, pessoa, conta, createConta, valorCargaProdutoInstituicao);
    if (Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI.equals(
            cadastrarContaPagPessoa.getIdInstituicao())
        && Constantes.PESSOA_JURIDICA.equals(
            pessoa.getTipoPessoa() != null
                ? pessoa.getTipoPessoa().getTipoPessoa()
                : pessoa.getIdTipoPessoa())) {
      conta.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);

    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(
        cadastrarContaPagPessoa.getIdInstituicao())) {
      if (Constantes.PESSOA_FISICA.equals(
          pessoa.getTipoPessoa() != null
              ? pessoa.getTipoPessoa().getTipoPessoa()
              : pessoa.getIdTipoPessoa())) {
        conta.setIdStatusV2(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);

      } else {
        conta.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);
      }
    }

    geradorContaService.createContaPagamento(createConta, conta);
    return conta;
  }

  /**
   * Método responsavel por criar conta(s) e credencia(l)(is) para uma pessoa existente
   *
   * @param criarContaPagamento
   */
  @Transactional
  public ContaPagamento criarContasPagamentoECredenciais(
      CriarContaPagamento criarContaPagamento, SecurityUser user) {

    ContaPagamento conta = new ContaPagamento();
    Pessoa pessoa = pessoaService.findById(criarContaPagamento.getIdPessoa());

    if (pessoa == null) {

      throw new GenericServiceException(
          "Pessoa não encontrada. idPessoa: " + criarContaPagamento.getIdPessoa());
    }

    travaServicosService.travaServicos(pessoa.getIdInstituicao(), Servicos.CRIAR_CONTA_ISSUER);

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(pessoa.getIdInstituicao())) {
      Boolean permiteCadastrar = verificaPermiteCadastrarConta(criarContaPagamento, pessoa);
      if (!permiteCadastrar) {
        throw new GenericServiceException(
            "É necessário possuir uma conta do Saldo Livre antes de cadastrar outra conta do Multiconta.");
      }
    }

    /*
     * cria a quantidade de contas e cartoes necessarios, conforme a
     * quantidade de produtos que vieram na requisicao
     */
    List<Boolean> temCorrespList = new ArrayList<>();
    Boolean produtoIntegracao = FALSE;
    List<Long> contasIntegracao = new ArrayList<>();
    for (ValorCargaProdutoInstituicao valorProduto :
        criarContaPagamento.getValoresCargasProdutos()) {
      if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(pessoa.getIdInstituicao())) {
        Boolean temCorresp =
            produtoInstituicaoCorrespondenteService.findIfExistsCorresp(
                valorProduto.getIdProdutoInstituicao());
        temCorrespList.add(temCorresp);
        if (temCorresp) {
          ContasCredenciaisReplicaveisReponse empresa =
              produtoInstituicaoCorrespondenteService.montaVerificacaoEmpresa(
                  valorProduto.getIdProdutoInstituicao(), user);
          produtoInstituicaoCorrespondenteService.validarDadosReplicacao(empresa);
        }
      }

      List<ContaPagamento> contasBuscadas =
          contaPagamentoService.findContasBuscadas(
              pessoa.getIdPessoa(), valorProduto.getIdProdutoInstituicao());

      if (contasBuscadas != null && !contasBuscadas.isEmpty()) {
        throw new GenericServiceException(
            "Pessoa já possui o produto solicitado. idPessoa= "
                + pessoa.getIdPessoa()
                + " idProdutoInstituicao= "
                + valorProduto.getIdProdutoInstituicao());
      }

      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa =
          new CadastrarContaPagamentoPessoaRequest();
      BeanUtils.copyProperties(
          criarContaPagamento,
          cadastrarContaPagPessoa,
          contaPagamentoService.getNullPropertyNames(criarContaPagamento));
      BeanUtils.copyProperties(
          pessoa, cadastrarContaPagPessoa, contaPagamentoService.getNullPropertyNames(pessoa));

      ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              cadastrarContaPagPessoa.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              cadastrarContaPagPessoa.getIdInstituicao());

      if (produtoInstituicaoConfiguracao.getIdGrupoProduto() != null) {
        List<CredencialConta> listaContasMesmoGrupo =
            credencialContaService.findCredencialContaByDocumentoAndGrupoProdutosAndHierarquia(
                pessoa.getDocumento(),
                produtoInstituicaoConfiguracao.getIdGrupoProduto(),
                cadastrarContaPagPessoa.getIdInstituicao(),
                cadastrarContaPagPessoa.getIdProcessadora(),
                cadastrarContaPagPessoa.getIdFilial(),
                cadastrarContaPagPessoa.getIdRegional(),
                cadastrarContaPagPessoa.getIdPontoDeRelacionamento());

        if (isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
            cadastrarContaPagPessoa.getIdProcessadora(),
            cadastrarContaPagPessoa.getIdInstituicao(),
            cadastrarContaPagPessoa.getDocumento(),
            valorProduto.getIdProdutoInstituicao(),
            cadastrarContaPagPessoa.getIdPontoDeRelacionamento())) {
          throw new GenericServiceException(
              "Conta já existente para esta instituição, documento, produto "
                  + "e ponto de relacionamento");
        }

        conta =
            createContaPagamento(
                cadastrarContaPagPessoa,
                pessoa,
                valorProduto.getIdProdutoInstituicao(),
                valorProduto);
        contaPagamentoService.vincularContaPessoa(pessoa, conta);

        // tive que trazer a lógica de verificação se o cartão é virtual ou físico pra cá
        Boolean seraVirtual =
            contaPagamentoService.setVirtualCartaoBaseadoConfiguracaoProduto(conta);

        // Filtra lista de credenciais-conta do mesmo grupo para deixar apenas status 0 (bloqueio
        // origem),
        // 1 (desbloqueado) e 5 (bloqueio temporário)
        int[] statusIncludeList = new int[] {0, 1, 5};
        listaContasMesmoGrupo =
            listaContasMesmoGrupo.stream()
                .filter(
                    cred ->
                        ArrayUtils.contains(
                            statusIncludeList, cred.getCredencial().getIdStatusV2()))
                .collect(Collectors.toList());

        // mapa de lista de contas -> map.get(true) = credenciais virtuais
        // map.get(false) = credenciais físicas
        Map<Boolean, List<CredencialConta>> contasMesmoGrupoVirtualMap =
            listaContasMesmoGrupo.stream()
                .collect(Collectors.partitioningBy(cc -> cc.getCredencial().getVirtual()));

        if (contasMesmoGrupoVirtualMap.get(seraVirtual).isEmpty()) {
          contaPagamentoService.createCredencial(cadastrarContaPagPessoa, pessoa, conta);
        } else {
          credencialContaService.vincularCredencialConta(
              conta,
              contasMesmoGrupoVirtualMap.get(seraVirtual).get(0).getCredencial(),
              produtoInstituicaoConfiguracao,
              pessoa);
        }

        if (!contasMesmoGrupoVirtualMap.get(!seraVirtual).isEmpty()) {
          credencialContaService.vincularCredencialConta(
              conta,
              contasMesmoGrupoVirtualMap.get(!seraVirtual).get(0).getCredencial(),
              produtoInstituicaoConfiguracao,
              pessoa);
        }

        if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
            conta, produtoInstituicaoConfiguracao)) {
          if (contasMesmoGrupoVirtualMap.get(false).isEmpty()) {
            contaPagamentoService.createCredencialFisicaComplementar(pessoa, conta);
          } else {
            credencialContaService.vincularCredencialConta(
                conta,
                contasMesmoGrupoVirtualMap.get(false).get(0).getCredencial(),
                produtoInstituicaoConfiguracao,
                pessoa);
          }
        }
      } else {
        conta =
            createContaPagamento(
                cadastrarContaPagPessoa,
                pessoa,
                valorProduto.getIdProdutoInstituicao(),
                valorProduto);
        contaPagamentoService.vincularContaPessoa(pessoa, conta);
        contaPagamentoService.createCredencial(cadastrarContaPagPessoa, pessoa, conta);

        if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
            conta, produtoInstituicaoConfiguracao)) {
          contaPagamentoService.createCredencialFisicaComplementar(pessoa, conta);
        }
      }

      if (Boolean.TRUE.equals(
          prodInstConfigService.obterCargaIntegracao(conta.getIdProdutoInstituicao()))) {
        produtoIntegracao = TRUE;
        contasIntegracao.add(conta.getIdConta());
      }
    }

    if (conta.getIdInstituicao() != null
        && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(conta.getIdInstituicao())) {
      if (temCorrespList.contains(TRUE)) {
        produtoInstituicaoCorrespondenteService.preparaAgendamentoReplicar();
      }
      if (produtoIntegracao) {
        HashMap<String, List<Long>> cpfsContasPreCadastro = new HashMap<>();
        cpfsContasPreCadastro.put(pessoa.getDocumento(), contasIntegracao);
        preCadastroService.agendarPreCadastro(cpfsContasPreCadastro, user);
      }
    }
    return conta;
  }

  public Boolean isDocumentoCadastradoConta(String documento, Long idConta) {
    return pessoaService.existeDocumentoPessoasConta(documento, idConta);
  }

  public Pessoa findOneByIdPessoa(Long idPessoa) {
    return pessoaService.findOneByIdPessoa(idPessoa);
  }

  public Pessoa findOneByHierarquia(
      Integer idProc,
      Integer idInst,
      String doc,
      Integer tipoPessoa,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRel) {
    return pessoaService.findOneByHierarquia(
        idProc, idInst, doc, tipoPessoa, idRegional, idFilial, idPontoRel);
  }

  public void createPessoaJuridica(CadastrarPessoaJuridica cad, Pessoa p) {
    pessoaService.createPessoaJuridica(cad, p);
  }

  public Pessoa createPessoaFisica(CadastrarPessoaFisica cadastrarPessoa, Pessoa pessoa) {
    return pessoaService.createPessoaFisica(cadastrarPessoa, pessoa);
  }

  public List<Pessoa> buscarPessoasAdicionaisConta(Long idConta) {
    return pessoaService.buscarPessoasAdicionaisConta(idConta);
  }

  public Pessoa createPFAdicional(Pessoa p) {
    return pessoaService.createPFAdicional(p);
  }

  public GetPerfilTariafarioResponse buscarTarifasConta(Long idConta) {

    Integer idPerfilTarifario = perfilTarifarioService.buscarIdPerfilTarifario(idConta);

    return perfilTarifarioService.findByIdPerfilTarifario(idPerfilTarifario);
  }

  @Transactional(rollbackFor = GenericServiceException.class)
  public void desbloquearCredencialByLote(Integer idLoteEmissao, SecurityUser user) {
    List<Credencial> credenciais = credencialService.findByIdLoteEmissao(idLoteEmissao);

    for (Credencial credencial : credenciais) {
      contaPagamentoService.desbloquearCredencial(
          credencial.getIdCredencial(),
          contaPagamentoService.getAutorAlteracao(user.getIdUsuario()),
          user.getIdUsuario());
    }
  }

  public Credencial findById(Long idCredencial) {
    return credencialService.findById(idCredencial);
  }

  public ContaPagamento findContaByIdConta(Long idConta) {
    return contaPagamentoService.findById(idConta);
  }

  public Boolean habilitarCredencial(Credencial credencial, Integer idUsuario) {
    return credencialService.habilitarUsoCredencial(credencial, idUsuario);
  }

  public Credencial saveCredencial(Credencial credencial) {
    return credencialService.save(credencial);
  }

  public List<Credencial> findByIdConta(Long idConta) {
    return credencialService.findByIdContaInCredencialConta(idConta);
  }

  public List<Credencial> findByIdContaNaoVirtual(Long idConta) {
    return credencialService.findByIdContaNaoVirtual(idConta);
  }

  public List<Credencial> findByIdContaAndUltimos4Digitos(Long idConta, Long ultimosQuatro) {
    return credencialService.findByIdContaAndUltimos4Digitos(idConta, ultimosQuatro);
  }

  public List<Credencial> findByIdContaAndUltimos4DigitosNaoVirtual(
      Long idConta, Long ultimosQuatro) {
    return credencialService.findByIdContaAndUltimos4DigitosNaoVirtual(idConta, ultimosQuatro);
  }

  public TipoStatus findByIdTipoStatus(Integer id) {
    return tipoStatusService.findById(id);
  }

  @Transactional
  public CredencialGerada gerarCredencial(GerarCredencialRequest gerarCredencial) {
    return geradorCredencialService.gerarCredencial(gerarCredencial);
  }

  /** Quando limite for nulo, valor sera recuperado da configuracao do produto */
  @Transactional
  public CredencialGerada gerarCredencialAdicional(
      GerarCredencialRequest gerarCredencial, BigDecimal limitePrioritario) {
    return geradorCredencialService.gerarCredencialAdicional(gerarCredencial, limitePrioritario);
  }

  public CredencialGerada gerarCredencialPreEmitida(
      GerarCredencialRequest gerarCredencial, CredencialPreEmitida credPreEmi) {
    return geradorCredencialService.gerarCredencialPreEmitida(gerarCredencial, credPreEmi);
  }

  public String getNumeroCredencialEmClaro(Long idCredencial) {
    return credencialService.getNumeroCredencialEmClaro(idCredencial);
  }

  public String getNumeroCredencialEmClaro(Credencial credencial) {
    return credencialService.getNumeroCredencialEmClaro(credencial);
  }

  public ContaPessoa findOneByIdPessoaAndIdConta(Long idPessoa, Long idConta) {
    return contaPessoaService.findOneByIdPessoaAndIdConta(idPessoa, idConta);
  }

  public ContaPessoa saveContaPessoa(ContaPessoa contaPessoa) {
    return contaPessoaService.save(contaPessoa);
  }

  public List<ContaPessoa> lisContaPessoafindByIdConta(Long idConta) {
    return contaPessoaService.findByIdConta(idConta);
  }

  public void vincularConta(ContaPessoa contaPessoa) {
    contaPessoaService.vincularConta(contaPessoa);
  }

  public void vincularPessoaAdicionalConta(
      Pessoa p, ContaPagamento conta, ContaPessoa contaPessoa) {
    contaPessoaService.vincularPessoaAdicionalConta(p, conta, contaPessoa);
  }

  @Async
  public void enviarNotificacaoPush(ComunicadoContaViaPush comunicado) {
    comunicadorPushService.prepararComunicacao(comunicado);
    comunicadorPushService.comunicar(comunicado.getIdLogEventoConta());
  }

  @Transactional
  public ResponseEntity<HashMap<String, Object>> doLancamentoTransacaoCompraFilialEstab(
      SecurityUser user, LancamentoContaEstabelecimentoVo model) throws AcquirerClientException {
    HashMap<String, Object> map = new HashMap<>();

    Long numeroLogico =
        estabelecimentoUnidadeService.getNumeroLogicoByIdFilialEstabelecimento(
            model.getIdFilialEstabelecimento());

    if (numeroLogico == null) {
      map.put("msg", "Não foi possível encontrar o número lógico do estabelecimento solicitante.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    ContaPagamento conta = contaPagamentoService.findContaTitular(model.getIdConta());

    if (conta == null) {
      map.put("msg", "Não foi possível encontrar a conta informada.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    Integer codigoTransacao =
        contaPagamentoService.getCodTransacaoByProdPlatAndProdInstituidorConta(
            conta.getIdProdutoPlataforma(),
            conta.getProdutoInstituidor().getFuncao(),
            model.getQuantidadeParcelas(),
            model.getEstorno(),
            model.getTipoTransacao());

    if (codigoTransacao == null) {
      map.put("msg", "O produto plataforma desta conta não permite esta transação.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    Credencial credencial = credencialService.findById(model.getIdCredencial());

    if (credencial == null) {
      throw new GenericServiceException(
          "Credencial não encontrada. ID: " + model.getIdCredencial());
    }

    String numeroCartao = null;

    if (credencial.getIdCredencialExterna() != null) {
      numeroCartao =
          credencialService.descriptografarCredencialExterna(
              credencial.getIdCredencialExterna(), Constantes.ZPK_001);
    } else {
      GetCardResponse card = cardService.getPan(credencial.getTokenInterno());

      if (!card.getSuccess()) {
        map.put("msg", "Não foi possível encontrar o cartão.");
        return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
      }

      numeroCartao = card.getCard().getPan();
    }

    String dataVencimentoCartao =
        DateUtil.dateFormat("yyMM", DateUtil.localDateTimeToDate(credencial.getDataValidade()));

    return doTransacaoWithConfirmacao(
        model,
        codigoTransacao,
        numeroLogico,
        numeroCartao,
        dataVencimentoCartao,
        user.getIdUsuario());
  }

  public ResponseEntity<HashMap<String, Object>> doTransacaoWithConfirmacao(
      LancamentoContaEstabelecimentoVo model,
      Integer codigoTransacao,
      Long numeroLogico,
      String numeroCartao,
      String dataVencimentoCartao,
      Integer idUsuario)
      throws AcquirerClientException {

    TransacaoAcquirer request =
        prepareTransacaoFinanceira(
            model, codigoTransacao, numeroLogico, numeroCartao, dataVencimentoCartao);

    return acquirerClientService.doTransacaoWithConfirmacao(
        request, model.getIdFilialEstabelecimento(), idUsuario);
  }

  public TransacaoAcquirer prepareTransacaoFinanceira(
      LancamentoContaEstabelecimentoVo model,
      Integer codigoTransacao,
      Long numeroLogico,
      String numeroCartao,
      String dataVencimentoCartao) {

    TransacaoAcquirer request = new TransacaoAcquirer();
    BeanUtils.copyProperties(model, request);

    if (model.getEstorno()) {
      request.setTipoTransacao(TipoTransacao.ESTORNO);

      StringBuilder sb = new StringBuilder();
      sb.append(model.getData());
      sb.append(" ");
      sb.append(model.getHora());

      request.setNumeroAutorizacao(model.getNumeroAutorizacao());
      request.setNsuOriginal(model.getNsuOriginal());
      request.setDataHoraTransacaoOriginal(DateUtil.parseDate("dd/MM/yyy HH:mm:ss", sb.toString()));

    } else {
      request.setTipoTransacao(TipoTransacao.FINANCEIRA);
    }

    request.setFunctionCode(codigoTransacao.toString());
    request.setNumeroCartao(numeroCartao);
    request.setMid(numeroLogico.toString());
    request.setValorMoedaOrigem(model.getValorMoedaOrigem());
    request.setQuantidadeParcelas(model.getQuantidadeParcelas());
    request.setDataHoraTransacao(new Date());
    request.setNsu(lancamentoService.getStan().toString());
    request.setDataHoraGMT(Utils.getDataHoraUTC());
    request.setCodigoCredenciador("00000000006");
    request.setCodigoRedeCaptura("1001");
    request.setModoDeEntrada(Constantes.MODO_ENTRADA_PADRAO);
    request.setCodigoMoedaOrigem(Constantes.CODIGO_MOEDA_PADRAO);
    request.setTid("TIDWEB");
    request.setDataVencimentoCartao(dataVencimentoCartao);

    return request;
  }

  public Pessoa savePessoa(Pessoa model) {
    return pessoaService.save(model);
  }

  public GetSaldoConta calcularValorTransacaoMaximoDataValidade(SecurityUser user, Long idConta) {

    ContaPagamento conta = contaPagamentoService.findContaTitular(idConta);

    HierarquiaInstituicao instituicao =
        hierarquiaInstituicaoService.findByIdProcessadoraAndIdInstituicao(
            user.getIdProcessadora(), conta.getIdInstituicao());
    if (instituicao == null) {
      throw new GenericServiceException(
          "Não foi possível localizar a instituição para definir o valor de primeira compra");
    }

    ProdutoInstituicaoConfiguracaoCredito prodInstConfigCredito =
        prodInstConfigCreditoService.getConfiguracaoProdutoCredito(
            user.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());
    if (prodInstConfigCredito.getId() == null) {
      throw new GenericServiceException(
          "Não foi possível localizar o produto configuração crédito para definir a data de validade para o cartão virtual de primeira compra");
    }

    return contaPagamentoService.calcularValorTransacaoMaximo(
        user, idConta, instituicao, prodInstConfigCredito);
  }

  public byte[] exportarTermoAdesaoPDF(Long idConta) throws DocumentException, IOException {

    TermoAdesaoContaVO vo = contaPagamentoService.getInfoTermoAdesao(idConta);

    return TermoAdesaoContaPDF.getPDFTermoAdesao(vo);
  }

  public Boolean isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndTitularidade(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      Integer idProdutoInstituicao) {

    Integer exists =
        contaPessoaService.isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndTitularidade(
            idProcessadora, idInstituicao, documento, idProdutoInstituicao);

    return exists > 0;
  }

  public Boolean
      isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
          Integer idProcessadora,
          Integer idInstituicao,
          String documento,
          Integer idProdutoInstituicao,
          Integer idPontoRelacionamento) {

    Integer exists =
        contaPessoaService
            .isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
                idProcessadora,
                idInstituicao,
                documento,
                idProdutoInstituicao,
                idPontoRelacionamento);

    return exists > 0;
  }

  /**
   * Servico de detalhes da conta
   *
   * @param idConta
   * @return
   */
  public DetalhesConta getDetalhesConta(Long idConta, SecurityUser user) {

    DetalhesConta detalhes = new DetalhesConta();

    DadosPortador dadosPortador = contaPagamentoService.getDadosTitularConta(idConta, user);

    if (dadosPortador == null) {
      return null;
    }

    verificaPessoaPoliticamenteExposta(dadosPortador);

    detalhes.setDadosPortador(dadosPortador);

    if (!contaPagamentoService.permissaoAcessarPropriaConta(detalhes.getDadosPortador(), user)) {
      throw new GenericServiceException(
          "O usuário logado não tem permissão para acessar a própria conta!");
    }

    detalhes.setDadosConta(getDadosConta(idConta));
    detalhes.setEnderecosPessoa(contaPagamentoService.getEnderecosTitularConta(idConta));

    if (detalhes.getDadosPortador().getIdTipoPessoa() == 2
        && detalhes.getDadosConta() != null
        && detalhes.getDadosConta().getRepresentantes() != null
        && !detalhes.getDadosConta().getRepresentantes().isEmpty()) {
      verificaRepresentantesLegaisPoliticamenteExpostos(
          detalhes.getDadosConta(), detalhes.getDadosPortador().getIdInstituicao());
    }

    // Verificar se a conta foi migrada
    MigracaoMulticontasVallooVO migracaoInfo =
        migracaoMulticontasVallooRepository.buscarContaMigradaPorId(idConta);

    InfoMigracaoConta infoMigracao = new InfoMigracaoConta();
    if (migracaoInfo != null && migracaoInfo.getMigrado()) {
      infoMigracao.setContaMigrada(true);
      infoMigracao.setDataHoraMigracao(migracaoInfo.getDtHrMigrado());
      infoMigracao.setContaDestino(migracaoInfo.getIdContaDestino());
    } else {
      infoMigracao.setContaMigrada(false);
    }
    detalhes.setInfoMigracao(infoMigracao);

    // detalhes.setIdCadastroComplementar(cadastroComplementarProdutoService.findIdCadastroComplementarByIdProduto(detalhes.getDadosConta().getIdProdutoInstituicao()));

    return detalhes;
  }

  /**
   * Método responsavel por buscar Dados da conta
   *
   * @param idConta
   * @return DadosConta
   */
  public DadosConta getDadosConta(Long idConta) {
    ContaPagamento conta = contaPagamentoService.findByIdNotNull(idConta);
    DadosConta dados = new DadosConta();
    BeanUtils.copyProperties(conta, dados);
    dados.setIsB2b(conta.getHierarquiaPontoDeRelacionamento().getB2b());
    dados.setDescRelacionamento(conta.getArranjoRelacionamento().getDescRelacionamento());
    dados.setDescProdInstituidor(conta.getProdutoInstituidor().getDescProdInstituidor());
    dados.setIdProdPlatadorma(conta.getIdProdutoPlataforma());

    String dataAberturaFmt =
        DateUtil.dateFormat(
            FMT_DD_MM_YYYY, DateUtil.localDateTimeToDate(conta.getDataHoraInclusao()));

    dados.setDataAberturaFMT(dataAberturaFmt);
    dados.setDataAbertura(conta.getDataHoraInclusao());
    dados.setIdStatusConta(conta.getIdStatusV2());
    TipoStatus tipoStatus = conta.getTipoStatusV2();

    if (tipoStatus == null) {
      tipoStatus = findByIdTipoStatus(conta.getIdStatusV2());
    }

    dados.setDescStatusConta(tipoStatus.getDescStatus());
    dados.setIdProdutoInstituicao(conta.getIdProdutoInstituicao());
    ProdutoInstituicao produtoInstituicao = conta.getProdutoInstituicao();

    if (produtoInstituicao != null) {
      dados.setDescProdutoInstituicao(produtoInstituicao.getDescProdInstituicao());
      dados.setIdadeMinimaPortador(
          produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaPortador());
      dados.setIdadeMinimaAdicional(
          produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaAdicional());
      dados.setIdMoeda(
          produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getMoeda().getIdMoeda());
      dados.setSimboloMoeda(
          produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getMoeda().getSimbolo());
      dados.setDiferenciacaoLimiteAdicional(
          produtoInstituicao
              .getProdutoInstituicaoConfiguracao()
              .get(0)
              .getDiferenciacaoLimiteAdicional());
      dados.setPercentualLimiteAdicional(
          produtoInstituicao
              .getProdutoInstituicaoConfiguracao()
              .get(0)
              .getPercentualLimiteAdicional());

      if (produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getIdArranjo() != null) {
        ArranjoPagamento arranjoPagamento =
            arranjoPagamentoService.findByIdArranjo(
                produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0).getIdArranjo());

        dados.setIdInstituidor(arranjoPagamento.getIdInstituidor());
      }
    }

    HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento =
        conta.getHierarquiaPontoDeRelacionamento();

    GrupoEmpresarial grupoEmpresarial = null;
    if (hierarquiaPontoDeRelacionamento == null) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              conta.getIdProcessadora(),
              conta.getIdInstituicao(),
              conta.getIdRegional(),
              conta.getIdFilial(),
              conta.getIdPontoDeRelacionamento());
      hierarquiaPontoDeRelacionamento = hierarquiaPontoRelacioService.findById(id);

    } else {
      grupoEmpresarial =
          grupoEmpresarialRepository.findGrupoEmpresarialByIdGrupoEmpresarial(
              hierarquiaPontoDeRelacionamento.getIdGrupoEmpresarial());
    }

    HierarquiaInstituicao hierarquiaInstituicao =
        hierarquiaPontoDeRelacionamento.getHierarquiaInstituicao();

    if (hierarquiaInstituicao != null) {
      dados.setDescInstituicao(hierarquiaInstituicao.getDescInstituicao());
      dados.setDescRegional(
          hierarquiaPontoDeRelacionamento.getHierarquiaRegional().getDescRegional());
      dados.setDescFilial(hierarquiaPontoDeRelacionamento.getHierarquiaFilial().getDescFilial());
      dados.setDescPontoDeRelacionamento(hierarquiaPontoDeRelacionamento.getDescricao());
    }

    if (grupoEmpresarial != null) {
      dados.setIdGrupoEmpresarial(grupoEmpresarial.getIdGrupoEmpresarial());
      dados.setNoGrupoEmpresarial(grupoEmpresarial.getNoGrupoEmpresarial());
      dados.setNuCnpjGrupoEmpresarial(grupoEmpresarial.getNuCnpjGrupoEmpresarial());
    }

    List<ContaPessoa> contaPessoa = contaPessoaService.findByIdConta(conta.getIdConta());

    ProdutoContratado prodContrato =
        produtoContratadoService.findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            conta.getIdRegional(),
            conta.getIdFilial(),
            conta.getIdPontoDeRelacionamento(),
            conta.getIdProdutoInstituicao());

    if (!contaPessoa.isEmpty()
        && StringUtils.isNotBlank(contaPessoa.get(0).getNomeCartaoImpresso())) {
      dados.setNomeCartaoImpresso(contaPessoa.get(0).getNomeCartaoImpresso());
    } else if (prodContrato != null
        && StringUtils.isNotBlank(prodContrato.getNomeCartaoImpresso())) {
      dados.setNomeCartaoImpresso(prodContrato.getNomeCartaoImpresso());
    }
    Pessoa pessoa = pessoaService.findById(contaPessoa.get(0).getIdPessoa());
    if (pessoa != null) {
      // se for pj busco os representantes
      if (pessoa.getIdTipoPessoa().equals(2)) {
        List<RepresentanteLegal> representantes =
            representanteRepository.findByIdConta(conta.getIdConta());
        dados.setRepresentantes(representantes);
      }
    }

    dados.setMetodoSegurancaTransacao(
        contaPagamentoService.buscaMetodoSegurancaTransacaoConta(conta));

    return dados;
  }

  /**
   * Servico de dados créditos
   *
   * @param idConta
   * @return
   */
  public GetDadosCredito getDadosCredito(Long idConta, SecurityUser user) {

    consultaRestritaService.checaPrivilegio(idConta, user);

    GetDadosCredito dadosCredito = new GetDadosCredito();

    dadosCredito.setConta(getDadosConta(idConta));
    dadosCredito.setSaldoConta(contaPagamentoService.getSaldoConta(idConta));
    dadosCredito.setContaPagamentoFatura(contaPagamentoFaturaService.findConta(idConta));
    dadosCredito.setDadosUltimoFaturamento(contaCreditoService.SaldoUltimoFaturamento(idConta));

    FaturaMovimento valorProximaFatura = faturaMovimentoService.findValorProximaFatura(idConta);
    if (valorProximaFatura.getValorParcela() != null) {
      dadosCredito.setValorProximaFatura(valorProximaFatura.getValorParcela());
    }

    FaturaMovimento dividaConsolidada = faturaMovimentoService.findDividaConsolidada(idConta);
    if (dividaConsolidada.getValorParcela() != null) {
      dadosCredito.setValorDividaConsolidada(dividaConsolidada.getValorParcela());
    }

    preencherSaqueAVista(idConta, dadosCredito);

    return dadosCredito;
  }

  private void preencherSaqueAVista(Long idConta, GetDadosCredito dadosCredito) {
    ContaPagamento conta = contaPagamentoService.findByIdNotNull(idConta);

    if (conta.getIdProdutoPlataforma() != null && conta.getIdProdutoPlataforma() == 9) {

      ProdutoInstituicaoConfiguracaoCredito produtoInstituicaoCredito =
          prodInstConfigCreditoService.findOneByIdProdInstituicao(conta.getIdProdutoInstituicao());

      if (produtoInstituicaoCredito.getPercentualLimiteSaque() != null
          && dadosCredito.getSaldoConta().getLimiteCredito() != null) {
        BigDecimal limiteSaqueAVista =
            produtoInstituicaoCredito
                .getPercentualLimiteSaque()
                .multiply(dadosCredito.getSaldoConta().getLimiteCredito())
                .divide(new BigDecimal(100));

        if (dadosCredito.getSaldoConta().getSaldoDisponivel() == null
            || BigDecimal.ZERO.compareTo(dadosCredito.getSaldoConta().getSaldoDisponivel()) == 1) {
          dadosCredito.setSaqueAVista(BigDecimal.ZERO);
        } else {
          if (limiteSaqueAVista.compareTo(dadosCredito.getSaldoConta().getSaldoDisponivel()) == 1) {
            dadosCredito.setSaqueAVista(dadosCredito.getSaldoConta().getSaldoDisponivel());
          } else {
            dadosCredito.setSaqueAVista(limiteSaqueAVista);
          }
        }
        dadosCredito.setLimiteSaqueAVista(limiteSaqueAVista);
      }
    }
  }

  public JcardResponse salvarLancamento(
      CadastroLancamentoManual lancamento,
      Integer idUsuario,
      String ipOrigem,
      boolean origemPortador) {

    return lancamentoService.saveLancamento(lancamento, idUsuario, ipOrigem, origemPortador, false);
  }

  public Boolean agendarReplicacaoCredenciaisContas(
      AgendarReplicacaoContaB2BRequest input, SecurityUser user) {

    if (input == null || input.getContasEmpresas() == null || input.getContasEmpresas().isEmpty()) {
      throw new GenericServiceException("Não foi possível efetuar o agendamento da Replicação.");
    }

    for (ContasCredenciaisReplicaveisReponse empresa : input.getContasEmpresas()) {

      solicitaReplicacaoService.agendarReplicacaoCredenciais(empresa, user);
    }
    return true;
  }

  public List<SolicitacaoReplicacaoCredencialResumoResponse> getAgendamentosReplicacaoConta() {
    return solicitaReplicacaoService.findSolicitacoesReplicacaoCredencialResponse();
  }

  public JcardResponse alterarStatusConta(ContaPagamento contaPagamento, Integer statusDestino) {
    String journal =
        contaPagamento.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal();

    return accountService.updateAccountState(
        contaPagamento.getIdAccountCode(), journal, statusDestino);
  }

  public boolean validarTransferenciaMesmaInstituicao(
      ValidarTransferenciaMesmaInstituicaoDTO validarTransferenciaMesmaInstituicaoDTO,
      SecurityUserPortador userPortador) {
    travaServicosService.travaServicos(
        userPortador.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    Credencial credencialOrigem =
        credencialService.findById(
            validarTransferenciaMesmaInstituicaoDTO
                .getTransMesmaInstituicaoIdCredencial()
                .getIdCredencial());

    ContaPagamento contaOrigem = contaPagamentoService.findById(credencialOrigem.getIdConta());

    contaPagamentoService.validaIdContaPeloRequestEPortador(contaOrigem.getIdConta(), userPortador);

    return this.transMesmaInstituicaoIdCredencial(
        validarTransferenciaMesmaInstituicaoDTO.getTransMesmaInstituicaoIdCredencial(),
        validarTransferenciaMesmaInstituicaoDTO.getTokenJwt(),
        userPortador.getDocumentoAcesso());
  }

  public boolean validarTransferenciaMesmaInstituicao(
      ValidarTransferenciaMesmaInstituicaoDTO validarTransferenciaMesmaInstituicaoDTO,
      SecurityUserCorporativo userCorporativo) {
    travaServicosService.travaServicos(
        userCorporativo.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    Credencial credencialOrigem =
        credencialService.findById(
            validarTransferenciaMesmaInstituicaoDTO
                .getTransMesmaInstituicaoIdCredencial()
                .getIdCredencial());

    ContaPagamento contaOrigem = contaPagamentoService.findById(credencialOrigem.getIdConta());

    contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
        contaOrigem.getIdConta(), userCorporativo);

    return this.transMesmaInstituicaoIdCredencial(
        validarTransferenciaMesmaInstituicaoDTO.getTransMesmaInstituicaoIdCredencial(),
        validarTransferenciaMesmaInstituicaoDTO.getTokenJwt(),
        null);
  }

  private boolean transMesmaInstituicaoIdCredencial(
      TransMesmaInstituicaoIdCredencial model, String tokenJWT, String documentoRepresentante) {

    Credencial credencialOrigem = credencialService.findById(model.getIdCredencial());

    validaCredencialEncontrada(credencialOrigem);

    Credencial credencialDestino = credencialService.findById(model.getIdCredencialDestino());

    if (credencialDestino == null) {
      throw new GenericServiceException("Credencial Destino não encontrado ");
    }

    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
        credencialOrigem, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    ContaPagamento contaOrigem = contaPagamentoService.findById(credencialOrigem.getIdConta());
    travaContasService.travaContas(
        contaOrigem.getIdConta(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    ContaPagamento contaDestino = contaPagamentoService.findById(credencialDestino.getIdConta());
    travaContasService.travaContas(
        contaDestino.getIdConta(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    if (!Constantes.CONTA_ATIVA.equals(contaOrigem.getIdStatusConta())) {
      throw new GenericServiceException(
          "A Conta precisa estar Desbloqueada para realizar transferência Entre Contas.");
    }

    ProdutoInstituicaoConfiguracao produtoOrigem =
        lancamentoService.getProduto(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoDestino =
        lancamentoService.getProduto(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());

    if (produtoOrigem.getMoeda().getIdMoeda() != produtoDestino.getMoeda().getIdMoeda()) {
      throw new GenericServiceException("Produtos com moedas diferentes.");
    }

    CodigoTransacao codigoTransacao = codigoTransacaoService.findById(model.getCodigoTransacao());

    validaCodigoTransacao(codigoTransacao);

    List<TED> tedList = tedService.findListTed(contaOrigem);
    BigDecimal somaDiaria = tedService.calcularSomaDiaria(tedList);
    limitesContaService.validaLimitesConta(
        contaOrigem,
        model.getValorTransferencia(),
        somaDiaria,
        tedList.size(),
        LimiteTransacaoTipoEnum.TED);

    validaSaldoSuficiente(
        produtoOrigem, contaOrigem, codigoTransacao, model.getValorTransferencia());

    boolean sucesso =
        realizarTransferencia(
                model.getIdInstituicaoOrigem(),
                credencialOrigem.getIdCredencial(),
                model.getPinCredencialOrigem(),
                model.getValorTransferencia(),
                tokenJWT,
                true,
                FALSE,
                credencialDestino,
                codigoTransacao.getCodTransacao(),
                documentoRepresentante,
                null,
                null)
            .getSuccess();

    return sucesso;
  }

  public JcardResponse transferenciaMesmoBolso(
      TransferenciaMesmoBolso transferenciaMesmoBolso,
      String tokenJWT,
      SecurityUserPortador userPortador) {

    ContaPagamento contaOrigem =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            transferenciaMesmoBolso.getIdContaOrigem(), userPortador);

    ContaPagamento contaDestino =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            transferenciaMesmoBolso.getIdContaDestino(), userPortador);

    trataCodigoTransacaoEntreBolsos(transferenciaMesmoBolso);

    CodigoTransacao codigoTransacao =
        codigoTransacaoService.findOneByCodTransacao(transferenciaMesmoBolso.getCodigoTransacao());

    Credencial credencial = credencialService.findById(transferenciaMesmoBolso.getIdCredencial());

    validaCredencialEncontrada(credencial);

    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
        credencial, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    Boolean validado =
        metodoSegurancaTransacaoService.descobreSeInstituicaoValidaMetodoDeSegurancaPorServico(
            contaOrigem.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    validaStatusContas(contaOrigem, contaDestino);

    ProdutoInstituicaoConfiguracao produtoOrigem =
        lancamentoService.getProduto(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoDestino =
        lancamentoService.getProduto(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());

    validaProdutosMulticonta(produtoOrigem, produtoDestino);

    if (transferenciaMesmoBolso.getMoedeiro()) {
      validaProdutoMoedeiro(produtoOrigem, produtoDestino);
    }

    validacoesTransferenciaEntreContas(
        contaOrigem, contaDestino, codigoTransacao, produtoOrigem, produtoDestino);

    validaMesmaHierarquia(contaOrigem, contaDestino);

    validaSaldoSuficiente(
        produtoOrigem,
        contaOrigem,
        codigoTransacao,
        transferenciaMesmoBolso.getValorTransferencia());

    JcardResponse sucesso =
        realizarTransferencia(
            produtoOrigem.getIdInstituicao(),
            credencial.getIdCredencial(),
            transferenciaMesmoBolso.getPinCredencial(),
            transferenciaMesmoBolso.getValorTransferencia(),
            tokenJWT,
            !validado,
            validado,
            credencial,
            codigoTransacao.getCodTransacao(),
            userPortador.getDocumentoAcesso(),
            contaOrigem,
            contaDestino);

    if (!sucesso.getSuccess()) {
      String mensagemErro = ConstantesErro.TIN_ERRO_INESPERADO_TRANSFERENCIA.getMensagem();
      if (sucesso.getErrors().contains(TRANSFER_NOT_ALLOWED_BETWEEN_PRODUCTS)) {
        mensagemErro =
            ConstantesErro.TIN_MATRIZ_PRODUTOS_NAO_PERMITIDOS.format(
                produtoOrigem.getProdutoInstituicao().getDescProdInstituicao(),
                produtoDestino.getProdutoInstituicao().getDescProdInstituicao());
      } else if (sucesso.getErrors().contains(TRANSACTION_NOT_ENABLED)) {
        mensagemErro =
            ConstantesErro.TIN_PRODUTOS_NAO_PERMITIDOS.format(
                produtoOrigem.getProdutoInstituicao().getDescProdInstituicao(),
                produtoDestino.getProdutoInstituicao().getDescProdInstituicao());
      }
      sucesso.setErrors(mensagemErro);
    }

    return sucesso;
  }

  public JcardResponse validarCobrancaTransferenciaQrCode(
      ValidarCobrarTranferenciaQrCodeDTO validarCobrarTranferenciaQrCodeDTO,
      SecurityUserPortador userPortador) {
    travaServicosService.travaServicos(
        userPortador.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    ContaPagamento contaDestino =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            validarCobrarTranferenciaQrCodeDTO
                .getTransferenciaCobrarComQrCode()
                .getIdContaDestino(),
            userPortador);
    return this.cobraTransferenciaQrCode(
        validarCobrarTranferenciaQrCodeDTO.getTransferenciaCobrarComQrCode(),
        validarCobrarTranferenciaQrCodeDTO.getTokenJwt(),
        contaDestino,
        userPortador.getIdLogin(),
        userPortador.getDocumentoAcesso());
  }

  public JcardResponse validarCobrancaTransferenciaQrCode(
      ValidarCobrarTranferenciaQrCodeDTO validarCobrarTranferenciaQrCodeDTO,
      SecurityUserCorporativo userCorporativo) {
    travaServicosService.travaServicos(
        userCorporativo.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
    ContaPagamento contaDestino =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
            validarCobrarTranferenciaQrCodeDTO
                .getTransferenciaCobrarComQrCode()
                .getIdContaDestino(),
            userCorporativo);
    return this.cobraTransferenciaQrCode(
        validarCobrarTranferenciaQrCodeDTO.getTransferenciaCobrarComQrCode(),
        validarCobrarTranferenciaQrCodeDTO.getTokenJwt(),
        contaDestino,
        userCorporativo.getId(),
        null);
  }

  private JcardResponse cobraTransferenciaQrCode(
      TransferenciaCobrarComQrCode transferenciaCobrarComQrCode,
      String tokenJWT,
      ContaPagamento contaDestino,
      Long idLogin,
      String documentoRepresentante) {

    TransacaoCartaoQrCode transacaoCartaoQrCode =
        new TransacaoCartaoQrCode(
            LocalDateTime.now(),
            transferenciaCobrarComQrCode.getIdCredencial(),
            transferenciaCobrarComQrCode.getIdContaOrigem(),
            transferenciaCobrarComQrCode.getIdContaDestino(),
            transferenciaCobrarComQrCode.getCodigoTransacao(),
            transferenciaCobrarComQrCode.getValorTransferencia(),
            idLogin,
            StatusTransacaoCartaoQrCodeEnum.CHAMADA_API,
            transferenciaCobrarComQrCode.getPinCredencial() != null
                && !transferenciaCobrarComQrCode.getPinCredencial().isEmpty(),
            transferenciaCobrarComQrCode.getTokenSms() != null
                && !transferenciaCobrarComQrCode.getTokenSms().isEmpty());
    transacaoCartaoQrCodeService.saveAndFlush(transacaoCartaoQrCode);

    ContaPagamento contaOrigem =
        contaPagamentoService.findByIdNotNull(transferenciaCobrarComQrCode.getIdContaOrigem());

    CodigoTransacao codigoTransacao =
        codigoTransacaoService.findOneByCodTransacao(
            transferenciaCobrarComQrCode.getCodigoTransacao());

    Credencial credencialOrigem =
        credencialService.findById(transferenciaCobrarComQrCode.getIdCredencial());

    Credencial credencialDestino =
        credencialService.buscarCredencialParaLancamentoManual(contaDestino.getIdConta());

    validaCredencialEncontrada(credencialOrigem);

    ProdutoInstituicaoConfiguracao produtoOrigem =
        lancamentoService.getProduto(
            contaOrigem.getIdProcessadora(),
            contaOrigem.getIdInstituicao(),
            contaOrigem.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoDestino =
        lancamentoService.getProduto(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());

    validaProdutoLojista(produtoDestino);

    MetodosSegurancaTransacaoEnum metodoSegurancaConta =
        MetodosSegurancaTransacaoEnum.encontraMetodoPorId(
            contaPagamentoService.buscaMetodoSegurancaTransacaoConta(contaOrigem));

    validacaoSegurancaCobrancaLojista(
        metodoSegurancaConta, transferenciaCobrarComQrCode, credencialOrigem, tokenJWT);

    validaStatusContas(contaOrigem, contaDestino);

    validacoesTransferenciaEntreContas(
        contaOrigem, contaDestino, codigoTransacao, produtoOrigem, produtoDestino);

    validaSaldoSuficiente(
        produtoOrigem,
        contaOrigem,
        codigoTransacao,
        transferenciaCobrarComQrCode.getValorTransferencia());

    transacaoCartaoQrCode.setStatusTransacaoCartaoQrCodeEnum(
        StatusTransacaoCartaoQrCodeEnum.PRE_TRANSACAO);
    transacaoCartaoQrCodeService.saveAndFlush(transacaoCartaoQrCode);

    JcardResponse sucesso =
        realizarTransferencia(
            produtoOrigem.getIdInstituicao(),
            credencialOrigem.getIdCredencial(),
            transferenciaCobrarComQrCode.getPinCredencial(),
            transferenciaCobrarComQrCode.getValorTransferencia(),
            tokenJWT,
            FALSE,
            TRUE,
            credencialDestino,
            codigoTransacao.getCodTransacao(),
            documentoRepresentante,
            contaOrigem,
            contaDestino);

    if (!sucesso.getSuccess()) {
      transacaoCartaoQrCode.setMsgErro(sucesso.getErrors());
      transacaoCartaoQrCode.setStatusTransacaoCartaoQrCodeEnum(
          StatusTransacaoCartaoQrCodeEnum.FALHA_TRANSACAO);
      transacaoCartaoQrCodeService.saveAndFlush(transacaoCartaoQrCode);
      String mensagemErro = ConstantesErro.TIN_ERRO_INESPERADO_TRANSFERENCIA.getMensagem();
      if (sucesso.getErrors().contains(TRANSFER_NOT_ALLOWED_BETWEEN_PRODUCTS)) {
        mensagemErro =
            ConstantesErro.TIN_MATRIZ_PRODUTOS_NAO_PERMITIDOS.format(
                produtoOrigem.getProdutoInstituicao().getDescProdInstituicao().trim(),
                produtoDestino.getProdutoInstituicao().getDescProdInstituicao().trim());
      }
      sucesso.setErrors(mensagemErro);
    } else {
      transacaoCartaoQrCode.setRrn(sucesso.getRrn());
      transacaoCartaoQrCode.setStatusTransacaoCartaoQrCodeEnum(
          StatusTransacaoCartaoQrCodeEnum.SUCESSO_TRANSACAO);
      transacaoCartaoQrCodeService.saveAndFlush(transacaoCartaoQrCode);
    }

    return sucesso;
  }

  private void validacaoSegurancaCobrancaLojista(
      MetodosSegurancaTransacaoEnum metodoSegurancaConta,
      TransferenciaCobrarComQrCode transferenciaCobrarComQrCode,
      Credencial credencial,
      String tokenJWT) {
    switch (metodoSegurancaConta) {
      case SEM_SEGURANCA:
        metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
            credencial, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
      case SENHA_CARTAO:
        try {
          credencialService.validarPin(
              transferenciaCobrarComQrCode.getPinCredencial(), credencial, tokenJWT);
          metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
              credencial, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
        } catch (GenericServiceException e) {
          if (ConstantesErro.SEG_VALIDACAO_NAO_CONCLUIDA_GENERICO
              .getMensagem()
              .equals(e.getMessage())) {
            throw new GenericServiceException(
                ConstantesErro.TIN_SENHA_INCORRETA.getMensagem(), HttpStatus.BAD_REQUEST);
          }
          throw e;
        }
        break;
      case TOKEN_SMS:
        try {
          metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
              credencial, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
        } catch (GenericServiceException e) {
          if (ConstantesErro.SEG_VALIDACAO_NAO_CONCLUIDA_GENERICO
              .getMensagem()
              .equals(e.getMessage())) {
            throw new GenericServiceException(
                ConstantesErro.TIN_TOKEN_INCORRETO.getMensagem(), HttpStatus.BAD_REQUEST);
          }
          throw e;
        }
        break;
      default:
        throw new GenericServiceException(
            ConstantesErro.TIN_METODO_SEGURANCA_NAO_CONFIGURADO.getMensagem());
    }
  }

  private void trataCodigoTransacaoEntreBolsos(TransferenciaMesmoBolso transferenciaMesmoBolso) {
    if (transferenciaMesmoBolso.getCodigoTransacao() == null) {
      if (transferenciaMesmoBolso.getMoedeiro()) {
        transferenciaMesmoBolso.setCodigoTransacao(Constantes.COD_TRANSACAO_RESGATE_SALDO_MOEDEIRO);
      } else {
        transferenciaMesmoBolso.setCodigoTransacao(
            Constantes.COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_DEBITO);
      }
    }
  }

  private void validaCredencialEncontrada(Credencial credencial) {
    if (credencial == null) {
      throw new GenericServiceException(ConstantesErro.TIN_CARTAO_NAO_ENCONTRADO.getMensagem());
    }
    if (!GRUPO_STATUS_ATIVO.equals(credencial.getTipoStatus().getIdGrupoStatus())) {
      throw new GenericServiceException(
          ConstantesErro.TIN_CREDENCIAL_NAO_DESBLOQUEADA.getMensagem());
    }
  }

  private void validaStatusContas(ContaPagamento contaOrigem, ContaPagamento contaDestino) {
    if (!Constantes.CONTA_ATIVA.equals(contaOrigem.getIdStatusConta())
        || !Constantes.CONTA_ATIVA.equals(contaDestino.getIdStatusConta())) {
      throw new GenericServiceException(ConstantesErro.TIN_STATUS_CONTA_NAO_ATIVA.getMensagem());
    }
  }

  private void validaProdutosMulticonta(
      ProdutoInstituicaoConfiguracao produtoOrigem, ProdutoInstituicaoConfiguracao produtoDestino) {
    if (produtoOrigem.getIdGrupoProduto() == null || produtoDestino.getIdGrupoProduto() == null) {
      throw new GenericServiceException(ConstantesErro.TIN_PRODUTOS_MULTI_CONTA.getMensagem());
    }
    if (!produtoOrigem.getIdGrupoProduto().equals(produtoDestino.getIdGrupoProduto())) {
      throw new GenericServiceException(ConstantesErro.TIN_GRUPO_PRODUTOS_DISTINTOS.getMensagem());
    }
  }

  private void validaProdutoMoedeiro(
      ProdutoInstituicaoConfiguracao produtoOrigem, ProdutoInstituicaoConfiguracao produtoDestino) {
    if (!TipoProdutoEnum.MOEDEIRO.equals(produtoOrigem.getTipoProduto())) {
      throw new GenericServiceException(ConstantesErro.TIN_RESGATE_MOEDEIRO_ORIGEM.getMensagem());
    }
    if (!TipoProdutoEnum.CONTA_LIVRE.equals(produtoDestino.getTipoProduto())) {
      throw new GenericServiceException(ConstantesErro.TIN_RESGATE_MOEDEIRO_DESTINO.getMensagem());
    }
  }

  private void validaProdutoLojista(ProdutoInstituicaoConfiguracao produtoDestino) {
    if (!TipoProdutoEnum.LOJISTA.equals(produtoDestino.getTipoProduto())) {
      throw new GenericServiceException(ConstantesErro.TIN_RESGATE_MOEDEIRO_DESTINO.getMensagem());
    }
  }

  private void validacoesTransferenciaEntreContas(
      ContaPagamento contaOrigem,
      ContaPagamento contaDestino,
      CodigoTransacao codigoTransacao,
      ProdutoInstituicaoConfiguracao produtoOrigem,
      ProdutoInstituicaoConfiguracao produtoDestino) {
    validaMesmaInstituicao(produtoOrigem, produtoDestino);

    validaMesmaMoeda(produtoOrigem, produtoDestino);

    validaCodigoTransacao(codigoTransacao);
  }

  private void validaMesmaInstituicao(
      ProdutoInstituicaoConfiguracao produtoOrigem, ProdutoInstituicaoConfiguracao produtoDestino) {
    if (!produtoOrigem.getIdInstituicao().equals(produtoDestino.getIdInstituicao())) {
      throw new GenericServiceException(ConstantesErro.TIN_MESMA_INSTITUICAO.getMensagem());
    }
  }

  private void validaMesmaHierarquia(ContaPagamento contaOrigem, ContaPagamento contaDestino) {
    if (UtilController.checkHierarquiaObjetosIguaisEmParidadeOuSuperior(
        contaOrigem, contaDestino)) {
      throw new GenericServiceException(ConstantesErro.TIN_MESMA_HIERARQUIA.getMensagem());
    }
  }

  private void validaMesmaMoeda(
      ProdutoInstituicaoConfiguracao produtoOrigem, ProdutoInstituicaoConfiguracao produtoDestino) {
    if (!Objects.equals(
        produtoOrigem.getMoeda().getIdMoeda(), produtoDestino.getMoeda().getIdMoeda())) {
      throw new GenericServiceException(ConstantesErro.TIN_MESMA_MOEDA.getMensagem());
    }
  }

  private void validaCodigoTransacao(CodigoTransacao codigoTransacao) {
    if (codigoTransacao == null) {
      throw new GenericServiceException(
          ConstantesErro.TIN_CODIGO_TRANSACAO_NAO_RECONHECIDO.getMensagem());
    }
  }

  private void validaSaldoSuficiente(
      ProdutoInstituicaoConfiguracao produtoOrigem,
      ContaPagamento contaOrigem,
      CodigoTransacao codigoTransacao,
      BigDecimal transferenciaMesmoBolso) {
    if (Constantes.CODIGO_MOEDA_DE_PONTO.equals(produtoOrigem.getMoeda().getIdMoeda())) {

      List<PontoControle> listSaldoPontosControle =
          utilManejoPontos.buscaPontosControleComSaldo(contaOrigem.getIdConta());

      BigDecimal tarifa =
          resgateContaBancariaService.buscarTarifa(contaOrigem, codigoTransacao.getCodTransacao());

      Map<String, Integer> mapSaldoResgatavel =
          utilManejoPontos.calculaSaldoResgatavel(
              listSaldoPontosControle,
              transferenciaMesmoBolso.longValueExact(),
              tarifa.longValue()); // retorna map com key nomeada 'suficiente' ou 'insuficiente'

      if (mapSaldoResgatavel.containsKey("insuficiente")) {
        throw new GenericServiceException(ConstantesErro.TIN_SALDO_INSUFICIENTE.getMensagem());
      }

    } else {
      GetSaldoConta saldoContaOrigem =
          contaPagamentoService.getSaldoConta(contaOrigem.getIdConta());
      BigDecimal saldoDisponivel = saldoContaOrigem.getSaldoDisponivel();
      BigDecimal limiteCredito = saldoContaOrigem.getLimiteCredito();
      BigDecimal somaSaldoLimite = saldoDisponivel.add(limiteCredito);
      if (somaSaldoLimite.compareTo(transferenciaMesmoBolso) < 0) {
        throw new GenericServiceException(ConstantesErro.TIN_SALDO_INSUFICIENTE.getMensagem());
      }
    }
  }

  @Transactional
  public ContaPagamento createContaPagamentoDigital(
      CadastrarContaDigital cadastrarContaPagPessoa,
      Pessoa pessoa,
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao) {
    ContaPagamento conta = new ContaPagamento();
    ContaPagamentoRequest createConta = new ContaPagamentoRequest();

    try {
      contaPagamentoService.prepareCadastrarContaDigital(
          cadastrarContaPagPessoa, pessoa, conta, createConta, valorCargaProdutoInstituicao);
    } catch (IllegalAccessException | InvocationTargetException e) {
      throw new GenericServiceException(
          "Ocorreu um erro de cópia de dados no cadastro de pessoa jurídica ", e);
    }

    geradorContaService.createContaPagamento(createConta, conta);
    return conta;
  }

  public GetDadosConsumo buscarDadosConsumo(Long idConta, SecurityUserPortador userPortador) {

    contaPagamentoService.validaIdContaPeloRequestEPortador(idConta, userPortador);

    GetDadosConsumo dadosConta = new GetDadosConsumo();
    dadosConta.setIdConta(idConta);

    GetDadosConsumo dadosProximaCarga =
        contaPagamentoService.buscarDadosProximaCarga(idConta, dadosConta);

    GetDadosConsumo dadosComConsumoFuturoEstimado =
        contaPagamentoService.buscarDadosConsumoEstimado(idConta, dadosProximaCarga);

    return contaPagamentoService.buscarDadosConsumoReal(idConta, dadosComConsumoFuturoEstimado);
  }

  public Boolean verificaPermiteCadastrarConta(CriarContaPagamento model, Pessoa pessoa) {
    Boolean permiteCadastrar = TRUE;
    ValorCargaProdutoInstituicao produtoContaLivre = null;
    boolean encontrouContaLivreSolicitada = false;
    boolean existeProdutoComGrupo = false;
    boolean pessoaJaPossuiSaldoLivre = false;

    // Verifica se a pessoa já possui uma conta do tipo Saldo Livre cadastrada
    for (ValorCargaProdutoInstituicao valorProduto : model.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoConfig =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              pessoa.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              pessoa.getIdInstituicao());

      if (produtoConfig.getIdGrupoProduto() != null) {
        existeProdutoComGrupo = true;

        ContaPagamento conta =
            contaPagamentoService.findContasPagamentosComGrupoAcessoETipoProduto(
                pessoa.getIdProcessadora(),
                pessoa.getIdInstituicao(),
                model.getIdRegional(),
                model.getIdFilial(),
                model.getIdPontoDeRelacionamento(),
                pessoa.getDocumento(),
                pessoa.getIdTipoPessoa(),
                produtoConfig.getIdGrupoProduto(),
                TipoProdutoEnum.CONTA_LIVRE);
        pessoaJaPossuiSaldoLivre = (conta != null);
        break;
      }
    }

    // Percorre novamente para encontrar a solicitação de Saldo Livre, se houver
    for (ValorCargaProdutoInstituicao valorProduto : model.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoConfig =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              pessoa.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              pessoa.getIdInstituicao());

      // Verifica se existe uma solicitação de conta do tipo Saldo Livre
      if (produtoConfig.getIdGrupoProduto() != null
          && TipoProdutoEnum.CONTA_LIVRE.equals(produtoConfig.getTipoProduto())) {
        produtoContaLivre =
            valorProduto; // Armazena o produto CONTA_LIVRE para cadastro prioritário
        encontrouContaLivreSolicitada = true;
        break; // Podemos parar a busca após encontrar a conta Saldo Livre
      }
    }

    // Agora percorre novamente para validar o cadastro das contas com grupo
    for (ValorCargaProdutoInstituicao valorProduto : model.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoConfig =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              pessoa.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              pessoa.getIdInstituicao());

      if (produtoConfig.getIdGrupoProduto() != null) {
        // Para produtos com grupo, verifica a necessidade de Saldo Livre
        if (!pessoaJaPossuiSaldoLivre && !encontrouContaLivreSolicitada) {
          // Se a pessoa não tem Saldo Livre e não está sendo solicitado, bloqueia o cadastro
          permiteCadastrar = FALSE;
          break;
        }
      }
    }

    // Se o produto Saldo Livre foi solicitado, coloca ele no início da lista
    if (produtoContaLivre != null) {
      model.getValoresCargasProdutos().remove(produtoContaLivre);
      model.getValoresCargasProdutos().add(0, produtoContaLivre);
      permiteCadastrar = TRUE; // Permite o cadastro, pois a conta Saldo Livre será criada primeiro
    }

    // Se não houver produtos com grupo, o cadastro é permitido
    if (!existeProdutoComGrupo) {
      permiteCadastrar = TRUE;
    }

    return permiteCadastrar;
  }

  public void verificaPessoaPoliticamenteExposta(DadosPortador dadosPortador) {
    dadosPortador.setPepCaf(
        antifraudeCafPortadorRepository.getPessoaPoliticamenteExposta(
            dadosPortador.getDocumento(), dadosPortador.getIdInstituicao()));
  }

  public void verificaRepresentantesLegaisPoliticamenteExpostos(
      DadosConta dadosConta, Integer idInstituicao) {

    List<PessoaPoliticamenteExpostaVO> pep =
        antifraudeCafPortadorRepository.getPessoaPoliticamenteExpostaRepresentanteLegal(
            dadosConta.getIdConta(), idInstituicao);

    if (pep.isEmpty()) {
      for (RepresentanteLegal representante : dadosConta.getRepresentantes()) {
        representante.setBlPepCaf(false);
      }
    } else {
      for (RepresentanteLegal representante : dadosConta.getRepresentantes()) {
        try {
          representante.setBlPepCaf(
              pep.stream()
                  .filter(p -> Objects.equals(p.getDocumento(), representante.getCpf()))
                  .collect(Collectors.toList())
                  .get(0)
                  .getBlPoliticamenteExposta());
        } catch (IndexOutOfBoundsException e) {
          representante.setBlPepCaf(false);
        }
      }
    }
  }
}
