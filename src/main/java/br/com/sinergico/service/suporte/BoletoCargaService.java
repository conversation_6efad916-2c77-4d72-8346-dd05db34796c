package br.com.sinergico.service.suporte;

import br.com.caelum.stella.boleto.Banco;
import br.com.caelum.stella.boleto.Beneficiario;
import br.com.caelum.stella.boleto.Boleto;
import br.com.caelum.stella.boleto.Datas;
import br.com.caelum.stella.boleto.Endereco;
import br.com.caelum.stella.boleto.Pagador;
import br.com.caelum.stella.boleto.bancos.GeradorDeLinhaDigitavel;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.suporte.GatewaySMS;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.MotivoCobranca;
import br.com.entity.suporte.ParametroDefinicao;
import br.com.entity.suporte.ParametroProcessamentoSistema;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.transacional.CobrancaBancaria;
import br.com.entity.transacional.CodigoTransacao;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.BoletoCarga;
import br.com.json.bean.suporte.ComunicadoContaViaSMS;
import br.com.json.bean.suporte.GerarBoletoCarga;
import br.com.json.bean.transacional.GetCobrancasBancarias;
import br.com.sinergico.boleto.BRB;
import br.com.sinergico.boleto.BancosBoletoFactory;
import br.com.sinergico.boleto.BoletoItsPay;
import br.com.sinergico.boleto.GeradorDeBoletoItspay;
import br.com.sinergico.repository.cadastral.ContaPagamentoRepository;
import br.com.sinergico.repository.cadastral.EnderecoPessoaRepository;
import br.com.sinergico.repository.cadastral.PessoaRepository;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoConfiguracaoRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.boletoregistrado.RegistroBoletoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PerfilTarifarioService;
import br.com.sinergico.service.cadastral.PerfilTarifarioTransacaoService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.CobrancaBancariaService;
import br.com.sinergico.service.transacional.CodigoTransacaoService;
import br.com.sinergico.util.BoletoUtil;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import com.google.common.base.Strings;
import java.io.File;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import javax.persistence.NoResultException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BoletoCargaService {

  private static final int TARIFA = 2;

  private static final String FMT_D_MMM = "d MMM";

  // tipo_evento_conta
  private static final Integer TIPO_EVENTO_ENVIO_LINHA_DIGITAVEL_SMS = 6;

  private static final int DIAS_VENCIMENTO = 5;
  private static final int POSICAO_ZERO = 0;
  private static final String BOLETO_DIR = "boletodir";
  private static final String IMG_CORRIDA_CAMINHO_COMPLETO = "img.corrida";
  private static final String IMG_CORRIDA_QUINTA_MEIA_MARATONA = "img.quinta.mmd";
  private static final String IMG_CORRIDA_MACONARIA = "corrida.maconaria";
  private static final String IMG_CORRIDA_YGLOO = "corrida.ygloo";
  private static final int UMA_POSICAO = 1;
  private static final char CHAR_ZERO = '0';
  private static final int TAM_COD_BANCO = 3;
  private static final int MES_DELAY = 1;

  private static final Integer PESSOA_FISICA = 1;
  private static final Integer ATIVO = 1;
  private static final Integer RESIDENCIAL = 1;
  private static final Integer FISCAL = 3;
  private static final Integer TITULAR = 1;

  @Autowired private ProdutoInstituicaoConfiguracaoRepository prodInstConfigRepository;

  @Autowired private HierarquiaInstituicaoRepository hie;

  @Autowired private PessoaRepository pessoaRepository;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private ContaPagamentoRepository contaRepository;

  @Autowired private CredencialService credencialService;

  @Autowired private CobrancaBancariaService cobrancaBancariaService;

  @Autowired private EnderecoPessoaRepository enderecoRepository;

  @Autowired private ParametroValorService paramValorService;

  @Autowired private MotivoCobrancaProdutoService motivoCobrancaProdutoService;

  @Autowired private EmailService emailService;

  @Autowired private ComunicadorPortadorSMSService agenteComunicadorService;

  @Autowired private RegistroBoletoService registroBoletoService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private GatewaySMSService gatewaySMSService;

  @Autowired private CodigoTransacaoService codigoTransacaoService;

  @Autowired private PerfilTarifarioTransacaoService pfTransacaoService;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private ParametroProcessamentoSistemaService parametroProcessamentoSistemaService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private UtilService utilService;

  @Value("${issuer.dir.emissor.logos}")
  private String issuerDirEmissorLogos;

  private static final Logger log = LoggerFactory.getLogger(BoletoCargaService.class);
  private static final Integer PORTADOR = 1;

  /**
   * Método responsável por gerar um boleto e salvar temporariamente no diretorio configurado
   *
   * @param gerarBoletoCarga
   * @return {@link br.com.caelum.stella.boleto.Boleto Boleto}
   */
  public Boleto gerarBoleto(GerarBoletoCarga gerarBoletoCarga) {
    Calendar documento = Calendar.getInstance();
    Calendar vencimento = Calendar.getInstance();
    // vencimento.setTime(gerarBoletoCarga.getVencimento())
    Date dateVencimento = DateUtil.addDayDate(new Date(), DIAS_VENCIMENTO);
    vencimento.setTime(dateVencimento);

    return getBoleto(gerarBoletoCarga, documento, vencimento);
  }

  public Boleto gerarBoletoEEnviarPorEmail(GerarBoletoCarga gerarBoletoCarga) {

    Calendar dataDocumento = Calendar.getInstance();
    Calendar dataVencimento = Calendar.getInstance();

    Boleto boleto = getBoleto(gerarBoletoCarga, dataDocumento, dataVencimento);

    Map<String, Object> parames =
        getParametrosAdicionaisBoleto(
            gerarBoletoCarga.getIdProcessadora(), gerarBoletoCarga.getIdInstituicao());

    GeradorDeBoletoItspay gerador = new GeradorDeBoletoItspay(parames, boleto);

    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(BOLETO_DIR);
    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(gerarBoletoCarga.getIdProcessadora());
    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    if (params == null || params.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: " + BOLETO_DIR);
    }

    String nomeArq =
        getNomeArquivoBoleto(
            dataDocumento.get(Calendar.HOUR_OF_DAY),
            dataDocumento.get(Calendar.MINUTE),
            dataDocumento.get(Calendar.SECOND),
            dataVencimento.get(Calendar.DAY_OF_MONTH),
            dataVencimento.get(Calendar.MONTH),
            dataVencimento.get(Calendar.YEAR),
            boleto.getNossoNumeroECodDocumento());

    String diretorio = params.get(POSICAO_ZERO).getValorParametro();
    String arquivo = diretorio + nomeArq;

    // salva num arquivo
    gerador.geraPDF(arquivo);

    HierarquiaInstituicao instituicao =
        hie.findByIdProcessadoraAndIdInstituicao(
            gerarBoletoCarga.getIdProcessadora(), gerarBoletoCarga.getIdInstituicao());

    String emailDeResposta;
    if (instituicao.getEmailReply() != null) {
      emailDeResposta = instituicao.getEmailReply();
    } else {
      emailDeResposta = "<EMAIL>";
    }

    emailService.sendBoletoEmail(
        gerarBoletoCarga.getEmailDestino(),
        emailDeResposta,
        nomeArq,
        diretorio,
        boleto.getPagador().getNome());

    return boleto;
  }

  public Boleto gerarBoletoCobrancaBancariaEEnviarPorEmail(Long idCobranca, String emailDestino) {

    CobrancaBancaria cobrancaBancaria = cobrancaBancariaService.findById(idCobranca);

    if (cobrancaBancaria == null) {
      throw new GenericServiceException(
          "Cobrança não encontrada. Verifique se a mesma existe", "idCobranca: " + idCobranca);
    }
    travaContasService.travaContas(cobrancaBancaria.getIdConta(), Servicos.BOLETO);

    BoletoItsPay boleto = (BoletoItsPay) getBoletoCobrancaBancaria(cobrancaBancaria);

    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(BOLETO_DIR);
    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(cobrancaBancaria.getIdProcessadora());
    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    if (params == null || params.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: " + BOLETO_DIR);
    }

    String nomeArq =
        getNomeArquivoBoleto(
            boleto.getDatas().getDocumento().get(Calendar.HOUR_OF_DAY),
            boleto.getDatas().getDocumento().get(Calendar.MINUTE),
            boleto.getDatas().getDocumento().get(Calendar.SECOND),
            boleto.getDatas().getVencimento().get(Calendar.DAY_OF_MONTH),
            boleto.getDatas().getVencimento().get(Calendar.MONTH),
            boleto.getDatas().getVencimento().get(Calendar.YEAR),
            boleto.getNossoNumeroECodDocumento());

    String diretorio = params.get(POSICAO_ZERO).getValorParametro();
    String arquivo = diretorio + nomeArq;

    if (cobrancaBancaria.getRegistrado() != null
        && cobrancaBancaria.getRegistrado()
        && cobrancaBancaria.getBancoEmissor().equals(Integer.valueOf(BRB.getNumeroDoBanco()))
        && cobrancaBancaria.getCodigoRegistroBanco() != null) {
      InputStream pdfStream =
          registroBoletoService.getPDFStream(
              BRB.getNumeroDoBanco(),
              cobrancaBancaria.getIdProcessadora(),
              cobrancaBancaria.getIdInstituicao(),
              cobrancaBancaria.getCodigoRegistroBanco());
      Util.inputStreamToFile(pdfStream, arquivo);
    } else {

      Map<String, Object> parames =
          getParametrosAdicionaisBoleto(
              cobrancaBancaria.getIdProcessadora(), cobrancaBancaria.getIdInstituicao());

      // PARAMETRO ADICIONADO APENAS PARA O BOLETO BANESE
      parames.put("linha_digitavel_formatada", cobrancaBancaria.getLinhaDigitavel());
      GeradorDeBoletoItspay gerador = new GeradorDeBoletoItspay(parames, boleto);
      gerador.geraPDF(arquivo);
    }

    HierarquiaInstituicao instituicao =
        hie.findByIdProcessadoraAndIdInstituicao(
            cobrancaBancaria.getIdProcessadora(), cobrancaBancaria.getIdInstituicao());

    String emailDeResposta;
    if (instituicao.getEmailReply() != null) {
      emailDeResposta = instituicao.getEmailReply();
    } else {
      emailDeResposta = "<EMAIL>";
    }

    if (Constantes.ID_PROD_INST_MEIA_MARATONA_1.equals(cobrancaBancaria.getIdProdutoInstituicao())
        || Constantes.ID_PROD_INST_MEIA_MARATONA_2.equals(
            cobrancaBancaria.getIdProdutoInstituicao())) {
      if (cobrancaBancaria.getIdMotivo() != null
          && Constantes.MOTIVO_COBRANCA_20.equals(cobrancaBancaria.getIdMotivo())) {
        ParametroDefinicao definicaoImg = new ParametroDefinicao();
        definicaoImg.setDescChaveParametro(IMG_CORRIDA_MACONARIA);
        ParametroValor valorImg = new ParametroValor();
        valorImg.setIdProcessadora(cobrancaBancaria.getIdProcessadora());
        List<ParametroValor> paramsImg = paramValorService.findParametros(definicaoImg, valorImg);
        validaParams(paramsImg, IMG_CORRIDA_MACONARIA);
        String caminhoCompletoImg = paramsImg.get(POSICAO_ZERO).getValorParametro();
        emailService.sendBoletoEmailCobrancaBancariaMeiaMaratonaMaconaria(
            emailDestino,
            emailDeResposta,
            nomeArq,
            diretorio,
            boleto.getPagador().getNome(),
            caminhoCompletoImg);

      } else if (cobrancaBancaria.getIdMotivo() != null
          && Constantes.MOTIVO_COBRANCA_30.equals(cobrancaBancaria.getIdMotivo())) { // novo motivo
        ParametroDefinicao definicaoImg = new ParametroDefinicao();
        definicaoImg.setDescChaveParametro(IMG_CORRIDA_YGLOO);
        ParametroValor valorImg = new ParametroValor();
        valorImg.setIdProcessadora(cobrancaBancaria.getIdProcessadora());
        valorImg.setIdInstituicao(cobrancaBancaria.getIdInstituicao());
        List<ParametroValor> paramsImg = paramValorService.findParametros(definicaoImg, valorImg);
        validaParams(paramsImg, IMG_CORRIDA_YGLOO);
        String caminhoCompletoImg = paramsImg.get(POSICAO_ZERO).getValorParametro();
        emailService.sendBoletoEmailCobrancaBancariaCorridaYgloo(
            emailDestino,
            emailDeResposta,
            nomeArq,
            diretorio,
            boleto.getPagador().getNome(),
            caminhoCompletoImg);

      } else if (cobrancaBancaria.getIdMotivo() != null
          && (Constantes.MOTIVO_COBRANCA_32.equals(cobrancaBancaria.getIdMotivo())
              || Constantes.MOTIVO_COBRANCA_33.equals(cobrancaBancaria.getIdMotivo()))) {
        ParametroDefinicao definicaoImg = new ParametroDefinicao();
        definicaoImg.setDescChaveParametro(IMG_CORRIDA_QUINTA_MEIA_MARATONA);
        ParametroValor valorImg = new ParametroValor();
        valorImg.setIdProcessadora(cobrancaBancaria.getIdProcessadora());
        List<ParametroValor> paramsImg = paramValorService.findParametros(definicaoImg, valorImg);
        validaParams(paramsImg, IMG_CORRIDA_QUINTA_MEIA_MARATONA);
        String caminhoCompletoImg = paramsImg.get(POSICAO_ZERO).getValorParametro();
        emailService.sendBoletoEmailCobrancaBancariaQuintaMeiaMaratona(
            emailDestino,
            emailDeResposta,
            nomeArq,
            diretorio,
            boleto.getPagador().getNome(),
            caminhoCompletoImg,
            cobrancaBancaria.getIdMotivo());
      } else {
        ParametroDefinicao definicaoImg = new ParametroDefinicao();
        definicaoImg.setDescChaveParametro(IMG_CORRIDA_CAMINHO_COMPLETO);
        ParametroValor valorImg = new ParametroValor();
        valorImg.setIdProcessadora(cobrancaBancaria.getIdProcessadora());
        List<ParametroValor> paramsImg = paramValorService.findParametros(definicaoImg, valorImg);
        validaParams(paramsImg, IMG_CORRIDA_CAMINHO_COMPLETO);
        String caminhoCompletoImg = paramsImg.get(POSICAO_ZERO).getValorParametro();
        emailService.sendBoletoEmailCobrancaBancariaMeiaMaratona(
            emailDestino,
            emailDeResposta,
            nomeArq,
            diretorio,
            boleto.getPagador().getNome(),
            caminhoCompletoImg);
      }

    } else {
      if (utilService
          .getProdutoInstituicaoBrbPay()
          .equals(cobrancaBancaria.getIdProdutoInstituicao())) {
        emailService.sendBoletoEmailCobrancaBancariaBRBPay(
            emailDestino, boleto.getPagador().getNome(), nomeArq, diretorio);
      } else {
        String logo =
            hie.findUrlLogoByIdProcessadoraAndIdInstituicao(
                cobrancaBancaria.getIdProcessadora(), cobrancaBancaria.getIdInstituicao());
        String caminho = issuerDirEmissorLogos;
        String path = caminho + logo;
        emailService.sendBoletoEmailCobrancaBancaria(
            emailDestino,
            emailDeResposta,
            nomeArq,
            diretorio,
            boleto.getPagador().getNome(),
            path,
            cobrancaBancaria.getIdInstituicao());
      }
    }

    return boleto;
  }

  public void validaParams(List<ParametroValor> paramsImg, String imgCorrida) {
    if (Objects.isNull(paramsImg) || paramsImg.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: " + imgCorrida);
    }
  }

  public InputStream downloadBoletoCobrancaBancaria(Long idCobranca) {

    CobrancaBancaria cobrancaBancaria = cobrancaBancariaService.findById(idCobranca);

    if (cobrancaBancaria == null) {
      throw new GenericServiceException(
          "Cobrança não encontrada. Verifique se a mesma existe", "idCobranca: " + idCobranca);
    }
    if (cobrancaBancaria.getRegistrado() != null
        && cobrancaBancaria.getRegistrado()
        && cobrancaBancaria.getBancoEmissor().equals(new Integer(BRB.getNumeroDoBanco()))
        && cobrancaBancaria.getCodigoRegistroBanco() != null) {
      return registroBoletoService.getPDFStream(
          BRB.getNumeroDoBanco(),
          cobrancaBancaria.getIdProcessadora(),
          cobrancaBancaria.getIdInstituicao(),
          cobrancaBancaria.getCodigoRegistroBanco());
    }

    Boleto boleto = getBoletoCobrancaBancaria(cobrancaBancaria);
    Map<String, Object> parames =
        getParametrosAdicionaisBoleto(
            cobrancaBancaria.getIdProcessadora(), cobrancaBancaria.getIdInstituicao());

    GeradorDeBoletoItspay gerador = new GeradorDeBoletoItspay(parames, boleto);
    return gerador.geraPDFStream();
  }

  private Map<String, Object> getParametrosAdicionaisBoleto(
      Integer idProcessadora, Integer idInstituicao) {
    HierarquiaInstituicao instituicao =
        hie.findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (instituicao == null) {
      throw new GenericServiceException(
          "Instituição não encontrada.",
          "IdProcessadora: " + idProcessadora + " IdInstituicao: " + idInstituicao);
    }

    Map<String, Object> parames = new HashMap<>();
    String logoEmissor = BoletoUtil.getLogoEmissor(instituicao.getUrlLogo());

    try {

      File logoCabecalho = new File(logoEmissor);

      if (logoCabecalho != null && logoCabecalho.exists()) {
        parames.put("logo_cabecalho", logoCabecalho.toURI().toURL());
      }
    } catch (MalformedURLException e) {
      e.printStackTrace();
      parames = new HashMap<>();
    }
    return parames;
  }

  public InputStream gerarBoletoParaDownload(GerarBoletoCarga gerarBoletoCarga) {

    Calendar dataDocumento = Calendar.getInstance();
    Calendar dataVencimento = Calendar.getInstance();

    Boleto boleto = getBoleto(gerarBoletoCarga, dataDocumento, dataVencimento);
    Map<String, Object> parames =
        getParametrosAdicionaisBoleto(
            gerarBoletoCarga.getIdProcessadora(), gerarBoletoCarga.getIdInstituicao());

    GeradorDeBoletoItspay gerador = new GeradorDeBoletoItspay(parames, boleto);

    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(BOLETO_DIR);
    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(gerarBoletoCarga.getIdProcessadora());
    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    if (Objects.isNull(params) || params.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: " + BOLETO_DIR);
    }

    String nomeArq =
        getNomeArquivoBoleto(
            dataDocumento.get(Calendar.HOUR_OF_DAY),
            dataDocumento.get(Calendar.MINUTE),
            dataDocumento.get(Calendar.SECOND),
            dataVencimento.get(Calendar.DAY_OF_MONTH),
            dataVencimento.get(Calendar.MONTH),
            dataVencimento.get(Calendar.YEAR),
            boleto.getNossoNumeroECodDocumento());

    String diretorio = params.get(POSICAO_ZERO).getValorParametro();
    String arquivo = diretorio + nomeArq;

    return gerador.geraPDFStream();
  }

  /**
   * Método responsavel por gerar a linha digitavel a partir de um codigo de barras. OBS: O codigo
   * de barras pode ser recuperado pelo objeto {@link Boleto}
   *
   * @param codigoDeBarras
   * @param banco
   * @return linha digitavel
   */
  public String gerarLinhaDigitavel(String codigoDeBarras, Banco banco) {
    return new GeradorDeLinhaDigitavel().geraLinhaDigitavelPara(codigoDeBarras, banco);
  }

  /**
   * @param gerarBoletoCarga
   * @param dataDocumento
   * @param dataVencimento
   * @return {@link br.com.caelum.stella.boleto.Boleto Boleto}
   */
  public Boleto getBoleto(
      GerarBoletoCarga gerarBoletoCarga, Calendar dataDocumento, Calendar dataVencimento) {

    HierarquiaInstituicao instituicao = getHierarquiaInstituicao(gerarBoletoCarga);

    if (instituicao == null) {
      throw new NoResultException(
          "Não foi possível iniciar a geração do boleto.Instituição não encontrada com os parametros informados: processadora ="
              + gerarBoletoCarga.getIdProcessadora()
              + ", instituicao = "
              + gerarBoletoCarga.getIdInstituicao());
    }

    ProdutoInstituicaoConfiguracao prodInstConfig =
        getProdutoInstituicaoConfiguracao(gerarBoletoCarga);

    if (prodInstConfig == null) {
      throw new NoResultException(
          "Não foi possível iniciar a geração do boleto com os parametros informados: processadora ="
              + gerarBoletoCarga.getIdProcessadora()
              + ", instituicao = "
              + gerarBoletoCarga.getIdInstituicao()
              + ", produto = "
              + gerarBoletoCarga.getIdProduto());
    }
    Pessoa pessoa = getPessoa(gerarBoletoCarga);

    if (pessoa == null) {
      throw new NoResultException(
          "Não foi possível encontrar a pessoa para a geração do boleto com os parametros informados: processadora ="
              + gerarBoletoCarga.getIdProcessadora()
              + ", instituicao = "
              + gerarBoletoCarga.getIdInstituicao()
              + ", documento = "
              + gerarBoletoCarga.getDocumentoPortador());
    }

    ContaPagamento contaPagamento = getContaPagamento(gerarBoletoCarga);

    if (contaPagamento == null) {
      throw new NoResultException(
          "Não foi possível encontrar a conta para a geração do boleto com os parametros informados: processadora ="
              + gerarBoletoCarga.getIdProcessadora()
              + ", instituicao = "
              + gerarBoletoCarga.getIdInstituicao()
              + ", contaPagamento = "
              + gerarBoletoCarga.getContaPagamento());
    }

    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(contaPagamento, Servicos.BOLETO);

    // agora
    int mes = dataDocumento.get(Calendar.MONTH) + MES_DELAY;
    int dia = dataDocumento.get(Calendar.DAY_OF_MONTH);
    int ano = dataDocumento.get(Calendar.YEAR);

    // dia vencimento
    // dataVencimento.setTime(dataVencimento.getTime())
    int diaVenc = dataVencimento.get(Calendar.DAY_OF_MONTH);
    int mesVenc = dataVencimento.get(Calendar.MONTH) + MES_DELAY;
    int anoVenc = dataVencimento.get(Calendar.YEAR);

    Datas datas =
        Datas.novasDatas()
            .comDocumento(dia, mes, ano)
            .comProcessamento(dia, mes, ano)
            .comVencimento(diaVenc, mesVenc, anoVenc);

    // Quem emite o boleto
    Endereco enderecoBeneficiario =
        Endereco.novoEndereco()
            .comLogradouro(instituicao.getLogradouro())
            .comBairro(instituicao.getBairro())
            .comCep(instituicao.getCep())
            .comCidade(instituicao.getCidade())
            .comUf(instituicao.getUf());

    Beneficiario beneficiario =
        Beneficiario.novoBeneficiario()
            .comNomeBeneficiario(instituicao.getRazaoSocial())
            .comAgencia(prodInstConfig.getBoletoAgencia())
            .comDigitoAgencia(prodInstConfig.getBoletoDigitoAgencia())
            .comCodigoBeneficiario(prodInstConfig.getBoletoCodigoBeneficiario())
            .comDigitoCodigoBeneficiario(prodInstConfig.getBoletoDigitoCodigoBeneficiario())
            .comNumeroConvenio(prodInstConfig.getBoletoNumeroConvenio())
            .comCarteira(prodInstConfig.getBoletoCarteira())
            .comEndereco(enderecoBeneficiario)
            .comNossoNumero(contaPagamento.getIdConta().toString());

    EnderecoPessoa end = getEnderecoPessoa(pessoa);

    // Quem paga o boleto
    Endereco enderecoPagador =
        Endereco.novoEndereco()
            .comLogradouro(end.getLogradouro())
            .comBairro(end.getBairro())
            .comCep(end.getCep())
            .comCidade(end.getCidade())
            .comUf(end.getUf());

    Pagador pagador =
        Pagador.novoPagador()
            .comNome(pessoa.getNomeCompleto())
            .comDocumento(pessoa.getDocumento())
            .comEndereco(enderecoPagador);

    String codigo =
        Strings.padStart(prodInstConfig.getBoletoBanco().toString(), TAM_COD_BANCO, CHAR_ZERO);
    Banco banco = BancosBoletoFactory.getBancoByCodigo(codigo);

    String[] instrucoes = getInstrucoesBoleto(prodInstConfig);
    String[] locaisPagamento = getLocaisPagamento(prodInstConfig);

    return BoletoItsPay.novoBoleto()
        .comBanco(banco)
        .comDatas(datas)
        .comBeneficiario(beneficiario)
        .comPagador(pagador)
        .comValorBoleto(gerarBoletoCarga.getValor())
        .comNumeroDoDocumento(gerarBoletoCarga.getContaPagamento())
        .comInstrucoes(instrucoes)
        .comLocaisDePagamento(locaisPagamento);
  }

  /**
   * @return {@link br.com.caelum.stella.boleto.Boleto Boleto}
   */
  public Boleto getBoletoCobrancaBancaria(
      HierarquiaInstituicao instituicao,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      Pessoa pessoa,
      ContaPagamento conta,
      CobrancaBancaria cobranca) {

    ParametroProcessamentoSistema parametroProcessamentoSistema =
        parametroProcessamentoSistemaService.findFirstByIdInstituicaoAndTipoParametro(
            cobranca.getIdInstituicao(), Constantes.BOLETO_RENDIMENTO);
    // agora
    int mes = cobranca.getDataDocumento().get(Calendar.MONTH) + MES_DELAY;
    int dia = cobranca.getDataDocumento().get(Calendar.DAY_OF_MONTH);
    int ano = cobranca.getDataDocumento().get(Calendar.YEAR);

    // dia vencimento
    // dataVencimento.setTime(dataVencimento.getTime())
    int diaVenc = cobranca.getDataVencimento().get(Calendar.DAY_OF_MONTH);
    int mesVenc = cobranca.getDataVencimento().get(Calendar.MONTH) + MES_DELAY;
    int anoVenc = cobranca.getDataVencimento().get(Calendar.YEAR);

    Datas datas =
        Datas.novasDatas()
            .comDocumento(dia, mes, ano)
            .comProcessamento(dia, mes, ano)
            .comVencimento(diaVenc, mesVenc, anoVenc);

    // Quem emite o boleto
    Endereco enderecoBeneficiario =
        Endereco.novoEndereco()
            .comLogradouro(instituicao.getLogradouro())
            .comBairro(instituicao.getBairro())
            .comCep(instituicao.getCep())
            .comCidade(instituicao.getCidade())
            .comUf(instituicao.getUf());

    Beneficiario beneficiario =
        Beneficiario.novoBeneficiario()
            .comNomeBeneficiario(instituicao.getRazaoSocial())
            .comAgencia(prodInstConfig.getBoletoAgencia())
            .comDocumento(instituicao.getCnpj())
            .comDigitoAgencia(prodInstConfig.getBoletoDigitoAgencia())
            .comDocumento(instituicao.getCnpj())
            .comCodigoBeneficiario(prodInstConfig.getBoletoCodigoBeneficiario())
            .comDigitoCodigoBeneficiario(prodInstConfig.getBoletoDigitoCodigoBeneficiario())
            .comNumeroConvenio(prodInstConfig.getBoletoNumeroConvenio())
            .comCarteira(prodInstConfig.getBoletoCarteira())
            .comEndereco(enderecoBeneficiario)
            .comNossoNumero(montarNossoNumero(cobranca));

    EnderecoPessoa end = getEnderecoPessoa(pessoa);

    // Quem paga o boleto
    String numero = end.getNumero();
    numero = numero == null ? "" : " , " + numero;

    Endereco enderecoPagador =
        Endereco.novoEndereco()
            .comLogradouro(end.getLogradouro() + numero)
            .comBairro(end.getBairro())
            .comCep(end.getCep())
            .comCidade(end.getCidade())
            .comUf(end.getUf());

    String nomeSacado =
        pessoa.getTipoPessoa().getId() == 1 ? pessoa.getNomeCompleto() : pessoa.getRazaoSocial();

    Pagador pagador =
        Pagador.novoPagador()
            .comNome(nomeSacado)
            .comDocumento(pessoa.getDocumento())
            .comEndereco(enderecoPagador);

    String codigo = null;

    // se o parametro autorizado esta para o banco bradesco, gera boleto pelo bradesco
    // se nao esta autorizado para o bradesco gera pelo banco rendimento
    if (!Objects.isNull(parametroProcessamentoSistema)
        && parametroProcessamentoSistema.getTexto().equals("AUTORIZADO")) {
      codigo = Strings.padStart(Constantes.CODIGO_BANCO_RENDIMENTO, TAM_COD_BANCO, CHAR_ZERO);
    } else {
      codigo =
          Strings.padStart(prodInstConfig.getBoletoBanco().toString(), TAM_COD_BANCO, CHAR_ZERO);
    }

    Banco banco = BancosBoletoFactory.getBancoByCodigo(codigo);

    String[] instrucoes = getInstrucoesBoleto(prodInstConfig);
    String[] locaisPagamento = getLocaisPagamento(prodInstConfig);

    return BoletoItsPay.novoBoleto()
        .comBanco(banco)
        .comDatas(datas)
        .comBeneficiario(beneficiario)
        .comPagador(pagador)
        .comValorBoleto(cobranca.getValorBoleto())
        .comNumeroDoDocumento(
            (!Objects.isNull(parametroProcessamentoSistema)
                        && parametroProcessamentoSistema.getTexto().equals("AUTORIZADO"))
                    || Constantes.CODIGO_BANCO_BANESE.equals(banco.getNumeroFormatado())
                ? montarNumeroDocumento(cobranca.getIdCobrancaBancaria())
                : montarNumeroDocumento(conta.getIdConta()))
        .comInstrucoes(instrucoes)
        .comLocaisDePagamento(locaisPagamento)
        .comEspecieDocumento("DS");
  }

  private EnderecoPessoa getEnderecoPessoa(Pessoa pessoa) {
    EnderecoPessoa end;
    if (pessoa.getTipoPessoa().getId() == 1) {
      end =
          enderecoRepository.findOneByIdPessoaAndIdTipoEnderecoAndStatus(
              pessoa.getIdPessoa(), RESIDENCIAL, ATIVO);
    } else {
      end =
          enderecoRepository.findOneByIdPessoaAndIdTipoEnderecoAndStatus(
              pessoa.getIdPessoa(), FISCAL, ATIVO);
    }
    if (end == null) {
      end = new EnderecoPessoa();
      end.setLogradouro("Endereço Não Cadastrado");
      end.setBairro("");
      end.setCep("");
      end.setCidade("");
      end.setComplemento("");
      end.setUf("");
    }
    return end;
  }

  /**
   * @return {@link br.com.caelum.stella.boleto.Boleto Boleto}
   */
  public Boleto getBoletoCobrancaBancaria(CobrancaBancaria cobranca) {
    // agora
    int mes = cobranca.getDataDocumento().get(Calendar.MONTH) + MES_DELAY;
    int dia = cobranca.getDataDocumento().get(Calendar.DAY_OF_MONTH);
    int ano = cobranca.getDataDocumento().get(Calendar.YEAR);

    // dia vencimento
    // dataVencimento.setTime(dataVencimento.getTime())
    int diaVenc = cobranca.getDataVencimento().get(Calendar.DAY_OF_MONTH);
    int mesVenc = cobranca.getDataVencimento().get(Calendar.MONTH) + MES_DELAY;
    int anoVenc = cobranca.getDataVencimento().get(Calendar.YEAR);

    Datas datas =
        Datas.novasDatas()
            .comDocumento(dia, mes, ano)
            .comProcessamento(dia, mes, ano)
            .comVencimento(diaVenc, mesVenc, anoVenc);

    // Quem emite o boleto
    Endereco enderecoBeneficiario =
        Endereco.novoEndereco()
            .comLogradouro(cobranca.getLogradouroBeneficiario())
            .comBairro(cobranca.getBairroBeneficiario())
            .comCep(cobranca.getCepBeneficiario())
            .comCidade(cobranca.getCidadeBeneficiario())
            .comUf(cobranca.getUfBeneficiario());

    Beneficiario beneficiario =
        Beneficiario.novoBeneficiario()
            .comNomeBeneficiario(cobranca.getNomeBeneficiario())
            .comAgencia(cobranca.getAgenciaBeneficiario())
            .comDocumento(cobranca.getDocumentoBeneficiario())
            .comDigitoAgencia(cobranca.getDigitoAgenciaBeneficiario())
            .comCodigoBeneficiario(cobranca.getCodigoBeneficiario())
            .comDigitoCodigoBeneficiario(cobranca.getDigitoCodigoBeneficiario())
            .comNumeroConvenio(cobranca.getNumeroConvenioBeneficiario())
            .comCarteira(cobranca.getCarteiraBeneficiario())
            .comEndereco(enderecoBeneficiario)
            .comNossoNumero(montarNossoNumero(cobranca));

    // Quem paga o boleto
    Endereco enderecoPagador =
        Endereco.novoEndereco()
            .comLogradouro(cobranca.getLogradouroPagador())
            .comBairro(cobranca.getBairroPagador())
            .comCep(cobranca.getCepPagador())
            .comCidade(cobranca.getCidadePagador())
            .comUf(cobranca.getUfPagador());

    Pagador pagador =
        Pagador.novoPagador()
            .comNome(cobranca.getNomePagador())
            .comDocumento(cobranca.getDocumentoPagador())
            .comEndereco(enderecoPagador);

    String codigo =
        Strings.padStart(cobranca.getBancoEmissor().toString(), TAM_COD_BANCO, CHAR_ZERO);
    Banco banco = BancosBoletoFactory.getBancoByCodigo(codigo);

    String[] instrucoes = getInstrucoesBoleto(cobranca.getInstrucoes());
    String[] locaisPagamento = getLocaisPagamento(cobranca.getLocaisDePagamento());

    Boleto boleto =
        BoletoItsPay.novoBoleto()
            .comBanco(banco)
            .comDatas(datas)
            .comBeneficiario(beneficiario)
            .comPagador(pagador)
            .comValorBoleto(cobranca.getValorBoleto())
            .comNumeroDoDocumento(montarNumeroDocumento(cobranca.getIdCobrancaBancaria()))
            .comInstrucoes(instrucoes)
            .comLocaisDePagamento(locaisPagamento)
            .comEspecieDocumento("DS");

    if (cobranca.getCodigoDeBarras() != null) {
      ((BoletoItsPay) (boleto)).setCodigoDeBarras(cobranca.getCodigoDeBarras());
    }

    if (cobranca.getLinhaDigitavel() != null) {
      ((BoletoItsPay) (boleto)).setLinhaDigitavel(cobranca.getLinhaDigitavel());
    }

    return boleto;
  }

  private String montarNossoNumero(CobrancaBancaria cobranca) {
    if (cobranca.getBancoEmissor() != null) {
      if (cobranca.getBancoEmissor() == 47
          && cobranca.getCodigoRegistroBanco() != null) { // BANCO BANESE
        return cobranca.getCodigoRegistroBanco();
      } else {
        return "99" + Strings.padStart(cobranca.getIdCobrancaBancaria().toString(), 8, '0');
      }
    } else {
      return "99" + Strings.padStart(cobranca.getIdCobrancaBancaria().toString(), 8, '0');
    }
  }

  private String montarNumeroDocumento(Long idCobrancaBancaria) {
    return "99" + Strings.padStart(idCobrancaBancaria.toString(), 8, '0');
  }

  private String getNomeArquivoBoleto(
      int hora,
      int minutos,
      int segundos,
      int diaVenc,
      int mesVenc,
      int anoVenc,
      String nossoNumero) {
    StringBuilder nomeArq = new StringBuilder();
    nomeArq.append("boleto_");
    nomeArq.append(nossoNumero.replaceAll("/", ""));
    nomeArq.append("_venc_");
    nomeArq.append(diaVenc);
    nomeArq.append("_");
    nomeArq.append(mesVenc + 1);
    nomeArq.append("_");
    nomeArq.append(anoVenc);
    nomeArq.append("_");
    nomeArq.append(hora);
    nomeArq.append("_");
    nomeArq.append(minutos);
    nomeArq.append("_");
    nomeArq.append(segundos);
    nomeArq.append(".pdf");
    return nomeArq.toString();
  }

  private String[] getInstrucoesBoleto(String instrucoes) {
    return instrucoes != null ? instrucoes.split(";") : new String[UMA_POSICAO];
  }

  private String[] getInstrucoesBoleto(ProdutoInstituicaoConfiguracao prodInstConfig) {
    return prodInstConfig.getBoletoInstrucoes() != null
        ? prodInstConfig.getBoletoInstrucoes().split(";")
        : new String[UMA_POSICAO];
  }

  private String[] getLocaisPagamento(ProdutoInstituicaoConfiguracao prodInstConfig) {
    return prodInstConfig.getBoletoLocaisPagamento() != null
        ? prodInstConfig.getBoletoLocaisPagamento().split(";")
        : new String[UMA_POSICAO];
  }

  private String[] getLocaisPagamento(String locais) {
    return locais != null ? locais.split(";") : new String[UMA_POSICAO];
  }

  private HierarquiaInstituicao getHierarquiaInstituicao(GerarBoletoCarga dados) {
    return hie.findByIdProcessadoraAndIdInstituicao(
        dados.getIdProcessadora(), dados.getIdInstituicao());
  }

  private ContaPagamento getContaPagamento(GerarBoletoCarga dados) {
    return contaRepository.findOneByIdProcessadoraAndIdInstituicaoAndIdContaPagamento(
        dados.getIdProcessadora(), dados.getIdInstituicao(), dados.getContaPagamento());
  }

  private Pessoa getPessoa(GerarBoletoCarga dados) {
    return pessoaRepository.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoa(
        dados.getIdProcessadora(),
        dados.getIdInstituicao(),
        dados.getDocumentoPortador(),
        PESSOA_FISICA);
  }

  private ProdutoInstituicaoConfiguracao getProdutoInstituicaoConfiguracao(GerarBoletoCarga dados) {
    return prodInstConfigRepository.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
        dados.getIdProcessadora(), dados.getIdProduto(), dados.getIdInstituicao());
  }

  @Transactional
  public CobrancaBancaria gerarBoletoCobrancaBancaria(
      Long idConta,
      BigDecimal valor,
      Calendar vencimento,
      Integer motivo,
      Boolean confirmacaoProc) {
    return gerarBoletoCobrancaBancaria(
        idConta,
        valor,
        vencimento,
        motivo,
        Constantes.ID_USUARIO_INCLUSAO_PORTADOR,
        confirmacaoProc);
  }

  @Transactional
  public CobrancaBancaria gerarBoletoCobrancaBancaria(
      Long idConta,
      BigDecimal valor,
      Calendar vencimento,
      Integer motivo,
      SecurityUser user,
      Boolean confirmacaoProc) {
    return gerarBoletoCobrancaBancaria(
        idConta, valor, vencimento, motivo, user.getIdUsuario(), confirmacaoProc);
  }

  /**
   * c
   *
   * @param idConta
   * @param valor
   * @param vencimento
   * @param motivo
   * @param confirmacaoProc
   * @return
   */
  @Transactional
  public CobrancaBancaria gerarBoletoCobrancaBancaria(
      Long idConta,
      BigDecimal valor,
      Calendar vencimento,
      Integer motivo,
      Integer idUsuario,
      Boolean confirmacaoProc) {
    ContaPagamento contaPagamento = obterContaPagamento(idConta);

    List<ContaPessoa> contasPessoa = listarContaPessoa(idConta);

    if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(contaPagamento.getIdInstituicao())
        && !Constantes.CONTA_ATIVA.equals(contaPagamento.getIdStatusConta())) {
      throw new GenericServiceException(
          "A Conta precisa estar Desbloqueada para realizar Emissão de Boletos.");
    }

    Pessoa pessoa = obterPessoa(contasPessoa);

    HierarquiaInstituicao instituicao = obterHierarquiaInstituicao(contaPagamento);

    ProdutoInstituicaoConfiguracao prodInstConf =
        obterProdutoInstituicaoConfiguracao(contaPagamento);

    // Não permitir criação se o valor do boleto não for maior ou igual à somatórias
    // dos valores de tarifas do motivo.
    permiteEmitirCobranca(contaPagamento, motivo, valor);

    CobrancaBancaria cobranca =
        salvarCobrancaBancaria(
            valor,
            vencimento,
            motivo,
            idUsuario,
            contaPagamento,
            pessoa,
            instituicao,
            prodInstConf,
            confirmacaoProc);

    Boleto b =
        getBoletoCobrancaBancaria(instituicao, prodInstConf, pessoa, contaPagamento, cobranca);
    return cobrancaBancariaService.gravarCobrancaBancariaBoleto(b, cobranca, prodInstConf);
  }

  private CobrancaBancaria salvarCobrancaBancaria(
      BigDecimal valor,
      Calendar vencimento,
      Integer motivo,
      Integer idUsuario,
      ContaPagamento contaPagamento,
      Pessoa pessoa,
      HierarquiaInstituicao instituicao,
      ProdutoInstituicaoConfiguracao prodInstConf,
      Boolean confirmacaoProc) {

    CobrancaBancaria cobranca = new CobrancaBancaria();
    cobranca.setDataVencimento(vencimento);
    cobranca.setDataDocumento(Calendar.getInstance());
    cobranca.setIdProcessadora(instituicao.getIdProcessadora());
    cobranca.setIdInstituicao(instituicao.getIdInstituicao());
    cobranca.setIdProdutoInstituicao(contaPagamento.getIdProdutoInstituicao());
    cobranca.setDataHoraGeracao(LocalDateTime.now());
    cobranca.setValorBoleto(valor);
    cobranca.setIdConta(contaPagamento.getIdConta());
    cobranca.setMotivo(motivo);
    cobranca.setDocumentoPagador(pessoa.getDocumento());
    cobranca.setTipoBoleto(PORTADOR);
    cobranca.setIdUsuarioInclusao(idUsuario);
    cobranca.setRegistrado(
        Objects.nonNull(prodInstConf.getBoletoRegistrado()) && prodInstConf.getBoletoRegistrado());
    cobranca.setConfirmacaoProc(confirmacaoProc);

    cobranca = cobrancaBancariaService.save(cobranca);
    return cobranca;
  }

  private ProdutoInstituicaoConfiguracao obterProdutoInstituicaoConfiguracao(
      ContaPagamento contaPagamento) {
    ProdutoInstituicaoConfiguracao prodInstConf =
        prodInstConfigRepository.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdProdutoInstituicao(),
            contaPagamento.getIdInstituicao());

    if (Objects.isNull(prodInstConf)) {
      throw new GenericServiceException(
          "Não foi possível encontrar configuração do produto para informações utilizadas.",
          "idProdInstituicao: " + contaPagamento.getIdProdutoInstituicao());
    }
    return prodInstConf;
  }

  private HierarquiaInstituicao obterHierarquiaInstituicao(ContaPagamento contaPagamento) {
    HierarquiaInstituicao instituicao =
        hie.findByIdProcessadoraAndIdInstituicao(
            contaPagamento.getIdProcessadora(), contaPagamento.getIdInstituicao());

    if (Objects.isNull(instituicao)) {
      throw new GenericServiceException(
          "Não foi possível encontrar Instituição para informações utilizadas.");
    }
    return instituicao;
  }

  private Pessoa obterPessoa(List<ContaPessoa> contasPessoa) {
    Pessoa[] pessoas = new Pessoa[1];

    contasPessoa.forEach(
        cp -> {
          if (cp.getIdTitularidade().equals(TITULAR)) {
            pessoas[0] = pessoaRepository.findById(cp.getIdPessoa()).orElse(null);
          }
        });

    Pessoa pessoa = pessoas[0];
    if (Objects.isNull(pessoa)) {
      throw new GenericServiceException(
          "Não foi possível encontrar Pessoa para informações utilizadas.");
    }
    return pessoa;
  }

  private List<ContaPessoa> listarContaPessoa(Long idConta) {
    List<ContaPessoa> contasPessoa = contaPessoaService.findByIdConta(idConta);

    if (Objects.isNull(contasPessoa)) {
      throw new GenericServiceException(
          "Não foi possível encontrar contas para informações utilizadas.", "IdConta: " + idConta);
    }
    return contasPessoa;
  }

  private ContaPagamento obterContaPagamento(Long idConta) {
    ContaPagamento contaPagamento = contaRepository.findByIdConta(idConta);

    if (Objects.isNull(contaPagamento)) {
      throw new GenericServiceException(
          "Não foi possível encontrar conta para informações utilizadas.", "IdConta: " + idConta);
    }
    return contaPagamento;
  }

  private void permiteEmitirCobranca(
      ContaPagamento contaPagamento, Integer motivo, BigDecimal valor) {
    List<CodigoTransacao> codigos =
        codigoTransacaoService.getCodsPassiveisCobrancaTarifaByMotivo(
            contaPagamento.getIdProdutoInstituicao(), motivo);

    BigDecimal somaTarifas = BigDecimal.ZERO;
    HashSet<Integer> cods = new HashSet<>();

    for (CodigoTransacao c : codigos) {
      if (Objects.nonNull(c.getCodTranTarifa())) {
        cods.add(c.getCodTranTarifa());
      } else if (Objects.nonNull(c.getIdTipoTransacao()) && c.getIdTipoTransacao().equals(TARIFA)) {
        cods.add(c.getCodTransacao());
      }
    }

    if (!cods.isEmpty()) {
      Integer perfilTarifario = contaPagamento.getIdPerfilTarifario();
      if (perfilTarifario == null) {
        perfilTarifario =
            perfilTarifarioService.buscarIdPerfilTarifarioProduto(contaPagamento.getIdConta());
      }
      BigDecimal tarifas =
          pfTransacaoService.sumTarifasByIdProdutoAndCodsTransacao(
              cods, contaPagamento.getIdProdutoInstituicao(), perfilTarifario);

      somaTarifas = Objects.nonNull(tarifas) ? tarifas : somaTarifas;

      if (somaTarifas.compareTo(valor) > 0) {
        throw new GenericServiceException(
            "Não é possível gerar a cobrança bancária. O valor do boleto deve ser maior ou igual ao valor de tarifas que "
                + "o motivo escolhido irá gerar. Valor Boleto: "
                + valor
                + ", Motivo: "
                + motivo
                + ", Soma das Tarifas: "
                + somaTarifas);
      }
    }
  }

  public List<GetCobrancasBancarias> findCobrancasByConta(Long idConta, SecurityUser user) {
    consultaRestritaService.checaPrivilegio(idConta, user);
    return cobrancaBancariaService.findCobrancasByConta(idConta);
  }

  public List<MotivoCobranca> findMotivosByProdConta(Long idConta) {

    ContaPagamento conta = contaRepository.findOneByIdConta(idConta);

    return motivoCobrancaProdutoService.findMotivosByProdConta(conta.getIdProdutoInstituicao());
  }

  public Boolean gerarBoletoAndSendLinhaDigitavelPorSMS(Long idCobrancaBancaria) {

    CobrancaBancaria cobranca = cobrancaBancariaService.findById(idCobrancaBancaria);
    travaContasService.travaContas(cobranca.getIdConta(), Servicos.BOLETO);
    Boleto boleto = getBoletoCobrancaBancaria(cobranca);
    BoletoCarga boletoCarga = new BoletoCarga();
    BeanUtils.copyProperties(boleto, boletoCarga);

    String dataValidadeFmtMes =
        DateUtil.dateFormat(FMT_D_MMM, boleto.getDatas().getVencimento().getTime());
    boletoCarga.setDataVencimentoFmtMes(dataValidadeFmtMes);

    Double d = boleto.getValorBoleto().doubleValue();
    Locale ptBr = new Locale("pt", "BR");
    String valorBoletoFmt = NumberFormat.getCurrencyInstance(ptBr).format(d);
    boletoCarga.setValorBoletoFmt(valorBoletoFmt);

    return enviarLinhaDigitavelPorSms(cobranca, boletoCarga);
  }

  public Boolean enviarLinhaDigitavelPorSms(CobrancaBancaria cobranca, BoletoCarga boletoCarga) {

    ContaPagamento conta = contaRepository.findContaTitular(cobranca.getIdConta());

    List<Credencial> credenciais =
        credencialService.recuperarCredenciaisPorIdConta(conta.getIdConta());

    Credencial credencial =
        credenciais.stream()
            .sorted(
                Comparator.comparing(Credencial::getCsn).thenComparing(Credencial::getTitularidade))
            .findFirst()
            .get();

    Pessoa pessoa = pessoaRepository.findOneByIdPessoa(credencial.getIdPessoa());
    if (validaNumeroCelular(pessoa)) {
      GatewaySMS gatewaySMS = getGatewaySMS(pessoa.getIdProcessadora(), pessoa.getIdInstituicao());
      montarEEnviarSMS(
          cobranca.getIdConta(), credencial.getIdCredencial(), pessoa, boletoCarga, gatewaySMS);
    } else {
      throw new GenericServiceException("Este número telefônico não é válido");
    }
    return true;
  }

  public boolean validaNumeroCelular(Pessoa pessoa) {
    if (pessoa.getDdiTelefoneCelular().equals(55)) {
      String telefone =
          pessoa.getDddTelefoneCelular().toString() + pessoa.getTelefoneCelular().toString();
      return telefone.matches("^[1-9]{2}[9]{1}[6-9]{1}[0-9]{3}[0-9]{4}");
    } else {
      throw new GenericServiceException(
          "Este número telefônico não pertence ao território Brasileiro");
    }
  }

  private void montarEEnviarSMS(
      Long idConta, Long idCredencial, Pessoa pessoa, BoletoCarga boletoCarga, GatewaySMS gateway) {
    ComunicadoContaViaSMS comunicado = new ComunicadoContaViaSMS();
    comunicado.setIdConta(idConta);
    comunicado.setIdCredencial(idCredencial);
    comunicado.setIdGatewaySMS(gateway.getIdGatewaySMS());
    comunicado.setMensagem(
        "Linha digitável do boleto: "
            + boletoCarga.getLinhaDigitavel()
            + ". Vencimento: "
            + boletoCarga.getDataVencimentoFmtMes()
            + ". Valor: "
            + boletoCarga.getValorBoletoFmt());
    comunicado.setTipoEventoConta(TIPO_EVENTO_ENVIO_LINHA_DIGITAVEL_SMS);
    StringBuilder tel = new StringBuilder();
    tel.append(pessoa.getDddTelefoneCelular());
    tel.append(pessoa.getTelefoneCelular());

    comunicado.setNumeroCelular(new Long(tel.toString()));
    agenteComunicadorService.prepararComunicacao(comunicado);

    enviarSms(comunicado);
  }

  @Async
  public void enviarSms(ComunicadoContaViaSMS comunicado) {
    agenteComunicadorService.comunicar(comunicado.getIdLogEventoConta());
  }

  private GatewaySMS getGatewaySMS(Integer idProcessadora, Integer idInstituicao) {
    GatewaySMS gatewaySMS =
        gatewaySMSService.findFirstByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    if (gatewaySMS == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar gatewaySMS. IdProcessadora: "
              + idProcessadora
              + " , idInstituicao: "
              + idInstituicao);
    }
    return gatewaySMS;
  }

  // VALIDAR SE COBRANCA EXISTE
  public Boolean validarExistenciaBoleto(Long token) {
    CobrancaBancaria cobranca = cobrancaBancariaService.findById(token);
    return cobranca != null;
  }
}
