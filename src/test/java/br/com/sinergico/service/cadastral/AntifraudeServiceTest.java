package br.com.sinergico.service.cadastral;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import br.com.entity.suporte.AntifraudeCafInstituicaoConfig;
import br.com.entity.suporte.AntifraudeCafPortador;
import br.com.entity.suporte.TokenCafDTO;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.antifraude.CombateFraudeCaf;
import br.com.json.bean.combateafraude.VerificacaoDocumentosRequest;
import br.com.json.bean.combateafraude.VerificacaoOcrCafResponse;
import br.com.json.bean.suporte.AntifraudeCafPortadorIssuerVO;
import br.com.json.bean.suporte.PortadoresCafFiltroRequest;
import br.com.sinergico.client.CombateFraudeClient;
import br.com.sinergico.repository.suporte.AntifraudeCafInstituicaoConfigRepository;
import br.com.sinergico.repository.suporte.AntifraudeCafPortadorRepository;
import br.com.sinergico.util.Constantes;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AntifraudeServiceTest {

  @Mock private AntifraudeCafInstituicaoConfigRepository antifraudeCafInstituicaoConfigRepository;
  @Mock private AntifraudeCafPortadorRepository antifraudeCafPortadorRepository;
  @Mock private DocumentoContaService documentoContaService;
  @Mock private PessoaService pessoaService;
  @Mock private AntifraudeService antifraudeServiceMock;
  @Mock private LogAntifraudeService logAntifraudeService;
  @Mock private CombateFraudeClient combateFraudeClient;

  @InjectMocks @Spy private AntifraudeService antifraudeService;

  @Mock private CombateFraudeCaf request;
  private AntifraudeCafInstituicaoConfig instituicaoConfig;

  @BeforeEach
  void setUp() {
    MockitoAnnotations.openMocks(this);
    request = new CombateFraudeCaf();
    request.setIdInstituicao(999);
    request.setDocumentoRepresentante("***********");
    request.setArquivo(null);
    request.setJwt("123422");

    instituicaoConfig = new AntifraudeCafInstituicaoConfig();
    when(antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(anyInt()))
        .thenReturn(instituicaoConfig);
  }

  @Test
  void testInstituicaoNaoConfiguradaException() {
    when(antifraudeCafInstituicaoConfigRepository.findByIdInstituicao(anyInt())).thenReturn(null);
    Exception exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              antifraudeService.startValidationCaf(request, java.util.Optional.empty());
            });
    assertEquals(
        "Instituição não configurada para validação do OCR - CAF.", exception.getMessage());
  }

  @Test
  void testContratoSocialObrigatorioException() {
    Exception exception =
        assertThrows(
            GenericServiceException.class,
            () -> {
              antifraudeService.startValidationCaf(request, java.util.Optional.empty());
            });
    assertEquals(
        "Contrato social é obrigatório para validação do OCR - CAF.", exception.getMessage());
  }

  @Test
  void testInstituicaoInvalidaLancaExcecao() {
    CombateFraudeCaf request = new CombateFraudeCaf();
    request.setIdInstituicao(999);
    request.setDocumentoRepresentante("***********");
    request.setArquivo(null);
    request.setJwt("123422");

    GenericServiceException exception =
        assertThrows(
            GenericServiceException.class,
            () -> antifraudeService.startValidationCaf(request, java.util.Optional.empty()));

    assertEquals(
        "Contrato social é obrigatório para validação do OCR - CAF.", exception.getMessage());
  }

  @Test
  void testInstituicoesValidasNaoLancaExcecao()
      throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

    Method metodoPrivado =
        AntifraudeService.class.getDeclaredMethod(
            "findOrCreateAntifraudeCafPortador",
            CombateFraudeCaf.class,
            VerificacaoOcrCafResponse.class,
            AntifraudeCafInstituicaoConfig.class);
    metodoPrivado.setAccessible(true);

    Integer[] instituicoesValidas = {
      Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS,
      Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS,
      Constantes.ID_PRODUCAO_INSTITUICAO_FINANCIAL,
      Constantes.ID_PRODUCAO_INSTITUICAO_POC_ELO,
      Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK,
      Constantes.ID_PRODUCAO_INSTITUICAO_DISNUVII,
      Constantes.ID_PRODUCAO_INSTITUICAO_CVS_CESTAS,
      Constantes.ID_PRODUCAO_INSTITUICAO_WIZ,
      Constantes.ID_PRODUCAO_QISTA,
      Constantes.ID_PRODUCAO_INSTITUICAO_TIRA_CHAPEU,
    };

    TokenCafDTO tokenMock = new TokenCafDTO();
    tokenMock.setIsAlive(true);
    doReturn(tokenMock).when(antifraudeService).verificarSelfieCaf(anyString());

    VerificacaoDocumentosRequest verificacaoDocumentosRequest = new VerificacaoDocumentosRequest();
    CombateFraudeCaf request = new CombateFraudeCaf();
    request.setDocumento("***********");
    request.setDocumentoRepresentante("***********");
    request.setArquivo(null);
    request.setJwt("123422");
    request.setRequestCaf(verificacaoDocumentosRequest);

    VerificacaoOcrCafResponse responseMock = new VerificacaoOcrCafResponse();
    responseMock.setRequestId("APROVADO");
    responseMock.setId("TRANSACTION123");
    when(combateFraudeClient.verificaDocumentosCaf(verificacaoDocumentosRequest))
        .thenReturn(responseMock);

    AntifraudeCafPortador antifraudeMock = new AntifraudeCafPortador();
    antifraudeMock.setIdCafInstituicaoConfig(1L);
    antifraudeMock.setTxDocumento("***********");
    antifraudeMock.setDtHrVerificacao(LocalDateTime.now());
    antifraudeMock.setBlFraude(Boolean.FALSE);
    antifraudeMock.setBlIgnorarValidacao(Boolean.FALSE);
    antifraudeMock.setBlForcarValidacao(Boolean.FALSE);
    antifraudeMock.setTxDocumentoRepresentante("***********");

    when(antifraudeCafPortadorRepository
            .findFirstByTxDocumentoAndTxDocumentoRepresentanteAndIdCafInstituicaoConfig(
                "***********", "***********", 1L))
        .thenReturn(antifraudeMock);

    when(antifraudeCafPortadorRepository.save(any())).thenReturn(antifraudeMock);

    doNothing().when(logAntifraudeService).salvarLog(any(AntifraudeCafPortador.class));

    for (Integer idInstituicao : instituicoesValidas) {
      request.setIdInstituicao(idInstituicao);
      AntifraudeCafInstituicaoConfig instituicaoConfig = new AntifraudeCafInstituicaoConfig();
      instituicaoConfig.setId(1L);

      assertDoesNotThrow(
          () -> {
            metodoPrivado.invoke(antifraudeService, request, responseMock, instituicaoConfig);
            antifraudeService.startValidationCaf(request, java.util.Optional.empty());
          },
          "Não deveria lançar exceção para a instituição: " + idInstituicao);
    }
  }

  @DisplayName("Deve retornar resultados ao buscar com filtros preenchidos")
  @Test
  void buscarTodasInformacoesComFiltrosPreenchidos_retornaResultados() {
    // Arrange
    PortadoresCafFiltroRequest filtro = new PortadoresCafFiltroRequest();
    filtro.setNome("João");
    filtro.setCpf("123.456.789-00");
    filtro.setData("2024-04-15T00:00:00");
    filtro.setDataFinal("2024-04-15T23:59:59");
    filtro.setStatus("APROVADO");

    AntifraudeCafPortadorIssuerVO vo = new AntifraudeCafPortadorIssuerVO();
    vo.setIdAcp(1L);
    vo.setNomeCompleto("João da Silva");
    vo.setCpf("***********");

    List<AntifraudeCafPortadorIssuerVO> resultados = List.of(vo);
    Page<AntifraudeCafPortadorIssuerVO> pageMock =
        new PageImpl<>(resultados, PageRequest.of(0, 10), 1);

    when(antifraudeCafPortadorRepository.buscarAntifraudeCafPortadorsDinamico(
            anyInt(),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            any(Pageable.class)))
        .thenReturn(pageMock);

    // Act
    Page<AntifraudeCafPortadorIssuerVO> resultado =
        antifraudeService.buscarTodasInformacoesAntifraudeCafPortadoresFiltroDinamico(
            filtro, 1, 10, 0);

    // Assert
    assertEquals(1, resultado.getTotalElements());
    assertEquals("João da Silva", resultado.getContent().get(0).getNomeCompleto());
    assertEquals("***********", resultado.getContent().get(0).getCpf());

    ArgumentCaptor<Pageable> pageableCaptor = ArgumentCaptor.forClass(Pageable.class);
    verify(antifraudeCafPortadorRepository)
        .buscarAntifraudeCafPortadorsDinamico(
            anyInt(),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            anyString(),
            pageableCaptor.capture());

    Pageable pageable = pageableCaptor.getValue();
    assertEquals(0, pageable.getPageNumber());
    assertEquals(10, pageable.getPageSize());
  }

  @DisplayName("Deve retornar resultados ao buscar com filtros preenchidos")
  @Test
  void buscarTodasInformacoesComFiltrosPreenchidos_retornaResultados1() {
    // Arrange
    PortadoresCafFiltroRequest filtro = new PortadoresCafFiltroRequest();
    filtro.setNome("João");
    filtro.setCpf("123.456.789-00");
    filtro.setData(null);
    filtro.setDataFinal(null);
    filtro.setStatus("APROVADO");

    AntifraudeCafPortadorIssuerVO vo = new AntifraudeCafPortadorIssuerVO();
    vo.setIdAcp(1L);
    vo.setNomeCompleto("João da Silva");
    vo.setCpf("***********");

    List<AntifraudeCafPortadorIssuerVO> resultados = List.of(vo);
    Page<AntifraudeCafPortadorIssuerVO> pageMock =
        new PageImpl<>(resultados, PageRequest.of(0, 10), 1);

    when(antifraudeCafPortadorRepository.buscarAntifraudeCafPortadorsDinamico(
            anyInt(),
            anyString(),
            anyString(),
            isNull(),
            isNull(),
            anyString(),
            any(Pageable.class)))
        .thenReturn(pageMock);

    // Act
    Page<AntifraudeCafPortadorIssuerVO> resultado =
        antifraudeService.buscarTodasInformacoesAntifraudeCafPortadoresFiltroDinamico(
            filtro, 1, 10, 0);

    // Assert
    assertEquals(1, resultado.getTotalElements());
    assertEquals("João da Silva", resultado.getContent().get(0).getNomeCompleto());
    assertEquals("***********", resultado.getContent().get(0).getCpf());

    ArgumentCaptor<Pageable> pageableCaptor = ArgumentCaptor.forClass(Pageable.class);
    verify(antifraudeCafPortadorRepository)
        .buscarAntifraudeCafPortadorsDinamico(
            anyInt(),
            anyString(),
            anyString(),
            isNull(),
            isNull(),
            anyString(),
            pageableCaptor.capture());

    Pageable pageable = pageableCaptor.getValue();
    assertEquals(0, pageable.getPageNumber());
    assertEquals(10, pageable.getPageSize());
  }
}
