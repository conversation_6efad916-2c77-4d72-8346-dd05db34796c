package br.com.sinergico.repository.jcard;

import br.com.entity.jcard.LogTransacoes;
import br.com.json.bean.jcard.AutorizacaoPendenteResponse;
import br.com.json.bean.jcard.TransacaoDePontoResponse;
import br.com.json.bean.transacional.TransacaoEstornadaResponse;
import br.com.json.bean.transacional.TransacoesByPeriodoResponse;
import br.com.json.bean.transacional.TransacoesPJB2BByPeriodoResponse;
import br.com.sinergico.vo.LogTransacoesVO;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface LogTransacoesRepositoryCustom {

  List<AutorizacaoPendenteResponse> findByLogTransacoesAutorizacoesPendentes(Long idConta);

  public BigDecimal findPontosDaJoyDisponiveisParaTransferencia(Long idConta);

  Long findIdLogTransacoesByRrn(String rrn);

  Integer findFunctionCodeByRrn(String rrn);

  List<TransacoesByPeriodoResponse> findTransacoesByHierarquiaAndProdutoAndPeriodo(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdInstituicao,
      String documento,
      Date dataInicio,
      Date dataFim);

  LogTransacoesVO findTransacaoByRrn(String rrn);

  List<TransacoesPJB2BByPeriodoResponse> findTransacoesPJB2BByHierarquiaAndPeriodo(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Date dataInicio,
      Date dataFim);

  Long findIdLogTransacoesQueEstornouRrn(String rrn);

  TransacaoDePontoResponse findTransacaoDePontoByRrn(String rrn);

  TransacaoDePontoResponse findTransacaoDePontoById(Long id);

  TransacaoEstornadaResponse findTransacaoEstornadaByIdTranlog(Long rrn);

  LogTransacoes findByRrn(String rrn);

  LogTransacoes findByIdTranlogOrQrCodeTransactionIdIsQrCodeElo(Long id, String transactionId);

  LogTransacoes findByRrnAndSsAndDia(String rrn, String ss, String dia);

  List<LogTransacoes> findByIdInstituicaoPeriodo(
      Integer idInstituicao, List<String> functionCodes, Date dataHoraInicio, Date dataHoraFim);
}
