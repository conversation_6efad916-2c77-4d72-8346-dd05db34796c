import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { MenuController, ModalController, Platform } from '@ionic/angular';
import { BiometryType, NativeBiometric } from 'capacitor-native-biometric';
import { ValidatorsApp } from '@utils/validators.util';
import { AuthService, environment, StorageService, TipoProdutoEnum, Usuario, TextsService, NavigationService } from '@mobile/shared';
import { loading } from '@utils/loading.util';
import { toast } from '@utils/toast.util';
import { TextUtil } from '@utils/text.util';
import { ModalAtencaoComponent } from '@mobile/modals';

@Component({
  selector: 'app-login',
  templateUrl: './login.page.html',
  styleUrls: ['./login.page.scss']
})
export class LoginPage implements OnInit {
  formLogin = new FormGroup(
    {
      // Leo - 05835504799 - Teste@123
      // pj alisson 30.388.519/0001-50
      // documento: new FormControl('30.388.519/0001-50', [Validators.required]),
      // documento: new FormControl('07869558133', [Validators.required]),
      // documentoAcesso: new FormControl('', [ValidatorsApp.cpf()]),
      // senha: new FormControl('lG04062002@', [Validators.required])
      // Rafinha - 05701516199 - Bbal0904109!
      // cpf: new FormControl('00972422170', [Validators.required, ValidatorsApp.cpf()]),
      // senha: new FormControl('Teste@123', [Validators.required]),
      // Willian - 06986915470 - @Teste13579
      // cpf: new FormControl('06986915470', [Validators.required, ValidatorsApp.cpf()]),
      // senha: new FormControl('@Teste13579', [Validators.required]),
      documento: new FormControl('', [Validators.required]),
      documentoAcesso: new FormControl('', [ValidatorsApp.cpf()]),
      senha: new FormControl('', [Validators.required])
    }
  );
  verSenha = false;
  usarBiometria = false;
  habilitarBiometria = false;
  isFaceId = false;
  usuario!: Usuario;
  rotaAtalho!: string;
  isRepresentanteLogin = false;
  mensagemErro = 'Informe um documento válido';

  showChat = environment.showChat;
  showIcon = environment.showIcon;
  showTitleDots = environment.showTitleDots;
  showLogo = environment.homeVersion === 'v1';

  constructor(private router: Router,
              private authService: AuthService,
              private storageService: StorageService,
              private menuController: MenuController,
              private modalController: ModalController,
              private platform: Platform,
              private textsService: TextsService,
              private navigationService: NavigationService) {
    this.menuController.enable(false);
  }

  async ngOnInit() {
    await this.setUsarBiometria();
    this.usuario = this.authService.getUser();
  }

  async entrar() {
    if (this.formLogin.invalid) {
      return;
    }
    const auth = this.formLogin.getRawValue();
    await this.logar(auth);
  }

  async logar(auth: any) {
    await loading(
      this.authService.entrar(auth).subscribe({
        next: (logon: any) => {
          console.log('logon', logon);
          this.storageService.setForcarOnboard(false);
          if (logon.encaminharAtendimento && environment.validarCadastroComCaf) {
            return this.router.navigate(['/erro-validacao'], { state: { retornoCaf: true } });
          }

          if (logon.isAntifraudeValidacaoNecessaria) {
            this.storageService.setForcarOnboard(true);
            return this.router.navigate(['/selecionar-tipo-pessoa'], {
              state: {
                cpf: TextUtil.removeNotDigit(auth.documento)
              }
            });
          }

          if (!logon.isDeviceIdValido) {
            return this.abrirModalTrocaDispositivo(auth);
          }

          this.storageService.setIdLogin(logon.idLogin);
          const usuario: Usuario = this.authService.getUser();

          let rota = this.navigationService.getRouteByRule('inicio');
          if (this.rotaAtalho) {
            const conta = usuario.credenciais[0].contas.find(x => x.tipoProduto == TipoProdutoEnum.SaldoLivre);
            if (conta) {
              rota = '/' + this.rotaAtalho + '/' + conta.idConta;
            }
          }
          return this.router.navigate([rota]);
        }, error: (error: any) => {
          const message = error.message ? error.message : error.msg;
          toast(message || 'Houve um erro inesperado. Tente novamente mais tarde.');
        }
      })
    );
  }

  async setUsarBiometria() {
    const biometria = this.authService.getBiometricActive();
    if (!biometria) {
      return;
    }

    if (this.platform.is('hybrid')) {
      const result = await NativeBiometric.isAvailable();
      if (!result.isAvailable) {
        this.usarBiometria = false;
        return;
      }

      this.isFaceId = result.biometryType == BiometryType.FACE_ID;

      if (biometria.first) {
        this.habilitarBiometria = true;
        return;
      }

      if (!biometria.active) {
        this.habilitarBiometria = false;
        return;
      }

      const credenciais = await this.authService.getBiometricCredentials();
      this.usarBiometria = !!credenciais;
    }
  }

  async performBiometricVerification() {
    const result = await NativeBiometric.isAvailable();
    if (!result.isAvailable) return;
    const verified = await NativeBiometric.verifyIdentity()
      .then(() => true)
      .catch(() => false);
    if (!verified) return;
    await this.entrarComBiometria();
  }

  async entrarComBiometria() {
    const credenciais: any = await this.authService.getBiometricCredentials();
    if (!credenciais) {
      return;
    }
    const usuario = this.authService.getUser();
    const auth: any = {
      documento: credenciais.username,
      senha: credenciais.password
    };
    if (TextUtil.removeNotDigit(usuario.documento).length > 11) {
      auth.documentoAcesso = usuario.documentoAcesso;
    }

    this.authService.setBiometricActive({ active: true, first: false });
    await this.logar(auth);
  }

  usarForm() {
    this.authService.setBiometricActive({ active: false, first: false });
    this.habilitarBiometria = false;
  }

  acessarOutraConta() {
    this.habilitarBiometria = false;
    this.usarBiometria = false;
  }

  acessarPorAtalho(rota: string) {
    this.rotaAtalho = rota;
    return this.performBiometricVerification();
  }

  async abrirModalTrocaDispositivo(auth: any) {
    const modal = await this.modalController.create({
      component: ModalAtencaoComponent,
      componentProps: {
        titulo: 'Identificamos um login em sua conta a partir de outro dispositivo. ',
        mensagem: 'Para garantir a segurança da sua conta, precisamos realizar uma nova validação. Vamos lá?',
        tituloBotaoPrimario: 'Avançar',
        tituloBotaoSecundario: 'Voltar ao início'
      },
      cssClass: 'modal-half-default'
    });
    await modal.present();
    const { data } = await modal.onWillDismiss();
    if (data.role == 'primaria') {
      const fluxoDados: any = {};
      fluxoDados.documento = TextUtil.removeNotDigit(auth.documento);
      fluxoDados.documentoRepresentante = auth.documentoAcesso ? TextUtil.removeNotDigit(auth.documentoAcesso) : '';
      fluxoDados.senha = auth.senha;
      fluxoDados.tipo = 'troca-dispositivo';
      await this.router.navigate(['/selfie'], { state: { fluxoDados: fluxoDados } });
    } else if (data.role == 'secundaria') {
      this.authService.clearToken();
      await this.router.navigate(['/login']);
    }
  }

  loginChange(value: any) {
    const documento = TextUtil.removeNotDigit(value);
    this.formLogin.get('documento')?.setValidators(documento.length === 14 || documento.length > 14 ? [Validators.required, ValidatorsApp.cnpj()] : [Validators.required, ValidatorsApp.cpf()]);
    if (documento.length === 14 || documento.length > 14) {
      this.mensagemErro = 'Informe um CNPJ válido';
    } else if (documento.length === 11) {
      this.formLogin.get('documentoAcesso')?.setValue('');
      this.mensagemErro = 'Informe um CPF válido';
    } else if (documento.length === 0) {
      this.mensagemErro = 'Informe um documento válido';
    }
    this.isRepresentanteLogin = documento.length > 11;
  }

  criarConta() {
    const tipoPessoa = environment.tipoLoginPf != '' && environment.tipoLoginPf != null ? 'pessoa-fisica' : 'pessoa-juridica';
    this.navigationService.navigateByRule('login-criar-conta', '/selecionar-tipo-pessoa', {state: {tipoPessoa}});
  }
}
