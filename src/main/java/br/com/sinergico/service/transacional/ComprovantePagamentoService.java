package br.com.sinergico.service.transacional;

import static br.com.sinergico.util.Constantes.COD_TRANSACAO_COBRAR_COM_QR_CODE;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.gatewaypagto.LogPagtoTituloTransacao;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.gatewaypagto.LogRecargaTransacao;
import br.com.entity.jcard.LogTransacoes;
import br.com.entity.pix.ContaTransacional;
import br.com.entity.pix.ContaTransacionalDevolucao;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.pix.ParticipantesIspb;
import br.com.entity.pix.RepasseInstitucional;
import br.com.entity.transacional.TED;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.pix.request.vo.ContaVO;
import br.com.json.bean.pix.response.vo.InstrucoesDevolucao;
import br.com.json.bean.pix.response.vo.MovimentoInfoVO;
import br.com.json.bean.pix.response.vo.OrigemMovimento;
import br.com.json.bean.transacional.ComprovantePagamentoRequest;
import br.com.sinergico.enums.MotivoTEDEnum;
import br.com.sinergico.enums.StatusTransacao;
import br.com.sinergico.enums.TipoTransacaoEnum;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloTransacaoRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.repository.pix.ContaTransacionalDevolucaoRepository;
import br.com.sinergico.repository.pix.ParticipantesIspbRepository;
import br.com.sinergico.repository.pix.RepasseInstitucionalRepository;
import br.com.sinergico.repository.transacional.TEDRepository;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.jcard.LogTransacoesService;
import br.com.sinergico.service.loyalty.LogRecargaTransacaoService;
import br.com.sinergico.service.pix.ContaTransacionalMovimentoService;
import br.com.sinergico.service.pix.ContaTransacionalService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.ComprovanteMovimentoPixVO;
import br.com.sinergico.vo.ComprovantePagamentoResponseVO;
import br.com.sinergico.vo.LogTransacoesAdditionalDataVO;
import br.com.sinergico.vo.LogTransacoesAdditionalDataWrapperVO;
import br.com.sinergico.vo.TransacaoBoletoResponseVO;
import br.com.sinergico.vo.TransacaoCompraEloResponseVO;
import br.com.sinergico.vo.TransacaoRecargaCelularResponseVO;
import br.com.sinergico.vo.TransacaoTedResponseVO;
import br.com.sinergico.vo.TransacaoTransferenciaResponseVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class ComprovantePagamentoService extends TEDService {

  public static final String TED_EM_PROCESSAMENTO = "TED em Processamento";
  public static final String SEM_COMPROVANTE = "Erro ao recuperar comprovante";
  public static final String TED_COM_SUCESSO = "TED efetuada com sucesso";
  public static final String TED_ESTORNADA = "TED Estornada";
  public static final String PAGAMENTO_EM_PROCESSAMENTO = "Pagamento em Processamento";
  public static final String PAGAMENTO_COM_SUCESSO = "Pagamento efetuado com sucesso";
  public static final String PAGAMENTO_ESTORNADA = "Pagamento Estornada";

  private final Logger logger = LoggerFactory.getLogger(ComprovantePagamentoService.class);

  public ComprovantePagamentoService() {
    super(null);
  }

  @Autowired private TEDRepository tedRepository;
  @Autowired private LogPagtoTituloTransacaoRepository logPagtoTituloTransacaoRepository;
  @Autowired private LogPagtoTituloValidacaoRepository logPagtoTituloValidacaoRepository;
  @Autowired private LogTransacoesService logTransacoesService;
  @Autowired private ContaTransacionalMovimentoService contaTransacionalMovimentoService;
  @Autowired private InstituicaoPixService instituicaoPixService;
  @Autowired private ContaTransacionalService contaTransacionalService;
  @Autowired private ContaTransacionalDevolucaoRepository contaTransacionalDevolucaoRepository;
  @Autowired private ParticipantesIspbRepository participantesIspbRepository;
  @Autowired private LogRecargaTransacaoService logRecargaTransacaoService;
  @Autowired private UtilService utilService;
  @Autowired private RepasseInstitucionalRepository repasseInstitucionalRepository;

  public ComprovantePagamentoResponseVO validarBuscarComprovante(
      ComprovantePagamentoRequest comprovantePagamentoRequest, SecurityUserPortador userPortador) {
    contaPagamentoService.validaIdContaPeloRequestEPortador(
        comprovantePagamentoRequest.getIdConta(), userPortador);
    ContaPagamento contaPagamento =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            comprovantePagamentoRequest.getIdConta(), userPortador);
    return this.buscarComprovante(comprovantePagamentoRequest, contaPagamento);
  }

  public ComprovantePagamentoResponseVO validarBuscarComprovante(
      ComprovantePagamentoRequest comprovantePagamentoRequest,
      SecurityUserCorporativo userCorporativo) {
    ContaPagamento contaPagamento =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
            comprovantePagamentoRequest.getIdConta(), userCorporativo);
    return this.buscarComprovante(comprovantePagamentoRequest, contaPagamento);
  }

  private ComprovantePagamentoResponseVO buscarComprovante(
      ComprovantePagamentoRequest comprovantePagamentoRequest, ContaPagamento contaPagamento) {

    if ((comprovantePagamentoRequest.getRrn() == null
            || comprovantePagamentoRequest.getRrn().isEmpty())
        && comprovantePagamentoRequest.getIdTranlog() == null
        && (comprovantePagamentoRequest.getTransactionId() == null
            || comprovantePagamentoRequest.getTransactionId().isEmpty())) {
      throw new GenericServiceException(
          "ID da Transação não informado", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    if (comprovantePagamentoRequest.getIdConta() == null) {
      throw new GenericServiceException("idConta não informado", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    ComprovantePagamentoResponseVO comprovante = new ComprovantePagamentoResponseVO();

    try {
      if (Objects.equals(
          comprovantePagamentoRequest.getTipoTransacao(), TipoTransacaoEnum.BOLETO.getCodigo())) {
        LogPagtoTituloTransacao logPagtoTituloTransacao =
            logPagtoTituloTransacaoRepository.findPagamentosByIdContaAndTransacao(
                comprovantePagamentoRequest.getIdConta(), comprovantePagamentoRequest.getRrn());
        if (logPagtoTituloTransacao != null) {

          if (logPagtoTituloTransacao.getProtocoloIdRendimento() == null
              && logPagtoTituloTransacao.getProtocoloId() != null) {
            LogPagtoTituloValidacao logPagtoTituloValidacao =
                logPagtoTituloValidacaoRepository.findPagamentoTituloValidacaoByIdLogPag(
                    logPagtoTituloTransacao.getIdLogPagtoTitulo());

            comprovante.setBoleto(new TransacaoBoletoResponseVO());
            comprovante.setStatus(HttpStatus.OK.value());
            // comprovante.setStatusOperacao(StatusTransacao.TRANSFERIDO.getCodigo());
            comprovante.setTituloStatusModal(PAGAMENTO_COM_SUCESSO);
            // comprovante.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());

            String idConta = logPagtoTituloTransacao.getIdConta().toString();
            if (Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(
                contaPagamento.getIdInstituicao())) {
              idConta = "RP3 BANK";
            }

            montarComprovanteBoletoResponse(
                comprovante,
                idConta,
                logPagtoTituloValidacao.getCodigoBarras() != null
                    ? logPagtoTituloValidacao.getCodigoBarras()
                    : logPagtoTituloValidacao.getLinhaDigitavel(),
                logPagtoTituloValidacao.getNomeBeneficiario(),
                logPagtoTituloValidacao.getNomeSacado(),
                logPagtoTituloValidacao.getDataLiquidacao(),
                logPagtoTituloTransacao.getDataHoraInicioPagto(),
                logPagtoTituloTransacao.getProtocoloIdRendimento(),
                logPagtoTituloValidacao.getValor() != null
                    ? Util.currencyFormat(BigDecimal.valueOf(logPagtoTituloValidacao.getValor()))
                    : null,
                logPagtoTituloTransacao.getValor() != null
                    ? Util.currencyFormat(BigDecimal.valueOf(logPagtoTituloTransacao.getValor()))
                    : null,
                logPagtoTituloTransacao.getAutenticacaoAPI());
          } else {
            if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
              comprovante.setTituloStatusModal(PAGAMENTO_EM_PROCESSAMENTO);
              comprovante.setDescricaoStatus(StatusTransacao.AGUARDANDO_APROVACAO.getDescricao());
            } else if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.TRANSFERIDO.getCodigo())) {

              LogPagtoTituloValidacao logPagtoTituloValidacao =
                  logPagtoTituloValidacaoRepository.findPagamentoTituloValidacaoByIdLogPag(
                      logPagtoTituloTransacao.getIdLogPagtoTitulo());

              comprovante.setBoleto(new TransacaoBoletoResponseVO());
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.TRANSFERIDO.getCodigo());
              comprovante.setTituloStatusModal(PAGAMENTO_COM_SUCESSO);
              comprovante.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());

              if (logPagtoTituloTransacao.getProtocoloIdRendimento() != null) {

                String idConta = logPagtoTituloTransacao.getIdConta().toString();
                if (Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(
                    contaPagamento.getIdInstituicao())) {
                  idConta = "RP3 BANK";
                }

                montarComprovanteBoletoResponse(
                    comprovante,
                    idConta,
                    logPagtoTituloValidacao.getCodigoBarras() != null
                        ? logPagtoTituloValidacao.getCodigoBarras()
                        : logPagtoTituloValidacao.getLinhaDigitavel(),
                    logPagtoTituloValidacao.getNomeBeneficiario(),
                    logPagtoTituloValidacao.getNomeSacado(),
                    logPagtoTituloValidacao.getDataLiquidacao(),
                    logPagtoTituloTransacao.getDataHoraInicioPagto(),
                    logPagtoTituloTransacao.getProtocoloIdRendimento(),
                    logPagtoTituloValidacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloValidacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloTransacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getAutenticacaoRendimento());

              } else if (logPagtoTituloTransacao.getProtocoloId() != null) {
                montarComprovanteBoletoResponse(
                    comprovante,
                    logPagtoTituloTransacao.getIdConta().toString(),
                    logPagtoTituloValidacao.getCodigoBarras() != null
                        ? logPagtoTituloValidacao.getCodigoBarras()
                        : logPagtoTituloValidacao.getLinhaDigitavel(),
                    logPagtoTituloValidacao.getNomeBeneficiario(),
                    logPagtoTituloValidacao.getNomeSacado(),
                    logPagtoTituloValidacao.getDataLiquidacao(),
                    logPagtoTituloTransacao.getDataHoraInicioPagto(),
                    logPagtoTituloTransacao.getProtocoloId().toString(),
                    logPagtoTituloValidacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloValidacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloTransacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getAutenticacao().toString());
              }
            } else if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.REPROVADO.getCodigo())) {
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.REPROVADO.getCodigo());
              comprovante.setTituloStatusModal(PAGAMENTO_ESTORNADA);
              comprovante.setDescricaoStatus(StatusTransacao.REPROVADO.getDescricao());
              comprovante.setMotivo(logPagtoTituloTransacao.getTxMotivoRecusRend());
            } else if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.REJEITADO.getCodigo())) {
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.REJEITADO.getCodigo());
              comprovante.setTituloStatusModal(TED_ESTORNADA);
              comprovante.setDescricaoStatus(
                  "Não foi possível realizar a transação. A transação será estornada");
              comprovante.setMotivo(logPagtoTituloTransacao.getTxMotivoRecusRend());
            } else {
              comprovante.setStatus(HttpStatus.NOT_FOUND.value());
              comprovante.setTituloStatusModal(SEM_COMPROVANTE);
              comprovante.setDescricaoStatus(
                  "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
              logger.warn("Pagamento não encontrado para o idTransacao informado");
            }
          }
        }
      } else if (Objects.equals(
          comprovantePagamentoRequest.getTipoTransacao(), TipoTransacaoEnum.TED.getCodigo())) {
        TED ted =
            tedRepository.findTedByIdTransacaoAndIdConta(
                comprovantePagamentoRequest.getRrn(), comprovantePagamentoRequest.getIdConta());
        if (ted != null) {
          if (ted.getStatusTed().equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
            comprovante.setTituloStatusModal(TED_EM_PROCESSAMENTO);
            comprovante.setDescricaoStatus(StatusTransacao.AGUARDANDO_APROVACAO.getDescricao());
          } else if (ted.getStatusTed().equals(StatusTransacao.TRANSFERIDO.getCodigo())) {
            comprovante.setTed(new TransacaoTedResponseVO());
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(ted.getStatusTed());
            comprovante.setTituloStatusModal(TED_COM_SUCESSO);
            comprovante.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());
            if (ted.getIdTransacaoRendimento() != null) {

              montarComprovanteTedResponse(
                  comprovante,
                  ted.getRrn(),
                  DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, ted.getDataHoraInclusao()),
                  ted.getNomeRemetente(),
                  ted.getNomeFavorecido(),
                  ted.getBancoFavorecido(),
                  ted.getAgenciaFavorecido(),
                  ted.getContaFavorecido(),
                  Util.currencyFormat(ted.getValorTransferencia()));

            } else if (ted.getTransactionId() != null) {
              montarComprovanteTedResponse(
                  comprovante,
                  ted.getRrn(),
                  DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, ted.getDataHoraInclusao()),
                  ted.getNomeRemetente(),
                  ted.getNomeFavorecido(),
                  ted.getBancoFavorecido(),
                  ted.getAgenciaFavorecido(),
                  ted.getContaFavorecido(),
                  Util.currencyFormat(ted.getValorTransferencia()));
            }
          } else if (ted.getStatusTed().equals(StatusTransacao.REPROVADO.getCodigo())) {
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(ted.getStatusTed());
            comprovante.setTituloStatusModal(TED_ESTORNADA);
            comprovante.setDescricaoStatus(StatusTransacao.REPROVADO.getDescricao());
            comprovante.setMotivo(
                MotivoTEDEnum.valueOfCodigo(ted.getIdMotivoRecusado()).getDescricao());
          } else if (ted.getStatusTed().equals(StatusTransacao.REJEITADO.getCodigo())) {
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(ted.getStatusTed());
            comprovante.setTituloStatusModal(TED_ESTORNADA);
            comprovante.setDescricaoStatus(
                "Não foi possível realizar a transação. A transação será estornada");
            comprovante.setMotivo(
                MotivoTEDEnum.valueOfCodigo(ted.getIdMotivoRecusado()).getDescricao());
          }
        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
          logger.warn("TED não encontrada para o idTransacao informado");
        }
      } else if (Objects.equals(
          comprovantePagamentoRequest.getTipoTransacao(), TipoTransacaoEnum.PIX.getCodigo())) {

        Long idConta = comprovantePagamentoRequest.getIdConta();

        if (Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(contaPagamento.getIdInstituicao())) {
          RepasseInstitucional repasseInstitucional =
              repasseInstitucionalRepository.findByTransferenciaInternaRrn(
                  comprovantePagamentoRequest.getRrn());
          if (repasseInstitucional != null) {
            comprovantePagamentoRequest.setRrn(repasseInstitucional.getRrnEnvioPix());
            idConta = utilService.getIdContaRepasseRp3();
            contaPagamento = contaPagamentoService.findByIdConta(idConta);
          }
        }

        InstituicaoPix instituicaoPix =
            instituicaoPixService.buscarInstituicaoPixPorProduto(
                contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());

        Long idContaPortador = idConta;
        ContaTransacional contaTransacional =
            contaTransacionalService
                .findByIdConta(idContaPortador)
                .orElseGet(
                    () ->
                        contaTransacionalService.criarContaTransacional(
                            idContaPortador, instituicaoPix));

        ComprovanteMovimentoPixVO comprovanteMovimentoPixVO =
            contaTransacionalMovimentoService.buscarExtratoPorRrn(
                contaTransacional.getConta().getIdConta() + contaTransacional.getDvConta(),
                contaTransacional.getAgencia().toString(),
                comprovantePagamentoRequest.getRrn());

        if (comprovanteMovimentoPixVO != null) {
          comprovante.setPix(new MovimentoInfoVO());
          comprovante.setStatus(HttpStatus.OK.value());
          montarComprovantePixResponse(comprovante, comprovanteMovimentoPixVO);
        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante pix. Favor, entre em contato com o suporte.");
        }
      } else if (Objects.equals(
          comprovantePagamentoRequest.getTipoTransacao(),
          Servicos.TRANSFERENCIA_INTERNA_PORTADOR.getId())) {

        LogTransacoes logTransacoes =
            logTransacoesService.findByRrn(comprovantePagamentoRequest.getRrn());

        if (logTransacoes != null) {
          comprovante.setStatus(HttpStatus.OK.value());

          montarComprovanteTransferenciaResponse(comprovante, logTransacoes);

        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        }

      } else if (Objects.equals(
          comprovantePagamentoRequest.getTipoTransacao(), Servicos.QRCODE_ELO.getId())) {

        LogTransacoes logTransacoes =
            logTransacoesService.findByIdTranlogOrQrCodeTransactionIdIsQrCodeElo(
                comprovantePagamentoRequest.getIdTranlog(),
                comprovantePagamentoRequest.getTransactionId());

        if (logTransacoes != null) {
          comprovante.setStatus(HttpStatus.OK.value());

          comprovante.setCompraElo(new TransacaoCompraEloResponseVO());
          comprovante.getCompraElo().setRrn(comprovantePagamentoRequest.getRrn());

          montarComprovanteCompraCartaoResponse(comprovante, logTransacoes, contaPagamento);

        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        }

      } else if (Objects.equals(
          comprovantePagamentoRequest.getTipoTransacao(), Servicos.RECARGA.getId())) {

        LogRecargaTransacao logRecargaTransacao =
            logRecargaTransacaoService.findByIdTransacaoPagto(comprovantePagamentoRequest.getRrn());

        if (logRecargaTransacao != null) {
          comprovante.setStatus(HttpStatus.OK.value());

          comprovante.setRecargaCelular(new TransacaoRecargaCelularResponseVO());
          comprovante.getRecargaCelular().setRrn(comprovantePagamentoRequest.getRrn());

          montarComprovanteRecargaCelularResponse(comprovante, logRecargaTransacao, contaPagamento);

        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        }
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
      e.printStackTrace();
      throw new GenericServiceException(e.getMessage());
    }
    return comprovante;
  }

  public void montarComprovanteBoletoResponse(
      ComprovantePagamentoResponseVO comprovante,
      String idConta,
      String codigoDeBarras,
      String destinatario,
      String pagador,
      Date dataLiquidacao,
      Date dataOperacao,
      String protocoloId,
      String valorTitulo,
      String valorCobrado,
      String autenticacao) {

    comprovante.getBoleto().setIdConta(idConta);
    comprovante.getBoleto().setPagador(pagador);
    comprovante.getBoleto().setAutenticacao(autenticacao);
    comprovante
        .getBoleto()
        .setDataLiquidacao(
            dataLiquidacao != null
                ? DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, dataLiquidacao)
                : null);
    comprovante
        .getBoleto()
        .setDataOperacao(
            dataOperacao != null
                ? DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, dataOperacao)
                : null);
    comprovante.getBoleto().setCodigoDeBarras(codigoDeBarras);
    comprovante.getBoleto().setValorTitulo(valorTitulo);
    comprovante.getBoleto().setValorCobrado(valorCobrado);
    comprovante.getBoleto().setDestinatario(destinatario);
    comprovante.getBoleto().setProtocoloId(protocoloId);
  }

  public void montarComprovanteTedResponse(
      ComprovantePagamentoResponseVO comprovante,
      String rrn,
      String dataTransacao,
      String remetente,
      String destinatario,
      String banco,
      String agencia,
      String conta,
      String valor) {

    comprovante.getTed().setRrn(rrn);
    comprovante.getTed().setDataTransacao(dataTransacao);
    comprovante.getTed().setDestinatario(destinatario == null ? remetente : destinatario);
    comprovante.getTed().setBanco(banco);
    comprovante.getTed().setAgencia(agencia);
    comprovante.getTed().setConta(conta);
    comprovante.getTed().setValor(valor);
  }

  public void montarComprovantePixResponse(
      ComprovantePagamentoResponseVO comprovante,
      ComprovanteMovimentoPixVO comprovanteMovimentoPixVO) {

    comprovante.getPix().setInstrucoesDevolucao(new InstrucoesDevolucao());
    comprovante.getPix().setOrigemMovimento(new OrigemMovimento());
    comprovante.getPix().setConta(new ContaVO());
    comprovante.getPix().setOrigem(comprovanteMovimentoPixVO.getOrigemMovimento());
    comprovante.getPix().setEndToEnd(comprovanteMovimentoPixVO.getEndToEnd());
    comprovante.getPix().setEndToEndOriginal(comprovanteMovimentoPixVO.getEndToEndOriginal());
    comprovante.getPix().setReferenciaInterna(comprovanteMovimentoPixVO.getReferenciaInterna());
    comprovante.getPix().setData(comprovanteMovimentoPixVO.getDtHrInclusao());
    comprovante.getPix().setValor(comprovanteMovimentoPixVO.getValor());
    comprovante.getPix().setNumeroEbank(comprovanteMovimentoPixVO.getIdIdempotenteEbank());
    comprovante
        .getPix()
        .setNatureza(comprovanteMovimentoPixVO.getTipoMovimento().toString().substring(0, 1));
    comprovante.getPix().setDescricao(comprovanteMovimentoPixVO.getCodOperacao());
    comprovante.getPix().setCampoLivre(comprovanteMovimentoPixVO.getCampoLivre());
    comprovante.getPix().setDevolvido(comprovanteMovimentoPixVO.getCodigoMotivoDevolucao() != null);

    if (comprovante.getPix().getNatureza().equals("C")) {
      List<ContaTransacionalDevolucao> contaTransacionalDevolucao =
          contaTransacionalDevolucaoRepository.findByEndToEndRecebimentoAndEstornado(
              comprovanteMovimentoPixVO.getEndToEnd(), false);
      BigDecimal valorDevolucaoTotal = new BigDecimal(0);

      for (ContaTransacionalDevolucao devolucao : contaTransacionalDevolucao) {
        valorDevolucaoTotal = valorDevolucaoTotal.add(devolucao.getValor());
      }
      if (valorDevolucaoTotal.compareTo(comprovanteMovimentoPixVO.getValor()) == 0
          || comprovante.getPix().getOrigem().equalsIgnoreCase("DEVOLUCAO")) {
        comprovante.getPix().getInstrucoesDevolucao().setPermiteDevolucao(false);
        comprovante
            .getPix()
            .getInstrucoesDevolucao()
            .setValorPermitido(comprovanteMovimentoPixVO.getValor().subtract(valorDevolucaoTotal));
      } else {
        comprovante.getPix().getInstrucoesDevolucao().setPermiteDevolucao(true);
        comprovante
            .getPix()
            .getInstrucoesDevolucao()
            .setValorPermitido(comprovanteMovimentoPixVO.getValor().subtract(valorDevolucaoTotal));
      }
    } else {
      comprovante.getPix().getInstrucoesDevolucao().setPermiteDevolucao(false);
      comprovante
          .getPix()
          .getInstrucoesDevolucao()
          .setValorPermitido(comprovanteMovimentoPixVO.getValor());
    }

    ParticipantesIspb participantesIspbBeneficiario =
        participantesIspbRepository.findByCodIspb(
            Long.parseLong(comprovanteMovimentoPixVO.getCodIspbContraParte()));
    comprovante
        .getPix()
        .getOrigemMovimento()
        .setNomeInstituicao(
            participantesIspbBeneficiario != null
                ? participantesIspbBeneficiario.getRazaoSocial()
                : "");
    comprovante
        .getPix()
        .getOrigemMovimento()
        .setAgencia(comprovanteMovimentoPixVO.getCodAgenciaContraParte());
    comprovante
        .getPix()
        .getOrigemMovimento()
        .setInscricaoNacional(comprovanteMovimentoPixVO.getDocumentoContraParte());
    comprovante
        .getPix()
        .getOrigemMovimento()
        .setIspbInstituicao(comprovanteMovimentoPixVO.getCodIspbContraParte());
    comprovante
        .getPix()
        .getOrigemMovimento()
        .setNome(comprovanteMovimentoPixVO.getNomeContraParte());
    comprovante
        .getPix()
        .getOrigemMovimento()
        .setNumero(comprovanteMovimentoPixVO.getNroContaContraParte());

    ParticipantesIspb participantesIspbConta =
        participantesIspbRepository.findByCodIspb(
            Long.parseLong(comprovanteMovimentoPixVO.getCodInstituicao()));
    comprovante
        .getPix()
        .getConta()
        .setNomeInstituicao(
            participantesIspbConta != null ? participantesIspbConta.getRazaoSocial() : "");
    comprovante.getPix().getConta().setAgencia(comprovanteMovimentoPixVO.getCodAgencia());
    comprovante.getPix().getConta().setInscricaoNacional(comprovanteMovimentoPixVO.getDocumento());
    comprovante.getPix().getConta().setNome(comprovanteMovimentoPixVO.getNome());
    comprovante
        .getPix()
        .getConta()
        .setIspbParticipante(comprovanteMovimentoPixVO.getCodInstituicao());
    comprovante.getPix().getConta().setIdConta(comprovanteMovimentoPixVO.getNroConta());
  }

  public void montarComprovanteTransferenciaResponse(
      ComprovantePagamentoResponseVO comprovante, LogTransacoes logTransacoes) {
    ObjectMapper objectMapper = new ObjectMapper();
    LogTransacoesAdditionalDataVO additionalData;
    try {
      LogTransacoesAdditionalDataWrapperVO wrapper =
          objectMapper.readValue(
              logTransacoes.getAdditionaldata(), LogTransacoesAdditionalDataWrapperVO.class);
      additionalData = wrapper.getJsonRequest();
    } catch (IOException e) {
      throw new RuntimeException(e);
    }

    ContaPagamento contaPagamentoOrigem =
        contaPagamentoService.findByAccountCode(additionalData.getAccount());
    ContaPagamento contaPagamentoDestino =
        contaPagamentoService.findByAccountCode(additionalData.getAccountDestination());

    Pessoa pessoaOrigem = pessoaService.findPessoaByIdConta(contaPagamentoOrigem.getIdConta());
    Pessoa pessoaDestino = pessoaService.findPessoaByIdConta(contaPagamentoDestino.getIdConta());

    if (Integer.valueOf(logTransacoes.getFunctionCode()).equals(COD_TRANSACAO_COBRAR_COM_QR_CODE)) {
      comprovante.setCobrancaQRCode(new TransacaoTransferenciaResponseVO());
      comprovante
          .getCobrancaQRCode()
          .setPagador(
              pessoaOrigem.getNomeCompleto() != null
                  ? pessoaOrigem.getNomeCompleto()
                  : pessoaOrigem.getRazaoSocial());
      comprovante.getCobrancaQRCode().setIdContaOrigem(contaPagamentoOrigem.getIdConta());
      comprovante.getCobrancaQRCode().setDocumentoPagador(pessoaOrigem.getDocumento());
      comprovante
          .getCobrancaQRCode()
          .setDestinatario(
              pessoaDestino.getNomeCompleto() != null
                  ? pessoaDestino.getNomeCompleto()
                  : pessoaDestino.getRazaoSocial());
      comprovante.getCobrancaQRCode().setIdContaDestino(contaPagamentoDestino.getIdConta());
      comprovante.getCobrancaQRCode().setDocumentoDestinatario(pessoaDestino.getDocumento());
      comprovante.getCobrancaQRCode().setRrn(additionalData.getRrn());
      comprovante.getCobrancaQRCode().setValor(BigDecimal.valueOf(additionalData.getAmount()));
      comprovante.getCobrancaQRCode().setTipoOperacao(additionalData.getFreeData());
      comprovante
          .getCobrancaQRCode()
          .setDataOperacao(DateUtil.dateToLocalDateTime(logTransacoes.getDate()));
    } else {
      comprovante.setTransferencia(new TransacaoTransferenciaResponseVO());
      comprovante
          .getTransferencia()
          .setPagador(
              pessoaOrigem.getNomeCompleto() != null
                  ? pessoaOrigem.getNomeCompleto()
                  : pessoaOrigem.getRazaoSocial());
      comprovante.getTransferencia().setIdContaOrigem(contaPagamentoOrigem.getIdConta());
      comprovante.getTransferencia().setDocumentoPagador(pessoaOrigem.getDocumento());
      comprovante
          .getTransferencia()
          .setDestinatario(
              pessoaDestino.getNomeCompleto() != null
                  ? pessoaDestino.getNomeCompleto()
                  : pessoaDestino.getRazaoSocial());
      comprovante.getTransferencia().setIdContaDestino(contaPagamentoDestino.getIdConta());
      comprovante.getTransferencia().setDocumentoDestinatario(pessoaDestino.getDocumento());
      comprovante.getTransferencia().setRrn(additionalData.getRrn());
      comprovante.getTransferencia().setValor(BigDecimal.valueOf(additionalData.getAmount()));
      comprovante.getTransferencia().setTipoOperacao(additionalData.getFreeData());
      comprovante
          .getTransferencia()
          .setDataOperacao(DateUtil.dateToLocalDateTime(logTransacoes.getDate()));
    }
  }

  public void montarComprovanteCompraCartaoResponse(
      ComprovantePagamentoResponseVO comprovante,
      LogTransacoes logTransacoes,
      ContaPagamento contaPagamento) {

    Pessoa pessoa = pessoaService.findPessoaByIdConta(contaPagamento.getIdConta());

    comprovante.getCompraElo().setIdConta(contaPagamento.getIdConta());
    comprovante
        .getCompraElo()
        .setPagador(
            pessoa.getNomeCompleto() != null ? pessoa.getNomeCompleto() : pessoa.getRazaoSocial());
    comprovante.getCompraElo().setDocumentoPagador(pessoa.getDocumento());
    comprovante.getCompraElo().setEstabelecimento(logTransacoes.getCaName());
    comprovante.getCompraElo().setCidade(logTransacoes.getCaCity());
    comprovante.getCompraElo().setNumeroAprovacao(logTransacoes.getApprovalNumber());
    comprovante.getCompraElo().setBandeira(logTransacoes.getSs());
    comprovante
        .getCompraElo()
        .setDataOperacao(DateUtil.dateToLocalDateTime(logTransacoes.getDate()));
    comprovante.getCompraElo().setValor(logTransacoes.getAmount());
    comprovante.getCompraElo().setIsQrCodeElo(logTransacoes.getIsTransacaoQrCodeElo());
  }

  public void montarComprovanteRecargaCelularResponse(
      ComprovantePagamentoResponseVO comprovante,
      LogRecargaTransacao logRecargaTransacao,
      ContaPagamento contaPagamento) {

    Pessoa pessoa = pessoaService.findPessoaByIdConta(contaPagamento.getIdConta());

    comprovante.getRecargaCelular().setIdConta(contaPagamento.getIdConta());
    comprovante
        .getRecargaCelular()
        .setPagador(
            pessoa.getNomeCompleto() != null ? pessoa.getNomeCompleto() : pessoa.getRazaoSocial());
    comprovante.getRecargaCelular().setDocumentoPagador(pessoa.getDocumento());
    comprovante
        .getRecargaCelular()
        .setDataOperacao(DateUtil.dateToLocalDateTime(logRecargaTransacao.getDataOperacao()));
    comprovante.getRecargaCelular().setValor(logRecargaTransacao.getValor());
    comprovante.getRecargaCelular().setProtocoloId(logRecargaTransacao.getProtocoloId());
    comprovante.getRecargaCelular().setDdd(logRecargaTransacao.getDdd());
    comprovante.getRecargaCelular().setNumero(logRecargaTransacao.getNumero());
  }
}
