package br.com.sinergico.service.suporte;

import br.com.entity.suporte.CargaAutoParametros;
import br.com.json.bean.adq.ParametrosCargaVO;
import br.com.json.bean.adq.ParametrosVO;
import br.com.sinergico.repository.suporte.CargaAutoParametrosRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CargaAutoParametrosService extends GenericService<CargaAutoParametros, Integer> {

  private CargaAutoParametrosRepository repository;

  @Autowired
  public CargaAutoParametrosService(CargaAutoParametrosRepository cargaAutoParametrosRepository) {
    super(cargaAutoParametrosRepository);
    this.repository = cargaAutoParametrosRepository;
  }

  public CargaAutoParametros encontrarParametroPorNome(String nome) {
    return repository.findByNmParametro(nome);
  }

  public void salvar(CargaAutoParametros cargaAutoParametros) {
    repository.save(cargaAutoParametros);
  }

  public void salvarLista(List<CargaAutoParametros> cargaAutoParametrosList) {
    repository.saveAll(cargaAutoParametrosList);
  }

  public List<CargaAutoParametros> salvarParametros(ParametrosCargaVO model, SecurityUser user) {

    List<CargaAutoParametros> parametros = new ArrayList<>();

    for (ParametrosVO listModel : model.getParametros()) {
      CargaAutoParametros carga = new CargaAutoParametros();
      carga.setNmParametro(listModel.getNmParametro());
      carga.setDcParametro(listModel.getDcParametro());
      carga.setIdTipoParametro(listModel.getIdTipoParametro());
      carga.setIdInstituicaoEspecifica(listModel.getIdInstituicaoEspecifica());
      carga.setDtHrInclusao(LocalDateTime.now());
      parametros.add(carga);
      repository.save(carga);
    }

    return parametros;
  }

  public List<CargaAutoParametros> buscarParametros(Integer idInstituicaoEspecifica) {
    return repository.buscarParametrosCarga(idInstituicaoEspecifica);
  }
}
