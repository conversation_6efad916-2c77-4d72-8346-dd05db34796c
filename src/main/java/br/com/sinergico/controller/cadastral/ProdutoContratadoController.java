package br.com.sinergico.controller.cadastral;

import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.DadosProdutosContratados;
import br.com.json.bean.cadastral.ProdutoInstituicaoResponse;
import br.com.json.bean.suporte.ProdutoContradoCliente;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.ProdutoContratadoMccService;
import br.com.sinergico.service.cadastral.ProdutoContratadoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.HashMap;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/produto-contratado")
public class ProdutoContratadoController extends UtilController {

  @Autowired private ProdutoContratadoService contratadoService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private ProdutoContratadoMccService produtoContratadoMccService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @ApiOperation(
      value = "Serviço responsável por contratar um produto para uma empresa cliente(b2b)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/editar",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, Object>> contratarProduto(
      @RequestBody @Valid ProdutoContratado model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do contrato de produto", result);
    }

    // ProdutoContratado produtoContratado = prepareContratatarProduto(model);
    contratadoService.editarProduto(model);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Produto editado com sucesso. ");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por contratar um produto para uma empresa cliente(b2b)")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/contratar/",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONTRATAR_PRODUTO_B2B"})
  public ResponseEntity<HashMap<String, Object>> contratarProduto(
      @RequestBody @Valid ProdutoContradoCliente model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do contrato de produto", result);
    }

    ProdutoContratado produtoContratado = contratadoService.contratarProduto(model);

    HashMap<String, Object> map = new HashMap<>();
    map.put(
        "msg", "Produto contratado com sucesso. Contrato: " + produtoContratado.getIdContrato());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por cancelar um produto de uma empresa cliente(b2b)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cancelar/",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CANCELAR_PRODUTO_B2B"})
  public ResponseEntity<HashMap<String, Object>> cancelarProdutoContratado(
      @RequestBody @Valid ProdutoContradoCliente model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Validação do cancelamento de contrato de produto", result);
    }
    SecurityUser user = getAuthenticatedUser(request, em);
    ProdutoContratado produtoContratado = prepareCancelarProdutoContratado(model);
    contratadoService.cancelarProdutoContratado(produtoContratado, user);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Produto cancelado com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por cancelar um produto de uma empresa cliente(b2b) por id do contrato")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cancelar/{idContrato}/{idUsuario}",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CANCELAR_PRODUTO_B2B_BY_ID_CONTRATO"})
  public ResponseEntity<HashMap<String, Object>> cancelarProdutoContratadoPorId(
      @PathVariable Long idContrato, @PathVariable Integer idUsuario) {

    contratadoService.cancelarProdutoContratadoPorId(idContrato, idUsuario);

    /**** PARA REMOVER TODOS OS MCC's do CONTRATO : SPEEDWAY TI BSB JUL 2018 - YESUS *****/
    try {
      SecurityUser user = getAuthenticatedUser(request, em);
      ProdutoContratado produtoContratadoBuscado = contratadoService.findById(idContrato);
      produtoContratadoMccService.removerTodosProdutoContratadoMcc(produtoContratadoBuscado, user);
    } catch (Exception e) {
      e.printStackTrace();
    }
    /*************************************************************************************/

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Produto cancelado com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  public ProdutoContratado prepareCancelarProdutoContratado(ProdutoContradoCliente model) {
    ProdutoContratado produtoContratado = new ProdutoContratado();
    BeanUtils.copyProperties(model, produtoContratado);
    return produtoContratado;
  }

  @ApiOperation(
      value = "Buscar os produtos contratados",
      response = ProdutoInstituicaoResponse.class,
      notes = "Lista os produtos instituição contratados.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/usuarioLogado",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public ResponseEntity<List<ProdutoInstituicaoResponse>>
      getAllProdutosPontoDeRelacionamentoByUsuarioLogado() {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    return new ResponseEntity<>(produtoInstituicaoService.findByUsuarioLogado(user), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar os produtos contratados, vigentes ou não.",
      response = ProdutoInstituicaoResponse.class,
      notes = "Lista os produtos instituição contratados.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/usuarioLogado/qualquer-vigencia",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public ResponseEntity<List<ProdutoInstituicaoResponse>>
      getAllProdutosVigentesOuNaoByUsuarioLogado() {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    return new ResponseEntity<>(
        produtoInstituicaoService.findByUsuarioLogadoAndQualquerVigencia(user), HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Buscar os produtos contratados (somente os ativos/vigentes) de uma empresa b2b passando hierarquia por url",
      response = ProdutoContratado.class,
      notes = "Retorna os produtos contratados ativos",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/vigentes/processadora/{idProc}/instituicao/{idInst}/regional/{idReg}/filial/{idFil}/{idPontRel}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public List<ProdutoContratado> getProdutosContratadosAtivos(
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer idReg,
      @PathVariable Integer idFil,
      @PathVariable Integer idPontRel) {

    return contratadoService.getProdutosContratadosAtivos(idProc, idInst, idReg, idFil, idPontRel);
  }

  @ApiOperation(
      value = "Buscar os produtos contratados que emitem própria empresa por Hierarquia",
      response = ProdutoInstituicaoConfiguracao.class,
      notes =
          "Lista os produtos contratados que emitem própria empresa, até ponto de relacionamento.",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/b2b/emite-propria-empresa",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public List<ProdutoInstituicaoResponse> getByHierarquiaAndTipoPessoaPJ() {
    SecurityUser user = getAuthenticatedUser(request, em);
    return produtoInstituicaoService.findByUsuarioLogadoAndTipoPessoaPJ(user);
  }

  @ApiOperation(
      value = "Buscar todos os produtos contratados vigentes na hierarquia da Instituição",
      response = ProdutoInstituicaoConfiguracao.class,
      notes = "Lista os produtos contratados vigentes da Instituição",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/vigentes/instituicao/{idProc}/{idInst}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public List<ProdutoInstituicaoResponse> getByHierarquiaInstituicao(
      @PathVariable Integer idProc, @PathVariable Integer idInst) {

    List<ProdutoInstituicaoResponse> prod =
        produtoInstituicaoService.findByInstituicaoComVigencia(idProc, idInst);

    return prod;
  }

  @ApiOperation(
      value =
          "Buscar todos os produtos Plataforma Convênio contratados vigentes na hierarquia da Instituição",
      response = ProdutoContratado.class,
      notes = "Lista os produtos contratados vigentes da Instituição",
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/vigentes/empresa/{idProc}/{idInst}/{idReg}/{idFil}/{idPontRel}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public List<ProdutoContratado> getByHierarquiaEmpresa(
      @PathVariable Integer idProc,
      @PathVariable Integer idInst,
      @PathVariable Integer idReg,
      @PathVariable Integer idFil,
      @PathVariable Integer idPontRel) {

    List<ProdutoContratado> prods =
        contratadoService.buscarProdutosConvenioByEmpresa(idProc, idInst, idReg, idFil, idPontRel);

    return prods;
  }

  @ApiOperation(
      value = "Buscar o numero seguinte da sequencia de Identificadores Externos",
      notes = "Busca o numero seguinte da sequencia de Identificadores Externos")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/proximo-id-externo", method = RequestMethod.GET)
  // @Secured({"ROLE_BUSCAR_PRODUTOS_CONTRATADOS_B2B"})
  public Integer getProximoIdExternoProdInstituicao() {

    Integer idExterno = contratadoService.getProximoIdExternoProdInstituicao();

    return idExterno;
  }

  @ApiOperation(
      value = "Buscar as informacoes obrigatorias pelo tipo produto conta livre",
      notes = "Buscar as informacoes obrigatorias pelo tipo produto conta livre")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/buscar/{idProcessadora}/{idInstituicao}/{idRegional}/{idFilial}/{idPontoDeRelacionamento}",
      method = RequestMethod.GET)
  public DadosProdutosContratados getBuscarInformacoes(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idRegional,
      @PathVariable Integer idFilial,
      @PathVariable Integer idPontoDeRelacionamento) {

    DadosProdutosContratados dados =
        contratadoService.buscarDadosProdutoContratadoPeloTipoProduto(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
    return dados;
  }

  @ApiOperation(value = "Alterar a filital de faturamento do produto contratado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-filial-faturamento/{idProduto}/{filialFaturamento}",
      method = RequestMethod.PUT)
  @Secured({"ROLE_ALTERAR_FILIAL_FATURAMENTO"})
  public ProdutoContratado alterarLocalFaturamento(
      @PathVariable Long idProduto, @PathVariable Integer filialFaturamento) {

    return contratadoService.alterarFilialFaturamento(idProduto, filialFaturamento);
  }
}
