package br.com.entity.suporte;

// Generated 17/11/2015 23:54:02 by Hibernate Tools 4.3.1.Final

import br.com.sinergico.annotation.NotUpperCase;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Objects;
import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import org.hibernate.annotations.Type;

/** HierarquiaInstituicao generated by hbm2java */
/**
 * <AUTHOR>
 */
@Entity
@Table(name = "hierarquia_instituicao", schema = "suporte")
public class HierarquiaInstituicao
    implements java.io.Serializable, Comparable<HierarquiaInstituicao> {

  private static final long serialVersionUID = 1L;
  private HierarquiaInstituicaoId id;
  private HierarquiaProcessadora hierarquiaProcessadora;
  private Integer idProcessadora;
  private Integer idInstituicao;
  private String descInstituicao;
  private String cnpj;
  private String emailReply;

  @NotUpperCase private String urlLogo;

  private String razaoSocial;

  // endereco
  private String cep;
  private String bairro;
  private String cidade;
  private String uf;
  private String logradouro;
  private String numero;
  private String complemento;

  private String sgInstituicao;

  //	private Set<HierarquiaRegional> hierarquiaRegionals = new HashSet<HierarquiaRegional>(0);

  // conta que sera usada na hora de criar uma conta
  // dentro da funcao debito na instituicao
  private String cardholderDebitAccount;

  // conta que sera usada na hora de criar uma conta
  // dentro da funcao credito na instituicao
  private String cardholderCreditAccount;
  private String feeAccount;
  private String account;
  private String journal;
  private String institutionId;

  // usuario tem ou não permissao para acessar a propria conta
  private Boolean acessoPropriaConta;

  // representa o id do aplicativo de portador no gateway de notificacao
  @NotUpperCase private String appIdGateway;

  private Integer seqConta;

  private Integer seqContaPreEmitida;

  private Integer idUsuarioManutencao;
  private Integer idUsuarioInclusao;
  private Date dtHrInclusao;

  private Boolean emiteNotaFiscal;
  private String InscricaoEstadual;

  private String inscricaoMunicipal;

  private String serieRps;

  private Integer tipoRps;

  private Integer statusRps;

  private Integer issRetido;

  private String idTagRps;

  private BigDecimal aliquotaIss;

  private String itemListaServico;

  private String codigoTributacao;

  private Integer codigoMunicipio;

  private Integer exigibilidadeIss;

  private Integer incentivoFiscal;

  private Integer optanteSimplesNacional;

  private BigDecimal multaBeneficio;

  private BigDecimal jurosBeneficio;

  private Boolean permitePropostaEspecifica;

  @Column(name = "valor_primeira_compra")
  private BigDecimal valorPrimeiraCompra;

  @Column(name = "perc_limite_primeira_compra")
  private BigDecimal percLimitePrimeiraCompra;

  private Long idLayoutPadrao;

  private Boolean permiteEnvioSMS;

  private BigDecimal iofDiario;
  private BigDecimal iofAdicional;

  @Column(name = "canal_email")
  private String canalEmail;

  @Column(name = "canal_sms")
  private String canalSms;

  @NotUpperCase
  @Column(name = "texto_inf_central_atendimento")
  private String textoInfCentralAtendimento;

  @Column(name = "plastico_img_arq_padrao")
  private String plasticoImgArqPadrao;

  @Column(name = "plastico_img_mobile_arq_padrao")
  private String plasticoImgMobileArqPadrao;

  @Column(name = "habilitacao_grupo")
  private Boolean habilitacaoGrupo = Boolean.FALSE;

  @Column(name = "redefinir_senha_caf")
  private Boolean redefinirSenhaCaf = Boolean.FALSE;

  @Column(name = "metodo_seguranca_transacao")
  private Integer metodoSegurancaTransacao = 1;

  @Column(name = "id_status")
  private Integer idStatus = 1;

  @Column(name = "dt_hr_status")
  private LocalDateTime dtHrStatus = LocalDateTime.now();

  @Column(name = "bl_notificacao")
  private Boolean blNotificacao = Boolean.FALSE;

  @Column(name = "tipo_login_customizavel")
  private Boolean tipoLoginCustomizavel = Boolean.FALSE;

  @Column(name = "tipo_produto_customizavel")
  private Boolean tipoProdutoCustomizavel = Boolean.FALSE;

  @Column(name = "validaContaAtiva")
  private Boolean validaContaAtiva = Boolean.FALSE;

  private Boolean permiteProcessamentoLancamentos;

  @Column(name = "envia_dados_totvs")
  private Boolean enviaDadosTotvs = Boolean.FALSE;

  /** construtor padrao */
  public HierarquiaInstituicao() {
    super();
  }

  public HierarquiaInstituicao(HierarquiaInstituicaoId id, String descInstituicao) {
    this.id = id;
    this.idProcessadora = id.getIdProcessadora();
    this.idInstituicao = id.getIdInstituicao();
    this.descInstituicao = descInstituicao;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) return true;
    if (o == null || getClass() != o.getClass()) return false;
    HierarquiaInstituicao that = (HierarquiaInstituicao) o;
    return Objects.equals(id, that.id);
  }

  @Override
  public int hashCode() {
    return Objects.hashCode(id);
  }

  @EmbeddedId
  @AttributeOverrides({
    @AttributeOverride(
        name = "idProcessadora",
        column = @Column(name = "id_processadora", nullable = false, precision = 2, scale = 0)),
    @AttributeOverride(
        name = "idInstituicao",
        column = @Column(name = "id_instituicao", nullable = false, precision = 4, scale = 0))
  })
  public HierarquiaInstituicaoId getId() {
    return this.id;
  }

  public void setId(HierarquiaInstituicaoId id) {
    this.id = id;
  }

  @ManyToOne
  @JoinColumn(name = "id_processadora", nullable = false, insertable = false, updatable = false)
  public HierarquiaProcessadora getHierarquiaProcessadora() {
    return this.hierarquiaProcessadora;
  }

  public void setHierarquiaProcessadora(HierarquiaProcessadora hierarquiaProcessadora) {
    this.hierarquiaProcessadora = hierarquiaProcessadora;
  }

  public String getCanalEmail() {
    return canalEmail;
  }

  public HierarquiaInstituicao setCanalEmail(String canalEmail) {
    this.canalEmail = canalEmail;
    return this;
  }

  @Column(name = "email_reply", length = 40)
  public String getEmailReply() {
    return emailReply;
  }

  public void setEmailReply(String emailReply) {
    this.emailReply = emailReply;
  }

  @Column(name = "DESC_INSTITUICAO", nullable = false, length = 60)
  public String getDescInstituicao() {
    return this.descInstituicao;
  }

  @Column(name = "sg_instituicao", length = 3)
  public String getSgInstituicao() {
    return sgInstituicao;
  }

  public void setSgInstituicao(String sgInstituicao) {
    this.sgInstituicao = sgInstituicao;
  }

  public void setDescInstituicao(String descInstituicao) {
    this.descInstituicao = descInstituicao;
  }

  //	@JsonIgnore
  //	@OneToMany(fetch = FetchType.LAZY, mappedBy = "hierarquiaInstituicao")
  //	public Set<HierarquiaRegional> getHierarquiaRegionals() {
  //		return this.hierarquiaRegionals;
  //	}
  //
  //	public void setHierarquiaRegionals(Set<HierarquiaRegional> hierarquiaRegionals) {
  //		this.hierarquiaRegionals = hierarquiaRegionals;
  //	}

  @Column(name = "id_processadora", insertable = false, updatable = false)
  @JsonIgnore
  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  @Column(name = "id_instituicao", insertable = false, updatable = false)
  @JsonIgnore
  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public BigDecimal getIofDiario() {
    return iofDiario;
  }

  public void setIofDiario(BigDecimal iofDiario) {
    this.iofDiario = iofDiario;
  }

  public BigDecimal getIofAdicional() {
    return iofAdicional;
  }

  public void setIofAdicional(BigDecimal iofAdicional) {
    this.iofAdicional = iofAdicional;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public String getCnpj() {
    return cnpj;
  }

  public void setCnpj(String cnpj) {
    this.cnpj = cnpj;
  }

  public String getCep() {
    return cep;
  }

  public void setCep(String cep) {
    this.cep = cep;
  }

  public String getBairro() {
    return bairro;
  }

  public void setBairro(String bairro) {
    this.bairro = bairro;
  }

  public String getCidade() {
    return cidade;
  }

  public void setCidade(String cidade) {
    this.cidade = cidade;
  }

  public String getUf() {
    return uf;
  }

  public void setUf(String uf) {
    this.uf = uf;
  }

  public String getLogradouro() {
    return logradouro;
  }

  public void setLogradouro(String logradouro) {
    this.logradouro = logradouro;
  }

  @Column(name = "jc_ch_debit_account")
  public String getCardholderDebitAccount() {
    return cardholderDebitAccount;
  }

  public void setCardholderDebitAccount(String cardholderDebitAccount) {
    this.cardholderDebitAccount = cardholderDebitAccount;
  }

  @Column(name = "jc_ch_credit_account")
  public String getCardholderCreditAccount() {
    return cardholderCreditAccount;
  }

  public void setCardholderCreditAccount(String cardholderCreditAccount) {
    this.cardholderCreditAccount = cardholderCreditAccount;
  }

  @Column(name = "jc_fee_account")
  public String getFeeAccount() {
    return feeAccount;
  }

  public void setFeeAccount(String feeAccount) {
    this.feeAccount = feeAccount;
  }

  @Column(name = "jc_account")
  public String getAccount() {
    return account;
  }

  public void setAccount(String account) {
    this.account = account;
  }

  @Column(name = "jc_journal")
  public String getJournal() {
    return journal;
  }

  public void setJournal(String journal) {
    this.journal = journal;
  }

  @Column(name = "jc_institution_id")
  public String getInstitutionId() {
    return institutionId;
  }

  public void setInstitutionId(String institutionId) {
    this.institutionId = institutionId;
  }

  public String getUrlLogo() {
    return urlLogo;
  }

  public void setUrlLogo(String urlLogo) {
    this.urlLogo = urlLogo;
  }

  public String getRazaoSocial() {
    return razaoSocial;
  }

  public void setRazaoSocial(String razaoSocial) {
    this.razaoSocial = razaoSocial;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }

  @Column(name = "acesso_propria_conta")
  @Type(type = "numeric_boolean")
  public Boolean getAcessoPropriaConta() {
    return acessoPropriaConta;
  }

  public void setAcessoPropriaConta(Boolean acessoPropriaConta) {
    this.acessoPropriaConta = acessoPropriaConta;
  }

  @Column(name = "app_id_gateway")
  public String getAppIdGateway() {
    return appIdGateway;
  }

  public void setAppIdGateway(String appIdGateway) {
    this.appIdGateway = appIdGateway;
  }

  @Override
  public int compareTo(HierarquiaInstituicao o) {
    if (this.idInstituicao < o.idInstituicao) {
      return -1;
    }
    if (this.idInstituicao > o.idInstituicao) {
      return 1;
    }
    return 0;
  }

  @Column(
      name = "seq_conta",
      nullable = true,
      precision = 6,
      scale = 0,
      columnDefinition = "numeric(6,0) DEFAULT 0")
  public Integer getSeqConta() {
    return seqConta;
  }

  public void setSeqConta(Integer seqConta) {
    this.seqConta = seqConta;
  }

  @Column(
      name = "seq_conta_pre_emitida",
      nullable = true,
      precision = 6,
      scale = 0,
      columnDefinition = "numeric(6,0) DEFAULT 0")
  public Integer getSeqContaPreEmitida() {
    return seqContaPreEmitida;
  }

  public void setSeqContaPreEmitida(Integer seqContaPreEmitida) {
    this.seqContaPreEmitida = seqContaPreEmitida;
  }

  @Column(name = "id_usuario_manutencao")
  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  @Column(name = "id_usuario_inclusao")
  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "dt_hr_inclusao")
  public Date getDtHrInclusao() {
    return dtHrInclusao;
  }

  public void setDtHrInclusao(Date dtHrInclusao) {
    this.dtHrInclusao = dtHrInclusao;
  }

  @Column(name = "emite_nota_fiscal")
  @Type(type = "numeric_boolean")
  public Boolean getEmiteNotaFiscal() {
    return emiteNotaFiscal;
  }

  public void setEmiteNotaFiscal(Boolean emiteNotaFiscal) {
    this.emiteNotaFiscal = emiteNotaFiscal;
  }

  public String getInscricaoEstadual() {
    return InscricaoEstadual;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    InscricaoEstadual = inscricaoEstadual;
  }

  public String getInscricaoMunicipal() {
    return inscricaoMunicipal;
  }

  public void setInscricaoMunicipal(String inscricaoMunicipal) {
    this.inscricaoMunicipal = inscricaoMunicipal;
  }

  public String getSerieRps() {
    return serieRps;
  }

  public void setSerieRps(String serieRps) {
    this.serieRps = serieRps;
  }

  public Integer getTipoRps() {
    return tipoRps;
  }

  public void setTipoRps(Integer tipoRps) {
    this.tipoRps = tipoRps;
  }

  public Integer getStatusRps() {
    return statusRps;
  }

  public void setStatusRps(Integer statusRps) {
    this.statusRps = statusRps;
  }

  public Integer getIssRetido() {
    return issRetido;
  }

  public void setIssRetido(Integer issRetido) {
    this.issRetido = issRetido;
  }

  public String getIdTagRps() {
    return idTagRps;
  }

  public void setIdTagRps(String idTagRps) {
    this.idTagRps = idTagRps;
  }

  public BigDecimal getAliquotaIss() {
    return aliquotaIss;
  }

  public void setAliquotaIss(BigDecimal aliquotaIss) {
    this.aliquotaIss = aliquotaIss;
  }

  public String getItemListaServico() {
    return itemListaServico;
  }

  public void setItemListaServico(String itemListaServico) {
    this.itemListaServico = itemListaServico;
  }

  public String getCodigoTributacao() {
    return codigoTributacao;
  }

  public void setCodigoTributacao(String codigoTributacao) {
    this.codigoTributacao = codigoTributacao;
  }

  public Integer getCodigoMunicipio() {
    return codigoMunicipio;
  }

  public void setCodigoMunicipio(Integer codigoMunicipio) {
    this.codigoMunicipio = codigoMunicipio;
  }

  public Integer getExigibilidadeIss() {
    return exigibilidadeIss;
  }

  public void setExigibilidadeIss(Integer exigibilidadeIss) {
    this.exigibilidadeIss = exigibilidadeIss;
  }

  public Integer getIncentivoFiscal() {
    return incentivoFiscal;
  }

  public void setIncentivoFiscal(Integer incentivoFiscal) {
    this.incentivoFiscal = incentivoFiscal;
  }

  public Integer getOptanteSimplesNacional() {
    return optanteSimplesNacional;
  }

  public void setOptanteSimplesNacional(Integer optanteSimplesNacional) {
    this.optanteSimplesNacional = optanteSimplesNacional;
  }

  public BigDecimal getMultaBeneficio() {
    return multaBeneficio;
  }

  public void setMultaBeneficio(BigDecimal multaBeneficio) {
    this.multaBeneficio = multaBeneficio;
  }

  public BigDecimal getJurosBeneficio() {
    return jurosBeneficio;
  }

  public void setJurosBeneficio(BigDecimal jurosBeneficio) {
    this.jurosBeneficio = jurosBeneficio;
  }

  public String getNumero() {
    return numero;
  }

  public void setNumero(String numero) {
    this.numero = numero;
  }

  public String getComplemento() {
    return complemento;
  }

  public void setComplemento(String complemento) {
    this.complemento = complemento;
  }

  @Column(name = "permite_proposta_especifica")
  @Type(type = "numeric_boolean")
  public Boolean getPermitePropostaEspecifica() {
    return permitePropostaEspecifica;
  }

  public void setPermitePropostaEspecifica(Boolean permitePropostaEspecifica) {
    this.permitePropostaEspecifica = permitePropostaEspecifica;
  }

  public BigDecimal getValorPrimeiraCompra() {
    return valorPrimeiraCompra;
  }

  public void setValorPrimeiraCompra(BigDecimal valorPrimeiraCompra) {
    this.valorPrimeiraCompra = valorPrimeiraCompra;
  }

  public BigDecimal getPercLimitePrimeiraCompra() {
    return percLimitePrimeiraCompra;
  }

  public void setPercLimitePrimeiraCompra(BigDecimal percLimitePrimeiraCompra) {
    this.percLimitePrimeiraCompra = percLimitePrimeiraCompra;
  }

  @Column(name = "id_layout_padrao")
  public Long getIdLayoutPadrao() {
    return idLayoutPadrao;
  }

  public void setIdLayoutPadrao(Long idLayoutPadrao) {
    this.idLayoutPadrao = idLayoutPadrao;
  }

  @Column(name = "permite_envio_sms")
  @Type(type = "numeric_boolean")
  public Boolean getPermiteEnvioSMS() {
    return permiteEnvioSMS;
  }

  public void setPermiteEnvioSMS(Boolean permiteEnvioSMS) {
    this.permiteEnvioSMS = permiteEnvioSMS;
  }

  public String getTextoInfCentralAtendimento() {
    return textoInfCentralAtendimento;
  }

  public void setTextoInfCentralAtendimento(String textoInfCentralAtendimento) {
    this.textoInfCentralAtendimento = textoInfCentralAtendimento;
  }

  public String getCanalSms() {
    return canalSms;
  }

  public void setCanalSms(String canalSms) {
    this.canalSms = canalSms;
  }

  public String getPlasticoImgArqPadrao() {
    return plasticoImgArqPadrao;
  }

  public void setPlasticoImgArqPadrao(String plasticoImgArqPadrao) {
    this.plasticoImgArqPadrao = plasticoImgArqPadrao;
  }

  public String getPlasticoImgMobileArqPadrao() {
    return plasticoImgMobileArqPadrao;
  }

  public void setPlasticoImgMobileArqPadrao(String plasticoImgMobileArqPadrao) {
    this.plasticoImgMobileArqPadrao = plasticoImgMobileArqPadrao;
  }

  public Boolean getHabilitacaoGrupo() {
    return habilitacaoGrupo;
  }

  public void setHabilitacaoGrupo(Boolean habilitacaoGrupo) {
    this.habilitacaoGrupo = habilitacaoGrupo;
  }

  public Boolean getRedefinirSenhaCaf() {
    return redefinirSenhaCaf;
  }

  public void setRedefinirSenhaCaf(Boolean redefinirSenhaCaf) {
    this.redefinirSenhaCaf = redefinirSenhaCaf;
  }

  public Integer getMetodoSegurancaTransacao() {
    return metodoSegurancaTransacao;
  }

  public void setMetodoSegurancaTransacao(Integer metodoSegurancaTransacao) {
    this.metodoSegurancaTransacao = metodoSegurancaTransacao;
  }

  public Integer getIdStatus() {
    return idStatus;
  }

  public void setIdStatus(Integer idStatus) {
    this.idStatus = idStatus;
  }

  public LocalDateTime getDtHrStatus() {
    return dtHrStatus;
  }

  public void setDtHrStatus(LocalDateTime dtHrStatus) {
    this.dtHrStatus = dtHrStatus;
  }

  public Boolean getTipoLoginCustomizavel() {
    return tipoLoginCustomizavel;
  }

  public void setTipoLoginCustomizavel(Boolean tipoLoginCustomizavel) {
    this.tipoLoginCustomizavel = tipoLoginCustomizavel;
  }

  public Boolean getBlNotificacao() {
    return blNotificacao;
  }

  public void setBlNotificacao(Boolean blNotificacao) {
    this.blNotificacao = blNotificacao;
  }

  public Boolean getTipoProdutoCustomizavel() {
    return tipoProdutoCustomizavel;
  }

  public void setTipoProdutoCustomizavel(Boolean tipoProdutoCustomizavel) {
    this.tipoProdutoCustomizavel = tipoProdutoCustomizavel;
  }

  public Boolean getValidaContaAtiva() {
    return validaContaAtiva;
  }

  public void setValidaContaAtiva(Boolean validaContaAtiva) {
    this.validaContaAtiva = validaContaAtiva;
  }

  @Column(name = "permite_processamento_lancamentos")
  @Type(type = "numeric_boolean")
  public Boolean getPermiteProcessamentoLancamentos() {
    return permiteProcessamentoLancamentos;
  }

  public void setPermiteProcessamentoLancamentos(Boolean permiteProcessamentoLancamentos) {
    this.permiteProcessamentoLancamentos = permiteProcessamentoLancamentos;
  }

  public Boolean getEnviaDadosTotvs() {
    return enviaDadosTotvs;
  }

  public void setEnviaDadosTotvs(Boolean enviaDadosTotvs) {
    this.enviaDadosTotvs = enviaDadosTotvs;
  }
}
