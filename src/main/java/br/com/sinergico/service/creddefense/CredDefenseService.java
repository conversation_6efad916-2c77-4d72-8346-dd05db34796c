package br.com.sinergico.service.creddefense;

import br.com.itspay.creddefense.api.CredDefenseItspayApi;
import br.com.itspay.creddefense.api.model.ResponseSolicitacaoAnaliseAPI;
import br.com.itspay.creddefense.api.model.ResponseTrocarEstadoSolicitacaoAPI;
import br.com.itspay.creddefense.restclient.enums.EnumDocument;
import br.com.itspay.creddefense.restclient.enums.EnumStatusCredit;
import br.com.itspay.creddefense.restclient.enums.EnumTypeEvaluation;
import br.com.itspay.creddefense.restclient.exception.LoginCredDefenseException;
import br.com.itspay.creddefense.restclient.exception.SolicitacaoAnaliseCredDefenseException;
import br.com.itspay.creddefense.restclient.model.ChangeCredit;
import br.com.itspay.creddefense.restclient.model.RequisicaoAnalise;
import br.com.itspay.creddefense.restclient.model.SurveyResponse;
import br.com.sinergico.service.UtilService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CredDefenseService {

  @Autowired private UtilService utilService;

  public static CredDefenseItspayApi credDefenseItspayApi;

  public void openConnection() {

    credDefenseItspayApi = new CredDefenseItspayApi("BAHAMASAPI", "Bahamas_7card");
    if (utilService.isAmbienteHomologacao()) {
      credDefenseItspayApi
          .config()
          .setUrlNovaSolicitacao("https://test.creddefense.com/index.php/api/v4/creditRequest/")
          .setUrlLogin("https://test.creddefense.com/index.php/api/v4/login/")
          .setUrlAlterarStatus("https://test.creddefense.com/index.php/api/v4/changestatus/")
          .setUrlPesquisa("https://test.creddefense.com/index.php/api/v4/survey/")
          .setPlaceId(97)
          .setLoginAPI("BAHAMASAPI")
          .setPasswordAPI("Bahamas_7card");
    } else if (utilService.isAmbienteProducao()) {
      credDefenseItspayApi
          .config()
          .setUrlNovaSolicitacao("https://www.creddefense.com/index.php/api/v4/creditRequest/")
          .setUrlLogin("https://www.creddefense.com/index.php/api/v4/login/")
          .setUrlAlterarStatus("https://www.creddefense.com/index.php/api/v4/changestatus/")
          .setUrlPesquisa("https://www.creddefense.com/index.php/api/v4/survey/")
          .setPlaceId(14648)
          .setLoginAPI("BAHAMASAPI")
          .setPasswordAPI("Bahamasc_76api");
    }
  }

  private SurveyResponse requisitaPesquisa() {
    System.out.println("************ SURVEY ******************");

    try {
      return credDefenseItspayApi.pesquisa("282.056.006-78");
    } catch (LoginCredDefenseException e) {
      e.printStackTrace();
      return null;
    }
  }

  public ResponseTrocarEstadoSolicitacaoAPI testarAlteracaoStatusDaAnalise(
      ResponseSolicitacaoAnaliseAPI responseSolicitacaoAnalise) {
    System.out.println("************ CHANGE STATUS ******************");
    ChangeCredit changeCredit = new ChangeCredit();
    changeCredit.setComment("SOLICITAÇÃO RESTRITA");
    changeCredit.setStatus(EnumStatusCredit.RESTRITO);
    changeCredit.setId(responseSolicitacaoAnalise.getIdCreditRequest());
    try {
      ResponseTrocarEstadoSolicitacaoAPI responseTrocarEstadoSolicitacaoAPI =
          credDefenseItspayApi.trocarEstadoSolicitacao(changeCredit);
      System.out.println(responseTrocarEstadoSolicitacaoAPI);
      return responseTrocarEstadoSolicitacaoAPI;
    } catch (LoginCredDefenseException e) {
      e.printStackTrace();
    }

    return null;
  }

  public ResponseSolicitacaoAnaliseAPI solicitacaoAnaliseCadastral(
      RequisicaoAnalise requisicaoAnalise) {

    openConnection();

    requisicaoAnalise.setDocumento(EnumDocument.CPF);
    requisicaoAnalise.setTipoAvaliacao(EnumTypeEvaluation.MEDIA);

    try {
      ResponseSolicitacaoAnaliseAPI responseSolicitacaoAnalise =
          credDefenseItspayApi.novaSolicitacaoAnalise(requisicaoAnalise);
      return responseSolicitacaoAnalise;
    } catch (LoginCredDefenseException e) {
      e.printStackTrace();
    } catch (SolicitacaoAnaliseCredDefenseException e) {
      e.printStackTrace();
    }
    return null;
  }
}
