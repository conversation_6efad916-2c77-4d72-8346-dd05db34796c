package br.com.json.bean.cadastral;

import br.com.sinergico.enums.PadraoSeqCredencial;
import br.com.sinergico.enums.SegundaViaBoletoEnum;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DadosProdutoInstituicao implements Serializable {

  private static final long serialVersionUID = -2574907824488247066L;

  private Boolean b2b;

  private Integer binLength;

  @Min(100000)
  @Max(999999)
  private Integer bin;

  @Min(100000L)
  @Max(value = 9999999999L, message = "BIN com a sua extensão deve ter no máximo 10 caracteres")
  private Long binEstendido;

  private String descProdInstituicao;
  private Integer idArranjo;
  private Integer idProcessadora;
  private Integer idInstituicao;
  private Integer idProdInstituicao;
  private Integer idMoeda;
  private Integer idProdutoInstituidor;
  private Integer idProdPlat;
  private Integer idRelacionamento;
  private Integer mesesValidadeFisico;
  private PadraoSeqCredencial padrao;
  private Integer tamanhoPin;
  private Integer idInstituidor;

  private String descInstituicao;
  private String descProdPlataforma;
  private String tipoConta;
  private String descArranjoInstituidor;
  private String descProdutoInstituidor;
  private String descMoeda;
  private String descArranjo;
  private Integer tipoPessoa;
  private Integer idadeMinimaPortador;
  private Integer idadeMinimaAdicional;
  private Integer serviceCode;
  private Boolean chip;
  private Integer tipoEmissao;
  private Boolean virtual;
  private Integer primeiroCartaoVirtual;
  private Boolean emitePropriaEmpresa;
  private Boolean suportaContaBase;
  private Integer qtdContasDocumentoProduto;
  private Double valorCargaMin;
  private Double valorCargaMax;
  private Boolean permitePreEmissao;
  private Boolean boletoRegistrado;

  private Integer moeda;
  private String simboloMoeda;

  private List<Integer> vencimentoFaturaList;
  private Integer permiteRestricaoMcc;
  private Integer tipoRestricaoMcc;
  private Integer qtdDiasCorte;
  private BigDecimal txJuroChequeEspecial;

  private BigDecimal percentualLimiteAdicional;
  private Boolean diferenciacaoLimiteAdicional;

  private Integer idRegional;
  private String descRegional;
  private Integer idFilial;
  private String descFilial;
  private Integer idPontoDeRelacionamento;
  private String descPontoDeRelacionamento;
  private TipoPortadorLoginEnum tipoLogin;
  private String tipoLoginTela;
  private Integer habilitaTodos;

  private TipoProdutoVo tipoProdutoVo;

  private SegundaViaBoletoEnum segundaViaBoleto;

  private List<HistoricoArranjo> historicoArranjo;

  private Boolean redefinirSenhaCaf;
  private Integer metodoSegurancaTransacao;
  private Long idGrupoProduto;
  private Boolean blCorporativo;

  private Integer idStatus;

  @AssertTrue(message = "binLength permite os valor 6, 8 ou 10.")
  private boolean isBinLengthValid() {
    return binLength == null || binLength == 6 || binLength == 8 || binLength == 10;
  }

  // Validacoes estao rodando multiplas vezes devido a versao antiga do Spring. Unico impacto:
  // performance
  @AssertTrue(
      message = "BIN Estendido é menor do que o permitido para o tamanho de BIN selecionado.")
  private boolean isBinEstendidoSizeLesserThanBinLength() {
    return binEstendido == null
        || binLength == null
        || binEstendido.toString().length() >= binLength;
  }
}
