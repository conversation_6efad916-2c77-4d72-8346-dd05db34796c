package br.com.sinergico.service.transacional;

import static br.com.sinergico.util.Constantes.*;

import br.com.client.rest.jcard.json.bean.CreateAccountManualEntrie;
import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponseRrn;
import br.com.client.rest.jcard.json.bean.Reverse;
import br.com.client.rest.jcard.json.bean.TransferAccountManualEntrie;
import br.com.entity.adq.EstabelecimentoUnidade;
import br.com.entity.cadastral.BonificacaoConfiguracao;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.ParceiroAcumulo;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PlanoSaudeProdutoContratado;
import br.com.entity.cadastral.PlanoSaudeProdutoVigencia;
import br.com.entity.cadastral.PreLancamentoVoucherPapel;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.jcard.LogTransacoes;
import br.com.entity.transacional.CodigoTransacao;
import br.com.entity.transacional.LancamentoAuto;
import br.com.entity.transacional.LancamentoManual;
import br.com.entity.transacional.PontoControle;
import br.com.entity.transacional.PontosRecebidos;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.JcardServiceException;
import br.com.itspay.acquirer.iso.client.TipoTransacao;
import br.com.itspay.acquirer.iso.client.TransacaoAcquirer;
import br.com.itspay.acquirer.iso.client.Utils;
import br.com.json.bean.cadastral.BonificacaoResponse;
import br.com.json.bean.cadastral.GetPerfisTarifarios;
import br.com.json.bean.cadastral.ParceiroAcumuloResponse;
import br.com.json.bean.jcard.TransacaoDePontoResponse;
import br.com.json.bean.transacional.AjustePontoRequestVO;
import br.com.json.bean.transacional.BuscaLancamentoManual;
import br.com.json.bean.transacional.CadastroLancamentoManual;
import br.com.json.bean.transacional.GetLancamentoManual;
import br.com.json.bean.transacional.LancamentoEstorno;
import br.com.json.bean.transacional.ManejoPontosLoyaltyVO;
import br.com.json.bean.transacional.PontoBonificadoVO;
import br.com.json.bean.transacional.PontoControleDetalhesVO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.ClasseTransacaoEnum;
import br.com.sinergico.enums.StatusContratoPlanoSaudeEnum;
import br.com.sinergico.enums.TransacoesPagamentoPlanoSaudeEnum;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.repository.transacional.LancamentoAutoRepository;
import br.com.sinergico.repository.transacional.LancamentoManualRepository;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.adq.EstabelecimentoUnidadeService;
import br.com.sinergico.service.cadastral.BonificacaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.ParceiroAcumuloService;
import br.com.sinergico.service.cadastral.PerfilTarifarioService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PlanoSaudeContratadoService;
import br.com.sinergico.service.cadastral.PlanoSaudeProdutoVigenciaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.jcard.AccountManualEntrieService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.jcard.LogTransacoesService;
import br.com.sinergico.service.jcard.TransactionService;
import br.com.sinergico.service.loyalty.ResgateContaBancariaService;
import br.com.sinergico.service.suporte.AcquirerClientService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.UtilManejoPontos;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class LancamentoService extends GenericService<LancamentoManual, Integer> {

  private static final int CODIGO_TRANSACAO_BONIFICACAO = 682;

  private static final String TEXTO_EXTRATO_DEBITO_PLANO_SAUDE = "DEBITO PLANO DE SAUDE";
  private static final String TEXTO_EXTRATO_EST_DEBITO_PLANO_SAUDE =
      "ESTORNO DEBITO PLANO DE SAUDE";
  private static final String TEXTO_EXTRATO_PAGAMENTO_PLANO_SAUDE = "PAGAMENTO PLANO DE SAUDE";
  private static final String TEXTO_EXTRATO_EST_PAGAMENTO_PLANO_SAUDE =
      "ESTORNO PAGAMENTO PLANO DE SAUDE";

  private static final int COMPRA = 1;

  private static final Integer COD_TRANSACAO_COMPRA_TELEVENDA = 992;

  public static final String DD_MM_YYYY = "dd/MM/yyyy";

  public static final String YYYY_MM_DD = "yyyy-MM-dd";

  public static final Integer TIPO_PONTO_AJUTES = 3;

  private static final String FORMAT_QUATRO_DIGITOS = "0000";

  private static final int NIVEL_PROCESSADORA = 1;

  private static final int DEBITO = -1;

  private static final int CREDITO = 1;

  private static final int PRODUTO_CREDITO = 9;

  private static Logger log = LoggerFactory.getLogger(LancamentoService.class);

  private LancamentoManualRepository repository;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private ParceiroAcumuloService parceiroAcumuloService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private AccountManualEntrieService accountManualEntrieService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfService;

  @Autowired private RrnLogService rrnLogService;

  @Autowired private EstabelecimentoUnidadeService estabelecimentoUnidadeService;

  @Autowired private LogTransacoesService logTransacoesService;

  //    @Autowired
  //    private CredencialFacade credencialService;

  @Autowired private CardService cardService;

  @Autowired private AcquirerClientService acquirerClientService;

  @Autowired private UtilManejoPontos utilManejoPontos;

  @Autowired private PontoControleService pontoControleService;

  @Autowired private PontosRecebidosService pontosRecebidosService;

  @Autowired private BonificacaoConfiguracaoService bonificacaoConfiguracaoService;

  @Autowired private LancamentoAutoRepository lancamentoAutoRepository;

  @Autowired private TransactionService transactionService;

  @Autowired private CredencialFacade credencialFacade;

  @Autowired private ResgateContaBancariaService resgateContaBancariaService;

  @Autowired private CodigoTransacaoService codigoTransacaoService;

  @Autowired private PlanoSaudeContratadoService planoSaudeContratadoService;

  @Autowired private PlanoSaudeProdutoVigenciaService planoSaudeProdutoVigenciaService;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private EventoService eventoService;

  @Autowired private EmailService emailService;

  @Autowired
  public LancamentoService(LancamentoManualRepository repository) {
    super(repository);
    this.repository = repository;
  }

  public List<GetLancamentoManual> getLancamentosByConta(
      Long idConta, SecurityUser user, Integer first, Integer max) {

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(idConta);

    if (conta == null) {
      throw new GenericServiceException("Não foi possível localizar a conta informada!");
    }

    if (!getPermissionAccess(user, conta)) {
      throw new GenericServiceException(
          "O usuário logado não tem acesso aos lançamentos manuais da conta: " + idConta);
    }

    consultaRestritaService.checaPrivilegio(conta, user);

    Pageable top = PageRequest.of(first, max);

    List<GetLancamentoManual> lancamentos =
        repository.findLancamentosByIdConta(idConta, top).getContent();

    return lancamentos;
  }

  private boolean getPermissionAccess(SecurityUser user, ContaPagamento conta) {
    if ((user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)
            && user.getIdProcessadora().equals(conta.getIdProcessadora()))
        || (!user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)
            && user.getIdProcessadora().equals(conta.getIdProcessadora())
            && user.getIdInstituicao().equals(conta.getIdInstituicao()))) {
      return true;
    }
    return false;
  }

  public Long countLancamentosByConta(Long idConta, SecurityUser user) {
    consultaRestritaService.checaPrivilegio(idConta, user);
    return repository.countByIdConta(idConta);
  }

  @Transactional
  public JcardResponse saveLancamento(
      CadastroLancamentoManual model,
      Integer idUsuario,
      String tid,
      Boolean origemPortador,
      Boolean disparoManual) {

    ContaPagamento conta = getContaById(model.getIdConta());

    Credencial credencial =
        credencialFacade.buscarCredencialParaLancamentoManual(conta.getIdConta());

    if (credencial == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar uma credencial para efetuar o lançamento.");
    }

    if (!conta.getTipoStatusV2().getTipoGrupoStatus().getAceitarAjuste()) {
      throw new GenericServiceException(
          "A conta ou cartão não está apta para realizar o lançamento manual",
          HttpStatus.UNAUTHORIZED);
    }

    String rrn;
    if (!origemPortador) {
      rrn = saveLancamentoManual(model, idUsuario, conta, disparoManual);
    } else {
      rrn = getRrn(Constantes.PREFIXO_RRN_PORTADOR);
    }

    String idParceiroResgate = defineIdParceiroResgate(model, conta);

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    Map<String, Boolean> mapResult =
        defineManejoLoyalty(model, idUsuario, conta, produto, rrn, utilManejoPontos);

    JcardResponse jcardResponse = new JcardResponse();

    if ((mapResult.get("loyalty").equals(Boolean.TRUE)
            && mapResult.get("sucesso").equals(Boolean.TRUE))
        || mapResult.get("loyalty").equals(Boolean.FALSE)) {

      String accountCode = contaPagamentoService.getOrPrepareAccountCode(conta, produto);
      Integer stan = getStan();
      jcardResponse =
          doLancamentoManual(
              accountCode,
              model.getCodTransacao(),
              model.getValor(),
              conta.getIdProdutoPlataforma().equals(PRODUTO_CREDITO)
                      && (COD_TRANSACAO_LANCAMENTO_PLANO_SAUDE
                              + ","
                              + COD_TRANSACAO_EST_LANCAMENTO_PLANO_SAUDE)
                          .contains(model.getCodTransacao().toString())
                  ? LAYER_PAGAMENTO_PLANO_SAUDE // Transacao lancamento plano de saude. Layer 368
                  : produto.getMoeda().getIdMoeda().toString(), // Transacao normal. Layer 986
              rrn,
              model.getSinal(),
              credencial.getTokenInterno(),
              model.getIdConta(),
              model.getTextoExtrato(),
              idParceiroResgate,
              tid,
              null,
              stan,
              null);
      if (!jcardResponse.getSuccess()) {
        throw new GenericServiceException("Não foi possível efetuar o lançamento no autorizador.");
      }
      jcardResponse.setRrn(rrn);

      // Soh eh relevante para transacoes presentes em TransacoesPagamentoPlanoSaudeEnum em contas
      // com plano de saude
      fluxoPagamentoManualPlanoSaude(model, tid, conta, accountCode, credencial, rrn);

      Pessoa pessoa = pessoaService.findPessoaTitularConta(conta.getIdConta());
      eventoService.publicarMovimentacaoFinanceiraEvent(
          Servicos.LANCAMENTO.getDescricao(),
          conta.getIdInstituicao(),
          null,
          conta.getIdConta(),
          null,
          model.getCodTransacao(),
          null,
          model.getValor(),
          pessoa != null ? pessoa.getDocumento() : "0");

    } else if (mapResult.get("loyalty").equals(Boolean.TRUE)
        && mapResult.get("sucesso").equals(Boolean.FALSE)) {
      throw new GenericServiceException("Não foi possível efetuar o lançamento manual.");
    }

    return jcardResponse;
  }

  public static Map<String, Boolean> defineManejoLoyalty(
      CadastroLancamentoManual model,
      Integer idUsuario,
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao produto,
      String rrn,
      UtilManejoPontos utilManejoPontos) {

    ManejoPontosLoyaltyVO manejoLoyalty =
        new ManejoPontosLoyaltyVO(
            rrn,
            idUsuario,
            model.getValor().longValue(),
            model.getIdPontoControle() != null ? model.getIdPontoControle() : null,
            model.getSinal(),
            model.getCodTransacao(),
            produto,
            conta,
            model.getIdBonificacaoLoyalty() != null ? model.getIdBonificacaoLoyalty() : null);

    return utilManejoPontos.identificaTransacaoDeLoyalty(manejoLoyalty);
  }

  // para salvar a transação com o valor do parceiro resgate correto, esse metodo é generico por
  // isso a melhor opção
  // é setar o valor do parceiro resgate de acordo com a instituicao e o codigo da transação
  // RF20210617
  private static String defineIdParceiroResgate(
      CadastroLancamentoManual model, ContaPagamento conta) {

    String idParceiroResgate = "";
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(conta.getIdInstituicao())) {
      if (model.getCodTransacao() != null && model.getCodTransacao() > 0) {

        switch (model.getCodTransacao()) {
          case Constantes.COD_TRANSACAO_RESGATE_RECARGA_CELULAR:
          case Constantes.COD_TRANSACAO_RECARGA_CELULAR:
            idParceiroResgate = Constantes.TIPO_RESGATE_RECARGA_CELULAR_CELCOIN.toString();

            break;

          case Constantes.COD_TRANSACAO_RESGATE_GIFFY_VOUCHER:
            idParceiroResgate = Constantes.ID_PARCEIRO_RESGATE_INMAIS_VOUCHER.toString();
            break;

          default:
            idParceiroResgate = null;
            break;
        }
      }
    }
    return idParceiroResgate;
  }

  public void estornaTransacao(LancamentoEstorno lancamentoEstorno, SecurityUser user) {

    LogTransacoes transacao = logTransacoesService.findById(lancamentoEstorno.getIdTranlog());
    if (transacao == null) {
      throw new GenericServiceException("Transação não encontrada!");
    }

    Credencial credencial =
        getCredencialService().findOneByTokenInterno(transacao.getTokenInterno());
    if (credencial == null) {
      throw new GenericServiceException("Credencial não encontrada!");
    }

    ContaPagamento conta = contaPagamentoService.findByIdAcct(transacao.getAccount());
    if (conta == null) {
      throw new GenericServiceException("Conta não encontrada!");
    }
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, conta);

    CodigoTransacao codigo =
        codigoTransacaoService.findById(Integer.valueOf(transacao.getFunctionCode()));
    if (codigo == null) {
      throw new GenericServiceException("Código da transação não encontrado!");
    }
    if (codigo
        .getClasseTransacao()
        .getClasseTransacao()
        .equals(ClasseTransacaoEnum.ESTORNO.getClasseTransacao())) {
      throw new GenericServiceException("Não é permitido estornar um estorno!");
    }

    if (lancamentoEstorno.getValorTransacao().compareTo(transacao.getAmount()) != 0) {
      throw new GenericServiceException("Houve um erro na solicitação! Cod.001");
    }

    if (!lancamentoEstorno.getIdConta().equals(conta.getIdConta())) {
      throw new GenericServiceException("Houve um erro na solicitação! Cod.002");
    }

    if (Arrays.asList(
            Constantes.COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_DEBITO,
            Constantes.COD_TRANSACAO_TRANSFER_B2B_DEBITO,
            Constantes.COD_TRANSACAO_TRANSFER_CONTA_VIA_WEB_MOB_CREDITO,
            Constantes.COD_TRANSACAO_TRANSFER_B2B_CREDITO,
            Constantes.COD_CUSTO_MANUTENCAO_CONTA,
            Constantes.COD_TRANSACAO_LANCAMENTO_PLANO_SAUDE,
            Constantes.COD_TRANSACAO_EST_LANCAMENTO_PLANO_SAUDE,
            Constantes.COD_TRANSACAO_DEBITO_PLANO_SAUDE,
            Constantes.COD_TRANSACAO_EST_DEBITO_PLANO_SAUDE,
            Constantes.COD_TRANSACAO_PAGAMENTO_PLANO_SAUDE,
            Constantes.COD_TRANSACAO_EST_PAGAMENTO_PLANO_SAUDE)
        .contains(codigo.getCodTransacao())) {
      throw new GenericServiceException("Este código de transação não pode ser estornado...");
    }

    JcardResponseRrn jcardResponseRrn =
        doLancamentoEstornoById(
            conta.getIdConta(),
            transacao.getId(),
            codigo.getCodTranEstorno(),
            Constantes.CODIGO_MOEDA_PADRAO,
            transacao.getAmount());

    LancamentoManual lancamento = new LancamentoManual();

    lancamento.setCodTransacao(codigo.getCodTranEstorno());
    lancamento.setIdConta(conta.getIdConta());
    lancamento.setValor(lancamentoEstorno.getValorTransacao());
    lancamento.setTextoExtrato(lancamentoEstorno.getTextoExtrato());
    lancamento.setTextoComentario(lancamentoEstorno.getTextoComentario());
    lancamento.setDataHoraLancamento(LocalDateTime.now());
    lancamento.setIdProcessadora(conta.getIdProcessadora());
    lancamento.setIdInstituicao(conta.getIdInstituicao());
    lancamento.setIdUsuario(user.getIdUsuario());
    lancamento.setRrn(jcardResponseRrn.getRrn());
    lancamento.setRrnEstornado(transacao.getRrn());
    saveAndFlush(lancamento);

    Pessoa pessoa = pessoaService.findPessoaTitularConta(conta.getIdConta());
    eventoService.publicarMovimentacaoFinanceiraEvent(
        Servicos.LANCAMENTO.getDescricao() + "_Estorno",
        conta.getIdInstituicao(),
        null,
        conta.getIdConta(),
        null,
        null,
        codigo.getCodTranEstorno(),
        lancamentoEstorno.getValorTransacao(),
        pessoa != null ? pessoa.getDocumento() : "0");
  }

  // Lança uma transação (debito plano de saude) na layer 986 (layer classica de reais) e outra
  // transação oposta
  // (pagamento plano de saude) na layer 986 (layer que não consta no saldo) quando é inserido um
  // pagamento de fatura ou análogo.
  // Esse comportamento sensibiliza no saldo a transação (lançamento plano saude)
  public void fluxoPagamentoManualPlanoSaude(
      CadastroLancamentoManual model,
      String tid,
      ContaPagamento conta,
      String accountCode,
      Credencial credencial,
      String rrn) {
    // Trata apenas de produtos credito
    if (conta.getIdProdutoPlataforma().equals(PRODUTO_CREDITO)) {
      // Se é uma transacao de pagamento de fatura ou análoga, inicia-se o fluxo de verificação de
      // plano de saude
      if (TransacoesPagamentoPlanoSaudeEnum.containsCodTransacao(model.getCodTransacao())) {

        List<PlanoSaudeProdutoContratado> planoSaudeProdutoContratado =
            planoSaudeContratadoService.findByContaPagamento(conta);

        if (planoSaudeProdutoContratado != null && !planoSaudeProdutoContratado.isEmpty()) {

          if (!TransacoesPagamentoPlanoSaudeEnum.isTransacaoEstorno(model.getCodTransacao())) {

            List<PlanoSaudeProdutoVigencia> vigenciaPendentesDePagamento =
                planoSaudeProdutoContratado.stream()
                    .flatMap(
                        plano ->
                            plano.getPlanoSaudeProdutoVigenciaList().stream()
                                .filter(vig -> vig.getDataPagamento() == null))
                    .sorted(Comparator.comparing(PlanoSaudeProdutoVigencia::getDtInicio))
                    .collect(Collectors.toList());

            if (vigenciaPendentesDePagamento != null && !vigenciaPendentesDePagamento.isEmpty()) {

              BigDecimal valorRestante = new BigDecimal(model.getValor().toString());
              List<PlanoSaudeProdutoVigencia> vigenciasPagasAgora = new ArrayList<>();

              for (PlanoSaudeProdutoVigencia vigenciaPendente : vigenciaPendentesDePagamento) {

                BigDecimal valorContratoPlanoSaude = vigenciaPendente.getValor();

                if (valorRestante.compareTo(valorContratoPlanoSaude) >= 0) {

                  Integer stanDebitoPlanoSaude = getStan();
                  String rrnDebitoPlanoSaude = getRrn(Constantes.PREFIXO_RRN);
                  JcardResponse jcardResponseDebitoPlanoSaude =
                      doLancamentoManual(
                          accountCode,
                          COD_TRANSACAO_DEBITO_PLANO_SAUDE,
                          valorContratoPlanoSaude,
                          LAYER_DEBITO_PLANO_SAUDE,
                          rrnDebitoPlanoSaude,
                          DEBITO,
                          credencial.getTokenInterno(),
                          model.getIdConta(),
                          TEXTO_EXTRATO_DEBITO_PLANO_SAUDE,
                          null,
                          tid,
                          null,
                          stanDebitoPlanoSaude,
                          null);
                  jcardResponseDebitoPlanoSaude.setRrn(rrnDebitoPlanoSaude);

                  if (jcardResponseDebitoPlanoSaude.getSuccess()) {

                    Integer stanPagamentoPlanoSaude = getStan();
                    String rrnPagamentoPlanoSaude = getRrn(Constantes.PREFIXO_RRN);
                    JcardResponse jcardPagamentoTransacaoPlanoSaude =
                        doLancamentoManual(
                            accountCode,
                            COD_TRANSACAO_PAGAMENTO_PLANO_SAUDE,
                            valorContratoPlanoSaude,
                            LAYER_PAGAMENTO_PLANO_SAUDE,
                            rrnPagamentoPlanoSaude,
                            CREDITO,
                            credencial.getTokenInterno(),
                            model.getIdConta(),
                            TEXTO_EXTRATO_PAGAMENTO_PLANO_SAUDE,
                            null,
                            tid,
                            null,
                            stanPagamentoPlanoSaude,
                            null);

                    jcardPagamentoTransacaoPlanoSaude.setRrn(rrnPagamentoPlanoSaude);

                    if (jcardPagamentoTransacaoPlanoSaude.getSuccess()) {

                      vigenciaPendente.setDataPagamento(new Date());
                      vigenciaPendente.setRrnPagamento(rrn);
                      vigenciasPagasAgora.add(vigenciaPendente);
                      valorRestante = valorRestante.subtract(valorContratoPlanoSaude);

                    } else {
                      throw new GenericServiceException(
                          "Não foi possível efetuar um lançamento de pagamento de plano de saude no autorizador.");
                    }
                  } else {
                    throw new GenericServiceException(
                        "Não foi possível efetuar um lançamento de debito de plano de saude no autorizador.");
                  }
                }
              }
              if (!vigenciasPagasAgora.isEmpty()) {
                planoSaudeProdutoVigenciaService.save(vigenciasPagasAgora);
              }
            }
          } else if (TransacoesPagamentoPlanoSaudeEnum.isTransacaoEstorno(
              model.getCodTransacao())) {
            List<PlanoSaudeProdutoVigencia> vigenciaPagasAntesDoEstorno =
                planoSaudeProdutoContratado.stream()
                    .flatMap(
                        plano ->
                            plano.getPlanoSaudeProdutoVigenciaList().stream()
                                .filter(vig -> vig.getDataPagamento() != null))
                    .sorted(
                        Comparator.comparing(
                            PlanoSaudeProdutoVigencia::getDtInicio, Comparator.reverseOrder()))
                    .collect(Collectors.toList());

            if (vigenciaPagasAntesDoEstorno != null && !vigenciaPagasAntesDoEstorno.isEmpty()) {

              BigDecimal valorRestante = new BigDecimal(model.getValor().toString());
              List<PlanoSaudeProdutoVigencia> vigenciasDesfeitasAgora = new ArrayList<>();

              for (PlanoSaudeProdutoVigencia vigenciaADesfazer : vigenciaPagasAntesDoEstorno) {

                BigDecimal valorContratoPlanoSaude = vigenciaADesfazer.getValor();

                if (valorRestante.compareTo(valorContratoPlanoSaude) >= 0) {

                  Integer stanEstornoPlanoSaude = getStan();
                  String rrnEstornoDebitoPlanoSaude = getRrn(Constantes.PREFIXO_RRN);
                  JcardResponse jcardResponseDebitoPlanoSaude =
                      doLancamentoManual(
                          accountCode,
                          COD_TRANSACAO_EST_DEBITO_PLANO_SAUDE,
                          valorContratoPlanoSaude,
                          LAYER_DEBITO_PLANO_SAUDE,
                          rrnEstornoDebitoPlanoSaude,
                          CREDITO,
                          credencial.getTokenInterno(),
                          model.getIdConta(),
                          TEXTO_EXTRATO_EST_DEBITO_PLANO_SAUDE,
                          null,
                          tid,
                          null,
                          stanEstornoPlanoSaude,
                          null);
                  jcardResponseDebitoPlanoSaude.setRrn(rrnEstornoDebitoPlanoSaude);

                  if (jcardResponseDebitoPlanoSaude.getSuccess()) {

                    Integer stanEstornoPagamentoPlanoSaude = getStan();
                    String rrnEstornoPagamentoPlanoSaude = getRrn(Constantes.PREFIXO_RRN);
                    JcardResponse jcardPagamentoTransacaoPlanoSaude =
                        doLancamentoManual(
                            accountCode,
                            COD_TRANSACAO_EST_PAGAMENTO_PLANO_SAUDE,
                            valorContratoPlanoSaude,
                            LAYER_PAGAMENTO_PLANO_SAUDE,
                            rrnEstornoPagamentoPlanoSaude,
                            DEBITO,
                            credencial.getTokenInterno(),
                            model.getIdConta(),
                            TEXTO_EXTRATO_EST_PAGAMENTO_PLANO_SAUDE,
                            null,
                            tid,
                            null,
                            stanEstornoPagamentoPlanoSaude,
                            null);
                    jcardPagamentoTransacaoPlanoSaude.setRrn(rrnEstornoPagamentoPlanoSaude);
                    if (jcardPagamentoTransacaoPlanoSaude.getSuccess()) {
                      vigenciaADesfazer.setDataPagamento(null);
                      vigenciaADesfazer.setRrnPagamento(null);
                      vigenciasDesfeitasAgora.add(vigenciaADesfazer);
                      valorRestante = valorRestante.subtract(valorContratoPlanoSaude);
                    } else {
                      throw new GenericServiceException(
                          "Não foi possível efetuar um lançamento de estorno pagamento de plano de saude no autorizador.");
                    }
                  } else {
                    throw new GenericServiceException(
                        "Não foi possível efetuar um lançamento de estorno debito de plano de saude no autorizador.");
                  }
                }
              }
              if (vigenciasDesfeitasAgora != null && !vigenciasDesfeitasAgora.isEmpty()) {

                planoSaudeProdutoVigenciaService.save(vigenciasDesfeitasAgora);

                // Desfazer status de balanceamento do contrato
                List<PlanoSaudeProdutoContratado> planosCanceladosADesbalancear =
                    vigenciasDesfeitasAgora.stream()
                        .filter(
                            v ->
                                v.getPlanoSaudeProdutoContratado()
                                    .getStatus()
                                    .equals(StatusContratoPlanoSaudeEnum.CANCELADO_E_BALANCEADO))
                        .map(PlanoSaudeProdutoVigencia::getPlanoSaudeProdutoContratado)
                        .collect(Collectors.toList());

                planoSaudeContratadoService.save(planosCanceladosADesbalancear);
              }
            }
          }
        }
      }
    }
  }

  public String saveLancamentoManual(
      CadastroLancamentoManual model,
      Integer idUsuario,
      ContaPagamento conta,
      Boolean disparoManual) {
    String rrn;
    rrn = getRrn(Constantes.PREFIXO_RRN);
    LancamentoManual lancamento = new LancamentoManual();
    BeanUtils.copyProperties(model, lancamento);
    lancamento.setDataHoraLancamento(LocalDateTime.now());
    lancamento.setIdProcessadora(conta.getIdProcessadora());
    lancamento.setIdInstituicao(conta.getIdInstituicao());
    lancamento.setIdUsuario(idUsuario);
    lancamento.setRrn(rrn);
    lancamento.setDisparoManual(disparoManual);
    lancamento = saveAndFlush(lancamento);
    log.info(
        "Lancamento manual realizado. Data "
            + lancamento.getDataHoraLancamento()
            + " - RRN = "
            + lancamento.getRrn());
    return rrn;
  }

  public JcardResponseRrn doLancamentoEstorno(
      Long idConta, String rrn, Integer codTransacao, String moeda, BigDecimal valor) {

    JcardResponseRrn response = transactionService.estonarTransacao(rrn);

    checaJcardExcecao(response);

    rrnLogService.salvarLog(
        idConta,
        codTransacao,
        response.getRrn(),
        response.getSuccess(),
        response.getErrors(),
        null,
        null,
        moeda,
        valor);

    return response;
  }

  public JcardResponseRrn doLancamentoEstornoById(
      Long idConta, Long idTranlog, Integer codTransacao, String moeda, BigDecimal valor) {

    JcardResponseRrn response = transactionService.estonarTransacaoById(idTranlog);

    checaJcardExcecao(response);

    rrnLogService.salvarLog(
        idConta,
        codTransacao,
        response.getRrn(),
        response.getSuccess(),
        response.getErrors(),
        null,
        null,
        moeda,
        valor);

    return response;
  }

  public JcardResponse doLancamentoManual(
      String accountCode,
      Integer codTransacao,
      BigDecimal valor,
      String idMoeda,
      String rrn,
      Integer sinal,
      String tokenInterno,
      Long idConta,
      String freedata,
      String mid,
      String tid,
      String acquirer,
      Integer stan,
      String locality) {

    // salvar no JCARD
    JcardResponse response =
        createAccountManualEntrie(
            accountCode,
            codTransacao,
            valor,
            idMoeda,
            rrn,
            sinal,
            tokenInterno,
            idConta,
            freedata,
            mid,
            tid,
            acquirer,
            stan,
            locality);

    // salvar log com resposta JCARD
    rrnLogService.salvarLog(
        idConta,
        codTransacao,
        rrn,
        response.getSuccess(),
        response.getErrors(),
        accountCode,
        tokenInterno,
        idMoeda,
        valor);

    return checaJcardExcecao(response);
  }

  // Lancamento manual incluindo string json com informações Loyalty no campo additionaldata
  public JcardResponse doLancamentoManualLoyalty(
      String accountCode,
      Integer codTransacao,
      BigDecimal valor,
      String idMoeda,
      String rrn,
      Integer sinal,
      String tokenInterno,
      Long idConta,
      String freedata,
      String mid,
      String tid,
      String acquirer,
      Integer stan,
      String locality,
      String loyalty) {

    // salvar no JCARD
    JcardResponse response =
        createAccountManualEntrieLoyalty(
            accountCode,
            codTransacao,
            valor,
            idMoeda,
            rrn,
            sinal,
            tokenInterno,
            idConta,
            freedata,
            mid,
            tid,
            acquirer,
            stan,
            locality,
            loyalty);

    // salvar log com resposta JCARD
    rrnLogService.salvarLog(
        idConta,
        codTransacao,
        rrn,
        response.getSuccess(),
        response.getErrors(),
        accountCode,
        tokenInterno,
        idMoeda,
        valor);

    return checaJcardExcecao(response);
  }

  public JcardResponse doTransferenciaB2b(
      String accountCode,
      Integer codTransacao,
      BigDecimal valor,
      String idMoeda,
      String rrn,
      Integer sinal,
      String tokenInterno,
      Long idConta,
      String freedata,
      String mid,
      String tid,
      String acquirer,
      Integer stan,
      String locality) {

    return doTransferenciaSimples(
        accountCode,
        codTransacao,
        valor,
        idMoeda,
        rrn,
        sinal,
        tokenInterno,
        idConta,
        freedata,
        mid,
        tid,
        acquirer,
        stan,
        locality);
  }

  public JcardResponse doTransferenciaSimples(
      String accountCode,
      Integer codTransacao,
      BigDecimal valor,
      String idMoeda,
      String rrn,
      Integer sinal,
      String tokenInterno,
      Long idConta,
      String freedata,
      String mid,
      String tid,
      String acquirer,
      Integer stan,
      String locality) {

    // salvar no JCARD
    return createAccountManualEntrie(
        accountCode,
        codTransacao,
        valor,
        idMoeda,
        rrn,
        sinal,
        tokenInterno,
        idConta,
        freedata,
        mid,
        tid,
        acquirer,
        stan,
        locality);
  }

  @Transactional
  public JcardResponse saveTransferencia(
      ContaPagamento contaOrigem,
      ContaPagamento contaDestino,
      Credencial credencialOrigem,
      Credencial credencialDestino,
      BigDecimal valorTransfer,
      String tokenJWT,
      String pinCredOrigem,
      Integer codigoTransacao,
      Boolean validadoComTokenOuMetodoDeSeguranca) {

    ProdutoInstituicaoConfiguracao produtoDestino =
        getProduto(
            contaDestino.getIdProcessadora(),
            contaDestino.getIdInstituicao(),
            contaDestino.getIdProdutoInstituicao());
    String rrn = getRrn(Constantes.PREFIXO_RRN_PORTADOR);
    TransferAccountManualEntrie transferAccountManualEntrie = null;
    if (codigoTransacao != null) {
      transferAccountManualEntrie = new TransferAccountManualEntrie(codigoTransacao);
    } else {
      transferAccountManualEntrie = new TransferAccountManualEntrie();
    }
    transferAccountManualEntrie.setAccount(
        contaPagamentoService.getOrPrepareAccountCode(
            contaOrigem,
            getProduto(
                contaOrigem.getIdProcessadora(),
                contaOrigem.getIdInstituicao(),
                contaOrigem.getIdProdutoInstituicao())));
    transferAccountManualEntrie.setAccountdestination(
        contaPagamentoService.getOrPrepareAccountCode(contaDestino, produtoDestino));
    transferAccountManualEntrie.setRrn(rrn);
    transferAccountManualEntrie.setCurrency(produtoDestino.getMoeda().getIdMoeda().toString());
    transferAccountManualEntrie.setExternalaccount(contaOrigem.getIdContaPagamento().toString());
    transferAccountManualEntrie.setExternalaccountdestination(
        contaDestino.getIdContaPagamento().toString());
    transferAccountManualEntrie.setAmount(valorTransfer);

    if (pinCredOrigem != null && tokenJWT != null && !validadoComTokenOuMetodoDeSeguranca) {
      transferAccountManualEntrie.setSalt(tokenJWT);
      transferAccountManualEntrie.setHashpin(pinCredOrigem.toUpperCase());
    } else {
      transferAccountManualEntrie.setIgnorehashpin(true);
    }

    JcardResponse response =
        accountManualEntrieService.transferAccountManualEntrie(
            transferAccountManualEntrie,
            credencialOrigem.getTokenInterno(),
            credencialDestino.getTokenInterno());
    response.setRrn(rrn);

    rrnLogService.salvarLog(
        contaOrigem.getIdConta(),
        contaDestino.getIdConta(),
        transferAccountManualEntrie.getFunctioncode(),
        rrn,
        response.getSuccess(),
        response.getErrors());

    if (Constantes.CODIGO_MOEDA_DE_PONTO.equals(produtoDestino.getMoeda().getIdMoeda())) {

      CodigoTransacao codigoDeTransacao = codigoTransacaoService.findById(codigoTransacao);

      try {

        List<PontoControle> listSaldoPontosControle =
            utilManejoPontos.buscaPontosControleComSaldo(contaOrigem.getIdConta());

        BigDecimal tarifa = resgateContaBancariaService.buscarTarifa(contaOrigem, codigoTransacao);

        utilManejoPontos.atualizarResgateEmPontoControle(
            rrn,
            tarifa.longValue(),
            valorTransfer.longValue(),
            listSaldoPontosControle,
            codigoTransacao.longValue());

        Pessoa pessoaDestino = pessoaService.findById(credencialDestino.getIdPessoa());

        ParceiroAcumulo parceiroAcumulo = new ParceiroAcumulo();

        List<ParceiroAcumuloResponse> parceirosAcumulo =
            parceiroAcumuloService.findListParceiroAcumuloByHierarquia(
                contaDestino.getIdProcessadora(),
                contaDestino.getIdInstituicao(),
                contaDestino.getIdRegional(),
                contaDestino.getIdFilial());

        if (parceirosAcumulo.size() > 0) {
          ParceiroAcumuloResponse parceiro = parceirosAcumulo.get(0);
          parceiroAcumulo = parceiroAcumuloService.findById(parceiro.getIdParceiro());
        }

        PontosRecebidos pontosRecebido =
            pontosRecebidosService.saveLancamentoPontosTransferidos(
                null, parceiroAcumulo, pessoaDestino, valorTransfer);
        preencherPontoControleTransferencia(
            contaDestino, valorTransfer, rrn, parceiroAcumulo, pontosRecebido);

      } catch (Exception e) {
        doLancamentoEstorno(
            contaDestino.getIdConta(),
            rrn,
            codigoDeTransacao.getCodTranEstorno(),
            Constantes.CODIGO_MOEDA_PONTO,
            valorTransfer);
        throw new GenericServiceException("Erro ao realizar transferência de pontos.");
      }
    }

    return response;
  }

  private PontoControle preencherPontoControleTransferencia(
      ContaPagamento contaDestino,
      BigDecimal valorTransfer,
      String rrn,
      ParceiroAcumulo parceiroAcumulo,
      PontosRecebidos pontosRecebido) {
    PontoControle pontoControle = new PontoControle();

    pontoControle.setIdJobInclusao(Long.valueOf(-9999));
    pontoControle.setDataHoraJobInclusao(new Date());
    pontoControle.setIdUsuarioInclusao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR.longValue());
    pontoControle.setIdPontoRecebido(pontosRecebido.getIdPontosRecebidos());
    pontoControle.setDataExpiracao(DateUtil.parseDate("yyyy-MM-dd", Constantes.PONTOS_NAO_EXPIRA));
    pontoControle.setTipoPonto(Constantes.TIPO_PONTO_TRANSFERENCIA);
    pontoControle.setQtdPontos(Long.valueOf(valorTransfer.longValue()));
    pontoControle.setIdProcessadora(parceiroAcumulo.getIdProcessadora().longValue());
    pontoControle.setIdInstituicao(parceiroAcumulo.getIdInstituicao().longValue());
    pontoControle.setIdConta(contaDestino.getIdConta());
    pontoControle.setQtdPontosUtilizados(Long.valueOf(0));
    pontoControle.setQtdPontosExpirados(Long.valueOf(0));
    pontoControle.setVlrVendaPonto(parceiroAcumulo.getValorVendaPontos().doubleValue());
    pontoControle.setIdParcAcumulo(parceiroAcumulo.getIdParceiroAcumulo().longValue());
    pontoControle.setRrn(rrn);
    pontoControle.setDocumento(pontosRecebido.getDocumento());
    pontoControle.setQtdPntsEstornoAcumulo(Long.valueOf(0));
    pontoControle.setQtdPntsEstornoExpirado(Long.valueOf(0));
    pontoControle.setQtdPntsEstornoUtilizado(Long.valueOf(0));
    pontoControle.setQtdPntsAjusteCredito(Long.valueOf(0));
    pontoControle.setQtdPntsAjusteDebito(Long.valueOf(0));

    return pontoControleService.saveAndFlush(pontoControle);
  }

  @Transactional
  public JcardResponse saveTransferenciaViaWebMob(
      ContaPagamento contaOrigem,
      ContaPagamento contaDestino,
      Credencial credencialOrigem,
      Credencial credencialDestino,
      BigDecimal valorTransf) {
    return saveTransferencia(
        contaOrigem,
        contaDestino,
        credencialOrigem,
        credencialDestino,
        valorTransf,
        null,
        null,
        null,
        false);
  }

  private JcardResponse createAccountManualEntrie(
      String account,
      Integer functioncode,
      BigDecimal amount,
      String currency,
      String rrn,
      Integer sinal,
      String tokenInterno,
      Long idConta,
      String freedata,
      String mid,
      String tid,
      String acquirer,
      Integer stan,
      String locality) {

    CreateAccountManualEntrie create =
        AccountManualEntrieService.prepareCreateAccountManualEntrie(
            account,
            functioncode,
            amount,
            currency,
            rrn,
            freedata,
            mid,
            tid,
            acquirer,
            stan,
            locality,
            null);

    JcardResponse response = new JcardResponse();

    System.out.println("Lancamento manual transação: " + rrn);

    if (sinal == CREDITO) {
      response = accountManualEntrieService.createCreditManualEntrie(create, tokenInterno);
    } else if (sinal == DEBITO) {
      response = accountManualEntrieService.createDebitManualEntrie(create, tokenInterno);
    }

    if (!response.getSuccess()) {
      doReversal(rrn);
      System.out.println("Fim da reversão");
    }

    return response;
  }

  public JcardResponse doReversal(String rrn) {
    System.out.println("Iniciando a reversão");
    Reverse reverse = new Reverse();
    reverse.setIdJob("");
    reverse.setUsuarioExecutor("");
    return accountManualEntrieService.createReverseManualEntrie(reverse, rrn);
  }

  // equivalente ao createAccountManualEntrie, mas incluindo atributo com string json com
  // informacoes Loyalty no campo additionaldata
  private JcardResponse createAccountManualEntrieLoyalty(
      String account,
      Integer functioncode,
      BigDecimal amount,
      String currency,
      String rrn,
      Integer sinal,
      String tokenInterno,
      Long idConta,
      String freedata,
      String mid,
      String tid,
      String acquirer,
      Integer stan,
      String locality,
      String loyalty) {

    CreateAccountManualEntrie create =
        AccountManualEntrieService.prepareCreateAccountManualEntrieLoyalty(
            account,
            functioncode,
            amount,
            currency,
            rrn,
            freedata,
            mid,
            tid,
            acquirer,
            stan,
            locality,
            null,
            loyalty);

    JcardResponse response = new JcardResponse();

    if (sinal == CREDITO) {
      response = accountManualEntrieService.createCreditManualEntrie(create, tokenInterno);
    } else if (sinal == DEBITO) {
      response = accountManualEntrieService.createDebitManualEntrie(create, tokenInterno);
    }

    if (!response.getSuccess()) {

      Reverse reverse = new Reverse();
      reverse.setIdJob("");
      reverse.setUsuarioExecutor("");

      accountManualEntrieService.createReverseManualEntrie(reverse, rrn);
    }

    return response;
  }

  public JcardResponse checaJcardExcecao(JcardResponse response) {
    // resultado JCARD
    if (!response.getSuccess()) {
      throw new JcardServiceException(
          response.getErrors() == null ? "Erro ao realizar operação" : response.getErrors());
    }
    return response;
  }

  public String getRrn(String prefixo) {
    return prefixo + repository.nextValSeqRrn().toString();
  }

  public Integer getStan() {
    return repository.nextValSeqStan();
  }

  public ContaPagamento getContaById(Long idConta) {
    ContaPagamento conta = contaPagamentoService.findContaTitular(idConta);

    if (conta == null) {
      throw new GenericServiceException("Não foi possível localizar a conta informada!");
    }
    return conta;
  }

  private Pessoa getPessoaDaCredencial(Long idPessoa) {
    Pessoa pessoa = pessoaService.findOneByIdPessoa(idPessoa);

    if (pessoa == null) {
      throw new GenericServiceException("Não foi possível localizar a pessoa informada!");
    }
    return pessoa;
  }

  public ProdutoInstituicaoConfiguracao getProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    ProdutoInstituicaoConfiguracao produto =
        prodInstConfService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            idProcessadora, idProdInstituicao, idInstituicao);
    if (produto == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar as configurações do produto da conta!");
    }
    return produto;
  }

  public List<GetLancamentoManual> getLancamentosByFilter(
      BuscaLancamentoManual model, SecurityUser user, Integer first, Integer max) {
    return (List<GetLancamentoManual>)
        repository.getOrCountLancamentosByFilter(model, user, first, max, List.class);
  }

  public Long countLancamentosByFilter(BuscaLancamentoManual model, SecurityUser user) {
    return repository.getOrCountLancamentosByFilter(model, user, null, null, Long.class);
  }

  @Transactional
  public ResponseEntity<HashMap<String, Object>> doLancManualEmpresaAndSendEmailEstab(
      Long idCredencial,
      BigDecimal valor,
      Integer idFilialEstabelecimento,
      String tid,
      SecurityUser user,
      Boolean origemPortador,
      String senha,
      String token)
      throws Exception {

    ResponseEntity<HashMap<String, Object>> mapAndStatus;
    HashMap<String, Object> map = new HashMap<>();

    EstabelecimentoUnidade unidade =
        estabelecimentoUnidadeService.findOneByIdFilialEstabelecimento(idFilialEstabelecimento);

    if (unidade == null) {
      throw new GenericServiceException("Unidade Filial não encontrada!");
    }

    if (unidade.getEmailUnidade() == null) {
      throw new GenericServiceException(
          "O estabelecimento selecionado não possui e-mail cadastrado!");
    }

    Credencial credencial = getCredencialService().findById(idCredencial);

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encotrada!");
    }

    ContaPagamento conta = getContaById(credencial.getIdConta());

    travaContasService.travaContas(conta.getIdConta(), Servicos.LANCAMENTO);

    if (!getCredencialService().validarPin(senha, idCredencial, token)) {
      throw new GenericServiceException("A senha do cartão está incorreta.", "Senha Inválida");
    }

    Integer codigoTransacao =
        contaPagamentoService.getCodTransacaoByProdPlatAndProdInstituidorConta(
            conta.getIdProdutoPlataforma(),
            conta.getProdutoInstituidor().getFuncao(),
            null,
            false,
            COMPRA);

    if (codigoTransacao == null) {
      map.put("msg", "O produto plataforma desta conta não permite esta transação.");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    String rrn = null;

    String numeroCartao = null;

    if (credencial.getIdCredencialExterna() != null
        && !credencial.getIdCredencialExterna().isEmpty()) {
      numeroCartao =
          getCredencialService()
              .descriptografarCredencialExterna(
                  credencial.getIdCredencialExterna(), Constantes.ZPK_001);
    } else {
      GetCardResponse card = cardService.getPan(credencial.getTokenInterno());

      if (!card.getSuccess()) {
        map.put("msg", "Não foi possível encontrar o cartão.");
        mapAndStatus = new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
        return mapAndStatus;
      }

      numeroCartao = card.getCard().getPan();
    }

    String nomeProduto = produto.getProdutoInstituicao().getDescProdInstituicao();

    String dataVencimentoCartao =
        DateUtil.dateFormat("yyMM", DateUtil.localDateTimeToDate(credencial.getDataValidade()));

    TransacaoAcquirer request =
        prepareTransacaoFinanceira(
            valor, codigoTransacao, unidade.getNumeroLogico(), numeroCartao, dataVencimentoCartao);

    mapAndStatus =
        acquirerClientService.doTransacaoWithConfirmacao(
            request, idFilialEstabelecimento, user.getIdUsuario());

    if (!origemPortador
        && mapAndStatus.getBody().containsValue("Transação realizada com sucesso!")) {
      rrn = getRrn(Constantes.PREFIXO_RRN);
      LancamentoManual lancamento = new LancamentoManual();
      lancamento.setIdConta(credencial.getIdConta());
      lancamento.setCodTransacao(codigoTransacao);
      lancamento.setValor(valor);
      lancamento.setTextoExtrato("Compra efetuada via email/telefone.");
      lancamento.setDataHoraLancamento(LocalDateTime.now());
      lancamento.setIdProcessadora(conta.getIdProcessadora());
      lancamento.setIdInstituicao(conta.getIdInstituicao());
      lancamento.setIdUsuario(user.getIdUsuario());
      lancamento.setRrn(rrn);
      lancamento.setIdFilialEstabelecimento(idFilialEstabelecimento);
      if (mapAndStatus.getBody().containsKey("comprovanteLojista"))
        lancamento.setComprovanteLojistaAcquirer(
            mapAndStatus.getBody().get("comprovanteLojista").toString());
      if (mapAndStatus.getBody().containsKey("comprovantePortador"))
        lancamento.setComprovantePortadorAcquirer(
            mapAndStatus.getBody().get("comprovantePortador").toString());
      lancamento = saveAndFlush(lancamento);
      // envia email de confirmacao para o estabelecimento da compra.
      sendEmail(
          credencial.getIdPessoa(),
          user,
          credencial.getUltimos4Digitos(),
          valor,
          unidade,
          nomeProduto,
          mapAndStatus.getBody());

      Pessoa pessoa = pessoaService.findPessoaTitularConta(conta.getIdConta());
      eventoService.publicarMovimentacaoFinanceiraEvent(
          Servicos.LANCAMENTO.getDescricao() + "_Manual",
          conta.getIdInstituicao(),
          null,
          conta.getIdConta(),
          null,
          codigoTransacao,
          null,
          valor,
          pessoa != null ? pessoa.getDocumento() : "0");
    } else {
      rrn = getRrn(Constantes.PREFIXO_RRN_PORTADOR);
    }

    return mapAndStatus;
  }

  private TransacaoAcquirer prepareTransacaoFinanceira(
      BigDecimal valor,
      Integer codigoTransacao,
      Long numeroLogico,
      String numeroCartao,
      String dataVencimentoCartao) {

    TransacaoAcquirer request = new TransacaoAcquirer();

    request.setValorMoedaOrigem(valor);
    request.setTipoTransacao(TipoTransacao.FINANCEIRA);
    request.setFunctionCode(codigoTransacao.toString());
    request.setNumeroCartao(numeroCartao);
    request.setMid(numeroLogico.toString());
    request.setDataHoraTransacao(new Date());
    request.setNsu(getStan().toString());
    request.setDataHoraGMT(Utils.getDataHoraUTC());
    request.setCodigoCredenciador("00000000006");
    request.setCodigoRedeCaptura("1001");
    request.setModoDeEntrada(Constantes.MODO_ENTRADA_PADRAO);
    request.setCodigoMoedaOrigem(Constantes.CODIGO_MOEDA_PADRAO);
    request.setTid("TIDWEB");
    request.setDataVencimentoCartao(dataVencimentoCartao);

    return request;
  }

  private void sendEmail(
      Long idPessoa,
      SecurityUser user,
      Integer ultimos4Digitos,
      BigDecimal valor,
      EstabelecimentoUnidade unidade,
      String nomeProduto,
      HashMap<String, Object> map)
      throws Exception {

    Pessoa pessoa = getPessoaDaCredencial(idPessoa);

    // Formata os ultimos 4 digitos se tiver apenas 3 numeros.
    StringBuilder sb = new StringBuilder();
    DecimalFormat df = new DecimalFormat(FORMAT_QUATRO_DIGITOS);
    sb.append(df.format(Integer.parseInt(ultimos4Digitos.toString())));

    emailService.enviarEmailUnidadeEstabCompraAutorizada(
        pessoa, user, sb.toString(), unidade, valor, nomeProduto, map);
  }

  public void reenviarEmialTeleVendas(
      Long idConta, BigDecimal valor, Integer idFilialEstabelecimento, SecurityUser user, Long id)
      throws Exception {

    List<Credencial> credencial = getCredencialService().findByIdContaInCredencialConta(idConta);
    if (credencial == null) {
      throw new GenericServiceException("Credencial não encotrada!");
    }

    EstabelecimentoUnidade unidade =
        estabelecimentoUnidadeService.findOneByIdFilialEstabelecimento(idFilialEstabelecimento);
    if (unidade == null) {
      throw new GenericServiceException("Unidade Filial não encontrada!");
    }
    if (unidade.getEmailUnidade() == null) {
      throw new GenericServiceException(
          "O estabelecimento selecionado não possui e-mail cadastrado!");
    }

    ContaPagamento conta = getContaById(idConta);

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    String nomeProduto = produto.getProdutoInstituicao().getDescProdInstituicao();

    HashMap<String, Object> map = new HashMap<>();

    LancamentoManual lancamento = findByIdLancamento(id.intValue());

    if (lancamento.getComprovanteLojistaAcquirer() != null)
      map.put("comprovanteLojista", lancamento.getComprovanteLojistaAcquirer());
    if (lancamento.getComprovantePortadorAcquirer() != null)
      map.put("comprovantePortador", lancamento.getComprovantePortadorAcquirer());

    sendEmail(
        credencial.get(0).getIdPessoa(),
        user,
        credencial.get(0).getUltimos4Digitos(),
        valor,
        unidade,
        nomeProduto,
        map);
  }

  public LancamentoManual findByIdLancamento(Integer id) {
    LancamentoManual lancamento = findById(id);
    if (lancamento == null) {
      throw new GenericServiceException("Não foi possível localizar o lancamento de id: " + id);
    }
    return lancamento;
  }

  public List<PontoControle> buscarListaSafrasAcumuloNaoBonificadas(Long idConta) {
    return utilManejoPontos.encontraAcumulosNaoBonificados(idConta);
  }

  public List<PontoControle> buscarTodasSafrasnaoExpiradasPorPortador(Long idConta) {
    return utilManejoPontos.buscaTodasSafrasNaoExpiradasPorPortador(idConta);
  }

  public List<BonificacaoResponse> buscarBonificacoesPorParceiroAcumulo(Integer idParcAcum) {
    return utilManejoPontos.buscarBonificacoesPorParceiroAcumulo(idParcAcum);
  }

  public LancamentoAuto cadastrarNovoLancamentoAutomatico(LancamentoAuto lancamentoAuto) {
    return lancamentoAutoRepository.save(lancamentoAuto);
  }

  @Transactional
  public ResponseEntity<HashMap<String, Object>> doLancManualTicketPapelEstabelecimento(
      PreLancamentoVoucherPapel lancamento, SecurityUser user, Integer idFilialEstabelecimento)
      throws Exception {

    ResponseEntity<HashMap<String, Object>> mapErroAndStatus =
        new ResponseEntity<HashMap<String, Object>>(null);

    HashMap<String, Object> mapErro = new HashMap<>();

    ContaPagamento contaPagadora = getContaById(lancamento.getIdConta().longValue());
    // getContaByIdPedidoVoucherPapel(lancamento.getIdConta(),
    // lancamento.getIdLoteLancamento().longValue());

    EstabelecimentoUnidade unidade =
        estabelecimentoUnidadeService.findOneByIdFilialEstabelecimento(idFilialEstabelecimento);

    if (unidade == null) {
      throw new GenericServiceException("Estabelecimento filial não encontrado!");
    }

    List<Credencial> listCredencial =
        getCredencialService().findByIdContaOrderByCsn(contaPagadora.getIdConta().longValue());

    Credencial credencial = listCredencial.get(0);

    if (credencial == null) {
      throw new GenericServiceException("Credencial não encotrada!");
    }

    Integer codigoTransacao =
        contaPagamentoService.getCodTransacaoByProdPlatAndProdInstituidorConta(
            contaPagadora.getIdProdutoPlataforma(),
            contaPagadora.getProdutoInstituidor().getFuncao(),
            null,
            false,
            COMPRA);

    if (codigoTransacao == null) {
      mapErro.put("msg", "O produto plataforma desta conta não permite esta transação.");
      return new ResponseEntity<>(mapErro, HttpStatus.BAD_REQUEST);
    }

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            contaPagadora.getIdProcessadora(),
            contaPagadora.getIdInstituicao(),
            contaPagadora.getIdProdutoInstituicao());

    String numeroCartao = null;

    if (credencial.getIdCredencialExterna() != null
        && !credencial.getIdCredencialExterna().isEmpty()) {
      numeroCartao =
          getCredencialService()
              .descriptografarCredencialExterna(
                  credencial.getIdCredencialExterna(), Constantes.ZPK_001);
    } else {
      GetCardResponse card = cardService.getPan(credencial.getTokenInterno());

      if (!card.getSuccess()) {
        mapErro.put("msg", "Não foi possível encontrar o cartão.");
        mapErroAndStatus = new ResponseEntity<>(mapErro, HttpStatus.BAD_REQUEST);
        return mapErroAndStatus;
      }

      numeroCartao = card.getCard().getPan();
    }

    String dataVencimentoCartao =
        DateUtil.dateFormat("yyMM", DateUtil.localDateTimeToDate(credencial.getDataValidade()));

    TransacaoAcquirer request =
        prepareTransacaoFinanceira(
            lancamento.getValorLancamento(),
            codigoTransacao,
            unidade.getNumeroLogico(),
            numeroCartao,
            dataVencimentoCartao);

    mapErroAndStatus =
        acquirerClientService.doTransacaoWithConfirmacao(
            request, unidade.getIdFilialEstabelecimento(), user.getIdUsuario());

    return mapErroAndStatus;
  }

  @Transactional
  public Boolean saveLancamentoPontoBonificado(
      PontoBonificadoVO pontoBonificado, Integer idUsuario, String ipOrigem) {

    PontoControle pontoControle =
        pontoControleService.findById(pontoBonificado.getIdPontoControle());

    PontoControle pontoControleBonificado = new PontoControle();

    if (pontoControle == null) {
      throw new GenericServiceException(
          "Não foi possivel encontrar o ponto acúmulo para bonificar");
    }

    BonificacaoConfiguracao bonificacaoConfig = new BonificacaoConfiguracao();

    if (pontoBonificado.getIdBonificacaoConfig().equals(Long.valueOf(9999))) {
      bonificacaoConfig.setIdBonificacaoConfig(9999);
      bonificacaoConfig.setValorVendaPontoBonificacao(new BigDecimal(0.0100));
      pontoControleBonificado.setDataExpiracao(
          DateUtil.parseDate(YYYY_MM_DD, pontoBonificado.getDataExpiracao()));

    } else {
      bonificacaoConfig =
          bonificacaoConfiguracaoService.findById(
              Integer.valueOf(pontoBonificado.getIdBonificacaoConfig().toString()));
      pontoControleBonificado.setDataExpiracao(
          DateUtil.parseDate(DD_MM_YYYY, pontoBonificado.getDataExpiracao()));
      if (bonificacaoConfig == null) {
        throw new GenericServiceException(
            "Não foi possivel encontrar a configuração da bonificação");
      }
    }

    ContaPagamento conta = getContaById(pontoControle.getIdConta());

    travaContasService.travaContas(conta.getIdConta(), Servicos.PONTOS);

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(conta, produto);

    List<Credencial> credenciais =
        credencialService.recuperarCredenciaisPorIdConta(conta.getIdConta());

    String tokenInterno =
        credenciais.stream()
            .sorted(
                new Comparator<Credencial>() {

                  @Override
                  public int compare(Credencial o1, Credencial o2) {
                    return o1.getCsn().compareTo(o2.getCsn());
                  }
                })
            .findFirst()
            .get()
            .getTokenInterno();

    CadastroLancamentoManual lancamentoManual = new CadastroLancamentoManual();

    lancamentoManual.setCodTransacao(CODIGO_TRANSACAO_BONIFICACAO);
    lancamentoManual.setIdConta(conta.getIdConta());
    lancamentoManual.setValor(new BigDecimal(pontoBonificado.getQtdPontos()));
    lancamentoManual.setTextoExtrato(pontoBonificado.getTextoExtrato());
    lancamentoManual.setTextoComentario(pontoBonificado.getTextoComentario());
    lancamentoManual.setSinal(CREDITO);

    String rrn = null;

    rrn = saveLancamentoManual(lancamentoManual, idUsuario, conta, true);

    pontoControleBonificado.setIdJobInclusao(Long.valueOf(1));
    pontoControleBonificado.setDataHoraJobInclusao(new Date());
    pontoControleBonificado.setIdUsuarioInclusao(Long.valueOf(idUsuario));
    pontoControleBonificado.setIdPontoRecebido(pontoControle.getIdPontoRecebido());
    pontoControleBonificado.setTipoPonto(2);
    pontoControleBonificado.setQtdPontos(pontoBonificado.getQtdPontos());
    pontoControleBonificado.setIdProcessadora(pontoControle.getIdProcessadora());
    pontoControleBonificado.setIdInstituicao(pontoControle.getIdInstituicao());
    pontoControleBonificado.setIdConta(pontoControle.getIdConta());
    pontoControleBonificado.setQtdPontosUtilizados(Long.valueOf(0));
    pontoControleBonificado.setIdPontoControleRef(pontoBonificado.getIdPontoControle());
    pontoControleBonificado.setIdBonificacaoConfig(pontoBonificado.getIdBonificacaoConfig());
    pontoControleBonificado.setVlrVendaPonto(
        bonificacaoConfig.getValorVendaPontoBonificacao().doubleValue());
    pontoControleBonificado.setIdParcAcumulo(pontoControle.getIdParcAcumulo());
    pontoControleBonificado.setIdParcBonificador(pontoControle.getIdParcBonificador());
    pontoControleBonificado.setRrn(rrn);
    pontoControleBonificado.setDocumento(pontoControle.getDocumento());
    pontoControleBonificado.setQtdPontosExpirados(Long.valueOf(0));
    pontoControleBonificado.setQtdPntsEstornoAcumulo(Long.valueOf(0));
    pontoControleBonificado.setQtdPntsEstornoExpirado(Long.valueOf(0));
    pontoControleBonificado.setQtdPntsEstornoUtilizado(Long.valueOf(0));
    pontoControleBonificado.setQtdPntsAjusteCredito(Long.valueOf(0));
    pontoControleBonificado.setQtdPntsAjusteDebito(Long.valueOf(0));

    pontoControleService.saveAndFlush(pontoControleBonificado);

    JcardResponse jcardResponse = new JcardResponse();

    Integer stan = getStan();

    jcardResponse =
        doLancamentoManual(
            accountCode,
            lancamentoManual.getCodTransacao(),
            lancamentoManual.getValor(),
            produto.getMoeda().getIdMoeda().toString(),
            rrn,
            lancamentoManual.getSinal(),
            tokenInterno,
            lancamentoManual.getIdConta(),
            lancamentoManual.getTextoExtrato(),
            null,
            ipOrigem,
            null,
            stan,
            null);

    if (!jcardResponse.getSuccess()) {
      throw new GenericServiceException("Não foi possível efetuar o lançamento no autorizador.");
    }

    Pessoa pessoa = pessoaService.findPessoaTitularConta(conta.getIdConta());
    eventoService.publicarMovimentacaoFinanceiraEvent(
        Servicos.LANCAMENTO.getDescricao() + "_PontoBonificado",
        conta.getIdInstituicao(),
        null,
        conta.getIdConta(),
        null,
        CODIGO_TRANSACAO_BONIFICACAO,
        null,
        new BigDecimal(pontoBonificado.getQtdPontos()),
        pessoa != null ? pessoa.getDocumento() : "0");

    return true;
  }

  @Transactional
  public Boolean efetuarAjusteDebitoOuCredito(
      AjustePontoRequestVO ajustePontoRequestVO, SecurityUser user, String ipOrigem) {

    consultaRestritaService.checaPrivilegio(ajustePontoRequestVO.getIdConta(), user);

    return efetuarAjusteDebitoOuCredito(ajustePontoRequestVO, user.getIdUsuario(), ipOrigem, false);
  }

  @Transactional
  public Boolean efetuarAjusteDebitoOuCredito(
      AjustePontoRequestVO ajustePontoRequestVO, Integer idUsuario, String ipOrigem, Boolean app) {

    if (ajustePontoRequestVO.getSinal().equals(CREDITO)) {
      lancarAjusteDePontoCredito(ajustePontoRequestVO, idUsuario, ipOrigem, app, true);
      return true;
    }

    if (ajustePontoRequestVO.getSinal().equals(DEBITO)) {
      lancarAjusteDePontoDebito(ajustePontoRequestVO, idUsuario, ipOrigem, true);
      return true;
    }

    return false;
  }

  @Transactional
  public String efetuarAjusteDebitoOuCreditoValoo(
      AjustePontoRequestVO ajustePontoRequestVO, Integer idUsuario, String ipOrigem, Boolean app) {

    if (ajustePontoRequestVO.getSinal().equals(CREDITO)) {
      return lancarAjusteDePontoCredito(ajustePontoRequestVO, idUsuario, ipOrigem, app, false);
    }

    if (ajustePontoRequestVO.getSinal().equals(DEBITO)) {
      return lancarAjusteDePontoDebito(ajustePontoRequestVO, idUsuario, ipOrigem, false);
    }

    return null;
  }

  private String lancarAjusteDePontoDebito(
      AjustePontoRequestVO ajustePontoRequestVO,
      Integer idUsuario,
      String ipOrigem,
      Boolean disparoManual) {

    ContaPagamento conta = contaPagamentoService.findById(ajustePontoRequestVO.getIdConta());

    if (conta == null) {
      throw new GenericServiceException(
          "Não foi possível efetuar o Ajuste de débito. Conta não encontrada");
    }

    CadastroLancamentoManual lancamentoManual = new CadastroLancamentoManual();

    lancamentoManual.setCodTransacao(Constantes.COD_TRANSACAO_AJUSTE_A_DEBITO_LOYALTY);
    lancamentoManual.setIdConta(conta.getIdConta());
    lancamentoManual.setValor(new BigDecimal(ajustePontoRequestVO.getQtdPontos()));
    lancamentoManual.setTextoExtrato(ajustePontoRequestVO.getTextoExtrato());
    lancamentoManual.setTextoComentario(ajustePontoRequestVO.getTextoComentario());
    lancamentoManual.setSinal(DEBITO);

    String rrn = null;

    rrn = saveLancamentoManual(lancamentoManual, idUsuario, conta, disparoManual);

    JcardResponse jcardResponse = new JcardResponse();

    Integer stan = getStan();

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(conta, produto);

    List<Credencial> credenciais =
        credencialService.recuperarCredenciaisPorIdConta(conta.getIdConta());

    String tokenInterno =
        credenciais.stream()
            .sorted(
                new Comparator<Credencial>() {

                  @Override
                  public int compare(Credencial o1, Credencial o2) {
                    return o1.getCsn().compareTo(o2.getCsn());
                  }
                })
            .findFirst()
            .get()
            .getTokenInterno();

    List<PontoControle> listSaldoPontosControle =
        utilManejoPontos.buscaPontosControleComSaldo(conta.getIdConta());
    if (lancamentoManual != null)
      utilManejoPontos.atualizarResgateEmPontoControle(
          rrn,
          Constantes.ZERO_LONG,
          lancamentoManual.getValor().longValue(),
          listSaldoPontosControle,
          conta.getIdConta());

    jcardResponse =
        doLancamentoManual(
            accountCode,
            lancamentoManual.getCodTransacao(),
            lancamentoManual.getValor(),
            produto.getMoeda().getIdMoeda().toString(),
            rrn,
            lancamentoManual.getSinal(),
            tokenInterno,
            lancamentoManual.getIdConta(),
            lancamentoManual.getTextoExtrato(),
            null,
            ipOrigem,
            null,
            stan,
            null);

    if (!jcardResponse.getSuccess()) {
      throw new GenericServiceException("Não foi possível efetuar o ajuste débito no autorizador.");
    }

    return rrn;
  }

  private String lancarAjusteDePontoCredito(
      AjustePontoRequestVO ajustePontoRequestVO,
      Integer idUsuario,
      String ipOrigem,
      Boolean app,
      Boolean disparoManual) {

    ContaPagamento conta = contaPagamentoService.findById(ajustePontoRequestVO.getIdConta());

    if (conta == null) {
      throw new GenericServiceException("Não foi possível efetuar o Ajuste. Conta não encontrada");
    }

    ParceiroAcumulo parceiroAcumulo =
        parceiroAcumuloService.findById(Integer.valueOf(ajustePontoRequestVO.getIdParceiro()));

    if (parceiroAcumulo == null) {
      throw new GenericServiceException(
          "Não foi possível efetuar o Ajuste. Parceiro Acúmulo não encontrado");
    }

    Pessoa pessoa = pessoaService.findPessoaTitularConta(conta.getIdConta());

    PontosRecebidos pontosRecebidos = new PontosRecebidos();

    PontosRecebidos savePontosRecebidos =
        pontosRecebidosService.saveLancamentoAjustePontosRecebidos(
            pontosRecebidos,
            parceiroAcumulo.getIdParceiroAcumulo(),
            idUsuario,
            pessoa,
            Long.valueOf(ajustePontoRequestVO.getQtdPontos()),
            app);

    CadastroLancamentoManual lancamentoManual = new CadastroLancamentoManual();

    lancamentoManual.setCodTransacao(
        ajustePontoRequestVO.getCodTransacao() == null
                || ajustePontoRequestVO.getCodTransacao() == 0
            ? Constantes.COD_TRANSACAO_AJUSTE_A_CREDITO_LOYALTY
            : ajustePontoRequestVO.getCodTransacao());
    lancamentoManual.setIdConta(conta.getIdConta());
    lancamentoManual.setValor(new BigDecimal(savePontosRecebidos.getQtdPontos()));
    lancamentoManual.setTextoExtrato(ajustePontoRequestVO.getTextoExtrato());
    lancamentoManual.setTextoComentario(ajustePontoRequestVO.getTextoComentario());
    lancamentoManual.setSinal(CREDITO);

    String rrn = null;

    rrn = saveLancamentoManual(lancamentoManual, idUsuario, conta, disparoManual);

    pontoControleService.saveLancamentoAjustePontoControle(
        rrn, idUsuario, savePontosRecebidos, ajustePontoRequestVO, parceiroAcumulo, conta);

    Integer stan = getStan();

    ProdutoInstituicaoConfiguracao produto =
        getProduto(
            conta.getIdProcessadora(), conta.getIdInstituicao(), conta.getIdProdutoInstituicao());

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(conta, produto);

    List<Credencial> credenciais =
        credencialService.recuperarCredenciaisPorIdConta(conta.getIdConta());

    String tokenInterno =
        credenciais.stream()
            .sorted(
                new Comparator<Credencial>() {

                  @Override
                  public int compare(Credencial o1, Credencial o2) {
                    return o1.getCsn().compareTo(o2.getCsn());
                  }
                })
            .findFirst()
            .get()
            .getTokenInterno();

    JcardResponse jcardResponse = new JcardResponse();

    jcardResponse =
        doLancamentoManual(
            accountCode,
            lancamentoManual.getCodTransacao(),
            lancamentoManual.getValor(),
            produto.getMoeda().getIdMoeda().toString(),
            rrn,
            lancamentoManual.getSinal(),
            tokenInterno,
            lancamentoManual.getIdConta(),
            lancamentoManual.getTextoExtrato(),
            null,
            ipOrigem,
            null,
            stan,
            null);

    if (!jcardResponse.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível efetuar o ajuste a crédito no autorizador.");
    }

    return rrn;
  }

  public TransacaoDePontoResponse getTransacaoDePontoEstornado(String rrn) {

    LancamentoManual lancamento = repository.findOneByRrnEstornado(rrn);

    TransacaoDePontoResponse transacao = new TransacaoDePontoResponse();

    if (lancamento != null) {
      transacao.setDataLancamento(
          DateUtil.dateFormat(
              DateUtil.DD_MM_YYYY,
              DateUtil.localDateTimeToDate(lancamento.getDataHoraLancamento())));
      transacao.setIdConta(lancamento.getIdConta());
      transacao.setRrn(lancamento.getRrn());
      transacao.setRrnEstornado(lancamento.getRrnEstornado());
      transacao.setFunctioncode(lancamento.getCodTransacao().toString());
      transacao.setDescricaoFunctioncode(
          lancamento.getCodigoTransacao().getDescTransacaoEstendida());
      transacao.setTextoComentario(lancamento.getTextoComentario());
      transacao.setTextoExtrato(lancamento.getTextoExtrato());
      transacao.setValor(lancamento.getValor().longValue());
      transacao.setIsPossuiEstorno(true);
    }

    return transacao;
  }

  public LancamentoManual getByRrn(String rrn) {
    return repository.findOneByRrn(rrn);
  }

  public LancamentoManual buscaRrn(String rrn) {
    return repository.findByRrn(rrn);
  }

  public PontoControleDetalhesVO getPontoControleDetalhesLancadoManualmente(String rrn) {

    PontoControleDetalhesVO detalhes = new PontoControleDetalhesVO();

    LancamentoManual lancamento = repository.findOneByRrn(rrn);
    if (lancamento != null) {

      if (lancamento.getCodTransacao() == Constantes.COD_TRANSACAO_ACUMULO_SAFRA
          || lancamento.getCodTransacao() == Constantes.COD_TRANSACAO_ESTORNO_ACUMULO_SAFRA) {
        detalhes.setTipoPonto(Constantes.TIPO_PONTO_ACUMULO);
        detalhes.setIsPontoAcumulo(true);
        detalhes.setDataExpiracao(Constantes.MENSAGEM_NAO_EXPIRA);
      } else if (lancamento.getCodTransacao() == Constantes.COD_TRANSACAO_BONIFICACAO_SAFRA
          || lancamento.getCodTransacao() == Constantes.COD_TRANSACAO_ESTORNO_BONIFICACAO_SAFRA) {
        detalhes.setTipoPonto(Constantes.TIPO_PONTO_BONIFICACAO);
        detalhes.setIsPontoBonificacao(true);
        detalhes.setDataExpiracao(null);
      }

      detalhes.setDataInclusao(
          DateUtil.dateFormat(
              "dd/MM/yyyy", DateUtil.localDateTimeToDate(lancamento.getDataHoraLancamento())));
      detalhes.setIdParceiroAcumulo(null);
      detalhes.setNomeParceiroAcumulo("Ainda não definido");
      detalhes.setIdParceiroBonificador(null);
      detalhes.setNomeParceiroBonificador("Ainda não definido");
      detalhes.setIdPontoControle(null);
      detalhes.setSaldoSafra(null);

      detalhes.setIsExpirado(false);
      detalhes.setQtdPontos(lancamento.getValor().longValue());
      detalhes.setRrn(rrn);
    }

    return detalhes;
  }

  public BigDecimal buscarTarifa(ContaPagamento conta, Integer codTransacao) {

    GetPerfisTarifarios perfilTarifarico =
        perfilTarifarioService.buscarPerfisTarifariosConta(conta.getIdConta());

    CodigoTransacao codigoTransacao = codigoTransacaoService.findById(codTransacao);

    Integer idTarifa = null;

    BigDecimal valorTarifa = new BigDecimal(0);

    if (codigoTransacao.getCodTranTarifa() != null) {
      idTarifa = codigoTransacao.getCodTranTarifa();

      if (!(perfilTarifarico.getIdPerfilTarifarioProduto() == null)) {

        valorTarifa =
            perfilTarifarioService.buscarTarifaPorId(
                perfilTarifarico.getIdPerfilTarifarioProduto(), idTarifa);

      } else if (!(perfilTarifarico.getIdPerfilTarifario() == null)) {

        valorTarifa =
            perfilTarifarioService.buscarTarifaPorId(
                perfilTarifarico.getIdPerfilTarifarioProduto(), idTarifa);
      }
    }
    if (valorTarifa == null) {
      valorTarifa = new BigDecimal(0);
    }

    return valorTarifa;
  }

  public LancamentoManual buscarLancamentoTxtComentarioLike(String transactionId) {
    return repository.findByTextoComentarioIsContaining(transactionId);
  }

  public LancamentoAuto buscarLancamentoAutoByRrn(String rrn) {
    return lancamentoAutoRepository.findLancamentoAutoByRrn(rrn);
  }
}
