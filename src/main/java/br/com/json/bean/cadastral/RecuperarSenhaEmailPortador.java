package br.com.json.bean.cadastral;

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;

/** Created by denniscremasco on 08/03/18. */
public class RecuperarSenhaEmailPortador {

  @NotNull
  @ApiModelProperty(required = true)
  private String documento;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idProcessadora;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idInstituicao;

  @NotNull
  @ApiModelProperty(required = true)
  private String email;

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }
}
