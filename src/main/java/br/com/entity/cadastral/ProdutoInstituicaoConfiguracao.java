package br.com.entity.cadastral;

import br.com.entity.suporte.MoedaConta;
import br.com.sinergico.enums.PadraoSeqCredencial;
import br.com.sinergico.enums.SegundaViaBoletoEnum;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.util.Util;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.Type;

/** The persistent class for the produto_instituicao_configuracao database table. */
@Getter
@Setter
@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "produto_instituicao_configuracao", schema = "cadastral")
@SequenceGenerator(
    name = "PRODUTO_INSTITUICAO_CONFIGURACAO_ID_GENERATOR",
    sequenceName = "CADASTRAL.SEQ_ID_PRODUTO_INSTITUICAO_CONFIG",
    initialValue = 1,
    allocationSize = 1)
public class ProdutoInstituicaoConfiguracao implements Serializable {
  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "PRODUTO_INSTITUICAO_CONFIGURACAO_ID_GENERATOR")
  @Column(unique = true, nullable = false)
  private Integer id;

  @Column(name = "id_moeda", nullable = false)
  private Integer idMoeda;

  @Column(name = "id_prod_instituicao", nullable = false)
  private Integer idProdInstituicao;

  @ManyToOne
  @JoinColumn(
      name = "id_prod_instituicao",
      referencedColumnName = "id_prod_instituicao",
      insertable = false,
      updatable = false)
  private ProdutoInstituicao produtoInstituicao;

  @Column(name = "id_prod_plat", nullable = false)
  private Integer idProdutoPlataforma;

  @ManyToOne
  @JoinColumn(
      name = "id_prod_plat",
      referencedColumnName = "id_prod_plat",
      insertable = false,
      updatable = false)
  private ProdutoPlataforma produtoPlataforma;

  @Column(name = "id_processadora", nullable = false)
  private Integer idProcessadora;

  @Column(name = "id_instituicao", nullable = false)
  private Integer idInstituicao;

  @Column(name = "id_produto_instituidor", nullable = false)
  private Integer idProdutoInstituidor;

  @ManyToOne
  @JoinColumn(
      name = "id_produto_instituidor",
      referencedColumnName = "id_prod_instituidor",
      insertable = false,
      updatable = false)
  private ProdutoInstituidor produtoInstituidor;

  @Column(name = "bin", nullable = false)
  private Integer bin;

  @Column(name = "bin_length", nullable = false)
  private Integer binLength;

  @Column(name = "bin_estendido", nullable = false)
  private Long binEstendido;

  @Column(name = "sequencial", nullable = false)
  private Integer sequencial;

  @Column(name = "padrao", nullable = false)
  @Enumerated(EnumType.ORDINAL)
  private PadraoSeqCredencial padrao;

  @ManyToOne
  @JoinColumn(name = "id_moeda", referencedColumnName = "id", updatable = false, insertable = false)
  private MoedaConta moeda;

  @Column(name = "id_arranjo")
  private Integer idArranjo;

  @Column(name = "id_relacionamento")
  private Integer idRelacionamento;

  @ManyToOne
  @JoinColumn(name = "id_arranjo", insertable = false, updatable = false)
  private ArranjoPagamento arranjoPagamento;

  // campos de boleto
  @Column(name = "bol_banco")
  private Integer boletoBanco;

  @Column(name = "bol_agencia")
  private String boletoAgencia;

  @Column(name = "bol_digito_agencia")
  private String boletoDigitoAgencia;

  @Column(name = "bol_codigo_beneficiario")
  private String boletoCodigoBeneficiario;

  @Column(name = "bol_digito_codigo_beneficiario")
  private String boletoDigitoCodigoBeneficiario;

  @Column(name = "bol_numero_convenio")
  private String boletoNumeroConvenio;

  @Column(name = "bol_numero_convenio_pj")
  private String boletoNumeroConvenioPJ;

  @Column(name = "bol_carteira")
  private String boletoCarteira;

  @Column(name = "bol_variacao_carteira")
  private String boletoVariacaoCarteira;

  @Column(name = "bol_variacao_carteira_pj")
  private String boletoVariacaoCarteiraPJ;

  /*se quiser pode separar por ; pra cadastrar vári(o)(a)s*/
  @Column(name = "bol_instrucoes")
  private String boletoInstrucoes;

  /*se quiser pode separar por ; pra cadastrar vári(o)(a)s*/
  @Column(name = "bol_locais_pagamento")
  private String boletoLocaisPagamento;

  // 4 - senha de 4 digitos / 6 - senha de 6 digitos / 0 - assinatura
  @Column(name = "tamanho_pin")
  private Integer tamanhoPin;

  @Column(name = "meses_validade_fisico")
  private Integer mesesValidadeFisico;

  @Column(name = "bin_virtual")
  private Integer binVirtual;

  @Column(name = "bin_estendido_virtual")
  private Long binEstendidoVirtual;

  @Column(name = "sequencial_virtual")
  private Integer sequencialVirtual;

  @Column(name = "meses_validade_max_virtual")
  private Integer mesesValidadeMaxVirtual;

  @Column(name = "quant_max_compras_virtual")
  private Integer quantMaxComprasVirtual;

  @Type(type = "numeric_boolean")
  @Column(name = "def_valor_max_transacao_virtual")
  private Boolean defValorMaxTransacaoVirtual;

  @Type(type = "numeric_boolean")
  @Column(name = "def_validade_virtual")
  private Boolean defValidadeVirtual;

  @Type(type = "numeric_boolean")
  @Column(name = "def_quant_max_compras_virtual")
  private Boolean defQuantMaxComprasVirtual;

  @Type(type = "numeric_boolean")
  @Column(name = "def_limite_virtual")
  private Boolean defLimiteVirtual;

  @Type(type = "numeric_boolean")
  @Column(name = "local_emissao")
  private Boolean localEmissao;

  @Type(type = "numeric_boolean")
  @Column(name = "def_habilitar_exterior_virtual")
  private Boolean defHabilitarExteriorVirtual;

  @Column(name = "bol_juros_atraso")
  private BigDecimal boletoJurosAtraso;

  @Column(name = "bol_multa_atraso")
  private BigDecimal boletoMultaAtraso;

  @Column(name = "bol_registrado")
  @Type(type = "numeric_boolean")
  private Boolean boletoRegistrado;

  @Column(name = "id_card_product")
  private Long idCardProduct;

  // 1 - PF / 2 - PJ
  @Column(name = "tipo_pessoa")
  private Integer tipoPessoa;

  @Column(name = "idade_min_portador")
  private Integer idadeMinimaPortador;

  @Column(name = "idade_min_adicional")
  private Integer idadeMinimaAdicional;

  @Column(name = "service_code")
  private String serviceCode;

  @Type(type = "numeric_boolean")
  @Column(name = "chip")
  private Boolean chip;

  @Column(name = "tipo_emissao")
  private Integer tipoEmissao;

  @Type(type = "numeric_boolean")
  @Column(name = "virtual")
  private Boolean virtual;

  @Column(name = "primeiro_cartao_virtual")
  private Integer primeiroCartaoVirtual;

  @Type(type = "numeric_boolean")
  @Column(name = "emite_propria_empresa")
  private Boolean emitePropriaEmpresa;

  @Type(type = "numeric_boolean")
  @Column(name = "suporta_conta_base")
  private Boolean suportaContaBase;

  @Column(name = "valor_carga_min", nullable = true)
  private Double valorCargaMin;

  @Column(name = "valor_carga_max", nullable = true)
  private Double valorCargaMax;

  @Type(type = "numeric_boolean")
  @Column(name = "permite_pre_emissao")
  private Boolean permitePreEmissao;

  @Column(name = "id_usuario_manutencao")
  private Integer idUsuarioManutencao;

  @Column(name = "id_usuario_inclusao")
  private Integer idUsuarioInclusao;

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "dt_hr_inclusao")
  private Date dtHrInclusao;

  @Column(name = "vencimentos_fatura")
  private String vencimentosFatura;

  @Column(name = "id_produto_externo")
  private String idProdutoExterno;

  @Column(name = "bandeira_produto")
  private Integer bandeiraProduto;

  @Column(name = "bol_apresentadora")
  private String boletoApresentadora;

  @Column(name = "permite_restricao_mcc")
  private Integer permiteRestricaoMcc;

  @Column(name = "tipo_restricao_mcc")
  private Integer tipoRestricaoMcc;

  @Column(name = "identificador_jcard")
  private Long identificadorJCard;

  @Type(type = "numeric_boolean")
  @Column(name = "manter_pwd_seg_via")
  private Boolean manterPwdSegundaVia;

  @Column(name = "qtd_dias_corte")
  private Integer qtdDiasCorte;

  @Column(name = "dias_emissao_segunda_via")
  private Integer diasEmissaoSegundaVia;

  @Column(name = "percentual_limite_adicional", precision = 3, scale = 2)
  private BigDecimal percentualLimiteAdicional;

  @Type(type = "numeric_boolean")
  @Column(name = "diferenciacao_limite_adicional")
  private Boolean diferenciacaoLimiteAdicional;

  @Column(name = "tipo_formulario", nullable = false)
  private Integer tipoFormulario = 0;

  @Column(name = "carga_integracao")
  private Boolean cargaIntegracao = Boolean.FALSE;

  @Column(name = "id_grupo_produto")
  private Long idGrupoProduto;

  @Enumerated(EnumType.ORDINAL)
  @Column(name = "tipo_produto")
  private TipoProdutoEnum tipoProduto;

  @Column(nullable = false, name = "id_bandeira", precision = 2, scale = 0)
  private Integer idBandeira;

  @ManyToOne
  @JoinColumn(
      name = "id_bandeira",
      referencedColumnName = "id",
      insertable = false,
      updatable = false)
  private Bandeira bandeira;

  @Type(type = "numeric_boolean")
  @Column(name = "produto_social_brb")
  private Boolean produtoSocialBrb = Boolean.FALSE;

  @Type(type = "br.com.entity.custom.IntegerListType")
  @Column(name = "historico_arranjos", columnDefinition = "integer[]")
  private List<Integer> historicoIdArranjos;

  @Column(name = "segunda_via_boleto")
  private SegundaViaBoletoEnum segundaViaBoleto = SegundaViaBoletoEnum.DESABILITADO;

  @Column(name = "redefinir_senha_caf")
  private Boolean redefinirSenhaCaf = Boolean.FALSE;

  @Column(name = "metodo_seguranca_transacao")
  private Integer metodoSegurancaTransacao;

  @Column(name = "maximo_qr_code_vinculados")
  private Integer maximoQrCodesVinculados;

  @Column(name = "qtd_contas_documento_produto")
  private Integer qtdContasDocumentoProduto;

  @Column(name = "bl_corporativo")
  private Boolean blCorporativo;

  @Transient private List<Integer> vencimentoFaturaList;

  public List<Integer> getVencimentoFaturaList() {
    if (Objects.nonNull(getVencimentosFatura()) && Objects.isNull(vencimentoFaturaList)) {
      List<Integer> prazos = new ArrayList<>();
      Arrays.asList(getVencimentosFatura().split(";")).forEach(i -> prazos.add(Integer.valueOf(i)));
      setVencimentoFaturaList(prazos);
    }
    return vencimentoFaturaList;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((id == null) ? 0 : id.hashCode());
    result = prime * result + ((idMoeda == null) ? 0 : idMoeda.hashCode());
    result = prime * result + ((idProdInstituicao == null) ? 0 : idProdInstituicao.hashCode());
    result = prime * result + ((idProdutoPlataforma == null) ? 0 : idProdutoPlataforma.hashCode());
    result =
        prime * result + ((idProdutoInstituidor == null) ? 0 : idProdutoInstituidor.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    ProdutoInstituicaoConfiguracao other = (ProdutoInstituicaoConfiguracao) obj;
    if (id == null) {
      if (other.id != null) return false;
    } else if (!id.equals(other.id)) return false;
    if (idMoeda == null) {
      if (other.idMoeda != null) return false;
    } else if (!idMoeda.equals(other.idMoeda)) return false;
    if (idProdInstituicao == null) {
      if (other.idProdInstituicao != null) return false;
    } else if (!idProdInstituicao.equals(other.idProdInstituicao)) return false;
    if (idProdutoPlataforma == null) {
      if (other.idProdutoPlataforma != null) return false;
    } else if (!idProdutoPlataforma.equals(other.idProdutoPlataforma)) return false;
    if (idProdutoInstituidor == null) {
      if (other.idProdutoInstituidor != null) return false;
    } else if (!idProdutoInstituidor.equals(other.idProdutoInstituidor)) return false;
    return true;
  }

  @Override
  public String toString() {
    return "ProdutoInstituicaoConfiguracao{"
        + "id="
        + id
        + ", idMoeda="
        + idMoeda
        + ", idProdInstituicao="
        + idProdInstituicao
        + ", idProdutoPlataforma="
        + idProdutoPlataforma
        + ", idProcessadora="
        + idProcessadora
        + ", idInstituicao="
        + idInstituicao
        + ", idProdutoInstituidor="
        + idProdutoInstituidor
        + ", bin="
        + bin
        + ", binLength="
        + binLength
        + ", binEstendido="
        + binEstendido
        + ", sequencial="
        + sequencial
        + ", idArranjo="
        + idArranjo
        + ", idRelacionamento="
        + idRelacionamento
        + ", boletoBanco="
        + boletoBanco
        + ", boletoAgencia='"
        + boletoAgencia
        + '\''
        + ", boletoDigitoAgencia='"
        + boletoDigitoAgencia
        + '\''
        + ", boletoCodigoBeneficiario='"
        + boletoCodigoBeneficiario
        + '\''
        + ", boletoDigitoCodigoBeneficiario='"
        + boletoDigitoCodigoBeneficiario
        + '\''
        + ", boletoNumeroConvenio='"
        + boletoNumeroConvenio
        + '\''
        + ", boletoNumeroConvenioPJ='"
        + boletoNumeroConvenioPJ
        + '\''
        + ", boletoCarteira='"
        + boletoCarteira
        + '\''
        + ", boletoVariacaoCarteira='"
        + boletoVariacaoCarteira
        + '\''
        + ", boletoVariacaoCarteiraPJ='"
        + boletoVariacaoCarteiraPJ
        + '\''
        + ", boletoInstrucoes='"
        + boletoInstrucoes
        + '\''
        + ", boletoLocaisPagamento='"
        + boletoLocaisPagamento
        + '\''
        + ", tamanhoPin="
        + tamanhoPin
        + ", mesesValidadeFisico="
        + mesesValidadeFisico
        + ", binVirtual="
        + binVirtual
        + ", binEstendidoVirtual="
        + binEstendidoVirtual
        + ", sequencialVirtual="
        + sequencialVirtual
        + ", mesesValidadeMaxVirtual="
        + mesesValidadeMaxVirtual
        + ", quantMaxComprasVirtual="
        + quantMaxComprasVirtual
        + ", defValorMaxTransacaoVirtual="
        + defValorMaxTransacaoVirtual
        + ", defValidadeVirtual="
        + defValidadeVirtual
        + ", defQuantMaxComprasVirtual="
        + defQuantMaxComprasVirtual
        + ", defLimiteVirtual="
        + defLimiteVirtual
        + ", localEmissao="
        + localEmissao
        + ", defHabilitarExteriorVirtual="
        + defHabilitarExteriorVirtual
        + ", boletoJurosAtraso="
        + boletoJurosAtraso
        + ", boletoMultaAtraso="
        + boletoMultaAtraso
        + ", boletoRegistrado="
        + boletoRegistrado
        + ", idCardProduct="
        + idCardProduct
        + ", tipoPessoa="
        + tipoPessoa
        + ", idadeMinimaPortador="
        + idadeMinimaPortador
        + ", idadeMinimaAdicional="
        + idadeMinimaAdicional
        + ", serviceCode='"
        + serviceCode
        + '\''
        + ", chip="
        + chip
        + ", tipoEmissao="
        + tipoEmissao
        + ", virtual="
        + virtual
        + ", primeiroCartaoVirtual="
        + primeiroCartaoVirtual
        + ", emitePropriaEmpresa="
        + emitePropriaEmpresa
        + ", suportaContaBase="
        + suportaContaBase
        + ", valorCargaMin="
        + valorCargaMin
        + ", valorCargaMax="
        + valorCargaMax
        + ", permitePreEmissao="
        + permitePreEmissao
        + ", idUsuarioManutencao="
        + idUsuarioManutencao
        + ", idUsuarioInclusao="
        + idUsuarioInclusao
        + ", dtHrInclusao="
        + dtHrInclusao
        + ", vencimentosFatura='"
        + vencimentosFatura
        + '\''
        + ", idProdutoExterno='"
        + idProdutoExterno
        + '\''
        + ", bandeiraProduto="
        + bandeiraProduto
        + ", boletoApresentadora='"
        + boletoApresentadora
        + '\''
        + ", permiteRestricaoMcc="
        + permiteRestricaoMcc
        + ", tipoRestricaoMcc="
        + tipoRestricaoMcc
        + ", identificadorJCard="
        + identificadorJCard
        + ", manterPwdSegundaVia="
        + manterPwdSegundaVia
        + ", qtdDiasCorte="
        + qtdDiasCorte
        + ", diasEmissaoSegundaVia="
        + diasEmissaoSegundaVia
        + ", percentualLimiteAdicional="
        + percentualLimiteAdicional
        + ", diferenciacaoLimiteAdicional="
        + diferenciacaoLimiteAdicional
        + ", tipoFormulario="
        + tipoFormulario
        + ", cargaIntegracao="
        + cargaIntegracao
        + ", idGrupoProduto="
        + idGrupoProduto
        + ", tipoProduto="
        + tipoProduto
        + ", idBandeira="
        + idBandeira
        + ", produtoSocialBrb="
        + produtoSocialBrb
        + ", redefinirSenhaCaf="
        + redefinirSenhaCaf
        + ", metodoSegurancaTransacao="
        + metodoSegurancaTransacao
        + ", maximoQrCodesVinculados="
        + maximoQrCodesVinculados
        + ", qtdContasDocumentoProduto="
        + qtdContasDocumentoProduto
        + '}';
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {
    try {
      Util.toUpperCase(this);
    } catch (Exception e) {

    }
  }
}
