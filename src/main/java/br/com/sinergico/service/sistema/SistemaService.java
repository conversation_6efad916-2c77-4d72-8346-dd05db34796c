package br.com.sinergico.service.sistema;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.suporte.AcessoLog;
import br.com.entity.suporte.AcessoUsuario;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.SistemaExternoServiceException;
import br.com.json.bean.cadastral.DesbloquearCredencialCompleta;
import br.com.json.bean.sistema.AlterarStatusCredencialRequest;
import br.com.json.bean.sistema.BloquearCredencialRequest;
import br.com.json.bean.sistema.BuscarSenhaCredencialRequest;
import br.com.json.bean.sistema.ConsultaSaldoResponse;
import br.com.json.bean.sistema.ConsultarSaldoContas;
import br.com.json.bean.sistema.ConsultarSaldoRequest;
import br.com.json.bean.sistema.DesbloquearCredencialRequest;
import br.com.json.bean.sistema.GenericResponse;
import br.com.json.bean.sistema.LoginResponse;
import br.com.json.bean.sistema.UraRequest;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.json.bean.suporte.Login;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.repository.suporte.AcessoLogRepository;
import br.com.sinergico.security.CriptoUtil;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.SpringContextUtils;
import br.com.sinergico.vo.sistema.InformacoesCredencialContaVO;
import com.google.common.base.Strings;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

@Service
public class SistemaService {

  private static final String LETRA_F = "F";

  private static final int POSICAO_ZERO = 0;

  private static final int TAMANHO_CPF = 11;

  private static final Logger log = LoggerFactory.getLogger(SistemaService.class);

  private static final int TAMANHO_CNPJ = 14;
  public static final String SUCESSO = "Login realizado com sucesso";
  public static final String SENHA_EXPIRADA = "Senha expirada";
  public static final String USUARIO_OU_SENHA_INVALIDO = "Usuário ou senha inválido";

  @Autowired private AcessoUsuarioService usuarioService;

  @Autowired private CredencialService credencialService;

  @Autowired private CredencialFacade credencialFacade;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private AcessoLogRepository acessoLogRepo;

  @Autowired private TravaServicosService travaServicosService;

  public ResponseEntity<?> doLogin(
      HttpServletRequest request, HttpServletResponse response, Login login) {

    if (request.getHeader("Proxy-Authorization") != null) {
      throw new GenericServiceException("Acesso impedido.", HttpStatus.UNAUTHORIZED);
    }

    LoginResponse resp = null;

    try {

      AcessoUsuario usuario = usuarioService.getUsuarioLogin(login.getLogin(), login.getSenha());
      HashMap<String, Object> map = new HashMap<>();

      if (usuario == null) {
        map.put("msg", USUARIO_OU_SENHA_INVALIDO);
        AcessoUsuario acessoUsuario = usuarioService.findByLoginSimples(login.getLogin());
        usuarioService.saveAcessoLog(
            acessoUsuario,
            request,
            HttpStatus.UNAUTHORIZED,
            USUARIO_OU_SENHA_INVALIDO + " | Informação interna - Login: " + login.getLogin());
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      }

      if (usuario.getSenhaExpirada() != null && usuario.getSenhaExpirada()) {
        map.put("senhaExpirada", true);
        usuarioService.saveAcessoLog(
            usuario,
            request,
            HttpStatus.FORBIDDEN,
            SENHA_EXPIRADA + " | Informação interna - Login: " + login.getLogin());
        return new ResponseEntity<>(map, HttpStatus.FORBIDDEN);
      }

      try {
        travaServicosService.travaServicos(usuario.getIdInstituicao(), Servicos.LOGIN_ISSUER);
      } catch (GenericServiceException e) {
        map.put("msg", e.getMensagem());
        usuarioService.saveAcessoLog(
            usuario,
            request,
            e.getHttpStatus(),
            e.getMensagem() + " | Informação interna - Login: " + login.getLogin());
        return new ResponseEntity<>(map, e.getHttpStatus());
      }

      AcessoLog ultimoAcesso =
          acessoLogRepo.findFirstByIdUsuarioOrderByDtHrAcessoDesc(usuario.getIdUsuario());
      String token = usuarioService.makeAuthenication(request, response, usuario, ultimoAcesso);
      usuario.setDtHrUltimoAcesso(new Date());
      usuario = usuarioService.save(usuario);

      resp = new LoginResponse(Boolean.TRUE, token);

      usuarioService.saveAcessoLog(usuario, request, HttpStatus.OK, SUCESSO);

    } catch (GenericServiceException e) {

      List<String> erros = new ArrayList<>();
      erros.add(e.getMensagem());

      throw new SistemaExternoServiceException(erros, e.getDetalhes());
    }

    return new ResponseEntity<>(resp, HttpStatus.OK);
  }

  public Boolean isSenhaCorreta(String senha, Credencial credencial, String tokenJWT) {
    return credencialService.validarPin(senha, credencial, tokenJWT);
  }

  private String getDocumentoDescriptografado(
      String documento, Integer idProcessadora, Integer idInstituicao) {
    String documentoDescriptografado = null;

    try {

      String aliasKey = getAliasKey(idProcessadora, idInstituicao);
      documentoDescriptografado = CriptoUtil.getConteudoEmClaro(documento, aliasKey);

      if (documentoDescriptografado != null) {
        int positionRequired = documentoDescriptografado.indexOf(LETRA_F);

        if ((positionRequired == -1 || positionRequired == TAMANHO_CPF)
            && documentoDescriptografado.length() >= TAMANHO_CPF) {
          documentoDescriptografado =
              documentoDescriptografado.substring(POSICAO_ZERO, TAMANHO_CPF);
        } else {
          documentoDescriptografado =
              documentoDescriptografado.substring(POSICAO_ZERO, TAMANHO_CNPJ);
        }
      }

    } catch (Exception e) {
      e.printStackTrace();
    }

    return documentoDescriptografado;
  }

  private String getAliasKey(Integer idProcessadora, Integer idInstituicao) {
    StringBuilder chaveAlias = new StringBuilder();
    chaveAlias.append("issuer.ura.security.alias.");
    chaveAlias.append(
        Strings.padStart(
            idProcessadora.toString(),
            Constantes.MIN_LENGHT_ID_PROCESSADORA,
            Constantes.CHAR_ZERO));
    chaveAlias.append(
        Strings.padStart(
            idInstituicao.toString(), Constantes.MIN_LENGHT_ID_INSTITUICAO, Constantes.CHAR_ZERO));

    //		pegar a propriedade do standalone conforme a processadora e instituicao
    Environment env = SpringContextUtils.getBean(Environment.class);
    String propAlias = env.getProperty(chaveAlias.toString());

    if (propAlias == null) {
      throw new GenericServiceException("Propriedade não configurada. " + chaveAlias.toString());
    }

    return propAlias;
  }

  public ResponseEntity<?> desbloquearCredencial(
      DesbloquearCredencialRequest request, AcessoUsuario usuario, String tokenJWT) {

    try {

      log.info("iniciando desbloqueio de credencial. Ult4: " + request.getUltimosDigitos());

      String documentoEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), usuario.getIdProcessadora(), usuario.getIdInstituicao());

      if (documentoEmClaro == null || documentoEmClaro.isEmpty()) {

        throw new GenericServiceException(
            "Não foi possível recuperar o DOCUMENTO. ",
            "Problemas com a descriptografia do DOCUMENTO. ");
      }

      log.debug("conseguiu descriptografar documento: " + documentoEmClaro);

      request.setCpf(documentoEmClaro);

      List<Credencial> credenciais =
          credencialService.findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
              documentoEmClaro, request.getUltimosDigitos());

      if (credenciais == null || credenciais.isEmpty()) {

        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível desbloquear Credencial.Credencial Não encontrada.");

        throw new SistemaExternoServiceException(
            erros,
            "Credencial não encontrada.CPF: "
                + request.getCpf()
                + " , ultimos4Digitos: "
                + request.getUltimosDigitos());

      } else if (credenciais.size() > 1) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível desbloquear Credencial.Duas ou mais Credenciais encontradas.");

        throw new SistemaExternoServiceException(
            erros,
            "Duas ou mais Credenciais encontradas.CPF: "
                + request.getCpf()
                + " , ultimos4Digitos: "
                + request.getUltimosDigitos());
      }

      Credencial credencial = credenciais.get(POSICAO_ZERO);

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível desbloquear Credencial.Senha Inválida");

        String detalhes = "Senha Inválida.";
        throw new SistemaExternoServiceException(erros, detalhes);
      }

      DesbloquearCredencialCompleta input = new DesbloquearCredencialCompleta();
      input.setIdCredencial(credencial.getIdCredencial());
      input.setIdUsuario(usuario.getIdUsuario());

      contaPagamentoService.desbloquearCredencialCompleta(input);

    } catch (GenericServiceException e) {

      List<String> erros = new ArrayList<>();
      erros.add(e.getMensagem());

      throw new SistemaExternoServiceException(erros, e.getMensagem());
    }
    log.info(
        "finalizando desbloqueio de credencial.Ult4: "
            + request.getUltimosDigitos()
            + " documento: "
            + request.getCpf());
    return new ResponseEntity<>(new GenericResponse(Boolean.TRUE), HttpStatus.OK);
  }

  public ResponseEntity<?> bloquearCredencialPorRoubo(
      BloquearCredencialRequest request, AcessoUsuario usuario, String tokenJWT) {

    log.info("iniciando bloqueio de credencial por roubo. Ult4: " + request.getUltimosDigitos());

    Boolean resultado = false;

    try {

      String cpfEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), usuario.getIdProcessadora(), usuario.getIdInstituicao());

      if (cpfEmClaro == null || cpfEmClaro.isEmpty()) {

        throw new GenericServiceException(
            "Não foi possível recuperar o CPF. ", "Problemas com a descriptografia do CPF. ");
      }
      log.debug("conseguiu descriptografar cpf: " + cpfEmClaro);

      request.setCpf(cpfEmClaro);

      List<Credencial> credenciais =
          credencialService.findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
              cpfEmClaro, request.getUltimosDigitos());

      if (credenciais == null || credenciais.isEmpty()) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível bloquear Credencial.");

        throw new SistemaExternoServiceException(
            erros,
            "Credencial não encontrada.CPF: "
                + request.getCpf()
                + " , ultimos4Digitos: "
                + request.getUltimosDigitos());
      }

      Credencial credencial = credenciais.get(POSICAO_ZERO);

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível bloquear Credencial.Senha Inválida");

        String detalhes = "Senha Inválida.";
        throw new SistemaExternoServiceException(erros, detalhes);
      }

      resultado =
          credencialFacade.avisarRoubo(credencial.getIdCredencial(), usuario.getIdUsuario(), true);

    } catch (GenericServiceException e) {

      List<String> erros = new ArrayList<>();
      erros.add(e.getMensagem());

      throw new SistemaExternoServiceException(erros, e.getDetalhes());
    }
    log.info(
        "finalizando bloqueio de credencial por roubo.Ult4: "
            + request.getUltimosDigitos()
            + " documento: "
            + request.getCpf());

    return new ResponseEntity<>(new GenericResponse(resultado), HttpStatus.OK);
  }

  public ResponseEntity<?> bloquearCredencialPorPerda(
      BloquearCredencialRequest request, AcessoUsuario usuario, String tokenJWT) {
    log.info("iniciando bloqueio de credencial por perda. Ult4: " + request.getUltimosDigitos());

    Boolean resultado = false;

    try {
      String cpfEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), usuario.getIdProcessadora(), usuario.getIdInstituicao());

      if (cpfEmClaro == null || cpfEmClaro.isEmpty()) {

        throw new GenericServiceException(
            "Não foi possível recuperar o CPF. ", "Problemas com a descriptografia do CPF. ");
      }

      request.setCpf(cpfEmClaro);
      log.debug("conseguiu descriptografar cpf: " + cpfEmClaro);

      List<Credencial> credenciais =
          credencialService.findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
              cpfEmClaro, request.getUltimosDigitos());

      if (credenciais == null || credenciais.isEmpty()) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível bloquear Credencial.");

        throw new SistemaExternoServiceException(
            erros,
            "Credencial não encontrada.CPF: "
                + request.getCpf()
                + " , ultimos4Digitos: "
                + request.getUltimosDigitos());
      }

      Credencial credencial = credenciais.get(POSICAO_ZERO);

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível bloquear Credencial.Senha Inválida");

        String detalhes = "Senha Inválida.";
        throw new SistemaExternoServiceException(erros, detalhes);
      }

      resultado =
          credencialFacade.avisarPerda(credencial.getIdCredencial(), usuario.getIdUsuario(), true);

    } catch (GenericServiceException e) {

      List<String> erros = new ArrayList<>();
      erros.add(e.getMensagem());

      throw new SistemaExternoServiceException(erros, e.getDetalhes());
    }

    log.info(
        "finalizando bloqueio de credencial por perda Ult4: "
            + request.getUltimosDigitos()
            + " documento: "
            + request.getCpf());

    return new ResponseEntity<>(new GenericResponse(resultado), HttpStatus.OK);
  }

  public ResponseEntity<?> consultarSaldo(
      ConsultarSaldoRequest request, AcessoUsuario usuario, String tokenJWT) {
    log.info("iniciando consulta de saldo. Ult4: " + request.getUltimosDigitos());

    BigDecimal saldo = BigDecimal.ZERO;

    try {

      String cpfEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), usuario.getIdProcessadora(), usuario.getIdInstituicao());

      if (cpfEmClaro == null || cpfEmClaro.isEmpty()) {

        throw new GenericServiceException(
            "Não foi possível recuperar o CPF. ", "Problemas com a descriptografia do CPF. ");
      }

      request.setCpf(cpfEmClaro);
      log.debug("conseguiu descriptografar cpf: " + cpfEmClaro);

      List<Credencial> credenciais =
          credencialService.findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
              cpfEmClaro, request.getUltimosDigitos());

      if (credenciais == null || credenciais.isEmpty()) {

        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível Consultar Saldo. Credencial não encontrada");

        throw new SistemaExternoServiceException(
            erros,
            "Credencial não encontrada.CPF: "
                + request.getCpf()
                + " , ultimos4Digitos: "
                + request.getUltimosDigitos());
      }

      Credencial credencial = credenciais.get(POSICAO_ZERO);
      Long idConta = credencial.getIdConta();

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível Consultar Saldo.Senha Inválida");

        String detalhes = "Senha Inválida.";
        throw new SistemaExternoServiceException(erros, detalhes);
      }

      ContaPagamento conta = contaPagamentoService.findById(idConta);

      if (conta == null) {
        List<String> erros = new ArrayList<>();
        erros.add("Não foi possível obter saldo da Conta. ");
        throw new SistemaExternoServiceException(
            erros, "Conta não Encontrada. IdConta: " + idConta);
      }

      GetSaldoConta getSaldo = contaPagamentoService.getSaldoConta(conta.getIdConta());
      saldo = getSaldo.getSaldoDisponivel();

    } catch (GenericServiceException e) {
      List<String> erros = new ArrayList<>();
      erros.add(e.getMensagem());

      throw new SistemaExternoServiceException(erros, e.getDetalhes());
    }
    log.info(
        "finalizando consulta de saldo Ult4: "
            + request.getUltimosDigitos()
            + " documento: "
            + request.getCpf());

    return new ResponseEntity<>(new ConsultaSaldoResponse(Boolean.TRUE, saldo), HttpStatus.OK);
  }

  public ResponseEntity<?> validarRequestUra(
      UraRequest request, AcessoUsuario usuario, String tokenJWT, BindingResult result) {

    try {

      if (result.hasErrors()) {
        throw new GenericServiceException(result.getFieldError().getDefaultMessage());
      }

      String cpfEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), usuario.getIdProcessadora(), usuario.getIdInstituicao());

      if (cpfEmClaro == null || cpfEmClaro.isEmpty()) {

        throw new GenericServiceException(
            "Não foi possível recuperar o CPF. ", "Problemas com a descriptografia do CPF. ");
      }

      request.setCpf(cpfEmClaro);
      log.info("conseguiu descriptografar cpf: " + cpfEmClaro);

      List<Credencial> credenciais =
          credencialService.findCredsByDocumentoAndUltimos4DigitosNaoVirtual(
              cpfEmClaro, request.getUltimosDigitos());

      if (credenciais == null || credenciais.isEmpty()) {
        List<String> erros = new ArrayList<>();
        erros.add("Credencial não encontrada.");

        throw new SistemaExternoServiceException(
            erros,
            "Credencial não encontrada.CPF: "
                + request.getCpf()
                + " , ultimos4Digitos: "
                + request.getUltimosDigitos());
      }

      Credencial credencial = credenciais.get(POSICAO_ZERO);

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        List<String> erros = new ArrayList<>();
        erros.add("Senha Inválida");

        String detalhes = "Senha Inválida.";
        throw new SistemaExternoServiceException(erros, detalhes);
      }

    } catch (GenericServiceException e) {
      List<String> erros = new ArrayList<>();
      erros.add(e.getMensagem());

      throw new SistemaExternoServiceException(erros, e.getDetalhes());
    }
    return new ResponseEntity<>(new GenericResponse(Boolean.TRUE), HttpStatus.OK);
  }

  /***
   * Realiza busca de contas multibeneficios para o documento informado
   * por ser tratar de 'multicontas', usa-se uma credencial para varias contas de varios produtos da instituicao
   * @param request
   * @param user
   * @param tokenJWT
   * @return
   */
  public ResponseEntity<HashMap<String, String>> alterarStatusCredencial(
      AlterarStatusCredencialRequest request, SecurityUser user, String tokenJWT) {

    HashMap<String, String> map = new HashMap<>();
    try {

      String documentoEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), user.getIdProcessadora(), user.getIdInstituicao());

      if (documentoEmClaro == null || documentoEmClaro.isEmpty()) {
        log.error(ConstantesErro.URA_ERRO_RECUPERAR_DOCUMENTO.getMensagem());
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_RECUPERAR_DOCUMENTO.getMensagem(), HttpStatus.NOT_FOUND);
      }

      request.setCpf(documentoEmClaro);

      List<InformacoesCredencialContaVO> contas =
          credencialService.findContasByDocumentoAndUltimos4DigitosNaoVirtualMulticontas(
              request.getCpf(),
              user.getIdInstituicao(),
              user.getIdProcessadora(),
              request.getUltimosDigitos());

      if (contas == null || contas.isEmpty()) {
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.format("Nenhuma conta encontrada."),
            HttpStatus.NOT_FOUND);
      }

      Credencial credencial =
          credencialService.findByIdCredencial(contas.get(POSICAO_ZERO).getIdCredencial());

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.format("Senha inválida."),
            HttpStatus.UNPROCESSABLE_ENTITY);
      }

      if (!Constantes.TIPO_STATUS_BLOQUEIO_TEMPORARIO.equals(request.getStatusDestino())
          && !Constantes.TIPO_STATUS_DESBLOQUEADO.equals(request.getStatusDestino())) {
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_STATUS_DESTINO_FORA_DO_PERMITIDO.getMensagem(),
            HttpStatus.UNPROCESSABLE_ENTITY);
      }

      if (request.getStatusDestino().equals(credencial.getStatus())) {
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_STATUS_JA_ESCOLHIDO.getMensagem(),
            HttpStatus.UNPROCESSABLE_ENTITY);
      }

      contaPagamentoService.alterarStatusCredencialURA(
          credencial, user, request.getStatusDestino());

      map.put("msg", "Status do cartão alterado com sucesso!");
    } catch (GenericServiceException e) {
      log.error(ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.format(e.getMessage()));
      throw new GenericServiceException(e.getMessage(), e.getHttpStatus());
    } catch (Exception e) {
      log.error(ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.format(e.getMessage()));
      throw new GenericServiceException(
          ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.format(e.getMessage()),
          HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  public List<ConsultarSaldoContas> consultarSaldoContas(
      ConsultarSaldoRequest request, SecurityUser usuario, String tokenJWT) {

    List<ConsultarSaldoContas> saldos = new ArrayList<>();

    try {

      String documentoEmClaro =
          getDocumentoDescriptografado(
              request.getCpf(), usuario.getIdProcessadora(), usuario.getIdInstituicao());

      if (documentoEmClaro == null || documentoEmClaro.isEmpty()) {

        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_RECUPERAR_DOCUMENTO.getMensagem());
      }

      request.setCpf(documentoEmClaro);

      List<InformacoesCredencialContaVO> contas =
          credencialService.findContasByDocumentoAndUltimos4DigitosNaoVirtualMulticontas(
              request.getCpf(),
              usuario.getIdInstituicao(),
              usuario.getIdProcessadora(),
              request.getUltimosDigitos());

      if (contas == null || contas.isEmpty()) {
        throw new GenericServiceException(
            ConstantesErro.URA_CONTA_NAO_ENCONTRADA.getMensagem(), HttpStatus.NOT_FOUND);
      }

      Credencial credencial =
          credencialService.findByIdCredencial(contas.get(POSICAO_ZERO).getIdCredencial());

      if (!isSenhaCorreta(request.getSenha(), credencial, tokenJWT)) {
        throw new GenericServiceException(
            ConstantesErro.URA_SENHA_INVALIDA.getMensagem(), HttpStatus.UNPROCESSABLE_ENTITY);
      }

      for (InformacoesCredencialContaVO conta : contas) {

        ConsultarSaldoContas saldo = new ConsultarSaldoContas();
        saldo.setSaldo(conta.getSaldoDisponivel());
        saldo.setDescProdutoInstituicao(conta.getDescProdInstituicao());
        saldos.add(saldo);
      }
    } catch (GenericServiceException e) {

      log.error(e.getMensagem());
      throw new GenericServiceException(e.getMensagem(), e.getHttpStatus());

    } catch (Exception e) {
      throw new GenericServiceException(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return saldos;
  }

  public ResponseEntity<HashMap<String, String>> buscarSenhaCredencial(
      BuscarSenhaCredencialRequest dadosCredencialRequest, SecurityUser user, String tokenJWT) {

    HashMap<String, String> map = new HashMap<>();
    try {

      String documentoEmClaro =
          getDocumentoDescriptografado(
              dadosCredencialRequest.getCpf(), user.getIdProcessadora(), user.getIdInstituicao());

      if (documentoEmClaro == null || documentoEmClaro.isEmpty()) {
        log.error(ConstantesErro.URA_ERRO_RECUPERAR_DOCUMENTO.getMensagem());
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_RECUPERAR_DOCUMENTO.getMensagem(), HttpStatus.NOT_FOUND);
      }

      dadosCredencialRequest.setCpf(documentoEmClaro);

      List<InformacoesCredencialContaVO> contas =
          credencialService.findContasByDocumentoAndUltimos4DigitosNaoVirtualMulticontas(
              dadosCredencialRequest.getCpf(),
              user.getIdInstituicao(),
              user.getIdProcessadora(),
              dadosCredencialRequest.getUltimosDigitos());

      if (contas == null || contas.isEmpty()) {
        throw new GenericServiceException(
            ConstantesErro.URA_CONTA_NAO_ENCONTRADA.getMensagem(), HttpStatus.NOT_FOUND);
      }

      Credencial credencial =
          credencialService.findByIdCredencial(contas.get(POSICAO_ZERO).getIdCredencial());

      String senhaCredencial = credencialService.buscarSenhaCredencialURA(credencial);

      map.put("msg", senhaCredencial);
    } catch (GenericServiceException e) {
      log.error(ConstantesErro.URA_ERRO_RECUPERAR_SENHA.format(e.getMessage()));
      throw new GenericServiceException(e.getMessage(), e.getHttpStatus());
    } catch (Exception e) {
      log.error(ConstantesErro.URA_ERRO_RECUPERAR_SENHA.format(e.getMessage()));
      throw new GenericServiceException(
          ConstantesErro.URA_ERRO_RECUPERAR_SENHA.format(e.getMessage()),
          HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(map, HttpStatus.OK);
  }
}
