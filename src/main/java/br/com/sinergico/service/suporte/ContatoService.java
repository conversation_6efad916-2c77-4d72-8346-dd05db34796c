package br.com.sinergico.service.suporte;

import br.com.entity.suporte.Contato;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.CadastrarContato;
import br.com.sinergico.repository.suporte.ContatoRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.totvs.api.PreRegistroContatoTotvsService;
import br.com.sinergico.vo.CentralAtendimentoVO;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class ContatoService extends GenericService<Contato, Long> {

  private static final int NIVEL_INSTITUICAO = 2;
  private static final int INATIVO = 9;
  private static final int ATIVO = 1;
  private ContatoRepository contatoRepository;

  @Autowired private HierarquiaInstituicaoRepository hierarquiaInstituicaoRepository;

  @Qualifier("preRegistroContatoTotvsService")
  @Autowired
  private PreRegistroContatoTotvsService preRegistroContatoTotvsService;

  @Autowired
  public ContatoService(ContatoRepository repo) {
    super(repo);
    contatoRepository = repo;
  }

  /**
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @param status
   * @return
   */
  public List<Contato> getContatosHierarquiaPorStatus(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer status) {
    return contatoRepository
        .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndStatus(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento, status);
  }

  /**
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  public List<Contato> getContatosHierarquia(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contatoRepository
        .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamento(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  /**
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  public List<Contato> getContatosHierarquiaOrderByData(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contatoRepository
        .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoOrderByDataHoraStatus(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  /**
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoDeRelacionamento
   * @return
   */
  public List<Contato> getContatosHierarquiaOrderByIdContato(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contatoRepository
        .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoOrderByIdContato(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  public Boolean inativarContato(Long idContato, Integer idUsuarioManutencao) {

    return alterarStatusContato(idContato, idUsuarioManutencao, INATIVO);
  }

  public Boolean ativarContato(Long idContato, Integer idUsuarioManutencao) {
    return alterarStatusContato(idContato, idUsuarioManutencao, ATIVO);
  }

  private Boolean alterarStatusContato(Long idContato, Integer idUsuarioManutencao, int status) {
    Contato contato = findContatoNotNullById(idContato);
    contato.setDataHoraStatus(LocalDateTime.now());
    contato.setStatus(status);
    contato.setIdUsuarioManutencao(idUsuarioManutencao);
    preRegistroContatoTotvsService.atualizarContatoEmpresaTotvs(contato);

    return save(contato) != null;
  }

  public Boolean update(SecurityUser user, CadastrarContato model, Long idContato) {
    Contato contato = findContatoNotNullById(idContato);
    BeanUtils.copyProperties(model, contato, getNullPropertyNames(model));
    contato.setIdUsuarioManutencao(user.getIdUsuario());
    preRegistroContatoTotvsService.atualizarContatoEmpresaTotvs(contato);

    return save(contato) != null;
  }

  private Contato findContatoNotNullById(Long idContato) {
    Contato contato = contatoRepository.findById(idContato).orElse(null);

    if (contato == null) {
      throw new GenericServiceException(
          "Contato não encontrado. IdContato: " + idContato, HttpStatus.NOT_FOUND);
    }
    return contato;
  }

  public Boolean cadastrarContato(SecurityUser user, CadastrarContato model) {
    Contato contato = new Contato();
    BeanUtils.copyProperties(model, contato);
    contato.setIdUsuarioInclusao(user.getIdUsuario());
    contato.setDataHoraStatus(LocalDateTime.now());
    contato.setStatus(ATIVO);

    save(contato);

    preRegistroContatoTotvsService.preRegistrarContatoEmpresaTotvs(contato);

    return contato != null;
  }

  public List<Contato> getContatosInstituicao(Integer idProcessadora, Integer idInstituicao) {
    return contatoRepository.findByIdProcessadoraAndIdInstituicaoAndIdNivelHierarquia(
        idProcessadora, idInstituicao, NIVEL_INSTITUICAO);
  }

  public CentralAtendimentoVO buscarFaleConoscoInstituicao(
      Integer idProcessadora, Integer idInstituicao) {

    HierarquiaInstituicao hierarquiaInstituicao =
        hierarquiaInstituicaoRepository.findByIdProcessadoraAndIdInstituicao(
            idProcessadora, idInstituicao);
    CentralAtendimentoVO centralAtendimentoVO = new CentralAtendimentoVO();
    centralAtendimentoVO.setCentralAtendimento(
        hierarquiaInstituicao.getTextoInfCentralAtendimento());

    return centralAtendimentoVO;
  }
}
