AMBIENTE=homologacao

ISSUER_URL=http://localhost:28080

#CRONs
EXECUTAR_CRON_ENVIO_ALERTA_CONTAGARANTIA=false

EXECUTAR_ATUALIZAR_ORDENS_PAGAMENTOS=true

#Database
DB_URL=****************************************************
DB_USERNAME_ISSUER=usr_app_backend_hom

#Evento Jcard Basic Auth
EVENTO_JCARD_BASIC_USER=evento_jcard_basic_rest
EVENTO_JCARD_BASIC_PASS="%Sk*#WC3E91G"

#VALLOO AUTENTICADOR
USERNAME_API_VALLOO_AUTHENTICATOR=ADMIN
URL_VALLOO_AUTENTICADOR=http://auth1.valloo.int:8082

#CAF
USUARIO_API_CAF="<EMAIL>"

#TRAVA SERVICOS
SERVIDORES_LIMPAR_CACHE=http://devback1.valloo-dev.int:28080

#COBRANCA BOLETO
BOLETOREGISTRADO_BRBCARD_URL=https://servicos.brbcard.com.br/boleto
BOLETOREGISTRADO_BRADESCO_URL=https://cobranca.bradesconetempresa.b.br/ibpjregistrotitulows/registrotitulohomologacao
API_BRADESCO_URL_V1_1=https://proxy.api.prebanco.com.br
BOLETOREGISTRADO_BB_BASEURL=https://api.hm.bb.com.br
BOLETOREGISTRADO_BB_GWAPPKEY=?gw-dev-app-key=
BOLETOREGISTRADO_BB_AUTHURL=https://oauth.sandbox.bb.com.br/oauth/token
BOLETOREGISTRADO_BB_DEVAPPKEY=e640b3df55c856907bf893e145dbe779
BOLETOREGISTRADO_BB_CLIENTID=eyJpZCI6ImQ1ZjYwOTEtN2MyMi00N2I3LWJhIiwiY29kaWdvUHVibGljYWRvciI6MCwiY29kaWdvU29mdHdhcmUiOjc1MTY1LCJzZXF1ZW5jaWFsSW5zdGFsYWNhbyI6MX0
BOLETOREGISTRADO_BB_CLIENTSECRET=eyJpZCI6IjkwMzQwOTQtZmZhMC00ZTgzLTg4NDMiLCJjb2RpZ29QdWJsaWNhZG9yIjowLCJjb2RpZ29Tb2Z0d2FyZSI6NzUxNjUsInNlcXVlbmNpYWxJbnN0YWxhY2FvIjoxLCJzZXF1ZW5jaWFsQ3JlZGVuY2lhbCI6MSwiYW1iaWVudGUiOiJob21vbG9nYWNhbyIsImlhdCI6MTY5NTkwMzM2Mzg1NH0
BOLETOREGISTRADO_BANESE_BASEURL=https://sandbox.banese.b.br

#PRONTO PAGUEI
SERVICE_PRONTOPAGUEI_URL=https://h.prontopaguei.com
SERVICE_PRONTOPAGUEI_TOKEN="Bearer $2y$10$pOsWtNao4g0P18AHotU5j.4EPl1/w7Lu2IfittLnuy.Hh6.8mduT2"

#ANTIFRAUDE
ANTIFRAUDE_BASIC_USER=antifraude_basic_rest

#RENDIMENTO
SERVICE_BANCORENDIMENTO_URL=https://apisandbox.rendimento.com.br
SERVICE_BANCORENDIMENTO_AUTH_URL=/oauth/access-token

#URL DE TROCA DE SENHA
URL_TROCA_SENHA=https://issuer.itspay-hom.com.br
URL_TROCA_SENHA_B2B=https://b2bclient.itspay-hom.com.br
URL_TROCA_SENHA_B2B_INFINANCAS=https://portal.itspay-hom.com.br
URL_TROCA_SENHA_B2B_JOYPOINTS=https://admin.joypoints.itspay-hom.com.br
URL_TROCA_SENHA_ACQUIRER_MERCHANT=https://merchant.itspay-hom.com.br/recadastro-senha

#JCARD REST API
JCARD_URL=http://devback1.valloo-dev.int:8081

#TOTVS API
TOTVS_API_URL=http://devback1.valloo-dev.int:28083
TOTVS_API_HEADER_TOKEN=TesteTotvs123

#ZENVIA ENVIO SMS
GATEWAY_ZENVIA_AUTH_USER=itspay.sms
GATEWAY_ZENVIA_PERMITEENVIAR=true

#DIRETORIO SEGURANCA
ISSUER_DIR_SECURITY_LMK=/plataforma/security/lmk/hom.lmk
ISSUER_DIR_SECURITY_CFG=/plataforma/security/cfg/hom-bdk.cfg
ISSUER_URA_SECURITY_ALIAS_101001=zpk.101001
ISSUER_URA_SECURITY_ALIAS_101201=zpk.101201
ISSUER_URA_SECURITY_ALIAS_102401=zpk.102401
ISSUER_URA_SECURITY_ALIAS_102601=zpk.102601
ISSUER_URA_SECURITY_ALIAS_100101=zpk.001

#PAYMENT PROCESS
PAYMENT_PROCESS_REST_URL=http://devback1.valloo-dev.int:28081/payment_process_rest

#NFSE URL
NFSE_URL=http://devback1.valloo-dev.int:28081/nfse/notafiscal-homolog-v2
NFSE_URL_INSTITUICAO=http://devback1.valloo-dev.int:28081/nfse/instituicao/configurar

#ISSUER ACQUIRER
IP_ACQUIRER=localhost
PORT_ACQUIRER=10040
ACQUIRER_REST_API_URL=http://devback1.valloo-dev.int:8083

#MARKETPLACE
MKTPLACE_URL=http://devback1.valloo-dev.int:28181

#URL EMAIL API
URL_EMAIL_API=http://devback1.valloo-dev.int:8037

#CRIPTOGRAFIA

#GATEWAY API
BR_COM_ITSPAY_GATEWAY_SERVICE_ANTIFRAUDSERVICE_MERCHANTID=7b827df9-5553-426b-b5d8-d3904b64daab
BR_COM_ITSPAY_GATEWAY_SERVICE_ANTIFRAUDSERVICE_CLIENTID=65fabc24-b6a3-4a53-a936-ca96398b6fb3
BR_COM_ITSPAY_GATEWAY_SERVICE_PAYMENTSSERVICE_ID=353acfcc-1305-4c62-997a-405a19f94aa6

#QRCODE
ELO_QRCODE_URL=https://ssl-hml-api.elo.com.br/payment/qr/v3

#QRCODE VALLOO
ELO_QRCODE_KEYSTORE_PATH=/plataforma/backend/arquivos/emissores/certificados/ssl-hml-bank10-api.elo.com.br.p12
ELO_QRCODE_KEYSTORE_PASSWORD=changeit
ELO_QRCODE_KEY_PASSWORD=changeit
ELO_QRCODE_TRUSTSTORE_PATH=/usr/lib/jvm/jre-openjdk/lib/security/cacerts

#QRCODE BRB
ELO_BRBCARD_QRCODE_KEYSTORE_PASSWORD=changeit
ELO_BRBCARD_QRCODE_KEY_PASSWORD=changeit
ELO_BRBCARD_QRCODE_KEYSTORE_PATH=/plataforma/backend/arquivos/emissores/certificados/ssl_hml_elo_api.brb.com.br.p12
ELO_BRBCARD_QRCODE_TRUSTSTORE_PATH=/usr/lib/jvm/jre-openjdk/lib/security/cacerts

#QRCODE MULVI
ELO_MULVI_QRCODE_KEYSTORE_PASSWORD=changeit
ELO_MULVI_QRCODE_KEY_PASSWORD=changeit
ELO_MULVI_QRCODE_KEYSTORE_PATH=/plataforma/backend/arquivos/emissores/certificados/ssl-hml-bank10-api.elo.com.br.p12
ELO_MULVI_QRCODE_TRUSTSTORE_PATH=/usr/lib/jvm/jre-openjdk/lib/security/cacerts

#INMAIS
URL_AMBIENTE_INMAIS=/homolog

#ELO VCN
ELO_VCN_URL=https://hml-api.elo.com.br/graphql
ELO_VCN_CLIENT_ID=78c4a218-7cb3-36a6-ba5e-abe4a39d6b0e
ELO_VCN_CLIENT_AUTH="Basic NzhjNGEyMTgtN2NiMy0zNmE2LWJhNWUtYWJlNGEzOWQ2YjBlOjU0N2VjZDc2LTU5NzAtM2U1Ny1hOWZlLTdhNGVmNTg2MzIyZg=="
ELO_VCN_USER_KEY_KID=8fb9157f37980bcb04070b5f18c922a73d9853efa044bf992dcdfde0a64e2eef
ELO_VCN_USER_KEY_X=LDLTKtHGNtflD9uulJRjHbNCrr8nAv1NK_rLzPMdk-k
ELO_VCN_USER_KEY_Y=vYC2yhjmASwLZ1RCBHoAwKeoE6A2jVi3_HlGW2pa5v0
ELO_VCN_USER_KEY_PRIVATE=7bifuKM_A7SYIWnSxSZtrkl73U2LERG71TJZR62_Cyo
ELO_VCN_USER_USERNAME=ptestebank10092311
ELO_VCN_USER_PASSWORD=abc5432109
ELO_VCN_BANK10_ID=37d8e720-e3ad-45f3-932b-04368de3817f

#SISTEMA
SYSTEM_LOG_PAYLOAD=true

#URL SITES VALLLO
URL_VALLOO_B2B_INFINANCAS=https://portal.itspay-hom.com.br
URL_VALLOO_B2B_CLIENT=https://b2bclient.itspay-hom.com.br
URL_VALLOO_ISSUER=https://issuer.itspay-hom.com.br
URL_LOGIN_UNICO=https://login.valloo-hom.com.br

#ENVIO EMAIL

#UNIDAS
SITE_UNIDAS_URL=http://hml-unidasclub.unidas.com.br/reset-senha

#REDIS
SPRING_REDIS_CLUSTER_NODES=ecache-login-valloo-devqa-001.ecache-login-valloo-devqa.gzwxvk.use1.cache.amazonaws.com:6379,ecache-login-valloo-devqa-002.ecache-login-valloo-devqa.gzwxvk.use1.cache.amazonaws.com:6379

#Celcoin
API_CELCOIN_CLIENTID=41b44ab9a56440.teste.celcoinapi.v5
API_CELCOIN_SECRETID=e9d15cde33024c1494de7480e69b7a18c09d7cd25a8446839b3be82a56a044a3
API_CELCOIN_URL=https://sandbox.openfinance.celcoin.dev
API_CELCOIN_KEYSTORE=/plataforma/backend/arquivos/emissores/certificados/oieger2m4x.bank10.celcoinapi.v5.pfx
API_CELCOIN_KEYSTORE_PASSWORD=yMo4GiSv3U07Cnok
API_CELCOIN_TRUSTSTORE=/plataforma/backend/arquivos/emissores/certificados/cacerts

API_CELCOIN_TRUSTSTORE_PASSWORD=changeit

