package br.com.json.bean.transacional;

import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

@Data
public class CadastroCargaEstornoBRB implements Serializable {

  private static final long serialVersionUID = 4465547895868874172L;

  @NotNull
  @Min(0)
  private Long idConta;

  @NotNull private BigDecimal valor;

  @Length(max = 100)
  private String textoComentario;

  @Length(max = 100)
  private String textoExtrato;

  @Min(
      value = 230101000000000L,
      message = "Crédito não realizado. UID de Transação informado é inválido.")
  private Long transactionUid;
}
