package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.PortadorLoginConta;
import br.com.entity.cadastral.PortadorLoginContaId;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PortadorLoginContaRepository
    extends JpaRepository<PortadorLoginConta, PortadorLoginContaId> {

  String PESSOA_FISICA = "1";
  String PESSOA_JURIDICA = "2";
  String PESSOA_TITULAR = "1";
  String PESSOA_ADICIONAL = "2";

  @Query(
      value =
          " select distinct plc.portadorLoginContaId.idConta "
              + " from PortadorLoginConta plc "
              + " where plc.portadorLoginContaId.idLogin = :idLogin "
              + " order by plc.portadorLoginContaId.idConta ")
  List<Long> buscarIdsContaPagamentoAssociadosAoIdLogin(@Param("idLogin") Long idLogin);

  @Query(
      value =
          " select plc.contaPagamento "
              + " from PortadorLoginConta plc "
              + " where plc.portadorLoginContaId.idLogin = :idLogin ")
  List<ContaPagamento> buscarContasPagamentoAssociadosAoIdLogin(@Param("idLogin") Long idLogin);

  // Busca as contas de um portador pela regra RegraTipoPortadorEnum.SIMPLES
  @Query(
      value =
          " select distinct cast(cpag.id_conta as bigint) \n "
              + " from cadastral.conta_pagamento cpag \n "
              + " inner join cadastral.conta_pessoa cp on cp.id_conta = cpag.id_conta and cp.id_titularidade = 1 \n "
              + " inner join cadastral.pessoa p on cp.id_pessoa = p.id_pessoa \n "
              + " inner join cadastral.produto_instituicao_configuracao pic on pic.id_prod_instituicao = cpag.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlp on tlp.id_prod_instituicao = pic.id_prod_instituicao \n "
              + " where tlp.tipo_login = :tipoLogin \n "
              + " \t and cpag.id_processadora = :idProcessadora \n "
              + " \t and cpag.id_instituicao = :idInstituicao \n "
              + " \t and p.documento = :documento \n "
              + " \t and p.tipo_pessoa = :tipoPessoa \n "
              + " order by cast(cpag.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContasRegraSimples(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa,
      @Param("documento") String documento,
      @Param("tipoLogin") String tipoLogin);

  // // Busca as contas de um portador pela regra RegraTipoPortadorEnum.PESSOA_ADICIONAL_PF_CONTA_PJ
  @Query(
      value =
          " select distinct cast(cpagAdc.id_conta as bigint) \n "
              + " from cadastral.pessoa pAdc \n "
              + " inner join cadastral.conta_pessoa cpesAdc on cpesAdc.id_pessoa = pAdc.id_pessoa \n "
              + " inner join cadastral.conta_pagamento cpagAdc on cpagAdc.id_conta = cpesAdc.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picAdc on picAdc.id_prod_instituicao = cpagAdc.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpAdc on tlpAdc.id_prod_instituicao = picAdc.id_prod_instituicao \n "
              + " inner join cadastral.pessoa ptit on ptit.id_processadora = pAdc.id_processadora and ptit.id_instituicao = pAdc.id_instituicao \n "
              + " inner join cadastral.conta_pessoa cpesTit on cpesTit.id_pessoa = ptit.id_pessoa and cpagAdc.id_conta = cpesTit.id_conta \n "
              + " inner join cadastral.conta_pagamento cpagTit on cpagTit.id_conta = cpesTit.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picTit on picTit.id_prod_instituicao = cpagTit.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpTit on tlpTit.id_prod_instituicao = picTit.id_prod_instituicao \n "
              + " where tlpTit.tipo_login = :tipoLogin \n "
              + " \t and tlpAdc.tipo_login = :tipoLogin \n "
              + " \t and pAdc.id_processadora = :idProcessadora and pAdc.id_instituicao = :idInstituicao \n "
              + " \t and pAdc.documento = :documentoAdicional and ptit.documento = :documentoTitular \n "
              + " \t and pAdc.tipo_pessoa = "
              + PESSOA_FISICA
              + " and ptit.tipo_pessoa = "
              + PESSOA_JURIDICA
              + " \n "
              + " \t and cpesAdc.id_titularidade = "
              + PESSOA_ADICIONAL
              + " and cpesTit.id_titularidade = "
              + PESSOA_TITULAR
              + " \n "
              + " order by cast(cpagAdc.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContasRegraPessoaAdicionalPfPessoaPj(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoTitular") String documentoTitular,
      @Param("documentoAdicional") String documentoAdicional,
      @Param("tipoLogin") String tipoLogin);

  // Busca as contas de um portador pela regra RegraTipoPortadorEnum.REPRESENTANTE_LEGAL
  @Query(
      value =
          " select distinct cast(cpag.id_conta as bigint) \n "
              + " from cadastral.representante_legal rl \n "
              + " inner join cadastral.conta_pagamento cpag on cpag.id_conta = rl.id_conta \n "
              + " inner join cadastral.conta_pessoa cp on cp.id_conta = cpag.id_conta and cp.id_titularidade = 1 \n "
              + " inner join cadastral.pessoa p on cp.id_pessoa = p.id_pessoa \n "
              + " inner join cadastral.produto_instituicao_configuracao pic on pic.id_prod_instituicao = cpag.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlp on tlp.id_prod_instituicao = pic.id_prod_instituicao \n "
              + " where tlp.tipo_login = :tipoLogin \n "
              + " \t and cpag.id_processadora = :idProcessadora \n "
              + " \t and cpag.id_instituicao = :idInstituicao \n "
              + " \t and p.documento = :documentoEmpresa \n "
              + " \t and rl.cpf = :documentoRepresentante \n "
              + " order by cast(cpag.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContasRegraRepresentanteLegal(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoEmpresa") String documentoEmpresa,
      @Param("documentoRepresentante") String documentoRepresentante,
      @Param("tipoLogin") String tipoLogin);

  // Busca as contas de um portador pela regra RegraTipoPortadorEnum.MULTIBENEFICIOS
  @Query(
      value =
          " select distinct cast(cpag.id_conta as bigint) \n "
              + " from cadastral.conta_pagamento cpag \n "
              + " inner join cadastral.produto_instituicao_grupo pig on pig.id_produto_instituicao = cpag.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlp on tlp.id_prod_instituicao = pig.id_produto_instituicao \n "
              + " inner join cadastral.conta_pessoa cp on cp.id_conta = cpag.id_conta and cp.id_titularidade = 1 \n "
              + " inner join cadastral.pessoa p on cp.id_pessoa = p.id_pessoa \n "
              + " where tlp.tipo_login = :tipoLogin \n "
              + " \t and cpag.id_processadora = :idProcessadora \n "
              + " \t and cpag.id_instituicao = :idInstituicao \n "
              + " \t and p.documento = :documento \n "
              + " \t and pig.id_grupo = :grupoAcesso \n "
              + " \t and p.tipo_pessoa = :tipoPessoa \n "
              + " order by cast(cpag.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContaPagamentoRegraMultibeneficios(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa,
      @Param("documento") String documento,
      @Param("grupoAcesso") Long grupoAcesso,
      @Param("tipoLogin") String tipoLogin);

  // Busca as contas de um portador pela regra
  // RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL
  @Query(
      value =
          " select distinct cast(cpag.id_conta as bigint) \n "
              + " from cadastral.representante_legal rl \n "
              + " inner join cadastral.conta_pagamento cpag on rl.id_conta = cpag.id_conta \n "
              + " inner join cadastral.produto_instituicao_grupo pig on pig.id_produto_instituicao = cpag.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlp on tlp.id_prod_instituicao = pig.id_produto_instituicao \n "
              + " inner join cadastral.conta_pessoa cpes on cpes.id_conta = cpag.id_conta and cpes.id_titularidade = 1 \n "
              + " inner join cadastral.pessoa p on p.id_pessoa = cpes.id_pessoa \n "
              + " where tlp.tipo_login = :tipoLogin \n "
              + " \t and p.documento = :documentoEmpresa \n "
              + " \t and rl.cpf = :documentoRepresentante \n "
              + " \t and pig.id_grupo = :grupoAcesso \n "
              + " \t and (p.id_processadora, p.id_instituicao) = (:idProcessadora, :idInstituicao) \n "
              + " order by cast(cpag.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContaPagamentoRegraMultibeneficiosRepresentanteLegal(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoEmpresa") String documentoEmpresa,
      @Param("documentoRepresentante") String documentoRepresentante,
      @Param("grupoAcesso") Long grupoAcesso,
      @Param("tipoLogin") String tipoLogin);

  // Busca as contas RESPONSAVEL, ainda nao atreladas ao RESPONSAVEL, a partir do portador
  // RESPONSAVEL pela regra RegraTipoPortadorEnum.RESPONSAVEL
  @Query(
      value =
          " select distinct cast(cpagResp.id_conta as bigint) \n "
              + " from cadastral.pessoa pResp \n "
              + " inner join cadastral.conta_pessoa cpesResp on cpesResp.id_pessoa = pResp.id_pessoa and cpesResp.id_titularidade = 1 \n "
              + " inner join cadastral.conta_pagamento cpagResp on cpagResp.id_conta = cpesResp.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picResp on picResp.id_prod_instituicao = cpagResp.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpResp on tlpResp.id_prod_instituicao = picResp.id_prod_instituicao \n "
              + " inner join cadastral.produto_instituicao_dependentes pid on pid.id_produto_responsavel = picResp.id_prod_instituicao \n "
              + " where tlpResp.tipo_login = :tipoLoginResponsavel \n "
              + // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
              " \t and pResp.documento = :documentoResponsavel \n "
              + " \t and (pResp.id_processadora, pResp.id_instituicao) = (:idProcessadora, :idInstituicao) \n "
              + " \t and not exists ( \n "
              + " \t\t select * from cadastral.portador_login_conta pcResp \n "
              + " \t\t where pcResp.id_login = :idLoginResponsavel and pcResp.id_conta = cpesResp.id_conta \n "
              + " \t ) \n "
              + " order by cast(cpagResp.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContaPagamentoResponsavelNaoAtreladasAoResponsavelAtravesDoResponsavel(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoResponsavel") String documentoResponsavel,
      @Param("idLoginResponsavel") Long idLoginResponsavel,
      @Param("tipoLoginResponsavel")
          String tipoLoginResponsavel // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
      );

  // Busca as contas DEPENDENTE, ainda nao atreladas ao RESPONSAVEL, a partir do portador
  // RESPONSAVEL pela regra RegraTipoPortadorEnum.RESPONSAVEL
  @Query(
      value =
          " select distinct cast(cpagDep.id_conta as bigint) \n "
              + " from cadastral.pessoa pResp \n "
              + " inner join cadastral.conta_pessoa cpesResp on cpesResp.id_pessoa = pResp.id_pessoa and cpesResp.id_titularidade = 1 \n "
              + " inner join cadastral.conta_pagamento cpagResp on cpagResp.id_conta = cpesResp.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picResp on picResp.id_prod_instituicao = cpagResp.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpResp on tlpResp.id_prod_instituicao = picResp.id_prod_instituicao \n "
              + " inner join cadastral.pessoa_responsavel_dependente prd on pResp.id_pessoa = prd.id_pessoa_responsavel \n "
              + " inner join cadastral.conta_pessoa cpesDep on cpesDep.id_pessoa = prd.id_pessoa_dependente and cpesDep.id_titularidade = 1 \n "
              + " inner join cadastral.conta_pagamento cpagDep on cpagDep.id_conta = cpesDep.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picDep on picDep.id_prod_instituicao = cpagDep.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpDep on tlpDep.id_prod_instituicao = picDep.id_prod_instituicao \n "
              + " inner join cadastral.produto_instituicao_dependentes pid on pid.id_produto_dependente = picDep.id_prod_instituicao \n "
              + " where tlpResp.tipo_login = :tipoLoginResponsavel \n "
              + // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
              " \t and tlpDep.tipo_login = :tipoLoginDependente \n "
              + // A principio somente usar tipoLoginDependente = DEPENDENTE
              " \t and pResp.documento = :documentoResponsavel \n "
              + " \t and (pResp.id_processadora, pResp.id_instituicao) = (:idProcessadora, :idInstituicao) \n "
              + " \t and not exists ( \n "
              + " \t\t select * from cadastral.portador_login_conta pcDep \n "
              + " \t\t where pcDep.id_login = :idLoginResponsavel and pcDep.id_conta = cpagDep.id_conta \n "
              + " \t ) \n "
              + " order by cast(cpagDep.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoResponsavel(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoResponsavel") String documentoResponsavel,
      @Param("idLoginResponsavel") Long idLoginResponsavel,
      @Param("tipoLoginResponsavel")
          String
              tipoLoginResponsavel, // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
      @Param("tipoLoginDependente")
          String tipoLoginDependente // A principio somente usar tipoLoginDependente = DEPENDENTE
      );

  // Busca as contas DEPENDENTE, ainda nao atreladas ao DEPENDENTE, a partir do portador DEPENDENTE
  // pela regra RegraTipoPortadorEnum.DEPENDENTE
  @Query(
      value =
          " select distinct cast(cpagDep.id_conta as bigint) \n "
              + " from cadastral.pessoa pDep \n "
              + " inner join cadastral.conta_pessoa cpesDep on cpesDep.id_pessoa = pDep.id_pessoa and cpesDep.id_titularidade = 1 \n "
              + " inner join cadastral.conta_pagamento cpagDep on cpagDep.id_conta = cpesDep.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picDep on picDep.id_prod_instituicao = cpagDep.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpDep on tlpDep.id_prod_instituicao = picDep.id_prod_instituicao \n "
              + " inner join cadastral.pessoa_responsavel_dependente prd on pDep.id_pessoa = prd.id_pessoa_dependente \n "
              + " inner join cadastral.conta_pessoa cpesResp on cpesResp.id_pessoa = prd.id_pessoa_responsavel and cpesResp.id_titularidade = 1 \n "
              + " inner join cadastral.conta_pagamento cpagResp on cpagResp.id_conta = cpesResp.id_conta \n "
              + " inner join cadastral.produto_instituicao_configuracao picResp on picResp.id_prod_instituicao = cpagResp.id_prod_instituicao \n "
              + " inner join cadastral.tipo_login_produto tlpResp on tlpResp.id_prod_instituicao = picResp.id_prod_instituicao \n "
              + " inner join cadastral.produto_instituicao_dependentes pid on pid.id_produto_dependente = picDep.id_prod_instituicao \n "
              + " \t and pid.id_produto_responsavel = picResp.id_prod_instituicao \n "
              + " where tlpResp.tipo_login = :tipoLoginResponsavel \n "
              + // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
              " \t and tlpDep.tipo_login = :tipoLoginDependente \n "
              + // A principio somente usar tipoLoginDependente = DEPENDENTE
              " \t and pDep.documento = :documentoDependente \n "
              + " \t and (pDep.id_processadora, pDep.id_instituicao) = (:idProcessadora, :idInstituicao) \n "
              + " \t and not exists ( \n "
              + " \t\t select * from cadastral.portador_login_conta pcDep \n "
              + " \t\t where pcDep.id_login = :idLoginDependente and pcDep.id_conta = cpagDep.id_conta \n "
              + " \t ) \n "
              + " order by cast(cpagDep.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContaPagamentoDependentesNaoAtreladasAoDependenteAtravesDoDependente(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoDependente") String documentoDependente,
      @Param("idLoginDependente") Long idLoginDependente,
      @Param("tipoLoginResponsavel")
          String
              tipoLoginResponsavel, // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
      @Param("tipoLoginDependente")
          String tipoLoginDependente // A principio somente usar tipoLoginDependente = DEPENDENTE
      );

  // Busca as contas DEPENDENTE, ainda nao atreladas ao Responsavel, a partir do portador DEPENDENTE
  // pela regra RegraTipoPortadorEnum.DEPENDENTE
  @Query(
      value =
          " \t select distinct cast(cpagDep.id_conta as bigint) \n "
              + " \t from cadastral.pessoa pDep \n "
              + " \t inner join cadastral.conta_pessoa cpesDep on cpesDep.id_pessoa = pDep.id_pessoa and cpesDep.id_titularidade = 1 \n "
              + " \t inner join cadastral.conta_pagamento cpagDep on cpagDep.id_conta = cpesDep.id_conta \n "
              + " \t inner join cadastral.produto_instituicao_configuracao picDep on picDep.id_prod_instituicao = cpagDep.id_prod_instituicao \n "
              + " \t inner join cadastral.tipo_login_produto tlpDep on tlpDep.id_prod_instituicao = picDep.id_prod_instituicao \n "
              + " \t inner join cadastral.pessoa_responsavel_dependente prd on pDep.id_pessoa = prd.id_pessoa_dependente \n "
              + " \t inner join cadastral.conta_pessoa cpesResp on cpesResp.id_pessoa = prd.id_pessoa_responsavel and cpesResp.id_titularidade = 1 \n "
              + " \t inner join cadastral.conta_pagamento cpagResp on cpagResp.id_conta = cpesResp.id_conta \n "
              + " \t inner join cadastral.produto_instituicao_configuracao picResp on picResp.id_prod_instituicao = cpagResp.id_prod_instituicao \n "
              + " \t inner join cadastral.tipo_login_produto tlpResp on tlpResp.id_prod_instituicao = picResp.id_prod_instituicao \n "
              + " \t inner join cadastral.produto_instituicao_dependentes pid on pid.id_produto_dependente = picDep.id_prod_instituicao \n "
              + " where tlpResp.tipo_login = :tipoLoginResponsavel \n "
              + // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
              " \t and tlpDep.tipo_login = :tipoLoginDependente \n "
              + // A principio somente usar tipoLoginDependente = DEPENDENTE
              " \t and pDep.documento = :documentoDependente \n "
              + " \t and (pDep.id_processadora, pDep.id_instituicao) = (:idProcessadora, :idInstituicao) \n "
              + " \t and not exists ( \n "
              + " \t\t select * from cadastral.portador_login_conta pc where pc.id_login = :idLoginResponsavel and pc.id_conta = cpagDep.id_conta \n "
              + " \t ) \n "
              + " order by cast(cpagDep.id_conta as bigint) ",
      nativeQuery = true)
  List<Long> buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoDependente(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documentoDependente") String documentoDependente,
      @Param("idLoginResponsavel") Long idLoginResponsavel,
      @Param("tipoLoginResponsavel")
          String
              tipoLoginResponsavel, // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
      @Param("tipoLoginDependente")
          String tipoLoginDependente // A principio somente usar tipoLoginDependente = DEPENDENTE
      );
}
