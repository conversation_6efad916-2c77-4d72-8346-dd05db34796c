package br.com.json.bean.cadastral;

import br.com.entity.cadastral.Pessoa;
import br.com.sinergico.enums.TipoTelefonePessoaEnum;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FakeTelefonePessoaVo {
  private Long idTelefonePessoa;
  private Long idPessoa;
  private Pessoa pessoa;
  private TipoTelefonePessoaEnum tipoTelefone;
  private Integer ddi;
  private Integer ddd;
  private Integer numeroTelefone;
  private LocalDateTime dtHrManutencao;
  private Integer idStatus;
  private Boolean preferencial;

  public static FakeTelefonePessoaVo of(
      Pessoa p,
      TipoTelefonePessoaEnum tipo,
      Integer ddi,
      Integer ddd,
      Integer numero,
      boolean preferencial) {

    FakeTelefonePessoaVo vo = new FakeTelefonePessoaVo();
    vo.idPessoa = p.getIdPessoa();
    vo.pessoa = p;
    vo.tipoTelefone = tipo;
    vo.ddi = ddi;
    vo.ddd = ddd;
    vo.numeroTelefone = numero;
    vo.idStatus = 1;
    vo.preferencial = preferencial;
    vo.dtHrManutencao = p.getDataHoraUltimaAtualizacao();
    return vo;
  }
}
