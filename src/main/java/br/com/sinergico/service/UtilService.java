package br.com.sinergico.service;

import java.util.Arrays;
import java.util.List;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class UtilService {

  @Value("${ambiente}")
  private String ambiente;

  public boolean isAmbienteHomologacao() {
    return ambiente == null || ambiente.isEmpty() || "homologacao".equals(ambiente);
  }

  public boolean isAmbienteProducao() {
    return ambiente != null && !ambiente.isEmpty() && "producao".equals(ambiente);
  }

  public Integer getProdutoInsPontosInMais() {
    if (isAmbienteHomologacao()) {
      return 180118;
    }
    return 180114;
  }

  public Integer getProdutoInsCampanha() {
    if (isAmbienteHomologacao()) {
      return 180119;
    }
    return 180194;
  }

  public Integer getProdutoInsInfinancasPagamento() {
    if (isAmbienteHomologacao()) {
      return 180120;
    }
    return 180195;
  }

  public List<Integer> getCorporativoInfinancas() {
    return Arrays.asList(getCompraSaque(), getCompra());
  }

  public Integer getProdutoInstituicaoBrbPay() {
    if (isAmbienteHomologacao()) {
      return 240125;
    }
    return 240110;
  }

  public Integer getProdutoBrbFlamengo() {
    if (isAmbienteHomologacao()) {
      return 240126;
    }
    return 240139;
  }

  public Integer getParametroDefinicaoLimiteBoletoDiario() {
    if (isAmbienteHomologacao()) {
      return 87;
    }
    return 71;
  }

  public Integer getParametroValorLimiteSms() {
    if (isAmbienteHomologacao()) {
      return 123;
    }
    return 122;
  }

  private Integer getCompraSaque() {
    if (isAmbienteHomologacao()) {
      return 180124;
    }
    return 180179;
  }

  private Integer getCompra() {
    if (isAmbienteHomologacao()) {
      return 180124;
    }
    return 180180;
  }

  public Long getIdRelatorioParceria() {
    if (isAmbienteHomologacao()) {
      return 253L;
    }
    return 698L;
  }

  public Long getIdContaRepasseRp3() {
    if (isAmbienteHomologacao()) {
      return 1223689L;
    }
    return 2080069L;
  }
}
