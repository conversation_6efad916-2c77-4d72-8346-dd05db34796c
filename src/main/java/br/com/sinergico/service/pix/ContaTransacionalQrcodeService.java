package br.com.sinergico.service.pix;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.pix.ContaTransacionalQrcode;
import br.com.entity.pix.InstituicaoPix;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.pix.request.GerarQrCodeEstaticoDTO;
import br.com.json.bean.pix.request.GerarQrCodeEstaticoRequest;
import br.com.json.bean.pix.response.ConsultarQrCodeResponse;
import br.com.json.bean.pix.response.FormatoQrCodeValueResponse;
import br.com.json.bean.pix.response.QrCodeEstaticoInfoResponse;
import br.com.json.bean.pix.response.QrCodeValueResponse;
import br.com.json.bean.pix.response.ValueQrCodeEstaticoResponse;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.repository.pix.ContaTransacionalQrcodeRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.vo.pix.GerarQrCodeImediatoSemVencimentoRequest;
import br.com.sinergico.vo.pix.GerarQrCodeImediatoVencimentoRequest;
import java.time.LocalDateTime;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

@Service
public class ContaTransacionalQrcodeService {

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  @Autowired private ContaTransacionalQrcodeRepository contaTransacionalQrcodeRepository;

  @Autowired private ContaPagamentoService contaPagamentoService;

  public ResponseEntity<QrCodeEstaticoInfoResponse> validarGerarQrcodeEstatico(
      GerarQrCodeEstaticoDTO gerarQrCodeEstaticoDTO, SecurityUserPortador userPortador) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());
    return this.gerarQrcodeEstatico(
        gerarQrCodeEstaticoDTO.getGerarQrcodeComValor(),
        gerarQrCodeEstaticoDTO.getGerarQrCodeEstaticoRequest(),
        instituicaoPix);
  }

  public ResponseEntity<QrCodeEstaticoInfoResponse> validarGerarQrcodeEstatico(
      GerarQrCodeEstaticoDTO gerarQrCodeEstaticoDTO, SecurityUser user) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            user.getIdInstituicao());
    return this.gerarQrcodeEstatico(
        gerarQrCodeEstaticoDTO.getGerarQrcodeComValor(),
        gerarQrCodeEstaticoDTO.getGerarQrCodeEstaticoRequest(),
        instituicaoPix);
  }

  public ResponseEntity<QrCodeEstaticoInfoResponse> validarGerarQrcodeEstatico(
      GerarQrCodeEstaticoDTO gerarQrCodeEstaticoDTO, SecurityUserCorporativo userCorporativo) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userCorporativo.getIdInstituicao());
    return this.gerarQrcodeEstatico(
        gerarQrCodeEstaticoDTO.getGerarQrcodeComValor(),
        gerarQrCodeEstaticoDTO.getGerarQrCodeEstaticoRequest(),
        instituicaoPix);
  }

  private ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeEstatico(
      String gerarQrcodeComValor,
      GerarQrCodeEstaticoRequest gerarQrCodeEstaticoRequest,
      InstituicaoPix instituicaoPix) {

    if (gerarQrcodeComValor.compareTo("2") == 0) {
      return bancoRendimentoClient.gerarQrcodeEstatico(instituicaoPix, gerarQrCodeEstaticoRequest);
    } else {
      ContaTransacionalQrcode qrcode =
          findOneByChave(gerarQrCodeEstaticoRequest.getBeneficiario().getChave());
      if (Objects.isNull(qrcode)) {
        ResponseEntity<QrCodeEstaticoInfoResponse> response =
            bancoRendimentoClient.gerarQrcodeEstatico(instituicaoPix, gerarQrCodeEstaticoRequest);
        QrCodeEstaticoInfoResponse responseQrcode = response.getBody();
        salvarQrcode(responseQrcode, gerarQrCodeEstaticoRequest.getBeneficiario().getChave(), null);
        return response;
      } else {
        QrCodeEstaticoInfoResponse responseQrCode = montarQrCodeEstaticoInfoResponse(qrcode);
        return new ResponseEntity<>(responseQrCode, HttpStatus.OK);
      }
    }
  }

  private QrCodeEstaticoInfoResponse montarQrCodeEstaticoInfoResponse(ContaTransacionalQrcode qr) {
    QrCodeEstaticoInfoResponse response = new QrCodeEstaticoInfoResponse();
    response.setTransactionId(qr.getTransactionId());
    response.setSuccess(true);
    response.setErroMessage(null);
    ValueQrCodeEstaticoResponse value = new ValueQrCodeEstaticoResponse();
    QrCodeValueResponse qrcode = new QrCodeValueResponse();
    qrcode.setValor(qr.getValor());
    FormatoQrCodeValueResponse formato = new FormatoQrCodeValueResponse();
    formato.setId(qr.getIdFormato());
    formato.setDescricao(qr.getDescricao());
    qrcode.setFormato(formato);
    value.setQrCode(qrcode);
    response.setValue(value);
    return response;
  }

  public ResponseEntity<ConsultarQrCodeResponse> consultarQrCodePortador(
      String qrCode, SecurityUserPortador userPortador) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());
    return bancoRendimentoClient.consultarQrCodePortador(
        qrCode, instituicaoPix, userPortador.getCpf());
  }

  public ResponseEntity<ConsultarQrCodeResponse> consultarQrCodePortador(
      String qrCode, SecurityUserCorporativo userCorporativo) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userCorporativo.getIdInstituicao());
    return bancoRendimentoClient.consultarQrCodePortador(
        qrCode, instituicaoPix, userCorporativo.getUsername());
  }

  private void salvarQrcode(
      QrCodeEstaticoInfoResponse qrCodeEstaticoInfoResponse,
      String chave,
      String referenciaInterna) {

    ContaTransacionalQrcode contaTransacionalQrcode = new ContaTransacionalQrcode();
    contaTransacionalQrcode.setChave(chave);
    contaTransacionalQrcode.setDataCriacao(LocalDateTime.now());
    contaTransacionalQrcode.setDataModificacao(LocalDateTime.now());
    contaTransacionalQrcode.setTransactionId(qrCodeEstaticoInfoResponse.getTransactionId());
    contaTransacionalQrcode.setValor(qrCodeEstaticoInfoResponse.getValue().getQrCode().getValor());
    contaTransacionalQrcode.setIdFormato(
        qrCodeEstaticoInfoResponse.getValue().getQrCode().getFormato().getId());
    contaTransacionalQrcode.setDescricao(
        qrCodeEstaticoInfoResponse.getValue().getQrCode().getFormato().getDescricao());
    contaTransacionalQrcode.setReferenciaInterna(referenciaInterna);
    contaTransacionalQrcodeRepository.save(contaTransacionalQrcode);
  }

  public ContaTransacionalQrcode findOneByChave(String chave) {
    return contaTransacionalQrcodeRepository.findOneByChave(chave);
  }

  public ContaTransacionalQrcode findOneByReferenciaInterna(String referenciaInterna) {
    return contaTransacionalQrcodeRepository.findOneByReferenciaInterna(referenciaInterna);
  }

  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeDinamicoSemVencimento(
      GerarQrCodeImediatoSemVencimentoRequest gerarQrCodeEstaticoRequest,
      SecurityUserPortador userPortador) {

    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());
    try {
      ContaTransacionalQrcode qrcode =
          findOneByChave(gerarQrCodeEstaticoRequest.getBeneficiario().getChave());
      if (Objects.isNull(qrcode)) {
        ResponseEntity<QrCodeEstaticoInfoResponse> response =
            bancoRendimentoClient.gerarQrcodeImediatoSemVencimento(
                instituicaoPix, gerarQrCodeEstaticoRequest);
        QrCodeEstaticoInfoResponse responseQrcode = response.getBody();
        salvarQrcode(responseQrcode, gerarQrCodeEstaticoRequest.getBeneficiario().getChave(), null);
        return response;
      } else {
        QrCodeEstaticoInfoResponse responseQrCode = montarQrCodeEstaticoInfoResponse(qrcode);
        return new ResponseEntity<>(responseQrCode, HttpStatus.OK);
      }
    } catch (Exception e) {
      throw new GenericServiceException("Ocorreu um erro ao salvar qrcode: ", e.getMessage());
    }
  }

  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeDinamicoVencimento(
      GerarQrCodeImediatoVencimentoRequest gerarQrCodeImediatoRequest,
      SecurityUserPortador userPortador) {

    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());
    try {
      ContaTransacionalQrcode qrcode =
          findOneByChave(gerarQrCodeImediatoRequest.getBeneficiario().getChave());
      if (Objects.isNull(qrcode)) {
        ResponseEntity<QrCodeEstaticoInfoResponse> response =
            bancoRendimentoClient.gerarQrcodeImediatoVencimento(
                instituicaoPix, gerarQrCodeImediatoRequest);
        QrCodeEstaticoInfoResponse responseQrcode = response.getBody();
        salvarQrcode(responseQrcode, gerarQrCodeImediatoRequest.getBeneficiario().getChave(), null);
        return response;
      } else {
        QrCodeEstaticoInfoResponse responseQrCode = montarQrCodeEstaticoInfoResponse(qrcode);
        return new ResponseEntity<>(responseQrCode, HttpStatus.OK);
      }
    } catch (Exception e) {
      throw new GenericServiceException("Ocorreu um erro ao salvar qrcode: ", e.getMessage());
    }
  }

  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeDinamicoVencimentoUser(
      GerarQrCodeImediatoVencimentoRequest gerarQrCodeImediatoRequest,
      SecurityUser user,
      Long idConta) {

    ContaPagamento contaPagamento = contaPagamentoService.findByIdNotNull(idConta);
    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());
    try {
      ContaTransacionalQrcode qrcode = null;
      if (gerarQrCodeImediatoRequest.getReferenciaInterna() != null) {
        qrcode = findOneByReferenciaInterna(gerarQrCodeImediatoRequest.getReferenciaInterna());
      }

      if (Objects.isNull(qrcode)) {
        ResponseEntity<QrCodeEstaticoInfoResponse> response =
            bancoRendimentoClient.gerarQrcodeImediatoVencimento(
                instituicaoPix, gerarQrCodeImediatoRequest);
        QrCodeEstaticoInfoResponse responseQrcode = response.getBody();
        salvarQrcode(
            responseQrcode,
            gerarQrCodeImediatoRequest.getBeneficiario().getChave(),
            gerarQrCodeImediatoRequest.getReferenciaInterna());
        return response;
      } else {
        QrCodeEstaticoInfoResponse responseQrCode = montarQrCodeEstaticoInfoResponse(qrcode);
        return new ResponseEntity<>(responseQrCode, HttpStatus.OK);
      }
    } catch (Exception e) {
      throw new GenericServiceException("Ocorreu um erro ao salvar qrcode: ", e.getMessage());
    }
  }
}
