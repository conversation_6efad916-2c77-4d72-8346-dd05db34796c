package br.com.sinergico.service.cadastral;

import static br.com.sinergico.util.Constantes.ID_USUARIO_INCLUSAO_PORTADOR;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.CredencialPreEmitida;
import br.com.entity.cadastral.PedidoCredenciaisPreEmitidas;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.cadastral.CadastrarContaPagPessoaCredPreEmitidaRequest;
import br.com.json.bean.cadastral.CadastrarPedidoCredenciaisPreEmitidas;
import br.com.json.bean.cadastral.LiberarCredencialPreEmitidaParaCardHolderRequest;
import br.com.json.bean.cadastral.VincularCredencialPreEmitidaPessoaCardHolderRequest;
import br.com.sinergico.repository.cadastral.PedidoCredenciasPreEmitidasRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.util.DateUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PedidoCredenciasPreEmitidasService
    extends GenericService<PedidoCredenciaisPreEmitidas, Long> {

  @Autowired private CredencialPreEmitidaService credencialPreEmitidaService;

  @Autowired private ContaPagamentoService contaService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfService;

  @Autowired private ProdutoInstituicaoService prodInstService;

  @Autowired private PessoaService pessoaService;
  @Autowired private UtilService utilService;

  private static final Integer PESSOA_FISICA = 1;

  @Autowired
  public PedidoCredenciasPreEmitidasService(PedidoCredenciasPreEmitidasRepository repository) {
    super(repository);
  }

  @Transactional
  public PedidoCredenciaisPreEmitidas createPedidoCredenciaisPreEmitidas(
      CadastrarPedidoCredenciaisPreEmitidas cadastrarPedidoCredenciaisPreEmitidas,
      Integer idUsuario) {
    ProdutoInstituicao prodInst =
        getProdutoInstituicaoNotNull(
            cadastrarPedidoCredenciaisPreEmitidas.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao prodInstConf =
        getProdutoInstituicaoConfiguracaoNotNull(
            cadastrarPedidoCredenciaisPreEmitidas.getIdProdutoInstituicao(), prodInst);

    if (!prodInstConf.getPermitePreEmissao()) {
      throw new GenericServiceException(
          "Não foi possível fazer pedido de cartões Pré-emitidos.Verifique a configuração do Produto selecionado.",
          "ProdutoInstituicaoConfiguracao não permite");
    }

    PedidoCredenciaisPreEmitidas pedido = new PedidoCredenciaisPreEmitidas();
    pedido.setDataHoraPedido(LocalDateTime.now());
    pedido.setIdProdutoInstituicao(cadastrarPedidoCredenciaisPreEmitidas.getIdProdutoInstituicao());
    pedido.setIdusuarioInclusao(idUsuario);
    pedido.setLinhaImpressa(cadastrarPedidoCredenciaisPreEmitidas.getLinhaImpressa().toUpperCase());
    pedido.setLinhaImpressa2(
        cadastrarPedidoCredenciaisPreEmitidas.getLinhaImpressa2() != null
            ? cadastrarPedidoCredenciaisPreEmitidas.getLinhaImpressa2().toUpperCase()
            : null);
    pedido.setIdProcessadora(prodInstConf.getIdProcessadora());
    pedido.setIdInstituicao(prodInstConf.getIdInstituicao());
    pedido.setTotalCredenciais(cadastrarPedidoCredenciaisPreEmitidas.getTotalCredenciais());
    pedido.setIdPlastico(cadastrarPedidoCredenciaisPreEmitidas.getIdPlastico());

    pedido = save(pedido);

    return pedido;
  }

  private ProdutoInstituicaoConfiguracao getProdutoInstituicaoConfiguracaoNotNull(
      Integer idProdutoInstituicao, ProdutoInstituicao prodInst) {
    ProdutoInstituicaoConfiguracao prodInstConf =
        prodInstConfService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            prodInst.getIdProcessadora(), idProdutoInstituicao, prodInst.getIdInstituicao());

    if (Objects.isNull(prodInstConf)) {
      throw new GenericServiceException(
          "Não foi possível fazer pedido de cartões Pré-emitidos.",
          "ProdutoInstituicaoConfiguracao não encontrado");
    }
    return prodInstConf;
  }

  private ProdutoInstituicao getProdutoInstituicaoNotNull(Integer idProdutoInstituicao) {
    ProdutoInstituicao prodInst = prodInstService.findByIdProdInstituicao(idProdutoInstituicao);

    if (Objects.isNull(prodInst)) {
      throw new GenericServiceException(
          "Não foi possível fazer pedido de cartões Pré-emitidos.",
          "ProdutoInstituicao não encontrado");
    }
    return prodInst;
  }

  @Transactional
  public CredencialGerada vincularPessoaEContaCredencialPreEmitida(
      CadastrarContaPagPessoaCredPreEmitidaRequest model, SecurityUser user) {

    CredencialPreEmitida credPreEmi = credencialPreEmitidaService.obter(model.getCredencial());

    PedidoCredenciaisPreEmitidas pedido = getPedidoCredsPreEmitidasNotNull(credPreEmi);

    Integer idProdutoInstituicao = obterIdProdutoInstituicao(pedido);

    // Se o indicador de liberação da credencial pré-emitida for nulo e o produto for BRB Flamengo
    // o cartão não foi vendido para que possa ser gerada uma credencial para este cliente
    // if (Objects.isNull(credPreEmi.getInLiberacaoCredencialPreEmitida()) &&
    // idProdutoInstituicao.equals(240139)) {
    // Homologação
    if ((!Objects.isNull(credPreEmi) && Objects.isNull(credPreEmi.getInLiberacao()))
        && (!Objects.isNull(idProdutoInstituicao)
            && utilService.getProdutoBrbFlamengo().equals(idProdutoInstituicao))) {
      throw new GenericServiceException(
          "Não foi possível criar conta." + "Credencial não liberada para utilização.");
    }

    if (Objects.isNull(user)) {
      model.setIdUsuarioInclusao(ID_USUARIO_INCLUSAO_PORTADOR);
    }

    CredencialGerada credencialGerada =
        obterCredencialGerada(model, credPreEmi, idProdutoInstituicao);

    credPreEmi.setDataHoraVinculacao(DateUtil.dateToLocalDateTime(new Date()));
    credPreEmi.setIdConta(credencialGerada.getCredencial().getIdConta());
    credPreEmi.setIdPessoa(credencialGerada.getCredencial().getIdPessoa());
    if (Objects.nonNull(user)) {
      credPreEmi.setIdUsuarioVinculacao(user.getIdUsuario());
    }
    credencialPreEmitidaService.save(credPreEmi);

    return credencialGerada;
  }

  /**
   * Procedimento de liberação de um cartão pré-emitido Fluxo de saída do Produto para o Card Holder
   *
   * @param model
   * @param user
   * @return
   */
  @Transactional
  public CredencialPreEmitida liberarContaPreEmitidaParaVinculacaoACredencial(
      LiberarCredencialPreEmitidaParaCardHolderRequest model, SecurityUser user) {

    CredencialPreEmitida credPreEmi = credencialPreEmitidaService.obter(model.getCredencial());

    // Se o indicador de liberação for nulo, ele seta o indicador de liberação para true, ou seja, o
    // cartão pré-emitido
    // foi vendido e pode gerar uma credencial para o Card Holder
    if ((!Objects.isNull(credPreEmi) && Objects.isNull(credPreEmi.getInLiberacao()))) {
      credPreEmi.setInLiberacao(true);
      credPreEmi.setDataHoraLiberacao(DateUtil.dateToLocalDateTime(new Date()));
      if (Objects.nonNull(user)) {
        credPreEmi.setIdUsuarioLiberacao(user.getIdUsuario());
      }
      credencialPreEmitidaService.save(credPreEmi);
    }

    return credPreEmi;
  }

  private Integer obterIdProdutoInstituicao(PedidoCredenciaisPreEmitidas pedido) {
    Integer idProdInst = pedido.getIdProdutoInstituicao();
    if (Objects.isNull(idProdInst)) {
      throw new GenericServiceException("Não foi possível encontrar o idProdutoinstituicao.");
    }
    return idProdInst;
  }

  private CredencialGerada obterCredencialGerada(
      CadastrarContaPagPessoaCredPreEmitidaRequest model,
      CredencialPreEmitida credPreEmi,
      Integer idProdInst) {
    CredencialGerada credencialGerada =
        contaService.cadastrarContaPagamentoPessoaCredPreEmitida(model, credPreEmi, idProdInst);
    if (Objects.isNull(credencialGerada)) {
      throw new GenericServiceException(
          "Não foi possível gerar uma credencial para este cartão pré-emitido.");
    }
    return credencialGerada;
  }

  public PedidoCredenciaisPreEmitidas getPedidoCredsPreEmitidasNotNull(
      CredencialPreEmitida credPreEmi) {
    PedidoCredenciaisPreEmitidas pedido =
        findById(credPreEmi.getPedidoCredenciaisPreEmitidas().getIdPedidoCredsPreEmitidas());

    if (Objects.isNull(pedido)) {
      throw new GenericServiceException("Pedido do cartão Pré-Emitido não encontrado.");
    }
    return pedido;
  }

  @Transactional
  public CredencialGerada vincularCredencialPreEmitidaPessoaCardHolder(
      VincularCredencialPreEmitidaPessoaCardHolderRequest model, SecurityUserPortador user) {

    CadastrarContaPagPessoaCredPreEmitidaRequest cadContaPag =
        new CadastrarContaPagPessoaCredPreEmitidaRequest();

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            user.getIdProcessadora(), user.getIdInstituicao(), user.getCpf(), PESSOA_FISICA);

    if (Objects.nonNull(pessoa)) {
      BeanUtils.copyProperties(pessoa, cadContaPag, getNullPropertyNames(pessoa));
    }
    cadContaPag.setCredencial(model.getCredencial());
    cadContaPag.setTipoPessoa(PESSOA_FISICA);
    cadContaPag.setIdPontoDeRelacionamento(model.getIdPontoDeRelacionamento());
    cadContaPag.setIdRegional(model.getIdRegional());
    cadContaPag.setIdFilial(model.getIdFilial());

    return vincularPessoaEContaCredencialPreEmitida(cadContaPag, null);
  }

  public List<ContaPagamento> getContasNotB2b(
      Integer idProcessadora, Integer idInstituicao, Integer idTipoPessoa, String documento) {
    return contaService.findByProdutoNotB2b(idProcessadora, idInstituicao, idTipoPessoa, documento);
  }

  public List<Long> getIdsContasNotB2b(
      Integer idProcessadora, Integer idInstituicao, Integer idTipoPessoa, String documento) {
    List<ContaPagamento> contasNotB2b =
        getContasNotB2b(idProcessadora, idInstituicao, idTipoPessoa, documento);
    List<Long> contas = new ArrayList<>();

    if (Objects.nonNull(contasNotB2b)) {
      contasNotB2b.forEach(c -> contas.add(c.getIdConta()));
    }

    return contas;
  }
}
