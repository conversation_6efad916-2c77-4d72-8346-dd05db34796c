package br.com.sinergico.service.pix;

import static br.com.sinergico.util.Constantes.MSG_ERRO_TIMEOUT_RENDIMENTO;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.pix.ContaTransacional;
import br.com.entity.pix.ContaTransacionalDevolucao;
import br.com.entity.pix.ContaTransacionalPagamento;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.pix.LimiteInstituicaoPix;
import br.com.entity.pix.RepasseInstitucional;
import br.com.entity.suporte.CotacaoPontos;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.JcardServiceException;
import br.com.json.bean.enums.pix.StatusPagamentoPixEnum;
import br.com.json.bean.enums.pix.TipoContaEnum;
import br.com.json.bean.pix.request.IncluirOrdemPagamentoRequest;
import br.com.json.bean.pix.request.ValidarIncluirOrdemPagamentoDTO;
import br.com.json.bean.pix.request.vo.PessoaVO;
import br.com.json.bean.pix.response.BancoRendimentoResponse;
import br.com.json.bean.pix.response.ConsultarOrdemPagamentoResponse;
import br.com.json.bean.pix.response.IncluirOrdemPagamentoResponse;
import br.com.json.bean.pix.response.vo.ErroDetailVO;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.facade.cadastral.ContaPagamentoFacade;
import br.com.sinergico.metrics.MetricsCollector;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.pix.ContaTransacionalPagamentoRepository;
import br.com.sinergico.repository.pix.RepasseInstitucionalRepository;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.LimitesContaService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.LancamentoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class ContaTransacionalPagamentoService {

  public static final LocalDateTime dataHoje = LocalDateTime.now().with(LocalTime.MIN);
  public static final String SALDO_INSUFICIENTE_NA_CONTA_PI =
      "Saldo insuficiente na conta de pagamento instantâneo";
  public static final String ESTORNADO_ANTERIORMENTE = "previously.voided";
  public static final String TRANSACAO_NAO_FINALIZADA = "tranlog.not.found";

  @Value("${executar.atualizar.ordens.pagamentos}")
  private String executarAtualizarOrdensPagamentos;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private LimiteInstituicaoPixService limiteInstituicaoPixService;

  @Autowired private ContaTransacionalService contaTransacionalService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private LancamentoService lancamentoService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired @Lazy private ContaPagamentoFacade facade;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private ContaTransacionalDevolucaoService contaTransacionalDevolucaoService;

  @Autowired private ContaTransacionalMovimentoService contaTransacionalMovimentoService;

  @Autowired private CotacaoPontosRepository cotacaoPontosRepository;

  @Autowired private ContaTransacionalPagamentoRepository contaTransacionalPagamentoRepository;

  @Autowired private EventoService eventoService;

  @Autowired private MetricsCollector metricsCollector;

  @Autowired private PessoaService pessoaService;

  @Autowired private RepasseInstitucionalRepository repasseInstitucionalRepository;

  @Autowired private CredencialRepository credencialRepository;

  @Autowired private UtilService utilService;

  @Autowired private LimitesContaService limitesContaService;
  @Autowired private TravaServicosService travaServicosService;
  @Autowired private TravaContasService travaContasService;

  @Transactional
  public ResponseEntity<IncluirOrdemPagamentoResponse> validarIncluirOrdemPagamento(
      ValidarIncluirOrdemPagamentoDTO validarIncluirOrdemPagamentoDTO,
      SecurityUserPortador userPortador) {
    long idConta =
        Long.parseLong(
            validarIncluirOrdemPagamentoDTO
                .getIncluirOrdemPagamentoRequest()
                .getPagador()
                .getConta()
                .getIdConta());
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.PIX);
    travaContasService.travaContas(idConta, Servicos.PIX);

    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(idConta, userPortador);
    return this.incluirOrdemPagamento(
        validarIncluirOrdemPagamentoDTO.getIncluirOrdemPagamentoRequest(),
        validarIncluirOrdemPagamentoDTO.getTokenJwt(),
        conta,
        userPortador.getDocumentoAcesso());
  }

  @Transactional
  public ResponseEntity<IncluirOrdemPagamentoResponse> validarIncluirOrdemPagamento(
      ValidarIncluirOrdemPagamentoDTO validarIncluirOrdemPagamentoDTO,
      SecurityUserCorporativo userCorporativo) {
    long idConta =
        Long.parseLong(
            validarIncluirOrdemPagamentoDTO
                .getIncluirOrdemPagamentoRequest()
                .getPagador()
                .getConta()
                .getIdConta());
    travaServicosService.travaServicos(userCorporativo.getIdInstituicao(), Servicos.PIX);
    travaContasService.travaContas(idConta, Servicos.PIX);
    ContaPagamento conta =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(idConta, userCorporativo);
    return this.incluirOrdemPagamento(
        validarIncluirOrdemPagamentoDTO.getIncluirOrdemPagamentoRequest(),
        validarIncluirOrdemPagamentoDTO.getTokenJwt(),
        conta,
        null);
  }

  @Transactional
  public ResponseEntity<IncluirOrdemPagamentoResponse> incluirOrdemPagamento(
      IncluirOrdemPagamentoRequest incluirOrdemPagamentoRequest,
      String tokenJWT,
      ContaPagamento conta,
      String documentoRepresentante) {

    if (validarAgenciaInstituicaoRequest(incluirOrdemPagamentoRequest, conta)) {
      throw new GenericServiceException(
          ConstantesErro.PAG_PIX_ERRO_AGENCIA_INSTITUICAO.getMensagem());
    }
    // Valida Dados Pagador
    PessoaVO pessoaVOPagador = validarPessoa(conta.getIdConta());
    incluirOrdemPagamentoRequest.getPagador().setPessoa(pessoaVOPagador);

    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.PIX);

    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    ContaTransacional contaTransacional =
        contaTransacionalService
            .findByIdConta(conta.getIdConta())
            .orElseGet(
                () ->
                    contaTransacionalService.criarContaTransacional(
                        conta.getIdConta(), instituicaoPix));

    if (contaTransacional == null) {
      throw new GenericServiceException(ConstantesErro.PIX_CONTA_PIX_NAO_ENCONTRADA.getMensagem());
    }
    if (contaTransacional.getConta() == null
        || !Constantes.CONTA_DESBLOQUEADA.equals(contaTransacional.getConta().getIdStatusConta())) {
      throw new GenericServiceException(ConstantesErro.PIX_CONTA_BLOQUEADA.getMensagem());
    }
    String numeroContaComDV =
        contaTransacional.getConta().getIdConta().toString() + contaTransacional.getDvConta();

    ValidaContaOrigemIgualContaDestino validacao =
        getValidaContaOrigemIgualContaDestino(incluirOrdemPagamentoRequest, numeroContaComDV);

    // Checagem de saldo disponível no JCard
    Boolean inMais =
        Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(contaTransacional.getAgencia());
    GetSaldoConta saldoConta =
        contaPagamentoService.getSaldoConta(contaTransacional.getConta().getIdConta());
    if (saldoConta
            .getSaldoDisponivel()
            .compareTo(
                trataValorInMais(
                    incluirOrdemPagamentoRequest.getDadosOperacao().getValorOperacao(), inMais))
        < 0) {
      throw new GenericServiceException(ConstantesErro.PAG_PIX_SALDO_INSUFICIENTE.getMensagem());
    }

    limitesContaService.validarLimiteContaDiario(
        incluirOrdemPagamentoRequest.getDadosOperacao().getValorOperacao(), conta);

    Optional<LimiteInstituicaoPix> limiteInstituicaoPix =
        limiteInstituicaoPixService.findByIdInstituicaoPix(contaTransacional.getIdInstituicaoPix());

    if (limiteInstituicaoPix.isPresent()) {
      if (limiteInstituicaoPix.get().getQtdTransacoesDiarias() != null) {
        Integer quantidade =
            contaTransacionalPagamentoRepository.countTransacoesDiariasPix(
                contaTransacional, dataHoje);
        if (quantidade >= limiteInstituicaoPix.get().getQtdTransacoesDiarias()) {
          throw new GenericServiceException(
              ConstantesErro.PAG_PIX_LIMITE_EFETUADOS_DIARIO.getMensagem(),
              HttpStatus.UNAUTHORIZED);
        }
      }
    }

    BigDecimal valorOperacao = incluirOrdemPagamentoRequest.getDadosOperacao().getValorOperacao();
    verificarContaPagador(contaTransacional, valorOperacao);
    String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);

    ResponseEntity<IncluirOrdemPagamentoResponse> incluirOrdemPagamentoResponseResponseEntity =
        null;

    if (validarEnvioComRepasse(conta)) {
      Credencial credencial =
          credencialRepository.findUltimaCredencialDesbloqueadaTitularConta(conta.getIdConta());
      incluirOrdemPagamentoResponseResponseEntity =
          transferirEntreContasEEnvioPix(
              credencial.getIdCredencial(),
              incluirOrdemPagamentoRequest,
              valorOperacao,
              tokenJWT,
              instituicaoPix,
              conta,
              documentoRepresentante);

    } else {
      JcardResponse jcardResponse = null;
      String textoExtrato = String.format("PIX enviado no valor de %s", valorOperacao);
      jcardResponse =
          movimentarContaPix(
              contaTransacional.getConta(),
              valorOperacao,
              textoExtrato,
              rrn,
              Constantes.COD_TRANSACAO_ENVIO_PIX,
              -1);

      if (Objects.nonNull(jcardResponse) && !jcardResponse.getSuccess().equals(Boolean.TRUE)) {
        throw new GenericServiceException(ConstantesErro.PAG_PIX_ERRO_AO_DEBITAR.getMensagem());
      }

      incluirOrdemPagamentoRequest.setNumeroContaPagador(numeroContaComDV);
      incluirOrdemPagamentoRequest.setNsu(rrn.replaceAll(Constantes.PREFIXO_RRN_PORTADOR, "x"));
      incluirOrdemPagamentoRequest.getPagador().getConta().setTipoId("1");

      if (incluirOrdemPagamentoRequest.getDadosOperacao().getCampoLivre() != null) {
        String caracteresInvalidos = "[^\\p{L}\\p{M}\\p{N}\\p{P}\\p{Z}\\p{Cf}\\p{Cs}\\s]";
        Pattern pattern = Pattern.compile(caracteresInvalidos);
        Matcher matcher =
            pattern.matcher(incluirOrdemPagamentoRequest.getDadosOperacao().getCampoLivre());
        boolean contemCaracteresInvalidos = matcher.find();
        if (contemCaracteresInvalidos) {
          String campoLivreSemEmoji =
              incluirOrdemPagamentoRequest
                  .getDadosOperacao()
                  .getCampoLivre()
                  .replaceAll(caracteresInvalidos, "");
          incluirOrdemPagamentoRequest.getDadosOperacao().setCampoLivre(campoLivreSemEmoji);
        }
      }

      try {
        incluirOrdemPagamentoResponseResponseEntity =
            bancoRendimentoClient.incluirOrdemPagamento(
                instituicaoPix, incluirOrdemPagamentoRequest);

        if (incluirOrdemPagamentoResponseResponseEntity.getBody() != null
            && incluirOrdemPagamentoResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)) {

          log.info(
              "Resposta ao incluir ordem de pagamento -> "
                  + incluirOrdemPagamentoResponseResponseEntity.getBody());
          salvarPagamento(
              incluirOrdemPagamentoRequest,
              incluirOrdemPagamentoResponseResponseEntity.getBody(),
              contaTransacional.getConta().getIdConta(),
              rrn);
          eventoService.publicarMovimentacaoFinanceiraEvent(
              Servicos.PIX.getDescricao(),
              instituicaoPix.getIdInstituicao(),
              validacao.agenciaBeneficiario != null
                  ? Integer.valueOf(validacao.agenciaBeneficiario)
                  : null,
              conta.getIdConta(),
              validacao.idContaBeneficiario != null
                  ? Long.valueOf(validacao.idContaBeneficiario)
                  : null,
              Constantes.COD_TRANSACAO_ENVIO_PIX,
              null,
              valorOperacao,
              incluirOrdemPagamentoRequest.getBeneficiario().getChave() != null
                  ? incluirOrdemPagamentoRequest.getBeneficiario().getChave()
                  : "0");

          // Salvar o limite diario
          limitesContaService.salvarLimiteDiario(valorOperacao, conta.getIdConta());

        } else if (incluirOrdemPagamentoResponseResponseEntity
                .getStatusCode()
                .equals(HttpStatus.INTERNAL_SERVER_ERROR)
            && ((BancoRendimentoResponse) incluirOrdemPagamentoResponseResponseEntity.getBody())
                .getErroMessage()
                .getMessage()
                .equals(MSG_ERRO_TIMEOUT_RENDIMENTO)) {
          salvarPagamentoErro(
              incluirOrdemPagamentoRequest,
              contaTransacional.getConta().getIdConta(),
              rrn,
              false,
              null);
          return incluirOrdemPagamentoResponseResponseEntity;
        } else {
          estornarPix(contaTransacional.getConta(), rrn, valorOperacao);
          String errorMessage = null;
          if (incluirOrdemPagamentoResponseResponseEntity.getBody().getErroMessage().getErrors()
                  != null
              && !incluirOrdemPagamentoResponseResponseEntity
                  .getBody()
                  .getErroMessage()
                  .getErrors()
                  .isEmpty()) {
            errorMessage =
                incluirOrdemPagamentoResponseResponseEntity
                    .getBody()
                    .getErroMessage()
                    .getErrors()
                    .stream()
                    .map(ErroDetailVO::getMessage)
                    .collect(Collectors.joining(";"));
          }
          salvarPagamentoErro(
              incluirOrdemPagamentoRequest,
              contaTransacional.getConta().getIdConta(),
              rrn,
              true,
              errorMessage);
          return incluirOrdemPagamentoResponseResponseEntity;
        }
      } catch (GenericServiceException e) {
        log.warn(e.getMensagem(), e);
        salvarPagamentoErro(
            incluirOrdemPagamentoRequest,
            contaTransacional.getConta().getIdConta(),
            rrn,
            false,
            null);
      } catch (Exception e) {
        log.warn(ConstantesErro.PAG_PIX_ERRO_COMUNICACAO_RENDIMENTO.getMensagem());
        salvarPagamentoErro(
            incluirOrdemPagamentoRequest,
            contaTransacional.getConta().getIdConta(),
            rrn,
            false,
            null);
      }
    }

    return incluirOrdemPagamentoResponseResponseEntity;
  }

  private static boolean validarEnvioComRepasse(ContaPagamento conta) {
    return Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(conta.getIdInstituicao());
  }

  private static boolean validarAgenciaInstituicaoRequest(
      IncluirOrdemPagamentoRequest incluirOrdemPagamentoRequest, ContaPagamento conta) {
    return incluirOrdemPagamentoRequest == null
        || incluirOrdemPagamentoRequest.getPagador() == null
        || incluirOrdemPagamentoRequest.getPagador().getConta() == null
        || incluirOrdemPagamentoRequest.getPagador().getConta().getAgencia() == null
        || !conta
            .getIdInstituicao()
            .toString()
            .equals(incluirOrdemPagamentoRequest.getPagador().getConta().getAgencia());
  }

  private PessoaVO validarPessoa(Long idConta) {
    // trocar os dados do PessoaVO Pagador
    List<Long> idPessoa = pessoaService.findIdPessoasByIdConta(idConta);
    if (idPessoa != null && !idPessoa.isEmpty()) {
      Pessoa pessoa = pessoaService.findOneByIdPessoa(idPessoa.get(0));

      PessoaVO pessoaVO = new PessoaVO();
      pessoaVO.setNome(
          pessoa.getIdTipoPessoa() == 1 ? pessoa.getNomeCompleto() : pessoa.getNomeFantasia());
      pessoaVO.setInscricaoNacional(pessoa.getDocumento());
      pessoaVO.setTipoId(pessoa.getIdTipoPessoa().toString());
      pessoaVO.setNomeFantasia(null);
      return pessoaVO;
    }
    return null;
  }

  private ValidaContaOrigemIgualContaDestino getValidaContaOrigemIgualContaDestino(
      IncluirOrdemPagamentoRequest incluirOrdemPagamentoRequest, String numeroContaComDV) {
    String instituicaoBeneficiario =
        incluirOrdemPagamentoRequest.getBeneficiario() != null
            ? incluirOrdemPagamentoRequest.getBeneficiario().getConta() != null
                ? incluirOrdemPagamentoRequest.getBeneficiario().getConta().getIdConta() != null
                    ? incluirOrdemPagamentoRequest
                        .getBeneficiario()
                        .getConta()
                        .getIspbParticipante()
                    : null
                : null
            : null;
    String agenciaBeneficiario =
        incluirOrdemPagamentoRequest.getBeneficiario() != null
            ? incluirOrdemPagamentoRequest.getBeneficiario().getConta() != null
                ? incluirOrdemPagamentoRequest.getBeneficiario().getConta().getAgencia() != null
                    ? incluirOrdemPagamentoRequest.getBeneficiario().getConta().getAgencia()
                    : null
                : null
            : null;
    String idContaBeneficiario =
        incluirOrdemPagamentoRequest.getBeneficiario() != null
            ? incluirOrdemPagamentoRequest.getBeneficiario().getConta() != null
                ? incluirOrdemPagamentoRequest.getBeneficiario().getConta().getIdConta() != null
                    ? incluirOrdemPagamentoRequest.getBeneficiario().getConta().getIdConta()
                    : null
                : null
            : null;
    String instituicaoPagador =
        incluirOrdemPagamentoRequest.getPagador() != null
            ? incluirOrdemPagamentoRequest.getPagador().getConta() != null
                ? incluirOrdemPagamentoRequest.getPagador().getConta().getIdConta() != null
                    ? incluirOrdemPagamentoRequest.getPagador().getConta().getIspbParticipante()
                    : null
                : null
            : null;
    String agenciaPagador =
        incluirOrdemPagamentoRequest.getPagador() != null
            ? incluirOrdemPagamentoRequest.getPagador().getConta() != null
                ? incluirOrdemPagamentoRequest.getPagador().getConta().getAgencia() != null
                    ? incluirOrdemPagamentoRequest.getPagador().getConta().getAgencia()
                    : null
                : null
            : null;
    if (instituicaoPagador != null
        && instituicaoPagador.equals(instituicaoBeneficiario)
        && agenciaPagador != null
        && agenciaPagador.equals(agenciaBeneficiario)
        && numeroContaComDV.equals(idContaBeneficiario)) {
      throw new GenericServiceException(ConstantesErro.PIX_ENVIO_MESMA_CONTA.getMensagem());
    }
    return new ValidaContaOrigemIgualContaDestino(agenciaBeneficiario, idContaBeneficiario);
  }

  private static class ValidaContaOrigemIgualContaDestino {
    public final String agenciaBeneficiario;
    public final String idContaBeneficiario;

    public ValidaContaOrigemIgualContaDestino(
        String agenciaBeneficiario, String idContaBeneficiario) {
      this.agenciaBeneficiario = agenciaBeneficiario;
      this.idContaBeneficiario = idContaBeneficiario;
    }
  }

  private Long getNumeroContaSemDV(String nroConta) {
    return Long.valueOf(nroConta.substring(0, nroConta.length() - 1));
  }

  @SuppressWarnings("SameParameterValue")
  private JcardResponse movimentarContaPix(
      ContaPagamento contaPagador,
      BigDecimal valor,
      String textoExtrato,
      String rrn,
      int codTransacao,
      int sinal) {

    ProdutoInstituicao produtoInstituicao = contaPagador.getProdutoInstituicao();
    ProdutoInstituicaoConfiguracao produto =
        produtoInstituicao.getProdutoInstituicaoConfiguracao().get(0);

    String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaPagador, produto);

    boolean isProdutoMoeda =
        Constantes.CODIGO_MOEDA_DE_PONTO.equals(produto.getMoeda().getIdMoeda());
    Optional<CotacaoPontos> cotacaoPontosOptional =
        Optional.ofNullable(
            cotacaoPontosRepository.findByIdInstituicao(contaPagador.getIdInstituicao()));
    CotacaoPontos cotacaoPontos = cotacaoPontosOptional.orElseGet(CotacaoPontos::new);
    Integer stan = lancamentoService.getStan();
    Credencial credencialPagador =
        credencialService.buscarCredencialParaLancamentoManual(contaPagador.getIdConta());
    return lancamentoService.doLancamentoManual(
        accountCode,
        codTransacao,
        isProdutoMoeda ? valor.multiply(cotacaoPontos.getValorConversao()) : valor,
        produto.getMoeda().getIdMoeda().toString(),
        rrn,
        sinal,
        credencialPagador.getTokenInterno(),
        contaPagador.getIdConta(),
        textoExtrato,
        null,
        null,
        null,
        stan,
        null);
  }

  private void verificarContaPagador(
      ContaTransacional contaTransacional, BigDecimal valorOperacao) {

    veriricarLimites(contaTransacional, valorOperacao);

    if (valorOperacao.compareTo(BigDecimal.ZERO) <= 0) {
      throw new GenericServiceException("Valor inválido para operação");
    }
  }

  private void veriricarLimites(ContaTransacional contaTransacional, BigDecimal valorOperacao) {
    LocalDateTime now = LocalDateTime.now();

    // hora alterada para menor que 20 pois estava considerando horário noturno somente após as 21.
    boolean isPeriodoDiurno = now.getHour() >= 6 && now.getHour() < 20;

    // verifica limites para periodo diurno
    if (isPeriodoDiurno) {

      LocalDateTime inicio = now.with(LocalTime.of(6, 0));
      LocalDateTime fim = now.with(LocalTime.of(20, 0));
      BigDecimal somaValoresPeriodoDiurno =
          contaTransacionalPagamentoRepository
              .findSomaValoresPorPeriodo(contaTransacional, inicio, fim)
              .add(valorOperacao);

      if (valorOperacao.compareTo(contaTransacional.getLimiteDiurnoPorTransacao()) > 0) {
        throw new GenericServiceException(
            "Valor da operação maior que o limite diurno por transação");
      }

      if (somaValoresPeriodoDiurno.compareTo(contaTransacional.getLimiteDiurnoMaximo()) > 0) {
        throw new GenericServiceException(
            "Valor da operação irá ultrapassar o limite total do período diurno");
      }
    } else {
      LocalDateTime inicio;
      LocalDateTime fim;

      if (now.getHour() >= 20 && !now.isAfter(LocalDate.now().atTime(LocalTime.MAX))) {
        inicio = now.with(LocalTime.of(20, 0, 0));
        fim = now.with(LocalTime.of(23, 59, 59));
      } else {
        inicio = now.minusDays(1).with(LocalTime.of(20, 0));
        fim = now.with(LocalTime.of(6, 0));
      }

      BigDecimal somaValoresPeriodoNoturno =
          contaTransacionalPagamentoRepository
              .findSomaValoresPorPeriodo(contaTransacional, inicio, fim)
              .add(valorOperacao);

      if (valorOperacao.compareTo(contaTransacional.getLimiteNoturnoPorTransacao()) > 0) {
        throw new GenericServiceException(
            "Valor da operação maior que o limite noturno por transação");
      }

      if (somaValoresPeriodoNoturno.compareTo(contaTransacional.getLimiteNoturnoMaximo()) > 0) {
        throw new GenericServiceException(
            "Valor da operação irá ultrapassar o limite total do período noturno");
      }
    }
  }

  private BigDecimal trataValorInMais(BigDecimal valor, Boolean inMais) {
    return inMais ? valor.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP) : valor;
  }

  @Async
  @Transactional
  public void salvarPagamento(
      IncluirOrdemPagamentoRequest pagamentoRequest,
      IncluirOrdemPagamentoResponse ordemPagamento,
      Long numeroConta,
      String rrn) {

    Optional<ContaTransacional> contaTransacionalOptional =
        contaTransacionalService.findByIdConta(numeroConta);

    if (contaTransacionalOptional.isPresent()) {

      ContaTransacionalPagamento contaTransacionalPagamento = new ContaTransacionalPagamento();
      StatusPagamentoPixEnum status =
          StatusPagamentoPixEnum.fromDescription(
              ordemPagamento.getValue().getOrdemPagamento().getStatus().getDescricao());
      contaTransacionalPagamento.setContaTransacional(contaTransacionalOptional.get());
      contaTransacionalPagamento.setChaveBeneficiario(
          pagamentoRequest.getBeneficiario().getChave());

      LocalDateTime dataPagamento =
          pagamentoRequest.getDadosOperacao().getDataPagamento() != null
              ? pagamentoRequest.getDadosOperacao().getDataPagamento()
              : LocalDateTime.now();
      contaTransacionalPagamento.setDataPagamento(dataPagamento);
      contaTransacionalPagamento.setRrn(rrn);

      contaTransacionalPagamento.setAgenciaBeneficiario(
          pagamentoRequest.getBeneficiario().getConta().getAgencia());
      contaTransacionalPagamento.setContaBeneficiario(
          pagamentoRequest.getBeneficiario().getConta().getIdConta());
      contaTransacionalPagamento.setValor(pagamentoRequest.getDadosOperacao().getValorOperacao());
      contaTransacionalPagamento.setCampoLivre(pagamentoRequest.getDadosOperacao().getCampoLivre());
      contaTransacionalPagamento.setTransactionId(ordemPagamento.getTransactionId());
      contaTransacionalPagamento.setIspbBeneficiario(
          pagamentoRequest.getBeneficiario().getConta().getIspbParticipante());
      contaTransacionalPagamento.setNomeBeneficiario(
          pagamentoRequest.getBeneficiario().getPessoa().getNome());
      contaTransacionalPagamento.setEndToEnd(
          ordemPagamento.getValue().getOrdemPagamento().getEndToEnd());
      contaTransacionalPagamento.setDocumentoBeneficiario(
          pagamentoRequest.getBeneficiario().getPessoa().getInscricaoNacional());
      contaTransacionalPagamento.setDataInclusao(LocalDateTime.now());

      TipoContaEnum tipoContaById =
          pagamentoRequest.getBeneficiario().getConta().getTipoId() != null
              ? TipoContaEnum.getTipoContaById(
                  Integer.valueOf(pagamentoRequest.getBeneficiario().getConta().getTipoId()))
              : null;
      contaTransacionalPagamento.setTipoContaBeneficiario(tipoContaById);

      contaTransacionalPagamento.setStatus(status);
      contaTransacionalPagamentoRepository.save(contaTransacionalPagamento);
    }
  }

  @Async
  @Transactional
  public void salvarPagamentoErro(
      IncluirOrdemPagamentoRequest pagamentoRequest,
      Long numeroConta,
      String rrn,
      Boolean estornado,
      String errorMessage) {

    Optional<ContaTransacional> contaTransacionalOptional =
        contaTransacionalService.findByIdConta(numeroConta);

    if (contaTransacionalOptional.isPresent()) {

      ContaTransacionalPagamento contaTransacionalPagamento = new ContaTransacionalPagamento();

      contaTransacionalPagamento.setContaTransacional(contaTransacionalOptional.get());
      contaTransacionalPagamento.setChaveBeneficiario(
          pagamentoRequest.getBeneficiario().getChave());

      LocalDateTime dataPagamento =
          pagamentoRequest.getDadosOperacao().getDataPagamento() != null
              ? pagamentoRequest.getDadosOperacao().getDataPagamento()
              : LocalDateTime.now();
      contaTransacionalPagamento.setDataPagamento(dataPagamento);
      contaTransacionalPagamento.setRrn(rrn);

      contaTransacionalPagamento.setAgenciaBeneficiario(
          pagamentoRequest.getBeneficiario().getConta().getAgencia());
      contaTransacionalPagamento.setContaBeneficiario(
          pagamentoRequest.getBeneficiario().getConta().getIdConta());
      contaTransacionalPagamento.setValor(pagamentoRequest.getDadosOperacao().getValorOperacao());
      contaTransacionalPagamento.setCampoLivre(pagamentoRequest.getDadosOperacao().getCampoLivre());
      contaTransacionalPagamento.setIspbBeneficiario(
          pagamentoRequest.getBeneficiario().getConta().getIspbParticipante());
      contaTransacionalPagamento.setNomeBeneficiario(
          pagamentoRequest.getBeneficiario().getPessoa().getNome());
      contaTransacionalPagamento.setEndToEnd(pagamentoRequest.getBeneficiario().getEndToEnd());
      contaTransacionalPagamento.setDocumentoBeneficiario(
          pagamentoRequest.getBeneficiario().getPessoa().getInscricaoNacional());
      contaTransacionalPagamento.setDataInclusao(LocalDateTime.now());

      TipoContaEnum tipoContaById =
          pagamentoRequest.getBeneficiario().getConta().getTipoId() != null
              ? TipoContaEnum.getTipoContaById(
                  Integer.valueOf(pagamentoRequest.getBeneficiario().getConta().getTipoId()))
              : null;
      contaTransacionalPagamento.setTipoContaBeneficiario(tipoContaById);

      contaTransacionalPagamento.setStatus(StatusPagamentoPixEnum.ERRO);
      contaTransacionalPagamento.setEstornado(estornado);
      contaTransacionalPagamento.setInformacoes(errorMessage);
      contaTransacionalPagamentoRepository.save(contaTransacionalPagamento);
    }
  }

  @Transactional
  public ResponseEntity<ConsultarOrdemPagamentoResponse> consultarOrdem(
      String endToEnd, Integer idInstituicao) {

    String isPagamentoOrDevolucao = Character.toString(endToEnd.charAt(0));
    if (isPagamentoOrDevolucao.compareTo("D") == 0) {
      return consultarOrdemDevolucao(endToEnd, idInstituicao);
    }
    return consultarOrdemPagamento(endToEnd, idInstituicao, Boolean.FALSE);
  }

  @Transactional
  public ResponseEntity<ConsultarOrdemPagamentoResponse> consultarOrdemPagamento(
      String endToEnd, Integer idInstituicao, Boolean isCron) {

    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderById(idInstituicao);
    ResponseEntity<ConsultarOrdemPagamentoResponse> consultarOrdemPagamentoResponseResponseEntity =
        bancoRendimentoClient.consultarOrdemPagamento(endToEnd, instituicaoPix);

    ConsultarOrdemPagamentoResponse consultarOrdemPagamentoResponse =
        consultarOrdemPagamentoResponseResponseEntity.getBody();
    log.info("Resposta ao consultar ordem de pagamento -> " + consultarOrdemPagamentoResponse);
    if (Objects.nonNull(consultarOrdemPagamentoResponse)) {
      Optional<ContaTransacionalPagamento> contaTransacionalPagamentoOptional =
          findFirstByEndToEnd(endToEnd);

      StatusPagamentoPixEnum status =
          Objects.nonNull(consultarOrdemPagamentoResponse.getValue())
                  && Objects.nonNull(consultarOrdemPagamentoResponse.getValue().getOrdemPagamento())
              ? StatusPagamentoPixEnum.fromDescription(
                  consultarOrdemPagamentoResponse
                      .getValue()
                      .getOrdemPagamento()
                      .getStatus()
                      .getDescricao())
              : StatusPagamentoPixEnum.ERRO;

      String informacoes =
          consultarOrdemPagamentoResponse.getValue().getOrdemPagamento().getInformacoes();

      if (informacoes != null && informacoes.contains(SALDO_INSUFICIENTE_NA_CONTA_PI)) {
        metricsCollector.saldoInsuficienteContaPagamento(idInstituicao.toString()).increment();
      }

      if (contaTransacionalPagamentoOptional.isPresent()) {
        ContaTransacionalPagamento contaTransacionalPagamento =
            contaTransacionalPagamentoOptional.get();
        if (!contaTransacionalPagamento.getStatus().equals(status)) {

          if ((status.equals(StatusPagamentoPixEnum.ERRO)
                  || status.equals(StatusPagamentoPixEnum.RECUSADA))
              && contaTransacionalPagamento.getStatus().equals(StatusPagamentoPixEnum.ENVIADA)
              && contaTransacionalPagamento.getEstornado().equals(Boolean.FALSE)) {
            try {
              JcardResponse jcardResponse =
                  estornarPix(
                      contaTransacionalPagamento.getContaTransacional().getConta(),
                      contaTransacionalPagamento.getRrn(),
                      contaTransacionalPagamento.getValor());
              if (Boolean.TRUE.equals(jcardResponse.getSuccess())) {
                if (Constantes.ID_PRODUCAO_INSTITUICAO_RP3_BANK.equals(idInstituicao)) {
                  RepasseInstitucional repasseInstitucional =
                      repasseInstitucionalRepository.findByEndToEnd(endToEnd);
                  ContaPagamento contaPagamentoOrigem =
                      contaPagamentoService.findByIdConta(repasseInstitucional.getIdContaOrigem());
                  repasseInstitucional.setTransferenciaInternaSucesso(false);
                  repasseInstitucional.setEnvioPixSucesso(false);
                  repasseInstitucional.setRrnEstornoPix(jcardResponse.getRrn());
                  processaEstornoTransferenciaEntreContas(
                      contaPagamentoOrigem,
                      repasseInstitucional.getTransferenciaInternaRrn(),
                      repasseInstitucional.getValor(),
                      repasseInstitucional);
                  repasseInstitucionalRepository.save(repasseInstitucional);
                }
                contaTransacionalPagamento.setEstornado(true);
              }
            } catch (JcardServiceException e) {
              boolean estornadoAnteriormente = e.getMensagem().contains(ESTORNADO_ANTERIORMENTE);
              boolean transacaoNaoFinalizada = e.getMensagem().contains(TRANSACAO_NAO_FINALIZADA);

              if (estornadoAnteriormente) {
                contaTransacionalPagamento.setEstornado(true);
              }

              if (!isCron || !(estornadoAnteriormente || transacaoNaoFinalizada)) {
                throw e;
              }
            }
          }

          contaTransacionalPagamento.setStatus(status);
          contaTransacionalPagamento.setInformacoes(informacoes);
          contaTransacionalPagamentoRepository.save(contaTransacionalPagamento);
        }
      }
    }

    return consultarOrdemPagamentoResponseResponseEntity;
  }

  @Transactional
  public ResponseEntity<ConsultarOrdemPagamentoResponse> consultarOrdemDevolucao(
      String endToEnd, Integer idInstituicao) {

    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderById(idInstituicao);
    ResponseEntity<ConsultarOrdemPagamentoResponse> consultarOrdemPagamentoResponseResponseEntity =
        bancoRendimentoClient.consultarOrdemPagamento(endToEnd, instituicaoPix);
    ConsultarOrdemPagamentoResponse consultarOrdemPagamentoResponse =
        consultarOrdemPagamentoResponseResponseEntity.getBody();

    log.info("Resposta ao consultar ordem de pagamento -> " + consultarOrdemPagamentoResponse);

    if (Objects.nonNull(consultarOrdemPagamentoResponse)) {
      Optional<ContaTransacionalDevolucao> contaTransacionalDevolucaoOptional =
          contaTransacionalDevolucaoService.findFirstByEndToEndDevolucao(endToEnd);

      StatusPagamentoPixEnum status =
          Objects.nonNull(consultarOrdemPagamentoResponse.getValue())
                  && Objects.nonNull(consultarOrdemPagamentoResponse.getValue().getOrdemPagamento())
              ? StatusPagamentoPixEnum.fromDescription(
                  consultarOrdemPagamentoResponse
                      .getValue()
                      .getOrdemPagamento()
                      .getStatus()
                      .getDescricao())
              : StatusPagamentoPixEnum.ERRO;

      String informacoes =
          consultarOrdemPagamentoResponse.getValue().getOrdemPagamento().getInformacoes();

      if (contaTransacionalDevolucaoOptional.isPresent()) {
        ContaTransacionalDevolucao contaTransacionalDevolucao =
            contaTransacionalDevolucaoOptional.get();
        if (!contaTransacionalDevolucao.getStatus().equals(status)) {

          if ((status.equals(StatusPagamentoPixEnum.ERRO)
                  || status.equals(StatusPagamentoPixEnum.RECUSADA))
              && contaTransacionalDevolucao.getStatus().equals(StatusPagamentoPixEnum.ENVIADA)
              && contaTransacionalDevolucao.getEstornado().equals(Boolean.FALSE)) {

            JcardResponse jcardResponse =
                estornarPixDevolucao(
                    contaTransacionalDevolucao.getContaTransacional().getConta(),
                    contaTransacionalDevolucao.getValor());
            if (jcardResponse.getSuccess().equals(Boolean.TRUE)) {
              contaTransacionalDevolucao.setEstornado(true);
            }
          }
          contaTransacionalDevolucao.setStatus(status);
          contaTransacionalDevolucao.setInformacoes(informacoes);
          this.contaTransacionalDevolucaoService.save(contaTransacionalDevolucao);
        }
      }
    }

    return consultarOrdemPagamentoResponseResponseEntity;
  }

  private JcardResponse estornarPixDevolucao(ContaPagamento contaPagamento, BigDecimal valor) {
    try {
      String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);
      Integer idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
      ProdutoInstituicaoConfiguracao produto =
          produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(idProdutoInstituicao);
      List<Credencial> credenciais =
          credencialService.findByIdContaAndTitularidadeOrderByCsnDesc(
              contaPagamento.getIdConta(), 1);

      String accountCode = contaPagamentoService.getOrPrepareAccountCode(contaPagamento, produto);

      boolean isProdutoMoeda =
          Constantes.CODIGO_MOEDA_DE_PONTO.equals(produto.getMoeda().getIdMoeda());
      Optional<CotacaoPontos> cotacaoPontosOptional =
          Optional.ofNullable(
              cotacaoPontosRepository.findByIdInstituicao(contaPagamento.getIdInstituicao()));
      CotacaoPontos cotacaoPontos = cotacaoPontosOptional.orElseGet(CotacaoPontos::new);
      Integer stan = lancamentoService.getStan();
      String textoExtrato = String.format("Estorno de Pix no valor de %s", valor);
      return lancamentoService.doLancamentoManual(
          accountCode,
          Constantes.COD_TRANSACAO_ESTORNO_ENVIO_PIX,
          isProdutoMoeda ? valor.multiply(cotacaoPontos.getValorConversao()) : valor,
          produto.getMoeda().getIdMoeda().toString(),
          rrn,
          1,
          credenciais.get(0).getTokenInterno(),
          contaPagamento.getIdConta(),
          textoExtrato,
          null,
          null,
          null,
          stan,
          null);
    } catch (GenericServiceException e) {
      throw new GenericServiceException("Erro ao realizar crédito no Jcard para estorno de Pix");
    }
  }

  private JcardResponse estornarPix(ContaPagamento contaPagamento, String rrn, BigDecimal valor) {
    try {
      Integer idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
      ProdutoInstituicaoConfiguracao produto =
          produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(idProdutoInstituicao);
      return lancamentoService.doLancamentoEstorno(
          contaPagamento.getIdConta(),
          rrn,
          Constantes.COD_TRANSACAO_ESTORNO_ENVIO_PIX,
          produto.getMoeda().getIdMoeda().toString(),
          valor);
    } catch (GenericServiceException e) {
      throw new GenericServiceException("Erro ao realizar crédito no Jcard para estorno de Pix");
    }
  }

  public Optional<ContaTransacionalPagamento> findFirstByEndToEnd(String endToEnd) {
    return contaTransacionalPagamentoRepository.findFirstByEndToEnd(endToEnd);
  }

  @Scheduled(cron = "0 0 8/12 * * ?")
  public void atualizarOrdensPagamentoSchedule() {

    if ("true".equals(executarAtualizarOrdensPagamentos)) {
      log.info("Iniciando Cron JOB de consulta das Ordens de Pagamento PIX Pendentes.");

      LocalDate hoje = LocalDate.now();
      LocalDateTime inicioDia = hoje.atStartOfDay();
      LocalDateTime trintaDiasAtras = inicioDia.minusDays(30);

      List<ContaTransacionalPagamento> pagamentosList =
          contaTransacionalPagamentoRepository.findByStatusAndDataInclusaoBefore(
              StatusPagamentoPixEnum.ENVIADA, trintaDiasAtras);
      pagamentosList.forEach(
          pagamento -> {
            Integer idInstituicao = pagamento.getContaTransacional().getConta().getIdInstituicao();
            consultarOrdemPagamento(pagamento.getEndToEnd(), idInstituicao, Boolean.TRUE);
          });
    } else {
      log.info(
          "Iniciou a execução da Cron JOB de consulta das Ordens de Pagamento PIX Pendentes na instância 2 agora.");
    }
  }

  public String encontraLoteParaExtratoAutomatizacaoCargaPIXPorRRN(String rrn) {
    return contaTransacionalPagamentoRepository.encontraLoteParaExtratoAutomatizacaoCargaPIXPorRRN(
        rrn);
  }

  public ResponseEntity<IncluirOrdemPagamentoResponse> transferirEntreContasEEnvioPix(
      Long idCredencialOrigem,
      IncluirOrdemPagamentoRequest incluirOrdemPagamentoRequest,
      BigDecimal valorOperacao,
      String tokenJWT,
      InstituicaoPix instituicaoPix,
      ContaPagamento contaOrigem,
      String documentoRepresentante) {

    Credencial credencialDestino =
        credencialRepository.findUltimaCredencialDesbloqueadaTitularConta(
            utilService.getIdContaRepasseRp3());
    if (credencialDestino == null) {
      throw new GenericServiceException("Não foi possível encontrar a credencial de destino");
    }

    RepasseInstitucional repasseInstitucional = new RepasseInstitucional();
    repasseInstitucional.setIdInstituicao(instituicaoPix.getIdInstituicao());
    repasseInstitucional.setIdContaOrigem(contaOrigem.getIdConta());
    repasseInstitucional.setIdContaInstitucional(utilService.getIdContaRepasseRp3());
    repasseInstitucional.setValor(valorOperacao);
    repasseInstitucional.setDtHrInclusao(new Date());

    JcardResponse jcardResponse = null;
    ResponseEntity<IncluirOrdemPagamentoResponse> incluirOrdemPagamentoResponseResponseEntity =
        null;

    JcardResponse sucesso =
        facade.realizarTransferencia(
            instituicaoPix.getIdInstituicao(),
            idCredencialOrigem,
            null,
            valorOperacao,
            tokenJWT,
            false,
            false,
            credencialDestino,
            Constantes.COD_TRANSACAO_SOLICITACAO_ENVIO_PIX,
            documentoRepresentante,
            null,
            null);

    repasseInstitucional.setTransferenciaInternaRrn(
        sucesso.getRrn() != null ? sucesso.getRrn() : null);

    if (!sucesso.getSuccess()) {
      Optional<IncluirOrdemPagamentoResponse> incluirOrdemPagamentoResponse =
          Optional.of(new IncluirOrdemPagamentoResponse());
      JsonObject jsonObject = JsonParser.parseString(sucesso.getErrors()).getAsJsonObject();
      String mensagemErro = jsonObject.get("errors").getAsString();
      return tratarRespostaAposEstornarRepasse(mensagemErro, repasseInstitucional, sucesso, false);
    } else {
      repasseInstitucional.setTransferenciaInternaSucesso(true);

      ContaTransacional contaTransacional =
          contaTransacionalService
              .findByIdConta(utilService.getIdContaRepasseRp3())
              .orElseGet(
                  () ->
                      contaTransacionalService.criarContaTransacional(
                          utilService.getIdContaRepasseRp3(), instituicaoPix));
      if (contaTransacional == null) {
        JcardResponse estornoTransferenciaEntreContas =
            estornarTransferenciaEntreContas(contaOrigem, sucesso.getRrn(), valorOperacao);
        if (estornoTransferenciaEntreContas.getSuccess()) {
          return tratarRespostaAposEstornarRepasse(
              ConstantesErro.PIX_CONTA_PIX_NAO_ENCONTRADA.getMensagem(),
              repasseInstitucional,
              sucesso,
              true);
        }
      }

      br.com.sinergico.vo.PessoaVO pessoaVO =
          pessoaService.findByIdConta(utilService.getIdContaRepasseRp3());
      incluirOrdemPagamentoRequest
          .getPagador()
          .getConta()
          .setInscricaoNacional(pessoaVO.getDocumento());
      incluirOrdemPagamentoRequest
          .getPagador()
          .getPessoa()
          .setInscricaoNacional(pessoaVO.getDocumento());
      incluirOrdemPagamentoRequest.getPagador().getPessoa().setNome(pessoaVO.getNomeCompleto());
      incluirOrdemPagamentoRequest.getPagador().getPessoa().setTipoId("2");
      incluirOrdemPagamentoRequest.getPagador().getConta().setTipoId("1");
      String numeroContaComDV =
          contaTransacional.getConta().getIdConta().toString() + contaTransacional.getDvConta();
      ValidaContaOrigemIgualContaDestino validacao =
          getValidaContaOrigemIgualContaDestino(incluirOrdemPagamentoRequest, numeroContaComDV);

      incluirOrdemPagamentoRequest.setNumeroContaPagador(numeroContaComDV);
      String textoExtrato = String.format("PIX enviado no valor de %s", valorOperacao);
      String rrn = lancamentoService.getRrn(Constantes.PREFIXO_RRN_PORTADOR);

      jcardResponse =
          movimentarContaPix(
              contaTransacional.getConta(),
              valorOperacao,
              textoExtrato,
              rrn,
              Constantes.COD_TRANSACAO_ENVIO_PIX,
              -1);

      if (Objects.nonNull(jcardResponse) && !jcardResponse.getSuccess().equals(Boolean.TRUE)) {
        JcardResponse estornoTransferenciaEntreContas =
            estornarTransferenciaEntreContas(contaOrigem, sucesso.getRrn(), valorOperacao);
        if (estornoTransferenciaEntreContas.getSuccess()) {
          return tratarRespostaAposEstornarRepasse(
              ConstantesErro.PAG_PIX_ERRO_AO_DEBITAR.getMensagem(),
              repasseInstitucional,
              sucesso,
              true);
        }
      }

      incluirOrdemPagamentoRequest.setNumeroContaPagador(numeroContaComDV);
      incluirOrdemPagamentoRequest.setNsu(rrn.replaceAll(Constantes.PREFIXO_RRN_PORTADOR, "x"));

      if (incluirOrdemPagamentoRequest.getDadosOperacao().getCampoLivre() != null) {
        String caracteresInvalidos = "[^\\p{L}\\p{M}\\p{N}\\p{P}\\p{Z}\\p{Cf}\\p{Cs}\\s]";
        Pattern pattern = Pattern.compile(caracteresInvalidos);
        Matcher matcher =
            pattern.matcher(incluirOrdemPagamentoRequest.getDadosOperacao().getCampoLivre());
        boolean contemCaracteresInvalidos = matcher.find();
        if (contemCaracteresInvalidos) {
          String campoLivreSemEmoji =
              incluirOrdemPagamentoRequest
                  .getDadosOperacao()
                  .getCampoLivre()
                  .replaceAll(caracteresInvalidos, "");
          incluirOrdemPagamentoRequest.getDadosOperacao().setCampoLivre(campoLivreSemEmoji);
        }
      }

      try {
        incluirOrdemPagamentoResponseResponseEntity =
            bancoRendimentoClient.incluirOrdemPagamento(
                instituicaoPix, incluirOrdemPagamentoRequest);

        if (incluirOrdemPagamentoResponseResponseEntity.getBody() != null
            && incluirOrdemPagamentoResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)) {

          salvarPagamento(
              incluirOrdemPagamentoRequest,
              incluirOrdemPagamentoResponseResponseEntity.getBody(),
              contaTransacional.getConta().getIdConta(),
              rrn);
          Optional<ContaTransacionalPagamento> contaTransacionalPagamentoOptional =
              contaTransacionalPagamentoRepository.findFirstByEndToEnd(
                  incluirOrdemPagamentoResponseResponseEntity
                      .getBody()
                      .getValue()
                      .getOrdemPagamento()
                      .getEndToEnd());
          repasseInstitucional.setEnvioPixSucesso(true);
          repasseInstitucional.setIdContaTransacionalPagamento(
              contaTransacionalPagamentoOptional.get().getId());
          repasseInstitucional.setEndToEnd(
              incluirOrdemPagamentoResponseResponseEntity
                  .getBody()
                  .getValue()
                  .getOrdemPagamento()
                  .getEndToEnd());
          repasseInstitucional.setRrnEnvioPix(rrn);
          eventoService.publicarMovimentacaoFinanceiraEvent(
              Servicos.PIX.getDescricao(),
              instituicaoPix.getIdInstituicao(),
              validacao.agenciaBeneficiario != null
                  ? Integer.valueOf(validacao.agenciaBeneficiario)
                  : null,
              utilService.getIdContaRepasseRp3(),
              validacao.idContaBeneficiario != null
                  ? Long.valueOf(validacao.idContaBeneficiario)
                  : null,
              Constantes.COD_TRANSACAO_ENVIO_PIX,
              null,
              valorOperacao,
              incluirOrdemPagamentoRequest.getBeneficiario().getChave() != null
                  ? incluirOrdemPagamentoRequest.getBeneficiario().getChave()
                  : "0");

          // Salvar o limite diario
          limitesContaService.salvarLimiteDiario(valorOperacao, contaOrigem.getIdConta());

        } else if (incluirOrdemPagamentoResponseResponseEntity
                .getStatusCode()
                .equals(HttpStatus.INTERNAL_SERVER_ERROR)
            && ((BancoRendimentoResponse) incluirOrdemPagamentoResponseResponseEntity.getBody())
                .getErroMessage()
                .getMessage()
                .equals(MSG_ERRO_TIMEOUT_RENDIMENTO)) {
          salvarPagamentoErro(
              incluirOrdemPagamentoRequest,
              contaTransacional.getConta().getIdConta(),
              rrn,
              false,
              null);
          return incluirOrdemPagamentoResponseResponseEntity;
        } else {
          JcardResponse estornoPix = estornarPix(contaTransacional.getConta(), rrn, valorOperacao);
          if (estornoPix.getSuccess()) {
            processaEstornoTransferenciaEntreContas(
                contaOrigem, sucesso.getRrn(), valorOperacao, repasseInstitucional);
          }

          String errorMessage = extrairMensagemErro(incluirOrdemPagamentoResponseResponseEntity);
          salvarPagamentoErro(
              incluirOrdemPagamentoRequest,
              contaTransacional.getConta().getIdConta(),
              rrn,
              true,
              errorMessage);
          atualizarRepasseInstitucionalComFalha(repasseInstitucional, estornoPix);
          repasseInstitucionalRepository.save(repasseInstitucional);
          return incluirOrdemPagamentoResponseResponseEntity;
        }
      } catch (GenericServiceException e) {
        log.warn(e.getMensagem(), e);
        salvarPagamentoErro(
            incluirOrdemPagamentoRequest,
            contaTransacional.getConta().getIdConta(),
            rrn,
            false,
            null);
      } catch (Exception e) {
        log.warn(ConstantesErro.PAG_PIX_ERRO_COMUNICACAO_RENDIMENTO.getMensagem());
        salvarPagamentoErro(
            incluirOrdemPagamentoRequest,
            contaTransacional.getConta().getIdConta(),
            rrn,
            false,
            null);
      }
    }

    repasseInstitucionalRepository.save(repasseInstitucional);
    return incluirOrdemPagamentoResponseResponseEntity;
  }

  private ResponseEntity<IncluirOrdemPagamentoResponse> tratarRespostaAposEstornarRepasse(
      String mensagemErro,
      RepasseInstitucional repasseInstitucional,
      JcardResponse sucesso,
      Boolean estorno) {
    Optional<IncluirOrdemPagamentoResponse> incluirOrdemPagamentoResponse =
        Optional.of(new IncluirOrdemPagamentoResponse());
    incluirOrdemPagamentoResponse.get().setMessage(mensagemErro);
    incluirOrdemPagamentoResponse.get().setSuccess(false);
    repasseInstitucional.setTransferenciaInternaSucesso(estorno);
    repasseInstitucional.setEnvioPixSucesso(false);
    repasseInstitucional.setTransferenciaInternaMensagem(mensagemErro);
    if (estorno) {
      repasseInstitucional.setTransferenciaInternaEstornada(true);
      repasseInstitucional.setTransferenciaInternaEstornoRrn(sucesso.getRrn());
    }
    repasseInstitucionalRepository.save(repasseInstitucional);
    return ResponseEntity.of(incluirOrdemPagamentoResponse);
  }

  private void processaEstornoTransferenciaEntreContas(
      ContaPagamento contaOrigem,
      String rrn,
      BigDecimal valorOperacao,
      RepasseInstitucional repasseInstitucional) {
    JcardResponse estornoTransferenciaEntreContas =
        estornarTransferenciaEntreContas(contaOrigem, rrn, valorOperacao);
    if (estornoTransferenciaEntreContas.getSuccess()) {
      repasseInstitucional.setTransferenciaInternaEstornada(true);
      repasseInstitucional.setTransferenciaInternaEstornoRrn(
          estornoTransferenciaEntreContas.getRrn());
    }
  }

  private JcardResponse estornarTransferenciaEntreContas(
      ContaPagamento contaPagamento, String rrn, BigDecimal valor) {
    try {
      Integer idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
      ProdutoInstituicaoConfiguracao produto =
          produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(idProdutoInstituicao);
      return lancamentoService.doLancamentoEstorno(
          contaPagamento.getIdConta(),
          rrn,
          Constantes.COD_TRANSACAO_ESTORNO_SOLICITACAO_ENVIO_PIX,
          produto.getMoeda().getIdMoeda().toString(),
          valor);
    } catch (GenericServiceException e) {
      throw new GenericServiceException("Erro ao realizar crédito no Jcard para estorno de Pix");
    }
  }

  private String extrairMensagemErro(
      ResponseEntity<IncluirOrdemPagamentoResponse> incluirOrdemPagamentoResponseResponseEntity) {
    return incluirOrdemPagamentoResponseResponseEntity
        .getBody()
        .getErroMessage()
        .getErrors()
        .stream()
        .map(ErroDetailVO::getMessage)
        .collect(Collectors.joining(";"));
  }

  private void atualizarRepasseInstitucionalComFalha(
      RepasseInstitucional repasseInstitucional, JcardResponse estorno) {
    repasseInstitucional.setEnvioPixSucesso(false);
    repasseInstitucional.setRrnEstornoPix(estorno.getRrn());
  }
}
