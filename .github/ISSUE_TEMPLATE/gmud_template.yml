
name: GMUD
description: Abertura de card para GMUD.
title: "[GMUD]: coloque_aqui_breve_descrição_da_mudança"
projects: "valloo-tecnologia/39"
labels: "enhancement"
type: "Feature"
body:
  - type: markdown
    attributes:
      value: |
        **<PERSON><PERSON><PERSON> as informações abaixo para abrir uma GMUD.**
        Certifique-se de fornecer todos os detalhes necessários para uma análise rápida e eficiente.
  - type: textarea
    id: descricao
    attributes:
      label: GMUD
      description: Descreva detalhadamente o que será alterado ou implementado.
      value: |  
        -----
        
        ### 🎯 `Objetivo da mudança`
        
        _Descreva aqui, de forma clara e objetiva, o motivo da mudança e o que ela pretende resolver ou melhorar._

        ### 🔁 `Planejamento de reversão`
        
        _Explique aqui como será feita a reversão em caso de erro. Informe se há script de rollback, reexecução do processo anterior ou outros mecanismos._

        ### 🔗 `Link do Pull Request`
        
        _Utilize `#` para facilitar a localização do PR no repositório._

        ### ✅ Tarefas relacionadas:
        _coloque aqui os links das tarefas relacionadas_  
        Exemplo:
        - GitHub Issue: [#789](https://github.com/sua-org/seu-repo/issues/numero_issue)
        
        -----
    validations:
      required: true
  - type: dropdown
    id: squad
    attributes:
      label: SQUAD
      description: Selecione o SQUAD no qual pertence.
      options:
        - SQUAD
        - BLUE
        - RED
        - SILVER
        - SHADOW
        - TRACKER
      default: 0
    validations:
      required: true
  - type: dropdown
    id: risco
    attributes:
      label: Risco da GMUD
      description: Selecione o nível de risco da implementação.
      options:
        - Baixo
        - Médio
        - Alto
      default: 0
    validations:
      required: true
  - type: dropdown
    id: impacto
    attributes:
      label: Impacto da GMUD
      description: Selecione o nível de impacto esperado.
      options:
        - Baixo
        - Médio
        - Alto
      default: 0
    validations:
      required: true
  - type: dropdown
    id: tipo
    attributes:
      label: Tipo da GMUD
      description: Selecione o tipo da mudança.
      options:
        - Selecione
        - Projetos
        - Sustentação
      default: 0
    validations:
      required: true
