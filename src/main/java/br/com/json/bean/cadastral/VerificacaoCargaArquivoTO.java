package br.com.json.bean.cadastral;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 */
@JsonInclude
public class VerificacaoCargaArquivoTO implements Serializable {
  private static final long serialVersionUID = 1L;

  /** Nome do arquivo */
  private String nomeArquivo;

  private String msg;
  private Integer status;

  /** Lista de verificações */
  private List<String> verificacoes;

  private List<String> listaValorRepetido;

  private List<? extends ImportacaoArquivoTO> registros;

  private static final String VALID_ERROR = "1";

  public VerificacaoCargaArquivoTO(
      String nomeArquivo, List<? extends ImportacaoArquivoTO> importacao) {

    setNomeArquivo(nomeArquivo);
    setRegistros(importacao);
  }

  public VerificacaoCargaArquivoTO() {}

  /** Buscar a quantidade de registros inválidos */
  public Integer getQtdErros() {

    List<String> novoArray = new ArrayList<String>();

    for (String tmp : getVerificacoes()) {
      if (tmp.substring(tmp.length() - 1).equals(VALID_ERROR)) {
        novoArray.add(tmp.toString());
      }
    }

    return novoArray.size();
  }

  /** Buscar a quantidade de registros OK */
  public Integer getQtdOk() {
    if (getRegistros() != null) {
      return getRegistros().size() - getQtdErros();
    }
    return 0;
  }

  /** Buscar a quantidade total de registros */
  public Integer getQtdRegistros() {
    if (getRegistros() != null) {
      return getRegistros().size();
    }
    return 0;
  }

  /** Carregar verificações a partir de um mapa, para cadastro COMPLETO de funcionario/portador */
  public void loadVerificacoesCadCompl(Map<String, VerificacaoLinhaTO> verificacoes) {
    if (verificacoes != null && !verificacoes.isEmpty()) {
      for (Entry<String, VerificacaoLinhaTO> entry : verificacoes.entrySet()) {
        String key = entry.getKey().substring(0, entry.getKey().lastIndexOf(':'));
        switch (key) {
          case "SETOR_FILIAL":
            addSetorFilialInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "LIMITE_MAXIMO":
            addLimiteConvenioInvalido(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "TIPO_DOCUMENTO":
            addTipoDocInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CPF":
            addCpfInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CPF_PRODUTO_CADASTRADO":
            addCpfProdutoCadastradoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NOME_COMPLETO":
            addNomeCompletoInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NOME_MAE":
            addNomeMaeInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NOME_PAI":
            addNomePaiInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NATURALIDADE":
            addNaturalidadeInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NACIONALIDADE":
            addNacionalidadeInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NUMERO_RG":
            addNumeroRgInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "EMISSAO_RG":
            addEmissaoRgInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "EMISSOR_RG":
            addEmissorRgInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "SEXO":
            addSexoInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "ESTADO_CIVIL":
            addEstadoCivilInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DATA_NASCIMENTO":
            addDtNascimentoInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "EMAIL_PROFISSIONAL":
            addEmailProfissionalInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "LOGRADOURO":
            addLogradouroInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CIDADE":
            addCidadeInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NUMERO":
            addNumeroInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "BAIRRO":
            addBairroInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "UF":
            addUfInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CEP":
            addCepInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "COMPLEMENTO":
            addComplementoInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "E_MAIL":
            addEmailInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DDD":
            addDddCelularInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CELULAR":
            addCelularInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DDD_RESIDENCIAL":
            addDddResidencialInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "TELEFONE_RESIDENCIAL":
            addTelefoneResidencialInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "BANCO":
            addBancoInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "AGENCIA":
            addAgenciaInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CONTA":
            addContaBancariaInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "TIPO_CONTA":
            addTipoContaBancariaInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DADOS_BANCARIOS":
            addDadosBancariosInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;

          case "ERRO":
            addErro(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DUPLICADO":
            addDuplicado(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "NOME_CARTAO_EXCLUSIVO":
            addNomeCartaoExclusivo(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "UF_EMISSOR_RG":
            addNomeUFEmissorRG(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CHAVE_PIX":
            addChavePixInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CHAVE_PIX_DOCUMENTO":
            addChavePixDocumentoInvalidoCadCompl(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
        }
      }
    }
  }

  public void loadVerificacoesCadPortadores(Map<String, VerificacaoLinhaTO> verificacoes) {
    if (verificacoes != null && !verificacoes.isEmpty()) {
      for (Entry<String, VerificacaoLinhaTO> entry : verificacoes.entrySet()) {
        String key = entry.getKey().substring(0, entry.getKey().lastIndexOf(':'));

        if (!key.equals("valorAtualizado")) {
          switch (key) {
            case "LIMITE_MAXIMO":
              addLimiteConvenioInvalido(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF":
              addCpfInvalido(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF_PRODUTO":
              addCpfProdutoNaoEncontrado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF_PRODUTO_CADASTRADO":
              addCpfProdutoCadastrado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF_PRODUTO_MULTI_CONTAS":
              addCpfProdutoMultiContas(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CONTA_PRODUTO":
              addContaProdutoDesativado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "MATRICULA_CADASTRADA":
              addMatriculaCadastrada(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "MATRICULA_CADASTRADA_SISTEMA":
              addMatriculaCadastrada(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;

            case "NOME_COMPLETO":
              addNomeCompletoInvalidoCadPortadores(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
            case "E_MAIL":
              addEmailCadPortadores(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "DDD":
              addDDDCadPortadores(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CELULAR":
              addCelularCadPortadores(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "ERRO":
              addErro(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "DUPLICADO":
              addDuplicado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
          }
        }
      }
    }
  }

  /** Carregar verificações a partir de um mapa, para cadastro funcionario */
  public void loadVerificacoes(Map<String, VerificacaoLinhaTO> verificacoes) {
    if (verificacoes != null && !verificacoes.isEmpty()) {
      for (Entry<String, VerificacaoLinhaTO> entry : verificacoes.entrySet()) {
        String key = entry.getKey().substring(0, entry.getKey().lastIndexOf(':'));

        if (!key.equals("valorAtualizado")) {
          switch (key) {
            case "LIMITE_MAXIMO":
              addLimiteConvenioInvalido(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF":
              addCpfInvalido(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF_PRODUTO":
              addCpfProdutoNaoEncontrado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CPF_PRODUTO_CADASTRADO":
              addCpfProdutoCadastrado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CONTA_PRODUTO":
              addContaProdutoDesativado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "MATRICULA_CADASTRADA":
              addMatriculaCadastrada(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "MATRICULA_CADASTRADA_SISTEMA":
              addMatriculaCadastrada(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "MATRICULA":
              addMatriculaInvalida(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "NOME_COMPLETO":
              addNomeCompletoInvalido(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "VALOR_CARGA":
              addCargaValor(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "SETOR_FILIAL":
              addSetorFilial(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "DATA_NASCIMENTO":
              addDataNascimento(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "E_MAIL":
              addEmail(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "DDD":
              addDDD(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "CELULAR":
              addCelular(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "PARCELAMENTO":
              addParcelamento(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "ERRO":
              addErro(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
            case "DUPLICADO":
              addDuplicado(
                  entry.getValue().getVerificacao(),
                  entry.getValue().getLinha(),
                  entry.getValue().getErroRegistros());
              break;
          }
        }
      }
    }
  }

  /** Carregar verificações a partir de um mapa, para carga via arquivo */
  public void loadVerificacoesCargas(Map<String, VerificacaoLinhaTO> verificacoes) {
    if (verificacoes != null && !verificacoes.isEmpty()) {
      for (Entry<String, VerificacaoLinhaTO> entry : verificacoes.entrySet()) {
        String key = entry.getKey().substring(0, entry.getKey().lastIndexOf(':'));
        switch (key) {
          case "TIPO_DOCUMENTO":
            addTipoDocInvalidoCarga(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CPF":
            addCpfInvalidoCarga(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CPF_PRODUTO":
            addCpfProdutoNaoEncontradoCarga(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "CONTA_PRODUTO":
            addContaProdutoDesativadoCarga(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "VALOR_CARGA":
            addCargaValorCarga(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "ERRO":
            addErro(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DUPLICADO":
            addCargaDuplicado(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
        }
      }
    }
  }

  public void loadVerificacoesLimiteCargas(Map<String, VerificacaoLinhaTO> verificacoes) {
    if (verificacoes != null && !verificacoes.isEmpty()) {
      for (Entry<String, VerificacaoLinhaTO> entry : verificacoes.entrySet()) {
        String key = entry.getKey().substring(0, entry.getKey().lastIndexOf(':'));
        switch (key) {
          case "CPF":
            addCpfInvalidoCargaLimite(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "MATRICULA":
            addMatriculaInvalidaOuVazia(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "VALOR_CARGA":
            addCargaValorCargaLimite(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "ERRO":
            addErro(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
          case "DUPLICADO":
            addCargaDuplicado(
                entry.getValue().getVerificacao(),
                entry.getValue().getLinha(),
                entry.getValue().getErroRegistros());
            break;
        }
      }
    }
  }

  /** Buscar o valor total da carga */
  public BigDecimal getValorTotalCarga() {
    BigDecimal total = new BigDecimal(0);
    for (ImportacaoArquivoTO to : getRegistros()) {
      if (to.getValorCarga() != null) {
        total = total.add(to.getValorCarga());
      }
    }
    return total;
  }

  // CARGA VIA ARQUIVO

  /** Adicionar um restrição para cadastro duplicado */
  public void addCargaDuplicado(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", com registro já existente/repetido dentro da lista verificada.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para TIPO DOCUMENTO em carga via arquivo */
  public void addTipoDocInvalidoCarga(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A', deve receber 1 para CPF ou 2 para CNPJ. ")
        .append(" - Recebendo: ")
        .append(cpf)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para DOCUMENTO em carga via arquivo */
  public void addCpfInvalidoCarga(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();
    if (cpf.equals("00000000000")) {
      builder
          .append("Linha ")
          .append("'" + linha + "'")
          .append(", coluna 'B', deve receber um CPF válido. ")
          .append(" - Recebendo: ")
          .append("<<NÃO INFORMADO>>")
          .append(erroRegistros);
    } else {
      builder
          .append("Linha ")
          .append("'" + linha + "'")
          .append(", coluna 'B', deve receber um CPF válido. ")
          .append(" - Recebendo: ")
          .append(cpf)
          .append(".")
          .append(erroRegistros);
    }

    getVerificacoes().add(builder.toString());
  }

  public void addCpfInvalidoCargaLimite(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();
    if (cpf.equals("00000000000")) {
      builder
          .append("Linha ")
          .append("'" + linha + "'")
          .append(", coluna 'A', deve receber um CPF válido. ")
          .append(" - Recebendo: ")
          .append("<<NÃO INFORMADO>>")
          .append(erroRegistros);
    } else {
      builder
          .append("Linha ")
          .append("'" + linha + "'")
          .append(", coluna 'A', deve receber um CPF válido. ")
          .append(" - Recebendo: ")
          .append(cpf)
          .append(".")
          .append(erroRegistros);
    }

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para cpf em carga via arquivo */
  public void addErro(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", COM ERRO ")
        .append(" - Recebendo cpf: ")
        .append(cpf + ".")
        .append(erroRegistros);
    getVerificacoes().add(builder.toString());
  }

  /** Adicionar restrição para funcionário não associado a um produto */
  public void addCpfProdutoNaoEncontradoCarga(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A', com o CPF: ")
        .append("<<" + cpf + ">>")
        .append(" - NÃO CADASTRADO PARA O PRODUTO INFORMADO.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar restrição para conta desativada */
  public void addContaProdutoDesativadoCarga(String conta, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Conta referente à linha ")
        .append("'" + linha + "', deve ser ativada para receber carga.")
        .append(" - CONTA DESATIVADA PARA O PRODUTO INFORMADO.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar restrição para conta desativada */
  public void addMatriculaInvalidaOuVazia(String conta, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'B' deve conter a Matricula. - MATRICULA INVALIDA.: ")
        .append(erroRegistros);
    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para carga valor não informada
   *
   * @param cargaValor
   * @param linha
   */
  public void addCargaValorCarga(String cargaValor, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'B' deve conter o Valor da Carga. - Recebendo: ")
        .append(cargaValor)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addCargaValorCargaLimite(String cargaValor, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'D' deve conter o Valor da Carga. - Recebendo: ")
        .append(cargaValor)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  // CADASTRO DE FUNCIONARIOS VIA ARQUIVO

  /** Adicionar um restrição para Limite de convênio maior que limite global da empreesa */
  public void addLimiteConvenioInvalido(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder.append("Linha ").append("'" + linha + "', ").append(cpf).append(".");

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para cadastro duplicado */
  public void addDuplicado(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", com registro já existente/repetido dentro da lista verificada.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para cpf de funcionario */
  public void addSetorFilialInvalidoCadCompl(String sf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AE', deve receber o nome do Setor ou Filial do portador. ")
        .append(" - Recebendo: ")
        .append(sf)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para cpf de funcionario */
  public void addCpfInvalido(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A', deve receber um CPF válido. ")
        .append(" - Recebendo: ")
        .append(cpf)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para tipoDocumento de funcionario */
  public void addTipoDocInvalidoCadCompl(String tipo, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'B', deve receber 1 para CPF ou 2 para CNPJ. ")
        .append(" - Recebendo: ")
        .append(tipo)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para documento de funcionario */
  public void addCpfInvalidoCadCompl(String doc, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'C', deve receber um Documento válido. ")
        .append(" - Recebendo: ")
        .append(doc)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para conta desativada */
  public void addContaProdutoDesativado(String conta, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'.")
        .append(" - Conta ")
        .append(conta)
        .append(" desativada.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para matrícula */
  public void addMatriculaInvalida(String matricula, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(
            ", coluna 'B', deve receber uma Matrícula alfa-numérica de até dez caracteres. - Recebendo: ")
        .append(matricula)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para o nome completo */
  public void addNomeCompletoInvalido(String nomeCompleto, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'C', deve receber um Nome Completo. - Recebendo: ")
        .append(nomeCompleto)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addNomeCompletoInvalidoCadPortadores(
      String nomeCompleto, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'B', deve receber um Nome Completo. - Recebendo: ")
        .append(nomeCompleto)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para o nome completo */
  public void addNomeCompletoInvalidoCadCompl(
      String nomeCompleto, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A', deve receber um Nome Completo. - Recebendo: ")
        .append(nomeCompleto)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para o nome mãe */
  public void addNomeMaeInvalidoCadCompl(String nomeMae, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'D', deve receber um Nome Completo. - Recebendo: ")
        .append(nomeMae)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para o nome pai */
  public void addNomePaiInvalidoCadCompl(String nomePai, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'E', deve receber um Nome Completo. - Recebendo: ")
        .append(nomePai)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para naturalidade */
  public void addNaturalidadeInvalidoCadCompl(
      String naturalidade, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'G', deve receber a naturalidade. - Recebendo: ")
        .append(naturalidade)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para nacionalidade */
  public void addNacionalidadeInvalidoCadCompl(
      String nacionalidade, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'F', deve receber a nacionalidade. - Recebendo: ")
        .append(nacionalidade)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para RG */
  public void addNumeroRgInvalidoCadCompl(String rg, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'H', deve receber o número de RG (Registro Geral). - Recebendo: ")
        .append(rg)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para data de emissão do RG */
  public void addEmissaoRgInvalidoCadCompl(String emissaoRg, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'I', deve receber a data de emissão do RG. - Recebendo: ")
        .append(emissaoRg)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para órgão emissor do RG */
  public void addEmissorRgInvalidoCadCompl(String emissorRg, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'J', deve receber o órgão expedidor do RG. - Recebendo: ")
        .append(emissorRg)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para o sexo/gênero */
  public void addSexoInvalidoCadCompl(String sexo, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(
            ", coluna 'K', deve receber a primeira letra do sexo/gênero. Ex.: M ou F.  - Recebendo: ")
        .append(sexo)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para estado cicil */
  public void addEstadoCivilInvalidoCadCompl(
      String estadoCivil, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'L', deve receber número identificador do estado civil. - Recebendo: ")
        .append(estadoCivil)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para data de nascimento */
  public void addDtNascimentoInvalidoCadCompl(String dtNasc, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'M', deve receber a data de nascimento. - Recebendo: ")
        .append(dtNasc)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addDataNascimentoCadPortadores(
      String dataNascimento, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'D', deve conter a Data de Nascimento. - Recebendo: ")
        .append(dataNascimento)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para logradouro */
  public void addLogradouroInvalidoCadCompl(
      String logradouro, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'N', deve receber logradouro. - Recebendo: ")
        .append(logradouro)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para bairro */
  public void addBairroInvalidoCadCompl(String bairro, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'O', deve receber bairro. - Recebendo: ")
        .append(bairro)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para NUMERO */
  public void addNumeroInvalidoCadCompl(String cidade, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'P', deve receber cidade. - Recebendo: ")
        .append(cidade)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para cidade */
  public void addCidadeInvalidoCadCompl(String cidade, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'Q', deve receber cidade. - Recebendo: ")
        .append(cidade)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para COMPLEMENTO */
  public void addComplementoInvalidoCadCompl(String cidade, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'T', deve receber complemento. - Recebendo: ")
        .append(cidade)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para UF */
  public void addUfInvalidoCadCompl(String uf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'R', deve receber UF (Unidade Federativa). - Recebendo: ")
        .append(uf)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para CEP */
  public void addCepInvalidoCadCompl(String cep, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'S', deve receber CEP. - Recebendo: ")
        .append(cep)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para email */
  public void addEmailInvalidoCadCompl(String email, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'U', deve receber e-mail. - Recebendo: ")
        .append(email)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addEmailCadPortadores(String email, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'E', deve receber um e-mail válido. - Recebendo: ")
        .append(email)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para DDD */
  public void addEmailProfissionalInvalidoCadCompl(
      String emailProf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'V', deve recebere-mail válido. - Recebendo: ")
        .append(emailProf)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para DDD */
  public void addDddResidencialInvalidoCadCompl(String ddd, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'W', deve receber número DDD. - Recebendo: ")
        .append(ddd)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addDDDCadPortadores(String ddd, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'F', deve receber o DDD. - Recebendo: ")
        .append(ddd)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para telefone residencial */
  public void addTelefoneResidencialInvalidoCadCompl(
      String telefone, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'X', deve receber número de telefone residencial (fixo). - Recebendo: ")
        .append(telefone)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para DDD */
  public void addDddCelularInvalidoCadCompl(String ddd, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'Y', deve receber número DDD. - Recebendo: ")
        .append(ddd)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para telefone móvel */
  public void addCelularInvalidoCadCompl(String telefone, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'Z', deve receber número telefone móvel (celular) . - Recebendo: ")
        .append(telefone)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addCelularCadPortadores(String celular, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'G', deve receber o Número de Celular. - Recebendo: ")
        .append(celular)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para banco */
  public void addBancoInvalidoCadCompl(String banco, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AA', deve receber código do banco. - Recebendo: ")
        .append(banco)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para agencia */
  public void addAgenciaInvalidoCadCompl(String agencia, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AB', deve receber número da agência. - Recebendo: ")
        .append(agencia)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para conta bancária */
  public void addContaBancariaInvalidoCadCompl(String conta, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AC', deve receber número da conta bancária. - Recebendo: ")
        .append(conta)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição paratipoConta */
  public void addTipoContaBancariaInvalidoCadCompl(
      String tipoConta, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AD', deve receber número de tipo da conta bancária. - Recebendo: ")
        .append(tipoConta)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição dados incompletos de conta bancárias */
  public void addDadosBancariosInvalidoCadCompl(
      String tipoConta, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", deve receber todos os dados bancários ou nenhum. - Recebendo: ")
        .append(tipoConta)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição nome de cartão incluído que não for da bandeira VISA */
  public void addNomeCartaoExclusivo(String verificacao, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(
            ", coluna 'AF' pode ser preenchido somente se o produto for de bandeira VISA. - Recebendo: ")
        .append(verificacao)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição nome de cartão incluído que não for da bandeira VISA */
  public void addNomeUFEmissorRG(String verificacao, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AG' deve ser preenchido com sigla da UF de emissão do RG. - Recebendo: ")
        .append(verificacao)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar uma restrição para chave pix */
  public void addChavePixInvalidoCadCompl(String chave, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AI', deve receber a chave pix. - Recebendo: ")
        .append(chave)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar validacao documento pix */
  public void addChavePixDocumentoInvalidoCadCompl(
      String chave, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'AI', deve receber um Documento válido. - Recebendo: ")
        .append(chave)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /** Adicionar um restrição para funcionário não associado a um produto */
  public void addCpfProdutoNaoEncontrado(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A'. - Funcionário com o CPF: ")
        .append(cpf)
        .append(" não cadastrado para o produto informado.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para filial não informada
   *
   * @param nomeSetorFilial
   * @param linha
   */
  public void addSetorFilial(String nomeSetorFilial, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'E', deve conter o Setor Filial. - Recebendo: ")
        .append(nomeSetorFilial)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para carga valor não informada
   *
   * @param cargaValor
   * @param linha
   */
  public void addCargaValor(String cargaValor, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'D' deve conter o Valor da Carga. - Recebendo: ")
        .append(cargaValor)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição data de nascimento informada
   *
   * @param linha
   */
  public void addDataNascimento(String dataNascimento, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'F', deve conter a Data de Nascimento. - Recebendo: ")
        .append(dataNascimento)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para o e-mail informado
   *
   * @param email
   * @param linha
   */
  public void addEmail(String email, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'G', deve receber um e-mail válido. - Recebendo: ")
        .append(email)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para o ddd informado
   *
   * @param linha
   */
  public void addDDD(String ddd, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'H', deve receber o DDD. - Recebendo: ")
        .append(ddd)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para o celular informado
   *
   * @param linha
   */
  public void addCelular(String celular, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'I', deve receber o Número de Celular. - Recebendo: ")
        .append(celular)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public void addParcelamento(String parcelamento, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'J', deve recebar números inteiros entre 0 e 100 - Recebendo: ")
        .append(parcelamento)
        .append(".")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para o cpf já cadastrado
   *
   * @param linha
   */
  public void addCpfProdutoCadastrado(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A'. - Funcionário com o CPF ")
        .append(cpf)
        .append(", já cadastrado para o produto informado.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para o cpf já cadastrado multicontas
   *
   * @param linha
   */
  public void addCpfProdutoMultiContas(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'A'. - Funcionário com o CPF ")
        .append(cpf)
        .append(
            ", é necessário possuir uma conta do Saldo Livre antes de cadastrar outra conta do Multiconta.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para o cpf já cadastrado
   *
   * @param linha
   */
  public void addCpfProdutoCadastradoCadCompl(String cpf, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'C'. - Funcionário com o CPF ")
        .append(cpf)
        .append(", já cadastrado para o produto informado.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  /**
   * Adicionar uma restrição para a matrícula já cadastrada
   *
   * @param linha
   */
  public void addMatriculaCadastrada(String matricula, Integer linha, Integer erroRegistros) {
    StringBuilder builder = new StringBuilder();

    builder
        .append("Linha ")
        .append("'" + linha + "'")
        .append(", coluna 'B'. <<MATRÍCULA ")
        .append("'" + matricula + "'")
        .append(", JÁ ESTÁ SENDO USADA POR ALGUÉM NESTA EMPRESA>>.")
        .append(erroRegistros);

    getVerificacoes().add(builder.toString());
  }

  public String getNomeArquivo() {
    return nomeArquivo;
  }

  public void setNomeArquivo(String nomeArquivo) {
    this.nomeArquivo = nomeArquivo;
  }

  public List<String> getVerificacoes() {
    if (this.verificacoes == null) {
      this.verificacoes = new ArrayList<>();
    }
    return verificacoes;
  }

  public void setVerificacoes(List<String> verificacoes) {
    this.verificacoes = verificacoes;
  }

  public List<? extends ImportacaoArquivoTO> getRegistros() {
    return registros;
  }

  public void setRegistros(List<? extends ImportacaoArquivoTO> cargaCredito) {
    this.registros = cargaCredito;
  }

  public List<String> getListaValorRepetido() {
    if (this.verificacoes == null) {
      this.verificacoes = new ArrayList<>();
    }
    return listaValorRepetido;
  }

  public void setListaValorRepetido(List<String> listaValorRepetido) {
    this.listaValorRepetido = listaValorRepetido;
  }

  public String getMsg() {
    return msg;
  }

  public void setMsg(String msg) {
    this.msg = msg;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  /** Verificação de uma linha (registro) */
  public static class VerificacaoLinhaTO {
    /** Número da linha */
    private Integer linha;

    private Integer erroRegistros;

    /** Verificação */
    private String verificacao;

    private BigDecimal somaLimiteFuncionarios;

    public VerificacaoLinhaTO(Integer erroRegistros, Integer linha, String verificacao) {
      setErroRegistros(erroRegistros);
      setLinha(linha);
      setVerificacao(verificacao);
    }

    public VerificacaoLinhaTO(BigDecimal somaLimiteFuncionarios) {
      this.somaLimiteFuncionarios = somaLimiteFuncionarios;
    }

    public BigDecimal getSomaLimiteFuncionarios() {
      return somaLimiteFuncionarios;
    }

    public void setSomaLimiteFuncionarios(BigDecimal somaLimiteFuncionarios) {
      this.somaLimiteFuncionarios = somaLimiteFuncionarios;
    }

    public Integer getLinha() {
      return linha;
    }

    public void setLinha(Integer linha) {
      this.linha = linha + 1;
    }

    public String getVerificacao() {
      return verificacao;
    }

    public void setVerificacao(String verificacao) {
      this.verificacao = verificacao;
    }

    public Integer getErroRegistros() {
      return erroRegistros;
    }

    public void setErroRegistros(Integer erroRegistros) {
      this.erroRegistros = erroRegistros;
    }
  }
}
