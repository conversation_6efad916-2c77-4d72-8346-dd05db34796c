package br.com.json.bean.suporte;

import br.com.entity.suporte.Contato;
import br.com.sinergico.annotation.Documento;
import br.com.sinergico.enums.GrauDeRiscoEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.Max;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 */
@ApiModel(value = "Cadastar Ponto de Relacionamento")
public class CadastrarPontoRelacionamento {

  @ApiModelProperty(hidden = false)
  private String descricao;

  @Max(999999)
  @ApiModelProperty(hidden = false)
  private Integer codPontoRelacionamento;

  @Max(99)
  @ApiModelProperty(hidden = false)
  private Integer idProcessadora;

  @Max(9999)
  @ApiModelProperty(hidden = false)
  private Integer idRegional;

  @Max(9999)
  @ApiModelProperty(hidden = false)
  private Integer idFilial;

  @Max(9999)
  @ApiModelProperty(hidden = false)
  private Integer idInstituicao;

  @Documento
  @ApiModelProperty(hidden = true)
  private String documento;

  @Max(99)
  @ApiModelProperty(hidden = true)
  private Integer dddTelefoneComercial;

  @Max(999999999)
  @ApiModelProperty(hidden = true)
  private Integer telefoneComercial;

  @Max(99)
  @ApiModelProperty(hidden = true)
  private Integer dddTelefoneComercial2;

  @Max(999999999)
  @ApiModelProperty(hidden = true)
  private Integer telefoneComercial2;

  @Length(max = 30)
  @ApiModelProperty(hidden = true)
  private String inscricaoEstadual;

  @Length(max = 30)
  @ApiModelProperty(hidden = true)
  private String inscricaoMunicipal;

  @JsonFormat(
      shape = JsonFormat.Shape.STRING,
      pattern = "yyyy-MM-dd",
      timezone = "America/Sao_Paulo")
  @ApiModelProperty(hidden = true)
  private Date dataFundacao;

  @Length(max = 60)
  @ApiModelProperty(hidden = true)
  private String atividadePrincipal;

  @Length(max = 20)
  @ApiModelProperty(hidden = true)
  private String formaDeConstituicao;

  @Max(999999)
  @ApiModelProperty(hidden = true)
  private Integer idUsuario;

  // campo que determina se o pr eh uma empresa cliente
  @ApiModelProperty(hidden = true)
  private Boolean b2b;

  // campo para PF. Ex: trabalhador rural que emite vale refeicao/alimentacao
  @Length(max = 80)
  @ApiModelProperty(hidden = true)
  private String cei;

  @Length(max = 8)
  @ApiModelProperty(hidden = true)
  private String cepSede;

  @Length(max = 100)
  @ApiModelProperty(hidden = true)
  private String logradouroSede;

  @Length(max = 8)
  @ApiModelProperty(hidden = true)
  private String numeroSede;

  @Length(max = 40)
  @ApiModelProperty(hidden = true)
  private String complementoSede;

  @Length(max = 100)
  @ApiModelProperty(hidden = true)
  private String bairroSede;

  @Length(max = 100)
  @ApiModelProperty(hidden = true)
  private String cidadeSede;

  @Length(max = 2)
  @ApiModelProperty(hidden = true)
  private String ufSede;

  @Length(max = 8)
  @ApiModelProperty(hidden = true)
  private String cepFaturamento;

  @Length(max = 100)
  @ApiModelProperty(hidden = true)
  private String logradouroFaturamento;

  @Length(max = 8)
  @ApiModelProperty(hidden = true)
  private String numeroFaturamento;

  @Length(max = 40)
  @ApiModelProperty(hidden = true)
  private String complementoFaturamento;

  @Length(max = 100)
  @ApiModelProperty(hidden = true)
  private String bairroFaturamento;

  @Length(max = 100)
  @ApiModelProperty(hidden = true)
  private String cidadeFaturamento;

  @Length(max = 2)
  @ApiModelProperty(hidden = true)
  private String ufFaturamento;

  @Max(99)
  @ApiModelProperty(hidden = true)
  private Integer tipoDocumento;

  @Length(max = 30)
  // nome impresso no cartao
  @ApiModelProperty(hidden = true)
  private String nomeImpresso;

  @Length(max = 30)
  @ApiModelProperty(hidden = true)
  private String nomeFantasia;

  @Max(99)
  @ApiModelProperty(hidden = true)
  private Integer tipoPostagem;

  @Max(9999999999999l)
  @ApiModelProperty(hidden = true)
  private BigDecimal limiteMaxCredito;

  @ApiModelProperty(hidden = true)
  private BigDecimal retencaoIss;

  @Length(max = 10)
  @ApiModelProperty(hidden = true)
  private String identificadorExterno;

  private GrauDeRiscoEnum grauDeRisco;

  // configuracoes produtoContratado

  @ApiModelProperty(hidden = true)
  private List<ProdutoContradoCliente> produtosSelecionados;

  @ApiModelProperty(hidden = true)
  private List<Contato> contatos;

  public CadastrarPontoRelacionamento(
      String descricao,
      Integer codPontoRelacionamento,
      Integer idRegional,
      Integer idFilial,
      Integer idInstituicao) {
    this();
    this.descricao = descricao;
    this.codPontoRelacionamento = codPontoRelacionamento;
    this.idRegional = idRegional;
    this.idFilial = idFilial;
    this.idInstituicao = idInstituicao;
  }

  public String getNomeFantasia() {
    return nomeFantasia;
  }

  public void setNomeFantasia(String nomeFantasia) {
    this.nomeFantasia = nomeFantasia;
  }

  public CadastrarPontoRelacionamento() {
    super();
  }

  public String getIdentificadorExterno() {
    return identificadorExterno;
  }

  public void setIdentificadorExterno(String identificadorExterno) {
    this.identificadorExterno = identificadorExterno;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getDddTelefoneComercial2() {
    return dddTelefoneComercial2;
  }

  public void setDddTelefoneComercial2(Integer dddTelefoneComercial2) {
    this.dddTelefoneComercial2 = dddTelefoneComercial2;
  }

  public Integer getTelefoneComercial2() {
    return telefoneComercial2;
  }

  public void setTelefoneComercial2(Integer telefoneComercial2) {
    this.telefoneComercial2 = telefoneComercial2;
  }

  public String getNomeImpresso() {
    return nomeImpresso;
  }

  public void setNomeImpresso(String nomeImpresso) {
    this.nomeImpresso = nomeImpresso;
  }

  public Integer getTipoPostagem() {
    return tipoPostagem;
  }

  public void setTipoPostagem(Integer tipoPostagem) {
    this.tipoPostagem = tipoPostagem;
  }

  public BigDecimal getLimiteMaxCredito() {
    return limiteMaxCredito;
  }

  public void setLimiteMaxCredito(BigDecimal limiteMaxCredito) {
    this.limiteMaxCredito = limiteMaxCredito;
  }

  public List<Contato> getContatos() {
    return contatos;
  }

  public void setContatos(List<Contato> contatos) {
    this.contatos = contatos;
  }

  public Integer getTipoDocumento() {
    return tipoDocumento;
  }

  public void setTipoDocumento(Integer tipoDocumento) {
    this.tipoDocumento = tipoDocumento;
  }

  public List<ProdutoContradoCliente> getProdutosSelecionados() {
    return produtosSelecionados;
  }

  public void setProdutosSelecionados(List<ProdutoContradoCliente> produtosSelecionados) {
    this.produtosSelecionados = produtosSelecionados;
  }

  public String getCepSede() {
    return cepSede;
  }

  public void setCepSede(String cepSede) {
    this.cepSede = cepSede;
  }

  public String getLogradouroSede() {
    return logradouroSede;
  }

  public void setLogradouroSede(String logradouroSede) {
    this.logradouroSede = logradouroSede;
  }

  public String getNumeroSede() {
    return numeroSede;
  }

  public void setNumeroSede(String numeroSede) {
    this.numeroSede = numeroSede;
  }

  public String getComplementoSede() {
    return complementoSede;
  }

  public void setComplementoSede(String complementoSede) {
    this.complementoSede = complementoSede;
  }

  public String getBairroSede() {
    return bairroSede;
  }

  public void setBairroSede(String bairroSede) {
    this.bairroSede = bairroSede;
  }

  public String getCidadeSede() {
    return cidadeSede;
  }

  public void setCidadeSede(String cidadeSede) {
    this.cidadeSede = cidadeSede;
  }

  public String getUfSede() {
    return ufSede;
  }

  public void setUfSede(String ufSede) {
    this.ufSede = ufSede;
  }

  public String getCepFaturamento() {
    return cepFaturamento;
  }

  public void setCepFaturamento(String cepFaturamento) {
    this.cepFaturamento = cepFaturamento;
  }

  public String getLogradouroFaturamento() {
    return logradouroFaturamento;
  }

  public void setLogradouroFaturamento(String logradouroFaturamento) {
    this.logradouroFaturamento = logradouroFaturamento;
  }

  public String getNumeroFaturamento() {
    return numeroFaturamento;
  }

  public void setNumeroFaturamento(String numeroFaturamento) {
    this.numeroFaturamento = numeroFaturamento;
  }

  public String getComplementoFaturamento() {
    return complementoFaturamento;
  }

  public void setComplementoFaturamento(String complementoFaturamento) {
    this.complementoFaturamento = complementoFaturamento;
  }

  public String getBairroFaturamento() {
    return bairroFaturamento;
  }

  public void setBairroFaturamento(String bairroFaturamento) {
    this.bairroFaturamento = bairroFaturamento;
  }

  public String getCidadeFaturamento() {
    return cidadeFaturamento;
  }

  public void setCidadeFaturamento(String cidadeFaturamento) {
    this.cidadeFaturamento = cidadeFaturamento;
  }

  public String getUfFaturamento() {
    return ufFaturamento;
  }

  public void setUfFaturamento(String ufFaturamento) {
    this.ufFaturamento = ufFaturamento;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public Integer getDddTelefoneComercial() {
    return dddTelefoneComercial;
  }

  public void setDddTelefoneComercial(Integer dddTelefoneComercial) {
    this.dddTelefoneComercial = dddTelefoneComercial;
  }

  public Integer getTelefoneComercial() {
    return telefoneComercial;
  }

  public void setTelefoneComercial(Integer telefoneComercial) {
    this.telefoneComercial = telefoneComercial;
  }

  public String getInscricaoEstadual() {
    return inscricaoEstadual;
  }

  public void setInscricaoEstadual(String inscricaoEstadual) {
    this.inscricaoEstadual = inscricaoEstadual;
  }

  public String getInscricaoMunicipal() {
    return inscricaoMunicipal;
  }

  public void setInscricaoMunicipal(String inscricaoMunicipal) {
    this.inscricaoMunicipal = inscricaoMunicipal;
  }

  public Date getDataFundacao() {
    return dataFundacao;
  }

  public void setDataFundacao(Date dataFundacao) {
    this.dataFundacao = dataFundacao;
  }

  public String getAtividadePrincipal() {
    return atividadePrincipal;
  }

  public void setAtividadePrincipal(String atividadePrincipal) {
    this.atividadePrincipal = atividadePrincipal;
  }

  public String getFormaDeConstituicao() {
    return formaDeConstituicao;
  }

  public void setFormaDeConstituicao(String formaDeConstituicao) {
    this.formaDeConstituicao = formaDeConstituicao;
  }

  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public Boolean getB2b() {
    return b2b;
  }

  public void setB2b(Boolean b2b) {
    this.b2b = b2b;
  }

  public String getCei() {
    return cei;
  }

  public void setCei(String cei) {
    this.cei = cei;
  }

  public String getDescricao() {
    return descricao;
  }

  public void setDescricao(String descricao) {
    this.descricao = descricao;
  }

  public Integer getCodPontoRelacionamento() {
    return codPontoRelacionamento;
  }

  public void setCodPontoRelacionamento(Integer codPontoRelacionamento) {
    this.codPontoRelacionamento = codPontoRelacionamento;
  }

  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public BigDecimal getRetencaoIss() {
    return retencaoIss;
  }

  public void setRetencaoIss(BigDecimal retencaoIss) {
    this.retencaoIss = retencaoIss;
  }

  public GrauDeRiscoEnum getGrauDeRisco() {
    return grauDeRisco;
  }

  public void setGrauDeRisco(GrauDeRiscoEnum grauDeRisco) {
    this.grauDeRisco = grauDeRisco;
  }
}
