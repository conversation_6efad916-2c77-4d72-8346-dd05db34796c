package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.PortadorLogin;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PortadorLoginRepository
    extends JpaRepository<PortadorLogin, Long>, PortadorLoginRepositoryCustom {

  String PESSOA_FISICA = "1";
  String PESSOA_JURIDICA = "2";

  @Query(
      "select port "
          + "from PortadorLogin port "
          + "where port.idProcessadora = :idProcessadora "
          + "and port.idInstituicao = :idInstituicao "
          + "and port.cpf = :documento "
          + "and port.tipoLoginEnum = :tipoLogin "
          + "and port.grupoAcesso is null "
          + "and port.documentoAcesso is null "
          + "and port.dataHoraCancelamento is null ")
  PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndTipoLoginAndGrupoAcessoIsNullAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("tipoLogin") TipoPortadorLoginEnum tipoLogin);

  @Query(
      "select port "
          + "from PortadorLogin port "
          + "where port.idProcessadora = :idProcessadora "
          + "and port.idInstituicao = :idInstituicao "
          + "and port.cpf = :documento "
          + "and port.documentoAcesso = :documentoAcesso "
          + "and port.tipoLoginEnum = :tipoLogin "
          + "and port.grupoAcesso is null "
          + "and port.dataHoraCancelamento is null")
  PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndTipoLoginAndGrupoAcessoIsNullDataHoraCancelamentoIsNull(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("documentoAcesso") String documentoAcesso,
          @Param("tipoLogin") TipoPortadorLoginEnum tipoLogin);

  @Query(
      "select port "
          + "from PortadorLogin port "
          + "where port.idProcessadora = :idProcessadora "
          + "and port.idInstituicao = :idInstituicao "
          + "and port.cpf = :documento "
          + "and port.grupoAcesso = :grupoAcesso "
          + "and port.tipoLoginEnum = :tipoLogin "
          + "and port.documentoAcesso is null "
          + "and port.dataHoraCancelamento is null")
  PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndGrupoAcessoAndTipoLoginAndDocumentoAcessoIsNullAndDataHoraCancelamentoIsNull(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("grupoAcesso") Long grupoAcesso,
          @Param("tipoLogin") TipoPortadorLoginEnum tipoLogin);

  @Query(
      "select port "
          + "from PortadorLogin port "
          + "where port.idProcessadora = :idProcessadora "
          + "and port.idInstituicao = :idInstituicao "
          + "and port.cpf = :documento "
          + "and port.documentoAcesso = :documentoAcesso "
          + "and port.grupoAcesso = :grupoAcesso "
          + "and port.tipoLoginEnum = :tipoLogin "
          + "and port.dataHoraCancelamento is null")
  PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndCpfAndDocumentoAcessoAndGrupoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("documentoAcesso") String documentoAcesso,
          @Param("grupoAcesso") Long grupoAcesso,
          @Param("tipoLogin") TipoPortadorLoginEnum tipoLogin);

  @Query(
      "select port "
          + "from PortadorLogin port "
          + "where port.idProcessadora = :idProcessadora "
          + "and port.idInstituicao = :idInstituicao "
          + "and port.documentoAcesso = :documentoAcesso "
          + "and port.tipoLoginEnum = :tipoLogin "
          + "and port.dataHoraCancelamento is null")
  PortadorLogin
      findByIdProcessadoraAndIdInstituicaoAndDocumentoAcessoAndTipoLoginAndDataHoraCancelamentoIsNull(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documentoAcesso") String documentoAcesso,
          @Param("tipoLogin") TipoPortadorLoginEnum tipoLogin);

  // TODO Funciona porém somente para o InMais.
  // É adequado remodelar junto com as APIs portador/login/contrato/{idInstituicao}/{documento}
  // e portador/login/salvar-contrato
  PortadorLogin findByCpfAndIdInstituicao(String documento, Integer idInstituicao);

  @Query(
      value =
          " select plResp.* "
              + " from cadastral.portador_login plDep "
              + " inner join cadastral.pessoa pDep on (pDep.id_processadora, pDep.id_instituicao) = (plDep.id_processadora, plDep.id_instituicao) "
              + "	and pDep.documento = plDep.cpf and pldep.tipo_login = :tipoLoginDependente "
              + " inner join cadastral.pessoa_responsavel_dependente prd on pDep.id_pessoa = prd.id_pessoa_dependente and prd.id_status = 1 "
              + " inner join cadastral.pessoa pResp on pResp.id_pessoa = prd.id_pessoa_responsavel "
              + " inner join cadastral.portador_login plResp on (pResp.id_processadora, pResp.id_instituicao) = (plResp.id_processadora, plResp.id_instituicao) "
              + "	and pResp.documento = plResp.cpf and plResp.tipo_login = :tipoLoginResponsavel "
              + " where plDep.id_login = :idLogin ",
      nativeQuery = true)
  PortadorLogin encontraPortadorLoginResponsavelAtravesDoPortadorLoginDependente(
      @Param("idLogin") Long idLogin,
      @Param("tipoLoginDependente") String tipoLoginDependente,
      @Param("tipoLoginResponsavel") String tipoLoginResponsavel);

  @Query(
      value =
          " select tipo_login \n "
              + " from cadastral.portador_login pl \n "
              + " \t inner join suporte.aplicativo_frontend af on (af.id_processadora, af.id_instituicao) = (pl.id_processadora, pl.id_instituicao) \n "
              + " where pl.cpf = :documento \n "
              + " \t and pl.dt_hr_cancelamento is null \n "
              + " \t and af.id = :idApp ",
      nativeQuery = true)
  List<String> encontraTipoLoginPorAppEDocumento(
      @Param("idApp") Integer idApp, @Param("documento") String documento);

  @Query(
      value =
          " select tipo_login \n "
              + " from cadastral.portador_login pl \n "
              + " \t inner join suporte.aplicativo_frontend af on (af.id_processadora, af.id_instituicao) = (pl.id_processadora, pl.id_instituicao) \n "
              + " where pl.documento_acesso = :documentoAcesso \n "
              + " \t and pl.dt_hr_cancelamento is null \n "
              + " \t and af.id = :idApp ",
      nativeQuery = true)
  List<String> encontraTipoLoginPorAppEDocumentoAcesso(
      @Param("idApp") Integer idApp, @Param("documentoAcesso") String documentoAcesso);

  @Query(
      value =
          " select tipo_login \n "
              + " from cadastral.portador_login pl \n "
              + " \t inner join cadastral.portador_login_conta plc on plc.id_login = pl.id_login \n "
              + " \t inner join cadastral.conta_pagamento cpag on cpag.id_conta = plc.id_conta \n "
              + " \t inner join cadastral.conta_pessoa cp on cp.id_conta = cpag.id_conta and cp.id_titularidade = 1 \n "
              + " \t inner join cadastral.pessoa p on p.id_pessoa = cp.id_pessoa \n "
              + " where true \n "
              + " \t and cpag.id_conta = :idConta \n "
              + " \t and (p.documento = pl.cpf or p.documento = pl.documento_acesso) ",
      nativeQuery = true)
  String encontraTipoLoginPorIdConta(@Param("idConta") Long idConta);

  @Query(
      value =
          "select pl.* "
              + "from cadastral.portador_login_conta plc "
              + "inner join cadastral.portador_login pl on pl.id_login = plc.id_login "
              + "where true "
              + "and plc.id_conta = :idConta "
              + "order by pl.dt_hr_cancelamento desc",
      nativeQuery = true)
  List<PortadorLogin> findByIdConta(@Param("idConta") Long idConta);

  @Query(
      "select port "
          + "from PortadorLogin port "
          + "where port.idProcessadora = :idProcessadora "
          + "and port.idInstituicao = :idInstituicao "
          + "and port.documentoAcesso = :documentoAcesso "
          + "and port.dataHoraCancelamento is null")
  PortadorLogin findByIdInstituicaoAndIdProcessadoraAndDocumentoAcesso(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("documentoAcesso") String documentoAcesso);
}
