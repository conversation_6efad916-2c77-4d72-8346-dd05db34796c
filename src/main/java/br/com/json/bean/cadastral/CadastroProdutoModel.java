package br.com.json.bean.cadastral;

import br.com.entity.suporte.TipoEnderecoProduto;
import br.com.sinergico.enums.PadraoSeqCredencial;
import br.com.sinergico.enums.SegundaViaBoletoEnum;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import br.com.sinergico.enums.TipoProdutoEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

public class CadastroProdutoModel implements Serializable {

  private static final long serialVersionUID = -6960260614576412647L;

  private Boolean b2b;

  @NotNull
  @Min(100000)
  @Max(999999)
  private Integer bin;

  @NotNull private Integer binLength;

  @Min(100000L)
  @Max(value = 9999999999L, message = "BIN com a sua extensão deve ter no máximo 10 caracteres")
  private Long binEstendido;

  @Min(100000L)
  @Max(
      value = 9999999999L,
      message = "BIN virtual com a sua extensão deve ter no máximo 10 caracteres")
  private Long binEstendidoVirtual;

  @Min(100000)
  @Max(999999)
  private Integer binVirtual;

  private String boletoAgencia;
  private Integer boletoBanco;
  private String boletoCarteira;
  private String boletoCodigoBeneficiario;
  private String boletoDigitoAgencia;
  private String boletoDigitoCodigoBeneficiario;
  private String boletoInstrucoes;
  private BigDecimal boletoJurosAtraso;
  private BigDecimal boletoMultaAtraso;
  private String boletoLocaisPagamento;
  private String boletoNumeroConvenio;
  private String boletoNumeroConvenioPJ;
  private String boletoVariacaoCarteira;
  private String boletoVariacaoCarteiraPJ;
  private Boolean defHabilitarExteriorVirtual;
  private Boolean defLimiteVirtual;
  private Boolean defQuantMaxComprasVirtual;
  private Boolean defValidadeVirtual;
  private Boolean defValorMaxTransacaoVirtual;
  private Integer quantMaxComprasVirtual;
  private Integer mesesValidadeMaxVirtual;
  private BigDecimal valorMaxTransacaoVirtual;
  private String descProdInstituicao;
  private Integer tipoEmissao;
  private Boolean virtual;
  private Integer primeiroCartaoVirtual;
  private Boolean emitePropriaEmpresa;
  private Boolean suportaContaBase;
  private Boolean permitePreEmissao;

  private Boolean boletoRegistrado;

  private List<TipoEnderecoProduto> tiposEnderecosProdutos;

  private Integer tipoPessoa;
  private Integer idadeMinimaPortador;
  private Integer idadeMinimaAdicional;
  private String serviceCode;
  private Boolean chip;
  private Double valorCargaMin;
  private Double valorCargaMax;
  private Integer idRegional;
  private Integer idFilial;
  private Integer idPontoDeRelacionamento;
  private Integer habilitaTodos;
  private Boolean blCorporativo;

  @NotNull private Integer idArranjo;

  @NotNull private Integer idProcessadora;

  @NotNull private Integer idInstituicao;

  @NotNull private Integer idProdInstituicao;

  @NotNull private Integer idMoeda;

  @NotNull private Integer idProdutoInstituidor;

  @NotNull private Integer idProdPlat;

  @NotNull private Integer idRelacionamento;

  private Integer mesesValidadeFisico;
  private PadraoSeqCredencial padrao;
  private Integer tamanhoPin;

  private Integer permiteRestricaoMcc;
  private Integer tipoRestricaoMcc;
  private Integer qtdDiasCorte;

  private BigDecimal txJuroChequeEspecial;

  private List<Integer> vencimentoFaturaList;

  private BigDecimal percentualLimiteAdicional;

  @NotNull private Boolean diferenciacaoLimiteAdicional;

  private Long idGrupo;

  private Integer posicaoHierarquia;

  private SegundaViaBoletoEnum segundaViaBoleto = SegundaViaBoletoEnum.DESABILITADO;

  private TipoProdutoEnum tipoProduto;

  private TipoPortadorLoginEnum tipoLogin;

  private Boolean habilitaPixApp;
  private Boolean habilitaPagContaApp;

  private Integer qtdContasDocumentoProduto;
  private Boolean redefinirSenhaCaf;
  private Integer metodoSegurancaTransacao;

  // Validacoes estao rodando multiplas vezes devido a versao antiga do Spring. Unico impacto:
  // performance
  @AssertTrue(
      message =
          "binVirtual e/ou binEstendidoVirtual precisam ser preenchidos quando "
              + "virtual é 1 ou primeiroCartaoVirtual é diferente de 0.")
  private boolean isVirtualWithBinValidation() {
    return ((virtual == null || !virtual)
            && (primeiroCartaoVirtual == null || primeiroCartaoVirtual == 0))
        || (binVirtual != null && binEstendidoVirtual != null);
  }

  @AssertTrue(
      message =
          "quantMaxComprasVirtual precisa ser preenchido quando defQuantMaxComprasVirtual é 1.")
  private boolean isVirtualWithQuantMaxComprasVirtualValidation() {
    return (defQuantMaxComprasVirtual == null || !defQuantMaxComprasVirtual)
        || (quantMaxComprasVirtual != null && quantMaxComprasVirtual >= 0);
  }

  @AssertTrue(
      message = "mesesValidadeMaxVirtual precisa ser preenchido quando defValidadeVirtual é 1.")
  private boolean isVirtualWithMesesValidadeMaxVirtualValidation() {
    return (defValidadeVirtual == null || !defValidadeVirtual)
        || (mesesValidadeMaxVirtual != null && mesesValidadeMaxVirtual >= 0);
  }

  @AssertTrue(
      message =
          "valorMaxTransacaoVirtual precisa ser preenchido quando defValorMaxTransacaoVirtual é 1.")
  private boolean isVirtualWithValorMaxTransacaoVirtualValidation() {
    return (defValorMaxTransacaoVirtual == null || !defValorMaxTransacaoVirtual)
        || (valorMaxTransacaoVirtual != null
            && valorMaxTransacaoVirtual.compareTo(BigDecimal.ZERO) >= 0);
  }

  @AssertTrue(message = "binLength permite os valor 6, 8 ou 10.")
  private boolean isBinLengthValid() {
    return binLength == null || binLength == 6 || binLength == 8 || binLength == 10;
  }

  @AssertTrue(message = "binEstendido é menor do que o permitido por binLength.")
  private boolean isBinEstendidoSizeLesserThanBinLength() {
    return binEstendido == null
        || binLength == null
        || binEstendido.toString().length() >= binLength;
  }

  @AssertTrue(message = "binEstendidoVirtual é menor do que o permitido por binLength.")
  private boolean isBinEstendidoVirtualSizeLesserThanBinLength() {
    return binEstendidoVirtual == null
        || binLength == null
        || binEstendidoVirtual.toString().length() >= binLength;
  }

  public Boolean getB2b() {
    return b2b;
  }

  public void setB2b(Boolean b2b) {
    this.b2b = b2b;
  }

  public Integer getBin() {
    return bin;
  }

  public void setBin(Integer bin) {
    this.bin = bin;
  }

  public Integer getBinLength() {
    return binLength;
  }

  public void setBinLength(Integer binLength) {
    this.binLength = binLength;
  }

  public Long getBinEstendido() {
    return binEstendido;
  }

  public void setBinEstendido(Long binEstendido) {
    this.binEstendido = binEstendido;
  }

  public Long getBinEstendidoVirtual() {
    return binEstendidoVirtual;
  }

  public void setBinEstendidoVirtual(Long binEstendidoVirtual) {
    this.binEstendidoVirtual = binEstendidoVirtual;
  }

  public Integer getBinVirtual() {
    return binVirtual;
  }

  public void setBinVirtual(Integer binVirtual) {
    this.binVirtual = binVirtual;
  }

  public String getBoletoAgencia() {
    return boletoAgencia;
  }

  public void setBoletoAgencia(String boletoAgencia) {
    this.boletoAgencia = boletoAgencia;
  }

  public Integer getBoletoBanco() {
    return boletoBanco;
  }

  public void setBoletoBanco(Integer boletoBanco) {
    this.boletoBanco = boletoBanco;
  }

  public String getBoletoCarteira() {
    return boletoCarteira;
  }

  public void setBoletoCarteira(String boletoCarteira) {
    this.boletoCarteira = boletoCarteira;
  }

  public String getBoletoCodigoBeneficiario() {
    return boletoCodigoBeneficiario;
  }

  public void setBoletoCodigoBeneficiario(String boletoCodigoBeneficiario) {
    this.boletoCodigoBeneficiario = boletoCodigoBeneficiario;
  }

  public String getBoletoDigitoAgencia() {
    return boletoDigitoAgencia;
  }

  public void setBoletoDigitoAgencia(String boletoDigitoAgencia) {
    this.boletoDigitoAgencia = boletoDigitoAgencia;
  }

  public String getBoletoDigitoCodigoBeneficiario() {
    return boletoDigitoCodigoBeneficiario;
  }

  public void setBoletoDigitoCodigoBeneficiario(String boletoDigitoCodigoBeneficiario) {
    this.boletoDigitoCodigoBeneficiario = boletoDigitoCodigoBeneficiario;
  }

  public String getBoletoInstrucoes() {
    return boletoInstrucoes;
  }

  public void setBoletoInstrucoes(String boletoInstrucoes) {
    this.boletoInstrucoes = boletoInstrucoes;
  }

  public BigDecimal getBoletoJurosAtraso() {
    return boletoJurosAtraso;
  }

  public void setBoletoJurosAtraso(BigDecimal boletoJurosAtraso) {
    this.boletoJurosAtraso = boletoJurosAtraso;
  }

  public BigDecimal getBoletoMultaAtraso() {
    return boletoMultaAtraso;
  }

  public void setBoletoMultaAtraso(BigDecimal boletoMultaAtraso) {
    this.boletoMultaAtraso = boletoMultaAtraso;
  }

  public String getBoletoLocaisPagamento() {
    return boletoLocaisPagamento;
  }

  public void setBoletoLocaisPagamento(String boletoLocaisPagamento) {
    this.boletoLocaisPagamento = boletoLocaisPagamento;
  }

  public String getBoletoNumeroConvenio() {
    return boletoNumeroConvenio;
  }

  public void setBoletoNumeroConvenio(String boletoNumeroConvenio) {
    this.boletoNumeroConvenio = boletoNumeroConvenio;
  }

  public String getBoletoNumeroConvenioPJ() {
    return boletoNumeroConvenioPJ;
  }

  public void setBoletoNumeroConvenioPJ(String boletoNumeroConvenioPJ) {
    this.boletoNumeroConvenioPJ = boletoNumeroConvenioPJ;
  }

  public Boolean getDefHabilitarExteriorVirtual() {
    return defHabilitarExteriorVirtual;
  }

  public void setDefHabilitarExteriorVirtual(Boolean defHabilitarExteriorVirtual) {
    this.defHabilitarExteriorVirtual = defHabilitarExteriorVirtual;
  }

  public Boolean getDefLimiteVirtual() {
    return defLimiteVirtual;
  }

  public void setDefLimiteVirtual(Boolean defLimiteVirtual) {
    this.defLimiteVirtual = defLimiteVirtual;
  }

  public Boolean getDefQuantMaxComprasVirtual() {
    return defQuantMaxComprasVirtual;
  }

  public void setDefQuantMaxComprasVirtual(Boolean defQuantMaxComprasVirtual) {
    this.defQuantMaxComprasVirtual = defQuantMaxComprasVirtual;
  }

  public Boolean getDefValidadeVirtual() {
    return defValidadeVirtual;
  }

  public void setDefValidadeVirtual(Boolean defValidadeVirtual) {
    this.defValidadeVirtual = defValidadeVirtual;
  }

  public Boolean getDefValorMaxTransacaoVirtual() {
    return defValorMaxTransacaoVirtual;
  }

  public void setDefValorMaxTransacaoVirtual(Boolean defValorMaxTransacaoVirtual) {
    this.defValorMaxTransacaoVirtual = defValorMaxTransacaoVirtual;
  }

  public String getDescProdInstituicao() {
    return descProdInstituicao;
  }

  public void setDescProdInstituicao(String descProdInstituicao) {
    this.descProdInstituicao = descProdInstituicao.toUpperCase();
  }

  public Integer getIdArranjo() {
    return idArranjo;
  }

  public void setIdArranjo(Integer idArranjo) {
    this.idArranjo = idArranjo;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public Integer getIdProdInstituicao() {
    return idProdInstituicao;
  }

  public void setIdProdInstituicao(Integer idProdInstituicao) {
    this.idProdInstituicao = idProdInstituicao;
  }

  public Integer getIdMoeda() {
    return idMoeda;
  }

  public void setIdMoeda(Integer idMoeda) {
    this.idMoeda = idMoeda;
  }

  public Integer getIdProdutoInstituidor() {
    return idProdutoInstituidor;
  }

  public void setIdProdutoInstituidor(Integer idProdutoInstituidor) {
    this.idProdutoInstituidor = idProdutoInstituidor;
  }

  public Integer getIdProdPlat() {
    return idProdPlat;
  }

  public void setIdProdPlat(Integer idProdPlat) {
    this.idProdPlat = idProdPlat;
  }

  public Integer getIdRelacionamento() {
    return idRelacionamento;
  }

  public void setIdRelacionamento(Integer idRelacionamento) {
    this.idRelacionamento = idRelacionamento;
  }

  public Integer getMesesValidadeFisico() {
    return mesesValidadeFisico;
  }

  public void setMesesValidadeFisico(Integer mesesValidadeFisico) {
    this.mesesValidadeFisico = mesesValidadeFisico;
  }

  public Integer getMesesValidadeMaxVirtual() {
    return mesesValidadeMaxVirtual;
  }

  public void setMesesValidadeMaxVirtual(Integer mesesValidadeMaxVirtual) {
    this.mesesValidadeMaxVirtual = mesesValidadeMaxVirtual;
  }

  public BigDecimal getValorMaxTransacaoVirtual() {
    return valorMaxTransacaoVirtual;
  }

  public void setValorMaxTransacaoVirtual(BigDecimal valorMaxTransacaoVirtual) {
    this.valorMaxTransacaoVirtual = valorMaxTransacaoVirtual;
  }

  public PadraoSeqCredencial getPadrao() {
    return padrao;
  }

  public void setPadrao(PadraoSeqCredencial padrao) {
    this.padrao = padrao;
  }

  public Integer getQuantMaxComprasVirtual() {
    return quantMaxComprasVirtual;
  }

  public void setQuantMaxComprasVirtual(Integer quantMaxComprasVirtual) {
    this.quantMaxComprasVirtual = quantMaxComprasVirtual;
  }

  public Integer getTamanhoPin() {
    return tamanhoPin;
  }

  public void setTamanhoPin(Integer tamanhoPin) {
    this.tamanhoPin = tamanhoPin;
  }

  public List<TipoEnderecoProduto> getTiposEnderecosProdutos() {
    return tiposEnderecosProdutos;
  }

  public void setTiposEnderecosProdutos(List<TipoEnderecoProduto> tiposEnderecosProdutos) {
    this.tiposEnderecosProdutos = tiposEnderecosProdutos;
  }

  public Integer getTipoPessoa() {
    return tipoPessoa;
  }

  public void setTipoPessoa(Integer tipoPessoa) {
    this.tipoPessoa = tipoPessoa;
  }

  public Integer getIdadeMinimaPortador() {
    return idadeMinimaPortador;
  }

  public void setIdadeMinimaPortador(Integer idadeMinimaPortador) {
    this.idadeMinimaPortador = idadeMinimaPortador;
  }

  public Integer getIdadeMinimaAdicional() {
    return idadeMinimaAdicional;
  }

  public void setIdadeMinimaAdicional(Integer idadeMinimaAdicional) {
    this.idadeMinimaAdicional = idadeMinimaAdicional;
  }

  public String getServiceCode() {
    return serviceCode;
  }

  public void setServiceCode(String serviceCode) {
    this.serviceCode = serviceCode;
  }

  public Boolean getChip() {
    return chip;
  }

  public void setChip(Boolean chip) {
    this.chip = chip;
  }

  public Integer getTipoEmissao() {
    return tipoEmissao;
  }

  public void setTipoEmissao(Integer tipoEmissao) {
    this.tipoEmissao = tipoEmissao;
  }

  public Boolean getVirtual() {
    return virtual;
  }

  public void setVirtual(Boolean virtual) {
    this.virtual = virtual;
  }

  public Integer getPrimeiroCartaoVirtual() {
    return primeiroCartaoVirtual;
  }

  public void setPrimeiroCartaoVirtual(Integer primeiroCartaoVirtual) {
    this.primeiroCartaoVirtual = primeiroCartaoVirtual;
  }

  public Boolean getEmitePropriaEmpresa() {
    return emitePropriaEmpresa;
  }

  public void setEmitePropriaEmpresa(Boolean emitePropriaEmpresa) {
    this.emitePropriaEmpresa = emitePropriaEmpresa;
  }

  public Boolean getSuportaContaBase() {
    return suportaContaBase;
  }

  public void setSuportaContaBase(Boolean suportaContaBase) {
    this.suportaContaBase = suportaContaBase;
  }

  public Double getValorCargaMin() {
    return valorCargaMin;
  }

  public void setValorCargaMin(Double valorCargaMin) {
    this.valorCargaMin = valorCargaMin;
  }

  public Double getValorCargaMax() {
    return valorCargaMax;
  }

  public void setValorCargaMax(Double valorCargaMax) {
    this.valorCargaMax = valorCargaMax;
  }

  public Boolean getPermitePreEmissao() {
    return permitePreEmissao;
  }

  public void setPermitePreEmissao(Boolean permitePreEmissao) {
    this.permitePreEmissao = permitePreEmissao;
  }

  public Boolean getBoletoRegistrado() {
    return boletoRegistrado;
  }

  public void setBoletoRegistrado(Boolean boletoRegistrado) {
    this.boletoRegistrado = boletoRegistrado;
  }

  public List<Integer> getVencimentoFaturaList() {
    return vencimentoFaturaList;
  }

  public void setVencimentoFaturaList(List<Integer> vencimentoFaturaList) {
    this.vencimentoFaturaList = vencimentoFaturaList;
  }

  public Integer getPermiteRestricaoMcc() {
    return permiteRestricaoMcc;
  }

  public void setPermiteRestricaoMcc(Integer permiteRestricaoMcc) {
    this.permiteRestricaoMcc = permiteRestricaoMcc;
  }

  public Integer getTipoRestricaoMcc() {
    return tipoRestricaoMcc;
  }

  public void setTipoRestricaoMcc(Integer tipoRestricaoMcc) {
    this.tipoRestricaoMcc = tipoRestricaoMcc;
  }

  public Integer getQtdDiasCorte() {
    return qtdDiasCorte;
  }

  public void setQtdDiasCorte(Integer qtdDiasCorte) {
    this.qtdDiasCorte = qtdDiasCorte;
  }

  public BigDecimal getTxJuroChequeEspecial() {
    return txJuroChequeEspecial;
  }

  public CadastroProdutoModel setTxJuroChequeEspecial(BigDecimal txJuroChequeEspecial) {
    this.txJuroChequeEspecial = txJuroChequeEspecial;
    return this;
  }

  public Boolean getDiferenciacaoLimiteAdicional() {
    return diferenciacaoLimiteAdicional;
  }

  public void setDiferenciacaoLimiteAdicional(Boolean diferenciacaoLimiteAdicional) {
    this.diferenciacaoLimiteAdicional = diferenciacaoLimiteAdicional;
  }

  public BigDecimal getPercentualLimiteAdicional() {
    return percentualLimiteAdicional;
  }

  public void setPercentualLimiteAdicional(BigDecimal percentualLimiteAdicional) {
    this.percentualLimiteAdicional = percentualLimiteAdicional;
  }

  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public Integer getIdPontoDeRelacionamento() {
    return idPontoDeRelacionamento;
  }

  public void setIdPontoDeRelacionamento(Integer idPontoDeRelacionamento) {
    this.idPontoDeRelacionamento = idPontoDeRelacionamento;
  }

  public Integer getHabilitaTodos() {
    return habilitaTodos;
  }

  public void setHabilitaTodos(Integer habilitaTodos) {
    this.habilitaTodos = habilitaTodos;
  }

  public Long getIdGrupo() {
    return idGrupo;
  }

  public void setIdGrupo(Long idGrupo) {
    this.idGrupo = idGrupo;
  }

  public SegundaViaBoletoEnum getSegundaViaBoleto() {
    return segundaViaBoleto;
  }

  public void setSegundaViaBoleto(SegundaViaBoletoEnum segundaViaBoleto) {
    this.segundaViaBoleto = segundaViaBoleto;
  }

  public TipoProdutoEnum getTipoProduto() {
    return tipoProduto;
  }

  public void setTipoProduto(TipoProdutoEnum tipoProduto) {
    this.tipoProduto = tipoProduto;
  }

  public TipoPortadorLoginEnum getTipoLogin() {
    return tipoLogin;
  }

  public void setTipoLogin(TipoPortadorLoginEnum tipoLogin) {
    this.tipoLogin = tipoLogin;
  }

  public String getBoletoVariacaoCarteira() {
    return boletoVariacaoCarteira;
  }

  public void setBoletoVariacaoCarteira(String boletoVariacaoCarteira) {
    this.boletoVariacaoCarteira = boletoVariacaoCarteira;
  }

  public String getBoletoVariacaoCarteiraPJ() {
    return boletoVariacaoCarteiraPJ;
  }

  public void setBoletoVariacaoCarteiraPJ(String boletoVariacaoCarteiraPJ) {
    this.boletoVariacaoCarteiraPJ = boletoVariacaoCarteiraPJ;
  }

  public Integer getQtdContasDocumentoProduto() {
    return qtdContasDocumentoProduto;
  }

  public Boolean getRedefinirSenhaCaf() {
    return redefinirSenhaCaf;
  }

  public Integer getMetodoSegurancaTransacao() {
    return metodoSegurancaTransacao;
  }

  public Boolean getBlCorporativo() {
    return blCorporativo;
  }

  public void setBlCorporativo(Boolean blCorporativo) {
    this.blCorporativo = blCorporativo;
  }
}
