package br.com.sinergico.controller.pix;

import br.com.json.bean.pix.request.GerarQrCodeEstaticoDTO;
import br.com.json.bean.pix.request.GerarQrCodeEstaticoRequest;
import br.com.json.bean.pix.response.ConsultarQrCodeResponse;
import br.com.json.bean.pix.response.QrCodeEstaticoInfoResponse;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.pix.ContaTransacionalQrcodeService;
import br.com.sinergico.vo.pix.GerarQrCodeImediatoSemVencimentoRequest;
import br.com.sinergico.vo.pix.GerarQrCodeImediatoVencimentoRequest;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/portador/pix/qrcode")
public class QrCodeController extends UtilController {

  @Autowired private EntityManager em;

  @Autowired private ContaTransacionalQrcodeService contaTransacionalQrcodeService;

  @ApiOperation(
      value = "Gera QR Code estático para pagamento PIX",
      response = QrCodeEstaticoInfoResponse.class)
  @RequestMapping(
      value = "/gera-qrcode-estatico/{gerarQrcodeComValor}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeEstatico(
      @PathVariable String gerarQrcodeComValor,
      @RequestBody GerarQrCodeEstaticoRequest gerarQrCodeEstaticoRequest,
      HttpServletRequest httpServletRequest) {
    return callFunctionPortadorOrFunctionCorporativo(
        httpServletRequest,
        new GerarQrCodeEstaticoDTO(gerarQrcodeComValor, gerarQrCodeEstaticoRequest),
        contaTransacionalQrcodeService::validarGerarQrcodeEstatico,
        contaTransacionalQrcodeService::validarGerarQrcodeEstatico,
        em);
  }

  @ApiOperation(
      value = "Gera QR Code estático para pagamento PIX em Backoffice",
      response = QrCodeEstaticoInfoResponse.class)
  @RequestMapping(
      value = "/gera-qrcode-estatico-backoffice/{gerarQrcodeComValor}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeEstaticoBackoffice(
      @PathVariable String gerarQrcodeComValor,
      @RequestBody GerarQrCodeEstaticoRequest gerarQrCodeEstaticoRequest,
      HttpServletRequest request) {

    SecurityUser user = getAuthenticatedUser(request, em);
    return contaTransacionalQrcodeService.validarGerarQrcodeEstatico(
        new GerarQrCodeEstaticoDTO(gerarQrcodeComValor, gerarQrCodeEstaticoRequest), user);
  }

  @ApiOperation(
      value = "Gera QR Code imediato para Pagamento PIX",
      response = QrCodeEstaticoInfoResponse.class)
  @RequestMapping(
      value = "/gera-qrcode-imediato",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeImediatoSemVencimento(
      @RequestBody GerarQrCodeImediatoSemVencimentoRequest gerarQrCodeImediatoRequest,
      HttpServletRequest request) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    return contaTransacionalQrcodeService.gerarQrcodeDinamicoSemVencimento(
        gerarQrCodeImediatoRequest, userPortador);
  }

  @ApiOperation(
      value = "Gera QR Code dinâmico com Vencimento",
      response = QrCodeEstaticoInfoResponse.class)
  @RequestMapping(
      value = "/gera-qrcode-vencimento",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeImediatoVencimento(
      @RequestBody GerarQrCodeImediatoVencimentoRequest gerarQrCodeImediatoRequest,
      HttpServletRequest request) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    return contaTransacionalQrcodeService.gerarQrcodeDinamicoVencimento(
        gerarQrCodeImediatoRequest, userPortador);
  }

  @ApiOperation(
      value = "Gera QR Code dinâmico backOffice com Vencimento",
      response = QrCodeEstaticoInfoResponse.class)
  @RequestMapping(
      value = "/gera-qrcode-vencimento-backoffice/{idConta}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<QrCodeEstaticoInfoResponse> gerarQrcodeImediatoVencimentoBackOffice(
      @PathVariable Long idConta,
      @RequestBody GerarQrCodeImediatoVencimentoRequest gerarQrCodeImediatoRequest,
      HttpServletRequest request) {

    SecurityUser user = getAuthenticatedUser(request, em);
    return contaTransacionalQrcodeService.gerarQrcodeDinamicoVencimentoUser(
        gerarQrCodeImediatoRequest, user, idConta);
  }

  @ApiOperation(value = "Consulta um QR Code PIX", response = ConsultarQrCodeResponse.class)
  @RequestMapping(
      value = "/consultar-qrcode/**",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  public ResponseEntity<ConsultarQrCodeResponse> consultarQrcode(
      HttpServletRequest httpServletRequest) {
    String requestURL = httpServletRequest.getRequestURL().toString();
    String qrCode = requestURL.split("/consultar-qrcode/")[1];

    return callFunctionPortadorOrFunctionCorporativo(
        httpServletRequest,
        qrCode,
        contaTransacionalQrcodeService::consultarQrCodePortador,
        contaTransacionalQrcodeService::consultarQrCodePortador,
        em);
  }
}
