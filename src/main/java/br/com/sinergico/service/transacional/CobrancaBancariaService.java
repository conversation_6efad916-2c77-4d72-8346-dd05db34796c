package br.com.sinergico.service.transacional;

import br.com.caelum.stella.boleto.Beneficiario;
import br.com.caelum.stella.boleto.Boleto;
import br.com.caelum.stella.boleto.Datas;
import br.com.caelum.stella.boleto.Pagador;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.transacional.CobrancaBancaria;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.transacional.GetCobrancasBancarias;
import br.com.sinergico.boleto.BoletoItsPay;
import br.com.sinergico.repository.transacional.CobrancaBancariaRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.boletoregistrado.RegistroBoletoService;
import br.com.sinergico.service.suporte.ParametroProcessamentoSistemaService;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CobrancaBancariaService extends GenericService<CobrancaBancaria, Long> {

  private static final String BANCO_DO_BRASIL = "001";

  private CobrancaBancariaRepository repository;

  @Autowired private RegistroBoletoService registroBoletoService;

  @Autowired private ParametroProcessamentoSistemaService parametroProcessamentoSistemaService;

  @Autowired
  public CobrancaBancariaService(CobrancaBancariaRepository repository) {
    super(repository);
    this.repository = repository;
  }

  public CobrancaBancaria gravarCobrancaBancariaBoleto(
      Boleto boleto, CobrancaBancaria cobranca, ProdutoInstituicaoConfiguracao prodInstConfig) {

    cobranca.setAceite(boleto.getAceite());
    cobranca.setCodigoEspecieMoeda(boleto.getCodigoEspecieMoeda());
    cobranca.setDataDocumento(boleto.getDatas().getDocumento());
    cobranca.setDataVencimento(boleto.getDatas().getVencimento());
    cobranca.setDescontoConcedido(boleto.getValorDescontos());
    cobranca.setEspecieDocumento(boleto.getEspecieDocumento());
    cobranca.setEspecieMoeda(boleto.getEspecieMoeda());
    cobranca.setValorAcrescimos(boleto.getValorAcrescimos());
    cobranca.setValorBoleto(boleto.getValorBoleto());
    cobranca.setValorDeducoes(boleto.getValorDeducoes());
    cobranca.setValorDescontos(boleto.getValorDescontos());
    cobranca.setValorMoeda(boleto.getValorMoeda());
    cobranca.setValorMulta(boleto.getValorMulta());
    cobranca.setDataHoraInclusao(LocalDateTime.now());
    cobranca.setNumeroDocumento(boleto.getNumeroDoDocumento());
    cobranca.setNossoNumeroCompletoBeneficiario(boleto.getNossoNumeroECodDocumento());
    cobranca.setDocumentoBeneficiario(boleto.getBeneficiario().getDocumento());
    preencherCobrancaBeneficiario(cobranca, boleto);
    preencherCobrancaBanco(cobranca, boleto);
    preencherCobrancaPagador(cobranca, boleto);
    preencherCobrancaDatas(cobranca, boleto);
    preencherCobrancaInstrucoes(cobranca, boleto);
    preencherCobrancaDescricoes(cobranca, boleto);
    preencherCobrancaLocaisPagamento(cobranca, boleto);

    String codigo = boleto.getBanco().getNumeroFormatado();
    if (codigo.equals(BANCO_DO_BRASIL)) {
      if (prodInstConfig.getBoletoNumeroConvenioPJ() != null
          && prodInstConfig.getBoletoVariacaoCarteiraPJ() != null
          && cobranca.getDocumentoPagador().length() > 11) {
        cobranca.setNumeroConvenioBeneficiario(prodInstConfig.getBoletoNumeroConvenioPJ());
        cobranca.setVariacaoCarteira(prodInstConfig.getBoletoVariacaoCarteiraPJ());
      } else if (prodInstConfig.getBoletoNumeroConvenio() != null
          && prodInstConfig.getBoletoVariacaoCarteira() != null
          && cobranca.getDocumentoPagador().length() <= 11) {
        cobranca.setVariacaoCarteira(prodInstConfig.getBoletoVariacaoCarteira());
      }
    }

    // se houver registro entao registra o boleto
    if (Objects.nonNull(cobranca.getRegistrado()) && cobranca.getRegistrado()) {
      registroBoletoService.registrarBoleto(cobranca, (BoletoItsPay) boleto);
    } else {
      // senao preencho conforme a api
      cobranca.setCodigoDeBarras(boleto.getCodigoDeBarras());
      cobranca.setLinhaDigitavel(boleto.getLinhaDigitavel());
    }

    try {
      return save(cobranca);
    } catch (Exception e) {
      throw new GenericServiceException("Erro ao gravar a entidade Cobrança Bancária.");
    }
  }

  private void preencherCobrancaLocaisPagamento(CobrancaBancaria c, Boleto boleto) {
    StringBuilder locais = new StringBuilder();

    boleto
        .getLocaisDePagamento()
        .forEach(
            local -> {
              locais.append(local);
              locais.append(";");
            });

    c.setLocaisDePagamento(locais.toString());
  }

  private void preencherCobrancaDescricoes(CobrancaBancaria c, Boleto boleto) {
    StringBuilder descricoes = new StringBuilder();

    boleto
        .getDescricoes()
        .forEach(
            desc -> {
              descricoes.append(desc);
              descricoes.append(";");
            });
    c.setDescricoes(descricoes.toString());
  }

  private void preencherCobrancaInstrucoes(CobrancaBancaria c, Boleto boleto) {
    StringBuilder instrucoes = new StringBuilder();

    boleto
        .getInstrucoes()
        .forEach(
            instrucao -> {
              instrucoes.append(instrucao);
              instrucoes.append(";");
            });
    c.setInstrucoes(instrucoes.toString());
  }

  private void preencherCobrancaDatas(CobrancaBancaria c, Boleto boleto) {
    Datas datas = boleto.getDatas();
    c.setDataDocumento(datas.getDocumento());
    c.setDataProcessamento(null);
    c.setDataVencimento(datas.getVencimento());
    c.setDataHoraGeracao(LocalDateTime.now());
  }

  private void preencherCobrancaPagador(CobrancaBancaria c, Boleto boleto) {
    Pagador p = boleto.getPagador();
    c.setDocumentoPagador(p.getDocumento());
    c.setNomePagador(p.getNome());
    c.setUfPagador(p.getEndereco().getUf());
    c.setBairroPagador(p.getEndereco().getBairro());
    c.setCepPagador(p.getEndereco().getCep());
    c.setCidadePagador(p.getEndereco().getCidade());
    c.setLogradouroPagador(p.getEndereco().getLogradouro());
  }

  private void preencherCobrancaBanco(CobrancaBancaria c, Boleto boleto) {
    String numeroFormatado = boleto.getBanco().getNumeroFormatado();
    int length = numeroFormatado.length();
    Integer numeroBanco = Integer.parseInt(numeroFormatado.substring(length - 3, length));
    c.setBancoEmissor(numeroBanco);
  }

  private void preencherCobrancaBeneficiario(CobrancaBancaria c, Boleto boleto) {

    Beneficiario beneficiario = boleto.getBeneficiario();
    c.setAgenciaBeneficiario(beneficiario.getAgencia());

    c.setBairroBeneficiario(beneficiario.getEndereco().getBairro());
    c.setCepBeneficiario(beneficiario.getEndereco().getCep());
    c.setCidadeBeneficiario(beneficiario.getEndereco().getCidade());
    c.setLogradouroBeneficiario(beneficiario.getEndereco().getLogradouro());
    c.setUfBeneficiario(beneficiario.getEndereco().getUf());

    c.setCarteiraBeneficiario(beneficiario.getCarteira());
    c.setCodigoBeneficiario(beneficiario.getCodigoBeneficiario());
    c.setDigitoAgenciaBeneficiario(beneficiario.getDigitoAgencia());
    c.setDigitoCodigoBeneficiario(beneficiario.getDigitoCodigoBeneficiario());
    c.setDigitoNossoNumeroBeneficiario(beneficiario.getDigitoNossoNumero());
    c.setDocumentoBeneficiario(beneficiario.getDocumento());
    c.setNomeBeneficiario(beneficiario.getNomeBeneficiario());
    c.setNossoNumeroBeneficiario(beneficiario.getNossoNumero());
    c.setNumeroConvenioBeneficiario(beneficiario.getNumeroConvenio());
  }

  public List<GetCobrancasBancarias> findCobrancasByConta(Long idConta) {
    return repository.findByIdConta(idConta);
  }

  public List<CobrancaBancaria> findByIdProdutoInstituicao(Integer idProdutoInstituicao) {
    return repository.findByIdProdutoInstituicao(idProdutoInstituicao);
  }

  public CobrancaBancaria getCobrancaOriginalRegistrada(Date dataVencimento, Long idConta) {
    List<CobrancaBancaria> cobrancas =
        repository.getCobrancaOriginalRegistrada(dataVencimento, idConta);
    if (Objects.isNull(cobrancas) || cobrancas.isEmpty()) {
      return null;
    }
    return cobrancas.get(0);
  }
}
