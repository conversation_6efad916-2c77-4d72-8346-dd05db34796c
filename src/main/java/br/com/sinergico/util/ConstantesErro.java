package br.com.sinergico.util;

/**
 * Classe para resgate de códigos de erro retornados pelos serviços do Issuerback - Ao criar uma
 * nova entrada, averiguar a melhor categoria e, se possível, utilizar um grupo já existente. -
 * Utilizar o método estático "montaCodigoErro" da Classe ConstantesErroGeradorCodigo enviando o
 * Prefixo desejado para a criação sequencial do código de erro do grupamento em questão.
 */
public enum ConstantesErro {

  /*
   *                          $~$~$~$~$~$~$~$~$~$
   *                          $~ TRANSACIONAIS ~$
   *                          $~$~$~$~$~$~$~$~$~$
   */

  /* #~#~#~# Início PIX (PIX) #~#~#~# */

  /* -> Gerais PIX <- */
  PIX_CONTA_PIX_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Não foi possível encontrar conta transacional referente aos dados informados."),
  PIX_CONTA_BLOQUEADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Conta não está desbloqueada ou contaPagamento não encontrada!"),
  PIX_PESSOA_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Não foi encontrada pessoa para conta informada!"),

  /* -> Pagamentos PIX <- */
  PIX_ENVIO_MESMA_CONTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Opa! Enviar pix para a mesma conta não é possível! Confira a chave inserida."),
  PAG_PIX_SALDO_INSUFICIENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"), "Saldo insuficiente."),
  PAG_PIX_LIMITE_EFETUADOS_DIARIO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Quantidade limite diária de PIX enviados atingida!"),
  PAG_PIX_ERRO_AO_DEBITAR(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"), "Erro ao debitar na conta do pagador"),
  PAG_PIX_ERRO_COMUNICACAO_RENDIMENTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Erro inesperado na comunicação com o banco Rendimento!"),
  PAG_PIX_ERRO_AGENCIA_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"), "Dados recebidos inválidos"),

  /* -> Solicitações de Limite <- */
  SOL_PIX_CONTA_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Não existe conta transacional pix para a conta enviada: %d"),
  SOL_PIX_SOLICITACAO_EXISTENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Já existe uma solicitação pendente deste mesmo tipo. Aguarde ser aprovada ou tente novamente em 24hs"),

  /* -> PIX BRB <- */
  BRB_PIX_PEDIDO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BPX"), "Não foi encontrado o pedido %s."),
  BRB_PIX_VALOR_DIFERENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("BPX"),
      "Valor informado não condiz com valor da fatura do pedido %s."),
  BRB_PIX_PAGAMENTO_JA_CONFIRMADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BPX"),
      "O pagamento do pedido %s já foi confirmado anteriormente."),
  BRB_PIX_PEDIDO_NAO_INFORMADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BPX"), "Pedido não informado."),
  BRB_PIX_VALOR_NAO_INFORMADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BPX"), "Valor não informado."),

  /* -> Agendamentos <- */

  /* -> Configuração <- */
  CFG_PIX_INSTITUICAO_PRODUTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Não configurada instituição Pix para o produto: %d"),
  CFG_PIX_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PIX"),
      "Não configurada instituição Pix para a instituição: %d"),

  /* #~#~#~# Fim PIX #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Transferências Internas (TIN) #~#~#~# */

  TIN_CARTAO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"), "Cartão origem não encontrado."),
  TIN_STATUS_CONTA_NAO_ATIVA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "A Conta precisa estar desbloqueada para realizar Transferência Entre Contas."),
  TIN_PRODUTOS_MULTI_CONTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Só é possível transferir entre contas que participam do programa Multibolsos!"),
  TIN_GRUPO_PRODUTOS_DISTINTOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Contas pertencentes ao programa multiconta devem pertencer ao mesmo grupo de produtos para realizar a transferência."),
  TIN_RESGATE_MOEDEIRO_ORIGEM(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Este tipo de resgate só pode ser efetuado a partir da conta Moedeiro!"),
  TIN_RESGATE_MOEDEIRO_DESTINO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "A conta Moedeiro só pode enviar saldo para a conta Saldo Livre!"),
  TIN_MESMA_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Só é possível transferir entre contas da mesma Instituição!"),
  TIN_MESMA_HIERARQUIA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Não é possível transferir para contas de programas Multibolsos em fontes distintas!"),
  TIN_MESMA_MOEDA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"), "Produtos com moedas diferentes."),
  TIN_CODIGO_TRANSACAO_NAO_RECONHECIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"), "Codigo de transação não encontrado"),
  TIN_SALDO_INSUFICIENTE(ConstantesErroGeradorCodigo.montaCodigoErro("TIN"), "Saldo insuficiente."),
  TIN_MATRIZ_PRODUTOS_NAO_PERMITIDOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Contas pertencentes ao produto %s não podem transferir fundos para contas do produto %s."),
  TIN_ERRO_INESPERADO_TRANSFERENCIA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Erro Inesperado ao realizar a transferência."),
  TIN_COBRAR_QR_CODE_APENAS_LOJISTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Apenas contas de produto Lojista podem fazer cobranças com QR Code."),
  TIN_METODO_SEGURANCA_NAO_CONFIGURADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "A conta não possui método de segurança configurado para efetivar a compra."),
  TIN_SENHA_INCORRETA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Senha do cartão incorreta. Após três tentativas mal-sucedidas consecutivas o cartão será bloqueado!"),
  TIN_TOKEN_INCORRETO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Token informado está incorreto ou expirado. Tente novamente!"),
  TIN_CREDENCIAL_NAO_DESBLOQUEADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Não é possível fazer transferência sem um cartão desbloqueado."),
  TIN_PRODUTOS_NAO_PERMITIDOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIN"),
      "Transferência não permitida entre os produtos."),

  /* #~#~#~# Fim Transferências Internas #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início TED (TED) #~#~#~# */
  /* #~#~#~# Fim TED #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Boletos (BOL) #~#~#~# */

  /* -> Sistema Geral <- */
  BOL_HORARIO_PERMITIDO_PAGAMENTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Pagamento de contas somente de segundas as sextas-feiras, dias úteis, das 7h às 20h."),
  BOL_NUM_BOLETO_NAO_INFORMADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"), "Número do boleto não informado"),
  BOL_ERRO_AO_REALIZAR_PAG(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Erro ao realizar pagamento de conta no rendimento."),
  BOL_SISTEMA_INDISPONIVEL_PARA_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("GEN"),
      "Serviço temporariamente indisponível para a instituição"),

  /* -> GatewayPagamento <- */
  GAT_BOL_CONTRATO_GATEWAY_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Gateway não corresponde aos gateways conhecidos."),
  CONFIG_CONTRATO_GARANTIA(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Contrato precisa ter o IdContaGarantiaInst configurada"),

  /* -> Convenios e Segmento <- */
  CONV_NAO_EXISTE_CONVENIO_PARA_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Desculpe! Infelizmente, não temos convênio com esta instituição."),
  CONV_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"), "Convênio não encontrado"),
  SEG_DIGITO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Não foi possível identificar primeiro digito ou segmento do boleto."),
  SEG_CONV_ERRO_GERAL_NAO_ESPECIFICADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Erro ao identificar convenioTipoSegmento, convenio ou realizar pagamento de titulo"),
  SEG_SEGMENTO_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Segmento inválido, verificar linha digitável"),

  /* -> Rendimento <- */
  REND_RETORNO_NULO_VAZIO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Value object ou Data object nulo ou vazio"),
  REND_ERRO_GERAL_CONSULTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Erro ao realizar consulta dos dados do boleto"),
  REND_FALHA_CONEXAO_RENDIMENTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"), "Falha na conexão com o rendimento"),
  REND_VALOR_MAXIMO_NAO_PERMITIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Valor do pagamento não pode ser maior que o valor máximo permitido."),
  REND_VALOR_DIFERENTE_DO_INTERVALO_PERMITIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Valor do pagamento não pode ser menor que o valor mínimo e maior que o valor máximo permitido."),
  REND_VALOR_DIFERENTE_DO_REGISTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Valor do pagamento não pode ser diferente do valor registrado."),
  REND_VALOR_DIFERENTE_DO_MINIMO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Valor do pagamento não pode ser diferente do valor mínimo."),
  REND_VALOR_DEVE_SER_INFORMADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"), "Valor a pagar deve ser informado."),
  REND_ERRO_AO_TRADUZIR_MSG(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Não foi possível traduzir mensagem devolvida pelo rendimento."),
  REND_ERRO_AO_REALIZAR_PAGAMENTO_TRIBUTOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Falha ao realizar pagamento de tributos no rendimento."),
  REND_ERRO_AO_REALIZAR_PAGAMENTO_CONSUMO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Falha ao realizar pagamento de contas consumo no rendimento."),
  REND_ERRO_AO_REALIZAR_PAGAMENTO_TITULOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Falha ao realizar pagamento de titulos no rendimento."),
  REND_BOLETO_EM_SITUACAO_INAPTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Código de situação do boleto fora da lista permitida. %s"),
  /* -> Fluxo Pagamento <- */
  REND_VALOR_FORA_DO_PERMITIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Valor a pagar não pode ser maior que R$ 9.999.999,99"),
  REND_VALOR_NAO_PODE_SER_ZERADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Valor a pagar deve ser maior que R$0,00"),
  CODIGO_MOEDA_NAO_ENCONTRADO_PARA_PRODUTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("BOL"),
      "Não foi possível encontrar o código da moeda do produto"),

  /* #~#~#~# Fim Boletos #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Pagamento e Saldo (SAL) #~#~#~# */

  SAL_ERRO_CONSULTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("SAL"),
      "Não foi possível consultar o saldo da conta."),
  SAL_SALDO_INSUFICIENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("SAL"),
      "Saldo insuficiente para efetuar esta transação"),
  SAL_FALHA_AO_SENSIBILIZAR_SALDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("SAL"),
      "Falha ao sensibilizar saldo da Conta do Portador."),

  /* #~#~#~# Fim Pagamento e Saldo #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Recarga (REC) #~#~#~# */
  /* #~#~#~# Fim Recarga #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Voucher (VOU) #~#~#~# */
  /* #~#~#~# Fim Voucher #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .-  |  -.. .. ...- .. ... --- .-. .. .- */

  /*
   *                          $~$~$~$~$~$~$~$~$~$
   *                          $~   CADASTRAIS  ~$
   *                          $~$~$~$~$~$~$~$~$~$
   */

  /* #~#~#~# Instituicao (INS) #~#~#~# */

  INS_INSTITUICAO_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("INS"), "Id Instituição %d não encontrada"),

  /* #~#~#~# Fim Instituicao #~#~#~# */

  /* #~#~#~# Codigo Transacao (CTX) #~#~#~# */

  CTX_CODIGO_TRANSACAO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CTX"), "Código de Transação %d não encontrado."),

  /* #~#~#~# Fim Codigo Transacao (CTX) #~#~#~# */

  /* #~#~#~# Início Integração TOTVS (TOT) #~#~#~# */
  TOT_FALHA_PRE_REGISTRO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TOT"), "FALHA PRE-REGISTRO DE %s NA TOTVS: %s"),
  /* #~#~#~# Fim Integração TOTVS #~#~#~# */

  /* #~#~#~# Início Usuários (USR) #~#~#~# */
  /* #~#~#~# Fim Usuários #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Portador Login (PTL) #~#~#~# */

  PTL_TIPO_LOGIN_NAO_IDENTIFICADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PTL"),
      "Tipo login não identificado para o documento %s."),
  PTL_APLICATIVO_NAO_IDENTIFICADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PTL"), "Aplicativo não reconhecido."),
  PTL_LOGADO_NAO_POSSUI_CONTAS(
      ConstantesErroGeradorCodigo.montaCodigoErro("PTL"), "Portador logado não possui contas."),
  PTL_CONTA_NAO_PERTENCE_AO_PORTADOR(
      ConstantesErroGeradorCodigo.montaCodigoErro("PTL"),
      "A conta informada %d não pertence ao portador"),
  PTL_NENHUMA_CONTA_PERTENCE_AO_PORTADOR(
      ConstantesErroGeradorCodigo.montaCodigoErro("PTL"),
      "Nenhuma das contas associadas %s pertence ao portador"),
  PTL_RESPONSAVEL_DO_DEPENDENTE_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PTL"),
      "Portador Responsável do dependente não encontrado."),

  /* #~#~#~# Fim Portador Login #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Pessoa Dependente (DEP) #~#~#~# */

  DEP_RELACAO_DEPENDENCIA_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("DEP"),
      "Não foi possível encontrar a relação de dependência com a pessoa informada."),
  DEP_DEPENDENTE_JA_AUTORIZADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("DEP"), "Este dependente já foi autorizado."),
  DEP_AUTORIZACAO_JA_NEGADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("DEP"),
      "A autorização deste dependente já está negada."),
  DEP_DEPENDENTE_JA_CADASTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("DEP"),
      "O Cancelamento não pôde ser realizado, pois este dependente já criou uma conta para acesso!"),

  /* #~#~#~# Fim Pessoa Dependente #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Contas (CTP) #~#~#~# */

  CTP_CONTA_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CTP"), "Conta não encontrada."),
  CTP_CONTA_PRECISA_ESTAR_DESBLOQUEADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CTP"),
      "A Conta precisa estar Desbloqueada para %s."),

  /* #~#~#~# Fim Contas #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Produtos (PRD) #~#~#~# */

  PRD_PRODUTO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PRD"), "Produto não encontrado."),
  PRD_INFORMACOES_LOGIN_CONTAS_NAO_ENCONTRADAS(
      ConstantesErroGeradorCodigo.montaCodigoErro("PRD"),
      "Não foram encontradas informações de login/contas para o documento %s."),
  /* #~#~#~# Fim Produtos #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Cartões (CAR) #~#~#~# */

  CAR_STATUS_BLOQUEADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"), "O Cartão físico não está ativo."),
  CAR_STATUS_NAO_PERMITE_ALTERACAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Status atual do cartão não permite esta alteração."),
  CAR_ESTADO_NFC_INEXISTENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Estado NFC desejado não existe. Por favor tente novamente."),
  CAR_ESTADO_NFC_JA_CONFIGURADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Credencial já está na configuração NFC escolhida!"),
  CAR_GERACAO_IMPEDIDA_PORTADOR(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Conta enviada não pertence à pessoa solicitante."),
  CAR_GERACAO_PESSOA_DIVERGENTE_CONTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Conta recebida não pertence à pessoa para qual o cartão foi solicitado."),
  CAR_GERACAO_CARTAO_VIRTUAL_OU_FISICO_JA_EXISTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Não é possível cadastrar nova credencial; Já existe credencial %s ativa para esta hierarquia e grupo."),

  CAR_VALIDAR_TROCA_ESTADO_NFC(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAR"),
      "Validação trocar estado NFC da Credencial."),

  /* #~#~#~# Fim Cartões #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Cartões QR Code (CQR) #~#~#~# */

  NAO_FOI_POSSIVEL_GERAR_IMAGEM_QR_CODE(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Não foi possível gerar a imagem do QR Code! Tente novamente mais tarde."),
  TERMOS_DE_BUSCA_INCOMPLETOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Termos de busca incompletos. Por favor, tente novamente!"),
  QR_CODE_LIDO_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "O QR Code lido não é válido em nosso sistema!"),
  CARTAO_QR_CODE_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"), "Cartão QR Code não encontrado. %s"),
  CARTAO_QR_CODE_NAO_ENCONTRADO_PARA_VINCULACAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Nenhum Cartão QR Code encontrado para vincular"),
  CARTAO_QR_CODE_JA_VINCULADO_A_PROPRIA_CONTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Este QR Code já está vinculado à sua conta!"),
  CARTAO_QR_CODE_JA_VINCULADO_A_OUTRA_CONTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Este QR Code já está vinculado a outra conta!"),
  DADOS_INCOMPLETOS_PARA_VINCULACAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Dados inseridos para vinculação estão incompletos."),
  MAXIMO_DE_VINCULACOES_POR_CONTA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Esta conta já possui o máximo de QR Codes ativos permitidos vinculados. Considere cancelar um antes de vincular outro."),
  CARTAO_QR_CODE_NAO_VINCULADO_AINDA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Este QR Code ainda não foi vinculado a uma Conta."),
  ERRO_AO_BUSCAR_DETALHES(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Ocorreu um erro ao buscar detalhes do Cartão QR Code."),
  QR_CODE_NAO_ENCONTRADO_PARA_CHAVE(
      ConstantesErroGeradorCodigo.montaCodigoErro("CQR"),
      "Não foi encontrado um Cartão QR Code para associação com a chave pré vinculação: %s"),

  /* #~#~#~# Fim Cartões QR Code #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Cargas #~#~#~# */
  /* #~#~#~# Fim Cargas #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Limite TED e Boleto (LTB)#~#~#~# */

  NAO_FOI_POSSIVEL_RECUPERAR_DADOS_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("LTB"),
      "Não foi possível recuperar os produtos instituição."),
  ID_INSTITUICAO_E_ACESSO_USUARIO_NAO_PODE_SER_NULO(
      ConstantesErroGeradorCodigo.montaCodigoErro("LTB"),
      "idInstituicao e acessoUsuario não podem ser nulos.."),

  /* #~#~#~# Limite TED e Boleto #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Limite diario#~#~#~# */
  MOTIVO_ALTERACAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("LD"), "Motivo da alteração obrigatório"),
  VALOR_LIMITE_DIARIO(
      ConstantesErroGeradorCodigo.montaCodigoErro("LD"), "Valor limite precisar ser maior que 0"),

  /* #~#~#~# Inicio Produto PDAF (PDAF) #~#~#~# */

  PDAF_ERRO_AO_PROCESSAR_ARQUIVO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PDAF"),
      "Erro ao receber arquivo do Pdaf para processamento."),
  PDAF_ARQUIVO_RECEBIDO_FORA_DO_PADRAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PDAF"),
      "Nome do arquivo recebido para cadastro de titular/adicional fora do padrão esperado."),
  ERRO_SALVAR_ARQUIVO_PDAF_SERVIDOR(
      ConstantesErroGeradorCodigo.montaCodigoErro("PDAF"),
      "Não foi possível salvar o arquivo no caminho indicado: %s"),

  /* #~#~#~# Inicio Produto PDAF #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Processamento Arquivos Recebidos PROC5000 (ARQCAD) #~#~#~# */

  ERRO_RECEBER_ARQUIVO_CADASTRO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Erro ao tentar salvar / processar o arquivo do cadastro do Pdaf acionado atraves da PROC5000."),
  ERRO_ARQUIVO_RECEBIDO_DIFERENTE_ESPERADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Recebido arquivo para processamento diferente do esperado. Recebido: %s . Esperado : %s."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está fora do padrão esperado."),
  ERRO_ARQUIVO_CADASTRO_EXTENSAO_NAO_RECONHECIDA_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O arquivo enviado do Pdaf pela PROC5000 não está no formato aceito."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_PREFIXO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está sem o prefixo."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_INSTITUICAO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está sem a instituição."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_REGIONAL_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está sem a regional."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_FILIAL_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está sem a filial."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_PONTO_RELACIONAMENTO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está sem o ponto de relacionamento."),
  ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_PRODUTO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "O nome do arquivo de cadastrado do Pdaf enviado pela PROC5000 está sem o produto."),
  ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_CRIAR_ARQUIVO_EXCEL(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Nào foi possível ler o arquivo do Pdaf enviado pela PROC5000."),
  ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_CRIAR_OBJETO(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Nào foi possível ler o arquivo do Pdaf enviado pela PROC5000 para realizar a conversão em objeto."),
  ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_PRODUTO_SEM_CONFIGURACAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Não foi possível completar a operação. Produto Instituição Configuração não encontrado."),
  ERRO_RECEBER_ARQUIVO_EDICAO_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Erro ao tentar salvar / processar o arquivo do edicao do Pdaf acionado atraves da PROC5000."),
  ERRO_RECEBER_ARQUIVO_ADICIONAL_PROC5000_PDAF(
      ConstantesErroGeradorCodigo.montaCodigoErro("ARQCAD"),
      "Erro ao tentar salvar / processar o arquivo do adicional do Pdaf acionado atraves da PROC5000."),

  /* #~#~#~# Fim Processamento Arquivos Recebidos PROC5000 #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início URA Multibenefícios #~#~#~# */

  URA_SENHA_INVALIDA(ConstantesErroGeradorCodigo.montaCodigoErro("URA"), "Senha inválida."),
  URA_ERRO_RECUPERAR_DOCUMENTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"),
      "Não foi possível recuperar o DOCUMENTO. Problemas com a descriptografia do DOCUMENTO."),
  URA_CONTA_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"), "Erro ao buscar a conta pelo documento."),
  URA_ERRO_CONSULTA_SALDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"), "Não foi possível consultar o saldo."),
  URA_ERRO_ALTERAR_STATUS_CARTAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"),
      "Não foi possível alterar o status solicitado para o cartão. %s"),
  URA_ERRO_STATUS_DESTINO_FORA_DO_PERMITIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"),
      "É permitido apenas status de desbloqueio e bloqueio temporário"),
  URA_CREDENCIAL_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"),
      "Não foi encontrada credencial para ser desbloqueada."),
  URA_ERRO_STATUS_JCARD(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"),
      "Falha ao alterar status da credencial no Jcard."),
  URA_ERRO_RECUPERAR_SENHA(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"), "Erro ao recuperar senha %s"),
  URA_ERRO_STATUS_JA_ESCOLHIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("URA"),
      "O status destino selecionado já é o atual. Escolha outro status destino."),

  /* #~#~#~# Fim URA Multibenefícios #~#~#~# */

  /* #~#~#~# Início Parceiro Comercial #~#~#~# */
  PARCOR_DOCUMENTO_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"),
      "Documento do Parceiro inserido é inválido"),
  PARCOR_DOCUMENTO_VINCULADO_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"),
      "Documento do Parceiro Comercial Vinculado inserido é inválido"),
  PARCOR_STATUS_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"),
      "Status do Parceiro inserido é inválido"),
  PARCOR_NOME_PARCEIRO_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"), "Nome do parceiro é inválido"),
  PARCOR_INATIVADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"), "Parceria Comercial Inativada"),
  PARCOR_NOME_PARCEIRO_COMERCIAL_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"),
      "Nome Parceiro inserido %s é inválido"),
  PARCOR_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"), "Parceira Comercial não encontrada."),
  PARCOR_PARCERIA_COMERCIAL_JA_EXISTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("PARCOM"),
      "A Parceria Comercial do documento %s já existe."),

  TIPPARCOR_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIPPARCOM"),
      "Tipo Parceira Comercial não encontrada."),
  TIPPARCOR_NOME_TIPO_INVALIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("TIPPARCOM"),
      "Nome Tipo Parceira Comercial é inválido."),
  /* #~#~#~# Fim Parceiro Comercial #~#~#~# */

  /* #~#~#~# Início Adicional Conta #~#~#~# */
  B2B_ADICIONAL_CONTA_PRODUTO_NAO_SELECIONADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("B2B"), "Não foi selecionado nenhum produto!"),
  B2B_ADICIONAL_CONTA_RELATORIO_NAO_DISPONIVEL(
      ConstantesErroGeradorCodigo.montaCodigoErro("B2B"),
      "Este relatório não está disponível para os produtos atualmente contratados!"),
  B2B_ADICIONAL_CONTA_ERRO_AO_PROCESSAR_QUERY_E_MONTAR_O_RELATORIO(
      ConstantesErroGeradorCodigo.montaCodigoErro("B2B"),
      "Erro ao processar consulta no banco de dados e montar o relatório!"),
  /* #~#~#~# Fim Adicional Conta #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .-  |  -.. .. ...- .. ... --- .-. .. .- */

  /*
   *                          $~$~$~$~$~$~$~$~$~$~$~$~$
   *                          $~  SEGURANÇA CAF/OCR  ~$
   *                          $~$~$~$~$~$~$~$~$~$~$~$~$
   */

  /* #~#~#~# Início CAF (CAF) #~#~#~# */

  CAF_ERRO_AO_SALVAR_DADOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAF"),
      "Não foi possível salvar portador caf após buscar status na API."),
  CAF_ERRO_AO_ALTERAR_STATUS(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAF"), "Não foi possível alterar status. %s"),
  CAF_TRANSACTION_ID_OU_ACTION_NULO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAF"),
      "Transaction ID ou action nulo ou vazio."),
  CAF_CAMPO_REASON_NULO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAF"),
      "Erro na reprovação da solicitação Caf, campo reason nulo"),
  CAF_NAO_ENCONTRADA_RECONHECIMENTO_FACIAL(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAF"),
      "Incapaz de validar reconhecimento facial."),

  /* #~#~#~# Fim CAF #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Método de Segurança (SEG) #~#~#~# */

  SEG_VALIDACAO_NAO_CONCLUIDA_GENERICO(
      ConstantesErroGeradorCodigo.montaCodigoErro("SEG"), "Validação de segurança não concluída."),
  SEG_OBJETO_VALIDACAO_NAO_RECONHECIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("SEG"), "Objeto de validação não reconhecido"),

  /* #~#~#~# Fim Método de Segurança #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Trava de Contas ou Serviços (TRV) #~#~#~# */

  TRV_TRAVA_CONTAS(
      ConstantesErroGeradorCodigo.montaCodigoErro("SEG"),
      "Serviço temporariamente indisponível para a conta %d."),
  TRV_TRAVA_SERVICOS(
      ConstantesErroGeradorCodigo.montaCodigoErro("SEG"), "Serviço temporariamente indisponível."),

  /* #~#~#~# Fim Trava de Contas ou Serviços #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .- */

  /* #~#~#~# Início Mensagens/Notificacoes (MSG) #~#~#~# */

  MSG_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("MSG"), "Não foi possivel buscar mensagens."),
  MSG_NAO_FOI_POSSIVEL_MARCAR_COMO_LIDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("MSG"),
      "Não foi possível marcar mensagens como lida"),

  /* #~#~#~# Fim Mensagens/Notificacoes #~#~#~# */

  /* -.. .. ...- .. ... --- .-. .. .-  |  -.. .. ...- .. ... --- .-. .. .- */

  /*
   *                          $~$~$~$~$~$~$~$~$~$
   *                          $~   GENÉRICOS   ~$
   *                          $~$~$~$~$~$~$~$~$~$
   */

  /* -> Parâmetros Valor <- */

  ERRO_RESGATE_PARAMETRO_VALOR(
      ConstantesErroGeradorCodigo.montaCodigoErro("PAR"),
      "Não foi possível resgatar informações de parâmetros!"),

  /* -> JCARD <- */
  JCARD_ERRO_TROCA_STATUS(
      ConstantesErroGeradorCodigo.montaCodigoErro("JCD"),
      "Não foi possível alterar o status do cartão. Credencial não encontrada. %s"),

  /* -> JCARD Conta Garantia <- */
  JCARD_GARANTIA_SALDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("JCG"),
      "Falha ao buscar o saldo da Conta Garantia: %s"),
  JCARD_GARANTIA_APORTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("JCG"),
      "Falha ao realizar aporte em Conta Garantia: %s"),
  JCARD_GARANTIA_APORTE_SEM_SALDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("JCG"),
      "Conta Garantia %s não possui saldo suficiente para o resgate"),
  JCARD_GARANTIA_FALHA_ALTERAR_VALOR_REFERENCIA(
      ConstantesErroGeradorCodigo.montaCodigoErro("JCG"),
      "Falha ao alterar o valor de referência da Conta Garantia: %s"),

  /* -> Plataforma Conta Garantia <- */
  PCG_GARANTIA_ID_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"),
      "Conta garantia de id %d não encontrada."),
  PCG_GARANTIA_ALERTA_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"), "Alerta de id %d não encontrado."),
  PCG_GARANTIA_INSTITUICAO_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"),
      "Não existe conta garantia cadastrada para a instituição %d."),
  PCG_GARANTIA_CONTATO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"), "Contato de id %d não encontrado."),
  PCG_GARANTIA_CONTATO_OUTRA_INSTITUICAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"),
      "O id da instituição do contato, %d, é distinta do id da instituição do alerta, %d."),
  PCG_GARANTIA_ALERTA_CORRENTE_ESPECIFICO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"),
      "AlertaCorrente com Conta Garantia de id %d e criticidade %s inexistente."),
  PCG_GARANTIA_ALERTA_SAIDA_ESPECIFICO_NAO_ENCONTRADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"),
      "AlertaSaida com Conta Garantia de id %d e criticidade %s inexistente."),
  PCG_GARANTIA_TRANSACAO_CONTROLADA_NAO_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("PCG"),
      "Transação controlada de id %d não encontrada."),

  /* -> Retornos Comuns <- */

  ERRO_INESPERADO(
      ConstantesErroGeradorCodigo.montaCodigoErro("GEN"),
      "Ocorreu um erro inesperado. %s"), // Utilizar FORMAT para adicionar string de contexto
  TENTE_NOVAMENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("GEN"), "Tente novamente mais tarde."),
  ERRO_AO_REALIZAR_OPERACAO(
      ConstantesErroGeradorCodigo.montaCodigoErro("GEN"), "Não foi possivel efetuar operação."),

  /* #~#~#~# Início Campanhas #~#~#~# */
  CAMP_ERRO_AO_CADASTRAR_CAMPANHA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAM"),
      "Não foi possivel cadastrar campanha. %s"),
  CAMP_ERRO_AO_BUCAR_PONTO_RELACIONAMENTO(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAM"),
      "Ponto de relacionamento não encontrado. %s"),
  CAMP_ERRO_NENHUMA_CAMPANHA_ENCONTRADA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAM"), "Nenhuma campanha foi encontrada"),
  CAMP_ERRO_NAO_FOI_POSSIVEL_BUSCAR_CAMPANHA(
      ConstantesErroGeradorCodigo.montaCodigoErro("CAM"), "Não foi possível buscar campanhas. %s"),

  /* #~#~#~# Início Campanhas #~#~#~# */

  /* #~#~#~# Início Corporativo #~#~#~# */
  CADASTRO_RESPONSAVEL_CORPORATIVO_EXISTENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("COR"),
      "Responsável já existente. Aguarde o período expirar para realizar um novo cadastro."),
  RESPONSAVEL_CORPORATIVO_EXISTENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("COR"), "Nenhum responsável cadastrado."),
  DADOS_RESPONSAVEL_CORPORATIVO_INEXISTENTE(
      ConstantesErroGeradorCodigo.montaCodigoErro("COR"), "Dados do responsável não encontrado."),
  DADA_INVALIDA_CORPORATIVO(
      ConstantesErroGeradorCodigo.montaCodigoErro("COR"),
      "Data de início e fim não pode ser anterior à data atual."),
  TRANSFERENCIA_SALDO(
      ConstantesErroGeradorCodigo.montaCodigoErro("COR"),
      "Não foi possível realizar a transferência de saldo.");

  private String codigo;
  private String mensagem;

  ConstantesErro(String codigo, String mensagem) {
    this.codigo = codigo;
    this.mensagem = mensagem;
  }

  public String getCodigo() {
    return codigo;
  }

  public String getMensagem() {
    return codigo + " - " + mensagem.replaceAll("%[sd]", "{Informação não fornecida}");
  }

  /**
   * Formata a mensagem do erro com os parâmetros fornecidos.
   *
   * @param args Argumentos utilizados para preencher os placeholders na mensagem.
   * @return A mensagem de erro formatada.
   */
  public String format(Object... args) {
    return codigo + " - " + String.format(mensagem, args);
  }
}
