package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.CargaAutoProdutoContaBancaria;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.adq.AtualizarProdutoBancarioVO;
import br.com.json.bean.adq.ParametrosCargaProdutosBancariosVO;
import br.com.json.bean.adq.ProdutosCargaContaBancariaVO;
import br.com.sinergico.repository.cadastral.CargaAutoProdutoContaBancariaRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import java.time.LocalDateTime;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CargaAutoProdutoContaBancariaService
    extends GenericService<CargaAutoProdutoContaBancaria, Long> {

  private CargaAutoProdutoContaBancariaRepository repository;

  @Autowired
  public CargaAutoProdutoContaBancariaService(
      CargaAutoProdutoContaBancariaRepository cargaAutoProdutoContaBancariaRepository) {
    super(cargaAutoProdutoContaBancariaRepository);
    this.repository = cargaAutoProdutoContaBancariaRepository;
  }

  public void salvar(CargaAutoProdutoContaBancaria cargaAutoProdutoContaBancaria) {
    repository.save(cargaAutoProdutoContaBancaria);
  }

  public void salvarLista(List<CargaAutoProdutoContaBancaria> cargaAutoProdutoContaBancariaList) {
    repository.saveAll(cargaAutoProdutoContaBancariaList);
  }

  public Boolean salvarProdutosContaBancaria(
      ParametrosCargaProdutosBancariosVO model, SecurityUser user) {

    for (Integer cargaProdutosBancariosVO : model.getIdProdInstituicao()) {

      for (ProdutosCargaContaBancariaVO listContaBancaria : model.getProdutosContaBancaria()) {
        CargaAutoProdutoContaBancaria produtoBancaria = new CargaAutoProdutoContaBancaria();
        produtoBancaria.setIdProdInstituicao(cargaProdutosBancariosVO);
        produtoBancaria.setIdBanco(listContaBancaria.getIdBanco());
        produtoBancaria.setIdTipoTransferencia(listContaBancaria.getIdTipoTransferencia());
        produtoBancaria.setNrAgencia(listContaBancaria.getNrAgencia());
        produtoBancaria.setNrContaBancaria(listContaBancaria.getNrContaBancaria());
        produtoBancaria.setNrTipoContaBancaria(listContaBancaria.getNrTipoContaBancaria());
        produtoBancaria.setIdTipoChavePix(listContaBancaria.getIdTipoChavePix());
        produtoBancaria.setNrDvAgencia(listContaBancaria.getNrDvAgencia());
        produtoBancaria.setNrDvContaBancaria(listContaBancaria.getNrDvContaBancaria());
        produtoBancaria.setTxChavePix(listContaBancaria.getTxChavePix());
        produtoBancaria.setIdStatus(1);
        produtoBancaria.setDtHrStatus(LocalDateTime.now());
        produtoBancaria.setIdUsuarioInclusao(user.getIdUsuario());
        produtoBancaria.setDtHrInclusao(LocalDateTime.now());
        repository.save(produtoBancaria);
      }
    }
    return true;
  }

  public List<CargaAutoProdutoContaBancaria> buscarProdutosContaBancaria(
      Integer idProdInstituicao) {
    return repository.findByIdProdInstituicao(idProdInstituicao);
  }

  public Boolean atualizarProdutoContaBancaria(
      Long id, AtualizarProdutoBancarioVO model, SecurityUser user) {

    CargaAutoProdutoContaBancaria cargaAutoProdutoContaBancaria =
        repository.findById(id).orElse(null);
    if (cargaAutoProdutoContaBancaria == null) {
      throw new GenericServiceException("Conta Bancária não encontrada!");
    }

    cargaAutoProdutoContaBancaria.setIdStatus(model.getIdStatus());
    cargaAutoProdutoContaBancaria.setDtHrStatus(LocalDateTime.now());
    cargaAutoProdutoContaBancaria.setIdUsuarioManutencao(user.getIdUsuario());
    repository.save(cargaAutoProdutoContaBancaria);

    return true;
  }
}
