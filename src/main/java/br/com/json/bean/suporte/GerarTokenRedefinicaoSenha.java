package br.com.json.bean.suporte;

import br.com.sinergico.annotation.Documento;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;
import org.hibernate.validator.constraints.br.CPF;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class GerarTokenRedefinicaoSenha {

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idProcessadora;

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idInstituicao;

  @ApiModelProperty(required = false)
  private Long idConta;

  @ApiModelProperty(required = false)
  private Long idCredencial;

  @NotEmpty
  @Documento
  @ApiModelProperty(required = true)
  private String documento;

  @CPF
  @ApiModelProperty(required = false)
  private String cpfRepresentante;

  @NotEmpty
  @ApiModelProperty(required = true)
  private String dataNascimento;

  @ApiModelProperty(required = false)
  private Integer minutosExpiracao;

  @ApiModelProperty(required = false)
  private Long grupoAcesso;

  @ApiModelProperty(required = false)
  private TipoPortadorLoginEnum tipoLogin;
}
