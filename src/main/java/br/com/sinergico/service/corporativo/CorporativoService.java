package br.com.sinergico.service.corporativo;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.CorporativoLogin;
import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.entity.cadastral.CorporativoResponsavelCredencial;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.suporte.AplicativoFrontend;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.CadastroResponsavelVO;
import br.com.json.bean.cadastral.ContaEnderecoPessoaDTO;
import br.com.json.bean.cadastral.DesvincularCredencialVO;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.facade.cadastral.ContaPagamentoFacade;
import br.com.sinergico.repository.cadastral.ContaPagamentoRepository;
import br.com.sinergico.repository.cadastral.CorporativoLoginRepository;
import br.com.sinergico.repository.cadastral.CorporativoResponsavelCredencialRepository;
import br.com.sinergico.repository.cadastral.CorporativoResponsavelRepository;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.suporte.AplicativoFrontendRepository;
import br.com.sinergico.repository.suporte.AplicativoFrontendServicoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.EnderecoPessoaService;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.AplicativoServicoVO;
import br.com.sinergico.vo.ContasEmLoteVO;
import br.com.sinergico.vo.ResponsavelCorporativoVO;
import br.com.sinergico.vo.TransferenciaContaVO;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
public class CorporativoService {

  public static final int STATUS_BLOQUEIO_TEMPORARIO = 5;
  public static final int STATUS_CANCELADO_PELO_USUARIO = 30;
  public static final int TAMANHO_SENHA = 4;
  public static final int COD_TRANSFERENCIA_INTERNA = 556;
  public static final int STATUS_DESBLOQUEADO = 1;

  @Autowired private CorporativoResponsavelRepository corporativoResponsavelRepository;

  @Autowired
  private CorporativoResponsavelCredencialRepository corporativoResponsavelCredencialRepository;

  @Autowired private CredencialService credencialService;

  @Autowired private CredencialRepository credencialRepository;

  @Autowired private ContaPagamentoRepository contaPagamentoRepository;

  @Autowired @Lazy private ContaPagamentoFacade contaPagamentoFacade;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private EnderecoPessoaService enderecoPessoaService;

  @Autowired private AplicativoFrontendRepository aplicativoFrontendRepository;

  @Autowired private AplicativoFrontendServicoRepository aplicativoFrontendServicoRepository;

  @Autowired private CorporativoLoginRepository corporativoLoginRepository;

  public void cadastrarResponsavel(SecurityUser user, CadastroResponsavelVO request) {

    if (request.getDataInicio().toLocalDate().isBefore(LocalDate.now())) {
      throw new GenericServiceException(ConstantesErro.DADA_INVALIDA_CORPORATIVO.getMensagem());
    }

    if (request.getDataFimPrevisto() != null
        && request
            .getDataFimPrevisto()
            .toInstant()
            .atZone(ZoneId.systemDefault())
            .toLocalDate()
            .isBefore(LocalDate.now())) {
      throw new GenericServiceException(ConstantesErro.DADA_INVALIDA_CORPORATIVO.getMensagem());
    }

    CorporativoResponsavel responsavelCredencial =
        corporativoResponsavelRepository.findCorporativoResponsavelByCredencial(
            request.getIdCredencial());

    if (responsavelCredencial != null) {
      throw new GenericServiceException(
          ConstantesErro.CADASTRO_RESPONSAVEL_CORPORATIVO_EXISTENTE.getMensagem());
    }

    CorporativoResponsavel responsavelDocumento =
        corporativoResponsavelRepository.findByDocumento(request.getDocumento());

    if (responsavelDocumento != null) {
      responsavelDocumento.setNome(request.getNome());
      responsavelDocumento.setEmail(request.getEmail());
      responsavelDocumento.setDdd(request.getDdd());
      responsavelDocumento.setCelular(request.getCelular());
      responsavelDocumento.setDtHrManutencao(LocalDateTime.now());
      responsavelDocumento.setIdUsuarioManutencao(user.getIdUsuario().longValue());
      corporativoResponsavelRepository.save(responsavelDocumento);
      vincularCredencial(responsavelDocumento, request);
    } else {
      CorporativoResponsavel corporativoResponsavel = new CorporativoResponsavel();
      corporativoResponsavel.setDocumento(request.getDocumento());
      corporativoResponsavel.setNome(request.getNome());
      corporativoResponsavel.setEmail(request.getEmail());
      corporativoResponsavel.setDdd(request.getDdd());
      corporativoResponsavel.setCelular(request.getCelular());
      corporativoResponsavel.setDtHrInclusao(LocalDateTime.now());
      corporativoResponsavel.setIdUsuarioInclusao(user.getIdUsuario().longValue());
      corporativoResponsavel.setDtHrManutencao(LocalDateTime.now());
      corporativoResponsavel.setIdUsuarioManutencao(user.getIdUsuario().longValue());
      corporativoResponsavelRepository.save(corporativoResponsavel);
      vincularCredencial(corporativoResponsavel, request);
    }
  }

  private void vincularCredencial(
      CorporativoResponsavel corporativoResponsavel, CadastroResponsavelVO request) {
    CorporativoResponsavelCredencial corporativoResponsavelCredencial =
        new CorporativoResponsavelCredencial();
    corporativoResponsavelCredencial.setIdResponsavel(corporativoResponsavel.getId());
    corporativoResponsavelCredencial.setIdCredencial(request.getIdCredencial());
    corporativoResponsavelCredencial.setDtHrInicio(request.getDataInicio());
    corporativoResponsavelCredencial.setDtHrFimPrevisto(request.getDataFimPrevisto());
    corporativoResponsavelCredencialRepository.save(corporativoResponsavelCredencial);
  }

  public CorporativoResponsavel findByDocumento(String documento) {
    return corporativoResponsavelRepository.findByDocumento(documento);
  }

  public List<ResponsavelCorporativoVO> listaResponsavelCorporativo(Long idCredencial) {
    return corporativoResponsavelRepository.listaResponsavelPorCredencial(idCredencial);
  }

  public CorporativoResponsavel buscarResponsavelAtivo(Long idCredencial) {
    return corporativoResponsavelRepository.findCorporativoResponsavelByCredencial(idCredencial);
  }

  public CorporativoResponsavel findResponsavelAtivoByDocumento(String documento) {
    return corporativoResponsavelRepository.findAtivoByDocumento(documento);
  }

  public void desvincularCredencial(
      SecurityUser user, DesvincularCredencialVO desvincularCredencialVO, String tokenJWT) {

    CorporativoResponsavelCredencial corporativoResponsavelCredencial =
        corporativoResponsavelCredencialRepository.findByIdCredencialAndDtHrFimIsNull(
            desvincularCredencialVO.getIdCredencial());

    if (corporativoResponsavelCredencial == null) {
      throw new GenericServiceException(
          ConstantesErro.RESPONSAVEL_CORPORATIVO_EXISTENTE.getMensagem());
    }

    GetSaldoConta saldo = new GetSaldoConta();
    saldo = contaPagamentoService.getSaldoConta(desvincularCredencialVO.getIdConta());

    if (!desvincularCredencialVO.getManterSaldo()) {
      List<ContaPagamento> contaPagamentoList =
          contaPagamentoRepository.buscarContasPagamentoPorDocumentoEInstituicao(
              desvincularCredencialVO.getIdProcessadora(),
              desvincularCredencialVO.getIdInstituicao(),
              desvincularCredencialVO.getIdRegional(),
              desvincularCredencialVO.getIdFilial(),
              desvincularCredencialVO.getIdPontoRelacionamento());

      if (!contaPagamentoList.isEmpty()
          && !Objects.equals(
              contaPagamentoList.get(0).getIdConta(), desvincularCredencialVO.getIdConta())) {
        Credencial credencialDestino =
            credencialRepository.findUltimaCredencialDesbloqueadaTitularConta(
                contaPagamentoList.get(0).getIdConta());
        if (credencialDestino != null
            && saldo.getSaldoDisponivel() != null
            && saldo.getSaldoDisponivel().compareTo(BigDecimal.ZERO) > 0) {
          JcardResponse sucesso =
              realizarTransferenciaEntreContas(
                  desvincularCredencialVO.getIdInstituicao(),
                  desvincularCredencialVO.getIdCredencial(),
                  credencialDestino,
                  saldo.getSaldoDisponivel());
          if (!sucesso.getSuccess()) {
            throw new GenericServiceException(ConstantesErro.TRANSFERENCIA_SALDO.getMensagem());
          }
        }
      }
    }

    Credencial credencial =
        credencialService.findByIdCredencial(desvincularCredencialVO.getIdCredencial());

    CorporativoLogin corporativoLogin =
        corporativoLoginRepository
            .findCorporativoLoginByIdResponsavelAndIdInstituicaoAndDtHrCancelamentoIsNull(
                corporativoResponsavelCredencial.getIdResponsavel(),
                desvincularCredencialVO.getIdInstituicao());

    if (!credencial.getStatus().equals(STATUS_BLOQUEIO_TEMPORARIO)) {
      credencialService.alterarStatusCredencial(
          corporativoResponsavelCredencial.getIdCredencial(),
          STATUS_BLOQUEIO_TEMPORARIO,
          user.getIdUsuario(),
          false);
    }

    List<Credencial> credencialList =
        credencialService.findByIdContaAndVirtual(desvincularCredencialVO.getIdConta(), true);
    if (!credencialList.isEmpty()) {
      for (Credencial cred : credencialList) {
        credencialService.alterarStatusCredencial(
            cred.getIdCredencial(), STATUS_CANCELADO_PELO_USUARIO, user.getIdUsuario(), false);
      }
    }

    if (credencial != null) {
      String senha = Util.generateRandomNumeros(TAMANHO_SENHA);
      credencialService.recadastraPinCredencialCorporativo(credencial, senha, tokenJWT, user);
    }

    if (corporativoLogin != null) {
      corporativoLogin.setDtHrCancelamento(LocalDateTime.now());
      corporativoLoginRepository.save(corporativoLogin);
    }

    corporativoResponsavelCredencial.setDtHrFim(LocalDateTime.now());
    corporativoResponsavelCredencial.setSaldoMantido(desvincularCredencialVO.getManterSaldo());
    corporativoResponsavelCredencial.setMotivoBaixa(desvincularCredencialVO.getMotivoBaixa());
    corporativoResponsavelCredencialRepository.save(corporativoResponsavelCredencial);
  }

  private JcardResponse realizarTransferenciaEntreContas(
      Integer idInstituicao,
      Long idCredencialOrigem,
      Credencial credencialDestino,
      BigDecimal saldo) {
    return contaPagamentoFacade.realizarTransferencia(
        idInstituicao,
        idCredencialOrigem,
        null,
        saldo,
        null,
        false,
        false,
        credencialDestino,
        COD_TRANSFERENCIA_INTERNA,
        null,
        null,
        null);
  }

  public List<ContaEnderecoPessoaDTO> buscarEnderecoContas(
      SecurityUserCorporativo userCorporativo) {
    // todo verificar se vale a pena aqui
    //  contaPagamentoService.validaContaAtivaCorporativo(userCorporativo);
    List<Credencial> credenciais =
        this.credencialService.obterCredenciaisCorporativo(userCorporativo);
    List<ContaEnderecoPessoaDTO> contasEnderecos = new ArrayList<>();
    for (Credencial credencial : credenciais) {
      ContaEnderecoPessoaDTO contaEnderecoPessoaDTO = new ContaEnderecoPessoaDTO();
      List<EnderecoPessoa> enderecosPessoa =
          this.enderecoPessoaService.findByIdPessoa(credencial.getIdPessoa());
      contaEnderecoPessoaDTO.setIdConta(credencial.getIdConta());
      contaEnderecoPessoaDTO.setEnderecos(enderecosPessoa);
      contasEnderecos.add(contaEnderecoPessoaDTO);
    }
    return contasEnderecos;
  }

  public List<AplicativoServicoVO> listarServicos(Integer idInstituicao, boolean agruparPix) {
    List<AplicativoServicoVO> aplicativoFrontendServicos = new ArrayList<>();
    AplicativoFrontend aplicativoFrontend =
        aplicativoFrontendRepository.buscarAplicativoFrontEndPorInstituicaoENomeAplicativo(
            idInstituicao, "corporativo");
    if (aplicativoFrontend != null) {
      aplicativoFrontendServicos =
          aplicativoFrontendServicoRepository.findByIdFrontendAndIdStatus(
              aplicativoFrontend.getId(), 1);
      List<AplicativoServicoVO> pixServicos =
          aplicativoFrontendServicos.stream()
              .filter(e -> e.getNome().toLowerCase().startsWith("pix"))
              .collect(Collectors.toList());
      if (agruparPix && !pixServicos.isEmpty()) {
        AplicativoServicoVO pixServico = pixServicos.get(0);
        aplicativoFrontendServicos.removeIf(e -> e.getNome().toLowerCase().startsWith("pix"));
        pixServico.setDescServico("Pix");
        pixServico.setId(999);
        aplicativoFrontendServicos.add(pixServico);
      }
    }
    return aplicativoFrontendServicos;
  }

  public List<ContasEmLoteVO> listaTransferenciaContas(
      TransferenciaContaVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {

    model.setIsCount(Boolean.FALSE);
    Long contaBase = null;
    List<ContasEmLoteVO> contaVO =
        buscarContaBaseEmLoteVO(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
    if (!contaVO.isEmpty()) {
      contaBase = contaVO.get(0).getIdConta();
    }
    List<ContasEmLoteVO> contasEmLoteVOS =
        contaPagamentoRepository.listarContasTransferenciaEmLote(
            model,
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoRelacionamento,
            contaBase,
            List.class);

    for (ContasEmLoteVO conta : contasEmLoteVOS) {
      GetSaldoConta saldo = contaPagamentoService.getSaldoConta(conta.getIdConta());
      conta.setSaldoDisponivel(saldo.getSaldoDisponivel());
    }

    return contasEmLoteVOS;
  }

  public Long countListarContas(
      TransferenciaContaVO model,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    model.setIsCount(Boolean.TRUE);
    Long contaBase = null;
    List<ContasEmLoteVO> contaVO =
        buscarContaBaseEmLoteVO(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
    if (!contaVO.isEmpty()) {
      contaBase = contaVO.get(0).getIdConta();
    }
    return contaPagamentoRepository.listarContasTransferenciaEmLote(
        model,
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoRelacionamento,
        contaBase,
        Long.class);
  }

  public List<ContasEmLoteVO> buscarContaBaseEmLoteVO(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    TransferenciaContaVO model = new TransferenciaContaVO();
    model.setIsCount(Boolean.FALSE);
    model.setPagina(0);
    model.setQtdRegistros(10);
    List<ContasEmLoteVO> contasEmLoteVOS =
        contaPagamentoRepository.listarContasTransferenciaEmLote(
            model,
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoRelacionamento,
            null,
            List.class);
    GetSaldoConta saldo = contaPagamentoService.getSaldoConta(contasEmLoteVOS.get(0).getIdConta());
    contasEmLoteVOS.get(0).setSaldoDisponivel(saldo.getSaldoDisponivel());
    if (!contasEmLoteVOS.isEmpty()) {
      return Collections.singletonList(contasEmLoteVOS.get(0));
    }
    return Collections.emptyList();
  }

  public void transferenciaEmLote(List<ContasEmLoteVO> contasEmLoteVO) {

    for (ContasEmLoteVO conta : contasEmLoteVO) {
      Credencial credencial = credencialService.findByIdCredencial(conta.getIdCredencial());
      if (credencial.getStatus() != STATUS_DESBLOQUEADO) {
        throw new GenericServiceException(
            "Não é possível transferir o saldo: a credencial da conta"
                + conta.getIdConta()
                + " precisa está desbloqueada para realizar a transferência.");
      }
    }

    for (ContasEmLoteVO conta : contasEmLoteVO) {
      if (conta.getSaldoDisponivel() != null
          && !conta.getSaldoDisponivel().equals(BigDecimal.ZERO)) {
        List<ContasEmLoteVO> contaVO =
            buscarContaBaseEmLoteVO(
                conta.getIdProcessadora(),
                conta.getIdInstituicao(),
                conta.getIdRegional(),
                conta.getIdFilial(),
                conta.getIdPontoRelacionamento());
        if (!contaVO.isEmpty()) {
          Credencial credencialDestino =
              credencialService.findByIdCredencial(contaVO.get(0).getIdCredencial());
          JcardResponse sucesso =
              realizarTransferenciaEntreContas(
                  conta.getIdInstituicao(),
                  conta.getIdCredencial(),
                  credencialDestino,
                  conta.getSaldoDisponivel());
          if (!sucesso.getSuccess()) {
            throw new GenericServiceException(ConstantesErro.TRANSFERENCIA_SALDO.getMensagem());
          }
        }
      }
    }
  }

  public CorporativoResponsavelCredencial findResponsavelCredencial(Long idResponsavel) {
    List<CorporativoResponsavelCredencial> responsavelCredenciais =
        corporativoResponsavelCredencialRepository.findByIdResponsavelAndDtHrFimIsNull(
            idResponsavel);
    if (responsavelCredenciais.isEmpty()) {
      return null;
    }
    return responsavelCredenciais.get(0);
  }
}
