package br.com.entity.transacional;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "carga_brb_api", schema = "transacional")
@Getter
@Setter
@NoArgsConstructor
@SequenceGenerator(
    name = "carga_brb_api_id_seq",
    sequenceName = "transacional.carga_brb_api_id_seq",
    initialValue = 1,
    allocationSize = 1)
public class CargaBRBApi implements Serializable {

  private static final long serialVersionUID = 160595457907008412L;

  @Id
  @Column(name = "id")
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "carga_brb_api_id_seq")
  private Long id;

  @Column(name = "id_conta")
  private Long idConta;

  @Column(name = "token_interno")
  private String tokenInterno;

  @Column(name = "dt_hr_transacao")
  private LocalDateTime dataHoraLancamento;

  @Column(name = "rrn")
  private String rrn;

  @Column(name = "rrn_estornado")
  private String rrnEstornado;

  @Column(name = "valor")
  private BigDecimal valor;

  @Column(name = "status")
  private Integer status;

  @Column(name = "txt_extrato")
  private String textoExtrato;

  @Column(name = "cod_transacao")
  private Integer codTransacao;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(
      name = "id_conta",
      referencedColumnName = "id_conta",
      insertable = false,
      updatable = false)
  private ContaPagamento contaPagamento;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(
      name = "token_interno",
      referencedColumnName = "token_interno",
      insertable = false,
      updatable = false)
  private Credencial credencial;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(
      name = "cod_transacao",
      referencedColumnName = "cod_transacao",
      insertable = false,
      updatable = false)
  private CodigoTransacao codigoTransacao;

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }
}
