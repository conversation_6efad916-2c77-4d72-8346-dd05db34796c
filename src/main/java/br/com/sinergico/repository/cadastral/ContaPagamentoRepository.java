package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Pessoa;
import br.com.json.bean.cadastral.PortadorCartaoPreEmitidoTO;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.vo.ContaPagamentoCadastroAvanceVO;
import br.com.sinergico.vo.ContaPagamentoCafVO;
import br.com.sinergico.vo.ContaPagamentoIntegracaoVO;
import br.com.sinergico.vo.ContaPagamentoVO;
import br.com.sinergico.vo.ContasPorProdutoPortador;
import br.com.sinergico.vo.ExisteMatriculaEmpresaVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.EntityGraph.EntityGraphType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ContaPagamentoRepository
    extends JpaRepository<ContaPagamento, Long>, ContaPagamentoRepositoryCustom {

  String PESSOA_FISICA = "1";
  String PESSOA_JURIDICA = "2";

  @EntityGraph(value = "ContaPagamento.contas", type = EntityGraphType.LOAD)
  ContaPagamento findOneByIdProcessadoraAndIdInstituicaoAndIdContaPagamento(
      Integer idProcessadora, Integer idInstituicao, String idContaPagamento);

  ContaPagamento findOneByIdContaAndIdRelacionamento(Long idConta, Integer idRelacionamento);

  @Query(
      "select cpg from ContaPagamento cpg inner join cpg.contasPessoa cp inner join cpg.produtoInstituicao pin "
          + "where cp.idPessoa = :idPessoa and pin.b2b = true")
  List<ContaPagamento> findByContasPessoaIdPessoa(@Param("idPessoa") Long idPessoa);

  @Query(
      "select cpg from ContaPagamento cpg inner join cpg.contasPessoa cp "
          + "where cp.idPessoa = :idPessoa ")
  List<ContaPagamento> findByIdPessoa(@Param("idPessoa") Long idPessoa);

  @Query(
      "select distinct new br.com.sinergico.vo.ContaPagamentoVO(cpg.idConta, cpg.idProcessadora, cpg.idInstituicao,cpg.idRegional, cpg.idFilial, cpg.idPontoDeRelacionamento, cpg.idProdutoInstituicao) "
          + " from ContaPagamento cpg "
          + " inner join cpg.contasPessoa cp "
          + " inner join cp.pessoa p "
          + " inner join cpg.tipoStatus ts "
          + " where cpg.idInstituicao = :idInstituicao and p.documento = :documento and ts.idGrupoStatus <> :idGrupoStatus ")
  List<ContaPagamentoVO> findIdContaByIdInstituicaoAndDocumentoAndTipoStatus_idTipoStatusIsNot(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("idGrupoStatus") Integer idGrupoStatus);

  List<ContaPagamento> findByContasPessoaIdPessoaAndIdProdutoInstituicao(
      Long idPessoa, Integer idProdutoInstituicao);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = 1 and cp.pessoa.documento = :cpf and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao")
  List<ContaPagamento> findByCpf(
      @Param("cpf") String cpf,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      "select cpag from ContaPagamento cpag"
          + " inner join cpag.contasPessoa cp "
          + " inner join cp.pessoa p "
          + " inner join cpag.tipoStatus ts "
          + " where cpag.idInstituicao = :idInstituicao and cpag.idProdutoInstituicao = :idProdutoInstituicao and ts.idGrupoStatus = :idGrupoStatus "
          + " and  cpag.idConta in ( select max(cpag1.idConta) from ContaPagamento cpag1 "
          + " inner join cpag1.contasPessoa cp1  "
          + " inner join cp1.pessoa p1 "
          + " inner join cpag1.tipoStatus ts1"
          + " where cpag1.idInstituicao = :idInstituicao and cpag1.idProdutoInstituicao = :idProdutoInstituicao and ts1.idGrupoStatus = :idGrupoStatus"
          + " group by p1.documento)")
  List<ContaPagamento> findByIdInstituicaoAndIdProdutoInstituicaoAndStatus(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("idGrupoStatus") Integer idGrupoStatus);

  @Query(
      "  select cpag from ContaPagamento cpag "
          + " inner join cpag.contasPessoa cp "
          + " inner join cpag.tipoStatus ts "
          + " where cp.pessoa.idTipoPessoa = :tipoPessoa "
          + " and cp.pessoa.documento = :cpf "
          + " and cpag.idProcessadora = :idProcessadora "
          + " and cpag.idInstituicao = :idInstituicao "
          + " and ts.idGrupoStatus = :idGrupoStatus")
  List<ContaPagamento> findByCpfAndTipoStatus_idGrupoStatus(
      @Param("cpf") String cpf,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa,
      @Param("idGrupoStatus") Integer idGrupoStatus);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = :idTipoPessoa and cp.pessoa.documento = :cpf and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao")
  List<ContaPagamento> findByCpf(
      @Param("cpf") String cpf,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idTipoPessoa") Integer idTipoPessoa);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = :idTipoPessoa and cp.pessoa.documento = :cpf and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao and cpag.idStatusConta != 31")
  List<ContaPagamento> findByCpfAtivos(
      @Param("cpf") String cpf,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idTipoPessoa") Integer idTipoPessoa);

  @Query(
      "select cpag from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.pessoa.idTipoPessoa = 1 "
          + "and cp.pessoa.documento = :cpf "
          + "and cpag.idProdutoInstituicao = :idProdutoInstituicao "
          + "and cpag.idProcessadora = :idProcessadora "
          + "and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional "
          + "and cpag.idFilial = :idFilial "
          + "and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento "
          + "and cpag.idStatusConta != 9")
  ContaPagamento findByCpfAndProdutoAndHierarquia(
      @Param("cpf") String cpf,
      @Param("idProdutoInstituicao") Integer idProdutoInsituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  @Query(
      "select cpag from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.pessoa.documento = :cpf "
          + "and cpag.idProcessadora = :idProcessadora "
          + "and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional "
          + "and cpag.idFilial = :idFilial "
          + "and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento "
          + "and cpag.idStatusConta != 9")
  List<ContaPagamento> findByCpfAndHierarquia(
      @Param("cpf") String cpf,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = :tipoPessoa and cp.pessoa.documento = :cpf and cpag.idProdutoInstituicao = :idProdutoInstituicao ")
  ContaPagamento findByCpfAndProduto(
      @Param("cpf") String cpf,
      @Param("idProdutoInstituicao") Integer idProdutoInsituicao,
      @Param("tipoPessoa") Integer tipoPessoa);

  @Query(
      "select cp.pessoa.documento from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = 1 and cp.pessoa.documento in :linhasCpf and cpag.idProdutoInstituicao = :idProdutoInstituicao ")
  List<String> findByListaCpfAndProduto(
      @Param("linhasCpf") Set<String> linhasCpf,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = 1 and cp.pessoa.documento in :linhasCpf and cpag.idProdutoInstituicao = :idProdutoInstituicao ")
  List<ContaPagamento> findContasPagamentoByListaCpfAndProduto(
      @Param("linhasCpf") List<String> linhasCpf,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao);

  @Query(
      "select cpag from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.pessoa.idTipoPessoa = :tipoDoc "
          + "and cp.pessoa.documento = :doc "
          + "and cpag.idProdutoInstituicao = :idProdutoInstituicao "
          + "and cpag.idProcessadora = :idProcessadora "
          + "and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional "
          + "and cpag.idFilial = :idFilial "
          + "and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento "
          + "and cpag.idStatusConta != 9")
  ContaPagamento findByTipoDocAndDocAndProduto(
      @Param("doc") String doc,
      @Param("idProdutoInstituicao") Integer idProdutoInsituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @Param("tipoDoc") Integer tipoDoc);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = 1 and cp.pessoa.matricula = :matricula and cpag.idProdutoInstituicao = :idProdutoInstituicao and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao and cpag.idRegional = :idRegional and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento")
  List<ContaPagamento> findByMatriculaAndProduto(
      @Param("matricula") String matricula,
      @Param("idProdutoInstituicao") Integer idProdutoInsituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  ContaPagamento findByContasPessoaIdPessoaAndContasPessoaIdTitularidadeAndIdInstituicao(
      Long idPessoa, Integer idTitularidade, Integer idInstituicao);

  @Query(
      "select new br.com.sinergico.vo.ExisteMatriculaEmpresaVO(cp.pessoa.matricula, cp.pessoa.documento) from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.idTipoPessoa = 1 and cp.pessoa.matricula = :matricula and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao and cpag.idRegional = :idRegional and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento")
  List<ExisteMatriculaEmpresaVO> findByMatriculaExisteEmpresa(
      @Param("matricula") String matricula,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  ContaPagamento findOneByIdConta(Long idConta);

  @Query(
      "select count(cpag.idConta) "
          + "from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.pessoa.documento = :documento "
          + "and cpag.idProdutoInstituicao = :idProdInstituicao "
          + "and cpag.hierarquiaPontoDeRelacionamento.b2b = false")
  Integer existeContaWithCpfAndProduto(
      @Param("documento") String documento, @Param("idProdInstituicao") Integer idProdInstituicao);

  @Query(
      "select cpag from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.idTitularidade = 1 "
          + "and cpag.idConta = :idConta")
  ContaPagamento findContaTitular(@Param("idConta") Long idConta);

  @Query(
      "select p from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "inner join cp.pessoa p "
          + "where cp.idTitularidade = 1 "
          + "and cpag.idConta = :idConta")
  Pessoa findPessoaTitularDaConta(@Param("idConta") Long idConta);

  @Query(
      "select cpag "
          + "from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.pessoa.documento = :documento "
          + "and cp.pessoa.idTipoPessoa = :idTipoPessoa and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + "and cpag.hierarquiaPontoDeRelacionamento.b2b = false ")
  List<ContaPagamento> findByProdutoNotB2b(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idTipoPessoa") Integer idTipoPessoa,
      @Param("documento") String documento);

  @Query(
      "select c from ContaPagamento c inner join c.contasPessoa contasPessoa "
          + "where contasPessoa.pessoa.idPessoa = :idPessoa "
          + "and c.idProdutoInstituicao = :idProdutoInstituicao "
          + "and c.tipoStatus.idGrupoStatus <> :idGrupoStatus")
  ContaPagamento
      findByContasPessoaPessoaIdPessoaAndIdProdutoInstituicaoAndTipoStatus_IdGrupoStatusIsNot(
          @Param("idPessoa") Long idPessoa,
          @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
          @Param("idGrupoStatus") Integer idGrupoStatus);

  @Query(
      "select count(cpag.idConta) from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.documento = :documento "
          + "and cp.pessoa.idTipoPessoa = :idTipoPessoa and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + "and cpag.produtoInstituicao.idProdInstituicao = :idProdutoInstituicao and cpag.hierarquiaPontoDeRelacionamento.b2b = :isB2B ")
  Integer countConta(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("idTipoPessoa") Integer idTipoPessoa,
      @Param("documento") String documento,
      @Param("isB2B") boolean isB2B);

  @Query(
      "select count(*) from ContaPagamento cpag inner join cpag.contasPessoa cp where cp.pessoa.documento = :documento "
          + "and cp.pessoa.idTipoPessoa = :idTipoPessoa and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + "and cpag.hierarquiaPontoDeRelacionamento.b2b = false ")
  Integer countByProdutoNotB2b(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idTipoPessoa") Integer idTipoPessoa,
      @Param("documento") String documento);

  @Query(
      "select sum(cpag.limiteUnico) from ContaPagamento cpag "
          + " inner join cpag.tipoStatus ts "
          + " where cpag.idProdutoPlataforma = 4"
          + " and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + " and cpag.idRegional = :idRegional and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento "
          + " and ts.idGrupoStatus != 9 ")
  BigDecimal findSomaLimiteGlobalEmpresaConvenio(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  @Query(
      "SELECT new br.com.json.bean.cadastral.PortadorCartaoPreEmitidoTO(c.idCredencialExterna, p.documento, p.nomeCompleto, p.idBanco, p.idAgencia, p.contaBancariaX, c.idCredencial) "
          + "from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "inner join cp.pessoa p "
          + "inner join cpag.credenciais c "
          + "inner join cpag.produtoInstituicao pi "
          + "where pi.idProdInstituicao = :idProdInstituicao and c.idContaPagamento = cpag.idContaPagamento "
          + "and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoRelacionamento "
          + "and c.idCredencialExterna is not null and c.dataHoraEmitido is null")
  List<PortadorCartaoPreEmitidoTO> existeContaPagamentoParaPreEmissaoExterna(
      @Param("idProdInstituicao") Integer idProdInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "SELECT count(cpag)"
          + "from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "inner join cp.pessoa p "
          + "inner join cpag.credenciais c "
          + "inner join cpag.produtoInstituicao pi "
          + "where pi.idProdInstituicao = :idProdInstituicao and c.idContaPagamento = cpag.idContaPagamento and c.idCredencialExterna is not null and c.dataHoraEmitido is null "
          + "and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoRelacionamento")
  Integer countContaPagamentoParaPreEmissaoExterna(
      @Param("idProdInstituicao") Integer idProdInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "select cpag from ContaPagamento cpag inner join cpag.contasPessoa cp "
          + "where cp.pessoa.idTipoPessoa = 2 and cp.pessoa.documento = :doc and cpag.idProdutoInstituicao = :idProdutoInstituicao "
          + "and cpag.idProcessadora = :idProcessadora and cpag.idInstituicao = :idInstituicao and cpag.idRegional = :idRegional "
          + "and cpag.idFilial = :idFilial and cpag.idPontoDeRelacionamento = :idPontoDeRelacionamento and cpag.nroPedidoVoucherPapel is not null")
  List<ContaPagamento> findBydocAndProdutoAndHierarquiaAndTipoJuridicaComVoucherPapel(
      @Param("doc") String doc,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  @Query(
      "select cpg from ContaPagamento c"
          + "pg inner join cpg.contasPessoa cp inner join cp.pessoa p "
          + "where cp.idTitularidade = 1 and cpg.idProdutoPlataforma = 4 and cpg.idPontoDeRelacionamento = :idPontoRelacionamento"
          + " and p.documento = :documento and p.matricula = :matricula and cpg.idFilial = :idFilial and cpg.idProdutoInstituicao = :idProdutoInstituicao ")
  List<ContaPagamento> findByContaCargaLimite(
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento,
      @Param("documento") String documento,
      @Param("matricula") String matricula,
      @Param("idFilial") Integer idFilial,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao);

  @Query(
      value =
          "select count(c.*) from cadastral.conta_pagamento c "
              + "inner join cadastral.conta_pessoa cp on cp.id_conta = c.id_conta "
              + "inner join cadastral.pessoa p on p.id_pessoa = cp.id_pessoa "
              + "where cp.id_titularidade = 1 "
              + "and c.id_processadora = :idProcessadora "
              + "and c.id_instituicao = :idInstituicao "
              + "and c.id_regional = :idRegional "
              + "and c.id_filial = :idFilial "
              + "and c.id_ponto_de_relacionamento = :idPontoDeRelacionamento "
              + "and p.documento = :documento "
              + "and c.id_prod_instituicao = :idProdutoInstituicao ",
      nativeQuery = true)
  Integer isContaPagamentoExisteByHierarquiaAndDocumentoAndProdutoAndTitularidade(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @Param("documento") String documento,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao);

  @Query(
      " select new br.com.sinergico.vo.ContasPorProdutoPortador(cp.idProdutoInstituicao, pi.descProdInstituicao, hpdr.descricao, p.idPessoa, p.telefoneCelular, cp.idConta) "
          + " from ContaPagamento as cp"
          + " inner join cp.contasPessoa as cp2 "
          + " inner join cp2.pessoa as p "
          + " inner join cp.produtoInstituicao as pi "
          + " inner join cp.hierarquiaPontoDeRelacionamento as hpdr "
          + " where p.documento = :documento "
          + " and p.idInstituicao = :idInstituicao "
          + " and p.idProcessadora = :idProcessadora "
          + " and cp.idStatusConta in ( :idStatus ) "
          + " and cp2.idTitularidade = 1 ")
  List<ContasPorProdutoPortador> findContasPagamentosByDocumentoAndInstituicaoAndStatusIn(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("idStatus") Collection<Integer> idStatus);

  List<ContaPagamento> findByIdContaIn(Collection<Long> idContas);

  @Query(
      value =
          "select c.id_conta_corresp from cadastral.conta_pagamento c where c.id_conta=:idConta ",
      nativeQuery = true)
  Long findIdContaCorresp(@Param("idConta") Long idConta);

  ContaPagamento findByIdConta(Long idConta);

  @Query(
      "select new br.com.sinergico.vo.ContaPagamentoCadastroAvanceVO(c.idConta, c.idProdutoInstituicao, p.documento, p.nomeCompleto, sf.descSetorFilial) "
          + " from ContaPagamento as c "
          + " inner join c.contasPessoa cp "
          + " inner join cp.pessoa p "
          + " inner join p.setorFilial sf "
          + " where c.idProdutoInstituicao = :idProdInstituicao "
          + " and c.idPontoDeRelacionamento = :idPontoRelacionamento "
          + " and c.idStatusConta = 1 "
          + " order by p.nomeCompleto asc ")
  List<ContaPagamentoCadastroAvanceVO> findContaByIdProdInstituicao(
      @Param("idProdInstituicao") Integer idProdInstituicao,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "Select distinct c From ContaPagamento c "
          + "inner join c.contasPessoa cp "
          + "inner join cp.pessoa p "
          + "where p.idInstituicao = :idInstituicao "
          + "and p.documento = :documento "
          + "and c.idConta = :idConta ")
  Optional<ContaPagamento> findByIdInstituicaoAndAndIdContaAndDocumento(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idConta") Long idConta,
      @Param("documento") String documento);

  @Query(
      "select new br.com.sinergico.vo.ContaPagamentoIntegracaoVO(cpag.idConta, cpag.idProcessadora, cpag.idInstituicao, cpag.idContaCorresp, cpag.dataHoraReplicacao, cpag.dataHoraConfirmaReplica, p.documento, p.idPessoa) "
          + "from ContaPagamento as cpag "
          + "left outer join cpag.contasPessoa as cp "
          + "left outer join cp.pessoa as p "
          + "where p.documento = :documento and cp.idTitularidade = 1 "
          + "and p.idInstituicao = :idInstituicao "
          + "and cpag.dataHoraReplicacao is not null")
  List<ContaPagamentoIntegracaoVO> findDadosContaByDataHoraReplicacao(
      @Param("idInstituicao") Integer idInstituicao, @Param("documento") String documento);

  @Query(
      "select cpag "
          + "from ContaPagamento as cpag "
          + "inner join cpag.contasPessoa as cp "
          + "inner join cp.pessoa as p "
          + "where p.documento = :documento and cp.idTitularidade = 1 "
          + "and p.idInstituicao = :idInstituicao "
          + "and cpag.dataHoraReplicacao is not null")
  List<ContaPagamento> findDadosContaByIdPessoa(
      @Param("documento") String documento, @Param("idInstituicao") Integer idInstituicao);

  @Query(
      "select c "
          + "from EnderecoPessoa ep "
          + "inner join ep.pessoa p "
          + "inner join p.contasPessoa cp "
          + "inner join cp.contaPagamento c "
          + "where ep.idEndereco in :idEndereco ")
  List<ContaPagamento> findContasByIdEndereco(@Param("idEndereco") Long idEndereco);

  @Query(
      value =
          " select exists ( "
              + " 	SELECT 1 "
              + " 	FROM cadastral.conta_pagamento cpag "
              + " 	inner join cadastral.conta_pessoa cp on cpag.id_conta = cp.id_conta and cp.id_titularidade = 1 "
              + " 	inner join cadastral.pessoa p on cp.id_pessoa = p.id_pessoa "
              + " 	where cpag.id_prod_instituicao = :idProdutoInstituicao "
              + " 		and cpag.id_processadora = :idProcessadora "
              + " 		and cpag.id_instituicao = :idInstituicao "
              + " 		and cpag.id_regional = :idRegional "
              + " 		and cpag.id_filial = :idFilial "
              + " 		and cpag.id_ponto_de_relacionamento = :idPontoDeRelacionamento "
              + " 		and p.documento = :documento "
              + " 		and cpag.id_conta_corresp is not null "
              + " ) ",
      nativeQuery = true)
  Boolean findIfExistsContaCorresp(
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @Param("documento") String documento);

  @Query(
      value =
          "select cp.* from cadastral.conta_pagamento cp "
              + "inner join cadastral.conta_pessoa cpp on cpp.id_conta = cp.id_conta and cpp.id_titularidade = 1 "
              + "inner join cadastral.pessoa p on p.id_pessoa = cpp.id_pessoa "
              + "inner join suporte.hierarquia_ponto_de_relacionamento hpdr on "
              + "(hpdr.id_processadora , hpdr.id_instituicao, hpdr.id_regional, hpdr.id_filial, hpdr.id_ponto_de_relacionamento) "
              + "= (cp.id_processadora , cp.id_instituicao, cp.id_regional, cp.id_filial, cp.id_ponto_de_relacionamento) "
              + "where cp.id_processadora = :idProcessadora "
              + "and cp.id_instituicao = :idInstituicao "
              + "and cp.id_regional = :idRegional "
              + "and cp.id_filial = :idFilial "
              + "and cp.id_ponto_de_relacionamento = :idPontoRelacionamento "
              + "and cp.id_prod_instituicao = :idProdInstituicao "
              + "and p.documento = :documento ",
      nativeQuery = true)
  List<ContaPagamento> findContasByDocumento(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento,
      @Param("idProdInstituicao") Integer idProdInstituicao,
      @Param("documento") String documento);

  @Query(
      value =
          "select "
              + " distinct on "
              + " (p.id_pessoa)  "
              + "cpp.id_pessoa , "
              + " cpp.id_conta , "
              + " cp.id_processadora , "
              + " cp.id_instituicao , "
              + " p.documento , "
              + " p.nome_completo , "
              + " pprp.bl_ativo , "
              + " pprp.id_empresa_parceira_instituicao "
              + "from "
              + " cadastral.parceira_empresa pe "
              + "inner join cadastral.parceira_ponto_relacionamento ppr on "
              + " pe.id = ppr.id_empresa_parceira "
              + "inner join cadastral.conta_pagamento cp on "
              + " cp.id_processadora = ppr.id_processadora "
              + " and cp.id_instituicao = ppr.id_instituicao "
              + " and cp.id_regional = ppr.id_regional "
              + " and cp.id_filial = ppr.id_filial "
              + " and cp.id_ponto_de_relacionamento = ppr.id_ponto_de_relacionamento "
              + " and cp.id_status_conta = 1 "
              + "inner join cadastral.conta_pessoa cpp on "
              + " cpp.id_conta = cp.id_conta "
              + " and cpp.id_titularidade = 1 "
              + "inner join cadastral.pessoa p on "
              + " p.id_pessoa = cpp.id_pessoa "
              + "left join cadastral.parceira_ponto_relacionamento_pessoa pprp on "
              + " pprp.id_pessoa = p.id_pessoa "
              + " and pprp.id_empresa_parceira_instituicao = :idEmpresaParceiraIntituicao "
              + "where "
              + " true "
              + " and cp.id_processadora = 10 "
              + " and cp.id_instituicao = :idInstituicao "
              + " and cp.id_regional = :idRegional "
              + " and cp.id_filial = :idFilial "
              + " and cp.id_ponto_de_relacionamento = :idPontoDeRelacionamento "
              + " and pe.id = :idEmpresaParceira "
              + " and ppr.id = :idEmpresaParceiraIntituicao "
              + " and ppr.bl_ativo = true "
              + " order by "
              + " p.id_pessoa ",
      nativeQuery = true)
  List<Object[]> findContasByPontoRelacionamento(
      @Param("idPontoDeRelacionamento") Integer idPontoRelacionamento,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idEmpresaParceira") Integer idEmpresaParceira,
      @Param("idEmpresaParceiraIntituicao") Integer idEmpresaParceiraIntituicao);

  @Query(
      value =
          "select cp.* from suporte.notificacao_grupo_contas ngc "
              + "inner join cadastral.conta_pagamento cp on ngc.id_conta = cp.id_conta "
              + "where cp.id_instituicao = :idInstituicao "
              + "and cp.id_status_conta = :status "
              + "and ngc.id_grupo = :idGrupo ",
      nativeQuery = true)
  List<ContaPagamento> findContasByIdInstituicaoAndStatusAndIdGrupo(
      @Param("idInstituicao") Integer idInstituicao,
      @Param("status") Integer status,
      @Param("idGrupo") Long idGrupo);

  ContaPagamento findByIdAcct(Long idAcct);

  @Query(
      " select new br.com.sinergico.vo.ContaPagamentoCafVO(cpag.idConta, cpag.idProcessadora, cpag.idInstituicao, p.documento, p.idPessoa, tp.tipoPessoa) "
          + " from ContaPagamento as cpag "
          + " inner join cpag.contasPessoa as cp "
          + " inner join cp.pessoa as p "
          + " inner join p.tipoPessoa as tp "
          + " where p.documento = :documento "
          + " and cp.idTitularidade = 1 "
          + " and cpag.idInstituicao = :idInstituicao "
          + " and p.idInstituicao = cpag.idInstituicao "
          + " and p.idPessoa = cp.idPessoa "
          + " and cp.idConta = cpag.idConta ")
  List<ContaPagamentoCafVO> findDadosContaByInstituicaoAndDocumento(
      @Param("idInstituicao") Integer idInstituicao, @Param("documento") String documento);

  ContaPagamento findByIdAccountCode(String code);

  @Query(
      "Select c From ContaPagamento c "
          + "inner join c.contasPessoa cp "
          + "inner join cp.pessoa p "
          + "inner join c.produtoInstituicao pi "
          + "inner join pi.produtosInstituicaoGrupoProdutos pigp "
          + "inner join pi.produtoInstituicaoConfiguracao pic "
          + "where c.idProcessadora = :idProcessadora "
          + "and c.idInstituicao = :idInstituicao "
          + "and c.idRegional = :idRegional "
          + "and c.idFilial = :idFilial "
          + "and c.idPontoDeRelacionamento = :idPontoDeRelacionamento "
          + "and p.documento = :documento "
          + "and p.idTipoPessoa = :idTipoPessoa "
          + "and pigp.produtoInstituicaoGrupoProdutosId.idGrupo = :grupoAcesso "
          + "and pic.tipoProduto =:tipoProduto "
          + "and c.idStatusConta = 1 "
          + "and cp.idTitularidade = 1 ")
  ContaPagamento findContasPagamentosComGrupoAcessoETipoProduto(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @Param("documento") String documento,
      @Param("idTipoPessoa") Integer idTipoPessoa,
      @Param("grupoAcesso") Long grupoAcesso,
      @Param("tipoProduto") TipoProdutoEnum tipoProduto);

  @Query(
      value =
          "SELECT "
              + "  pi2.desc_prod_instituicao "
              + "  , cpag.id_conta "
              + "  , p2.documento as doccnpj "
              + "  , p2.razao_social "
              + "  , p2.data_fundacao "
              + "  , p.nome_completo "
              + "  , p.data_nascimento "
              + "  , p.documento as doccpf"
              + "  , CAST(p.ddd_tel_celular AS varchar) || CAST(p.tel_celular AS varchar) as tel"
              + "  , p.email "
              + "  , p.nome_mae "
              + "  , p.cep_representante_legal "
              + "  , p.logradouro_representante_legal "
              + "  , p.numero_representante_legal "
              + "  , p.bairro_representante_legal "
              + "  , p.cidade_representante_legal "
              + "  , p.complemento_representante_legal "
              + "  , p.uf_representante_legal "
              + "  , c.dt_hr_inclusao as datacadastroadicionalconta "
              + "  , CONCAT(c.bin_6, '-', c.ultimos_4_dig) "
              + "  , c.dt_hr_emitido as dataemissao "
              + "  , c.dt_validade "
              + "  , ts.desc_status "
              + "  , c.dt_hr_status "
              + "FROM cadastral.credencial c "
              + "INNER JOIN cadastral.credencial_conta cc ON cc.id_credencial = c.id_credencial "
              + "INNER JOIN cadastral.conta_pagamento cpag ON cpag.id_conta = cc.id_conta "
              + "INNER JOIN cadastral.conta_pessoa cp ON cp.id_conta = cpag.id_conta "
              + "  AND cp.id_titularidade = 2 "
              + "  AND c.id_pessoa = cp.id_pessoa "
              + "INNER JOIN cadastral.pessoa p ON p.id_pessoa = cp.id_pessoa "
              + "INNER JOIN cadastral.conta_pessoa cp2 ON cpag.id_conta = cp2.id_conta"
              + "  AND cp2.id_titularidade = 1 "
              + "INNER JOIN cadastral.pessoa p2 ON p2.id_pessoa = cp2.id_pessoa "
              + "LEFT JOIN suporte.tipo_status ts ON ts.id_status = c.status "
              + "INNER JOIN cadastral.produto_instituicao pi2 ON pi2.id_prod_instituicao = cpag.id_prod_instituicao "
              + "WHERE cpag.id_prod_instituicao = :idProdutoInstituicao "
              + " AND c.status in (0, 1, 5)"
              + " AND pi2.id_instituicao = :idInstituicao"
              + " AND p.dt_hr_inclusao >= :dataCadastroAdicional"
              + " AND p.dt_hr_inclusao <= :dataCadastroAdicionalFinal"
              + " order by cpag.id_prod_instituicao , p2.documento , p.documento",
      nativeQuery = true)
  List<Object[]> buscarAdicionalProdutoPorCredencialEProdutoContratado(
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("dataCadastroAdicional") LocalDateTime dataCadastroAdicional,
      @Param("dataCadastroAdicionalFinal") LocalDateTime dataCadastroAdicionalFinal);

  @Query(
      "select cpag "
          + "from ContaPagamento cpag "
          + "inner join cpag.contasPessoa cp "
          + "where cp.pessoa.idTipoPessoa = 2 "
          + "and cpag.idProcessadora = :idProcessadora "
          + "and cpag.idInstituicao = :idInstituicao "
          + "and cpag.idRegional = :idRegional "
          + "and cpag.idFilial = :idFilial "
          + "and cpag.idPontoDeRelacionamento = :idPontoRelacionamento "
          + "and cpag.idStatusConta = 1 "
          + "order by cpag.dataHoraInclusao asc ")
  List<ContaPagamento> buscarContasPagamentoPorDocumentoEInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);
}
