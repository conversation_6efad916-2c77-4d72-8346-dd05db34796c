package br.com.sinergico.facade.gatewaypagto;

import br.com.client.rest.gatewaypagto.json.bean.ConsultarContasPagasPorPeriodoResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarLinhaDigitavelTitulo;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarLinhaDigitavelTituloResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarOperadoraPorDDDResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarRecargasPorPeriodoResponse;
import br.com.client.rest.gatewaypagto.json.bean.ConsultarValorOperadoraResponse;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTitulo;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTituloBRB;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTituloBRBResponse;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarPagamentoTituloResponse;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarRecargaRequest;
import br.com.client.rest.gatewaypagto.json.bean.EfetuarRecargaResponse;
import br.com.client.rest.gatewaypagto.json.bean.GatewayPagtoResponse;
import br.com.client.rest.gatewaypagto.json.bean.LogPagtoTituloTransacaoResponse;
import br.com.client.rest.gatewaypagto.json.bean.LogRecargaTransacaoResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.gatewaypagto.ContratoGatewayPagto;
import br.com.entity.gatewaypagto.ContratoGatewayPagtoInstTransacaoConfig;
import br.com.entity.gatewaypagto.DDDLocalidade;
import br.com.entity.gatewaypagto.DDDOperadora;
import br.com.entity.gatewaypagto.LogPagtoTituloTransacao;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.gatewaypagto.LogRecargaTransacao;
import br.com.entity.suporte.AntifraudeCafPortador;
import br.com.entity.suporte.ParametroProcessamentoSistema;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.celcoin.CelcoinService;
import br.com.sinergico.enums.AntifraudeCafPortadorStatusEnum;
import br.com.sinergico.enums.LimiteTransacaoTipoEnum;
import br.com.sinergico.enums.TipoOperacaoEnum;
import br.com.sinergico.facade.transacional.ControleGarantiaFacade;
import br.com.sinergico.repository.gatewaypagto.ContratoGatewayPagtoTituloInsituicaoRepository;
import br.com.sinergico.repository.gatewaypagto.ContratoGatewayPagtoTituloRepository;
import br.com.sinergico.repository.gatewaypagto.DDDLocalidadeRepository;
import br.com.sinergico.repository.gatewaypagto.DDDOperadoraRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloTransacaoRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.repository.suporte.AntifraudeCafPortadorRepository;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.LimitesContaService;
import br.com.sinergico.service.gatewaypagto.GatewayPagtoExternoService;
import br.com.sinergico.service.suporte.CondicaoResgateService;
import br.com.sinergico.service.suporte.MoedaContaService;
import br.com.sinergico.service.suporte.ParametroProcessamentoSistemaService;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.Bank;
import br.com.sinergico.vo.FindProvidersDto;
import br.com.sinergico.vo.StatusFindProvidersDto;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GatewayPagamentoExternoFacade {

  private static final int MAX_TEL = 9;
  private static final int MIN_TEL = 8;
  private static final int DIAS30 = 30;
  private static final int MAX_LINHA_DIG = 48;
  private static final int OP_PAGTO_CONTA = 1;
  private static final Integer STATUS_ATIVO = 1;
  private static final Integer OP_RECARGA = 2;
  private static final Integer OP_VOUCHER = 3;
  private static final int ID_CAT_VOUCHER = 2;
  private static final int ID_CAT_RECARGA = 1;
  private static final String PGTO_CTA = "PGTO_CTA";

  @Autowired private ContaPagamentoService contaService;

  @Autowired private MoedaContaService moedaContaService;

  @Autowired private ControleGarantiaFacade controleGarantiaFacade;

  @Autowired private GatewayPagtoExternoService gatewayPagtoExtService;

  @Autowired private LogPagtoTituloValidacaoRepository logPagtoValidacaoRepository;

  @Autowired private LogPagtoTituloTransacaoRepository logPagtoTransacaoRepository;

  @Autowired private ContratoGatewayPagtoTituloInsituicaoRepository contratoInstRepository;

  @Autowired private ContratoGatewayPagtoTituloRepository contratoRepository;

  @Autowired private DDDOperadoraRepository dddOperadoraRepository;

  @Autowired private DDDLocalidadeRepository dddLocalidadeRepository;

  @Autowired private AntifraudeCafPortadorRepository antifraudeCafPortadorRepository;

  @Autowired private CondicaoResgateService condicaoResgateService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private ParametroProcessamentoSistemaService parametroProcessamentoSistemaService;

  @Autowired private LimitesContaService limitesContaService;

  @Autowired private CelcoinService celcoinService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private TravaServicosService travaServicosService;

  private final Logger logger = LoggerFactory.getLogger(GatewayPagamentoExternoFacade.class);

  public ConsultarLinhaDigitavelTituloResponse validarConsultarLinhaDigitavel(
      ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo, SecurityUser user) {
    ContaPagamento conta =
        contaService.findContaByHierarquia(consultarLinhaDigitavelTitulo.getIdConta(), user);
    return this.consultarLinhaDigitavel(
        consultarLinhaDigitavelTitulo, conta, Long.valueOf(user.getIdUsuario()));
  }

  public ConsultarLinhaDigitavelTituloResponse validarConsultarLinhaDigitavel(
      ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo,
      SecurityUserPortador userPortador) {
    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            consultarLinhaDigitavelTitulo.getIdConta(), userPortador);
    validarSeContaOnboardingClientePortador(conta, userPortador);
    return this.consultarLinhaDigitavel(
        consultarLinhaDigitavelTitulo,
        conta,
        Long.valueOf(Constantes.ID_USUARIO_INCLUSAO_PORTADOR));
  }

  public ConsultarLinhaDigitavelTituloResponse validarConsultarLinhaDigitavel(
      ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo,
      SecurityUserCorporativo userCorporativo) {
    ContaPagamento conta =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
            consultarLinhaDigitavelTitulo.getIdConta(), userCorporativo);
    return this.consultarLinhaDigitavel(
        consultarLinhaDigitavelTitulo,
        conta,
        Long.valueOf(Constantes.ID_USUARIO_INCLUSAO_PORTADOR));
  }

  public ConsultarLinhaDigitavelTituloResponse consultarLinhaDigitavel(
      ConsultarLinhaDigitavelTitulo consultarLinhaDigitavelTitulo,
      ContaPagamento conta,
      Long idUsuario) {

    ConsultarLinhaDigitavelTituloResponse resultado = new ConsultarLinhaDigitavelTituloResponse();

    try {
      validarLinhaDigitavel(consultarLinhaDigitavelTitulo);
      if (!Objects.isNull(conta)) {
        ParametroProcessamentoSistema parametroProcessamentoSistema =
            parametroProcessamentoSistemaService.findFirstByIdInstituicaoAndTipoParametro(
                conta.getIdInstituicao(), PGTO_CTA);

        if (!Objects.isNull(parametroProcessamentoSistema)) {
          if (!parametroProcessamentoSistema.getTexto().equals("AUTORIZADO")) {
            throw new GenericServiceException(
                ConstantesErro.BOL_HORARIO_PERMITIDO_PAGAMENTO.getMensagem());
          }
        }

        verificarPermissaoConta(conta);

        // buscar by idProc e idInst, se nao houver contrato para a instituicao ela utilizará a da
        // processadora
        ContratoGatewayPagtoInstTransacaoConfig contrato =
            getContratoAtivoInstOperacao(conta, OP_PAGTO_CONTA);
        ContratoGatewayPagto contratoUtilizado = getContratoUtilizado(contrato);

        resultado =
            gatewayPagtoExtService.consultarLinhaDigitavel(
                consultarLinhaDigitavelTitulo, conta, idUsuario, contratoUtilizado);
      } else {
        throw new GenericServiceException(ConstantesErro.CTP_CONTA_NAO_ENCONTRADA.getMensagem());
      }
    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  private void validarSeContaOnboardingClientePortador(
      ContaPagamento conta, SecurityUserPortador userPortador) {

    if (Objects.nonNull(conta)
        && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(conta.getIdInstituicao())) {
      AntifraudeCafPortador antifraudeCafPortador =
          this.antifraudeCafPortadorRepository.findFirstByTxDocumentoAndIdCafInstituicao(
              userPortador.getCpf(), conta.getIdInstituicao().longValue());

      if (Objects.isNull(antifraudeCafPortador)
          || (Objects.nonNull(antifraudeCafPortador.getIdStatus())
              && !antifraudeCafPortador
                  .getIdStatus()
                  .equals(AntifraudeCafPortadorStatusEnum.APPROVED.getStatus()))) {
        throw new GenericServiceException(
            "Consulta e Pagamento de Contas não permitida para o CPF.");
      }
    }
  }

  private void validarLinhaDigitavel(ConsultarLinhaDigitavelTitulo req) {
    if ((req.getLinhaDigitavel() == null || req.getLinhaDigitavel().trim().isEmpty())
        && (req.getCodigoBarras() == null || req.getCodigoBarras().trim().isEmpty())) {
      throw new GenericServiceException(
          "O Campo linha digitável ou Código de barras deve ser informado!");
    }
    if (req.getLinhaDigitavel() != null
        && !req.getLinhaDigitavel().trim().isEmpty()
        && req.getCodigoBarras() != null
        && !req.getCodigoBarras().trim().isEmpty()) {
      throw new GenericServiceException(
          "Favor preencher apenas um Campo, linha digitável ou Código de barras!");
    }

    if ((req.getLinhaDigitavel() != null && req.getLinhaDigitavel().length() > MAX_LINHA_DIG)
        || (req.getCodigoBarras() != null && req.getCodigoBarras().length() > MAX_LINHA_DIG)) {
      throw new GenericServiceException(
          "Verifique a linha digitável/Código de Barras informado.Ela deve ter no máximo 48 dígitos!");
    }
  }

  // TODO colocar um preenchimento do objeto generico de resposta
  private void tratarGenericServiceException(
      GenericServiceException e, GatewayPagtoResponse resultado) {
    resultado.setSucesso(false);
    resultado.setCodigoRetorno(-1);
    resultado.setMensagemErro(e.getMensagem());
    e.printStackTrace();
  }

  private void tratarException(Exception e, GatewayPagtoResponse resultado) {
    resultado.setSucesso(false);
    resultado.setCodigoRetorno(999);
    resultado.setMensagemErro("Ocorreu um erro interno: " + e.getMessage());
    e.printStackTrace();
  }

  private void verificarPermissaoConta(ContaPagamento conta) {
    if (!temPermissaoAcessarConta(conta)) {
      throw new GenericServiceException("Usuário sem permissão para efetuar a operação");
    }
  }

  private Map<String, Object> getContaAndUsuarioLogado(
      SecurityUser user, SecurityUserPortador userPortador, Long idConta) {

    Map<String, Object> params = new HashMap<>();

    // se o usuario nao for o proprio portador, busca pela conta e hierarquia
    if (user != null) {

      ContaPagamento conta = contaService.findContaByHierarquia(idConta, user);
      params.put("conta", conta);
      params.put("idUsuario", Long.valueOf(user.getIdUsuario()));
      params.put("isPortador", false);

      // se o usuario for o proprio portador, verifica se o idConta pertence a ele
    } else if (userPortador != null) {
      params.put("idUsuario", Long.valueOf(Constantes.ID_USUARIO_INCLUSAO_PORTADOR));

      List<ContaPagamento> contas =
          contaService.findByCpf(
              userPortador.getCpf(),
              userPortador.getIdProcessadora(),
              userPortador.getIdInstituicao(),
              userPortador.getIdTipoPessoa());
      for (ContaPagamento conta : contas) {
        if (conta.getIdConta().equals(idConta)) {
          params.put("conta", conta);
          params.put("isPortador", true);
        }
      }
    }

    return params;
  }

  // se a conta for diferente de null eh porque a pessoa possui permissao de acesso
  private boolean temPermissaoAcessarConta(ContaPagamento conta) {
    return conta != null;
  }

  public EfetuarPagamentoTituloResponse efetuarPagamentoTitulo(
      EfetuarPagamentoTitulo req, SecurityUser user, SecurityUserPortador userPortador) {

    if (controleGarantiaFacade
            .getInstituicao(user.getIdProcessadora(), user.getIdInstituicao())
            .getStatus()
        == 9) {
      throw new GenericServiceException("Serviço temporariamente indisponível para a instituição");
    }

    EfetuarPagamentoTituloResponse resultado = new EfetuarPagamentoTituloResponse();

    try {

      LogPagtoTituloValidacao logPagtoValidacao = getLogPagtoValidacao(req);

      // buscar conta para ve o usuário logado possui acesso
      Map<String, Object> params =
          getContaAndUsuarioLogado(user, userPortador, logPagtoValidacao.getIdConta());

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;

      Long idUsuario = params.get("idUsuario") != null ? (Long) params.get("idUsuario") : null;
      verificarPermissaoConta(conta);
      boolean isPortador =
          params.get("isPortador") != null ? (Boolean) params.get("isPortador") : false;

      GetSaldoConta saldoConta = null;
      GetSaldoConta saldoContaInst = null;

      String cpf = "";
      String nome = "";
      Integer tipoPessoa = null;
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao = null;
      ContratoGatewayPagto contratoUtilizado = null;
      ContaPagamento contaInstituicao = null;

      if (conta != null) {

        ParametroProcessamentoSistema parametroProcessamentoSistema =
            parametroProcessamentoSistemaService.findFirstByIdInstituicaoAndTipoParametro(
                conta.getIdInstituicao(), PGTO_CTA);

        if (!Objects.isNull(parametroProcessamentoSistema)) {
          if (!parametroProcessamentoSistema.getTexto().equals("AUTORIZADO")) {
            throw new GenericServiceException(
                "Pagamento de contas somente de segundas as sextas-feiras, dias úteis, das 7h às 20h.");
          }
        }

        Integer idMoeda = moedaContaService.getIdMoedaByIdProduto(conta.getIdProdutoInstituicao());

        if (idMoeda == null) {
          throw new GenericServiceException(
              ConstantesErro.CODIGO_MOEDA_NAO_ENCONTRADO_PARA_PRODUTO.getMensagem());
        }

        Pessoa pessoa = contaService.findPessoaTitularDaConta(conta.getIdConta());
        if (pessoa != null) {
          cpf = pessoa.getDocumento();
          nome = pessoa.getNomeCompleto();
          tipoPessoa = pessoa.getIdTipoPessoa();
        }
        saldoConta = getSaldoConta(conta);
        Boolean debitarInstituicao = Boolean.FALSE;
        //				 contratoInstituicao = getContratoAtivoInst(conta);
        contratoInstituicao = getContratoAtivoPorTipoTransacao(conta, TipoOperacaoEnum.PAGAMENTO);
        contratoUtilizado = getContratoUtilizado(contratoInstituicao);

        // verifico se eh para pagar a conta somente apos fazer o debito na instituicao
        if (contratoInstituicao.getDebitarGarantiaInst() != null
            && contratoInstituicao.getDebitarGarantiaInst()) {

          if (contratoInstituicao.getIdContaGarantiaInst() == null) {
            throw new GenericServiceException(
                "Não foi possivel efetuar operação.",
                "Contrato precisa ter o IdContaGarantiaInst configurada");
          }

          debitarInstituicao = true;
          contaInstituicao =
              contaService.findByIdNotNull(contratoInstituicao.getIdContaGarantiaInst());
          saldoContaInst = getSaldoConta(contaInstituicao);
        }
        validarMesmoUsuarioConsulta(idUsuario, logPagtoValidacao);
        validarDataVencimento(req, logPagtoValidacao);

        resultado =
            gatewayPagtoExtService.efetuarPagamentoTitulo(
                logPagtoValidacao,
                conta,
                idUsuario,
                req,
                cpf,
                nome,
                tipoPessoa,
                saldoConta,
                isPortador,
                contratoInstituicao,
                contratoUtilizado,
                contaInstituicao,
                saldoContaInst,
                idMoeda);

      } else {
        throw new GenericServiceException("Não foi possível encontrar a conta!");
      }

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  public EfetuarPagamentoTituloBRBResponse efetuarPagamentoTitulo(
      EfetuarPagamentoTituloBRB req, SecurityUserPortador userPortador) {
    EfetuarPagamentoTituloBRBResponse resultado = new EfetuarPagamentoTituloBRBResponse();

    try {

      LogPagtoTituloValidacao logPagtoValidacao = getLogPagtoValidacao(req);

      // buscar conta para ve o usuário logado possui acesso
      Map<String, Object> params =
          getContaAndUsuarioLogado(null, userPortador, logPagtoValidacao.getIdConta());

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      Long idUsuario = params.get("idUsuario") != null ? (Long) params.get("idUsuario") : null;
      verificarPermissaoConta(conta);
      boolean isPortador =
          params.get("isPortador") != null ? (Boolean) params.get("isPortador") : false;

      GetSaldoConta saldoConta = null;
      GetSaldoConta saldoContaInst = null;

      String cpf = "";
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao = null;
      ContratoGatewayPagto contratoUtilizado = null;
      ContaPagamento contaInstituicao = null;

      if (conta != null) {

        travaContasService.travaContas(conta.getIdConta(), Servicos.BOLETO);

        metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.BOLETO);

        Integer idMoeda = moedaContaService.getIdMoedaByIdProduto(conta.getIdProdutoInstituicao());

        if (req.getValor() >= 9999999.99) {
          throw new GenericServiceException("Valor a pagar não pode ser maior que R$ 9.999.999,99");
        }

        validaPossibilidadeDaContaPagarBoletoNoDia(BigDecimal.valueOf(req.getValor()), conta);

        if (idMoeda == null) {
          throw new GenericServiceException(
              ConstantesErro.CODIGO_MOEDA_NAO_ENCONTRADO_PARA_PRODUTO.getMensagem());
        }

        Pessoa pessoa = contaService.findPessoaTitularDaConta(conta.getIdConta());
        if (pessoa != null) {
          cpf = pessoa.getDocumento();
        }
        saldoConta = getSaldoConta(conta);
        contratoInstituicao = getContratoAtivoPorTipoTransacao(conta, TipoOperacaoEnum.PAGAMENTO);
        contratoUtilizado = getContratoUtilizado(contratoInstituicao);

        // verifico se eh para pagar a conta somente apos fazer o debito na instituicao
        if (contratoInstituicao.getDebitarGarantiaInst() != null
            && contratoInstituicao.getDebitarGarantiaInst()) {

          if (contratoInstituicao.getIdContaGarantiaInst() == null) {
            throw new GenericServiceException(
                "Não foi possivel efetuar operação.",
                "Contrato precisa ter o IdContaGarantiaInst configurada");
          }

          contaInstituicao =
              contaService.findByIdNotNull(contratoInstituicao.getIdContaGarantiaInst());
          saldoContaInst = getSaldoConta(contaInstituicao);
        }
        validarMesmoUsuarioConsulta(idUsuario, logPagtoValidacao);
        validarDataVencimento(req, logPagtoValidacao);

        resultado =
            gatewayPagtoExtService.efetuarPagamentoTitulo(
                logPagtoValidacao,
                conta,
                idUsuario,
                req,
                cpf,
                saldoConta,
                isPortador,
                contratoInstituicao,
                contratoUtilizado,
                contaInstituicao,
                saldoContaInst,
                idMoeda);

      } else {
        throw new GenericServiceException("Não foi possível encontrar a conta!");
      }

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  /**
   * verificar se o usuario que está pagando é o mesmo que consultou
   *
   * @param logPagtoValidacao
   */
  private void validarMesmoUsuarioConsulta(
      Long idUsuario, LogPagtoTituloValidacao logPagtoValidacao) {

    if (!logPagtoValidacao.getIdUsuarioInclusao().equals(idUsuario)) {
      throw new GenericServiceException("Usuário não autorizado a realizar esta operação");
    }
  }

  private void validarDataVencimento(
      EfetuarPagamentoTitulo req, LogPagtoTituloValidacao logPagtoValidacao) {
    Date dtEsperada = getDataVencimentoEsperada(logPagtoValidacao);
    if (req.getDataVencimento() != null && dtEsperada != null) {
      String dataEsperada = DateUtil.dateFormat("dd/MM/yyyy", dtEsperada);
      String dataEnviada = DateUtil.dateFormat("dd/MM/yyyy", req.getDataVencimento());
      logger.info("validarDataVencimento");
      logger.info("dataEsperada = " + dataEsperada);
      logger.info("dataEnviada = " + dataEnviada);
      if (dataEsperada != null && !dataEsperada.equals(dataEnviada)) {
        throw new GenericServiceException("Data de vencimento diferente da esperada.");
      }
    }
  }

  private void validarDataVencimento(
      EfetuarPagamentoTituloBRB req, LogPagtoTituloValidacao logPagtoValidacao) {
    if (req.getDataVencimento() != null) {
      Date dataEsperada = DateUtil.getDateSemTime(getDataVencimentoEsperada(logPagtoValidacao));
      Date dataEnviada = DateUtil.getDateSemTime(req.getDataVencimento());

      if (dataEsperada != null && !dataEsperada.equals(dataEnviada)) {
        throw new GenericServiceException("Data de vencimento diferente da esperada.");
      }
    }
  }

  public EfetuarPagamentoTituloResponse validarEfetuarPagamentoTituloV5(
      EfetuarPagamentoTitulo efetuarPagamentoTitulo, SecurityUserPortador userPortador) {
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.BOLETO);
    if (controleGarantiaFacade
            .getInstituicao(userPortador.getIdProcessadora(), userPortador.getIdInstituicao())
            .getStatus()
        == 9) {
      throw new GenericServiceException(
          ConstantesErro.BOL_SISTEMA_INDISPONIVEL_PARA_INSTITUICAO.getMensagem());
    }
    LogPagtoTituloValidacao logPagtoValidacao = getLogPagtoValidacao(efetuarPagamentoTitulo);
    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            logPagtoValidacao.getIdConta(), userPortador);
    return this.efetuarPagamentoTituloV5(efetuarPagamentoTitulo, conta, logPagtoValidacao);
  }

  public EfetuarPagamentoTituloResponse validarEfetuarPagamentoTituloV5(
      EfetuarPagamentoTitulo efetuarPagamentoTitulo, SecurityUserCorporativo userCorporativo) {
    travaServicosService.travaServicos(userCorporativo.getIdInstituicao(), Servicos.BOLETO);
    if (controleGarantiaFacade
            .getInstituicao(userCorporativo.getIdProcessadora(), userCorporativo.getIdInstituicao())
            .getStatus()
        == 9) {
      throw new GenericServiceException(
          ConstantesErro.BOL_SISTEMA_INDISPONIVEL_PARA_INSTITUICAO.getMensagem());
    }
    LogPagtoTituloValidacao logPagtoValidacao = getLogPagtoValidacao(efetuarPagamentoTitulo);
    ContaPagamento conta =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(
            logPagtoValidacao.getIdConta(), userCorporativo);
    return this.efetuarPagamentoTituloV5(efetuarPagamentoTitulo, conta, logPagtoValidacao);
  }

  private EfetuarPagamentoTituloResponse efetuarPagamentoTituloV5(
      EfetuarPagamentoTitulo efetuarPagamentoTitulo,
      ContaPagamento conta,
      LogPagtoTituloValidacao logPagtoValidacao) {

    EfetuarPagamentoTituloResponse resultado = new EfetuarPagamentoTituloResponse();

    try {
      Long idUsuario = Long.valueOf(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);

      GetSaldoConta saldoConta = null;
      GetSaldoConta saldoContaInst = null;

      String cpf = "";
      String nome = "";
      Integer tipoPessoa = null;
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao = null;
      ContratoGatewayPagto contratoUtilizado = null;
      ContaPagamento contaInstituicao = null;

      if (conta != null) {

        if (!Constantes.CONTA_ATIVA.equals(conta.getIdStatusConta())) {
          throw new GenericServiceException(
              ConstantesErro.CTP_CONTA_PRECISA_ESTAR_DESBLOQUEADA.format(
                  "realizar Pagamento de Contas"));
        }

        travaContasService.travaContas(conta.getIdConta(), Servicos.BOLETO);

        metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.BOLETO);

        limitesContaService.validarLimiteContaDiario(
            BigDecimal.valueOf(efetuarPagamentoTitulo.getValor()), conta);

        Integer idMoeda = moedaContaService.getIdMoedaByIdProduto(conta.getIdProdutoInstituicao());

        if (efetuarPagamentoTitulo.getValor() >= 9999999.99) {
          throw new GenericServiceException(
              ConstantesErro.REND_VALOR_FORA_DO_PERMITIDO.getMensagem());
        }

        validaPossibilidadeDaContaPagarBoletoNoDia(
            BigDecimal.valueOf(efetuarPagamentoTitulo.getValor()), conta);

        if (idMoeda == null) {
          throw new GenericServiceException(
              ConstantesErro.CODIGO_MOEDA_NAO_ENCONTRADO_PARA_PRODUTO.getMensagem());
        }

        Pessoa pessoa = contaService.findPessoaTitularDaConta(conta.getIdConta());
        if (pessoa != null) {
          cpf = pessoa.getDocumento();
          nome = pessoa.getNomeCompleto();
          tipoPessoa = pessoa.getIdTipoPessoa();
        }
        saldoConta = getSaldoConta(conta);
        Boolean debitarInstituicao = Boolean.FALSE;

        contratoInstituicao = getContratoAtivoPorTipoTransacao(conta, TipoOperacaoEnum.PAGAMENTO);
        contratoUtilizado = getContratoUtilizado(contratoInstituicao);

        // verifico se eh para pagar a conta somente apos fazer o debito na instituicao
        if (contratoInstituicao.getDebitarGarantiaInst() != null
            && contratoInstituicao.getDebitarGarantiaInst()) {

          if (contratoInstituicao.getIdContaGarantiaInst() == null) {
            throw new GenericServiceException(
                ConstantesErro.ERRO_AO_REALIZAR_OPERACAO.getMensagem(),
                ConstantesErro.CONFIG_CONTRATO_GARANTIA.getMensagem());
          }

          debitarInstituicao = true;
          contaInstituicao =
              contaService.findByIdNotNull(contratoInstituicao.getIdContaGarantiaInst());
          saldoContaInst = getSaldoConta(contaInstituicao);
        }
        validarMesmoUsuarioConsulta(idUsuario, logPagtoValidacao);
        validarDataVencimento(efetuarPagamentoTitulo, logPagtoValidacao);

        resultado =
            gatewayPagtoExtService.efetuarPagamentoTitulo(
                logPagtoValidacao,
                conta,
                idUsuario,
                efetuarPagamentoTitulo,
                cpf,
                nome,
                tipoPessoa,
                saldoConta,
                true,
                contratoInstituicao,
                contratoUtilizado,
                contaInstituicao,
                saldoContaInst,
                idMoeda);
      }

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  private void validaPossibilidadeDaContaPagarBoletoNoDia(BigDecimal valor, ContaPagamento conta) {
    Date inicioDoDia =
        Date.from(LocalDate.now().atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    Date agora = Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant());
    List<LogPagtoTituloTransacao> pagtoTituloTransacaoList =
        logPagtoTransacaoRepository.findPagamentosByIdConta(conta.getIdConta(), inicioDoDia, agora);
    BigDecimal somaDiaria = calcularSomaDiaria(pagtoTituloTransacaoList);

    limitesContaService.validaLimitesConta(
        conta, valor, somaDiaria, pagtoTituloTransacaoList.size(), LimiteTransacaoTipoEnum.BOLETO);
  }

  private BigDecimal calcularSomaDiaria(List<LogPagtoTituloTransacao> pagtoTituloTransacaos) {
    if (pagtoTituloTransacaos == null || pagtoTituloTransacaos.isEmpty()) {
      return BigDecimal.valueOf(0.00);
    }
    return pagtoTituloTransacaos.stream()
        .map(pagTitulo -> BigDecimal.valueOf(pagTitulo.getValor()))
        .reduce(BigDecimal.ZERO, BigDecimal::add);
  }

  private Date getDataVencimentoEsperada(LogPagtoTituloValidacao entityProtocolo) {
    if (entityProtocolo.getDataVencimentoRegistro() != null) {
      return entityProtocolo.getDataVencimentoRegistro();
    } else if (entityProtocolo.getDataVencimento() != null) {
      return entityProtocolo.getDataVencimento();
    }
    return null;
  }

  private LogPagtoTituloValidacao getLogPagtoValidacao(EfetuarPagamentoTitulo req) {
    // recuperar consulta pelo protocolo para proseguir validacao de acesso de conta
    LogPagtoTituloValidacao logPagtoValidacao =
        logPagtoValidacaoRepository.findById(req.getProtocoloInterno()).orElse(null);

    if (logPagtoValidacao == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar o protocolo interno enviado.",
          "idLogPagtoValidacao: " + req.getProtocoloInterno());
    }

    return logPagtoValidacao;
  }

  private LogPagtoTituloValidacao getLogPagtoValidacao(EfetuarPagamentoTituloBRB req) {
    // recuperar consulta pelo protocolo para proseguir validacao de acesso de conta
    LogPagtoTituloValidacao logPagtoValidacao =
        logPagtoValidacaoRepository.findByIdLogPagtoTitulo(req.getProtocoloInterno()).orElse(null);

    if (logPagtoValidacao == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar o protocolo interno enviado.",
          "idLogPagtoValidacao: " + req.getProtocoloInterno());
    }

    return logPagtoValidacao;
  }

  private GetSaldoConta getSaldoConta(ContaPagamento conta) {
    GetSaldoConta saldo = new GetSaldoConta();

    try {
      saldo = contaService.getSaldoConta(conta.getIdConta());
    } catch (Exception e) {
      e.printStackTrace();
    }
    return saldo;
  }

  public ConsultarOperadoraPorDDDResponse consultarOperadoraPorDDD(
      Integer ddd, SecurityUserPortador userPortador, Boolean voucher) {

    ConsultarOperadoraPorDDDResponse resp =
        gatewayPagtoExtService.consultarOperadoraPorDDD(ddd, voucher);

    // Filter voucher to bypass Apple validation
    if (userPortador != null && voucher) {
      String filterVoucher =
          parametroValorService.findSingleResult(
              "filter.voucher", userPortador.getIdInstituicao(), userPortador.getIdProcessadora());

      if ("true".equalsIgnoreCase(filterVoucher)) {
        List<String> filteredItems = Arrays.asList("claro tv", "oi tv", "sky tv");
        resp.setOperadoras(
            resp.getOperadoras().stream()
                .filter(o -> filteredItems.contains(o.getNome().toLowerCase()))
                .collect(Collectors.toList()));
      }
    }
    return resp;
  }

  public ConsultarValorOperadoraResponse consultarValorOperadora(Integer ddd, Integer operadoraId) {
    return gatewayPagtoExtService.consultarValorOperadora(ddd, operadoraId);
  }

  public EfetuarRecargaResponse efetuarRecargaV5(
      EfetuarRecargaRequest req, SecurityUserPortador userPortador, Boolean voucher) {

    EfetuarRecargaResponse resultado = new EfetuarRecargaResponse();

    try {

      // buscar conta para ve o usuário logado possui acesso
      Map<String, Object> params = getContaAndUsuarioLogado(null, userPortador, req.getIdConta());

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      Long idUsuario = params.get("idUsuario") != null ? (Long) params.get("idUsuario") : null;
      verificarPermissaoConta(conta);
      boolean isPortador =
          params.get("isPortador") != null ? (Boolean) params.get("isPortador") : false;

      FindProvidersDto providers = new FindProvidersDto();
      providers.setStateCode(req.getDdd());
      providers.setPhoneNumber(req.getNumero().intValue());

      if (!Constantes.CONTA_ATIVA.equals(conta.getIdStatusConta()) && !voucher) {
        throw new GenericServiceException(
            "A Conta precisa estar Desbloqueada para realizar Recarga de Celular.");
      }

      metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.RECARGA);

      if (!Constantes.CONTA_ATIVA.equals(conta.getIdStatusConta()) && voucher) {
        throw new GenericServiceException(
            "A Conta precisa estar Desbloqueada para realizar Resgate de Voucher.");
      }

      if (!condicaoResgateService.permitirResgate(conta)) {
        if (voucher) {
          throw new GenericServiceException(
              "Não foi possível resgate de voucher. Quantidade limite ultrapassada.");
        } else {
          throw new GenericServiceException(
              "Não foi possível efetuar a recarga. Quantidade limite ultrapassada.");
        }
      }

      limitesContaService.validarLimiteContaDiario(BigDecimal.valueOf(req.getValor()), conta);

      StatusFindProvidersDto statusFindProvidersDto =
          celcoinService.findProviderByStateCodeAndPhoneNumber(
              providers.getStateCode(), providers.getPhoneNumber());
      req.setOperadora(statusFindProvidersDto.getNameProvider());

      GetSaldoConta saldoConta = null;
      GetSaldoConta saldoContaInst = null;

      String cpf = "";
      ContratoGatewayPagtoInstTransacaoConfig contratoInstituicao = null;
      ContratoGatewayPagto contratoUtilizado = null;
      ContaPagamento contaInstituicao = null;

      if (conta != null) {
        Pessoa pessoa = contaService.findPessoaTitularDaConta(conta.getIdConta());
        if (pessoa != null) {
          cpf = pessoa.getDocumento();
        }

        Integer idMoeda = moedaContaService.getIdMoedaByIdProduto(conta.getIdProdutoInstituicao());

        if (idMoeda == null) {
          throw new GenericServiceException(
              ConstantesErro.CODIGO_MOEDA_NAO_ENCONTRADO_PARA_PRODUTO.getMensagem());
        }

        Integer codOperacao = voucher ? OP_VOUCHER : OP_RECARGA;
        validarNumeroCelularV5(req, voucher);

        saldoConta = getSaldoConta(conta);
        Boolean debitarInstituicao = Boolean.FALSE;
        contratoInstituicao = getContratoAtivoInstOperacao(conta, codOperacao);
        contratoUtilizado = getContratoUtilizado(contratoInstituicao);

        // verifico se eh para pagar a conta somente apos fazer o debito na instituicao
        if (contratoInstituicao.getDebitarGarantiaInst() != null
            && contratoInstituicao.getDebitarGarantiaInst()) {

          if (contratoInstituicao.getIdContaGarantiaInst() == null) {
            throw new GenericServiceException(
                "Não foi possivel efetuar operação.",
                "Contrato precisa ter o IdContaGarantiaInst configurada");
          }

          debitarInstituicao = true;
          contaInstituicao =
              contaService.findByIdNotNull(contratoInstituicao.getIdContaGarantiaInst());
          saldoContaInst = getSaldoConta(contaInstituicao);
        }

        resultado =
            gatewayPagtoExtService.efetuarRecargaV5(
                req,
                conta,
                idUsuario,
                cpf,
                saldoConta,
                isPortador,
                contratoInstituicao,
                contratoUtilizado,
                saldoContaInst,
                contaInstituicao,
                codOperacao,
                idMoeda);
      } else {
        throw new GenericServiceException("Não foi possível encontrar a conta!");
      }

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }
    return resultado;
  }

  private void validarNumeroCelularV5(EfetuarRecargaRequest req, Boolean voucher) {
    if (!voucher && req.getNumero() == null) {
      throw new GenericServiceException("Número de Celular é obrigatório para Recargas.");
    }

    if (req.getNumero() != null && !voucher) {
      String numero = req.getNumero().toString();
      if (numero.length() < MIN_TEL || numero.length() > MAX_TEL) {
        throw new GenericServiceException(
            "Número de Celular inválido.Verifique o número e tente novamente!");
      }
    }
  }

  public ContratoGatewayPagto getContratoUtilizado(
      ContratoGatewayPagtoInstTransacaoConfig contrato) {
    ContratoGatewayPagto contratoUtilizado =
        contratoRepository.findById(contrato.getIdContratoGatewayPagto()).orElse(null);

    if (contratoUtilizado == null) {
      throw new GenericServiceException(
          "Contrato não encontrado.",
          "IdContratoGatewayPagto: " + contrato.getIdContratoGatewayPagto());
    }
    return contratoUtilizado;
  }

  public ContratoGatewayPagtoInstTransacaoConfig getContratoAtivoInst(ContaPagamento conta) {

    List<ContratoGatewayPagtoInstTransacaoConfig> contratos =
        contratoInstRepository.findByIdProcessadoraAndIdInstituicaoAndStatus(
            conta.getIdProcessadora(), conta.getIdInstituicao(), STATUS_ATIVO);
    if (contratos == null || contratos.isEmpty()) {
      contratos =
          contratoInstRepository.findByIdProcessadoraAndStatusAndIdInstituicaoIsNull(
              conta.getIdProcessadora(), STATUS_ATIVO);
      if (contratos == null || contratos.isEmpty()) {
        throw new GenericServiceException(
            "ContratoGatewayPagto ativo não encontrado para a instituicao ",
            "idProc:" + conta.getIdProcessadora() + " idInstituicao: " + conta.getIdInstituicao());
      }
    }
    return contratos.get(0);
  }

  public ContratoGatewayPagtoInstTransacaoConfig getContratoAtivoPorTipoTransacao(
      ContaPagamento conta, TipoOperacaoEnum tipoOperacaoEnum) {

    logger.info("Buscando contrato ativo por TipoTransacao");

    List<ContratoGatewayPagtoInstTransacaoConfig> contratos =
        contratoInstRepository.findByIdProcessadoraAndIdInstituicaoAndStatusAndTipoTransacao(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            STATUS_ATIVO,
            tipoOperacaoEnum.getId());
    if (contratos == null || contratos.isEmpty()) {
      contratos =
          contratoInstRepository.findByIdProcessadoraAndStatusAndIdInstituicaoIsNull(
              conta.getIdProcessadora(), STATUS_ATIVO);
      if (contratos == null || contratos.isEmpty()) {
        throw new GenericServiceException(
            "ContratoGatewayPagto ativo não encontrado para a instituicao ",
            "idProc:" + conta.getIdProcessadora() + " idInstituicao: " + conta.getIdInstituicao());
      }
    }
    return contratos.get(0);
  }

  private ContratoGatewayPagtoInstTransacaoConfig getContratoAtivoInstOperacao(
      ContaPagamento conta, Integer idOperacao) {
    return getContratoAtivoInstOperacao(
        conta.getIdProcessadora(), conta.getIdInstituicao(), idOperacao);
  }

  private ContratoGatewayPagtoInstTransacaoConfig getContratoAtivoInstOperacao(
      Integer idProcessadora, Integer idInstituicao, Integer idOperacao) {

    List<ContratoGatewayPagtoInstTransacaoConfig> contratos =
        contratoInstRepository.findByHierarquiaAndOperacao(
            idProcessadora, idInstituicao, STATUS_ATIVO, idOperacao);
    //		if(contratos == null || contratos.isEmpty()) {
    //			contratos =
    // contratoInstRepository.findByIdProcessadoraAndStatusAndIdInstituicaoIsNull(conta.getIdProcessadora(), STATUS_ATIVO);
    if (contratos == null || contratos.isEmpty()) {
      throw new GenericServiceException(
          "ContratoGatewayPagtoInstTransacaoConfig ativo não encontrado para a instituicao ",
          "idProc:" + idProcessadora + " idInstituicao: " + idInstituicao);
    }
    if (contratos.size() > 1) {
      throw new GenericServiceException(
          "Mais de um ContratoGatewayPagtoInstTransacaoConfig ativo encontrado para a instituicao ",
          "idProc:" + idProcessadora + " idInstituicao: " + idInstituicao);
    }
    //		}
    return contratos.get(0);
  }

  public ConsultarContasPagasPorPeriodoResponse consultarPagamentosConta(
      SecurityUserPortador userPortador, Long idConta, Date dataInicial, Date dataFinal) {
    ConsultarContasPagasPorPeriodoResponse resultado = new ConsultarContasPagasPorPeriodoResponse();

    try {

      // se a dataInicial nao vier entao use 30 dias atrás
      dataInicial = dataInicial == null ? DateUtil.diminuirDias(new Date(), DIAS30) : dataInicial;
      // se a dataFinal nao vier use a data de hoje
      dataFinal = dataFinal == null ? new Date() : dataFinal;

      Map<String, Object> params = getContaAndUsuarioLogado(null, userPortador, idConta);

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      // Long idUsuario = params.get("idUsuario")!=null?(Long)params.get("idUsuario"):null;

      verificarPermissaoConta(conta);
      List<LogPagtoTituloTransacao> pagtos =
          gatewayPagtoExtService.consultarPagamentosConta(idConta, dataInicial, dataFinal);

      List<LogPagtoTituloTransacaoResponse> pagtosResp = new ArrayList<>();

      if (pagtos != null && !pagtos.isEmpty()) {
        pagtos.forEach(
            pag -> {
              LogPagtoTituloTransacaoResponse logPag = new LogPagtoTituloTransacaoResponse();
              BeanUtils.copyProperties(pag, logPag, Util.getNullPropertyNames(pag));

              LogPagtoTituloValidacao validacao =
                  logPagtoValidacaoRepository.findById(pag.getIdLogPagtoTitulo()).orElse(null);

              if (validacao != null) {
                logPag.setBeneficiario(validacao.getCedente());
                logPag.setDataVencimento(
                    logPag.getDataVencimento() == null
                        ? validacao.getDataVencimento()
                        : logPag.getDataVencimento());

                if (logPag.getDataVencimento() == null
                    && validacao.getDataVencimentoRegistro() != null) {
                  logPag.setDataVencimento(validacao.getDataVencimentoRegistro());
                }
              }
              logPag.setDataOperacao(
                  logPag.getDataOperacao() == null
                      ? pag.getDataHoraInicioPagto()
                      : logPag.getDataOperacao());

              pagtosResp.add(logPag);
            });
      }
      resultado.setSucesso(true);
      resultado.setPagamentos(pagtosResp);

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }
    return resultado;
  }

  public ConsultarRecargasPorPeriodoResponse consultarRecargasConta(
      SecurityUserPortador userPortador, Long idConta, Date dataInicial, Date dataFinal) {
    ConsultarRecargasPorPeriodoResponse resultado = new ConsultarRecargasPorPeriodoResponse();

    try {

      // se a dataInicial nao vier entao use 30 dias atrás
      dataInicial = dataInicial == null ? DateUtil.diminuirDias(new Date(), DIAS30) : dataInicial;
      // se a dataFinal nao vier use a data de hoje
      dataFinal = dataFinal == null ? new Date() : dataFinal;

      Map<String, Object> params = getContaAndUsuarioLogado(null, userPortador, idConta);

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      // Long idUsuario = params.get("idUsuario")!=null?(Long)params.get("idUsuario"):null;

      verificarPermissaoConta(conta);
      List<LogRecargaTransacao> recargas =
          gatewayPagtoExtService.consultarRecargasConta(idConta, dataInicial, dataFinal);
      List<LogRecargaTransacaoResponse> recargasResp = new ArrayList<>();

      if (recargas != null && !recargas.isEmpty()) {
        recargas.forEach(
            rec -> {
              LogRecargaTransacaoResponse logRec = new LogRecargaTransacaoResponse();
              BeanUtils.copyProperties(rec, logRec, Util.getNullPropertyNames(rec));
              logRec.setDataOperacao(
                  logRec.getDataOperacao() == null
                      ? rec.getDataHoraFimConfirm()
                      : logRec.getDataOperacao());

              if (rec.getOperadoraId() != null) {
                DDDOperadora operadora =
                    dddOperadoraRepository.findById(rec.getOperadoraId().intValue()).orElse(null);

                if (operadora != null) {
                  logRec.setOperadora(operadora.getNome());
                }
              }

              recargasResp.add(logRec);
            });
        ;
      }

      resultado.setSucesso(true);
      resultado.setRecargas(recargasResp);

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }
    return resultado;
  }

  public LogPagtoTituloTransacaoResponse consultarPagamentoContaByCodigoTransacao(
      SecurityUser user, SecurityUserPortador userPortador, Long idConta, String idTransacaoPagto) {

    LogPagtoTituloTransacaoResponse resultado = new LogPagtoTituloTransacaoResponse();

    try {

      Map<String, Object> params = getContaAndUsuarioLogado(user, userPortador, idConta);

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      // Long idUsuario = params.get("idUsuario")!=null?(Long)params.get("idUsuario"):null;

      verificarPermissaoConta(conta);
      LogPagtoTituloTransacao logPagtoTran =
          gatewayPagtoExtService.consultarPagamentoByIdTransacao(idConta, idTransacaoPagto);

      if (logPagtoTran == null) {
        throw new GenericServiceException(
            "Pagamento não encontrado",
            "idConta:" + idConta + " idTransacaoPagto: " + idTransacaoPagto);
      }
      BeanUtils.copyProperties(logPagtoTran, resultado, Util.getNullPropertyNames(logPagtoTran));

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  public LogRecargaTransacaoResponse consultarRecargasContaByCodigoTransacao(
      SecurityUser user, SecurityUserPortador userPortador, Long idConta, String idTransacaoPagto) {
    LogRecargaTransacaoResponse resultado = new LogRecargaTransacaoResponse();

    try {

      Map<String, Object> params = getContaAndUsuarioLogado(user, userPortador, idConta);

      ContaPagamento conta =
          params.get("conta") != null ? (ContaPagamento) params.get("conta") : null;
      // Long idUsuario = params.get("idUsuario")!=null?(Long)params.get("idUsuario"):null;

      verificarPermissaoConta(conta);
      LogRecargaTransacao logPagtoTran =
          gatewayPagtoExtService.consultarRecargasContaByCodigoTransacao(idConta, idTransacaoPagto);

      if (logPagtoTran == null) {
        throw new GenericServiceException(
            "Recarga não encontrada ",
            "idConta:" + idConta + " idTransacaoPagto: " + idTransacaoPagto);
      }

      BeanUtils.copyProperties(logPagtoTran, resultado, Util.getNullPropertyNames(logPagtoTran));

    } catch (GenericServiceException e) {
      tratarGenericServiceException(e, resultado);
    } catch (Exception e) {
      tratarException(e, resultado);
    }

    return resultado;
  }

  public List<DDDLocalidade> getAllDDDs() {
    return dddLocalidadeRepository.findAll();
  }

  public List<Bank> getBancosCelcoin() {
    return gatewayPagtoExtService.resgatarBancosCelcoin();
  }
}
