package br.com.json.bean.cadastral;

import static java.lang.Boolean.FALSE;

import br.com.sinergico.util.DateUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class GetCredencial implements Serializable {

  private static final long serialVersionUID = 1L;

  private String credencialMascarada;
  private String credencialMascaradaReduzida;
  private String credencialUltimosDigitos;
  private String nomeProduto;
  private Integer tipoConta;
  private Double saldo;
  private Double limiteDisponivel;
  private String urlImagemProduto;
  private Integer idProduto;
  private LocalDateTime dataValidade;
  private String contaPagamento;
  private Long idCredencial;
  private Boolean virtual;
  private String apelidoVirtual;
  private String credencialVirtual;
  private Long idConta;
  private Long idPessoa;
  private String nomeImpresso;
  private String dataValidadeFmt;
  private String codigoSeguranca;
  private String email;
  private LocalDateTime dataHoraInclusao;
  private Integer statusConta;
  private String descStatusConta;
  private Integer metodoSegurancaTransacao;
  private Integer status;
  private String descStatus;
  private Integer grupoStatus;
  private String descGrupoStatus;
  private Integer idPlastico;
  private Integer idProdutoPlataforma;
  private String dataSaldo;
  private Date PreparaDataSaldo;
  private String idCredencialExterna;
  private Boolean permiteInibirFatura;
  private Boolean faturaInibida;
  private Integer idProdutoInstituicao;
  private LocalDateTime dtBloqueioResgatePontos;
  private Integer mesesValidadeCartaoVitual;
  private String nomeCompleto;
  private Integer statusNfc;
  private LocalDateTime dtHrStatusNfc;
  private Boolean permiteCartaoFisico;
  private Integer pontoRelacionamento;
  private String descPontoRelacionamento;
  private LocalDateTime dataHoraEmitido;
  private Long bin;
  private Boolean senhaAlterada;

  // limite de credito
  private Double limite;
  // valores carga prod plat 1
  private Double valorMinCarga;
  private Double valorMaxCarga;

  private boolean b2b = FALSE;

  public GetCredencial() {
    super();
  }

  public GetCredencial(Long idPessoa, Long idConta, Integer idProduto, String idCredencialExterna) {
    this.idPessoa = idPessoa;
    this.idConta = idConta;
    this.idProduto = idProduto;
    this.idCredencialExterna = idCredencialExterna;
  }

  public String getIdCredencialExterna() {
    return idCredencialExterna;
  }

  public Double getLimite() {
    return limite;
  }

  public void setLimite(Double limite) {
    this.limite = limite;
  }

  public void setIdCredencialExterna(String idCredencialExterna) {
    this.idCredencialExterna = idCredencialExterna;
  }

  public Integer getIdProdutoPlataforma() {
    return idProdutoPlataforma;
  }

  public void setIdProdutoPlataforma(Integer idProdutoPlataforma) {
    this.idProdutoPlataforma = idProdutoPlataforma;
  }

  public Integer getIdPlastico() {
    return idPlastico;
  }

  public void setIdPlastico(Integer idPlastico) {
    this.idPlastico = idPlastico;
  }

  public Integer getStatusConta() {
    return statusConta;
  }

  public void setStatusConta(Integer statusConta) {
    this.statusConta = statusConta;
  }

  public String getDescStatusConta() {
    return descStatusConta;
  }

  public void setDescStatusConta(String descStatusConta) {
    this.descStatusConta = descStatusConta;
  }

  public Integer getMetodoSegurancaTransacao() {
    return metodoSegurancaTransacao;
  }

  public void setMetodoSegurancaTransacao(Integer metodoSegurancaTransacao) {
    this.metodoSegurancaTransacao = metodoSegurancaTransacao;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public String getDescStatus() {
    return descStatus;
  }

  public void setDescStatus(String descStatus) {
    this.descStatus = descStatus;
  }

  public Integer getGrupoStatus() {
    return grupoStatus;
  }

  public void setGrupoStatus(Integer grupoStatus) {
    this.grupoStatus = grupoStatus;
  }

  public String getDescGrupoStatus() {
    return descGrupoStatus;
  }

  public void setDescGrupoStatus(String descGrupoStatus) {
    this.descGrupoStatus = descGrupoStatus;
  }

  public String getEmail() {
    return email;
  }

  public void setEmail(String email) {
    this.email = email;
  }

  public String getCodigoSeguranca() {
    return codigoSeguranca;
  }

  public void setCodigoSeguranca(String codigoSeguranca) {
    this.codigoSeguranca = codigoSeguranca;
  }

  public String getNomeImpresso() {
    return nomeImpresso;
  }

  public void setNomeImpresso(String nomeImpresso) {
    this.nomeImpresso = nomeImpresso;
  }

  public String getDataValidadeFmt() {
    return dataValidadeFmt;
  }

  public void setDataValidadeFmt(String dataValidadeFmt) {
    this.dataValidadeFmt = dataValidadeFmt;
  }

  public Long getIdPessoa() {
    return idPessoa;
  }

  public void setIdPessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  public Long getIdConta() {
    return idConta;
  }

  public void setIdConta(Long idConta) {
    this.idConta = idConta;
  }

  public Boolean getVirtual() {
    return virtual;
  }

  public void setVirtual(Boolean virtual) {
    this.virtual = virtual;
  }

  public String getApelidoVirtual() {
    return apelidoVirtual;
  }

  public void setApelidoVirtual(String apelidoVirtual) {
    this.apelidoVirtual = apelidoVirtual;
  }

  public String getCredencialVirtual() {
    return credencialVirtual;
  }

  public void setCredencialVirtual(String credencialVirtual) {
    this.credencialVirtual = credencialVirtual;
  }

  public Long getIdCredencial() {
    return idCredencial;
  }

  public void setIdCredencial(Long idCredencial) {
    this.idCredencial = idCredencial;
  }

  public String getContaPagamento() {
    return contaPagamento;
  }

  public void setContaPagamento(String contaPagamento) {
    this.contaPagamento = contaPagamento;
  }

  public String getCredencialMascarada() {
    return credencialMascarada;
  }

  public void setCredencialMascarada(String credencialMascarada) {
    this.credencialMascarada = credencialMascarada;
  }

  public String getCredencialMascaradaReduzida() {
    return credencialMascaradaReduzida;
  }

  public void setCredencialMascaradaReduzida(String credencialMascaradaReduzida) {
    this.credencialMascaradaReduzida = credencialMascaradaReduzida;
  }

  public String getCredencialUltimosDigitos() {
    return credencialUltimosDigitos;
  }

  public void setCredencialUltimosDigitos(String credencialUlimosDigitos) {
    this.credencialUltimosDigitos = credencialUlimosDigitos;
  }

  public String getNomeProduto() {
    return nomeProduto;
  }

  public void setNomeProduto(String nomeProduto) {
    this.nomeProduto = nomeProduto;
  }

  public Integer getTipoConta() {
    return tipoConta;
  }

  public void setTipoConta(Integer tipoConta) {
    this.tipoConta = tipoConta;
  }

  public Double getSaldo() {
    return saldo;
  }

  public void setSaldo(Double saldo) {
    this.saldo = saldo;
  }

  public Double getLimiteDisponivel() {
    return limiteDisponivel;
  }

  public void setLimiteDisponivel(Double limiteDisponivel) {
    this.limiteDisponivel = limiteDisponivel;
  }

  public String getUrlImagemProduto() {
    return urlImagemProduto;
  }

  public void setUrlImagemProduto(String urlImagemProduto) {
    this.urlImagemProduto = urlImagemProduto;
  }

  public Integer getIdProduto() {
    return idProduto;
  }

  public void setIdProduto(Integer idProduto) {
    this.idProduto = idProduto;
  }

  public LocalDateTime getDataValidade() {
    return dataValidade;
  }

  public void setDataValidade(LocalDateTime dataValidade) {
    this.dataValidade = dataValidade;
  }

  public LocalDateTime getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(LocalDateTime localDateTime) {
    this.dataHoraInclusao = localDateTime;
  }

  public String getDataSaldo() {
    if (dataSaldo == null && PreparaDataSaldo != null) {
      dataSaldo = DateUtil.dateFormat("dd/MM/yyyy HH:mm:ss", PreparaDataSaldo);
    }
    return dataSaldo;
  }

  public void setDataSaldo(String dataSaldo) {
    this.dataSaldo = dataSaldo;
  }

  public Date getPreparaDataSaldo() {
    return PreparaDataSaldo;
  }

  public void setPreparaDataSaldo(Date preparaDataSaldo) {
    PreparaDataSaldo = preparaDataSaldo;
  }

  public Boolean getPermiteInibirFatura() {
    return permiteInibirFatura;
  }

  public void setPermiteInibirFatura(Boolean permiteInibirFatura) {
    this.permiteInibirFatura = permiteInibirFatura;
  }

  public Boolean getFaturaInibida() {
    return faturaInibida;
  }

  public void setFaturaInibida(Boolean faturaInibida) {
    this.faturaInibida = faturaInibida;
  }

  public Integer getIdProdutoInstituicao() {
    return idProdutoInstituicao;
  }

  public void setIdProdutoInstituicao(Integer idProdutoInstituicao) {
    this.idProdutoInstituicao = idProdutoInstituicao;
  }

  public LocalDateTime getDtBloqueioResgatePontos() {
    return dtBloqueioResgatePontos;
  }

  public void setDtBloqueioResgatePontos(LocalDateTime dtBloqueioResgatePontos) {
    this.dtBloqueioResgatePontos = dtBloqueioResgatePontos;
  }

  public Double getValorMinCarga() {
    return valorMinCarga;
  }

  public void setValorMinCarga(Double valorMinCarga) {
    this.valorMinCarga = valorMinCarga;
  }

  public Double getValorMaxCarga() {
    return valorMaxCarga;
  }

  public void setValorMaxCarga(Double valorMaxCarga) {
    this.valorMaxCarga = valorMaxCarga;
  }

  public Integer getMesesValidadeCartaoVitual() {
    return mesesValidadeCartaoVitual;
  }

  public void setMesesValidadeCartaoVitual(Integer mesesValidadeCartaoVitual) {
    this.mesesValidadeCartaoVitual = mesesValidadeCartaoVitual;
  }

  public String getNomeCompleto() {
    return nomeCompleto;
  }

  public void setNomeCompleto(String nomeCompleto) {
    this.nomeCompleto = nomeCompleto;
  }

  public Integer getStatusNfc() {
    return statusNfc;
  }

  public void setStatusNfc(Integer statusNfc) {
    this.statusNfc = statusNfc;
  }

  public LocalDateTime getDtHrStatusNfc() {
    return dtHrStatusNfc;
  }

  public void setDtHrStatusNfc(LocalDateTime dtHrStatusNfc) {
    this.dtHrStatusNfc = dtHrStatusNfc;
  }

  public boolean isB2b() {
    return b2b;
  }

  public void setB2b(boolean b2b) {
    this.b2b = b2b;
  }

  public Boolean getPermiteCartaoFisico() {
    return permiteCartaoFisico;
  }

  public void setPermiteCartaoFisico(Boolean permiteCartaoFisico) {
    this.permiteCartaoFisico = permiteCartaoFisico;
  }

  public Integer getPontoRelacionamento() {
    return pontoRelacionamento;
  }

  public void setPontoRelacionamento(Integer pontoRelacionamento) {
    this.pontoRelacionamento = pontoRelacionamento;
  }

  public String getDescPontoRelacionamento() {
    return descPontoRelacionamento;
  }

  public void setDescPontoRelacionamento(String descPontoRelacionamento) {
    this.descPontoRelacionamento = descPontoRelacionamento;
  }

  public LocalDateTime getDataHoraEmitido() {
    return dataHoraEmitido;
  }

  public void setDataHoraEmitido(LocalDateTime dataHoraEmitido) {
    this.dataHoraEmitido = dataHoraEmitido;
  }

  public Long getBin() {
    return bin;
  }

  public void setBin(Long bin) {
    this.bin = bin;
  }

  public Boolean getSenhaAlterada() {
    return senhaAlterada;
  }

  public void setSenhaAlterada(Boolean senhaAlterada) {
    this.senhaAlterada = senhaAlterada;
  }
}
