package br.com.entity.cadastral;

import br.com.entity.suporte.TipoEndereco;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import org.hibernate.annotations.DynamicUpdate;

@Entity
@DynamicUpdate(true)
@Table(name = "endereco_pessoa", schema = "cadastral")
@ApiModel(value = "EnderecoPessoa")
@SequenceGenerator(
    name = "seq_id_endereco_generator",
    sequenceName = "cadastral.seq_id_endereco",
    initialValue = 1,
    allocationSize = 1)
public class EnderecoPessoa implements Serializable {

  private static final long serialVersionUID = -5124319439346646367L;

  private static final Integer ATIVO = 1;

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_id_endereco_generator")
  @Column(name = "id_endereco", nullable = false)
  @Max(value = 999999999999L)
  private Long idEndereco;

  @Column(name = "id_pessoa")
  private Long idPessoa;

  @Column(name = "id_tipo_endereco")
  private Integer idTipoEndereco;

  @Size(max = 8)
  private String cep;

  @Size(max = 100)
  private String logradouro;

  @Size(max = 50)
  private String numero;

  @Size(max = 40)
  private String complemento;

  @Size(max = 100)
  private String bairro;

  @Size(max = 100)
  private String cidade;

  @Size(max = 2)
  private String uf;

  @Max(value = 99)
  private Integer status;

  @Transient private String descStatus;

  @Column(name = "dt_hr_status")
  private LocalDateTime dataHoraStatus;

  @Column(name = "id_usuario_inclusao")
  private Integer idUsuarioInclusao;

  @Column(name = "id_usuario_manutencao")
  private Integer idUsuarioManutencao;

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "dt_hr_inclusao")
  private Date dtHrInclusao;

  @Temporal(TemporalType.TIMESTAMP)
  @Column(name = "dt_hr_confirmacao")
  private Date dtHrConfirmacao;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(name = "id_pessoa", insertable = false, updatable = false)
  private Pessoa pessoa;

  @ManyToOne
  @JoinColumn(name = "id_tipo_endereco", insertable = false, updatable = false)
  private TipoEndereco tipoEndereco;

  @Column(name = "origem_confirmacao")
  private String origemConfirmacao;

  public String getDescStatus() {
    if (status != null) {
      return status.equals(ATIVO) ? "Ativo" : "Inativo";
    }
    return descStatus;
  }

  public void setDescStatus(String descStatus) {
    this.descStatus = descStatus;
  }

  public Pessoa getPessoa() {
    return pessoa;
  }

  public void setPessoa(Pessoa pessoa) {
    this.pessoa = pessoa;
  }

  public TipoEndereco getTipoEndereco() {
    return tipoEndereco;
  }

  public void setTipoEndereco(TipoEndereco tipoEndereco) {
    this.tipoEndereco = tipoEndereco;
  }

  public Long getIdEndereco() {
    return idEndereco;
  }

  public void setIdEndereco(Long idEndereco) {
    this.idEndereco = idEndereco;
  }

  public Long getIdPessoa() {
    return idPessoa;
  }

  public void setIdPessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  public Integer getIdTipoEndereco() {
    return idTipoEndereco;
  }

  public void setIdTipoEndereco(Integer idTipoEndereco) {
    this.idTipoEndereco = idTipoEndereco;
  }

  public String getCep() {
    return cep;
  }

  public void setCep(String cep) {
    this.cep = cep;
  }

  public String getLogradouro() {
    return logradouro;
  }

  public void setLogradouro(String logradouro) {
    this.logradouro = logradouro;
  }

  public String getNumero() {
    return numero;
  }

  public void setNumero(String numero) {
    this.numero = numero;
  }

  public String getComplemento() {
    return complemento;
  }

  public void setComplemento(String complemento) {
    this.complemento = complemento;
  }

  public String getBairro() {
    return bairro;
  }

  public void setBairro(String bairro) {
    this.bairro = bairro;
  }

  public String getCidade() {
    return cidade;
  }

  public void setCidade(String cidade) {
    this.cidade = cidade;
  }

  public String getUf() {
    return uf;
  }

  public void setUf(String uf) {
    this.uf = uf;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public LocalDateTime getDataHoraStatus() {
    return dataHoraStatus;
  }

  public void setDataHoraStatus(LocalDateTime dataHoraStatus) {
    this.dataHoraStatus = dataHoraStatus;
  }

  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  public Date getDtHrInclusao() {
    return dtHrInclusao;
  }

  public void setDtHrInclusao(Date dtHrInclusao) {
    this.dtHrInclusao = dtHrInclusao;
  }

  public Date getDtHrConfirmacao() {
    return dtHrConfirmacao;
  }

  public void setDtHrConfirmacao(Date dtHrConfirmacao) {
    this.dtHrConfirmacao = dtHrConfirmacao;
  }

  public String getOrigemConfirmacao() {
    return origemConfirmacao;
  }

  public void setOrigemConfirmacao(String origemConfirmacao) {
    this.origemConfirmacao = origemConfirmacao;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((idEndereco == null) ? 0 : idEndereco.hashCode());
    result = prime * result + ((idPessoa == null) ? 0 : idPessoa.hashCode());
    result = prime * result + ((idTipoEndereco == null) ? 0 : idTipoEndereco.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    EnderecoPessoa other = (EnderecoPessoa) obj;
    if (idEndereco == null) {
      if (other.idEndereco != null) return false;
    } else if (!idEndereco.equals(other.idEndereco)) return false;
    if (idPessoa == null) {
      if (other.idPessoa != null) return false;
    } else if (!idPessoa.equals(other.idPessoa)) return false;
    if (idTipoEndereco == null) {
      if (other.idTipoEndereco != null) return false;
    } else if (!idTipoEndereco.equals(other.idTipoEndereco)) return false;
    return true;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }
}
