package br.com.entity.cadastral;

import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.TipoStatus;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import org.hibernate.annotations.DynamicUpdate;

/**
 * <AUTHOR>
 */
@Entity
@DynamicUpdate(true)
@Table(name = "conta_pagamento", schema = "cadastral")
@NamedEntityGraph(
    name = "ContaPagamento.contas",
    attributeNodes = @NamedAttributeNode("contasPessoa"))
@SequenceGenerator(
    name = "seq_id_conta_pagamento_generator",
    sequenceName = "cadastral.seq_id_conta_pagamento",
    initialValue = 1,
    allocationSize = 1)
public class ContaPagamento implements Serializable {

  private static final long serialVersionUID = -1948707300966949648L;

  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "seq_id_conta_pagamento_generator")
  @Column(name = "id_conta", nullable = false)
  private Long idConta;

  @Column(name = "id_conta_externa", nullable = true)
  private Long idContaExterna;

  @Column(name = "id_processadora", nullable = false)
  private Integer idProcessadora;

  @Column(name = "id_instituicao", nullable = false)
  private Integer idInstituicao;

  @Column(name = "id_regional", nullable = false)
  private Integer idRegional;

  @Column(name = "id_filial", nullable = false)
  private Integer idFilial;

  @Column(name = "id_ponto_de_relacionamento", nullable = false)
  private Integer idPontoDeRelacionamento;

  @Column(name = "id_prod_instituicao", nullable = false)
  private Integer idProdutoInstituicao;

  @Column(name = "id_prod_plat", nullable = false)
  private Integer idProdutoPlataforma;

  @Column(name = "id_prod_instituidor", nullable = false)
  private Integer idProdutoInstituidor;

  @ManyToOne
  @JoinColumn(
      name = "id_prod_instituidor",
      referencedColumnName = "id_prod_instituidor",
      insertable = false,
      updatable = false)
  private ProdutoInstituidor produtoInstituidor;

  @Column(name = "id_relacionamento", nullable = false)
  private Integer idRelacionamento;

  @ManyToOne
  @JoinColumn(
      name = "id_relacionamento",
      referencedColumnName = "id_relacionamento",
      insertable = false,
      updatable = false)
  private ArranjoRelacionamento arranjoRelacionamento;

  @Column(name = "id_conta_pagamento", nullable = false)
  private String idContaPagamento;

  @Column(name = "dt_hr_inclusao", nullable = false)
  private LocalDateTime dataHoraInclusao;

  @Column(name = "id_usuario_inclusao", nullable = false)
  private Integer idUsuarioInclusao;

  @Column(name = "id_status_conta", nullable = false)
  private Integer idStatusConta;

  @Column(name = "id_status_v2", nullable = false)
  private Integer idStatusV2;

  @Column(name = "dt_hr_status_conta", nullable = false)
  private LocalDateTime dataHoraStatusConta;

  @Column(name = "id_usuario_manutencao", nullable = false)
  private Integer idUsuarioManutencao;

  @Column(name = "saldo_disponivel", nullable = false)
  private Double saldoDisponivel;

  @Column(name = "id_perfil_tarifario", nullable = false)
  private Integer idPerfilTarifario;

  @OneToMany(mappedBy = "contaPessoaId.contaPagamento")
  private List<ContaPessoa> contasPessoa;

  @OneToMany(mappedBy = "idConta", fetch = FetchType.LAZY)
  @JsonIgnore
  private List<Credencial> credenciais;

  @ManyToOne
  @JoinColumn(
      name = "id_prod_instituicao",
      referencedColumnName = "id_prod_instituicao",
      insertable = false,
      updatable = false)
  private ProdutoInstituicao produtoInstituicao;

  @ManyToOne
  @JoinColumns({
    @JoinColumn(
        name = "id_processadora",
        referencedColumnName = "id_processadora",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_instituicao",
        referencedColumnName = "id_instituicao",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_regional",
        referencedColumnName = "id_regional",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_filial",
        referencedColumnName = "id_filial",
        nullable = false,
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_ponto_de_relacionamento",
        referencedColumnName = "id_ponto_de_relacionamento",
        nullable = false,
        insertable = false,
        updatable = false)
  })
  @JsonIgnore
  private HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(
      name = "id_status_conta",
      referencedColumnName = "id_status",
      insertable = false,
      updatable = false)
  private TipoStatus tipoStatus;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(
      name = "id_status_v2",
      referencedColumnName = "id_status",
      insertable = false,
      updatable = false)
  private TipoStatus tipoStatusV2;

  @Column(name = "valor_carga_padrao")
  private Double valorCargaPadrao;

  @Column(name = "limite_unico", nullable = true)
  private Double limiteUnico;

  @Column(name = "id_account_code")
  private String idAccountCode;

  @Column(name = "nro_proposta")
  private Integer numeroProposta;

  @Column(name = "nro_pedido_voucher_papel")
  private Long nroPedidoVoucherPapel;

  @Column(name = "id_conta_corresp")
  private Long idContaCorresp;

  @Column(name = "dt_hr_replicacao")
  private LocalDateTime dataHoraReplicacao;

  @Column(name = "dt_hr_confirma_replica")
  private Date dataHoraConfirmaReplica;

  @Column(name = "pix_habilitado")
  private Boolean pixHabilitado;

  @Column(name = "id_acct")
  private Long idAcct;

  @Column(name = "metodo_seguranca_transacao")
  private Integer metodoSegurancaTransacao;

  public ContaPagamento() {
    this(null);
  }

  public ContaPagamento(Long idConta) {
    super();
    this.idConta = idConta;
  }

  public Long getIdContaExterna() {
    return idContaExterna;
  }

  public void setIdContaExterna(Long idContaExterna) {
    this.idContaExterna = idContaExterna;
  }

  public String getIdAccountCode() {
    return idAccountCode;
  }

  public void setIdAccountCode(String idAccountCode) {
    this.idAccountCode = idAccountCode;
  }

  public ProdutoInstituidor getProdutoInstituidor() {
    return produtoInstituidor;
  }

  public void setProdutoInstituidor(ProdutoInstituidor produtoInstituidor) {
    this.produtoInstituidor = produtoInstituidor;
  }

  public List<Credencial> getCredenciais() {
    return credenciais;
  }

  public void setCredenciais(List<Credencial> credenciais) {
    this.credenciais = credenciais;
  }

  public Double getValorCargaPadrao() {
    return valorCargaPadrao;
  }

  public void setValorCargaPadrao(Double valorCargaPadrao) {
    this.valorCargaPadrao = valorCargaPadrao;
  }

  public TipoStatus getTipoStatus() {
    return tipoStatus;
  }

  public void setTipoStatus(TipoStatus tipoStatus) {
    this.tipoStatus = tipoStatus;
  }

  public List<ContaPessoa> getContasPessoa() {
    return contasPessoa;
  }

  public ProdutoInstituicao getProdutoInstituicao() {
    return produtoInstituicao;
  }

  public void setProdutoInstituicao(ProdutoInstituicao produtoInstituicao) {
    this.produtoInstituicao = produtoInstituicao;
  }

  public void setContasPessoa(List<ContaPessoa> contasPessoa) {
    this.contasPessoa = contasPessoa;
  }

  public Long getIdConta() {
    return idConta;
  }

  public void setIdConta(Long idConta) {
    this.idConta = idConta;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public Integer getIdPontoDeRelacionamento() {
    return idPontoDeRelacionamento;
  }

  public void setIdPontoDeRelacionamento(Integer idPontoDeRelacionamento) {
    this.idPontoDeRelacionamento = idPontoDeRelacionamento;
  }

  public Integer getIdProdutoInstituicao() {
    return idProdutoInstituicao;
  }

  public void setIdProdutoInstituicao(Integer idProdutoInstituicao) {
    this.idProdutoInstituicao = idProdutoInstituicao;
  }

  public Integer getIdProdutoPlataforma() {
    return idProdutoPlataforma;
  }

  public void setIdProdutoPlataforma(Integer idProdutoPlataforma) {
    this.idProdutoPlataforma = idProdutoPlataforma;
  }

  public Integer getIdProdutoInstituidor() {
    return idProdutoInstituidor;
  }

  public void setIdProdutoInstituidor(Integer idProdutoInstituidor) {
    this.idProdutoInstituidor = idProdutoInstituidor;
  }

  public Integer getIdRelacionamento() {
    return idRelacionamento;
  }

  public void setIdRelacionamento(Integer idRelacionamento) {
    this.idRelacionamento = idRelacionamento;
  }

  public String getIdContaPagamento() {
    return idContaPagamento;
  }

  public void setIdContaPagamento(String idContaPagamento) {
    this.idContaPagamento = idContaPagamento;
  }

  public LocalDateTime getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(LocalDateTime dataHoraInclusao) {
    this.dataHoraInclusao = dataHoraInclusao;
  }

  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Integer getIdStatusConta() {
    return idStatusConta;
  }

  public void setIdStatusConta(Integer idStatusConta) {
    this.idStatusConta = idStatusConta;
  }

  public LocalDateTime getDataHoraStatusConta() {
    return dataHoraStatusConta;
  }

  public void setDataHoraStatusConta(LocalDateTime dataHoraStatusConta) {
    this.dataHoraStatusConta = dataHoraStatusConta;
  }

  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  public Double getSaldoDisponivel() {
    return saldoDisponivel;
  }

  public void setSaldoDisponivel(Double saldoDisponivel) {
    this.saldoDisponivel = saldoDisponivel;
  }

  public HierarquiaPontoDeRelacionamento getHierarquiaPontoDeRelacionamento() {
    return hierarquiaPontoDeRelacionamento;
  }

  public void setHierarquiaPontoDeRelacionamento(
      HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento) {
    this.hierarquiaPontoDeRelacionamento = hierarquiaPontoDeRelacionamento;
  }

  public Integer getIdPerfilTarifario() {
    return idPerfilTarifario;
  }

  public void setIdPerfilTarifario(Integer idPerfilTarifario) {
    this.idPerfilTarifario = idPerfilTarifario;
  }

  public Double getLimiteUnico() {
    return limiteUnico;
  }

  public void setLimiteUnico(Double limiteUnico) {
    this.limiteUnico = limiteUnico;
  }

  public ArranjoRelacionamento getArranjoRelacionamento() {
    return arranjoRelacionamento;
  }

  public void setArranjoRelacionamento(ArranjoRelacionamento arranjoRelacionamento) {
    this.arranjoRelacionamento = arranjoRelacionamento;
  }

  public Integer getNumeroProposta() {
    return numeroProposta;
  }

  public void setNumeroProposta(Integer numeroProposta) {
    this.numeroProposta = numeroProposta;
  }

  public Long getNroPedidoVoucherPapel() {
    return nroPedidoVoucherPapel;
  }

  public void setNroPedidoVoucherPapel(Long nroPedidoVoucherPapel) {
    this.nroPedidoVoucherPapel = nroPedidoVoucherPapel;
  }

  public LocalDateTime getDataHoraReplicacao() {
    return dataHoraReplicacao;
  }

  public void setDataHoraReplicacao(LocalDateTime dataHoraReplicacao) {
    this.dataHoraReplicacao = dataHoraReplicacao;
  }

  public Long getIdContaCorresp() {
    return idContaCorresp;
  }

  public void setIdContaCorresp(Long idContaCorresp) {
    this.idContaCorresp = idContaCorresp;
  }

  public Date getDataHoraConfirmaReplica() {
    return dataHoraConfirmaReplica;
  }

  public void setDataHoraConfirmaReplica(Date dataHoraConfirmaReplica) {
    this.dataHoraConfirmaReplica = dataHoraConfirmaReplica;
  }

  public Boolean getPixHabilitado() {
    return pixHabilitado;
  }

  public void setPixHabilitado(Boolean pixHabilitado) {
    this.pixHabilitado = pixHabilitado;
  }

  public Long getIdAcct() {
    return idAcct;
  }

  public void setIdAcct(Long idAcct) {
    this.idAcct = idAcct;
  }

  public Integer getMetodoSegurancaTransacao() {
    return metodoSegurancaTransacao;
  }

  public void setMetodoSegurancaTransacao(Integer metodoSegurancaTransacao) {
    this.metodoSegurancaTransacao = metodoSegurancaTransacao;
  }

  public Integer getIdStatusV2() {
    if (idStatusV2 != null) {
      return idStatusV2;
    } else {
      return idStatusConta;
    }
  }

  public void setIdStatusV2(Integer idStatusV2) {
    this.idStatusV2 = idStatusV2;
    this.idStatusConta = idStatusV2;
  }

  public TipoStatus getTipoStatusV2() {
    if (tipoStatusV2 != null) {
      return tipoStatusV2;
    } else {
      return tipoStatus;
    }
  }

  public void setTipoStatusV2(TipoStatus tipoStatusV2) {
    this.tipoStatusV2 = tipoStatusV2;
    this.tipoStatus = tipoStatusV2;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + ((idConta == null) ? 0 : idConta.hashCode());
    result = prime * result + ((idContaPagamento == null) ? 0 : idContaPagamento.hashCode());
    result = prime * result + ((idFilial == null) ? 0 : idFilial.hashCode());
    result = prime * result + ((idInstituicao == null) ? 0 : idInstituicao.hashCode());
    result =
        prime * result
            + ((idPontoDeRelacionamento == null) ? 0 : idPontoDeRelacionamento.hashCode());
    result = prime * result + ((idProcessadora == null) ? 0 : idProcessadora.hashCode());
    result = prime * result + ((idRegional == null) ? 0 : idRegional.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    ContaPagamento other = (ContaPagamento) obj;
    if (idConta == null) {
      if (other.idConta != null) return false;
    } else if (!idConta.equals(other.idConta)) return false;
    if (idContaPagamento == null) {
      if (other.idContaPagamento != null) return false;
    } else if (!idContaPagamento.equals(other.idContaPagamento)) return false;
    if (idFilial == null) {
      if (other.idFilial != null) return false;
    } else if (!idFilial.equals(other.idFilial)) return false;
    if (idInstituicao == null) {
      if (other.idInstituicao != null) return false;
    } else if (!idInstituicao.equals(other.idInstituicao)) return false;
    if (idPontoDeRelacionamento == null) {
      if (other.idPontoDeRelacionamento != null) return false;
    } else if (!idPontoDeRelacionamento.equals(other.idPontoDeRelacionamento)) return false;
    if (idProcessadora == null) {
      if (other.idProcessadora != null) return false;
    } else if (!idProcessadora.equals(other.idProcessadora)) return false;
    if (idRegional == null) {
      if (other.idRegional != null) return false;
    } else if (!idRegional.equals(other.idRegional)) return false;
    return true;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }

  @Override
  public String toString() {
    return "ContaPagamento{"
        + "idConta="
        + idConta
        + ", idContaExterna="
        + idContaExterna
        + ", idProcessadora="
        + idProcessadora
        + ", idInstituicao="
        + idInstituicao
        + ", idRegional="
        + idRegional
        + ", idFilial="
        + idFilial
        + ", idPontoDeRelacionamento="
        + idPontoDeRelacionamento
        + ", idProdutoInstituicao="
        + idProdutoInstituicao
        + ", idProdutoPlataforma="
        + idProdutoPlataforma
        + ", idProdutoInstituidor="
        + idProdutoInstituidor
        + ", produtoInstituidor="
        + produtoInstituidor
        + ", idRelacionamento="
        + idRelacionamento
        + ", arranjoRelacionamento="
        + arranjoRelacionamento
        + ", idContaPagamento='"
        + idContaPagamento
        + '\''
        + ", dataHoraInclusao="
        + dataHoraInclusao
        + ", idUsuarioInclusao="
        + idUsuarioInclusao
        + ", idStatusConta="
        + idStatusConta
        + ", idStatusV2="
        + idStatusV2
        + ", dataHoraStatusConta="
        + dataHoraStatusConta
        + ", idUsuarioManutencao="
        + idUsuarioManutencao
        + ", saldoDisponivel="
        + saldoDisponivel
        + ", idPerfilTarifario="
        + idPerfilTarifario
        + ", contasPessoa="
        + contasPessoa
        + ", credenciais="
        + credenciais
        + ", produtoInstituicao="
        + produtoInstituicao
        + ", hierarquiaPontoDeRelacionamento="
        + hierarquiaPontoDeRelacionamento
        + ", tipoStatus="
        + tipoStatus
        + ", tipoStatusV2="
        + tipoStatusV2
        + ",valorCargaPadrao="
        + valorCargaPadrao
        + ", limiteUnico="
        + limiteUnico
        + ", idAccountCode='"
        + idAccountCode
        + '\''
        + ", numeroProposta="
        + numeroProposta
        + ", nroPedidoVoucherPapel="
        + nroPedidoVoucherPapel
        + ", idAcct="
        + idAcct
        + '}';
  }
}
