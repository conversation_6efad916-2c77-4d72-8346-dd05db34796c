package br.com.sinergico.service.suporte;

import static br.com.sinergico.service.suporte.AcessoUsuarioService.*;

import br.com.entity.suporte.AcessoLog;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioUnico;
import br.com.entity.suporte.AcessoUsuarioUnicoView;
import br.com.entity.suporte.AcessoUsuarioUnicoVinculacao;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.suporte.TokenFuncionalidade;
import br.com.enumVO.TipoTokenFuncionalidadeEnum;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.enums.EnumServicesVallooAuthenticator;
import br.com.json.bean.enums.MecanismosAutenticacao;
import br.com.json.bean.suporte.AcessoUsuarioUnicoDTO;
import br.com.json.bean.suporte.InfoLoginUnicoCache;
import br.com.json.bean.suporte.LoginUnicoResponseDTO;
import br.com.sinergico.enums.StatusUsuario;
import br.com.sinergico.repository.suporte.AcessoUsuarioRepository;
import br.com.sinergico.repository.suporte.AcessoUsuarioUnicoRepository;
import br.com.sinergico.repository.suporte.AcessoUsuarioUnicoViewRepository;
import br.com.sinergico.repository.suporte.AcessoUsuarioUnicoVinculacaoRepository;
import br.com.sinergico.repository.suporte.impl.InfoLoginUnicoCacheRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUsuarioUnico;
import br.com.sinergico.security.TokenAuthenticationUsuarioUnicoService;
import br.com.sinergico.security.UsuarioUnicoAuthentication;
import br.com.sinergico.security.UsuarioUnicoService;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.suporte.valloo.authenticator.VallooAuthenticatorService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.SpringContextUtils;
import br.com.sinergico.vo.AcessoUsuarioUnicoResponse;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AcessoUsuarioUnicoService {

  @Autowired private AcessoUsuarioUnicoRepository acessoUsuarioUnicoRepository;

  @Autowired private AcessoUsuarioUnicoViewRepository acessoUsuarioUnicoViewRepository;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private AcessoLogService acessoLogService;

  @Autowired private TokenFuncionalidadeService tokenFuncionalidadeService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired private UsuarioUnicoService usuarioUnicoService;

  @Autowired private InfoLoginUnicoCacheRepository infoLoginUnicoCacheRepository;

  @Autowired private VallooAuthenticatorService vallooAuthenticatorService;

  @Autowired private AcessoUsuarioUnicoVinculacaoRepository acessoUsuarioUnicoVinculacaoRepository;

  @Autowired private AcessoUsuarioRepository acessoUsuarioRepository;

  @Autowired private TokenAuthenticationUsuarioUnicoService tokenAuthenticationUsuarioUnicoService;

  @Autowired private UtilService utilService;

  @Value("${url.valloo.b2b.infinancas}")
  private String urlVallooB2bInfinancas;

  @Value("${url.valloo.b2b.client}")
  private String urlVallooB2bClient;

  @Value("${url.valloo.issuer}")
  private String urlVallooIssuer;

  @Value("${url.login.unico}")
  private String urlLoginUnico;

  public AcessoUsuarioUnicoResponse findByCpf(String cpf) {
    AcessoUsuarioUnicoResponse acessoUsuarioUnico;
    try {
      String cleanedCpf = cpf.trim().replaceAll("[-.]", "");
      acessoUsuarioUnico = acessoUsuarioUnicoRepository.findByCpf(cleanedCpf);

      List<AcessoUsuarioUnicoView> acessoUsuarioUnicoViews =
          getAllAcessoUsuarioUnicoViewByCpfAcessoUsuarioUnico(acessoUsuarioUnico.getCpf());
      acessoUsuarioUnico.setAcessoUsuarioUnicoViews(acessoUsuarioUnicoViews);

    } catch (Exception e) {
      return null;
    }
    return acessoUsuarioUnico;
  }

  public AcessoUsuarioUnico findByLogin(String login) {
    AcessoUsuarioUnico acessoUsuarioUnico;
    try {
      acessoUsuarioUnico =
          acessoUsuarioUnicoRepository.findByLoginStartingWithIgnoreCase(login.trim());
    } catch (Exception e) {
      return null;
    }
    return acessoUsuarioUnico;
  }

  public AcessoUsuarioUnico findByLoginAndSenha(String login, String senha) {
    AcessoUsuarioUnico acessoUsuarioUnico = this.findByLogin(login);
    if (acessoUsuarioUnico != null) {
      if (isPassMatch(senha, acessoUsuarioUnico.getCpf(), acessoUsuarioUnico.getSenha())) {
        return acessoUsuarioUnico;
      }
    }
    return null;
  }

  public List<AcessoUsuarioUnicoView> getAllAcessoUsuarioUnicoViewByLoginAcessoUsuarioUnico(
      String loginAcessoUsuarioUnico) {
    return acessoUsuarioUnicoViewRepository.findAllByLoginAcessoUsuarioUnico(
        loginAcessoUsuarioUnico.trim());
  }

  public List<AcessoUsuarioUnicoView> getAllAcessoUsuarioUnicoViewByCpfAcessoUsuarioUnico(
      String cpf) {
    return acessoUsuarioUnicoViewRepository.findAllByCpfAcessoUsuarioUnico(cpf.trim());
  }

  public boolean isUsuarioVinculadoComUsuarioUnico(String loginAcessoUsuario) {
    AcessoUsuarioUnicoView acessoUsuarioUnicoView =
        acessoUsuarioUnicoViewRepository.findByLoginAcessoUsuario(loginAcessoUsuario.trim());
    if (acessoUsuarioUnicoView == null) {
      return false;
    }
    return true;
  }

  private boolean isPassMatch(String senhaInformada, String cpf, String senhaArmazenada) {
    return BCrypt.checkpw(senhaInformada + cpf, senhaArmazenada);
  }

  public ResponseEntity<?> validarAcessoUsuarioUnico(
      AcessoUsuarioUnico acessoUsuarioUnico,
      String login,
      String senha,
      HttpServletRequest httpRequest,
      HashMap<String, Object> map) {
    if (acessoUsuarioUnico == null || acessoUsuarioUnico.getSenha() == null) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      acessoLogService.saveAcessoLog(
          null,
          httpRequest,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    boolean bloquearUsuarioTemporariamente =
        verificarQuantidadeTentativasLoginErradas(
            acessoUsuarioUnico.getId(), acessoUsuarioUnico.getStatus());
    if (bloquearUsuarioTemporariamente) {
      acessoUsuarioUnico.setDtHrStatus(LocalDateTime.now());
      acessoUsuarioUnico.setStatus(StatusUsuario.BLOQUEIO_TEMPORARIO.value());
      acessoUsuarioUnicoRepository.save(acessoUsuarioUnico);
    }
    if (acessoUsuarioService.usuarioBloqueado(acessoUsuarioUnico.getStatus(), map)) {
      acessoLogService.saveAcessoLog(
          acessoUsuarioUnico.getId(),
          httpRequest,
          HttpStatus.UNAUTHORIZED,
          map.get("msg").toString() + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    if (!isPassMatch(senha, acessoUsuarioUnico.getCpf(), acessoUsuarioUnico.getSenha())) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      acessoLogService.saveAcessoLog(
          acessoUsuarioUnico.getId(),
          httpRequest,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);

    } else {
      // TODO verificar com o Bruno como vai ser a trava de servicos
      travaServicosService.travaServicos(null, Servicos.LOGIN_ISSUER);
    }
    if (acessoUsuarioService.usuarioSenhaExpirada(acessoUsuarioUnico.getSenhaExpirada(), map)) {
      acessoLogService.saveAcessoLog(
          acessoUsuarioUnico.getId(),
          httpRequest,
          HttpStatus.FORBIDDEN,
          map.get("msg").toString() + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.FORBIDDEN);
    }

    return null;
  }

  public boolean verificarQuantidadeTentativasLoginErradas(Long idUsuario, Integer statusUsuario) {
    int quantidadeTentativasErradasLogin =
        acessoUsuarioUnicoRepository.verificarQuantidadeTentativasLoginErradas(idUsuario);
    return quantidadeTentativasErradasLogin >= Constantes.VALOR_PADRAO_QUANTIDADE_TENTATIVAS_LOGIN
        && StatusUsuario.BLOQUEIO_TEMPORARIO.value() != statusUsuario;
  }

  public ResponseEntity<?> doLoginUsuarioUnico(
      HttpServletRequest request,
      HttpServletResponse response,
      String usuario,
      String senha,
      String token)
      throws IOException {
    if (request.getHeader("Proxy-Authorization") != null) {
      throw new GenericServiceException("Acesso impedido.", HttpStatus.UNAUTHORIZED);
    }

    HashMap<String, Object> map = new HashMap<>();
    AcessoUsuarioUnico acessoUsuarioUnico =
        acessoUsuarioUnicoRepository.findByLoginStartingWithIgnoreCase(usuario.trim());

    ResponseEntity<?> validacaoUsuario =
        validarAcessoUsuarioUnico(acessoUsuarioUnico, usuario, senha, request, map);
    if (validacaoUsuario != null) {
      return validacaoUsuario;
    }

    validarTokenLogin(acessoUsuarioUnico, request, usuario, token);
    List<AcessoUsuarioUnicoView> acessoUsuarioUnicoViews;
    acessoUsuarioUnicoViews =
        acessoUsuarioUnicoViewRepository.findAllByLoginAcessoUsuarioUnico(
            acessoUsuarioUnico.getLogin().trim());

    if (acessoUsuarioUnicoViews == null || acessoUsuarioUnicoViews.isEmpty()) {
      throw new GenericServiceException(
          "Usuário sem nenhum vinculação.", HttpStatus.INTERNAL_SERVER_ERROR);
    } else {
      LoginUnicoResponseDTO loginUnicoResponseDTO = new LoginUnicoResponseDTO();
      String tokenGeradoLoginUnico =
          tokenFuncionalidadeService.gerarTokenFuncionalidadeLoginUnico(
              acessoUsuarioUnico.getCpf(), acessoUsuarioUnico.getLogin());
      String idLoginUnico = generateRandomString();
      List<AcessoUsuarioUnicoDTO> acessoUsuarioUnicoDTOS = new ArrayList<>();
      acessoUsuarioUnicoViews.forEach(
          acessoUsuarioUnicoView -> {
            AcessoUsuarioUnicoDTO acessoUsuarioUnicoDTO = new AcessoUsuarioUnicoDTO();
            try {
              BeanUtils.copyProperties(acessoUsuarioUnicoDTO, acessoUsuarioUnicoView);
              if (acessoUsuarioUnicoView.getTipoB2b() != null
                  && acessoUsuarioUnicoView.getTipoB2b() == 1) {
                acessoUsuarioUnicoDTO.setUrlBaseRedirect(urlVallooB2bInfinancas);
              } else {
                if (acessoUsuarioUnicoView.getTipoB2b() != null
                    && acessoUsuarioUnicoView.getTipoB2b() == 0) {
                  acessoUsuarioUnicoDTO.setUrlBaseRedirect(urlVallooB2bClient);
                } else {
                  acessoUsuarioUnicoDTO.setUrlBaseRedirect(urlVallooIssuer);
                }
              }
              acessoUsuarioUnicoDTOS.add(acessoUsuarioUnicoDTO);
            } catch (IllegalAccessException | InvocationTargetException e) {
              throw new GenericServiceException("Não possível copiar as propriedades do usuário");
            }
          });
      loginUnicoResponseDTO.setToken(tokenGeradoLoginUnico);
      loginUnicoResponseDTO.setListaAcesso(acessoUsuarioUnicoDTOS);
      loginUnicoResponseDTO.setIdentificador(idLoginUnico);
      InfoLoginUnicoCache infoLoginUnicoCache = new InfoLoginUnicoCache();
      infoLoginUnicoCache.setIdUnico(idLoginUnico);
      infoLoginUnicoCache.setLogin(usuario);
      infoLoginUnicoCache.setSenha(senha);
      infoLoginUnicoCacheRepository.save(infoLoginUnicoCache);
      acessoLogService.saveAcessoLog(acessoUsuarioUnico.getId(), request, HttpStatus.OK, SUCESSO);
      return new ResponseEntity<>(loginUnicoResponseDTO, HttpStatus.OK);
    }
  }

  protected EmailService getEmailService() {
    return SpringContextUtils.getBean(EmailService.class);
  }

  private void validarTokenLogin(
      AcessoUsuarioUnico acessoUsuarioUnico,
      HttpServletRequest request,
      String login,
      String tokenMfa) {
    if (verificarParametroValidarToken()) {
      if (tokenMfa == null || tokenMfa.isEmpty()) {
        acessoLogService.saveAcessoLog(
            acessoUsuarioUnico.getId(),
            request,
            HttpStatus.UNAUTHORIZED,
            TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                + " | Informação interna - Login: "
                + login
                + " Token nulo ou em branco");
        throw new GenericServiceException(
            TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
      }
      TokenFuncionalidade tokenFuncionalidade;
      if (utilService.isAmbienteProducao()) {
        TipoTokenFuncionalidadeEnum tipoTokenFuncionalidadeEnum = null;
        try {
          if (MecanismosAutenticacao.VALLOO_AUTHENTICATOR
              .getCodigo()
              .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
            vallooAuthenticatorService.validateToken(
                acessoUsuarioUnico.getId().intValue(),
                tokenMfa,
                EnumServicesVallooAuthenticator.VALLOO.getCodigo());
          } else {
            if (MecanismosAutenticacao.SMS
                .getCodigo()
                .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
              tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_SMS;
            } else if (MecanismosAutenticacao.EMAIL
                .getCodigo()
                .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
              tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_EMAIL;
            } else if (MecanismosAutenticacao.WHATSAPP
                .getCodigo()
                .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
              tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_WHATSAPP;
            } else if (MecanismosAutenticacao.TODOS
                .getCodigo()
                .equals(acessoUsuarioUnico.getIdMecanismoAutenticacao())) {
              tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_TODOS;
            }
            tokenFuncionalidade =
                tokenFuncionalidadeService.utilizarByFuncionalidadeAndChaveExterna(
                    tokenMfa, acessoUsuarioUnico.getLogin(), tipoTokenFuncionalidadeEnum);
            if (tokenFuncionalidade == null) {
              acessoLogService.saveAcessoLog(
                  acessoUsuarioUnico.getId(),
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INVALIDO
                      + " | Informação interna - Login: "
                      + login
                      + " Token: "
                      + tokenMfa);
              throw new GenericServiceException(TOKEN_INVALIDO, HttpStatus.UNAUTHORIZED);
            }
            if (!tokenMfa.equalsIgnoreCase(tokenFuncionalidade.getToken().trim())) {
              acessoLogService.saveAcessoLog(
                  acessoUsuarioUnico.getId(),
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                      + " | Informação interna - Login: "
                      + login
                      + " Token nulo ou em branco");
              throw new GenericServiceException(
                  TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
            }
            if (!tokenFuncionalidade.getDocumento().equals(acessoUsuarioUnico.getCpf())) {
              acessoLogService.saveAcessoLog(
                  acessoUsuarioUnico.getId(),
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO
                      + " | Informação interna - Login: "
                      + login
                      + " Token: "
                      + tokenMfa);
              throw new GenericServiceException(
                  TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO, HttpStatus.UNAUTHORIZED);
            }
          }

        } catch (Exception e) {
          if (acessoUsuarioUnico.getBlIgnorarTokenAutenticacao()) {
            tokenFuncionalidade =
                tokenFuncionalidadeService.generateTokenFromInformacoesSuperUsuario(
                    acessoUsuarioUnico);

            if (tokenFuncionalidade == null) {
              acessoLogService.saveAcessoLog(
                  acessoUsuarioUnico.getId(),
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INVALIDO
                      + " | Informação interna - Login: "
                      + login
                      + " Token: "
                      + tokenMfa);
              throw new GenericServiceException(TOKEN_INVALIDO, HttpStatus.UNAUTHORIZED);
            }
            if (!tokenMfa.equalsIgnoreCase(tokenFuncionalidade.getToken().trim())) {
              acessoLogService.saveAcessoLog(
                  acessoUsuarioUnico.getId(),
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                      + " | Informação interna - Login: "
                      + login
                      + " Token nulo ou em branco");
              throw new GenericServiceException(
                  TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
            }
            if (!tokenFuncionalidade.getDocumento().equals(acessoUsuarioUnico.getCpf())) {
              acessoLogService.saveAcessoLog(
                  acessoUsuarioUnico.getId(),
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO
                      + " | Informação interna - Login: "
                      + login
                      + " Token: "
                      + tokenMfa);
              throw new GenericServiceException(
                  TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO, HttpStatus.UNAUTHORIZED);
            }
          } else {
            acessoLogService.saveAcessoLog(
                acessoUsuarioUnico.getId(),
                request,
                HttpStatus.UNAUTHORIZED,
                TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                    + " | Informação interna - Login: "
                    + login
                    + " Token: "
                    + tokenMfa);
            throw new GenericServiceException(
                TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
          }
        }
      } else {
        tokenFuncionalidade =
            tokenFuncionalidadeService.generateTokenFromInformacoesSuperUsuario(acessoUsuarioUnico);
        if (tokenFuncionalidade == null) {
          acessoLogService.saveAcessoLog(
              acessoUsuarioUnico.getId(),
              request,
              HttpStatus.UNAUTHORIZED,
              TOKEN_INVALIDO + " | Informação interna - Login: " + login + " Token: " + tokenMfa);
          throw new GenericServiceException(TOKEN_INVALIDO, HttpStatus.UNAUTHORIZED);
        }
        if (!tokenMfa.equalsIgnoreCase(tokenFuncionalidade.getToken().trim())) {
          acessoLogService.saveAcessoLog(
              acessoUsuarioUnico.getId(),
              request,
              HttpStatus.UNAUTHORIZED,
              TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                  + " | Informação interna - Login: "
                  + login
                  + " Token nulo ou em branco");
          throw new GenericServiceException(
              TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
        }
        if (!tokenFuncionalidade.getDocumento().equals(acessoUsuarioUnico.getCpf())) {
          acessoLogService.saveAcessoLog(
              acessoUsuarioUnico.getId(),
              request,
              HttpStatus.UNAUTHORIZED,
              TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO
                  + " | Informação interna - Login: "
                  + login
                  + " Token: "
                  + tokenMfa);
          throw new GenericServiceException(
              TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO, HttpStatus.UNAUTHORIZED);
        }
      }
    }
  }

  private boolean verificarParametroValidarToken() {
    Boolean isValidarToken = true;
    try {
      List<ParametroValor> parametroValores =
          parametroValorService.getParametrosByDescricaoChaveParametro(
              Constantes.PARAMETRO_VALIDAR_TOKEN_SUPER_USUARIO);
      if (parametroValores != null
          && !parametroValores.isEmpty()
          && parametroValores.get(0) != null) {
        ParametroValor parametroValor = parametroValores.get(0);
        if ("B".equalsIgnoreCase(parametroValor.getParametroDefinicao().getTipo())) {
          isValidarToken = Boolean.valueOf(parametroValor.getValorParametro());
        }
      }
    } catch (Exception e) {
      isValidarToken = true;
    }
    return isValidarToken;
  }

  public AcessoUsuarioUnico save(AcessoUsuarioUnico acessoUsuarioUnico) {
    return this.acessoUsuarioUnicoRepository.save(acessoUsuarioUnico);
  }

  public AcessoUsuarioUnico findById(Long id) {
    return this.acessoUsuarioUnicoRepository.findById(id).orElse(null);
  }

  public String getUrlTrocaSenhaLoginUnico() {
    return urlLoginUnico;
  }

  public String makeAuthenicationUsuarioUnico(
      HttpServletRequest request,
      HttpServletResponse response,
      AcessoUsuarioUnico acessoUsuarioUnico,
      AcessoLog acessoLog) {

    SecurityUsuarioUnico securityUsuarioUnico = new SecurityUsuarioUnico(acessoUsuarioUnico);
    usuarioUnicoService.addUser(request.getSession(), securityUsuarioUnico);

    UsuarioUnicoAuthentication usuarioUnicoAuthentication =
        new UsuarioUnicoAuthentication(securityUsuarioUnico);
    String userToken =
        tokenAuthenticationUsuarioUnicoService.addAuthentication(
            request, usuarioUnicoAuthentication, acessoLog);
    Authentication authentication =
        tokenAuthenticationUsuarioUnicoService.getAuthentication(request);
    SecurityContextHolder.getContext().setAuthentication(authentication);
    return userToken;
  }

  private String generateRandomString() {
    int length = 16;
    boolean useLetters = true;
    boolean useNumbers = true;
    return RandomStringUtils.random(length, useLetters, useNumbers);
  }

  @Transactional
  public AcessoUsuarioUnico createOrUpdateUsuarioUnico(
      AcessoUsuarioUnico acessoUsuarioUnico, SecurityUser user) {
    AcessoUsuarioUnico usuarioExistente = null;
    if (acessoUsuarioUnico.getId() != null) {
      usuarioExistente = acessoUsuarioUnicoRepository.getOne(acessoUsuarioUnico.getId());
    }

    if (usuarioExistente != null) {
      // Atualizar os campos do usuário existente
      usuarioExistente.setCpf(acessoUsuarioUnico.getCpf());
      usuarioExistente.setLogin(acessoUsuarioUnico.getLogin());
      usuarioExistente.setNome(acessoUsuarioUnico.getNome());
      usuarioExistente.setEmail(acessoUsuarioUnico.getEmail());
      usuarioExistente.setDddNroCelular(acessoUsuarioUnico.getDddNroCelular());
      usuarioExistente.setNroCelular(acessoUsuarioUnico.getNroCelular());
      usuarioExistente.setSexo(acessoUsuarioUnico.getSexo());
      usuarioExistente.setStatus(acessoUsuarioUnico.getStatus());
      usuarioExistente.setSenhaExpirada(acessoUsuarioUnico.getSenhaExpirada());
      usuarioExistente.setIdMecanismoAutenticacao(acessoUsuarioUnico.getIdMecanismoAutenticacao());
      usuarioExistente.setIdUsuarioManutencao(user.getIdUsuario().longValue());
      usuarioExistente.setDtHrStatus(LocalDateTime.now());

      usuarioExistente = acessoUsuarioUnicoRepository.save(usuarioExistente);
    } else {
      // Criar um novo usuário
      String senhaRandom = acessoUsuarioService.generatePassword();
      String senhaEncoded =
          acessoUsuarioService.encodePassword(senhaRandom + acessoUsuarioUnico.getCpf());
      acessoUsuarioUnico.setIdUsuarioManutencao(user.getIdUsuario().longValue());
      acessoUsuarioUnico.setSenha(senhaEncoded);
      acessoUsuarioUnico.setBlIgnorarTokenAutenticacao(false);
      acessoUsuarioUnico.setDtHrInclusao(LocalDateTime.now());
      acessoUsuarioUnico.setDtHrStatus(LocalDateTime.now());
      usuarioExistente = acessoUsuarioUnicoRepository.save(acessoUsuarioUnico);

      try {
        String url = this.getUrlTrocaSenhaLoginUnico();
        getEmailService().sendPasswordEmailSuperUsuario(acessoUsuarioUnico, senhaRandom, url);
      } catch (Exception e) {
        throw new GenericServiceException(
            "Não foi possível enviar Senha por E-mail para o Usuário", e);
      }
    }

    // Sincronizar vínculos
    List<AcessoUsuarioUnicoView> views = acessoUsuarioUnico.getAcessoUsuarioUnicoViews();
    List<AcessoUsuarioUnicoVinculacao> vinculacoesExistentes =
        acessoUsuarioUnicoVinculacaoRepository.findByCpfUsuarioUnico(acessoUsuarioUnico.getCpf());

    // Verificar e deletar vínculos que não estão na lista recebida
    for (AcessoUsuarioUnicoVinculacao vinculacao : vinculacoesExistentes) {
      boolean existsInView =
          views.stream()
              .anyMatch(
                  view ->
                      view.getLoginAcessoUsuario()
                          .equals(vinculacao.getAcessoUsuario().getLogin()));
      if (!existsInView) {
        acessoUsuarioUnicoVinculacaoRepository.delete(vinculacao);
      }
    }

    // Verificar e adicionar novos vínculos que estão na lista recebida, mas não existem no banco
    for (AcessoUsuarioUnicoView view : views) {
      boolean existsInDb =
          vinculacoesExistentes.stream()
              .anyMatch(
                  vinculacao ->
                      vinculacao
                          .getAcessoUsuario()
                          .getLogin()
                          .equals(view.getLoginAcessoUsuario()));
      if (!existsInDb) {
        AcessoUsuarioUnicoVinculacao novaVinculacao = new AcessoUsuarioUnicoVinculacao();
        novaVinculacao.setCpfUsuarioUnico(acessoUsuarioUnico.getCpf());
        novaVinculacao.setLoginAcessoUsuario(view.getLoginAcessoUsuario());
        novaVinculacao.setCpfAcessoUsuario(acessoUsuarioUnico.getCpf());

        AcessoUsuario acessoUsuario =
            acessoUsuarioRepository.findByIdUsuario(view.getIdAcessoUsuario().intValue());
        novaVinculacao.setAcessoUsuario(acessoUsuario);

        acessoUsuarioUnicoVinculacaoRepository.save(novaVinculacao);
      }
    }

    return usuarioExistente;
  }
}
