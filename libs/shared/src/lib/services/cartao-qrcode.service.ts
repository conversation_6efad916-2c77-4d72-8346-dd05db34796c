import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AbstractService } from './abstract.service';
import { resolve } from '@utils/resolve.util';
import {
  CriaVinculaEditaOuCancelaCartaoQrCodeVO,
  ImagemCartaoQrCodeVO,
  CartaoQrCode
} from './qrcode-lojista.service';

@Injectable({
  providedIn: 'root'
})
export class CartaoQRCodeService extends AbstractService<any> {

  constructor(protected override http: HttpClient) {
    super('valloo', 'api', http);
  }

  /**
   * Gera um novo cartão QR code permanente
   * @param idContaVinculada ID da conta a ser vinculada
   * @param apelido Apelido opcional para o QR code
   * @returns Observable com a imagem do QR code gerado
   */
  gerarCartaoQrCode(idContaVinculada: number, apelido?: string): Observable<ImagemCartaoQrCodeVO> {
    const url = resolve('valloo://gerarCartaoQrCode');
    const body: CriaVinculaEditaOuCancelaCartaoQrCodeVO = {
      idContaVinculada,
      apelido
    };
    return this.http.post<ImagemCartaoQrCodeVO>(url, body);
  }

  /**
   * Lista cartões QR code existentes para uma conta
   * @param idContaVinculada ID da conta
   * @returns Observable com lista de cartões QR code
   */
  listarCartoesQrCode(idContaVinculada: number): Observable<CartaoQrCode[]> {
    const url = resolve('valloo://listarCartoesQrCode');
    const body: CriaVinculaEditaOuCancelaCartaoQrCodeVO = {
      idContaVinculada,
      status: 1
    };
    return this.http.post<CartaoQrCode[]>(url, body);
  }

  /**
   * Busca um cartão QR code específico por ID
   * @param idCartaoQrCode ID do cartão QR code
   * @returns Observable com o cartão QR code
   */
  buscarCartaoQrCode(idCartaoQrCode: number): Observable<CartaoQrCode> {
    const url = resolve('valloo://cartaoQrCode') + `/${idCartaoQrCode}`;
    return this.http.get<CartaoQrCode>(url);
  }

  /**
   * Busca a imagem de um cartão QR code específico
   * @param idCartaoQrCode ID do cartão QR code
   * @returns Observable com a imagem do QR code
   */
  buscarImagemCartaoQrCode(idCartaoQrCode: number): Observable<ImagemCartaoQrCodeVO> {
    const url = resolve('valloo://buscarImagemCartaoQrCode', { idConta: idCartaoQrCode });
    return this.http.get<ImagemCartaoQrCodeVO>(url);
  }

  /**
   * Cancela um cartão QR code
   * @param idCartaoQrCode ID do cartão QR code a ser cancelado
   * @returns Observable com o cartão QR code cancelado
   */
  cancelarCartaoQrCode(idCartaoQrCode: number): Observable<CartaoQrCode> {
    const url = resolve('valloo://bloquearCartaoQrCode', { id: idCartaoQrCode });
    return this.http.delete<CartaoQrCode>(url);
  }
}
