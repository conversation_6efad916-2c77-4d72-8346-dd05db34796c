package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.PortadorLoginConta;
import br.com.entity.cadastral.PortadorLoginContaId;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import br.com.sinergico.repository.cadastral.PortadorLoginContaRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.util.ConstantesErro;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class PortadorLoginContaService
    extends GenericService<PortadorLoginConta, PortadorLoginContaId> {

  @Autowired private PortadorLoginService portadorLoginService;

  private PortadorLoginContaRepository portadorLoginContaRepository;

  @Autowired
  public PortadorLoginContaService(PortadorLoginContaRepository repo) {
    super(repo);
    portadorLoginContaRepository = repo;
  }

  List<Long> buscarIdsContasPagamentoAssociadosAoLogin(Long idLogin) {
    return portadorLoginContaRepository.buscarIdsContaPagamentoAssociadosAoIdLogin(idLogin);
  }

  List<ContaPagamento> buscarContasPagamentoAssociadosAoIdLogin(Long idLogin) {
    return portadorLoginContaRepository.buscarContasPagamentoAssociadosAoIdLogin(idLogin);
  }

  List<Long> buscarIdsContaPagamentoResponsavelNaoAtreladasAoResponsavelAtravesDoResponsavel(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoResponsavel,
      Long idLoginResponsavel,
      TipoPortadorLoginEnum tipoPortadorLoginEnumResponsavel) {
    return portadorLoginContaRepository
        .buscarIdsContaPagamentoResponsavelNaoAtreladasAoResponsavelAtravesDoResponsavel(
            idProcessadora,
            idInstituicao,
            documentoResponsavel,
            idLoginResponsavel,
            tipoPortadorLoginEnumResponsavel
                .name() // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
            );
  }

  List<Long> buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoResponsavel(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoResponsavel,
      Long idLoginResponsavel,
      TipoPortadorLoginEnum tipoPortadorLoginEnumResponsavel) {
    return portadorLoginContaRepository
        .buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoResponsavel(
            idProcessadora,
            idInstituicao,
            documentoResponsavel,
            idLoginResponsavel,
            tipoPortadorLoginEnumResponsavel
                .name(), // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
            TipoPortadorLoginEnum.DEPENDENTE
                .name() // A principio somente usar tipoLoginDependente = DEPENDENTE. //TODO
            // estender para ficar genérico
            );
  }

  List<Long> buscarIdsContaPagamentoDependentesNaoAtreladasAoDependenteAtravesDoDependente(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoDependente,
      Long idLoginDependente,
      TipoPortadorLoginEnum tipoPortadorLoginEnumResponsavel,
      TipoPortadorLoginEnum tipoPortadorLoginEnumDependente) {
    return portadorLoginContaRepository
        .buscarIdsContaPagamentoDependentesNaoAtreladasAoDependenteAtravesDoDependente(
            idProcessadora,
            idInstituicao,
            documentoDependente,
            idLoginDependente,
            tipoPortadorLoginEnumResponsavel
                .name(), // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
            tipoPortadorLoginEnumDependente
                .name() // A principio somente usar tipoLoginDependente = DEPENDENTE
            );
  }

  List<Long> buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoDependente(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoDependente,
      Long idLoginResponsavel,
      TipoPortadorLoginEnum tipoPortadorLoginEnumResponsavel,
      TipoPortadorLoginEnum tipoPortadorLoginEnumDependente) {
    return portadorLoginContaRepository
        .buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoDependente(
            idProcessadora,
            idInstituicao,
            documentoDependente,
            idLoginResponsavel,
            tipoPortadorLoginEnumResponsavel
                .name(), // A principio somente usar tipoLoginResponsavel = RESPONSAVEL
            tipoPortadorLoginEnumDependente
                .name() // A principio somente usar tipoLoginDependente = DEPENDENTE
            );
  }

  List<Long> buscarIdsContaPagamentoRegraRepresentanteLegal(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoEmpresa,
      String documentoRepresentante,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginContaRepository.buscarIdsContasRegraRepresentanteLegal(
        idProcessadora,
        idInstituicao,
        documentoEmpresa,
        documentoRepresentante,
        tipoPortadorLoginEnum.name());
  }

  List<Long> buscarIdsContaPagamentoRegraMultibeneficiosRepresentanteLegal(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoEmpresa,
      String documentoRepresentante,
      Long grupoAcesso,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginContaRepository
        .buscarIdsContaPagamentoRegraMultibeneficiosRepresentanteLegal(
            idProcessadora,
            idInstituicao,
            documentoEmpresa,
            documentoRepresentante,
            grupoAcesso,
            tipoPortadorLoginEnum.name());
  }

  List<Long> buscarIdsContaPagamentoPessoaAdicionalPfPessoaPj(
      Integer idProcessadora,
      Integer idInstituicao,
      String documentoTitular,
      String documentoAdicional,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginContaRepository.buscarIdsContasRegraPessoaAdicionalPfPessoaPj(
        idProcessadora,
        idInstituicao,
        documentoTitular,
        documentoAdicional,
        tipoPortadorLoginEnum.name());
  }

  List<Long> buscarIdsContaPagamentoRegraMultibeneficios(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer tipoPessoa,
      String documento,
      Long grupoAcesso,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginContaRepository.buscarIdsContaPagamentoRegraMultibeneficios(
        idProcessadora,
        idInstituicao,
        tipoPessoa,
        documento,
        grupoAcesso,
        tipoPortadorLoginEnum.name());
  }

  List<Long> buscarIdsContasRegraSimples(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer tipoPessoa,
      String documento,
      TipoPortadorLoginEnum tipoPortadorLoginEnum) {
    return portadorLoginContaRepository.buscarIdsContasRegraSimples(
        idProcessadora, idInstituicao, tipoPessoa, documento, tipoPortadorLoginEnum.name());
  }

  public List<PortadorLoginConta> criaPortadoresContaCorporativo(
      PortadorLogin portadorLogin, String documento) {
    List<Long> idsContaPagamento =
        buscarIdsContasRegraSimples(
            portadorLogin.getIdProcessadora(),
            portadorLogin.getIdInstituicao(),
            portadorLogin.getIdTipoPessoa(),
            documento,
            portadorLogin.getTipoLoginEnum());
    return constroiPortadoresConta(portadorLogin, idsContaPagamento);
  }

  public List<PortadorLoginConta> criaPortadoresConta(PortadorLogin portadorLogin) {

    List<Long> idsContaPagamento;
    List<Long> idsContaPagamentoResponsavel;
    List<Long> idsContaPagamentoDependente;
    List<Long> idsContaPagamentoDependenteNaoAtreladasAoResponsavel;
    switch (portadorLogin.getTipoLoginEnum().getRegraTipoPortadorLoginEnum()) {
      case SIMPLES:
      case CORPORATIVO:
        idsContaPagamento =
            buscarIdsContasRegraSimples(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getIdTipoPessoa(),
                portadorLogin.getCpf(),
                portadorLogin.getTipoLoginEnum());
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
      case MULTIBENEFICIOS:
        idsContaPagamento =
            buscarIdsContaPagamentoRegraMultibeneficios(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getIdTipoPessoa(),
                portadorLogin.getCpf(),
                portadorLogin.getGrupoAcesso(),
                portadorLogin.getTipoLoginEnum());
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
      case MULTIBENEFICIOS_REPRESENTANTE_LEGAL:
        idsContaPagamento =
            buscarIdsContaPagamentoRegraMultibeneficiosRepresentanteLegal(
                portadorLogin.getIdProcessadora(), portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(), portadorLogin.getDocumentoAcesso(),
                portadorLogin.getGrupoAcesso(), portadorLogin.getTipoLoginEnum());
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
      case REPRESENTANTE_LEGAL:
        idsContaPagamento =
            buscarIdsContaPagamentoRegraRepresentanteLegal(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(),
                portadorLogin.getDocumentoAcesso(),
                portadorLogin.getTipoLoginEnum());
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
      case PESSOA_ADICIONAL_PF_CONTA_PJ:
        idsContaPagamento =
            buscarIdsContaPagamentoPessoaAdicionalPfPessoaPj(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(),
                portadorLogin.getDocumentoAcesso(),
                portadorLogin.getTipoLoginEnum());
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
      case RESPONSAVEL:
        idsContaPagamento = new ArrayList<>();
        idsContaPagamentoResponsavel =
            buscarIdsContaPagamentoResponsavelNaoAtreladasAoResponsavelAtravesDoResponsavel(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(),
                portadorLogin.getIdLogin(),
                portadorLogin.getTipoLoginEnum());
        idsContaPagamentoDependente =
            buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoResponsavel(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(),
                portadorLogin.getIdLogin(),
                portadorLogin.getTipoLoginEnum());
        idsContaPagamento.addAll(idsContaPagamentoResponsavel);
        idsContaPagamento.addAll(idsContaPagamentoDependente);
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
      case DEPENDENTE:
        List<PortadorLoginConta> portadorLoginContaList = new ArrayList<>();
        PortadorLogin portadorLoginResponsavel =
            portadorLoginService.encontraPortadorLoginResponsavelAtravesDoPortadorLoginDependente(
                portadorLogin.getIdLogin());
        if (portadorLoginResponsavel == null) {
          throw new GenericServiceException(
              ConstantesErro.PTL_RESPONSAVEL_DO_DEPENDENTE_NAO_ENCONTRADO.getMensagem());
        }
        idsContaPagamentoDependente =
            buscarIdsContaPagamentoDependentesNaoAtreladasAoDependenteAtravesDoDependente(
                portadorLogin.getIdProcessadora(), portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(), portadorLogin.getIdLogin(),
                portadorLoginResponsavel.getTipoLoginEnum(), portadorLogin.getTipoLoginEnum());
        idsContaPagamentoDependenteNaoAtreladasAoResponsavel =
            buscarIdsContaPagamentoDependentesNaoAtreladasAoResponsavelAtravesDoDependente(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getCpf(),
                portadorLoginResponsavel.getIdLogin(),
                portadorLoginResponsavel.getTipoLoginEnum(),
                portadorLogin.getTipoLoginEnum());
        portadorLoginContaList.addAll(
            constroiPortadoresConta(
                portadorLoginResponsavel, idsContaPagamentoDependenteNaoAtreladasAoResponsavel));
        portadorLoginContaList.addAll(
            constroiPortadoresConta(portadorLogin, idsContaPagamentoDependente));
        return portadorLoginContaList;
      default:
        idsContaPagamento =
            buscarIdsContasRegraSimples(
                portadorLogin.getIdProcessadora(),
                portadorLogin.getIdInstituicao(),
                portadorLogin.getIdTipoPessoa(),
                portadorLogin.getCpf(),
                portadorLogin.getTipoLoginEnum());
        return constroiPortadoresConta(portadorLogin, idsContaPagamento);
    }
  }

  private List<PortadorLoginConta> constroiPortadoresConta(
      PortadorLogin portadorLogin, List<Long> idsContaPagamento) {

    // Retorna uma lista vazia se qualquer parâmetro for inválido
    if (portadorLogin == null || idsContaPagamento == null || idsContaPagamento.isEmpty()) {
      return Collections.emptyList();
    }

    // Remove contas já associadas
    List<Long> idsContaJaAssociadas =
        buscarIdsContasPagamentoAssociadosAoLogin(portadorLogin.getIdLogin());
    idsContaPagamento.removeAll(idsContaJaAssociadas);

    // Retorna uma lista vazia se não houver novas contas a associar
    if (idsContaPagamento.isEmpty()) {
      return Collections.emptyList();
    }

    List<PortadorLoginConta> portadorLoginContaList =
        idsContaPagamento.stream()
            .map(
                idContaPagamento ->
                    new PortadorLoginConta(
                        new PortadorLoginContaId(portadorLogin.getIdLogin(), idContaPagamento)))
            .collect(Collectors.toList());

    return portadorLoginContaRepository.saveAll(portadorLoginContaList);
  }

  public void adicionaPortadorMultiConta(PortadorLogin portadorLogin, Long idContaPagamento) {

    if (portadorLogin == null || idContaPagamento == null) {
      return;
    }

    PortadorLoginContaId portadorLoginContaId =
        new PortadorLoginContaId(portadorLogin.getIdLogin(), idContaPagamento);

    if (portadorLoginContaRepository.existsById(portadorLoginContaId)) {
      return;
    }

    PortadorLoginConta portadorLoginConta = new PortadorLoginConta(portadorLoginContaId);

    portadorLoginContaRepository.save(portadorLoginConta);
  }
}
