package br.com.sinergico.service.suporte;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import br.com.entity.cadastral.ProdutoCondicoes;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.BuscaB2B;
import br.com.sinergico.repository.suporte.HierarquiaPontoRelacionamentoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.ProdutoCondicoesService;
import br.com.sinergico.service.cadastral.ProdutoContratadoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class HierarquiaPontoRelacionamentoServiceTest {

  private static final Integer ID_PROCESSADORA = 10;
  private static final Integer ID_PROD_INSTITUICAO = 990107;
  private static final Integer ID_INSTITUICAO = 9901;
  private static final Integer ID_REGIONAL = 98;
  private static final Integer ID_FILIAL = 99;
  private static final Integer ID_PONTO_DE_RELACIONAMENTO = 9999;

  @Nested
  class ProdutosPassiveisDeContratacao {

    @Mock private ProdutoCondicoesService produtoCondicoesService;

    @Mock private ProdutoContratadoService produtoContratadoService;

    @Mock private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

    @InjectMocks private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

    private ProdutoCondicoes produtoCondicao;
    private ProdutoContratado produtoContratado;

    @BeforeEach
    void setUp() {
      MockitoAnnotations.openMocks(this);
      produtoCondicao = new ProdutoCondicoes();
      produtoCondicao.setIdProdInstituicao(ID_PROD_INSTITUICAO);
      produtoCondicao.setIsProdutoB2b(Boolean.TRUE);

      produtoContratado = new ProdutoContratado();
      produtoContratado.setIdProdInstituicao(ID_PROD_INSTITUICAO);
    }

    @Test
    void deveLancarExcessaoCasoNaoEncontreProdutoComCondicoesParaContratar() {
      when(produtoCondicoesService.findByIdProcessadoraAndIdInstituicao(anyInt(), anyInt()))
          .thenReturn(Collections.emptyList());

      GenericServiceException exception =
          assertThrows(
              GenericServiceException.class,
              () ->
                  hierarquiaPontoRelacionamentoService.getProdutosPassiveisDeContratacao(
                      ID_PROCESSADORA,
                      ID_INSTITUICAO,
                      ID_REGIONAL,
                      ID_FILIAL,
                      ID_PONTO_DE_RELACIONAMENTO));

      assertEquals(
          "Instituição não possui produtos com condições de contratação", exception.getMessage());
    }

    @Test
    void deveRetornarListaDeProdutoCondicoes() {
      when(produtoCondicoesService.findByIdProcessadoraAndIdInstituicao(anyInt(), anyInt()))
          .thenReturn(Arrays.asList(produtoCondicao));

      List<ProdutoCondicoes> produtoCondicoesList =
          hierarquiaPontoRelacionamentoService.getProdutosPassiveisDeContratacao(
              ID_PROCESSADORA, ID_INSTITUICAO, ID_REGIONAL, ID_FILIAL, ID_PONTO_DE_RELACIONAMENTO);

      assertEquals(1, produtoCondicoesList.size());
    }
  }

  @Nested
  class buscarEmpresaB2BPorHierarquiaInsituicao {

    @Mock private HierarquiaPontoRelacionamentoRepository repo;

    @Mock private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

    @InjectMocks private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

    @BeforeEach
    void setUp() {
      MockitoAnnotations.openMocks(this);
    }

    public List<ProdutoContratado> criarListaDeProdutosContratados() {
      List<ProdutoContratado> produtoContratadoList = new ArrayList<>();
      ProdutoContratado produtoContratado = new ProdutoContratado();
      produtoContratado.setIdContrato(1L);
      produtoContratado.setDtHrInclusao(new Date());
      produtoContratado.setDtContrato(new Date());
      produtoContratado.setIdProdInstituicao(ID_PROD_INSTITUICAO);
      produtoContratado.setDtHrCancelamento(new Date());
      produtoContratado.setIdUsuarioInclusao(101);
      produtoContratado.setIdUsuarioCancelamento(102);
      produtoContratado.setTaxaCarga(new BigDecimal("1.23"));
      produtoContratado.setTarifaCarga(new BigDecimal("2.34"));
      produtoContratado.setTarifaEmissao(new BigDecimal("3.45"));
      produtoContratado.setTarifaReposicao(new BigDecimal("4.56"));
      produtoContratado.setTarifaEmissaoDescartavel(new BigDecimal("5.67"));
      produtoContratado.setTarifaValorFatura(new BigDecimal("6.78"));
      produtoContratado.setValorFaturaMin(new BigDecimal("7.89"));
      produtoContratado.setPrazoPagamento(30);
      produtoContratado.setIdProcessadora(201);
      produtoContratado.setIdInstituicao(301);
      produtoContratado.setIdRegional(401);
      produtoContratado.setIdFilial(501);
      produtoContratado.setIdPontoRelacionamento(601);
      produtoContratado.setReestabelecimentoLimite(1);
      produtoContratado.setDiasCorte("1");
      produtoContratado.setPermitirNomeImpresso(1);
      produtoContratado.setTarifaCargaEmergencial(new BigDecimal("8.90"));
      produtoContratado.setTarifaDocTed(new BigDecimal("9.01"));
      produtoContratado.setTarifaOp(new BigDecimal("10.11"));
      produtoContratado.setTarifaPostagem(new BigDecimal("11.12"));
      produtoContratado.setTarifaRenovacao(new BigDecimal("12.13"));
      produtoContratado.setTipoPostagemProduto(1);
      produtoContratado.setIdentificadorExterno("EXT-123");
      produtoContratado.setPermiteCarga(1);
      produtoContratado.setNomeCartaoImpresso("NOME EXEMPLO");
      produtoContratado.setTipoCobrancaReposicao(2);
      produtoContratado.setIdentificadorJCard(987654321L);
      produtoContratado.setIdContratoCorrespondente(123456789L);
      produtoContratado.setDataHoraReplicacao(new Date());
      produtoContratado.setTarifaConvenio(new BigDecimal("13.14"));
      produtoContratado.setTaxaConvenio(new BigDecimal("14.15"));
      produtoContratado.setTxJuroChequeEspecial(new BigDecimal("15.16"));
      produtoContratado.setPercentualChequeEspecial(new BigDecimal("16.17"));
      produtoContratado.setIdContract(202L);
      produtoContratado.setPrimeiroCartaoVirtual(1);
      produtoContratado.setPermiteCartaoFisico(Boolean.TRUE);
      produtoContratado.setFilialFaturamento(808);
      produtoContratado.setPermiteCargaB2b(Boolean.TRUE);

      ProdutoContratado produtoContratado2 = new ProdutoContratado();
      produtoContratado2.setIdContrato(2L);
      produtoContratado2.setDtHrInclusao(new Date());
      produtoContratado2.setDtContrato(new Date());
      produtoContratado2.setIdProdInstituicao(9991111);
      produtoContratado2.setDtHrCancelamento(new Date());
      produtoContratado2.setIdUsuarioInclusao(102);
      produtoContratado2.setIdUsuarioCancelamento(103);
      produtoContratado2.setTaxaCarga(new BigDecimal("1.23"));
      produtoContratado2.setTarifaCarga(new BigDecimal("2.34"));
      produtoContratado2.setTarifaEmissao(new BigDecimal("3.45"));
      produtoContratado2.setTarifaReposicao(new BigDecimal("4.56"));
      produtoContratado2.setTarifaEmissaoDescartavel(new BigDecimal("5.67"));
      produtoContratado2.setTarifaValorFatura(new BigDecimal("6.78"));
      produtoContratado2.setValorFaturaMin(new BigDecimal("7.89"));
      produtoContratado2.setPrazoPagamento(30);
      produtoContratado2.setIdProcessadora(2011);
      produtoContratado2.setIdInstituicao(3012);
      produtoContratado2.setIdRegional(4013);
      produtoContratado2.setIdFilial(5014);
      produtoContratado2.setIdPontoRelacionamento(6012);
      produtoContratado2.setReestabelecimentoLimite(1);
      produtoContratado2.setDiasCorte("2");
      produtoContratado2.setPermitirNomeImpresso(1);
      produtoContratado2.setTarifaCargaEmergencial(new BigDecimal("8.90"));
      produtoContratado2.setTarifaDocTed(new BigDecimal("9.01"));
      produtoContratado2.setTarifaOp(new BigDecimal("10.11"));
      produtoContratado2.setTarifaPostagem(new BigDecimal("11.12"));
      produtoContratado2.setTarifaRenovacao(new BigDecimal("12.13"));
      produtoContratado2.setTipoPostagemProduto(1);
      produtoContratado2.setIdentificadorExterno("EXT-123");
      produtoContratado2.setPermiteCarga(1);
      produtoContratado2.setNomeCartaoImpresso("NOME EXEMPLO");
      produtoContratado2.setTipoCobrancaReposicao(2);
      produtoContratado2.setIdentificadorJCard(987654321L);
      produtoContratado2.setIdContratoCorrespondente(123456789L);
      produtoContratado2.setDataHoraReplicacao(new Date());
      produtoContratado2.setTarifaConvenio(new BigDecimal("13.14"));
      produtoContratado2.setTaxaConvenio(new BigDecimal("14.15"));
      produtoContratado2.setTxJuroChequeEspecial(new BigDecimal("15.16"));
      produtoContratado2.setPercentualChequeEspecial(new BigDecimal("16.17"));
      produtoContratado2.setIdContract(202L);
      produtoContratado2.setPrimeiroCartaoVirtual(1);
      produtoContratado2.setPermiteCartaoFisico(Boolean.TRUE);
      produtoContratado2.setFilialFaturamento(808);
      produtoContratado2.setPermiteCargaB2b(Boolean.TRUE);

      produtoContratadoList.add(produtoContratado);
      produtoContratadoList.add(produtoContratado2);

      return produtoContratadoList;
    }

    @Test
    void deveRetornarProdutosContratados() {

      SecurityUser user = new SecurityUser();
      user.setIdUsuario(2054);
      user.setIdProcessadora(ID_PROCESSADORA);
      user.setIdInstituicao(ID_INSTITUICAO);
      user.setIdRegional(ID_REGIONAL);
      user.setIdFilial(ID_FILIAL);
      user.setIdPontoDeRelacionamento(ID_PONTO_DE_RELACIONAMENTO);
      user.setIdHierarquiaNivel(1);

      BuscaB2B buscaB2B = new BuscaB2B();
      buscaB2B.setId(ID_PONTO_DE_RELACIONAMENTO);
      buscaB2B.setIdInstituicao(ID_INSTITUICAO);
      buscaB2B.setIdProcessadora(ID_PROCESSADORA);
      buscaB2B.setIdRegional(ID_REGIONAL);
      buscaB2B.setIdFilial(ID_FILIAL);

      HierarquiaPontoDeRelacionamento hpr = new HierarquiaPontoDeRelacionamento();
      List<ProdutoContratado> produtoContratadoList = new ArrayList<>();

      ProdutoInstituicaoConfiguracao prodInstConf = new ProdutoInstituicaoConfiguracao();

      lenient()
          .when(
              repo
                  .findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
                      ID_PROCESSADORA,
                      ID_INSTITUICAO,
                      ID_REGIONAL,
                      ID_FILIAL,
                      ID_PONTO_DE_RELACIONAMENTO,
                      Boolean.TRUE))
          .thenReturn(hpr);

      hpr.setIdInstituicao(ID_INSTITUICAO);
      hpr.setIdProcessadora(ID_PROCESSADORA);
      hpr.setProdutosContratados(this.criarListaDeProdutosContratados());

      for (ProdutoContratado pc : hpr.getProdutosContratados()) {

        lenient()
            .when(
                produtoInstituicaoConfiguracaoService
                    .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                        hpr.getIdProcessadora(), pc.getIdProdInstituicao(), hpr.getIdInstituicao()))
            .thenReturn(prodInstConf);

        pc.setTipoProduto(prodInstConf.getTipoProduto());
        pc.setIdGrupoProduto(prodInstConf.getIdGrupoProduto());

        produtoContratadoList.add(pc);
        hpr.setProdutosContratados(produtoContratadoList);

        assertNotNull(pc.getPermiteCargaB2b());
      }

      hierarquiaPontoRelacionamentoService.getB2bByHierarquia(buscaB2B, user);

      assertEquals(2, hpr.getProdutosContratados().size());
    }
  }
}
