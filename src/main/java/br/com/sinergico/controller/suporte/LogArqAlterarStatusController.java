package br.com.sinergico.controller.suporte;

import br.com.entity.suporte.LogArqAlterarStatus;
import br.com.entity.suporte.LogRegistroAlterarStatus;
import br.com.json.bean.suporte.VerificacaoArqAlterarStatusCredencialTO;
import br.com.json.bean.suporte.VerificacaoArqAlterarStatusTO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.LogArqAlterarStatusService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("api/log-arq-alterar-status")
public class LogArqAlterarStatusController extends UtilController {

  @Autowired private LogArqAlterarStatusService logArqAlterarStatusService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @ApiOperation(
      value = "Serviço responsável por verificar processamento de arquivo de mudança de status")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/listar-arquivos-alterar-status/idInstituicao/{idInstituicao}/{first}/{max}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LER_ARQUIVO_ALTERAR_STATUS"})
  public ResponseEntity<List<LogArqAlterarStatus>> listarProcessamentoArquivoAlterarStatus(
      @PathVariable("idInstituicao") Integer idInstituicao,
      @PathVariable("first") Integer first,
      @PathVariable("max") Integer max) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LogArqAlterarStatus> resp;

    if (idInstituicao != 0) {
      resp =
          logArqAlterarStatusService.listarProcessamentoArquivoAlterarStatusPorInstituicao(
              idInstituicao, first, max);
    } else {
      resp =
          logArqAlterarStatusService.listarProcessamentoArquivoAlterarStatusSemInstituicao(
              first, max);
    }

    return new ResponseEntity<>(resp, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por contar arquivos de mudança de status")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/contar-arquivos-alterar-status/idInstituicao/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LER_ARQUIVO_ALTERAR_STATUS"})
  public ResponseEntity<Long> contarProcessamentoArquivoAlterarStatus(
      @PathVariable("idInstituicao") Integer idInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count;

    if (idInstituicao != 0) {
      count = logArqAlterarStatusService.contaTodosPorInstituicao(idInstituicao);
    } else {
      count = logArqAlterarStatusService.contaTodos();
    }

    return new ResponseEntity<>(count, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por fazer upload de um arquivo de mudança de status")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/upload-arquivo-alterar-status/tipo-arq/{tipoArq}/idInstituicao/{idInstituicao}/qtdRegistros/{qtdRegistros}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ESCREVER_ARQUIVO_ALTERAR_STATUS"})
  public ResponseEntity<Map<String, Object>> uploadArquivoAlterarStatus(
      @RequestBody MultipartFile file,
      @PathVariable("tipoArq") Integer tipoArq,
      @PathVariable("idInstituicao") Integer idInstituicao,
      @PathVariable("qtdRegistros") Integer qtdRegistros)
      throws IOException, NoSuchAlgorithmException {

    SecurityUser user = getAuthenticatedUser(request, em);
    user.checkHierarquiaUsuarioLogadoInstituicao(idInstituicao);
    LogArqAlterarStatus.TipoArquivoAlterarEnum tipoArqEnum =
        LogArqAlterarStatus.TipoArquivoAlterarEnum.values()[tipoArq];
    return logArqAlterarStatusService.uploadArquivoAlterarStatus(
        file, user, tipoArqEnum, idInstituicao, qtdRegistros);
  }

  @ApiOperation(value = "Serviço reponsável por cancelar um arquivo para que não seja lido")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cancelar-arquivo-alterar-status/idArq/{idArq}",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ESCREVER_ARQUIVO_ALTERAR_STATUS"})
  public ResponseEntity<Boolean> cancelarArquivoAlterarStatus(@PathVariable("idArq") Long idArq) {

    return new ResponseEntity<>(logArqAlterarStatusService.cancelarArquivo(idArq), HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por verificar um arquivo de mudança de status")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/verificar-arquivo-alterar-status/tipo-arq/{tipoArq}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ESCREVER_ARQUIVO_ALTERAR_STATUS"})
  public ResponseEntity<?> verificaAlterarStatusArquivo(
      @RequestParam("file") MultipartFile file, @PathVariable("tipoArq") Integer tipoArq) {

    SecurityUser user = getAuthenticatedUser(request, em);
    VerificacaoArqAlterarStatusTO to = null;
    VerificacaoArqAlterarStatusCredencialTO tos = null;
    try {
      if (tipoArq.equals(1)) {
        to =
            logArqAlterarStatusService.verificaAlterarStatusViaArquivo(
                file.getInputStream(), file.getOriginalFilename(), user);
      } else {
        tos =
            logArqAlterarStatusService.verificaAlterarStatusViaArquivoCredencial(
                file.getInputStream(), file.getOriginalFilename(), user);
      }
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar verificar o arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }
    if (tipoArq.equals(1)) {
      return new ResponseEntity<>(to, HttpStatus.OK);
    } else {
      return new ResponseEntity<>(tos, HttpStatus.OK);
    }
  }

  @ApiOperation(value = "Serviço responsável por trazer registros rejeitados de mudança de status")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/detalhe-registro-alterar-status/idArq/{idArq}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LER_ARQUIVO_ALTERAR_STATUS"})
  ResponseEntity<List<LogRegistroAlterarStatus>> listarRegistrosRejeitados(
      @PathVariable("idArq") Long idArq) {
    List<LogRegistroAlterarStatus> response =
        logArqAlterarStatusService.listarRegistrosRejeitados(idArq);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }
}
