package br.com.sinergico.controller.corporativo;

import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.CadastroResponsavelVO;
import br.com.json.bean.cadastral.ContaEnderecoPessoaDTO;
import br.com.json.bean.cadastral.CorporativoLoginDTO;
import br.com.json.bean.cadastral.CorporativoLoginRetornoDTO;
import br.com.json.bean.cadastral.CountResponseLong;
import br.com.json.bean.cadastral.DesvincularCredencialVO;
import br.com.json.bean.cadastral.DetalhesPortadorLogin;
import br.com.json.bean.cadastral.OnboardCorporativoVO;
import br.com.json.bean.cadastral.PortadorLoginCorporativo;
import br.com.json.bean.suporte.RedefinirSenhaPortadorCorporativoVO;
import br.com.json.bean.suporte.ValidarCadastroOnboardCorporativoVO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.service.corporativo.CorporativoLoginService;
import br.com.sinergico.service.corporativo.CorporativoService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.vo.AplicativoServicoVO;
import br.com.sinergico.vo.ContasEmLoteVO;
import br.com.sinergico.vo.ResponsavelCorporativoVO;
import br.com.sinergico.vo.TransferenciaContaVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/corporativo")
public class CorporativoController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private CorporativoService corporativoService;

  @Autowired private CorporativoLoginService corporativoLoginService;

  @Autowired private TravaServicosService travaServicosService;

  @ApiOperation(value = "Cadastro de um responsável corporativo")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cadastrar/responsavel",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CORPORATIVO_CADASTRAR_RESPONSAVEL"})
  public ResponseEntity<?> cadastrarResponsavel(
      @RequestBody @Valid CadastroResponsavelVO cadastroResponsavelVO) {

    SecurityUser user = getAuthenticatedUser(request, em);
    corporativoService.cadastrarResponsavel(user, cadastroResponsavelVO);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Cadastro realizado com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @RequestMapping(value = "/auth", method = RequestMethod.POST)
  @ApiOperation(
      notes = "",
      value = "Login do corporativo no sistema via aplicativo",
      response = CorporativoLoginRetornoDTO.class)
  public ResponseEntity<CorporativoLoginRetornoDTO> realizarLogin(
      @RequestBody @Valid CorporativoLoginDTO login,
      HttpServletRequest request,
      BindingResult result)
      throws IOException {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          "Erro na validação de login do corporativo portador.", result);
    }

    // todo verificar se vale a pena criar trava de corporativo login
    travaServicosService.travaServicos(login.getIdInstituicao(), Servicos.LOGIN_PORTADOR);

    return corporativoLoginService.realizarLogin(request, login);
  }

  @ApiOperation(value = "Busca responsavel pelo documento", response = CorporativoResponsavel.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar/responsavel/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Secured({"ROLE_CORPORATIVO_BUSCAR_RESPONSAVEL"})
  public CorporativoResponsavel buscarResponsavel(@PathVariable String documento) {

    return corporativoService.findByDocumento(documento);
  }

  @ApiOperation(
      value = "Busca lista de responsavel corporativo",
      response = CorporativoResponsavel.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar/responsavel/credencial/{idCredencial}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Secured({"ROLE_CORPORATIVO_LISTA_CREDENCIAL_RESPONSAVEL"})
  public List<ResponsavelCorporativoVO> listaResponsavelCorporativo(
      @PathVariable Long idCredencial) {

    return corporativoService.listaResponsavelCorporativo(idCredencial);
  }

  @ApiOperation(
      value = "Busca responsavel ativo pela credencial",
      response = CorporativoResponsavel.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar/responsavel/ativo/{idCredencial}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_VALUE)
  @Secured({"ROLE_CORPORATIVO_BUSCAR_RESPONSAVEL"})
  public CorporativoResponsavel buscaResponsavelAtivo(@PathVariable Long idCredencial) {

    return corporativoService.buscarResponsavelAtivo(idCredencial);
  }

  @ApiOperation(
      value = "Desvincular credencial do responsavel",
      response = CorporativoResponsavel.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/desvincular/credencial",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CORPORATIVO_DESVINCULAR_RESPONSAVEL"})
  public ResponseEntity<?> desvincularCredencial(
      @RequestBody @Valid DesvincularCredencialVO desvincularCredencialVO) {

    SecurityUser user = getAuthenticatedUser(request, em);
    corporativoService.desvincularCredencial(user, desvincularCredencialVO, getTokenJWT(request));
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Desvinculo realizado com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por validar cadastro para onboard do corporativo",
      response = DetalhesPortadorLogin.class,
      notes = "Validar cadastro para onboard")
  @RequestMapping(
      value = "/validar-cadastro-onboard",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<ValidarCadastroOnboardCorporativoVO> validarCadastroOnboard(
      @RequestBody OnboardCorporativoVO request) throws IOException {

    ValidarCadastroOnboardCorporativoVO validacao =
        corporativoLoginService.validarCadastroOnboard(request);
    return new ResponseEntity<>(validacao, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por cadastrar login portador")
  @RequestMapping(
      value = "/cadastrar-portador-login",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, Object>> cadastrarPortadorLogin(
      @RequestBody @Valid PortadorLoginCorporativo portadorLoginCorporativo, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados inválidos.", result);
    }

    corporativoLoginService.cadastrarPortadorLogin(portadorLoginCorporativo);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Login cadastrado com sucesso");
    map.put("created", true);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @RequestMapping(
      value = "/redefinir-senha-login",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por definir a senha do portador corporativo")
  public ResponseEntity<?> redefinirSenha(
      @RequestBody @Valid RedefinirSenhaPortadorCorporativoVO redefinirSenhaPortadorCorporativoVO,
      BindingResult result,
      HttpServletRequest request) {

    if (result.hasErrors()) {
      throw new InvalidRequestException("Dados Inválidos", result);
    }

    corporativoLoginService.redefinirSenhaPortador(redefinirSenhaPortadorCorporativoVO, request);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Senha alterada com sucesso");
    map.put("sucesso", true);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar dados de endereco de uma pessoa vinculada a uma conta",
      response = ContaEnderecoPessoaDTO.class)
  @RequestMapping(
      value = "/enderecos",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  public ResponseEntity<List<ContaEnderecoPessoaDTO>> buscarEnderecos(
      HttpServletRequest httpServletRequest) {

    SecurityUserCorporativo userCorporativo = getAuthenticatedCorporativo(httpServletRequest, em);

    List<ContaEnderecoPessoaDTO> enderecos =
        corporativoService.buscarEnderecoContas(userCorporativo);

    return new ResponseEntity<List<ContaEnderecoPessoaDTO>>(enderecos, HttpStatus.OK);
  }

  @ApiOperation(value = "Listar os servicos aplicativo frontend")
  @RequestMapping(
      value = "/listar-servicos/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<List<AplicativoServicoVO>> listarServicos(
      @PathVariable Integer idInstituicao) {

    List<AplicativoServicoVO> servicos = corporativoService.listarServicos(idInstituicao, true);
    return new ResponseEntity<List<AplicativoServicoVO>>(servicos, HttpStatus.OK);
  }

  @ApiOperation(value = "Listar contas para transferencia em lote")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path =
          "/listar-contas/{idProcessadora}/{idInstituicao}/{idRegional}/{idFilial}/{idPontoRelacionamento}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CORPORATIVO_LISTAR_CONTAS_TRANSFERENCIA"})
  public ResponseEntity<?> listaTransferenciaContas(
      @RequestBody TransferenciaContaVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idRegional,
      @PathVariable Integer idFilial,
      @PathVariable Integer idPontoRelacionamento) {

    List<ContasEmLoteVO> retorno =
        corporativoService.listaTransferenciaContas(
            model, idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar contas para transferencia em lote")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path =
          "/count/listar-contas/{idProcessadora}/{idInstituicao}/{idRegional}/{idFilial}/{idPontoRelacionamento}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CORPORATIVO_LISTAR_CONTAS_TRANSFERENCIA"})
  public ResponseEntity<CountResponseLong> countListarContas(
      @RequestBody TransferenciaContaVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idRegional,
      @PathVariable Integer idFilial,
      @PathVariable Integer idPontoRelacionamento) {

    Long count =
        corporativoService.countListarContas(
            model, idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar conta base")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path =
          "/buscar-conta-base/{idProcessadora}/{idInstituicao}/{idRegional}/{idFilial}/{idPontoRelacionamento}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CORPORATIVO_BUSCAR_CONTA_BASE"})
  public ResponseEntity<?> buscarContaBase(
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idRegional,
      @PathVariable Integer idFilial,
      @PathVariable Integer idPontoRelacionamento) {

    List<ContasEmLoteVO> retorno =
        corporativoService.buscarContaBaseEmLoteVO(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Realizar transferencia de saldo em lote")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/transferencia-saldo/lote",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CORPORATIVO_TRANSFERENCIA_LOTE"})
  public ResponseEntity<?> buscarContaBase(@RequestBody List<ContasEmLoteVO> model) {

    corporativoService.transferenciaEmLote(model);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Transferência realizada com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }
}
