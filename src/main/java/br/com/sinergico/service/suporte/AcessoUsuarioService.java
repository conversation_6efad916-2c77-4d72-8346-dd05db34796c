package br.com.sinergico.service.suporte;

import static br.com.sinergico.util.MyHibernateUtils.listAndCast;

import br.com.entity.suporte.AcessoGrupo;
import br.com.entity.suporte.AcessoGrupoUsuario;
import br.com.entity.suporte.AcessoGrupoUsuarioId;
import br.com.entity.suporte.AcessoHistoricoSenha;
import br.com.entity.suporte.AcessoLog;
import br.com.entity.suporte.AcessoTemporarioB2B;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioUnico;
import br.com.entity.suporte.AcessoUsuarioUnicoView;
import br.com.entity.suporte.HierarquiaNivel;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.HierarquiaProcessadora;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.suporte.TipoB2B;
import br.com.entity.suporte.TokenFuncionalidade;
import br.com.enumVO.TipoTokenFuncionalidadeEnum;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.BuscaGenericaFiltro;
import br.com.json.bean.enums.EnumServicesVallooAuthenticator;
import br.com.json.bean.enums.MecanismosAutenticacao;
import br.com.json.bean.suporte.AcessoUsuarioDTO;
import br.com.json.bean.suporte.AcessoUsuarioRequest;
import br.com.json.bean.suporte.CadastrarUsuario;
import br.com.json.bean.suporte.GenerateLoginTemporario;
import br.com.json.bean.suporte.InfoLoginUnicoCache;
import br.com.json.bean.suporte.LoginTemporarioResponse;
import br.com.json.bean.suporte.TrocarSenha;
import br.com.json.bean.suporte.UpdateUsuario;
import br.com.json.bean.suporte.UsuarioAtribuirChamado;
import br.com.json.bean.suporte.VincularGrupoUsuario;
import br.com.sinergico.controller.Token;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.criteria.UsuarioCriteria;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.enums.StatusUsuario;
import br.com.sinergico.repository.suporte.AcessoLogRepository;
import br.com.sinergico.repository.suporte.AcessoUsuarioRepository;
import br.com.sinergico.repository.suporte.HierarquiaNivelRepository;
import br.com.sinergico.repository.suporte.HierarquiaProcessadoraRepository;
import br.com.sinergico.repository.suporte.impl.InfoLoginUnicoCacheRepository;
import br.com.sinergico.security.PasswordValidatorService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.TokenAuthenticationService;
import br.com.sinergico.security.UserAuthentication;
import br.com.sinergico.security.UserService;
import br.com.sinergico.security.UsuarioUnicoService;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.suporte.valloo.authenticator.VallooAuthenticatorService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.DocumentoUtil;
import br.com.sinergico.util.Hierarquia;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import javax.persistence.NoResultException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.hibernate.Criteria;
import org.hibernate.criterion.Order;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class AcessoUsuarioService extends GenericService<AcessoUsuario, Integer> {

  private static final int INATIVO = 0;
  private static final int CANCELADO = 9;
  private static final int BLOQUEIO_TEMPORARIO = 2;
  private static final int NIVEL_PROCESSADORA = 1;
  private static final int NIVEL_INSTITUICAO = 2;
  private static final int NIVEL_REGIONAL = 3;
  private static final int NIVEL_FILIAL = 4;
  private static final int NIVEL_PONTO_DE_RELACIONAMENTO = 5;
  private static final Integer QUANTIDADE_HISTORICO_SENHA = 5;
  public static final String IP_EXTERNO_ESCRITORIO_KREDIT = "**************";
  public static final String USUARIO_OU_SENHA_INVALIDO = "Usuário ou senha inválido";
  public static final String ACESSO_NAO_PERMITIDO = "Acesso não permitido.";
  public static final String TOKEN_INVALIDO = "Token inválido.";
  public static final String TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO =
      "Token informado não pertence ao usuário.";
  public static final String TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO =
      "Token informado é inválido ou está expirado";

  public static final String SERVICO_AUTENTICACAO_DESCONHECIDO =
      "Serviço de autenticação desconhecido";

  public static final String USUARIO_NAO_CADASTRO_SENHA_INVALIDA =
      "Usuario Não Cadastrado ou Senha Inválida.";
  public static final String SESSAO_USUARIO_INEXISTENTE = "A sessão do usuário unico não existe.";

  public static final String USUARIO_NAO_CADASTRO_PORTAL =
      "Acesso Negado.Usuário não pertence a este Portal.";
  public static final String SUCESSO = "Login realizado com sucesso.";

  private AcessoUsuarioRepository usuarioRepo;

  @Autowired private UsuarioCriteria usuarioCriteria;

  @Autowired private TipoB2BService tipoB2BService;

  @Autowired private UserService userService;

  @Autowired private UsuarioUnicoService usuarioUnicoService;

  @Autowired private AcessoLogService acessoLogService;

  @Autowired private HierarquiaProcessadoraRepository processadoraRepository;

  @Autowired private HierarquiaNivelRepository hierarquiaNivelRepository;

  @Autowired private PasswordEncoder passwordEncoder;

  @Autowired private HttpSession session;

  @Lazy @Autowired private EmailService emailService;

  @Lazy @Autowired private HierarquiaPontoRelacionamentoService ptRelService;

  @Autowired private AcessoTemporarioB2BService acessoTemporarioB2BService;

  @Autowired private AcessoLogRepository acessoLogRepo;

  @Autowired private AcessoGrupoUsuarioService acessoGrupoUsuarioService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private TokenFuncionalidadeService tokenFuncionalidadeService;

  @Autowired private GrupoService grupoService;

  @Autowired private ParametroValorService parametroValorService;

  @Autowired LogRequestErrorIpBlacklistService logRequestErrorIpBlacklistService;

  @Autowired private PasswordValidatorService passwordValidatorService;

  @Autowired private VallooAuthenticatorService vallooAuthenticatorService;

  @Autowired private InfoLoginUnicoCacheRepository infoLoginUnicoCacheRepository;

  @Autowired private AcessoUsuarioUnicoService acessoUsuarioUnicoService;

  @Autowired private TokenAuthenticationService tokenAuthenticationService;

  @Autowired private UtilService utilService;

  @Value("${url.troca.senha}")
  private String urlTrocaSenha;

  @Value("${url.troca.senha.b2b.infinancas}")
  private String urlTrocaSenhaB2bInfinancas;

  @Value("${url.troca.senha.b2b}")
  private String urlTrocaSenhaB2b;

  @Autowired
  public AcessoUsuarioService(AcessoUsuarioRepository usuarioRepo) {
    super(usuarioRepo);
    this.usuarioRepo = usuarioRepo;
  }

  public AcessoUsuario findByLoginAndSenha(String login, String senha) {
    AcessoUsuario usuario = usuarioRepo.findUsuarioByLoginWithGrupos(login.trim());
    if (usuario != null && isPassMatch(senha, usuario)) {
      return usuario;
    }
    return null;
  }

  public AcessoUsuario findByLoginSimples(String login) {
    return usuarioRepo.findUsuarioByLoginWithGrupos(login.trim());
  }

  public List<AcessoUsuario> findByLogin(String login) {
    return usuarioRepo.findByLoginStartingWithIgnoreCase(login.trim());
  }

  @Transactional
  public ResponseEntity<?> cadastrarUsuario(CadastrarUsuario model, SecurityUser user)
      throws GenericServiceException {

    grupoService.impedeTentativaDeAlterarUsuarioComPermissoesSuperiores(
        model.getGrupoUsuario(), user.getIdUsuario());

    List<AcessoUsuario> userExist = findByLogin(model.getLogin());
    for (AcessoUsuario acessoUsuario : userExist) {
      if (acessoUsuario.getLogin().toUpperCase().equals(model.getLogin())) {
        throw new GenericServiceException("O Login escolhido já está cadastrado!");
      }
    }
    if (!DocumentoUtil.isCPF(model.getCpf())) {
      throw new GenericServiceException("O CPF digitado não é válido!");
    }

    AcessoUsuario usuario = new AcessoUsuario();
    BeanUtils.copyProperties(model, usuario, getNullPropertyNames(model));
    usuario.setIdUsuarioInclusao(user.getIdUsuario());
    usuario.setIdHierarquiaNivel(
        HierarquiaType.getHierarquiaTypePelaHierarquia(
                model.getIdProcessadora(),
                model.getIdInstituicao(),
                model.getIdRegional(),
                model.getIdFilial(),
                model.getIdPontoDeRelacionamento())
            .value());
    usuario.setIdMecanismoAutenticacao(MecanismosAutenticacao.SMS.getCodigo());
    usuario.setVlIgnorarTokenAutenticacao(false);
    checksUsuario(usuario, user);

    if (usuario.getIdPontoDeRelacionamento() != null) {
      TipoB2B tipoB2b = tipoB2BService.findByInstituicao(usuario.getIdInstituicao());

      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              usuario.getIdProcessadora(),
              usuario.getIdInstituicao(),
              usuario.getIdRegional(),
              usuario.getIdFilial(),
              usuario.getIdPontoDeRelacionamento());
      Boolean b2b = ptRelService.findById(id).getB2b();

      if (b2b) {
        if (tipoB2b != null) {
          usuario.setTipoB2B(tipoB2b.getId());
        } else {
          usuario.setTipoB2B(ConstantesB2B.TIPO_B2B_GENERICO);
        }
      }
    }

    String senhaRandom = generatePassword();
    usuario.setSenha(encodePassword(senhaRandom + usuario.getCpf()));
    usuario.setStatus(StatusUsuario.ATIVO.value());
    usuario.setSenhaExpirada(true);
    usuario.setDtHrInclusao(new Date());
    usuario.setDtHrStatus(new Date());
    usuario = usuarioRepo.save(usuario);

    // salvar grupo usuário
    for (Integer tmp : model.getGrupoUsuario().getIdGrupos()) {
      AcessoGrupoUsuarioId id = new AcessoGrupoUsuarioId(tmp, usuario.getIdUsuario());
      AcessoGrupoUsuario agu = new AcessoGrupoUsuario();
      agu.setId(id);
      agu.setDtHrInclusao(new Date());
      agu.setIdUsuarioInclusao(user.getIdUsuario());
      acessoGrupoUsuarioService.save(agu);
    }

    // enviar e-mail com senha
    String url = getUrlTrocaSenha(usuario);
    emailService.sendPasswordEmail(usuario, senhaRandom, url);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Usuario " + usuario.getNome() + " cadastrado com sucesso");
    map.put("id", usuario.getIdUsuario());
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  private void checksUsuario(AcessoUsuario userNewOrUpdate, SecurityUser user) {
    new Hierarquia.Builder()
        .usuario(user)
        .nivel(userNewOrUpdate.getIdHierarquiaNivel())
        .processadora(userNewOrUpdate.getIdProcessadora())
        .instituicao(userNewOrUpdate.getIdInstituicao())
        .regional(userNewOrUpdate.getIdRegional())
        .filial(userNewOrUpdate.getIdFilial())
        .checkHierarquiaPermission()
        .verificaUnidadesHierarquia();
  }

  public String findNomeById(Integer idUsuarioInclusao) {
    return usuarioRepo.findNomeByIdUsuario(idUsuarioInclusao);
  }

  public String getUrlTrocaSenha(AcessoUsuario usuario) {
    if (usuario.getIdPontoDeRelacionamento() != null) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              usuario.getIdProcessadora(),
              usuario.getIdInstituicao(),
              usuario.getIdRegional(),
              usuario.getIdFilial(),
              usuario.getIdPontoDeRelacionamento());
      Boolean b2b = ptRelService.findById(id).getB2b();
      if (!b2b) {
        return urlTrocaSenha;
      } else {
        if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
            usuario.getIdInstituicao())) {
          return urlTrocaSenhaB2bInfinancas;
        }
        return urlTrocaSenhaB2b;
      }
    } else {
      return urlTrocaSenha;
    }
  }

  @Transactional
  public ResponseEntity<?> doLogin(
      HttpServletRequest request,
      HttpServletResponse response,
      String login,
      String senha,
      String tokenMfa,
      String identificador) {

    if (request.getHeader("Proxy-Authorization") != null) {
      throw new GenericServiceException("Acesso impedido.", HttpStatus.UNAUTHORIZED);
    }

    HashMap<String, Object> map = new HashMap<>();
    ResponseEntity<?> validacaoUsuario =
        validarAcessoUsuario(login, senha, request, map, false, null);
    if (validacaoUsuario != null) {
      return validacaoUsuario;
    }

    AcessoUsuario usuario = getUsuarioLogin(login, senha);

    travaServicosService.travaServicos(usuario.getIdInstituicao(), Servicos.LOGIN_ISSUER);

    logRequestErrorIpBlacklistService.verificarIPBloqueado(request, usuario);

    // * Não validar o token de autenticacao se o usuário já tiver logado como Super Usuario
    boolean validarToken = true;
    InfoLoginUnicoCache infoLoginUnicoCache = null;
    AcessoUsuarioUnico acessoUsuarioUnico = null;
    if (identificador != null) {
      infoLoginUnicoCache = infoLoginUnicoCacheRepository.findById(identificador).orElse(null);
      if (infoLoginUnicoCache != null
          && identificador.equalsIgnoreCase(infoLoginUnicoCache.getIdUnico())) {
        List<AcessoUsuarioUnicoView> acessoUsuarioUnicoViews =
            acessoUsuarioUnicoService.getAllAcessoUsuarioUnicoViewByLoginAcessoUsuarioUnico(
                infoLoginUnicoCache.getLogin());
        AcessoUsuarioUnicoView acessoUsuarioUnicoViewExistente =
            acessoUsuarioUnicoViews.stream()
                .filter(
                    acessoUsuarioUnicoView ->
                        login.equalsIgnoreCase(acessoUsuarioUnicoView.getLoginAcessoUsuario()))
                .findFirst()
                .orElse(null);
        if (acessoUsuarioUnicoViewExistente != null) {
          validarToken = false;
          acessoUsuarioUnico =
              acessoUsuarioUnicoService.findByLogin(infoLoginUnicoCache.getLogin());
        } else {
          map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
          AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
          saveAcessoLog(
              acessoUsuarioSimples,
              request,
              HttpStatus.UNAUTHORIZED,
              USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
          return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
        }
      }
    }
    if (validarToken) {
      validarTokenLogin(usuario, request, login, tokenMfa);
    }

    AcessoLog ultimoAcesso =
        acessoLogRepo.findFirstByIdUsuarioOrderByDtHrAcessoDesc(usuario.getIdUsuario());
    usuario.setTipoAcesso(Constantes.ACESSO_ISSUER_NORMAL);
    usuario.setAcessoUsuarioUnico(acessoUsuarioUnico);
    Token tokenJwt = new Token(makeAuthenication(request, response, usuario, ultimoAcesso));

    if (utilService.isAmbienteProducao()
        && (usuario.getOrigemAcesso() == null || !usuario.getOrigemAcesso().equals(0))) {
      try {
        emailService.sendEmailNotificationLoginIssuer(usuario, request);
      } catch (Exception e) {
        e.printStackTrace();
      }
    }
    if (infoLoginUnicoCache != null) {
      infoLoginUnicoCacheRepository.delete(infoLoginUnicoCache);
    }
    saveAcessoLog(usuario, request, HttpStatus.OK, SUCESSO);
    return new ResponseEntity<>(tokenJwt, HttpStatus.OK);
  }

  private void validarTokenLogin(
      AcessoUsuario usuario, HttpServletRequest request, String login, String tokenMfa) {
    if (usuario.getOrigemAcesso() == null || !usuario.getOrigemAcesso().equals(0)) {
      if (verificarParametroValidarToken(usuario)) {
        if (tokenMfa == null || tokenMfa.isEmpty()) {
          saveAcessoLog(
              usuario,
              request,
              HttpStatus.UNAUTHORIZED,
              TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                  + " | Informação interna - Login: "
                  + login
                  + " Token nulo ou em branco");
          throw new GenericServiceException(
              TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
        }
        TokenFuncionalidade tokenFuncionalidade;
        try {
          if (utilService.isAmbienteProducao()) {
            if (MecanismosAutenticacao.VALLOO_AUTHENTICATOR
                .getCodigo()
                .equals(usuario.getIdMecanismoAutenticacao())) {
              vallooAuthenticatorService.validateToken(
                  usuario.getIdUsuario(),
                  tokenMfa,
                  EnumServicesVallooAuthenticator.ISSUER.getCodigo());
            } else {
              TipoTokenFuncionalidadeEnum tipoTokenFuncionalidadeEnum = null;
              if (MecanismosAutenticacao.SMS
                  .getCodigo()
                  .equals(usuario.getIdMecanismoAutenticacao())) {
                tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_SMS;
              } else if (MecanismosAutenticacao.EMAIL
                  .getCodigo()
                  .equals(usuario.getIdMecanismoAutenticacao())) {
                tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_EMAIL;
              } else if (MecanismosAutenticacao.WHATSAPP
                  .getCodigo()
                  .equals(usuario.getIdMecanismoAutenticacao())) {
                tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_WHATSAPP;
              } else if (MecanismosAutenticacao.TODOS
                  .getCodigo()
                  .equals(usuario.getIdMecanismoAutenticacao())) {
                tipoTokenFuncionalidadeEnum = TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_TODOS;
              }
              tokenFuncionalidade =
                  tokenFuncionalidadeService.utilizarTokenByFuncionalidade(
                      tokenMfa, tipoTokenFuncionalidadeEnum);
              if (tokenFuncionalidade == null) {
                saveAcessoLog(
                    usuario,
                    request,
                    HttpStatus.UNAUTHORIZED,
                    TOKEN_INVALIDO
                        + " | Informação interna - Login: "
                        + login
                        + " Token: "
                        + tokenMfa);
                throw new GenericServiceException(TOKEN_INVALIDO, HttpStatus.UNAUTHORIZED);
              }
              if (!tokenMfa.equalsIgnoreCase(tokenFuncionalidade.getToken().trim())) {
                saveAcessoLog(
                    usuario,
                    request,
                    HttpStatus.UNAUTHORIZED,
                    TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                        + " | Informação interna - Login: "
                        + login
                        + " Token nulo ou em branco");
                throw new GenericServiceException(
                    TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
              }
              if (!tokenFuncionalidade.getIdUsuarioIssuer().equals(usuario.getIdUsuario())) {
                saveAcessoLog(
                    usuario,
                    request,
                    HttpStatus.UNAUTHORIZED,
                    TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO
                        + " | Informação interna - Login: "
                        + login
                        + " Token: "
                        + tokenMfa);
                throw new GenericServiceException(
                    TOKEN_INFORMADO_NAO_PERTENCE_AO_USUARIO, HttpStatus.UNAUTHORIZED);
              }
            }
          } else {
            tokenFuncionalidade =
                tokenFuncionalidadeService.generateTokenFromInformacoesUsuario(usuario);
            if (!tokenMfa.equalsIgnoreCase(tokenFuncionalidade.getToken().trim())) {
              saveAcessoLog(
                  usuario,
                  request,
                  HttpStatus.UNAUTHORIZED,
                  TOKEN_INVALIDO
                      + " | Informação interna - Login: "
                      + login
                      + " Token: "
                      + tokenMfa);
              throw new GenericServiceException(TOKEN_INVALIDO, HttpStatus.UNAUTHORIZED);
            }
          }
        } catch (Exception e) {
          if (!usuario.getVlIgnorarTokenAutenticacao()) {
            saveAcessoLog(
                usuario,
                request,
                HttpStatus.UNAUTHORIZED,
                TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO
                    + " | Informação interna - Login: "
                    + login
                    + " Token: "
                    + tokenMfa);
            throw new GenericServiceException(
                TOKEN_INFORMADO_E_INVALIDO_OU_ESTA_EXPIRADO, HttpStatus.UNAUTHORIZED);
          }
        }
      } else {
        tokenFuncionalidadeService.utilizarTodosTokenUsuarioByFuncionalidade(
            usuario.getIdUsuario());
      }
    }
  }

  public boolean usuarioBloqueado(Integer status, HashMap<String, Object> map) {
    if (status != null
        && (BLOQUEIO_TEMPORARIO == status || CANCELADO == status || INATIVO == status)) {
      map.put("msg", "O usuário encontra-se inativo ou bloqueado temporariamente!");
      return true;
    }

    return false;
  }

  public boolean usuarioSenhaExpirada(Boolean senhaExpirada, HashMap<String, Object> map) {

    if (senhaExpirada != null && Boolean.TRUE.equals(senhaExpirada)) {
      map.put("msg", "Usuário encontra-se com a senha expirada!");
      map.put("senhaExpirada", true);
      return true;
    }
    return false;
  }

  public AcessoUsuario getUsuarioLogin(String login, String senha) {
    AcessoUsuario usuario = this.findByLoginAndSenha(login.toUpperCase(), senha);

    if (usuario != null && usuario.getIdHierarquiaNivel() == NIVEL_PONTO_DE_RELACIONAMENTO) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              usuario.getIdProcessadora(),
              usuario.getIdInstituicao(),
              usuario.getIdRegional(),
              usuario.getIdFilial(),
              usuario.getIdPontoDeRelacionamento());
      Boolean b2b = ptRelService.findById(id).getB2b();

      return b2b ? null : usuario;

    } else {
      return usuario;
    }
  }

  @Transactional
  public ResponseEntity<?> doLoginB2b(
      HttpServletRequest request,
      HttpServletResponse response,
      String login,
      String senha,
      Integer tipoB2B,
      String tokenMfa,
      String identificador) {

    if (request.getHeader("Proxy-Authorization") != null) {
      throw new GenericServiceException("Acesso impedido.", HttpStatus.UNAUTHORIZED);
    }

    HashMap<String, Object> map = new HashMap<>();
    ResponseEntity<?> validacaoUsuario =
        validarAcessoUsuario(login, senha, request, map, true, tipoB2B);
    if (validacaoUsuario != null) {
      return validacaoUsuario;
    }
    AcessoUsuario usuario = this.findByLoginSimples(login.toUpperCase());
    // * Não validar o token de autenticacao se o usuário já tiver logado como Super Usuario
    boolean validarToken = true;
    InfoLoginUnicoCache infoLoginUnicoCache = null;
    AcessoUsuarioUnico acessoUsuarioUnico = null;
    if (identificador != null) {
      infoLoginUnicoCache = infoLoginUnicoCacheRepository.findById(identificador).orElse(null);
      if (infoLoginUnicoCache != null
          && identificador.equalsIgnoreCase(infoLoginUnicoCache.getIdUnico())) {
        List<AcessoUsuarioUnicoView> acessoUsuarioUnicoViews =
            acessoUsuarioUnicoService.getAllAcessoUsuarioUnicoViewByLoginAcessoUsuarioUnico(
                infoLoginUnicoCache.getLogin());
        AcessoUsuarioUnicoView acessoUsuarioUnicoViewExistente =
            acessoUsuarioUnicoViews.stream()
                .filter(
                    acessoUsuarioUnicoView ->
                        login.equalsIgnoreCase(acessoUsuarioUnicoView.getLoginAcessoUsuario()))
                .findFirst()
                .orElse(null);
        if (acessoUsuarioUnicoViewExistente != null) {
          validarToken = false;
          acessoUsuarioUnico =
              acessoUsuarioUnicoService.findByLogin(infoLoginUnicoCache.getLogin());
          usuario.setAcessoUsuarioUnico(acessoUsuarioUnico);
        } else {
          map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
          AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
          saveAcessoLog(
              acessoUsuarioSimples,
              request,
              HttpStatus.UNAUTHORIZED,
              USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
          return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
        }
      }
    }
    if (validarToken) {
      validarTokenLogin(usuario, request, login, tokenMfa);
    }

    if (usuario != null
        && usuario.getIdHierarquiaNivel() == Constantes.NIVEL_PONTO_DE_RELACIONAMENTO) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              usuario.getIdProcessadora(),
              usuario.getIdInstituicao(),
              usuario.getIdRegional(),
              usuario.getIdFilial(),
              usuario.getIdPontoDeRelacionamento());

      Boolean b2b = ptRelService.findById(id).getB2b();
      if (b2b && usuario.getTipoB2B().equals(tipoB2B)) {
        return hasAcessoB2b(usuario, request, response, infoLoginUnicoCache);
      } else {
        return hasAcessoTemporario(login, senha, request, response);
      }
    } else {
      return hasAcessoTemporario(login, senha, request, response);
    }
  }

  @Transactional
  public ResponseEntity<?> hasAcessoB2b(
      AcessoUsuario usuario,
      HttpServletRequest request,
      HttpServletResponse response,
      InfoLoginUnicoCache infoLoginUnicoCache) {
    AcessoLog ultimoAcesso =
        acessoLogRepo.findFirstByIdUsuarioOrderByDtHrAcessoDesc(usuario.getIdUsuario());
    if (usuario.getTipoAcesso() == null) {
      usuario.setTipoAcesso(Constantes.B2B_ACESSO_NORMAL);
    }
    Token token = new Token(makeAuthenication(request, response, usuario, ultimoAcesso));
    if (infoLoginUnicoCache != null) {
      infoLoginUnicoCacheRepository.delete(infoLoginUnicoCache);
    }
    saveAcessoLog(usuario, request, HttpStatus.OK, SUCESSO);
    return new ResponseEntity<>(token, HttpStatus.OK);
  }

  private ResponseEntity<?> hasAcessoTemporario(
      String login, String senha, HttpServletRequest request, HttpServletResponse response) {

    LocalDateTime dataAtual = LocalDateTime.now();
    List<AcessoTemporarioB2B> acessosTemp =
        acessoTemporarioB2BService.findByLoginSolicitanteAndDataExpiracaoAfter(
            login.toUpperCase(), dataAtual);

    for (AcessoTemporarioB2B tmp : acessosTemp) {
      if (BCrypt.checkpw(senha + tmp.getCpfSolicitante(), tmp.getSenhaProvisoria())) {
        AcessoUsuario usuario = usuarioRepo.findByIdUsuario(tmp.getIdUsuarioSolicitante());
        AcessoUsuario usuarioSolicitado =
            usuarioRepo.findUsuarioByIdWithGrupos(tmp.getIdUsuarioSolicitado());

        AcessoUsuario usuarioTemp = new AcessoUsuario();

        BeanUtils.copyProperties(usuario, usuarioTemp, "acessoGrupos");
        usuarioTemp.setAcessoGrupos(usuarioSolicitado.getAcessoGrupos());
        usuarioTemp.setIdHierarquiaNivel(usuarioSolicitado.getIdHierarquiaNivel());
        usuarioTemp.setIdInstituicao(usuarioSolicitado.getIdInstituicao());
        usuarioTemp.setIdRegional(usuarioSolicitado.getIdRegional());
        usuarioTemp.setIdFilial(usuarioSolicitado.getIdFilial());
        usuarioTemp.setIdPontoDeRelacionamento(usuarioSolicitado.getIdPontoDeRelacionamento());
        usuarioTemp.setTipoAcesso(Constantes.B2B_ACESSO_VIA_ISSUER);
        return hasAcessoB2b(usuarioTemp, request, response, null);
      }
    }
    return null;
  }

  public String makeAuthenication(
      HttpServletRequest request,
      HttpServletResponse response,
      AcessoUsuario usuario,
      AcessoLog acessoLog) {

    SecurityUser securityUser = new SecurityUser(usuario);
    if (usuario.getTipoAcesso() != null) {
      securityUser.setTipoAcesso(usuario.getTipoAcesso());
    }
    userService.addUser(request.getSession(), securityUser);

    UserAuthentication userAuthentication = new UserAuthentication(securityUser);
    String userToken =
        tokenAuthenticationService.addAuthentication(request, userAuthentication, acessoLog);
    Authentication authentication = tokenAuthenticationService.getAuthentication(request);
    SecurityContextHolder.getContext().setAuthentication(authentication);
    return userToken;
  }

  /**
   * @param senha
   *     <p>(?=.*[0-9]) a digit must occur at least once (?=.*[a-z]) a lower case letter must occur
   *     at least once (?=.*[A-Z]) an upper case letter must occur at least once (?=.*[@#$%^&+=]) a
   *     special character must occur at least once (?=\\S+$) no whitespace allowed in the entire
   *     string .{8,} at least 8 characters
   * @return
   */
  public Boolean isPasswordWeak(String senha) {
    String pattern = "(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[!@#$%&*+=])(?=\\S+$).{5,20}";
    return !senha.matches(pattern);
  }

  public HierarquiaNivel getHierarquiaNivelById(Integer id) {
    HierarquiaNivel hierarquiaNivel = hierarquiaNivelRepository.findById(id).orElse(null);
    if (hierarquiaNivel == null) {
      throw new NoResultException("Nível da hierarquia não encontrado");
    }
    return hierarquiaNivel;
  }

  public HierarquiaProcessadora getHierarquiaProcessadoraById(Integer id) {
    HierarquiaProcessadora processadora = processadoraRepository.findById(id).orElse(null);
    if (processadora == null) {
      throw new NoResultException("Processadora não encontrada");
    }
    return processadora;
  }

  public String generatePassword() {
    return Long.toHexString(Double.doubleToLongBits(Math.random())).substring(0, 8);
  }

  public String encodePassword(String senha) {
    return passwordEncoder.encode(senha);
  }

  private boolean isPassMatch(String senha, AcessoUsuario usuario) {
    return BCrypt.checkpw(senha + usuario.getCpf(), usuario.getSenha());
  }

  public List<AcessoUsuario> findAll(Integer max, Integer first) {
    Criteria criteria = usuarioCriteria.getHierarquiaCriteria(AcessoUsuario.class, max, first);
    criteria.addOrder(Order.asc("nome"));
    criteria.setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY);
    List<AcessoUsuario> usuarios = listAndCast(criteria);
    return usuarios;
  }

  public void saveAcessoLog(
      AcessoUsuario usuario, HttpServletRequest request, HttpStatus status, String mensagem) {
    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");
    Enumeration<String> headerNames = request.getHeaderNames();
    JSONObject jsonHeader = new JSONObject();
    try {
      if (headerNames != null) {
        for (String headerName : Collections.list(headerNames)) {
          jsonHeader.put(headerName, request.getHeader(headerName));
        }
      }
    } catch (JSONException e) {
      jsonHeader = new JSONObject();
    }
    acessoLogService.saveAcessoLog(
        usuario, ipOrigem, jsonHeader.toString(), session.getId(), status.value(), mensagem);
  }

  public List<AcessoUsuario> findByPontoDeRelacionamento(AcessoUsuarioRequest model) {
    List<AcessoUsuario> usuarios =
        usuarioRepo
            .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamento(
                model.getIdProcessadora(),
                model.getIdInstituicao(),
                model.getIdRegional(),
                model.getIdFilial(),
                model.getIdPontoDeRelacionamento());
    return usuarios;
  }

  public AcessoUsuario findByIdUsuario(Integer idUsuario) {
    return usuarioRepo.findByIdUsuario(idUsuario);
  }

  private static boolean isValidInteger(String verificador) {
    return verificador.matches("\\d{1,9}|[\\p{Lower}\\p{Upper}\\p{Space}\\p{L}]+");
  }

  public String findDescricaoUsuario(Integer idUsuario) {
    return usuarioRepo.findDescricaoUsuario(idUsuario);
  }

  public boolean isPassMatch(String s1, String s2) {
    return BCrypt.checkpw(s1, s2);
  }

  public AcessoUsuario trocarPropriaSenha(TrocarSenha trocarSenha, SecurityUser user) {

    if (!passwordValidatorService.validate(
        trocarSenha.getNovaSenha(),
        Arrays.asList(user.getCpf(), user.getNome(), user.getLogin()))) {
      throw new GenericServiceException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números."
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.");
    }

    AcessoUsuario usuario = findByLoginAndSenha(user.getLogin(), trocarSenha.getSenha());
    if (usuario == null) {
      throw new BadCredentialsException("Login ou senha incorretos");
    }

    List<AcessoHistoricoSenha> senhas = new ArrayList<AcessoHistoricoSenha>();
    senhas.addAll(usuario.getAcessoHistoricoSenhas());

    AcessoHistoricoSenha senha = new AcessoHistoricoSenha();
    senha.setDataHoraTroca(new Date());
    for (AcessoHistoricoSenha a : senhas) {
      if (isPassMatch(trocarSenha.getNovaSenha(), a.getSenhaAnterior())) {
        throw new GenericServiceException(
            "Você não pode usar uma senha que já utilizou recentemente.");
      }
      if (senhas.size() == QUANTIDADE_HISTORICO_SENHA
          && a.getDataHoraTroca().before(senha.getDataHoraTroca())) {
        senha = a;
      }
    }
    senha.setAcessoUsuario(usuario);
    senhas.remove(senha);
    senha.setDataHoraTroca(new Date());
    senha.setSenhaAnterior(encodePassword(trocarSenha.getSenha() + usuario.getCpf()));
    senhas.add(senha);

    usuario.setSenhaExpirada(false);
    usuario.setSenha(encodePassword(trocarSenha.getNovaSenha() + usuario.getCpf()));
    usuario.setDtHrUltimaTrocaSenha(new Date());
    usuario.getAcessoHistoricoSenhas().addAll(senhas);

    usuario = save(usuario);

    return usuario;
  }

  public List<AcessoUsuario> buscarUsuariosHierarquia(SecurityUser user) {
    return usuarioRepo.getUsuariosPorUsuarioLogado(user);
  }

  @Transactional
  public LoginTemporarioResponse gerarLoginTemporario(
      GenerateLoginTemporario model, SecurityUser user) {

    AcessoUsuario usuarioLogado = usuarioRepo.findByIdUsuario(user.getIdUsuario());
    if (usuarioLogado == null) {
      throw new GenericServiceException("O usuário solicitante não existe.");
    }

    AcessoUsuario usuarioSolicitado = usuarioRepo.findByIdUsuario(model.getIdUsuarioSolicitado());
    if (usuarioSolicitado == null) {
      throw new GenericServiceException("O usuário solicitado não existe.");
    }

    UtilController.checkHierarquiaUsuarioLogadoEmSuperioridade(user, usuarioSolicitado);

    AcessoTemporarioB2B acessoTemp = new AcessoTemporarioB2B();
    acessoTemp.setIdUsuarioSolicitante(usuarioLogado.getIdUsuario());
    acessoTemp.setLoginSolicitante(usuarioLogado.getLogin());
    acessoTemp.setCpfSolicitante(usuarioLogado.getCpf());
    acessoTemp.setIdUsuarioSolicitado(model.getIdUsuarioSolicitado());

    String senhaRandom = generatePassword();
    acessoTemp.setSenhaProvisoria(encodePassword(senhaRandom + usuarioLogado.getCpf()));

    Calendar agora = Calendar.getInstance();
    agora.set(Calendar.MINUTE, agora.get(Calendar.MINUTE) + 2);
    acessoTemp.setDataExpiracao(DateUtil.dateToLocalDateTime(agora.getTime()));

    acessoTemporarioB2BService.save(acessoTemp);

    LoginTemporarioResponse response = new LoginTemporarioResponse();
    response.setLogin(usuarioLogado.getLogin());
    response.setSenha(senhaRandom);

    if (usuarioSolicitado.getIdInstituicao() != null
        && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
            usuarioSolicitado.getIdInstituicao())) {
      response.setUrlB2B(urlTrocaSenhaB2bInfinancas);
    } else {
      response.setUrlB2B(urlTrocaSenhaB2b);
    }

    return response;
  }

  public ResponseEntity<?> alterarUsuario(Integer id, UpdateUsuario model, SecurityUser user) {

    if (!DocumentoUtil.isCPF(model.getCpf())) {
      throw new GenericServiceException("O CPF digitado não é válido!");
    }

    AcessoUsuario userUpdate = findById(id);
    grupoService.impedeTentativaDeAlterarUsuarioComPermissoesSuperiores(
        userUpdate.getIdUsuario(),
        userUpdate.getAcessoGrupos().stream().map(AcessoGrupo::getIdGrupo).toArray(Integer[]::new),
        user.getIdUsuario());

    if (userUpdate == null) {
      throw new GenericServiceException("O Usuário solicitado não existe na base");
    }

    // RN altera a dt_hr_status somente se o status mudar
    if (!model.getStatus().equals(userUpdate.getStatus())) {
      userUpdate.setDtHrStatus(new Date());
    }
    Integer oldIdMecanismoAutenticacao = 1;
    if (MecanismosAutenticacao.TODOS.getCodigo().equals(model.getIdMecanismoAutenticacao())) {
      oldIdMecanismoAutenticacao = userUpdate.getIdMecanismoAutenticacao();
    }

    BeanUtils.copyProperties(model, userUpdate, getNullPropertyNames(model));
    if (MecanismosAutenticacao.TODOS.getCodigo().equals(userUpdate.getIdMecanismoAutenticacao())) {
      userUpdate.setIdMecanismoAutenticacao(oldIdMecanismoAutenticacao);
    }
    checksUsuario(userUpdate, user);
    userUpdate.setIdUsuarioManutencao(user.getIdUsuario());
    save(userUpdate);

    HashMap<String, String> map = new HashMap<>();
    map.put("msg", "Dados do Usuário " + userUpdate.getNome() + " alterados com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  public ResponseEntity<?> cadastrarUserAdmB2b(CadastrarUsuario model, SecurityUser user)
      throws GenericServiceException {
    model.setIdHierarquiaNivel(5);
    model.setIdProcessadora(user.getIdProcessadora());
    model.setIdInstituicao(user.getIdInstituicao());
    model.setIdRegional(user.getIdRegional());
    model.setIdFilial(user.getIdFilial());
    model.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());

    Optional<AcessoGrupo> op = user.getAcessoGrupos().stream().findFirst();
    if (op.isPresent()) {
      Integer[] idGrupos = {op.get().getIdGrupo()};
      VincularGrupoUsuario grupoUsuario = new VincularGrupoUsuario();
      grupoUsuario.setIdGrupos(idGrupos);

      model.setGrupoUsuario(grupoUsuario);
    } else {
      throw new GenericServiceException("Grupo do usuário logado não foi encontrado!");
    }

    return cadastrarUsuario(model, user);
  }

  public HashMap<String, String> esqueceuSenhaViaEmail(String login, String cpf) {

    List<AcessoUsuario> userChange = findByLogin(login);

    AcessoUsuario userChangePw = new AcessoUsuario();

    for (AcessoUsuario acessoUsuario : userChange) {
      if (acessoUsuario.getLogin().toUpperCase().equals(login)) {
        BeanUtils.copyProperties(acessoUsuario, userChangePw);
      }
    }

    if (userChangePw == null || !userChangePw.getCpf().equals(cpf)) {
      throw new GenericServiceException("O Usuário solicitado não existe na base");
    }

    if (acessoUsuarioUnicoService.isUsuarioVinculadoComUsuarioUnico(userChangePw.getLogin())) {
      throw new GenericServiceException(
          "O Usuário está Vinculado com um Usuário, realizar a esqueceu de senha do ogin Unico");
    }

    userChangePw.setSenhaExpirada(true);
    save(userChangePw);

    try {
      String url = getUrlTrocaSenha(userChangePw);
      emailService.sendPasswordEmail(userChangePw, generatePassword(), url);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível enviar Senha por E-mail para o Usuário", e);
    }

    HashMap<String, String> map = new HashMap<>();
    map.put(
        "msg",
        "Solicitação de Alteração de Senha do Usuário "
            + userChangePw.getNome()
            + " realizada com sucesso.");
    return map;
  }

  public List<UsuarioAtribuirChamado> getUsrsAtribuirChamados(SecurityUser user) {
    return usuarioRepo.getUsrsAtribuirChamados(user);
  }

  public AcessoUsuario findUsuarioByLoginAndStatus(String login, Integer status) {
    return usuarioRepo.findUsuarioByLoginAndStatus(login, status);
  }

  public AcessoUsuario findUsuarioByIdWithGrupos(Integer id) {
    return usuarioRepo.findUsuarioByIdWithGrupos(id);
  }

  public AcessoUsuario verificarQuantidadeTentativasLoginErradas(AcessoUsuario usuario) {
    int quantidadeTentativasErradasLogin =
        usuarioRepo.verificarQuantidadeTentativasLoginErradas(usuario.getIdUsuario());
    Long valorTempoParametro;
    try {
      valorTempoParametro =
          parametroValorService.findByParametroDefinicaoAndIdInstituicao(
              Constantes.PARAMETRO_QUANTIDADE_TENTATIVAS_LOGIN_ISSUER,
              usuario.getIdInstituicao() == null
                  ? Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL
                  : usuario.getIdInstituicao());
    } catch (Exception e) {
      valorTempoParametro = Constantes.VALOR_PADRAO_QUANTIDADE_TENTATIVAS_LOGIN;
    }
    if (valorTempoParametro == null) {
      valorTempoParametro = Constantes.VALOR_PADRAO_QUANTIDADE_TENTATIVAS_LOGIN;
    }
    if (quantidadeTentativasErradasLogin >= valorTempoParametro.intValue()
        && StatusUsuario.BLOQUEIO_TEMPORARIO.value() != usuario.getStatus()) {
      usuario.setDtHrStatus(new Date());
      usuario.setStatus(StatusUsuario.BLOQUEIO_TEMPORARIO.value());
      return usuarioRepo.save(usuario);
    }
    return usuario;
  }

  public ResponseEntity<?> validarAcessoUsuario(
      String login,
      String senha,
      HttpServletRequest request,
      HashMap<String, Object> map,
      boolean isB2b,
      Integer tipoB2b) {
    AcessoUsuario acessoUsuario;
    try {
      acessoUsuario = findByLoginSimples(login.toUpperCase());
    } catch (Exception e) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
      saveAcessoLog(
          acessoUsuarioSimples,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    if (acessoUsuario == null) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
      saveAcessoLog(
          acessoUsuarioSimples,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    } else {
      travaServicosService.travaServicos(acessoUsuario.getIdInstituicao(), Servicos.LOGIN_ISSUER);
    }
    acessoUsuario = verificarQuantidadeTentativasLoginErradas(acessoUsuario);

    if (usuarioBloqueado(acessoUsuario.getStatus(), map)) {
      saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.UNAUTHORIZED,
          map.get("msg").toString() + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }

    if (usuarioSenhaExpirada(acessoUsuario.getSenhaExpirada(), map)) {
      saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.FORBIDDEN,
          map.get("msg").toString() + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.FORBIDDEN);
    }
    ResponseEntity<?> validacaoSenhaUsuario;
    if (isB2b) {
      validacaoSenhaUsuario = validarSenhaUsuarioB2b(login, senha, request, map, tipoB2b);
    } else {
      validacaoSenhaUsuario = validarSenhaUsuarioIssuer(login, senha, request, map);
    }
    if (validacaoSenhaUsuario != null) {
      return validacaoSenhaUsuario;
    }
    return null;
  }

  private ResponseEntity<?> validarSenhaUsuarioIssuer(
      String login, String senha, HttpServletRequest request, HashMap<String, Object> map) {
    AcessoUsuario acessoUsuarioCompleto;
    try {
      acessoUsuarioCompleto = findByLoginAndSenha(login.toUpperCase(), senha);
    } catch (Exception e) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
      saveAcessoLog(
          acessoUsuarioSimples,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    if (acessoUsuarioCompleto == null) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
      saveAcessoLog(
          acessoUsuarioSimples,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    return null;
  }

  private ResponseEntity<?> validarSenhaUsuarioB2b(
      String login,
      String senha,
      HttpServletRequest request,
      HashMap<String, Object> map,
      Integer tipoB2B) {
    AcessoUsuario acessoUsuario = findByLoginSimples(login.toUpperCase());

    if (acessoUsuario != null
        && acessoUsuario.getIdHierarquiaNivel() == Constantes.NIVEL_PONTO_DE_RELACIONAMENTO) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              acessoUsuario.getIdProcessadora(),
              acessoUsuario.getIdInstituicao(),
              acessoUsuario.getIdRegional(),
              acessoUsuario.getIdFilial(),
              acessoUsuario.getIdPontoDeRelacionamento());
      Boolean b2b = ptRelService.findById(id).getB2b();
      ResponseEntity<?> validacaoUsuario;
      if (b2b) {
        validacaoUsuario = validarSenhaUsuarioIssuer(login, senha, request, map);
        if (validacaoUsuario == null) {
          if (!acessoUsuario.getTipoB2B().equals(tipoB2B)) {
            map.put("msg", USUARIO_NAO_CADASTRO_PORTAL);
            AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
            saveAcessoLog(
                acessoUsuarioSimples,
                request,
                HttpStatus.UNAUTHORIZED,
                USUARIO_NAO_CADASTRO_PORTAL + " | Informação interna - Login: " + login);
            return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
          }
        } else {
          return validacaoUsuario;
        }
      } else {
        map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
        AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
        saveAcessoLog(
            acessoUsuarioSimples,
            request,
            HttpStatus.UNAUTHORIZED,
            USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      }
    } else {
      LocalDateTime dataAtual = LocalDateTime.now();
      List<AcessoTemporarioB2B> acessosTemp =
          acessoTemporarioB2BService.findByLoginSolicitanteAndDataExpiracaoAfter(
              login.toUpperCase(), dataAtual);
      if (acessosTemp == null || acessosTemp.isEmpty()) {
        map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
        AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
        saveAcessoLog(
            acessoUsuarioSimples,
            request,
            HttpStatus.UNAUTHORIZED,
            USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      }
      boolean encontrouAcessoTemporario = false;
      for (AcessoTemporarioB2B tmp : acessosTemp) {
        if (BCrypt.checkpw(senha + tmp.getCpfSolicitante(), tmp.getSenhaProvisoria())) {
          encontrouAcessoTemporario = true;
          break;
        }
      }
      if (!encontrouAcessoTemporario) {
        map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
        AcessoUsuario acessoUsuarioSimples = findByLoginSimples(login.toUpperCase());
        saveAcessoLog(
            acessoUsuarioSimples,
            request,
            HttpStatus.UNAUTHORIZED,
            USUARIO_NAO_CADASTRO_SENHA_INVALIDA + " | Informação interna - Login: " + login);
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      }
    }
    return null;
  }

  private boolean verificarParametroValidarToken(AcessoUsuario usuario) {
    Boolean isValidarToken = true;
    try {
      List<ParametroValor> parametroValores =
          parametroValorService.getParametrosByDescricaoChaveParametro(
              Constantes.PARAMETRO_VALIDAR_TOKEN);
      AtomicBoolean isTemParametroInstNull = new AtomicBoolean(false);
      ParametroValor parametroValorInstituicao = null;
      for (ParametroValor parametroValor : parametroValores) {
        if (usuario.getIdInstituicao() != null
            && usuario.getIdInstituicao().equals(parametroValor.getIdInstituicao())) {
          parametroValorInstituicao = parametroValor;
        }
      }
      if (parametroValorInstituicao != null) {
        if ("B".equalsIgnoreCase(parametroValorInstituicao.getParametroDefinicao().getTipo())) {
          isValidarToken = Boolean.valueOf(parametroValorInstituicao.getValorParametro());
        }
      } else {
        ParametroValor parametroValorInstituicaoNulo = null;
        for (ParametroValor parametroValor : parametroValores) {
          if (parametroValor.getIdInstituicao() == null) {
            parametroValorInstituicaoNulo = parametroValor;
          }
        }
        if (parametroValorInstituicaoNulo != null) {
          if ("B"
              .equalsIgnoreCase(parametroValorInstituicaoNulo.getParametroDefinicao().getTipo())) {
            isValidarToken = Boolean.valueOf(parametroValorInstituicaoNulo.getValorParametro());
          }
        }
      }
    } catch (Exception e) {
      isValidarToken = true;
    }
    return isValidarToken;
  }

  public Page<AcessoUsuarioDTO> findByDynamicFilters(
      Integer first, Integer max, BuscaGenericaFiltro termo) {
    String nome = null;
    if (termo.getUsuario() != null) {
      nome = "%" + termo.getUsuario().toUpperCase() + "%";
    }

    Pageable pageable = PageRequest.of(first, max);
    List<AcessoUsuarioDTO> acessoUsuarioDTOList = new ArrayList<>();
    Page<AcessoUsuario> acessoUsuario =
        usuarioRepo.buscarPessoasComFiltros(
            termo.getIdInstituicao(),
            termo.getIdRegional(),
            termo.getIdFilial(),
            termo.getIdPontoDeRelacionamento(),
            nome,
            termo.getDocumento(),
            termo.getStatus(),
            termo.getIdUsuario(),
            termo.getIdGrupoAcesso(),
            pageable);

    for (AcessoUsuario usuario : acessoUsuario) {
      AcessoUsuarioDTO acessoUsuarioDTO = new AcessoUsuarioDTO(usuario);
      String gruposAcesso =
          usuario.getAcessoGrupos().stream()
              .map(acessoGrupo -> acessoGrupo.getIdGrupo().toString())
              .collect(Collectors.joining(", "));
      acessoUsuarioDTO.setGruposAcesso(gruposAcesso);
      acessoUsuarioDTOList.add(acessoUsuarioDTO);
    }

    Page<AcessoUsuarioDTO> parceriaComercialVOPage =
        new PageImpl<>(acessoUsuarioDTOList, pageable, acessoUsuario.getTotalElements());

    return parceriaComercialVOPage;
  }
}
