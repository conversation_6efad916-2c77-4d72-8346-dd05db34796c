package br.com.sinergico.service;

import br.com.entity.suporte.LogChamadaGoogleMapsApi;
import br.com.json.bean.GooglePlaceApiResponse;
import br.com.sinergico.repository.suporte.LogChamadaGoogleMapsApiRepository;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.util.Constantes;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class GoogleMapsApiService {
  private final Logger logger = LoggerFactory.getLogger(GoogleMapsApiService.class);

  @Autowired private RestTemplate restTemplate;

  @Autowired private LogChamadaGoogleMapsApiRepository logChamadaGoogleMapsApiRepository;

  public GooglePlaceApiResponse buscarLocaisProximosPorTexto(
      SecurityUserPortador userPortador,
      Optional<Double> optLatitude,
      Optional<Double> optLongitude,
      Optional<String> optTexto,
      Optional<String> optPageToken,
      Optional<List<String>> optTiposLocais) {
    logger.info("Chamada da Google Maps Places API - Locais proximos");
    String key = Constantes.GOOGLE_MAPS_API_KEY;
    String url = Constantes.GOOGLE_MAPS_API + "place/";

    // Determina os tipos de locais a serem usados
    String tiposLocais =
        optTiposLocais.isPresent() && !optTiposLocais.get().isEmpty()
            ? String.join("|", optTiposLocais.get())
            : this.tiposLocais();

    if (!optTexto.isPresent() && !optLatitude.isPresent() && !optLongitude.isPresent()) {
      url = url + "textsearch/json?type=" + tiposLocais + "&language=pt-BR";
    }
    if (!optTexto.isPresent() && optLatitude.isPresent() && optLongitude.isPresent()) {
      url =
          url
              + "textsearch/json?type="
              + tiposLocais
              + "&language=pt-BR&location="
              + optLatitude.get()
              + ","
              + optLongitude.get()
              + "&rankby=distance";
    }
    if (optTexto.isPresent() && optLatitude.isPresent() && optLongitude.isPresent()) {
      url =
          url
              + "textsearch/json?type="
              + tiposLocais
              + "&language=pt-BR&location="
              + optLatitude.get()
              + ","
              + optLongitude.get()
              + "&rankby=distance"
              + "&query="
              + optTexto.get();
    }
    if (optPageToken.isPresent()) {
      url = url + "&pagetoken=" + optPageToken.get();
    }
    url = url + "&key=" + key;
    logger.info("Chamada da Google Maps Places API - Locais proximos url => " + url);

    GooglePlaceApiResponse response =
        restTemplate.getForEntity(url, GooglePlaceApiResponse.class).getBody();
    this.salvarLog(url, userPortador.getIdInstituicao(), userPortador.getIdLogin());
    return response;
  }

  private String tiposLocais() {
    List<String> tipos =
        Arrays.asList(
            "restaurant",
            "supermarket",
            "store",
            "bar",
            "book_store",
            "cafe",
            "car_repair",
            "car_wash",
            "clothing_store",
            "convenience_store",
            "department_store",
            "drugstore",
            "electrician",
            "electronics_store",
            "furniture_store",
            "gas_station",
            "gym",
            "hardware_store",
            "home_goods_store",
            "liquor_store",
            "meal_delivery",
            "meal_takeaway",
            "parking",
            "pharmacy",
            "bakery",
            "shoe_store",
            "shopping_mall",
            "storage");
    return String.join("|", tipos);
  }

  private void salvarLog(String url, Integer idInstituicao, Long idLogin) {
    LogChamadaGoogleMapsApi chamadaGoogleMapsApi = new LogChamadaGoogleMapsApi();
    chamadaGoogleMapsApi.setUrl(url);
    chamadaGoogleMapsApi.setIdInstituicao(idInstituicao);
    chamadaGoogleMapsApi.setIdLogin(idLogin);
    chamadaGoogleMapsApi.setDataHoraInclusao(LocalDateTime.now());
    this.logChamadaGoogleMapsApiRepository.save(chamadaGoogleMapsApi);
  }
}
