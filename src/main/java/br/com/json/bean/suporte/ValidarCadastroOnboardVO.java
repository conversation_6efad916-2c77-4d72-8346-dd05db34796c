package br.com.json.bean.suporte;

import br.com.sinergico.enums.TipoPortadorLoginEnum;
import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ValidarCadastroOnboardVO {
  private Boolean possuiLogin;
  private Boolean encaminharAtendimento;
  private Boolean preCadastro;
  private LocalDateTime dataNascimento;
  private Boolean dadosInvalidos;
  private Boolean cadastroAprovado;
  private Boolean dispositivoValido;
  private Boolean dependenteAutorizado;
  private TipoPortadorLoginEnum tipoPortadorLogin;
  private Boolean validacaoCafHabilitada;

  // Novos campos para dados pessoais
  private String nomeCompleto;
  private String razaoSocial;
  private String email;
  private LocalDateTime dataFundacao;
  private String nacionalidade;
  private String naturalidade;
  private Integer estadoCivil;
  private Integer idSexo;

  // Novo campo para endereço residencial
  private EnderecoResidencial enderecoResidencial;

  @Getter
  @Setter
  public static class EnderecoResidencial {
    private String cep;
    private String logradouro;
    private String numero;
    private String complemento;
    private String bairro;
    private String cidade;
    private String uf;
  }
}
