package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.CampanhaB2B;
import br.com.entity.cadastral.ProdutoInstituicaoCorrespondente;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.repository.cadastral.CampanhaB2BRepository;
import br.com.sinergico.repository.suporte.HierarquiaPontoDeRelacionamentoRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.vo.CampanhaB2BUsuarioVO;
import br.com.sinergico.vo.CampanhaB2BVO;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.Normalizer;
import java.util.List;
import java.util.Optional;
import org.bouncycastle.util.io.pem.PemGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class CampanhaB2BService extends GenericService<CampanhaB2B, Long> {

  @Autowired private CampanhaB2BRepository campanhaB2BRepository;

  @Value("${issuer.dir.regulamento}")
  private String issuerDirRegulamento;

  @Autowired
  private HierarquiaPontoDeRelacionamentoRepository hierarquiaPontoDeRelacionamentoRepository;

  private Path arquivoEncontrado;

  protected Logger log = LoggerFactory.getLogger(this.getClass());

  @Autowired
  private ProdutoInstituicaoCorrespondenteService produtoInstituicaoCorrespondenteService;

  @Autowired
  public CampanhaB2BService(CampanhaB2BRepository repository) {
    super(repository);
    this.campanhaB2BRepository = repository;
  }

  public List<CampanhaB2BVO> buscarCampanhas(Integer idInstituicao, Integer idPontoRelacionamento) {
    List<CampanhaB2BVO> campanhas = null;

    try {

      if (idInstituicao == null || idPontoRelacionamento == null) {
        throw new GenericServiceException(
            "idInstituicão ou idPontoRelacionamento não podem ser nulos", HttpStatus.BAD_REQUEST);
      }

      if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(idInstituicao)) {
        campanhas =
            campanhaB2BRepository.buscarCampanhasByIdInstituicao(
                Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS, idPontoRelacionamento);
      } else {
        campanhas =
            campanhaB2BRepository.buscarCampanhasByIdInstituicao(
                idInstituicao, idPontoRelacionamento);
      }

      if (campanhas == null || campanhas.isEmpty()) {
        throw new GenericServiceException(
            ConstantesErro.CAMP_ERRO_NENHUMA_CAMPANHA_ENCONTRADA.getMensagem(),
            HttpStatus.NOT_FOUND);
      }
    } catch (GenericServiceException e) {
      throw new GenericServiceException(e.getMensagem(), e.getHttpStatus());
    } catch (Exception e) {
      throw new GenericServiceException(
          ConstantesErro.CAMP_ERRO_NAO_FOI_POSSIVEL_BUSCAR_CAMPANHA.format(
              "Detalhes: " + e.getMessage()),
          HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return campanhas;
  }

  public CampanhaB2B buscarUltimaCampanhaCadastrada() {
    return campanhaB2BRepository.buscarUltimaCampanhaCadastrada();
  }

  public void confirmarCampanha(CampanhaB2B model, Long idCampanha) {
    CampanhaB2B campanhaB2B = findById(idCampanha);
    campanhaB2B.setSituacaoCampanha(model.getSituacaoCampanha());
    campanhaB2BRepository.save(campanhaB2B);
  }

  public void cancelarCampanha(CampanhaB2B model, Long idCampanha) {
    CampanhaB2B campanhaB2B = findById(idCampanha);
    campanhaB2B.setMotivoCancelamento(model.getMotivoCancelamento());
    campanhaB2B.setSituacaoCampanha(model.getSituacaoCampanha());
    campanhaB2BRepository.save(campanhaB2B);
  }

  public CampanhaB2BVO buscarCampanhasByIdCampanha(Long idCampanha) {
    return campanhaB2BRepository.buscarCampanhasByIdCampanha(idCampanha);
  }

  public CampanhaB2BVO buscarCampanhasByIdCampanhaAndIdPontoRelacionamento(
      Long idCampanha, Integer idPontoRelacionamento) {
    return campanhaB2BRepository.buscarCampanhasByIdCampanhaAndIdPontoRelacionamento(
        idCampanha, idPontoRelacionamento);
  }

  public List<CampanhaB2BUsuarioVO> findCampanhaB2BAdesaoByDocumento(
      String documento, Optional<Integer> idPontoRelacionamentoCorresp) {
    if (idPontoRelacionamentoCorresp.isPresent()) {
      return campanhaB2BRepository.findCampanhaB2BAdesaoByDocumentoAndIdPontoRelacionamentoCorresp(
          documento, idPontoRelacionamentoCorresp.get());
    }
    return campanhaB2BRepository.findCampanhaB2BAdesaoByDocumento(documento);
  }

  public void salvarArquivoRegulamento(MultipartFile file, Long idCampanha) throws IOException {

    InputStream is = file.getInputStream();
    int posicao = file.getOriginalFilename().lastIndexOf('.');
    String extensao = file.getOriginalFilename().substring(posicao);

    if (extensao.equalsIgnoreCase(".pdf")) {
      String pasta = issuerDirRegulamento;
      String caminhoCampanha = pasta + idCampanha + "/";
      File diretorioCampanha = new File(caminhoCampanha);

      if (!diretorioCampanha.exists()) {
        diretorioCampanha.mkdir();
      }

      String nomeArquivoOriginal = file.getOriginalFilename().substring(0, posicao);
      String nomeArquivoNormalizado =
          Normalizer.normalize(nomeArquivoOriginal, Normalizer.Form.NFD);
      String nomeArquivo = nomeArquivoNormalizado.replaceAll("[^\\p{L}\\p{N}\\s]", "") + extensao;
      Path destinoFinal = Paths.get(caminhoCampanha + "/" + nomeArquivo);

      if (pasta == null) {
        throw new GenericServiceException("O diretório para salvar o anexo não foi encontrado!");
      }

      CampanhaB2B campanhaB2B = findById(idCampanha);

      StringBuilder caminhoFinal = new StringBuilder();
      StringBuilder caminhoBanco = new StringBuilder();

      //            Definindo o caminho a ser gravado (url) , no banco
      caminhoBanco.append(destinoFinal);

      //            Definindo o caminho da pasta para salvar
      caminhoFinal.append(destinoFinal);

      try {
        FileOutputStream fout = new FileOutputStream(destinoFinal.toFile());
        while (is.available() != 0) {
          fout.write(is.read());
        }
        is.close();
        fout.close();
      } catch (Exception e) {
        throw new GenericServiceException(
            "Não foi possível gravar o documento no caminho indicado: " + caminhoFinal.toString(),
            e);
      }

      //          Salvar caminho do arquivo
      campanhaB2B.setRegulamentoCampanha(caminhoBanco.toString());
      save(campanhaB2B);

    } else {
      throw new PemGenerationException(
          "O documento com o formato "
              + extensao
              + " não é suportado. Upload apenas de arquivos com formato .pdf");
    }
  }

  public void atualizarCampanha(CampanhaB2B model, Long idCampanha) {

    CampanhaB2B campanhaB2B = findById(idCampanha);

    campanhaB2B.setNomeCampanha(
        model.getNomeCampanha() != null ? model.getNomeCampanha() : campanhaB2B.getNomeCampanha());
    campanhaB2B.setDataInicioCampanha(
        model.getDataInicioCampanha() != null
            ? model.getDataInicioCampanha()
            : campanhaB2B.getDataInicioCampanha());
    campanhaB2B.setDataFimCampanha(
        model.getDataFimCampanha() != null
            ? model.getDataFimCampanha()
            : campanhaB2B.getDataFimCampanha());
    //        campanhaB2B.setIdProdInstituicao(model.getIdProdInstituicao() != null ||
    // !model.getIdProdInstituicao().equals(0) ? model.getIdProdInstituicao() :
    // campanhaB2B.getIdProdInstituicao());

    campanhaB2BRepository.save(campanhaB2B);
  }

  public void deletarArquivoRegulamento(Long idCampanha, String nomeArquivo) throws IOException {

    String pasta = issuerDirRegulamento;
    String caminhoCampanha = pasta + idCampanha + "/" + nomeArquivo + ".pdf";
    File diretorioArquivoCampanha = new File(caminhoCampanha);

    CampanhaB2B campanhaB2B = findById(idCampanha);

    if (diretorioArquivoCampanha.exists()) {
      diretorioArquivoCampanha.delete();
      campanhaB2B.setRegulamentoCampanha("");
      campanhaB2BRepository.save(campanhaB2B);
    }

    if (pasta == null) {
      throw new GenericServiceException("O diretório para salvar o anexo não foi encontrado!");
    }
  }

  /***
   * Embora o idPontoRelacionamento é passado como request, via issuerback o comportamento será diferente:
   * Irá usar o idPontoRelacionamento para buscar o idPontoRelacionamentoCorrespondente para salvar na tabela.
   * Motivo: Replicações de contas
   * @param campanhaModel
   */
  public void cadastrarCampanha(CampanhaB2B campanhaModel) {

    try {

      if (campanhaModel == null) {
        throw new GenericServiceException("Objeto request nulo", HttpStatus.BAD_REQUEST);
      }

      if (campanhaModel.getNomeCampanha() == null
          || campanhaModel.getDataInicioCampanha() == null
          || campanhaModel.getDataFimCampanha() == null
          || campanhaModel.getIdProdInstituicao() == null
          || campanhaModel.getIdInstituicao() == null
          || campanhaModel.getIdRegional() == null
          || campanhaModel.getIdFilial() == null
          || campanhaModel.getIdPontoRelacionamento() == null) {
        throw new GenericServiceException(
            "Existem campos nulos na solicitação, favor preencher todos corretamente.",
            HttpStatus.UNPROCESSABLE_ENTITY);
      }

      HierarquiaPontoDeRelacionamento pontoDeRelacionamento =
          hierarquiaPontoDeRelacionamentoRepository.findHierarquiaPontoDeRelacionamentoByIds(
              campanhaModel.getIdInstituicao(),
              campanhaModel.getIdRegional(),
              campanhaModel.getIdFilial(),
              campanhaModel.getIdPontoRelacionamento());

      if (pontoDeRelacionamento == null) {
        log.error(
            ConstantesErro.CAMP_ERRO_AO_BUCAR_PONTO_RELACIONAMENTO.format(
                "Não foi possível repassar ponto de relacionamento correspondente."));
        throw new GenericServiceException(
            ConstantesErro.CAMP_ERRO_AO_CADASTRAR_CAMPANHA.getMensagem(), HttpStatus.NOT_FOUND);
      }

      ProdutoInstituicaoCorrespondente produtoInstituicaoCorrespondente =
          produtoInstituicaoCorrespondenteService.buscarProdutoInstituicaoCorrespondente(
              campanhaModel.getIdInstituicao(), campanhaModel.getIdProdInstituicao());

      campanhaModel.setIdInstituicaoCorresp(pontoDeRelacionamento.getIdInstituicaoCorresp2());
      campanhaModel.setIdProdutoCorrespMulticontas(
          produtoInstituicaoCorrespondente != null
              ? produtoInstituicaoCorrespondente.getIdProdInstituicaoDest()
              : null);
      campanhaModel.setIdRegionalCorresp(pontoDeRelacionamento.getIdRegionalCorresp2());
      campanhaModel.setIdFilialCorresp(pontoDeRelacionamento.getIdFilialCorresp2());
      campanhaModel.setIdPontoRelacionamentoCorresp(
          pontoDeRelacionamento.getIdPontoDeRelacionamentoCorresp2());

      save(campanhaModel);
    } catch (GenericServiceException e) {
      throw new GenericServiceException(
          ConstantesErro.CAMP_ERRO_AO_CADASTRAR_CAMPANHA.getMensagem(), e.getHttpStatus());
    } catch (Exception e) {
      log.error(
          ConstantesErro.CAMP_ERRO_AO_CADASTRAR_CAMPANHA.getMensagem() + " " + e.getMessage());
      throw new GenericServiceException(
          ConstantesErro.CAMP_ERRO_AO_CADASTRAR_CAMPANHA.getMensagem(),
          HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  public List<CampanhaB2BUsuarioVO> findCampanhaB2BAdesaoByDocumentoAndOnlyConfirmed(
      String documento) {
    List<CampanhaB2BUsuarioVO> campanhasAtivasPortador = null;
    try {
      campanhasAtivasPortador =
          campanhaB2BRepository.findCampanhaB2BAdesaoByDocumentoAndOnlyConfirmed(documento);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possivel buscar campanhas do portador");
    }
    return campanhasAtivasPortador;
  }
}
