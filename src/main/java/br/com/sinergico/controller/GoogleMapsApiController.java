package br.com.sinergico.controller;

import br.com.json.bean.GooglePlaceApiResponse;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GoogleMapsApiService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Optional;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/google-maps-api")
public class GoogleMapsApiController extends UtilController {

  @Autowired private GoogleMapsApiService googleMapsApiService;

  @Autowired private EntityManager em;

  @Autowired private HttpServletRequest request;

  @ApiOperation(
      value = "Serviço responsável por buscar o os estabelecimentos ao redor de uma localizacao")
  @RequestMapping(
      value = "/buscar-lugares",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(
      paramType = "header",
      name = UtilController.AUTH_HEADER_PORTADOR,
      value = "API Key")
  public ResponseEntity<GooglePlaceApiResponse> buscarLocaisProximos(
      @RequestParam("latitude") Optional<Double> latitude,
      @RequestParam("longitude") Optional<Double> longitude,
      @RequestParam("texto") Optional<String> texto,
      @RequestParam(value = "tipos", required = false) Optional<List<String>> tiposLocais,
      @RequestParam("pageToken") Optional<String> pageToken) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(this.request, this.em);
    GooglePlaceApiResponse response =
        googleMapsApiService.buscarLocaisProximosPorTexto(
            userPortador, latitude, longitude, texto, pageToken, tiposLocais);
    return new ResponseEntity<>(response, HttpStatus.OK);
  }
}
