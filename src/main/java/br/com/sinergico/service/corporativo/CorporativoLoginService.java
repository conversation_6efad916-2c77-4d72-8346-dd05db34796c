package br.com.sinergico.service.corporativo;

import static br.com.sinergico.service.GenericService.encodeSenhaSHA256;

import br.com.entity.cadastral.CorporativoLogin;
import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.entity.cadastral.CorporativoResponsavelCredencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.suporte.CorporativoPortadorDispositivo;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.ParametroProcessamentoSistema;
import br.com.entity.suporte.TokenRedefinicaoSenha;
import br.com.enumVO.TipoLogRequestError;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.CorporativoLoginDTO;
import br.com.json.bean.cadastral.CorporativoLoginRetornoDTO;
import br.com.json.bean.cadastral.OnboardCorporativoVO;
import br.com.json.bean.cadastral.PortadorLoginCorporativo;
import br.com.json.bean.suporte.RedefinirSenhaPortadorCorporativoVO;
import br.com.json.bean.suporte.ValidarCadastroOnboardCorporativoVO;
import br.com.sinergico.controller.Token;
import br.com.sinergico.enums.AntifraudeCafFacialObjetivosEnum;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.facade.transacional.ControleGarantiaFacade;
import br.com.sinergico.repository.cadastral.CorporativoLoginRepository;
import br.com.sinergico.repository.cadastral.CorporativoResponsavelCredencialRepository;
import br.com.sinergico.repository.cadastral.CorporativoResponsavelRepository;
import br.com.sinergico.repository.suporte.AcessoServicoRepository;
import br.com.sinergico.repository.suporte.CorporativoPortadorDispositivoRepository;
import br.com.sinergico.security.CorporativoAuthentication;
import br.com.sinergico.security.CorporativoSecurityService;
import br.com.sinergico.security.PasswordValidatorService;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.TokenAuthenticationCorporativoService;
import br.com.sinergico.service.cadastral.AntifraudeService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.LogRequestErrorIpBlacklistService;
import br.com.sinergico.service.suporte.LogRequestErrorService;
import br.com.sinergico.service.suporte.ParametroProcessamentoSistemaService;
import br.com.sinergico.service.suporte.RegistroValidacaoFacialCafService;
import br.com.sinergico.service.suporte.TokenRedefinicaoSenhaService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.validator.UtilValidator;
import br.com.sinergico.vo.HierarquiaInstituicaoVO;
import br.com.sinergico.vo.combateFraude.StatusCafLoginDTO;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

@Service
public class CorporativoLoginService {

  private static final String MSG_DADOS_INV_CRIAR_LOGIN =
      "Não foi possível Realizar Operação. Dados inválidos. ";

  @Autowired private CorporativoLoginRepository corporativoLoginRepository;
  @Autowired private CorporativoService corporativoService;
  @Autowired private HttpSession session;
  @Autowired private ControleGarantiaFacade controleGarantiaFacade;
  @Autowired private LogRequestErrorIpBlacklistService logRequestErrorIpBlacklistService;
  @Autowired private LogRequestErrorService logRequestErrorService;
  @Autowired private AcessoServicoRepository acessoServicoRepository;
  @Autowired private TokenAuthenticationCorporativoService tokenAuthenticationCorporativoService;
  @Autowired private AntifraudeService antifraudeService;
  @Autowired private CorporativoResponsavelRepository corporativoResponsavelRepository;
  @Autowired private ContaPagamentoService contaPagamentoService;
  @Autowired private TokenRedefinicaoSenhaService tokenRedefinicaoSenhaService;
  @Autowired private ParametroProcessamentoSistemaService parametroProcessamentoSistemaService;
  @Autowired private PessoaService pessoaService;

  @Autowired
  private CorporativoPortadorDispositivoRepository corporativoPortadorDispositivoRepository;

  @Autowired private EventoService eventoService;

  @Autowired
  private CorporativoResponsavelCredencialRepository corporativoResponsavelCredencialRepository;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;
  @Autowired private PasswordValidatorService passwordValidatorService;
  @Autowired private PortadorLoginService portadorLoginService;
  @Autowired private RegistroValidacaoFacialCafService registroValidacaoFacialCafService;

  public ResponseEntity<CorporativoLoginRetornoDTO> realizarLogin(
      HttpServletRequest request, CorporativoLoginDTO loginDto) throws IOException {

    if (request.getHeader("Proxy-Authorization") != null) {
      throw new GenericServiceException("Acesso impedido.", HttpStatus.UNAUTHORIZED);
    }

    CorporativoLoginRetornoDTO corporativoLoginRetornoDTO = new CorporativoLoginRetornoDTO();

    HierarquiaInstituicaoVO hierarquiaInstituicao =
        this.controleGarantiaFacade.getInstituicao(
            loginDto.getIdProcessadora(), loginDto.getIdInstituicao());

    if (hierarquiaInstituicao.getStatus().equals(Constantes.INSTITUICAO_BLOQUEADA)) {
      throw new GenericServiceException("Serviço temporariamente indisponível para a instituição");
    }

    this.logRequestErrorIpBlacklistService.verificarIPBloqueado(request, loginDto);

    CorporativoResponsavel responsavel =
        corporativoService.findResponsavelAtivoByDocumento(loginDto.getCpf());
    if (responsavel == null) {
      throw new BadCredentialsException("CPF ou senha inválido.");
    }
    CorporativoLogin corporativoLogin =
        this.corporativoLoginRepository
            .findCorporativoLoginByIdResponsavelAndIdInstituicaoAndDtHrCancelamentoIsNull(
                responsavel.getId(), loginDto.getIdInstituicao());
    if (corporativoLogin == null
        || !corporativoLogin.getSenha().equals(loginDto.getSenha().trim())) {
      HashMap<String, String> mapLogRequestError =
          this.logRequestErrorService.preencherMapLogRequestError(
              request, loginDto, TipoLogRequestError.LOGIN_CORPORATIVO, HttpStatus.UNAUTHORIZED);
      this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);
      throw new BadCredentialsException("CPF ou senha inválido.");
    }

    if (hierarquiaInstituicao.getValidaContaAtiva()) {
      contaPagamentoService.validaContaAtivaCorporativo(corporativoLogin);
    }

    Token token = new Token(fazerAutenticacao(request, session, corporativoLogin));
    corporativoLoginRetornoDTO.setToken(token.getToken());
    corporativoLoginRetornoDTO.setDataHoraUltimoAcessso(corporativoLogin.getDtHrUltimoAcesso());
    corporativoLoginRetornoDTO.setIdLogin(corporativoLogin.getId());

    StatusCafLoginDTO statusCafLoginDTO =
        antifraudeService.verificarStatusCafLogin(
            loginDto.getCpf(), loginDto.getIdInstituicao(), null);
    validarDispositivo(loginDto, corporativoLogin, corporativoLoginRetornoDTO);
    if (statusCafLoginDTO.getIgnorarValidacao()) {
      corporativoLoginRetornoDTO.setIsDeviceIdValido(true);
    }
    corporativoLoginRetornoDTO.setEncaminharAtendimento(
        statusCafLoginDTO.getEncaminharAtendimento());
    corporativoLoginRetornoDTO.setOnboardRealizado(statusCafLoginDTO.getOnboardRealizado());
    corporativoLoginRetornoDTO.setIsAntifraudeValidacaoNecessaria(
        statusCafLoginDTO.getIsValidacaoNecessaria());

    request.getSession().setAttribute("corporativoLogin", corporativoLogin);
    return new ResponseEntity<>(corporativoLoginRetornoDTO, HttpStatus.OK);
  }

  public String fazerAutenticacao(
      HttpServletRequest request, HttpSession session, CorporativoLogin usuario) {
    List<String> distinctRoles =
        acessoServicoRepository.retornaRolesDistintasDoGrupoCorporativoDaInstituicao(
            usuario.getIdProcessadora(), usuario.getIdInstituicao());
    SecurityUserCorporativo securityUser = new SecurityUserCorporativo(usuario, distinctRoles);
    securityUser.setResponsavel(usuario.getResponsavel());
    CorporativoSecurityService userService = new CorporativoSecurityService();
    userService.addUser(session, securityUser);

    CorporativoAuthentication userAuthentication = new CorporativoAuthentication(securityUser);
    String token = tokenAuthenticationCorporativoService.addAuthentication(userAuthentication);
    Authentication authentication =
        tokenAuthenticationCorporativoService.getAuthentication(request);
    SecurityContextHolder.getContext().setAuthentication(authentication);

    return token;
  }

  public ValidarCadastroOnboardCorporativoVO validarCadastroOnboard(OnboardCorporativoVO request)
      throws IOException {

    ValidarCadastroOnboardCorporativoVO validarCadastroOnboardCorporativoVO =
        new ValidarCadastroOnboardCorporativoVO();

    Integer ddd = Integer.parseInt(request.getTelefone().substring(0, 2));
    Integer celular = Integer.parseInt(request.getTelefone().substring(2));
    CorporativoResponsavel corporativoResponsavel =
        corporativoResponsavelRepository.findByDocumentoAndDddAndCelular(
            request.getCpf(), ddd, celular);
    if (corporativoResponsavel == null) {
      throw new GenericServiceException(
          ConstantesErro.DADOS_RESPONSAVEL_CORPORATIVO_INEXISTENTE.getMensagem());
    }

    Pessoa pessoa =
        pessoaService.findPessoaAtualizadaRecente(
            request.getIdProcessadora(),
            request.getIdInstituicao(),
            corporativoResponsavel.getDocumento());

    List<CorporativoResponsavelCredencial> corporativoResponsavelCredencial =
        corporativoResponsavelCredencialRepository.findByIdResponsavelAndDtHrFimIsNull(
            corporativoResponsavel.getId());

    if (corporativoResponsavelCredencial.isEmpty()) {
      throw new GenericServiceException(
          ConstantesErro.DADOS_RESPONSAVEL_CORPORATIVO_INEXISTENTE.getMensagem());
    }

    if (corporativoResponsavelCredencial.get(0).getDtHrCafValidado() != null) {
      throw new GenericServiceException(
          "Login já existente. Por favor, acesse sua conta utilizando seu CPF e senha cadastrados.");
    }

    StatusCafLoginDTO statusCafLoginDTO =
        antifraudeService.verificarStatusCafLogin(
            request.getCpf(), request.getIdInstituicao(), null);

    validarCadastroOnboardCorporativoVO.setOnboardRealizado(
        statusCafLoginDTO.getOnboardRealizado());
    validarCadastroOnboardCorporativoVO.setCafReprovado(statusCafLoginDTO.getIsReprovado());
    validarCadastroOnboardCorporativoVO.setEncaminharAtendimento(
        statusCafLoginDTO.getEncaminharAtendimento());
    validarCadastroOnboardCorporativoVO.setValidacaoNecessaria(
        statusCafLoginDTO.getIsValidacaoNecessaria());

    if (pessoa != null) {
      validarCadastroOnboardCorporativoVO.setDataNascimento(pessoa.getDataNascimento());
    }

    return validarCadastroOnboardCorporativoVO;
  }

  public void cadastrarPortadorLogin(PortadorLoginCorporativo request) {

    if (!hierarquiaInstituicaoExists(request.getIdInstituicao(), request.getIdProcessadora())) {
      throw new GenericServiceException(MSG_DADOS_INV_CRIAR_LOGIN, HttpStatus.UNAUTHORIZED);
    }

    if (UtilValidator.existeCaracteresNaoAceitos(request.getSenha())) {
      throw new GenericServiceException("Apenas os caracteres especiais !@#$%&*+= são aceitos.");
    }

    if (!passwordValidatorService.validate(
        request.getSenha(), Arrays.asList(request.getCpf(), null))) {
      throw new GenericServiceException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números"
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.");
    }

    CorporativoResponsavel corporativoResponsavel =
        corporativoResponsavelRepository.findByDocumento(request.getCpf());

    if (corporativoResponsavel == null) {
      throw new GenericServiceException(
          ConstantesErro.DADOS_RESPONSAVEL_CORPORATIVO_INEXISTENTE.getMensagem());
    }

    List<CorporativoResponsavelCredencial> corporativoResponsavelCredencial =
        corporativoResponsavelCredencialRepository.findByIdResponsavelAndDtHrFimIsNull(
            corporativoResponsavel.getId());

    if (corporativoResponsavelCredencial.isEmpty()) {
      throw new GenericServiceException(
          ConstantesErro.DADOS_RESPONSAVEL_CORPORATIVO_INEXISTENTE.getMensagem());
    }

    CorporativoLogin corporativoLogin = new CorporativoLogin();
    verificarLoginExistente(corporativoResponsavel, request.getIdInstituicao());

    corporativoLogin.setIdResponsavel(corporativoResponsavel.getId());
    corporativoLogin.setIdProcessadora(request.getIdProcessadora());
    corporativoLogin.setIdInstituicao(request.getIdInstituicao());
    corporativoLogin.setSenha(encodeSenhaSHA256(request.getSenha()));
    corporativoLogin.setDtHrInclusao(LocalDateTime.now());
    corporativoLogin.setDtHrPrimeiroAcesso(LocalDateTime.now());
    corporativoLogin.setDtHrUltimoAcesso(LocalDateTime.now());
    corporativoLoginRepository.save(corporativoLogin);

    registrarDispositivo(request, corporativoLogin);
  }

  private void verificarLoginExistente(
      CorporativoResponsavel corporativoResponsavel, Integer request) {
    CorporativoLogin login =
        this.corporativoLoginRepository
            .findCorporativoLoginByIdResponsavelAndIdInstituicaoAndDtHrCancelamentoIsNull(
                corporativoResponsavel.getId(), request);
    if (login != null) {
      throw new GenericServiceException(
          "Login já existente. Por favor, acesse sua conta utilizando seu CPF e senha cadastrados.");
    }
  }

  private boolean hierarquiaInstituicaoExists(Integer idInstituicao, Integer idProcessadora) {
    HierarquiaInstituicaoId id = new HierarquiaInstituicaoId(idProcessadora, idInstituicao);
    return hierarquiaInstituicaoService.findById(id) != null;
  }

  public void redefinirSenhaPortador(
      RedefinirSenhaPortadorCorporativoVO redefinirSenhaPortadorCorporativoVO,
      HttpServletRequest request) {

    if (UtilValidator.existeCaracteresNaoAceitos(
        redefinirSenhaPortadorCorporativoVO.getNovaSenha())) {
      throw new GenericServiceException("Apenas os caracteres especiais !@#$%&*+= são aceitos.");
    }

    if (!passwordValidatorService.validate(
        redefinirSenhaPortadorCorporativoVO.getNovaSenha(),
        Arrays.asList(redefinirSenhaPortadorCorporativoVO.getCpf(), null))) {
      throw new GenericServiceException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números"
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.");
    }

    redefinirSenhaPortadorCorporativoVO.setNovaSenha(
        encodeSenhaSHA256(redefinirSenhaPortadorCorporativoVO.getNovaSenha()));

    Boolean cafNecessario =
        portadorLoginService.encontraConfiguracaoCaf(
            redefinirSenhaPortadorCorporativoVO.getIdInstituicao(),
            redefinirSenhaPortadorCorporativoVO.getCpf(),
            Optional.empty());

    if (cafNecessario) {
      Long idValidacao =
          registroValidacaoFacialCafService.checaValidacao(
              redefinirSenhaPortadorCorporativoVO.getCpf(),
              redefinirSenhaPortadorCorporativoVO.getIdInstituicao(),
              AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_LOGIN,
              AntifraudeCafFacialObjetivosEnum.ONBOARDING_CAF_FORCADO);
      if (idValidacao == null) {
        throw new GenericServiceException(
            "Não foi possível reconhecer a validação facial. Por favor, tente novamente!");
      }

      validaSenha(redefinirSenhaPortadorCorporativoVO);
      registroValidacaoFacialCafService.efetivaValidacao(idValidacao);

    } else {
      if (redefinirSenhaPortadorCorporativoVO.getToken() == null
          || redefinirSenhaPortadorCorporativoVO.getToken().isEmpty()) {
        throw new GenericServiceException(
            "Token de confirmação necessário para alteração de senha.", HttpStatus.BAD_REQUEST);
      }

      List<TokenRedefinicaoSenha> tokenAcessoList =
          tokenRedefinicaoSenhaService
              .findAllByTokenIgnoreCaseAndIdInstituicaoAndIdProcessadoraAndDocumentoOrderByDataHoraGeracaoDesc(
                  redefinirSenhaPortadorCorporativoVO.getToken(),
                  redefinirSenhaPortadorCorporativoVO.getIdInstituicao(),
                  redefinirSenhaPortadorCorporativoVO.getIdProcessadora(),
                  redefinirSenhaPortadorCorporativoVO.getCpf());

      ParametroProcessamentoSistema parametroProcessamentoSistemaTokenGenerico =
          parametroProcessamentoSistemaService.findFirstByIdInstituicaoAndTipoParametro(
              redefinirSenhaPortadorCorporativoVO.getIdInstituicao(), Constantes.TKN_REDEF_SENHA);

      if (tokenAcessoList == null || tokenAcessoList.isEmpty()) {
        if (!validaParametroProcessamentoSistemaTokenGenerico(
            parametroProcessamentoSistemaTokenGenerico, redefinirSenhaPortadorCorporativoVO)) {

          HashMap<String, String> mapLogRequestError =
              this.logRequestErrorService.preencherMapLogRequestError(
                  request,
                  redefinirSenhaPortadorCorporativoVO,
                  TipoLogRequestError.REDEFINIR_SENHA,
                  HttpStatus.UNAUTHORIZED);
          this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

          throw new GenericServiceException(
              "Ocorreu um erro ao redefinir a senha, entre em contato com o nosso Suporte.",
              HttpStatus.UNAUTHORIZED);
        } else {
          validaSenha(redefinirSenhaPortadorCorporativoVO);
          return;
        }
      }

      TokenRedefinicaoSenha token = tokenAcessoList.get(0);
      validarDataValidade(tokenAcessoList.get(0));

      if (Objects.isNull(token.getInTokenValidado())
          || token.getInTokenValidado()
          || token.getInTokenExpirado()
          || Objects.nonNull(token.getDataHoraUtilizacao())) {
        HashMap<String, String> mapLogRequestError =
            this.logRequestErrorService.preencherMapLogRequestError(
                request,
                redefinirSenhaPortadorCorporativoVO,
                TipoLogRequestError.REDEFINIR_SENHA,
                HttpStatus.FORBIDDEN);
        this.logRequestErrorService.montarESalvarLogRequestError(mapLogRequestError);

        throw new GenericServiceException(
            "Ocorreu um erro ao redefinir a senha, entre em contato com o nosso Suporte.",
            HttpStatus.FORBIDDEN);
      }

      validaSenha(redefinirSenhaPortadorCorporativoVO);
      token.setInTokenExpirado(true);
      token.setInTokenValidado(true);
      token.setDataHoraUtilizacao(LocalDateTime.now());
      tokenRedefinicaoSenhaService.save(token);
    }
  }

  private void validaSenha(RedefinirSenhaPortadorCorporativoVO request) {
    CorporativoResponsavel corporativoResponsavel =
        corporativoResponsavelRepository.findByDocumento(request.getCpf());

    if (corporativoResponsavel == null) {
      throw new GenericServiceException(
          ConstantesErro.DADOS_RESPONSAVEL_CORPORATIVO_INEXISTENTE.getMensagem());
    }

    CorporativoLogin corporativoLogin =
        corporativoLoginRepository
            .findCorporativoLoginByIdResponsavelAndIdInstituicaoAndDtHrCancelamentoIsNull(
                corporativoResponsavel.getId(), request.getIdInstituicao());

    if (corporativoLogin.getSenha().equals(request.getNovaSenha())) {
      throw new GenericServiceException(
          "A nova senha deve ser diferente da senha anterior. Tente novamente.");
    }

    corporativoLogin.setSenha(request.getNovaSenha());
    corporativoLoginRepository.save(corporativoLogin);
  }

  private void validarDispositivo(
      CorporativoLoginDTO loginDto,
      CorporativoLogin corporativoLogin,
      CorporativoLoginRetornoDTO corporativoLoginRetornoDTO) {

    CorporativoPortadorDispositivo dispositivo =
        corporativoPortadorDispositivoRepository.findFirstByIdCorporativoLoginOrderByIdDesc(
            corporativoLogin.getId());

    if (!Objects.isNull(dispositivo)) {
      String dispositivoModel =
          dispositivo.getModel() != null ? dispositivo.getModel().trim().toUpperCase() : "";
      String loginModel =
          loginDto.getModel() != null ? loginDto.getModel().trim().toUpperCase() : "";
      dispositivo.setLatitude(loginDto.getLatitude());
      dispositivo.setLongitude(loginDto.getLongitude());
      dispositivo.setDeviceId(loginDto.getDeviceId());
      dispositivo.setModel(loginDto.getModel());
      dispositivo.setPlataformVersion(loginDto.getPlataformVersion());
      dispositivo.setArchitectureInfo(loginDto.getArchitectureInfo());
      dispositivo.setPlatformName(loginDto.getPlatformName());
      dispositivo.setVersaoAplicativo(loginDto.getVersaoAplicativo());
      dispositivo.setPushNotificationDeviceId(loginDto.getPushNotificationDeviceId());
      corporativoPortadorDispositivoRepository.save(dispositivo);
      if (!loginModel.equals(dispositivoModel)) {
        eventoService.publicarCorporativoTrocaDispositivoEvent(dispositivo);
      }
    }

    corporativoLoginRetornoDTO.setIsDeviceIdValido(true);
  }

  private boolean validaParametroProcessamentoSistemaTokenGenerico(
      ParametroProcessamentoSistema pps, RedefinirSenhaPortadorCorporativoVO rsp) {
    if (Objects.isNull(pps)) {
      return false;
    }
    return pps.getTexto().equals(rsp.getToken());
  }

  private void validarDataValidade(TokenRedefinicaoSenha tokenAcesso) {
    if (LocalDateTime.now().isAfter(tokenAcesso.getDataHoraExpiracaoToken())) {
      tokenAcesso.setInTokenExpirado(true);
      tokenRedefinicaoSenhaService.save(tokenAcesso);
      throw new GenericServiceException(
          "Código expirado. Solicite um novo", "Código expirado solicite um novo");
    }
  }

  private void registrarDispositivo(
      PortadorLoginCorporativo request, CorporativoLogin corporativoLogin) {

    CorporativoPortadorDispositivo dispositivo = new CorporativoPortadorDispositivo();
    dispositivo.setIdCorporativoLogin(corporativoLogin.getId());
    dispositivo.setDataCadastro(LocalDateTime.now());
    dispositivo.setDataAlteracao(LocalDateTime.now());
    dispositivo.setDeviceId(request.getDeviceId());
    dispositivo.setPermiteNotificacao(request.getPermiteNotificacao());
    dispositivo.setArchitectureInfo(request.getArchitectureInfo());
    dispositivo.setModel(request.getModel());
    dispositivo.setPlatformName(request.getPlatformName());
    dispositivo.setPlataformVersion(request.getPlataformVersion());
    dispositivo.setLatitude(request.getLatitude());
    dispositivo.setLongitude(request.getLongitude());
    dispositivo.setPushNotificationDeviceId(request.getPushNotificationDeviceId());
    dispositivo.setVersaoAplicativo(request.getVersaoAplicativo());
    corporativoPortadorDispositivoRepository.save(dispositivo);
  }
}
