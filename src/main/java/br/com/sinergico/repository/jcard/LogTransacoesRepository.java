package br.com.sinergico.repository.jcard;

import br.com.entity.jcard.LogTransacoes;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface LogTransacoesRepository
    extends JpaRepository<LogTransacoes, Long>, LogTransacoesRepositoryCustom {

  @Query(" select lt from LogTransacoes lt where lt.voidId = :voidId ")
  public LogTransacoes findLogTransacoesByVoidId(@Param("voidId") Long voidId);

  @Query(" select lt from LogTransacoes lt where lt.rrn = :rrn ")
  public LogTransacoes findLogTransacoesByRrn(@Param("rrn") String rrn);
}
