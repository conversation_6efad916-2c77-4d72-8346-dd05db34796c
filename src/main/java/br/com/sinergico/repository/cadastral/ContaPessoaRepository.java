package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.ContaPessoaId;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ContaPessoaRepository extends JpaRepository<ContaPessoa, ContaPessoaId> {

  ContaPessoa findOneByIdPessoaAndIdContaAndStatus(Long idPessoa, Long idConta, Integer status);

  ContaPessoa findOneByIdPessoaAndIdConta(Long idPessoa, Long idConta);

  ContaPessoa findOneByIdConta(Long idConta);

  ContaPessoa findOneByIdPessoaAndIdContaAndIdTitularidade(
      Long idPessoa, Long idConta, Integer idTitularidade);

  List<ContaPessoa> findByIdPessoaAndIdTitularidade(Long idPessoa, Integer idTitularidade);

  List<ContaPessoa> findByIdPessoa(Long idPessoa);

  List<ContaPessoa> findByIdPessoaAndIdTitularidadeAndStatus(
      Long idPessoa, Integer idTitularidade, Integer status);

  List<ContaPessoa> findByIdConta(Long idConta);

  ContaPessoa findOneByIdContaAndIdTitularidade(Long idConta, Integer titularidade);

  @Query(
      "select cp.idConta is not null from ContaPessoa cp where cp.pessoa.idPessoa = :idPessoa and cp.idConta = :idConta")
  Boolean isContaPessoaExisteByIdContaAndIdPessoa(
      @Param("idConta") Long idConta, @Param("idPessoa") Long idPessoa);

  @Query(
      value =
          "select count(c.*) from cadastral.conta_pagamento c "
              + "inner join cadastral.conta_pessoa cp on cp.id_conta = c.id_conta "
              + "inner join cadastral.pessoa p on p.id_pessoa = cp.id_pessoa "
              + "where cp.id_titularidade = 1 "
              + "and c.id_processadora = :idProcessadora "
              + "and c.id_instituicao = :idInstituicao "
              + "and p.documento = :documento "
              + "and c.id_prod_instituicao = :idProdutoInstituicao ",
      nativeQuery = true)
  Integer isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndTitularidade(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("documento") String documento,
      @Param("idProdutoInstituicao") Integer idProdutoInstituicao);

  @Query(
      value =
          "select count(c.*) from cadastral.conta_pagamento c "
              + "inner join cadastral.conta_pessoa cp on cp.id_conta = c.id_conta "
              + "inner join cadastral.pessoa p on p.id_pessoa = cp.id_pessoa "
              + "where cp.id_titularidade = 1 "
              + "and c.id_processadora = :idProcessadora "
              + "and c.id_instituicao = :idInstituicao "
              + "and p.documento = :documento "
              + "and c.id_prod_instituicao = :idProdutoInstituicao "
              + "and c.id_ponto_de_relacionamento = :idPontoRelacionamento ",
      nativeQuery = true)
  Integer
      isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
          @Param("idProcessadora") Integer idProcessadora,
          @Param("idInstituicao") Integer idInstituicao,
          @Param("documento") String documento,
          @Param("idProdutoInstituicao") Integer idProdutoInstituicao,
          @Param("idPontoRelacionamento") Integer idPontoRelacionamento);
}
