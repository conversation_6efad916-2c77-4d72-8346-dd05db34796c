package br.com.sinergico.controller.apielo;

import br.com.entity.suporte.QrCodeAccessToken;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.service.apielo.ApiEloService;
import br.com.sinergico.vo.qrcode.PostQrCodeAccessToken;
import br.com.sinergico.vo.qrcode.PostQrCodeAuthorization;
import br.com.sinergico.vo.qrcode.PostQrCodeAuthorizationAndCode;
import br.com.sinergico.vo.qrcode.PostQrCodeCompleteTransaction;
import br.com.sinergico.vo.qrcode.PostQrCodeStatusTransaction;
import br.com.sinergico.vo.qrcode.QrCodeCallBackRequest;
import br.com.sinergico.vo.qrcode.QrCodeEloSendTransactionResponse;
import br.com.sinergico.vo.qrcode.QrCodeParseResponse;
import br.com.sinergico.vo.qrcode.QrCodePublicKeyResponse;
import br.com.sinergico.vo.vcn.PostCreateNewVcnTokenV2;
import br.com.sinergico.vo.vcn.PostCreateNewVcnTokenV2Externo;
import br.com.sinergico.vo.vcn.PostCredenciaisElo;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/elo/")
public class ApiEloController extends UtilController {

  @Autowired EntityManager em;

  @Autowired private ApiEloService apiEloService;

  /*******************************
   * ENDPOINTS TOKENIZAÇÃO (VCN) *
   * *****************************/

  /**
   * Endpoint simplesmente para teste. O método apiEloService.queryServerPublicKey() é usado no
   * método que gera o sensitive, a string que contém os dados do cartão do cliente assinados
   */
  @ApiOperation(
      value = "Consultar Chave Pública da ELO para o VCN (Tokenização)",
      notes = "Retorna a chave pública do algoritmo Curvas Elípticas.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/vcn/chave-publica-elo",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MONITOR_VCN_ELO"})
  public ResponseEntity<?> consultarChavePublicaElo() {
    return new ResponseEntity<>(
        "Serviço inativado temporariamente.", HttpStatus.SERVICE_UNAVAILABLE);
  }

  /**
   * Foi informado que o access_token gerado no login dura apenas 10 minutos. É necessário confirmar
   * essa informação, caso seja o caso, deve se pensar em uma solução que acionará este point de X
   * em X minutos e atualizar o banco de dados com o access_token mais recente.
   */
  @ApiOperation(
      value = "Login de usuário na plataforma da ELO API de VCN",
      notes = "Realiza o login do usuário na plataforma da ELO para fazer chamadas à API.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/vcn/login",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MONITOR_VCN_ELO"})
  public ResponseEntity<?> loginElo(@RequestBody PostCredenciaisElo loginParams) {
    return new ResponseEntity<>(
        "Serviço inativado temporariamente.", HttpStatus.SERVICE_UNAVAILABLE);
  }

  @ApiOperation(
      value = "Gerar novo token VCN (Virtual Card Number)",
      notes =
          "Gera um token (cartão virtual) através da API da ELO usando os dados do cartão físico (criptografados) do usuário")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/vcn/gerar-token-sensitive-v2",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CRIAR_VCN_ELO"})
  public ResponseEntity<?> gerarTokenSensitiveV2(
      @RequestBody PostCreateNewVcnTokenV2 postCreateNewVcnTokenV2,
      HttpServletRequest httpServletRequest) {
    return new ResponseEntity<>(
        "Serviço inativado temporariamente.", HttpStatus.SERVICE_UNAVAILABLE);
  }

  @ApiOperation(
      value = "Gerar novo token VCN (Virtual Card Number)",
      notes =
          "Gera um token (cartão virtual) através da API da ELO usando os dados do cartão físico (criptografados) do usuário")
  @RequestMapping(
      value = "/vcn/gerar-token-sensitive-v2-externo",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> gerarTokenSensitiveV2Externo(
      @RequestBody PostCreateNewVcnTokenV2Externo postCreateNewVcnTokenV2Externo) {
    return new ResponseEntity<>(
        "Serviço inativado temporariamente.", HttpStatus.SERVICE_UNAVAILABLE);
    //    return
    // apiEloService.createCardTokenForUserBySensitiveV2Externo(postCreateNewVcnTokenV2Externo);
  }

  // deixado aqui para, caso peçam a implementação do ciclo de vida do VCN, já ter um ponto de
  // partida para a suspensão.
  //	@ApiOperation(value = "Suspender token VCN por token id", notes = "Suspende um card token
  // usando o cardTokenId e não o sensitive")
  //	@ApiImplicitParams({
  ////		@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
  //			@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key") })
  //	@RequestMapping(value = "/suspender-token", method = RequestMethod.POST, consumes =
  // MediaType.APPLICATION_JSON_UTF8_VALUE, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  ////	@Secured({ "ROLE_" })
  //	public ResponseEntity<CardTokenElo> suspenderTokenPorId(@RequestBody PostSuspendVcnToken
  // suspendTokenParams) {
  //		CardTokenElo token = apiEloService.suspendCardToken(suspendTokenParams.getAccessToken(),
  //				suspendTokenParams.getCardTokenId());
  //		return new ResponseEntity<>(token, HttpStatus.OK);
  //	}

  /*************************
   * 	ENDPOINTS QR CODE 	 *
   * ***********************/

  @ApiOperation(
      value = "Gerar accessToken QR Code para chamadas futuras na API",
      notes =
          "Gera um accessToken (com validade de alguns segundos) para realizar requisições para a API")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/qrcode/access-token",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MONITOR_QRCODE_ELO"})
  public ResponseEntity<QrCodeAccessToken> gerarAccessTokenQrCode(
      @RequestBody PostQrCodeAccessToken accessTokenQrCodeParams) {

    return new ResponseEntity<>(
        apiEloService.gerarTokenAcessoApiQrCode(accessTokenQrCodeParams.getIdInstituicao()),
        HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar chave pública da ELO",
      notes =
          "Retorna uma chave pública que deve ser utilizada para a criptografia dos dados sensíveis a serem enviados(dados do cartão e dados do portador).")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/qrcode/elo-public-key",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MONITOR_QRCODE_ELO"})
  public ResponseEntity<QrCodePublicKeyResponse> getChavePublicaQrCode(
      @RequestBody PostQrCodeAuthorization postParams) {

    return new ResponseEntity<>(
        apiEloService.getQrCodePublicKey(postParams.getAuthorization()), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Realizar o parse de um QR Code ELO",
      notes =
          "Realiza o parse de um QR Code no formato padrão ELO. Um QR Code válido deve terminar com a sequência \"6304\".")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/qrcode/parse",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CRIAR_TRANSACAO_QRCODE_ELO"})
  public ResponseEntity<QrCodeParseResponse> getInfoFromQrCode(
      @RequestBody PostQrCodeAuthorizationAndCode postParams,
      HttpServletRequest httpServletRequest) {

    return new ResponseEntity<QrCodeParseResponse>(
        callFunctionUserOrFunctionPortadorOrFunctionCorporativo(
            httpServletRequest,
            postParams,
            apiEloService::getQrCodeParsedUsuarioIssuer,
            apiEloService::getQrCodeParsedPortador,
            apiEloService::getQrCodeParsedCorporativo,
            em),
        HttpStatus.OK);
  }

  // Pretendemos tornar legado esse código. Substituí-lo pelo /qrcode/parse com autenticacao de
  // usuario Issuer
  // Está por trás de uma autenticação Basic
  @ApiOperation(
      value = "Realizar o parse de um QR Code ELO. Uso externo.",
      notes =
          "Realiza o parse de um QR Code no formato padrão ELO. Um QR Code válido deve terminar com a sequência \"6304\".")
  @RequestMapping(
      value = "/qrcode/parse-externo",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<QrCodeParseResponse> getInfoFromQrCodeExterno(
      @RequestBody PostQrCodeAuthorizationAndCode postParams) {

    return new ResponseEntity<QrCodeParseResponse>(
        apiEloService.getQrCodeParsedExterno(postParams), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Verificar o status da API de QRCode ELO",
      notes =
          "Verifica o status da API de QRCode. UP significa que está tudo normal e DOWN que está fora do ar.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/qrcode/status-api",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MONITOR_QRCODE_ELO"})
  public ResponseEntity<String> getStatusApiEloQrCode(
      @RequestBody PostQrCodeAuthorization postParams) {

    return new ResponseEntity<>(
        apiEloService.getStatusApiEloQrCode(postParams.getAuthorization()), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Iniciar um processo transacional com um QR code e um cartão de crédito",
      notes =
          "Inicia um processo transacional com a ELO com as informações da transação (QR Code) e o cartão pagador."
              + " Espera-se um código HTTP 202, indicando que a requisição foi aceita para processamento.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/qrcode/send-transaction",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CRIAR_TRANSACAO_QRCODE_ELO"})
  public ResponseEntity<QrCodeEloSendTransactionResponse>
      enviarTransacaoCompletaParaProcessamentoPelaELO(
          @RequestBody PostQrCodeCompleteTransaction postParams,
          HttpServletRequest httpServletRequest) {

    return callFunctionUserOrFunctionPortadorOrFunctionCorporativo(
        httpServletRequest,
        postParams,
        apiEloService::enviarTransacaoQrCodeCompletaParaProcessamentoPelaEloUsuarioIssuer,
        apiEloService::enviarTransacaoQrCodeCompletaParaProcessamentoPelaEloPortador,
        apiEloService::enviarTransacaoQrCodeCompletaParaProcessamentoEloCorporativo,
        em);
  }

  // TODO Arrumar essa API para se adequar aos criterios de seguranca e modularidade
  @ApiOperation(
      value = "Consulta os dados de uma transação solicitada anteriormente.",
      notes = "Consultar status de transação.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/qrcode/status-transaction",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_MONITOR_QRCODE_ELO"})
  public ResponseEntity<String> getInfo(
      @RequestBody PostQrCodeStatusTransaction postParams,
      HttpServletRequest request,
      HttpServletResponse response) {

    return new ResponseEntity<>(
        apiEloService.buscarStatusTransacaoQrCode(
            postParams.getAuthorization(), postParams.getTransactionId()),
        HttpStatus.OK);
  }

  // Não precisa de outros mecanismos de segurança, pois está atrás de um certificado
  @ApiOperation(
      value =
          "Callback para notificar a alteração no status de uma transação previamente solicitada na API.")
  @RequestMapping(
      value = "/qrcode/callbacks/transactions/{transactionId}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<String> getCallback(
      @PathVariable("transactionId") String transactionId,
      @RequestBody QrCodeCallBackRequest qrCodeCallBackRequest) {

    return apiEloService.getCallbackByTransactionId(transactionId, qrCodeCallBackRequest);
  }
}
