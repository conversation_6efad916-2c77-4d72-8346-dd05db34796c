package br.com.sinergico.enums;

public enum AntifraudeCafFacialObjetivosEnum {
  TROCA_SENHA_LOGIN(0, "TROCAR SENHA DE ACESSO APLICATIVO"),
  TROCA_SENHA_CARTAO(1, "ALTERAR SENHA DO CARTÃO"),
  DESBLOQUEAR_CARTAO_FISICO(2, "DESBLOQUEIO DE CARTÃO FÍSICO"),
  DESBLOQUEAR_CARTAO_VIRTUAL(3, "DESB<PERSON>OQUEIO DE CARTÃO VIRTUAL RECORRENTE"),
  CRIACAO_VCN(4, "CRIAR CARTÃO TEMPORÁRIO"),
  ONBOARDING_CAF_FORCADO(5, "ONBOARDING CAF FORÇADO"),
  TROCA_DE_DISPOSITIVO(6, "TROCA DE DISPOSITIVO"),
  SELFIE_ONBOARDING_CAF(7, "SELFIE ONBOARDING CAF"),
  ;

  private final Integer idObjetivo;
  private final String descObjetivo;

  AntifraudeCafFacialObjetivosEnum(Integer idObjetivo, String descObjetivo) {
    this.idObjetivo = idObjetivo;
    this.descObjetivo = descObjetivo;
  }

  public Integer getIdObjetivo() {
    return this.idObjetivo;
  }

  public String getDescObjetivo() {
    return this.descObjetivo;
  }

  public static AntifraudeCafFacialObjetivosEnum encontraObjetivoPorId(Integer idObjetivo) {
    for (AntifraudeCafFacialObjetivosEnum objetivo : values()) {
      if (objetivo.getIdObjetivo().equals(idObjetivo)) {
        return objetivo;
      }
    }
    throw new IllegalArgumentException("IdObjetivo inválido: " + idObjetivo);
  }
}
