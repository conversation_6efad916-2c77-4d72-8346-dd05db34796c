package br.com.sinergico.controller.cadastral;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;

import br.com.entity.cadastral.Credencial;
import br.com.json.bean.cadastral.TrocarEstadoNFCCredencialRequest;
import br.com.json.bean.pix.request.IncluirOrdemPagamentoRequest;
import br.com.json.bean.pix.request.vo.BeneficiarioVO;
import br.com.json.bean.pix.request.vo.ContaVO;
import br.com.json.bean.pix.request.vo.DadosOperacaoVO;
import br.com.json.bean.pix.request.vo.PagadorVO;
import br.com.json.bean.pix.request.vo.PessoaVO;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.CredencialService;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.BindingResult;

@ExtendWith(MockitoExtension.class)
class CredencialControllerTest {

  @Mock private CredencialService credencialService;

  @InjectMocks private CredencialController credencialController;

  @BeforeEach
  void setup() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  void alterarNFCCredencialIssuer() throws Exception {

    final TrocarEstadoNFCCredencialRequest request = new TrocarEstadoNFCCredencialRequest();
    request.setIdCredencial(1256263L);
    request.setEstado(1);

    BindingResult bindingResult = new BeanPropertyBindingResult(request, "request");

    Boolean response = false;

    final ResponseEntity<Boolean> alterarNFCCredencialIssuerResponseEntity =
        new ResponseEntity<>(response, HttpStatus.OK);
    credencialService.alterarEstadoNFCCredencialIssuer(
        any(Credencial.class), any(Integer.class), any(SecurityUser.class));
  }

  @Test
  void alterarNFCCredencialIssuer2() throws Exception {

    final TrocarEstadoNFCCredencialRequest request = new TrocarEstadoNFCCredencialRequest();
    request.setIdCredencial(1256263L);
    request.setEstado(1);

    BindingResult bindingResult = new BeanPropertyBindingResult(request, "request");

    Boolean response = false;

    final ResponseEntity<Boolean> alterarNFCCredencialIssuerResponseEntity =
        new ResponseEntity<>(response, HttpStatus.OK);
    credencialService.alterarEstadoNFCCredencialIssuer(
        any(Credencial.class), any(Integer.class), any(SecurityUser.class));

    credencialService.alterarEstadoNFCCredencialIssuer(
        request, MockUtilCredencial.getSecurityUser());
  }

  public static class MockUtilCredencial {

    public static SecurityUser getSecurityUser() {
      SecurityUser securityUser = new SecurityUser();
      IncluirOrdemPagamentoRequest incluirOrdemPagamentoRequest =
          new IncluirOrdemPagamentoRequest();
      PagadorVO pagadorVO = new PagadorVO();
      BeneficiarioVO beneficiarioVO = new BeneficiarioVO();
      DadosOperacaoVO dadosOperacaoVO = new DadosOperacaoVO();
      // Set the properties of pagadorVO, beneficiarioVO, and dadosOperacaoVO
      ContaVO contaVO = new ContaVO();
      PessoaVO pessoaVO = new PessoaVO();
      // Set the properties of contaVO
      contaVO.setIdConta("1220110");
      contaVO.setAgencia("2001");
      contaVO.setDataAbertura("21/05/2024");
      contaVO.setTipoId("1");
      contaVO.setIspbParticipante("23273917");
      // Set the properties of pessoaVO
      pessoaVO.setInscricaoNacional("05835504799");
      pessoaVO.setNome("LEONARDO PINTO DA SILVA MACHADO");
      pessoaVO.setNomeFantasia("null");
      pessoaVO.setTipoId("1");
      pagadorVO.setConta(contaVO);
      pagadorVO.setPessoa(pessoaVO);

      // Set the properties of beneficiarioVO
      beneficiarioVO.setEndToEnd("E23273917202501122022PpAEAhHQqV0");
      beneficiarioVO.setChave("034656f5-dd2c-4992-b827-8bca63b3d567");

      // Set the properties of dadosOperacaoVO
      dadosOperacaoVO.setNsu("x365784");
      dadosOperacaoVO.setCampoLivre("");
      dadosOperacaoVO.setValorOperacao(BigDecimal.ONE);

      incluirOrdemPagamentoRequest.setDadosOperacao(dadosOperacaoVO);
      incluirOrdemPagamentoRequest.setBeneficiario(beneficiarioVO);
      incluirOrdemPagamentoRequest.setPagador(pagadorVO);

      return securityUser;
    }
  }
}
