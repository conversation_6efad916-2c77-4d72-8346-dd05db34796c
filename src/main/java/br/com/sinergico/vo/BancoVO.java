package br.com.sinergico.vo;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang.StringUtils;

@Getter
@Setter
public class BancoVO {

  private Integer idBanco;
  private String descBanco;
  private String descCompleto;
  private String prioridade;

  public BancoVO(Integer idBanco, String descBanco, String prioridade) {
    this.idBanco = idBanco;
    this.descBanco = descBanco;
    this.descCompleto =
        StringUtils.leftPad(this.idBanco.toString(), 3, '0') + " - " + this.descBanco;
    this.prioridade = prioridade;
  }
}
