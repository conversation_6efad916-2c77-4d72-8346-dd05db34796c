package br.com.sinergico.service.suporte;

import br.com.entity.suporte.AcessoGrupo;
import br.com.entity.suporte.AcessoGrupoFuncionalidade;
import br.com.entity.suporte.AcessoGrupoFuncionalidadeId;
import br.com.entity.suporte.AcessoGrupoUsuario;
import br.com.entity.suporte.AcessoGrupoUsuarioEstabelecimento;
import br.com.entity.suporte.AcessoGrupoUsuarioEstabelecimentoId;
import br.com.entity.suporte.AcessoGrupoUsuarioId;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioEstabelecimento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.HierarquiaProcessadora;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.suporte.CadastrarGrupo;
import br.com.json.bean.suporte.GetGrupo;
import br.com.json.bean.suporte.VincularGrupoUsuario;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.criteria.UsuarioCriteria;
import br.com.sinergico.repository.suporte.AcessoGrupoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.Hierarquia;
import br.com.sinergico.util.TituloComparator;
import br.com.sinergico.vo.AcessoGrupoNode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.TreeMap;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import org.hibernate.Criteria;
import org.hibernate.criterion.Restrictions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.BindingResult;

@Service
public class GrupoService extends GenericService<AcessoGrupo, Integer> {

  private static final Integer ZERO = 0;
  private static final Integer GRUPO_MODELO = 0;
  private static final Integer NIVEL_PROCESSADORA = 1;
  private static final Integer NIVEL_INSTITUICAO = 2;
  private static final Integer NIVEL_REGIONAL = 3;
  private static final Integer NIVEL_FILIAL = 4;
  private static final Integer NIVEL_PONTO_DE_RELACIONAMENTO = 5;
  public static final AcessoGrupoNode ACESSO_GRUPO_NODE_TOPO = new AcessoGrupoNode(0, null, 0);
  public static final int ID_GRUPO_MODELO_B2B = 8;

  @Autowired private UsuarioCriteria usuarioCriteria;

  @Autowired private EntityManager em;

  @Autowired private AcessoGrupoRepository repository;

  @Autowired private AcessoUsuarioService usuarioService;

  @Autowired private AcessoUsuarioEstabelecimentoService acessoUsuarioEstabelecimentoService;

  @Autowired private GrupoFuncionalidadeService grupoFuncionalidadeService;

  @Lazy @Autowired private HierarquiaPontoRelacionamentoService prService;

  @Autowired private AcessoGrupoUsuarioService acessoGrupoUsuarioService;

  @Autowired
  private AcessoGrupoUsuarioEstabelecimentoService acessoGrupoUsuarioEstabelecimentoService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  private List<AcessoGrupo> acessoGrupo = new ArrayList<AcessoGrupo>();

  @Autowired
  public GrupoService(AcessoGrupoRepository repo) {
    super(repo);
  }

  public List<AcessoGrupo> findAll(SecurityUser user, Integer first, Integer max, String termo) {

    if (termo.length() > 0) {
      try {
        Integer id = Integer.parseInt(termo);

        return buscarGruposPorId(id, user, first, max);

      } catch (NumberFormatException e) {
        return buscarGruposPorDescricao(termo, user, first, max);
      }
    } else {
      return buscarGrupos(user, first, max);
    }
  }

  private List<AcessoGrupo> buscarGruposPorId(
      Integer id, SecurityUser user, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max);

    List<AcessoGrupo> grupos = new ArrayList<AcessoGrupo>();
    Collection<Integer> idsGruposDelete = new ArrayList<>();
    for (AcessoGrupo stm : user.getAcessoGrupos()) {
      idsGruposDelete.add(stm.getIdGrupo());
    }

    if (user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)) {
      grupos =
          repository.findByIdProcessadoraAndIdGrupoPaiNotAndIdGrupoOrderByIdGrupo(
              user.getIdProcessadora(), GRUPO_MODELO, id, top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_INSTITUICAO)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  id,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_REGIONAL)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  id,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_FILIAL)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  id,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  user.getIdPontoDeRelacionamento(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  id,
                  top);
    }

    if (grupos.size() > 0) {
      for (AcessoGrupo tmp : grupos) {
        tmp.setDescGrupoPai(repository.getDescByIdGrupo(tmp.getIdGrupoPai()));
        tmp.setDescNivelHierarquia(tmp.getNivel().getDescNivelHierarquia());
      }
    }

    return grupos;
  }

  private List<AcessoGrupo> buscarGruposPorDescricao(
      String termo, SecurityUser user, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max);

    List<AcessoGrupo> grupos = new ArrayList<AcessoGrupo>();
    Collection<Integer> idsGruposDelete = new ArrayList<>();
    for (AcessoGrupo stm : user.getAcessoGrupos()) {
      idsGruposDelete.add(stm.getIdGrupo());
    }

    if (user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdGrupoPaiNotAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
                  user.getIdProcessadora(), GRUPO_MODELO, termo, top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_INSTITUICAO)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  termo,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_REGIONAL)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  termo,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_FILIAL)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  termo,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  user.getIdPontoDeRelacionamento(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  termo,
                  top);
    }

    if (grupos.size() > 0) {
      for (AcessoGrupo tmp : grupos) {
        tmp.setDescGrupoPai(repository.getDescByIdGrupo(tmp.getIdGrupoPai()));
        tmp.setDescNivelHierarquia(tmp.getNivel().getDescNivelHierarquia());
      }
    }

    return grupos;
  }

  private List<AcessoGrupo> buscarGrupos(SecurityUser user, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max);

    List<AcessoGrupo> grupos = new ArrayList<AcessoGrupo>();
    Collection<Integer> idsGruposDelete = new ArrayList<>();
    for (AcessoGrupo stm : user.getAcessoGrupos()) {
      idsGruposDelete.add(stm.getIdGrupo());
    }

    if (user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)) {
      grupos =
          repository.findByIdProcessadoraAndIdGrupoPaiNotOrderByIdGrupo(
              user.getIdProcessadora(), GRUPO_MODELO, top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_INSTITUICAO)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_REGIONAL)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_FILIAL)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  top);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      grupos =
          repository
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  user.getIdPontoDeRelacionamento(),
                  GRUPO_MODELO,
                  idsGruposDelete,
                  top);
    }

    if (grupos.size() > 0) {
      for (AcessoGrupo tmp : grupos) {
        tmp.setDescGrupoPai(repository.getDescByIdGrupo(tmp.getIdGrupoPai()));
        tmp.setDescNivelHierarquia(tmp.getNivel().getDescNivelHierarquia());
      }
    }

    return grupos;
  }

  public Integer countGrupos(SecurityUser user, String termo) {

    if (termo.length() > 0) {
      try {
        Integer id = Integer.parseInt(termo);
        return contarGruposPorId(id, user);

      } catch (NumberFormatException e) {
        return contarGruposPorDescricao(termo, user);
      }
    } else {
      return contarGrupos(user);
    }
  }

  private Integer contarGruposPorId(Integer id, SecurityUser user) {
    Collection<Integer> idsGruposDelete = new ArrayList<>();
    for (AcessoGrupo stm : user.getAcessoGrupos()) {
      idsGruposDelete.add(stm.getIdGrupo());
    }

    if (user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)) {
      return repository.countByIdProcessadoraAndIdGrupoPaiNotAndIdGrupoOrderByIdGrupo(
          user.getIdProcessadora(), GRUPO_MODELO, id);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_INSTITUICAO)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
              user.getIdProcessadora(), user.getIdInstituicao(), GRUPO_MODELO, idsGruposDelete, id);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_REGIONAL)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              GRUPO_MODELO,
              idsGruposDelete,
              id);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_FILIAL)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              GRUPO_MODELO,
              idsGruposDelete,
              id);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNotAndIdGrupoNotInAndIdGrupoOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              user.getIdPontoDeRelacionamento(),
              GRUPO_MODELO,
              idsGruposDelete,
              id);
    }

    return null;
  }

  private Integer contarGruposPorDescricao(String termo, SecurityUser user) {
    Collection<Integer> idsGruposDelete = new ArrayList<>();
    for (AcessoGrupo stm : user.getAcessoGrupos()) {
      idsGruposDelete.add(stm.getIdGrupo());
    }

    if (user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)) {
      return repository
          .countByIdProcessadoraAndIdGrupoPaiNotAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
              user.getIdProcessadora(), GRUPO_MODELO, termo);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_INSTITUICAO)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              GRUPO_MODELO,
              idsGruposDelete,
              termo);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_REGIONAL)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              GRUPO_MODELO,
              idsGruposDelete,
              termo);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_FILIAL)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              GRUPO_MODELO,
              idsGruposDelete,
              termo);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNotAndIdGrupoNotInAndDescGrupoContainingIgnoreCaseOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              user.getIdPontoDeRelacionamento(),
              GRUPO_MODELO,
              idsGruposDelete,
              termo);
    }

    return null;
  }

  private Integer contarGrupos(SecurityUser user) {
    Collection<Integer> idsGruposDelete = new ArrayList<>();
    for (AcessoGrupo stm : user.getAcessoGrupos()) {
      idsGruposDelete.add(stm.getIdGrupo());
    }

    if (user.getIdHierarquiaNivel().equals(NIVEL_PROCESSADORA)) {
      return repository.countByIdProcessadoraAndIdGrupoPaiNotOrderByIdGrupo(
          user.getIdProcessadora(), GRUPO_MODELO);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_INSTITUICAO)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
              user.getIdProcessadora(), user.getIdInstituicao(), GRUPO_MODELO, idsGruposDelete);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_REGIONAL)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              GRUPO_MODELO,
              idsGruposDelete);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_FILIAL)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              GRUPO_MODELO,
              idsGruposDelete);
    } else if (user.getIdHierarquiaNivel().equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      return repository
          .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNotAndIdGrupoNotInOrderByIdGrupo(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              user.getIdPontoDeRelacionamento(),
              GRUPO_MODELO,
              idsGruposDelete);
    }

    return null;
  }

  public AcessoGrupo findByIdGrupoPai(Integer idGrupoPai, Integer idGrupo) {
    Query query =
        em.createQuery(
            "select g from AcessoGrupo g where g.idGrupoPai = :idGrupoPai and g.id = :id");
    query.setParameter("idGrupoPai", idGrupoPai);
    query.setParameter("id", idGrupo);
    return (AcessoGrupo) query.getSingleResult();
  }

  public AcessoGrupo findById(Integer id) {
    Criteria criteria = grupoCriteria();
    criteria.add(Restrictions.eq("id", id));
    return (AcessoGrupo) criteria.uniqueResult();
  }

  public AcessoGrupo findByIdGrupo(Integer id) {
    GetGrupo grupo = repository.findGrupoByIdGrupo(id);

    AcessoGrupo acessoGrupo = new AcessoGrupo();
    BeanUtils.copyProperties(grupo, acessoGrupo);

    return acessoGrupo;
  }

  @SuppressWarnings("unchecked")
  public List<AcessoGrupo> findByUsuarioId(Integer id) {
    Query q =
        em.createNativeQuery(
            "WITH RECURSIVE q AS ( "
                + "select distinct grupo.id_grupo, grupo.* from suporte.acesso_grupo grupo where grupo.id_grupo=grupo.id_grupo_pai"
                + "    UNION"
                + "    select distinct grupo.id_grupo, grupo.* from suporte.acesso_grupo grupo inner join "
                + "suporte.acesso_grupo_usuario gusu on gusu.id_usuario = ? and gusu.id_grupo = grupo.id_grupo) "
                + "SELECT * FROM q;",
            AcessoGrupo.class);
    q.setParameter(1, id);
    List<AcessoGrupo> result = q.getResultList();
    return result;
  }

  private Criteria grupoCriteria() {
    return usuarioCriteria.getHierarquiaCriteria(AcessoGrupo.class, null, null);
  }

  public List<AcessoGrupo>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNot(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento,
          Integer tipoEstabelecimento) {

    return repository
        .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndTipoEstabelecimentoAndIdGrupoPaiNot(
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoDeRelacionamento,
            tipoEstabelecimento,
            GRUPO_MODELO);
  }

  public Boolean
      countByIdNivelHierarquiaAndIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndDescGrupoAndIdGrupoPai(
          Integer idNivelHierarquia,
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento,
          String descGrupo,
          Integer idGrupoPai) {
    if (repository
            .countByIdNivelHierarquiaAndIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndDescGrupoAndIdGrupoPai(
                idNivelHierarquia,
                idProcessadora,
                idInstituicao,
                idRegional,
                idFilial,
                idPontoDeRelacionamento,
                descGrupo,
                idGrupoPai)
        > 0) {
      return true;
    } else {
      return false;
    }
  }

  @Transactional
  public AcessoGrupo criarGrupo(CadastrarGrupo model, SecurityUser user) {

    if (model.getIdInstituicao() != null && model.getIdInstituicao() == ZERO) {
      model.setIdInstituicao(null);
    }
    if (model.getIdRegional() != null && model.getIdRegional() == ZERO) {
      model.setIdRegional(null);
    }
    if (model.getIdFilial() != null && model.getIdFilial() == ZERO) {
      model.setIdFilial(null);
    }
    if (model.getIdPontoDeRelacionamento() != null && model.getIdPontoDeRelacionamento() == ZERO) {
      model.setIdPontoDeRelacionamento(null);
    }
    if (model.getIdGrupoPai() == null) {
      model.setIdGrupoPai(ZERO);
    }

    if (countByIdNivelHierarquiaAndIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndDescGrupoAndIdGrupoPai(
        model.getIdNivelHierarquia(),
        model.getIdProcessadora(),
        model.getIdInstituicao(),
        model.getIdRegional(),
        model.getIdFilial(),
        model.getIdPontoDeRelacionamento(),
        model.getDescGrupo(),
        model.getIdGrupoPai())) {
      throw new GenericServiceException(
          "Já existe um grupo cadastrado com essa mesma hierarquia e nome!");
    }

    AcessoGrupo acessoGrupo = new AcessoGrupo();
    acessoGrupo.setIdUsuarioInclusao(user.getIdUsuario());
    acessoGrupo.setDtHrInclusao(new Date());
    acessoGrupo = prepareGrupo(model, acessoGrupo, user);

    return acessoGrupo;
  }

  @Transactional
  public AcessoGrupo prepareGrupo(
      CadastrarGrupo model, AcessoGrupo acessoGrupo, SecurityUser user) {

    if (model.getIdGrupoPai().equals(GRUPO_MODELO)
        && !model.getIdNivelHierarquia().equals(NIVEL_PROCESSADORA)) {
      model.setIdGrupoPai(
          findGrupoAdmHierarquiaAnterior(
              model.getIdNivelHierarquia(),
              model.getIdProcessadora(),
              model.getIdInstituicao(),
              model.getIdRegional(),
              model.getIdFilial(),
              model.getIdPontoDeRelacionamento()));
    }

    HierarquiaProcessadora processadora =
        usuarioService.getHierarquiaProcessadoraById(model.getIdProcessadora());
    UtilController.checkProcessadora(processadora);

    BeanUtils.copyProperties(model, acessoGrupo, getNullPropertyNames(model));

    new Hierarquia.Builder()
        .usuario(user)
        .nivel(acessoGrupo.getIdNivelHierarquia())
        .processadora(acessoGrupo.getIdProcessadora())
        .instituicao(acessoGrupo.getIdInstituicao())
        .regional(acessoGrupo.getIdRegional())
        .filial(acessoGrupo.getIdFilial())
        .checkHierarquiaPermission()
        .verificaUnidadesHierarquia();

    acessoGrupo = save(acessoGrupo);

    if (acessoGrupo.getIdPontoDeRelacionamento() != null) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              acessoGrupo.getIdProcessadora(),
              acessoGrupo.getIdInstituicao(),
              acessoGrupo.getIdRegional(),
              acessoGrupo.getIdFilial(),
              acessoGrupo.getIdPontoDeRelacionamento());
      HierarquiaPontoDeRelacionamento pr = prService.findById(id);

      if (pr == null) {
        throw new GenericServiceException("O ponto de relacionamento selecionado não existe!");
      }

      if (pr.getB2b()) {
        AcessoGrupoFuncionalidadeId agfId = new AcessoGrupoFuncionalidadeId();
        agfId.setIdGrupo(acessoGrupo.getIdGrupoPai());
        List<AcessoGrupoFuncionalidade> admPai = new ArrayList<AcessoGrupoFuncionalidade>();
        admPai = grupoFuncionalidadeService.buscafuncionalidadesAcessoGrupo(acessoGrupo);
        for (int i = 0; i < admPai.size(); i++) {
          AcessoGrupoFuncionalidade admFilho = new AcessoGrupoFuncionalidade();
          admFilho.setPropagavel(true);
          admFilho.setId(
              new AcessoGrupoFuncionalidadeId(
                  acessoGrupo.getIdGrupo(), admPai.get(i).getId().getIdFuncionalidade()));
          grupoFuncionalidadeService.save(admFilho);
        }
      }
    }

    return acessoGrupo;
  }

  @Transactional
  public AcessoGrupo prepareGrupoWithoutCheckHierarquia(
      CadastrarGrupo model, AcessoGrupo acessoGrupo) {

    if (model.getIdGrupoPai().equals(GRUPO_MODELO)
        && !model.getIdNivelHierarquia().equals(NIVEL_PROCESSADORA)) {
      model.setIdGrupoPai(
          findGrupoAdmHierarquiaAnterior(
              model.getIdNivelHierarquia(),
              model.getIdProcessadora(),
              model.getIdInstituicao(),
              model.getIdRegional(),
              model.getIdFilial(),
              model.getIdPontoDeRelacionamento()));
    }

    HierarquiaProcessadora processadora =
        usuarioService.getHierarquiaProcessadoraById(model.getIdProcessadora());
    UtilController.checkProcessadora(processadora);

    BeanUtils.copyProperties(model, acessoGrupo, getNullPropertyNames(model));

    acessoGrupo = save(acessoGrupo);

    if (acessoGrupo.getIdPontoDeRelacionamento() != null) {
      HierarquiaPontoDeRelacionamentoId id =
          new HierarquiaPontoDeRelacionamentoId(
              acessoGrupo.getIdProcessadora(),
              acessoGrupo.getIdInstituicao(),
              acessoGrupo.getIdRegional(),
              acessoGrupo.getIdFilial(),
              acessoGrupo.getIdPontoDeRelacionamento());
      HierarquiaPontoDeRelacionamento pr = prService.findById(id);

      if (pr == null) {
        throw new GenericServiceException("O ponto de relacionamento selecionado não existe!");
      }

      if (pr.getB2b()) {
        AcessoGrupoFuncionalidadeId agfId = new AcessoGrupoFuncionalidadeId();
        agfId.setIdGrupo(acessoGrupo.getIdGrupoPai());
        List<AcessoGrupoFuncionalidade> admPai = new ArrayList<AcessoGrupoFuncionalidade>();
        admPai = grupoFuncionalidadeService.buscafuncionalidadesAcessoGrupo(acessoGrupo);
        for (int i = 0; i < admPai.size(); i++) {
          AcessoGrupoFuncionalidade admFilho = new AcessoGrupoFuncionalidade();
          admFilho.setPropagavel(true);
          admFilho.setId(
              new AcessoGrupoFuncionalidadeId(
                  acessoGrupo.getIdGrupo(), admPai.get(i).getId().getIdFuncionalidade()));
          grupoFuncionalidadeService.save(admFilho);
        }
      }
    }

    return acessoGrupo;
  }

  @Transactional
  public ResponseEntity<HashMap<String, Object>> saveOrDelete(
      VincularGrupoUsuario model, BindingResult result, boolean save, SecurityUser user) {

    if (model.getIdGrupos().length == 0) {
      throw new InvalidRequestException(
          "Solicite a inclusão/deleção de ao menos um Grupo ao Usuário.", result);
    }

    if (model.getTipoEstabelecimento() != null
        && Constantes.USUARIO_DE_ESTABELECIMENTO.equals(model.getTipoEstabelecimento())) {
      AcessoUsuarioEstabelecimento usuarioEstabelecimentoRequisicao =
          acessoUsuarioEstabelecimentoService.findById(model.getIdUsuario());
      if (usuarioEstabelecimentoRequisicao == null) {
        throw new GenericServiceException("Usuario Estabelecimento inexistente.");
      }
      user.isNivelHierarquiaAllowed(usuarioEstabelecimentoRequisicao.getIdNivelHierarquia());

      // TODO Fazer a versao do metodo impedeTentativaDeAlterarUsuarioComPermissoesSuperiores para
      // usuarioEstabelecimento

    } else {
      AcessoUsuario usuarioRequisicao = acessoUsuarioService.findByIdUsuario(model.getIdUsuario());
      if (usuarioRequisicao == null) {
        throw new GenericServiceException("Usuario inexistente.");
      }
      user.isNivelHierarquiaAllowed(usuarioRequisicao.getIdHierarquiaNivel());

      impedeTentativaDeAlterarUsuarioComPermissoesSuperiores(model, user.getIdUsuario());
    }

    if (save) {
      for (Integer tmp : model.getIdGrupos()) {
        if (tmp != null) {
          if (model.getTipoEstabelecimento() != null
              && Constantes.USUARIO_DE_ESTABELECIMENTO.equals(model.getTipoEstabelecimento())) {
            AcessoGrupoUsuarioEstabelecimentoId id =
                new AcessoGrupoUsuarioEstabelecimentoId(tmp, model.getIdUsuario());
            AcessoGrupoUsuarioEstabelecimento agu = new AcessoGrupoUsuarioEstabelecimento();
            agu.setId(id);
            agu.setDtHrInclusao(new Date());
            agu.setIdUsuarioManutencao(user.getIdUsuario());
            acessoGrupoUsuarioEstabelecimentoService.save(agu);
          } else {
            AcessoGrupoUsuarioId id = new AcessoGrupoUsuarioId(tmp, model.getIdUsuario());
            AcessoGrupoUsuario agu = new AcessoGrupoUsuario();
            agu.setId(id);
            agu.setDtHrInclusao(new Date());
            agu.setIdUsuarioInclusao(user.getIdUsuario());
            acessoGrupoUsuarioService.save(agu);
          }
        }
      }
    } else {
      for (Integer tmp : model.getIdGrupos()) {
        if (tmp != null) {
          if (model.getTipoEstabelecimento() != null
              && Constantes.USUARIO_DE_ESTABELECIMENTO.equals(model.getTipoEstabelecimento())) {
            AcessoGrupoUsuarioEstabelecimentoId id =
                new AcessoGrupoUsuarioEstabelecimentoId(tmp, model.getIdUsuario());
            AcessoGrupoUsuarioEstabelecimento agu =
                acessoGrupoUsuarioEstabelecimentoService.findById(id);
            if (agu == null) {
              throw new GenericServiceException(
                  "Impossível remover o grupo "
                      + tmp
                      + ". Este usuário não pertence à este grupo!");
            }
            acessoGrupoUsuarioEstabelecimentoService.desassociarGrupoUsuario(agu);
          } else {
            AcessoGrupoUsuarioId id = new AcessoGrupoUsuarioId(tmp, model.getIdUsuario());
            AcessoGrupoUsuario agu = acessoGrupoUsuarioService.findById(id);
            if (agu == null) {
              throw new GenericServiceException(
                  "Impossível remover o grupo "
                      + tmp
                      + ". Este usuário não pertence à este grupo!");
            }
            acessoGrupoUsuarioService.desassociarGrupoUsuario(agu);
          }
        }
      }
    }

    HashMap<String, Object> map = new HashMap<>();
    if (save) {
      map.put("msg", "Grupos adicionados ao usuário com sucesso!");
    } else {
      map.put("msg", "Grupos removidos do usuário com sucesso!");
    }
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  public void impedeTentativaDeAlterarUsuarioComPermissoesSuperiores(
      VincularGrupoUsuario model, Integer idUsuarioLogado) {
    impedeTentativaDeAlterarUsuarioComPermissoesSuperiores(
        model.getIdUsuario(), model.getIdGrupos(), idUsuarioLogado);
  }

  public void impedeTentativaDeAlterarUsuarioComPermissoesSuperiores(
      Integer idUsuarioAModificar, Integer[] gruposUsuarioAModificar, Integer idUsuarioLogado) {

    // Impede a vinculação de super-grupos
    if (Arrays.asList(gruposUsuarioAModificar).contains(Constantes.SUPER_GRUPO_2)) {
      throw new GenericServiceException(
          "Tentativa de atribuir ou retirar grupos com permissoes mais elevadas do que permitido",
          String.format("Grupo %d bloqueou a mudança.", Constantes.SUPER_GRUPO_2),
          HttpStatus.FORBIDDEN);
    }

    // Pegar os grupos do UsuarioLogado
    Set<AcessoGrupo> gruposUsuarioLogado = repository.findByIdAcessoUsuario(idUsuarioLogado);
    TreeMap<Integer, AcessoGrupoNode> gruposPaiUsuarioLogadoTree = new TreeMap<>();
    gruposPaiUsuarioLogadoTree.put(0, ACESSO_GRUPO_NODE_TOPO);
    // Carregar a Estrutura gruposPaiUsuarioLogadoTree com os grupos pai do UsuarioLogado
    for (AcessoGrupo grupo : gruposUsuarioLogado) {
      getFirstGrupoNodePai(gruposPaiUsuarioLogadoTree, grupo);
    }
    // Remover da estrutura os grupos que o UsuarioLogado ja possui
    for (AcessoGrupo grupo : gruposUsuarioLogado) {
      gruposPaiUsuarioLogadoTree.remove(grupo.getIdGrupo());
    }

    // Pegar os grupos do UsuarioAModificar
    Set<AcessoGrupo> gruposUsuarioRequest = repository.findByIdAcessoUsuario(idUsuarioAModificar);
    // Pegar os grupos do request
    Set<AcessoGrupo> gruposRequest =
        repository.findByIdGrupoIn(Arrays.asList(gruposUsuarioAModificar));
    // Somar aos gruposUsuarioRequest os grupos do request
    gruposRequest.addAll(gruposUsuarioRequest);
    // Carregar a Estrutura gruposRequestNodeTree com a soma dos grupos do UsuarioAModificar e os
    // grupos request
    TreeMap<Integer, AcessoGrupoNode> gruposRequestNodeTree = acessoGrupoToGrupoNode(gruposRequest);

    // Impedir que o UsuarioAModificar tenha grupos que sejam pai de algum grupo do UsuarioLogado
    // Grupos do UsuarioLogado nao podem ser folha dos grupos do UsuarioAModificar
    // Buscar todos os pais de UsuarioLogado e checar se UsuarioAModificar e seus grupos adicionados
    // contem qualquer valor dos pais
    for (Integer grupoPaiUsuarioTreeKey : gruposPaiUsuarioLogadoTree.descendingKeySet()) {
      if (gruposRequestNodeTree.containsKey(grupoPaiUsuarioTreeKey)) {
        throw new GenericServiceException(
            "Tentativa de modificar usuário ou atribuir/retirar grupos com permissoes mais elevadas do que permitido",
            String.format("Grupo %d bloqueou a mudança.", grupoPaiUsuarioTreeKey));
      }
    }
  }

  protected TreeMap<Integer, AcessoGrupoNode> acessoGrupoToGrupoNode(
      Set<AcessoGrupo> acessoGrupoSet) {

    TreeMap<Integer, AcessoGrupoNode> acessoGrupoNodeTreeMap = new TreeMap<>();
    for (AcessoGrupo grupo : acessoGrupoSet) {
      acessoGrupoNodeTreeMap.put(grupo.getIdGrupo(), new AcessoGrupoNode(grupo.getIdGrupo()));
    }
    return acessoGrupoNodeTreeMap;
  }

  private void getFirstGrupoNodePai(
      TreeMap<Integer, AcessoGrupoNode> gruposPaiUsuarioLogadoTree, AcessoGrupo grupo) {

    // Precisa do grupo 0 pre-carregado
    if (gruposPaiUsuarioLogadoTree.containsKey(grupo.getIdGrupoPai())) {
      return;
    }
    AcessoGrupo grupoPai = repository.findById(grupo.getIdGrupoPai()).orElse(null);
    getGrupoNodePai(gruposPaiUsuarioLogadoTree, grupoPai);
  }

  private AcessoGrupoNode getGrupoNodePai(
      TreeMap<Integer, AcessoGrupoNode> gruposPaiUsuarioLogadoTree, AcessoGrupo grupo) {

    if (gruposPaiUsuarioLogadoTree.containsKey(grupo.getIdGrupo())) {
      return gruposPaiUsuarioLogadoTree.get(grupo.getIdGrupo());
    }

    // Caso base da recursao
    if (grupo.getIdGrupoPai() == 0) {
      AcessoGrupoNode acessoGrupoNode =
          new AcessoGrupoNode(
              grupo.getIdGrupo(), ACESSO_GRUPO_NODE_TOPO, grupo.getIdNivelHierarquia());
      gruposPaiUsuarioLogadoTree.put(grupo.getIdGrupo(), acessoGrupoNode);
      return acessoGrupoNode;
    }

    AcessoGrupo grupoPai = repository.findById(grupo.getIdGrupoPai()).orElse(null);

    // Recursao
    AcessoGrupoNode acessoGrupoNodePai = getGrupoNodePai(gruposPaiUsuarioLogadoTree, grupoPai);

    AcessoGrupoNode acessoGrupoNode =
        new AcessoGrupoNode(
            grupo.getIdGrupo(),
            new AcessoGrupoNode(
                grupoPai.getIdGrupo(), acessoGrupoNodePai, grupoPai.getIdNivelHierarquia()),
            grupo.getIdNivelHierarquia());
    gruposPaiUsuarioLogadoTree.put(grupo.getIdGrupo(), acessoGrupoNode);
    return acessoGrupoNode;
  }

  public List<AcessoGrupo> findGruposParaAssociar(
      SecurityUser user,
      Integer idNivelHierarquia,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer tipoEstabelecimento) {

    List<AcessoGrupo> grupo = new ArrayList<AcessoGrupo>();

    if (idInstituicao == 0) {
      idInstituicao = null;
    }
    if (idRegional == 0) {
      idRegional = null;
    }
    if (idFilial == 0) {
      idFilial = null;
    }
    if (idPontoDeRelacionamento == 0) {
      idPontoDeRelacionamento = null;
    }
    if (tipoEstabelecimento != null && tipoEstabelecimento == 0) {
      tipoEstabelecimento = null;
    }

    if (idProcessadora == null || idProcessadora == 0) {
      idProcessadora = null;
    } else {

      grupo =
          findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndIdGrupoPaiNot(
              idProcessadora,
              idInstituicao,
              idRegional,
              idFilial,
              idPontoDeRelacionamento,
              tipoEstabelecimento);

      if (user.getIdHierarquiaNivel().equals(idNivelHierarquia)) {
        List<AcessoGrupo> grupoControle = new ArrayList<AcessoGrupo>();
        List<AcessoGrupo> grupoFinal = grupo;

        for (AcessoGrupo i : grupo) {

          //					if(user.getAcessoGrupos().contains(i) && i.getIdGrupoPai() == 0){
          //						grupo = grupoFinal;
          //						break;
          //					}else
          if (user.getAcessoGrupos().contains(i)
              && i.getIdGrupoPai() != 0
              && !getAcessoGrupo().contains(i)) {
            grupoControle.add(i);
            getHierarquia(i, grupoFinal, i);
            if (getAcessoGrupo() != null) {
              grupoControle.addAll(getAcessoGrupo());
              setAcessoGrupo(new ArrayList<AcessoGrupo>());
            }

            grupo = grupoControle;
          }
        }
      }

      TituloComparator comparator = new TituloComparator();
      grupo.sort(comparator);
    }
    return grupo;
  }

  public Integer findGrupoAdmHierarquiaAnterior(
      Integer idNivelHierarquia,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {

    List<AcessoGrupo> grupos = new ArrayList<AcessoGrupo>();

    if (idNivelHierarquia.equals(NIVEL_INSTITUICAO)) {
      grupos =
          repository.findByIdNivelHierarquiaAndIdProcessadoraAndIdGrupoPaiNot(
              NIVEL_PROCESSADORA, idProcessadora, GRUPO_MODELO);
      for (AcessoGrupo tmp : grupos) {
        AcessoGrupo grupoPai = findByIdGrupo(tmp.getIdGrupoPai());
        if (grupoPai.getIdGrupoPai().equals(GRUPO_MODELO)) {
          return tmp.getIdGrupo();
        }
      }
    } else if (idNivelHierarquia.equals(NIVEL_REGIONAL)) {
      grupos =
          repository.findByIdNivelHierarquiaAndIdProcessadoraAndIdInstituicao(
              NIVEL_INSTITUICAO, idProcessadora, idInstituicao);
    } else if (idNivelHierarquia.equals(NIVEL_FILIAL)) {
      grupos =
          repository.findByIdNivelHierarquiaAndIdProcessadoraAndIdInstituicaoAndIdRegional(
              NIVEL_REGIONAL, idProcessadora, idInstituicao, idRegional);
    } else if (idNivelHierarquia.equals(NIVEL_PONTO_DE_RELACIONAMENTO)) {
      grupos =
          repository
              .findByIdNivelHierarquiaAndIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilial(
                  NIVEL_FILIAL, idProcessadora, idInstituicao, idRegional, idFilial);
    }

    for (AcessoGrupo tmp : grupos) {
      AcessoGrupo grupoPai = findByIdGrupo(tmp.getIdGrupoPai());
      if (!grupoPai.getIdNivelHierarquia().equals(tmp.getIdNivelHierarquia())) {
        return tmp.getIdGrupo();
      }
    }

    return null;
  }

  public AcessoGrupo getHierarquia(
      AcessoGrupo grupo, List<AcessoGrupo> grupoFinal, AcessoGrupo grupoOriginal) {
    for (AcessoGrupo temp : grupoFinal) {
      if (temp.getIdGrupoPai().equals(grupo.getIdGrupo()) && !getAcessoGrupo().contains(temp)) {
        acessoGrupo.add(temp);
        return getHierarquia(temp, grupoFinal, grupoOriginal);
      }
    }
    for (AcessoGrupo var : grupoFinal) {
      if (var.getIdGrupoPai().equals(grupoOriginal.getIdGrupo())
          && !getAcessoGrupo().contains(var)) {
        acessoGrupo.add(var);
        return getHierarquia(var, grupoFinal, grupoOriginal);
      }
    }
    for (AcessoGrupo tmp : grupoFinal) {
      if (tmp.getIdGrupoPai() != 0) {
        AcessoGrupo grupoPai = findById(tmp.getIdGrupoPai());
        if (acessoGrupo.contains(grupoPai) && !acessoGrupo.contains(tmp)) {
          return getHierarquia(grupoPai, grupoFinal, grupoOriginal);
        }
      }
    }
    return null;
  }

  public List<AcessoGrupo> hideUserGroups(SecurityUser user, List<AcessoGrupo> grupos) {
    List<AcessoGrupo> exclusao = new ArrayList<AcessoGrupo>();

    for (AcessoGrupo temp : grupos) {
      if (user.getAcessoGrupos().contains(temp)) {
        exclusao.add(temp);
      }
    }
    grupos.removeAll(exclusao);
    return grupos;
  }

  public List<AcessoGrupo> getAcessoGrupo() {
    return acessoGrupo;
  }

  public void setAcessoGrupo(List<AcessoGrupo> acessoGrupo) {
    this.acessoGrupo = acessoGrupo;
  }

  public String findDescGrupoByIdGrupo(Integer idGrupo) {
    return repository.getDescByIdGrupo(idGrupo);
  }

  public AcessoGrupo findGrupoByIdWithFuncionalidades(Integer id) {
    return repository.findGrupoByIdWithFuncionalidades(id);
  }

  public AcessoGrupo buscaGrupoPaiB2B(
      int nivelHierarquiaPr, Integer semGrupoPai, Integer idInstituicao) {

    AcessoGrupo grupoPai = new AcessoGrupo();

    grupoPai =
        repository.findByIdNivelHierarquiaAndIdGrupoPaiAndIdInstituicao(
            nivelHierarquiaPr, semGrupoPai, idInstituicao);

    if (grupoPai == null) {

      grupoPai =
          repository.findByIdNivelHierarquiaAndIdGrupoAndIdGrupoPaiAndIdInstituicaoIsNull(
              nivelHierarquiaPr, ID_GRUPO_MODELO_B2B, semGrupoPai);
    }

    return grupoPai;
  }
}
