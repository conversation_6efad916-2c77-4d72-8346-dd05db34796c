package br.com.sinergico.service.cadastral;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import br.com.entity.cadastral.LimiteTransacaoConta;
import br.com.entity.cadastral.LimiteTransacaoProduto;
import br.com.entity.cadastral.LimiteTransacaoTipo;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.enums.LimiteTransacaoTipoEnum;
import br.com.sinergico.repository.cadastral.LimiteTransacaoContaRepository;
import br.com.sinergico.repository.cadastral.LimiteTransacaoProdutoRepository;
import br.com.sinergico.repository.cadastral.LimiteTransacaoTipoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.vo.BuscaLimiteVO;
import br.com.sinergico.vo.LimiteDiarioVO;
import br.com.sinergico.vo.LimiteTransacaoContaVO;
import br.com.sinergico.vo.LimiteTransacaoProdutoVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class LimitesContaServiceTest {

  public static final int ID_USUARIO_VALIDO = 1596;
  public static final int LIMITE_ATIVO = 1;
  public static final long ID_CONTA_VALIDA = 1596;

  @Mock private SecurityUser user;
  @Mock AcessoUsuarioService acessoUsuarioService;
  @Mock private LimiteTransacaoTipoRepository limiteTransacaoTipoRepository;
  @Mock private LimiteTransacaoContaRepository limiteTransacaoContaRepository;
  @Mock private LimiteTransacaoProdutoRepository limiteTransacaoProdutoRepository;

  @InjectMocks private LimitesContaService limiteContaService;

  @Nested
  class cadastrarLimiteDiario {

    @Test
    void deveLancarExcecao_QuandoMotivoAlteracaoForNulo() {
      LimiteDiarioVO limiteDiarioVO = new LimiteDiarioVO();
      limiteDiarioVO.setMotivoAlteracao(null);

      SecurityUser securityUser = new SecurityUser();
      BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();

      GenericServiceException exception =
          assertThrows(
              GenericServiceException.class,
              () ->
                  limiteContaService.cadastrarLimiteDiario(
                      limiteDiarioVO, securityUser, buscaLimiteVO));

      assertEquals(ConstantesErro.MOTIVO_ALTERACAO.getMensagem(), exception.getMessage());
    }

    @Test
    void deveLancarExcecao_QuandoValorLimiteGlobalForZero() {
      LimiteDiarioVO limiteDiarioVO = new LimiteDiarioVO();
      limiteDiarioVO.setMotivoAlteracao("Ajuste de limite");
      limiteDiarioVO.setValorLimiteGlobal(BigDecimal.ZERO);

      SecurityUser securityUser = new SecurityUser();
      BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();

      GenericServiceException exception =
          assertThrows(
              GenericServiceException.class,
              () ->
                  limiteContaService.cadastrarLimiteDiario(
                      limiteDiarioVO, securityUser, buscaLimiteVO));

      assertEquals(ConstantesErro.VALOR_LIMITE_DIARIO.getMensagem(), exception.getMessage());
    }

    @Test
    void deveCriarNovoLimiteConta_QuandoContaExiste() {
      LimiteDiarioVO limiteDiarioVO = new LimiteDiarioVO();
      limiteDiarioVO.setMotivoAlteracao("Ajuste de limite");
      limiteDiarioVO.setValorLimiteGlobal(BigDecimal.TEN);
      limiteDiarioVO.setConta(true);
      limiteDiarioVO.setIdConta(1L);

      SecurityUser securityUser = new SecurityUser();
      securityUser.setIdUsuario(1);

      BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();

      LimiteTransacaoTipo limiteTransacaoTipo = new LimiteTransacaoTipo();

      when(limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(any()))
          .thenReturn(limiteTransacaoTipo);

      when(limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
              anyLong(), any(), any()))
          .thenReturn(null);

      limiteContaService.cadastrarLimiteDiario(limiteDiarioVO, securityUser, buscaLimiteVO);

      verify(limiteTransacaoContaRepository, times(1)).save(any(LimiteTransacaoConta.class));
    }

    @Test
    void deveCriarNovoLimiteProduto_QuandoProdutoExiste() {
      LimiteDiarioVO limiteDiarioVO = new LimiteDiarioVO();
      limiteDiarioVO.setMotivoAlteracao("Ajuste de limite");
      limiteDiarioVO.setValorLimiteGlobal(BigDecimal.TEN);
      limiteDiarioVO.setConta(false);
      limiteDiarioVO.setIdProdInstituicao(1);

      SecurityUser securityUser = new SecurityUser();
      securityUser.setIdUsuario(1);

      BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();

      LimiteTransacaoTipo limiteTransacaoTipo = new LimiteTransacaoTipo();

      when(limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(any()))
          .thenReturn(limiteTransacaoTipo);

      when(limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
              anyInt(), any(), any()))
          .thenReturn(null);

      limiteContaService.cadastrarLimiteDiario(limiteDiarioVO, securityUser, buscaLimiteVO);

      verify(limiteTransacaoProdutoRepository, times(1)).save(any(LimiteTransacaoProduto.class));
    }
  }

  @Nested
  class listarLimitesDiarioConta {

    @Test
    void deveRetornarListaDeLimites_QuandoExistiremLimites() {
      Long idConta = ID_CONTA_VALIDA;
      Integer idTipoTransacao = 28;
      SecurityUser securityUser = new SecurityUser();
      securityUser.setIdUsuario(10);

      LimiteTransacaoConta limite1 = new LimiteTransacaoConta();
      limite1.setId(ID_CONTA_VALIDA);
      limite1.setIdConta(idConta);
      limite1.setIdUsuarioInclusao(ID_USUARIO_VALIDO);
      limite1.setValorLimiteGlobal(BigDecimal.valueOf(5000));
      limite1.setSituacaoStatus(LIMITE_ATIVO);

      List<LimiteTransacaoConta> listaMock = List.of(limite1);

      when(limiteTransacaoContaRepository.listaLimiteTransacaoConta(idConta, idTipoTransacao))
          .thenReturn(listaMock);
      when(acessoUsuarioService.findNomeById(ID_USUARIO_VALIDO)).thenReturn("Usuário Teste");

      List<LimiteTransacaoContaVO> resultado =
          limiteContaService.listarLimitesDiarioConta(idConta, idTipoTransacao, securityUser);

      assertNotNull(resultado);
      assertEquals(1, resultado.size());
      assertEquals("Usuário Teste", resultado.get(0).getNomeUsuario());
      assertEquals(BigDecimal.valueOf(5000), resultado.get(0).getValorLimiteGlobal());
    }

    @Test
    void deveRetornarListaVazia_QuandoNaoExistiremLimites() {
      Long idConta = 1L;
      Integer idTipoTransacao = 2;
      SecurityUser securityUser = new SecurityUser();

      when(limiteTransacaoContaRepository.listaLimiteTransacaoConta(idConta, idTipoTransacao))
          .thenReturn(Collections.emptyList());

      List<LimiteTransacaoContaVO> resultado =
          limiteContaService.listarLimitesDiarioConta(idConta, idTipoTransacao, securityUser);

      assertNotNull(resultado);
      assertTrue(resultado.isEmpty());
    }
  }

  @Nested
  class buscarLimiteDiarioProduto {

    @Test
    void deveRetornarLimiteTransacaoProduto_QuandoEncontrado() {
      // Arrange
      BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();
      buscaLimiteVO.setIdTipoTransacao(LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA);

      Integer idProdInstituicao = 100;
      LimiteTransacaoTipo limiteTransacaoTipo = new LimiteTransacaoTipo();
      LimiteTransacaoProduto limiteTransacaoProduto = new LimiteTransacaoProduto();

      when(limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
              LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA))
          .thenReturn(limiteTransacaoTipo);
      when(limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
              idProdInstituicao, limiteTransacaoTipo, LIMITE_ATIVO))
          .thenReturn(limiteTransacaoProduto);

      // Act
      LimiteTransacaoProduto resultado =
          limiteContaService.buscarLimiteDiarioProduto(buscaLimiteVO, idProdInstituicao);

      // Assert
      assertNotNull(resultado);
      assertEquals(limiteTransacaoProduto, resultado);
    }

    @Test
    void deveRetornarNulo_QuandoLimiteTransacaoProdutoNaoEncontrado() {
      // Arrange
      BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();
      buscaLimiteVO.setIdTipoTransacao(LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA);

      Integer idProdInstituicao = 100;
      LimiteTransacaoTipo limiteTransacaoTipo = new LimiteTransacaoTipo();

      when(limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(
              LimiteTransacaoTipoEnum.LIMITE_DIARIO_CONTA))
          .thenReturn(limiteTransacaoTipo);
      when(limiteTransacaoProdutoRepository.findByIdProdInstituicaoAndLimiteTransacaoTipoAndStatus(
              idProdInstituicao, limiteTransacaoTipo, LIMITE_ATIVO))
          .thenReturn(null);

      // Act
      LimiteTransacaoProduto resultado =
          limiteContaService.buscarLimiteDiarioProduto(buscaLimiteVO, idProdInstituicao);

      // Assert
      assertNull(resultado);
    }

    @Test
    void deveRetornarListaLimitesDiarioProduto_QuandoExistemLimites() {
      // Arrange (Preparação)
      Integer idProdInstituicao = 1;
      Integer idTipoTransacao = 2;

      LimiteTransacaoProduto limite1 = new LimiteTransacaoProduto();
      limite1.setId(100);
      limite1.setValorLimiteGlobal(BigDecimal.TEN);
      limite1.setDtHrInclusao(LocalDateTime.now());
      limite1.setDtHrAlteracao(LocalDateTime.now());
      limite1.setStatus(LIMITE_ATIVO);
      limite1.setIdUsuarioInclusao(1);

      List<LimiteTransacaoProduto> listaMock = List.of(limite1);

      when(limiteTransacaoProdutoRepository.listaLimiteTransacaoProduto(
              idProdInstituicao, idTipoTransacao))
          .thenReturn(listaMock);

      // Act (Execução)
      List<LimiteTransacaoProdutoVO> resultado =
          limiteContaService.listarLimitesDiarioProduto(idProdInstituicao, idTipoTransacao, user);

      // Assert (Verificação)
      assertNotNull(resultado);
      assertEquals(1, resultado.size());
      assertEquals(limite1.getId(), resultado.get(0).getIdLimite());
      assertEquals(BigDecimal.TEN, resultado.get(0).getValorLimiteGlobal());
      assertEquals(limite1.getStatus(), resultado.get(0).getStatus());

      verify(limiteTransacaoProdutoRepository, times(1))
          .listaLimiteTransacaoProduto(idProdInstituicao, idTipoTransacao);
    }

    @Test
    void deveRetornarListaVazia_QuandoNaoExistemLimites() {
      // Arrange
      Integer idProdInstituicao = 1;
      Integer idTipoTransacao = 2;

      when(limiteTransacaoProdutoRepository.listaLimiteTransacaoProduto(
              idProdInstituicao, idTipoTransacao))
          .thenReturn(Collections.emptyList());

      // Act
      List<LimiteTransacaoProdutoVO> resultado =
          limiteContaService.listarLimitesDiarioProduto(idProdInstituicao, idTipoTransacao, user);

      // Assert
      assertNotNull(resultado);
      assertTrue(resultado.isEmpty());

      verify(limiteTransacaoProdutoRepository, times(1))
          .listaLimiteTransacaoProduto(idProdInstituicao, idTipoTransacao);
    }
  }

  @Test
  void cadastrarLimiteDiarioNovo() {

    LimiteDiarioVO limiteDiarioVO = new LimiteDiarioVO();
    limiteDiarioVO.setMotivoAlteracao("Ajuste de limite");
    limiteDiarioVO.setValorLimiteGlobal(BigDecimal.valueOf(30000));
    limiteDiarioVO.setConta(true);
    limiteDiarioVO.setIdConta(3L);

    SecurityUser securityUser = new SecurityUser();
    securityUser.setIdUsuario(2334);

    BuscaLimiteVO buscaLimiteVO = new BuscaLimiteVO();

    LimiteTransacaoTipo limiteTransacaoTipo = new LimiteTransacaoTipo();

    when(limiteTransacaoTipoRepository.findLimiteTransacaoTipoById(any()))
        .thenReturn(limiteTransacaoTipo);

    when(limiteTransacaoContaRepository.findByIdContaAndSituacaoStatusAndLimiteTransacaoTipo_Id(
            anyLong(), any(), any()))
        .thenReturn(null);

    limiteContaService.cadastrarLimiteDiario(limiteDiarioVO, securityUser, buscaLimiteVO);

    verify(limiteTransacaoContaRepository, times(1)).save(any(LimiteTransacaoConta.class));
  }
}
