package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ProdutoInstituicaoGrupoProdutos;
import br.com.entity.cadastral.ProdutoInstituicaoGrupoProdutosId;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ProdutoInstituicaoGrupoProdutosRepository
    extends JpaRepository<ProdutoInstituicaoGrupoProdutos, ProdutoInstituicaoGrupoProdutosId> {

  List<ProdutoInstituicaoGrupoProdutos>
      findByProdutoInstituicaoGrupoProdutosId_IdProdutoInstituicao(Integer idProdutoInstituicao);

  List<ProdutoInstituicaoGrupoProdutos>
      findByProdutoInstituicaoGrupoProdutosId_IdGrupoOrderByPosicaoHierarquiaDesc(Long idGrupo);

  List<ProdutoInstituicaoGrupoProdutos>
      findByProdutoInstituicaoGrupoProdutosId_IdGrupoOrderByOrdemApresentacaoAsc(Long idGrupo);

  List<ProdutoInstituicaoGrupoProdutos> findByProdutoInstituicaoGrupoProdutosId_IdGrupo(
      Long idGrupo);

  List<ProdutoInstituicaoGrupoProdutos> findByProdutoInstituicaoGrupoProdutosIdIdGrupo(
      Long idGrupo);
}
