package br.com.sinergico.service.cadastral;

import static br.com.sinergico.service.cadastral.CredencialService.*;
import static br.com.sinergico.util.Constantes.*;
import static br.com.sinergico.util.ConstantesB2B.*;
import static br.com.sinergico.util.ConstantesErro.PTL_CONTA_NAO_PERTENCE_AO_PORTADOR;
import static br.com.sinergico.util.ConstantesErro.PTL_NENHUMA_CONTA_PERTENCE_AO_PORTADOR;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

import br.com.client.rest.jcard.json.bean.AccountBalance;
import br.com.client.rest.jcard.json.bean.AccountStatement;
import br.com.client.rest.jcard.json.bean.AccountStatementEntrie;
import br.com.client.rest.jcard.json.bean.AccountStatementResponse;
import br.com.client.rest.jcard.json.bean.CreateAccountBalance;
import br.com.client.rest.jcard.json.bean.CreateTaxAccount;
import br.com.client.rest.jcard.json.bean.Customer;
import br.com.client.rest.jcard.json.bean.GetAccountBalanceResponse;
import br.com.client.rest.jcard.json.bean.GetAccountStatementResponse;
import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.client.rest.jcard.json.bean.GetTaxAccountResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.client.rest.jcard.json.bean.TaxAccount;
import br.com.client.rest.jcard.json.bean.VincularCardAnonymousRequest;
import br.com.entity.cadastral.ComplementoProdutoSocialBRB;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPagamentoFatura;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.CorporativoLogin;
import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.entity.cadastral.CorporativoResponsavelCredencial;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.CredencialConta;
import br.com.entity.cadastral.CredencialPreEmitida;
import br.com.entity.cadastral.DocumentoConta;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.PerfilTarifarioTransacao;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PlanoSaudeProduto;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracaoCredito;
import br.com.entity.cadastral.ProdutoInstituidor;
import br.com.entity.cadastral.ProdutoMcc;
import br.com.entity.cadastral.Proposta;
import br.com.entity.cadastral.SetorFilial;
import br.com.entity.jcard.LogTransacoes;
import br.com.entity.jcard.LogTransacoesControle;
import br.com.entity.suporte.Agencia;
import br.com.entity.suporte.AgenciaId;
import br.com.entity.suporte.CotacaoPontos;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.LogNotificacaoAlterStatusContaCeler;
import br.com.entity.suporte.LogRegistroAlterarStatus;
import br.com.entity.suporte.LogUltimasContas;
import br.com.entity.suporte.MapaStatus;
import br.com.entity.suporte.Plastico;
import br.com.entity.suporte.TipoEnderecoProduto;
import br.com.entity.suporte.TipoStatus;
import br.com.entity.suporte.TipoTitularidade;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.exceptions.JcardServiceException;
import br.com.json.bean.CredencialGerada;
import br.com.json.bean.cadastral.AlterarAdicionalPessoa;
import br.com.json.bean.cadastral.ArquivoCvRequest;
import br.com.json.bean.cadastral.AtivarInativarProdutoRequest;
import br.com.json.bean.cadastral.BuscaContaDocumentoProduto;
import br.com.json.bean.cadastral.BuscaContaPagamentoRetorno;
import br.com.json.bean.cadastral.CadastrarContaDigital;
import br.com.json.bean.cadastral.CadastrarContaPagPessoaCredPreEmitidaRequest;
import br.com.json.bean.cadastral.CadastrarContaPagamentoPessoaRequest;
import br.com.json.bean.cadastral.CadastrarPessoaFisica;
import br.com.json.bean.cadastral.CadastrarPessoaJuridica;
import br.com.json.bean.cadastral.CadastrarPessoaPDAFRequest;
import br.com.json.bean.cadastral.CadastrarPortadorLogin;
import br.com.json.bean.cadastral.CartaoAdicionalRequest;
import br.com.json.bean.cadastral.ConfiguracaoParcelamentoVO;
import br.com.json.bean.cadastral.ContaPagamentoRequest;
import br.com.json.bean.cadastral.ContaPagamentoResponse;
import br.com.json.bean.cadastral.ContasCredenciaisReplicaveisReponse;
import br.com.json.bean.cadastral.CredencialResumida;
import br.com.json.bean.cadastral.CriarContaPagamento;
import br.com.json.bean.cadastral.DadosCredencial;
import br.com.json.bean.cadastral.DadosPortador;
import br.com.json.bean.cadastral.DesbloquearCredencialCompleta;
import br.com.json.bean.cadastral.EnderecoPessoaRequest;
import br.com.json.bean.cadastral.EnderecosPessoaRequest;
import br.com.json.bean.cadastral.ExtratoTransRejeitada;
import br.com.json.bean.cadastral.FunctionCodeProdutoPlataformaEnum;
import br.com.json.bean.cadastral.GerarCredencialRequest;
import br.com.json.bean.cadastral.GetDadosConsumo;
import br.com.json.bean.cadastral.HierarquiaPontoRelacionamentoRequest;
import br.com.json.bean.cadastral.ListarContaPagamentoGrupoResponse;
import br.com.json.bean.cadastral.PessoaAdicionalPDAFRequest;
import br.com.json.bean.cadastral.PessoaAdicionalResponse;
import br.com.json.bean.cadastral.PlanoSaudeContratadoVO;
import br.com.json.bean.cadastral.PortadorCartaoPreEmitidoTO;
import br.com.json.bean.cadastral.TermoAdesaoContaVO;
import br.com.json.bean.cadastral.TransacoesNaoApresentadas;
import br.com.json.bean.cadastral.TrocarEstadoCredencialJcard;
import br.com.json.bean.enums.StatusJcardEnum;
import br.com.json.bean.suporte.ComunicadoContaViaPush;
import br.com.json.bean.suporte.ExtratoRequestVo;
import br.com.json.bean.suporte.GetExtratoCredencial;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.json.bean.suporte.GetSaldoGrupoResponse;
import br.com.json.bean.suporte.LogSMSContaResponse;
import br.com.json.bean.suporte.LogUltimaContaModel;
import br.com.json.bean.suporte.SmsContaByFiltersRequest;
import br.com.json.bean.suporte.ValorCargaProdutoInstituicao;
import br.com.json.bean.transacional.CadastroLancamentoManual;
import br.com.json.bean.transacional.TransferenciaEntreConta;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.ArranjoInstituicaoEnum;
import br.com.sinergico.enums.StatusRegistroAlterarEnum;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.facade.cadastral.ContaPagamentoFacade;
import br.com.sinergico.repository.cadastral.ContaPagamentoHistoricoStatusViewRepository;
import br.com.sinergico.repository.cadastral.ContaPagamentoRepository;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.jcard.impl.LogTransacoesRepositoryImpl;
import br.com.sinergico.repository.suporte.CotacaoPontosRepository;
import br.com.sinergico.repository.suporte.LogNotificacaoStatusContaCelerRepository;
import br.com.sinergico.repository.suporte.PlasticoRepository;
import br.com.sinergico.security.ConsultaRestritaService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.GeradorContaService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.corporativo.CorporativoService;
import br.com.sinergico.service.gatewaypagto.GatewayPagtoExternoService;
import br.com.sinergico.service.jcard.AccountBalanceService;
import br.com.sinergico.service.jcard.AccountService;
import br.com.sinergico.service.jcard.AccountStatementService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.jcard.CustomerService;
import br.com.sinergico.service.jcard.LogTransacoesControleService;
import br.com.sinergico.service.jcard.TaxAccountService;
import br.com.sinergico.service.jcard.TransacoesOmitidasExtratoService;
import br.com.sinergico.service.pix.ContaTransacionalPagamentoService;
import br.com.sinergico.service.suporte.AgenciaService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.LogUltimasContasService;
import br.com.sinergico.service.suporte.MapaStatusService;
import br.com.sinergico.service.suporte.SolicitacaoPreCadastroService;
import br.com.sinergico.service.suporte.TipoEnderecoProdutoService;
import br.com.sinergico.service.suporte.TipoStatusService;
import br.com.sinergico.service.transacional.TEDService;
import br.com.sinergico.util.Abreviador;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.ContaPagamentoUtil;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.ObjectUtil;
import br.com.sinergico.util.StatusCredencialComparator;
import br.com.sinergico.util.Util;
import br.com.sinergico.util.bigdecimal.BigDecimalUtils;
import br.com.sinergico.validator.UtilValidator;
import br.com.sinergico.vo.ContaPagamentoCadastroAvanceVO;
import br.com.sinergico.vo.ContaPagamentoCafVO;
import br.com.sinergico.vo.ContaPagamentoHistoricoStatusViewVO;
import br.com.sinergico.vo.ContaPagamentoIntegracaoVO;
import br.com.sinergico.vo.ContaPagamentoVO;
import br.com.sinergico.vo.ContasPorProdutoPortador;
import br.com.sinergico.vo.CredenciaisProdutoVirtualVO;
import br.com.sinergico.vo.DadosPortadorPontoRelacionamentoB2BVO;
import br.com.sinergico.vo.DocumentoOCRPropostaVO;
import br.com.sinergico.vo.ExisteMatriculaEmpresaVO;
import com.google.common.base.Strings;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
public class ContaPagamentoService extends GenericService<ContaPagamento, Long> {

  private static final int TAMANHO_CPF = 11;
  private static final int AUTOR = 1;
  private static final int PROD_PLAT_CREDITO = 9;
  private static final int COMPRA = 1;
  private static final String ERRO_SALDO_INSUFICIENTE = "not.sufficient.funds";
  private static final int PRIMEIRO_DIA = 1;
  private static final int ULTIMO_MINUTO_SEGUNDO_DIA = 59;
  private static final int ULTIMA_HORA_DIA = 23;
  private static final int ZERO_HORA_MINUTO_SEGUNDO = 0;
  private static final int ADICIONAL = 2;
  private static final int PESSOA_FISICA = 1;
  private static final String ADMINISTRATIVE = "ADMINISTRATIVE";
  private static final int TYPE_CREDITO = 1;
  private static final int TYPE_DEBITO = 2;
  private static final int NEGATIVO = -1;

  private static final int CREDITO = 1;

  public static final int DEBITADO = NEGATIVO;

  private static final int CREDITADO = 1;

  private static final Integer DEBITO = 2;

  private static final boolean CRIAR_CARTAO_VIRTUAL = true;
  private static final boolean NAO_CRIAR_CARTAO_VIRTUAL = false;

  private static final int TITULAR = 1;

  private static final int INICIO_CONTA = 0;

  private static final Double VALOR_ABRIR_CONTA = 0d;

  private static final Integer PESSOA_JURIDICA = 2;

  private static final Integer DEFAULT_RELACIONAMENTO = 1;

  private static final String FMT_DD_MM_YYYY = "dd/MM/yyyy";

  private static final String FMT_D_MMM = "d MMM";

  private static final String FMT_MM_YY = "MM/yy";

  private static final Integer APLICABILIDADE_CONTA = 2;

  private static final Integer BLOQUEIO_CRIACAO = 0;
  private static final Integer STATUS_ATIVO = 1;
  private static final List<Integer> STATUS_ATIVO_OU_ORIGEM_LIST =
      Arrays.asList(STATUS_ATIVO, BLOQUEIO_CRIACAO);

  private static final Integer STATUS_CRELIQ = 68;
  private static final Integer STATUS_ACORDO_CRELIQ_CONCLUIDO = 72;
  private static final Integer STATUS_ACORDO_PERDA_CONCLUIDO = 73;
  private static final Integer STATUS_CREQLIQ_QUITADO = 74;
  private static final Integer STATUS_PERDA_QUITADA = 75;
  private static final Integer STATUS_PERDA = 70;

  private static final Integer APLICABILIDADE_CREDENCIAL = 1;

  private static final Integer ENDERECO_RESIDENCIAL = 1;

  private static final int PRIMEIRA_POSICAO = 0;

  private static final int QTD_DIG_ULTIMOS_4 = 4;

  private static final char CHAR_ZERO = '0';

  private static final Integer ID_ONE_SIGNAL = 1;

  private static final Integer ENVIO_PUSH_ENVIO_TRANSFERENCIA = 4;
  private static final Integer ENVIO_PUSH_RECEBIMENTO_TRANSFERENCIA = 5;

  static final List<String> CODS_TRANSACAO_SALDO_MIGRADO_DEVEDOR_CREDOR =
      Arrays.asList("296", "297", "730", "731");
  static final List<String> CODS_TRANSACAO_PAGAMENTOS_COM_COMPROVANTE =
      Collections.singletonList(String.valueOf(Constantes.COD_TRANSACAO_DEBITO_GENERICO));
  static final List<String> CODS_TRANSACAO_TEDS_COM_COMPROVANTE =
      Collections.singletonList(String.valueOf(Constantes.COD_TRANSACAO_TED));
  static final List<String> CODS_TRANSACAO_RECARGAS_COM_COMPROVANTE =
      Arrays.asList(
          String.valueOf(Constantes.COD_TRANSACAO_VOUCHER_RESGATE),
          String.valueOf(Constantes.COD_TRANSACAO_RESGATE_RECARGA_CELULAR));
  public static final int SETOR_FILIAL = 6;

  private final ReentrantLock lock = new ReentrantLock();

  private ContaPagamentoRepository contaPagamentoRepository;

  private static final Logger log = LoggerFactory.getLogger(ContaPagamentoService.class);

  @Autowired private PerfilTarifarioTransacaoService perfilTarifarioTransacaoService;

  @Autowired private TaxAccountService taxAccountService;

  @Lazy @Autowired private EnderecoPessoaService enderecoPessoaService;

  @Autowired @Lazy private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacioService;

  @Autowired private MapaStatusService mapaStatusService;

  @Autowired private CustomerService customerService;

  @Autowired private LogUltimasContasService ultimasContasService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfigService;

  @Autowired private SolicitacaoPreCadastroService preCadastroService;

  @Autowired
  private ProdutoInstituicaoCorrespondenteService produtoInstituicaoCorrespondenteService;

  @Autowired private CardService cardService;

  @Autowired private TipoEnderecoProdutoService tipoEndProdService;

  @Autowired private HierarquiaInstituicaoService instituicaoService;

  @Autowired
  private @Lazy HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

  @Autowired private AccountBalanceService accountBalanceService;

  @Autowired private AccountStatementService accountStatementService;

  @Autowired private AgenciaService agenciaService;

  @Autowired private DocumentoContaService documentoContaService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private LogTransacoesControleService logTransacoesControleService;

  @Autowired private ContaPagamentoFaturaService contaPagamentoFaturaService;

  @Autowired private CotacaoPontosRepository cotacaoPontosRepository;

  @Autowired private LogNotificacaoStatusContaCelerRepository logStatusContaCelerRepository;

  @Autowired private TipoStatusService tipoStatusService;

  @Autowired private PlanoSaudeContratadoService planoSaudeContratadoService;

  @Autowired private GatewayPagtoExternoService gatewayPagtoExternoService;

  @Autowired private TEDService tedService;

  @Autowired private LogTransacoesRepositoryImpl logTransacoesRepositoryImpl;

  @Lazy @Autowired private EmailService emailService;

  @Autowired private GeradorContaService geradorContaService;

  @Autowired private SetorFilialService setorFilialService;

  @Autowired private PessoaService pessoaService;

  @Autowired private ConsultaRestritaService consultaRestritaService;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private ComplementoProdutoSocialBRBService complementoProdutoSocialBRBService;

  @Autowired @Lazy private ContaPagamentoFacade contaPagamentoFacade;

  @Autowired private CredencialRepository credencialRepository;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired private PortadorLoginContaService portadorLoginContaService;

  @Autowired private ProdutoMccService produtoMccService;

  @Autowired private CredencialContaService credencialContaService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private PlasticoRepository plasticoRepo;

  @Autowired private B2bFaturaPixService b2bFaturaPixService;

  @Autowired private TransacoesOmitidasExtratoService transacoesOmitidasExtratoService;

  @Autowired private AntifraudeService antifraudeService;

  @Autowired private PropostaService propostaService;

  @Autowired
  private ConfiguracaoParcelamentoPessoaB2BService configuracaoParcelamentoPessoaB2BService;

  @Autowired @Lazy private ProdutoContratadoService produtoContratadoService;

  @Autowired
  private ContaPagamentoHistoricoStatusViewRepository contaPagamentoHistoricoStatusViewRepository;

  @Autowired private UtilService utilService;
  @Autowired private ContaTransacionalPagamentoService contaTransacionalPagamentoService;

  @Autowired private CorporativoService corporativoService;

  @Autowired
  public ContaPagamentoService(ContaPagamentoRepository repo) {
    super(repo);
    contaPagamentoRepository = repo;
  }

  public ContaPagamento findOneByIdContaAndIdRelacionamento(Long idConta) {
    return contaPagamentoRepository.findOneByIdContaAndIdRelacionamento(
        idConta, DEFAULT_RELACIONAMENTO);
  }

  public List<ContaPagamento> findByContasPessoaIdPessoa(Long idPessoa) {
    return contaPagamentoRepository.findByContasPessoaIdPessoa(idPessoa);
  }

  public List<ContaPagamento> findByIdPessoa(Long idPessoa) {
    return contaPagamentoRepository.findByIdPessoa(idPessoa);
  }

  public List<ContaPagamento> findDadosContaByIdPessoa(String documento, Integer idInstituicao) {
    return contaPagamentoRepository.findDadosContaByIdPessoa(documento, idInstituicao);
  }

  public Long findByIdPortadorLogin(Long idPortadorLogin) {
    return contaPagamentoRepository.findByIdPortadorLogin(idPortadorLogin);
  }

  public ProdutoInstituicaoConfiguracao getProdutoConfig(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    ProdutoInstituicaoConfiguracao produto =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            idProcessadora, idProdInstituicao, idInstituicao);
    if (produto == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar as configurações do produto da conta!");
    }
    return produto;
  }

  public ComunicadoContaViaPush getComunicadoPushTransferenciaCredencialOrigem(
      Credencial credencialOrigem, Double valor) {

    ComunicadoContaViaPush comunicado = new ComunicadoContaViaPush();
    comunicado.setIdConta(credencialOrigem.getIdConta());
    comunicado.setIdCredencial(credencialOrigem.getIdCredencial());
    comunicado.setIdGateway(ID_ONE_SIGNAL);
    comunicado.setMensagem("Transferência (Envio) no valor de R$ " + valor);
    comunicado.setTipoEventoConta(ENVIO_PUSH_ENVIO_TRANSFERENCIA);

    return comunicado;
  }

  public ComunicadoContaViaPush getComunicadoPushTransferenciaCredencialDestino(
      Credencial credencialDestino, Double valor) {

    ComunicadoContaViaPush comunicado = new ComunicadoContaViaPush();
    comunicado.setIdConta(credencialDestino.getIdConta());
    comunicado.setIdCredencial(credencialDestino.getIdCredencial());
    comunicado.setIdGateway(ID_ONE_SIGNAL);
    comunicado.setMensagem("Transferência (Recebimento) no valor de R$ " + valor);
    comunicado.setTipoEventoConta(ENVIO_PUSH_RECEBIMENTO_TRANSFERENCIA);

    return comunicado;
  }

  private BigDecimal getSaldoDisponivel(
      String idInstituicao, String idAccountCode, String idMoeda) {

    AccountBalance accountBalance =
        AccountBalanceService.prepareAccountBalance(idInstituicao, idAccountCode, idMoeda);

    GetAccountBalanceResponse response = accountBalanceService.findAccountBalance(accountBalance);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível buscar o saldo da conta: " + response.getErrors());
    }

    return response.getResponse().getBalance();
  }

  public void salvarConta(ContaPagamento conta) {
    save(conta);
  }

  /**
   * Método transacional responsável por criar uma conta
   * completa(pessoa,conta,contaPessoa,credencial) para uma pessoa
   *
   * @param cadastrarContaPagPessoa
   * @return número da conta gerada
   */
  @Transactional
  public ContaPagamento cadastrarContaPagamentoPessoa(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, SecurityUser user) {

    Pessoa pessoa = new Pessoa();

    for (ValorCargaProdutoInstituicao valoresCargasProduto :
        cadastrarContaPagPessoa.getValoresCargasProdutos()) {
      if (!prodInstConfigService.obterPermiteMultiplasContas(
          valoresCargasProduto.getIdProdutoInstituicao())) {
        try {
          if (cadastrarContaPagPessoa.getDocumento().length() > 12
              && cadastrarContaPagPessoa.getIdProcessadora() != null
              && cadastrarContaPagPessoa.getIdInstituicao() != null
              && cadastrarContaPagPessoa.getDocumento() != null
              && cadastrarContaPagPessoa.getIdRegional() != null
              && cadastrarContaPagPessoa.getIdFilial() != null
              && cadastrarContaPagPessoa.getIdPontoDeRelacionamento() != null) {
            pessoa =
                getContaPagamentoFacade()
                    .findOneByHierarquia(
                        cadastrarContaPagPessoa.getIdProcessadora(),
                        cadastrarContaPagPessoa.getIdInstituicao(),
                        cadastrarContaPagPessoa.getDocumento(),
                        cadastrarContaPagPessoa.getRazaoSocial() != null
                            ? Integer.valueOf(Constantes.COD_DOC_CNPJ_2)
                            : cadastrarContaPagPessoa.getTipoPessoa(),
                        cadastrarContaPagPessoa.getIdRegional(),
                        cadastrarContaPagPessoa.getIdFilial(),
                        cadastrarContaPagPessoa.getIdPontoDeRelacionamento());
          }
        } catch (Exception e) {
          info("Erro ao buscar pessoa pela hierarquia e documento.");
        }
      }
    }
    List<Boolean> temCorrespList = new ArrayList<>();
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
        cadastrarContaPagPessoa.getIdInstituicao())) {
      for (ValorCargaProdutoInstituicao valoresCargasProduto :
          cadastrarContaPagPessoa.getValoresCargasProdutos()) {
        Boolean temCorresp =
            produtoInstituicaoCorrespondenteService.findIfExistsCorresp(
                valoresCargasProduto.getIdProdutoInstituicao());
        temCorrespList.add(temCorresp);
        if (temCorresp) {
          ContasCredenciaisReplicaveisReponse empresa =
              produtoInstituicaoCorrespondenteService.montaVerificacaoEmpresa(
                  valoresCargasProduto.getIdProdutoInstituicao(), user);
          produtoInstituicaoCorrespondenteService.validarDadosReplicacao(empresa);
        }
      }
    }

    for (ValorCargaProdutoInstituicao valoresCargasProduto :
        cadastrarContaPagPessoa.getValoresCargasProdutos()) {
      if (!prodInstConfigService.obterPermiteMultiplasContas(
          valoresCargasProduto.getIdProdutoInstituicao())) {
        if (contaPagamentoRepository
                .isContaPagamentoExisteByHierarquiaAndDocumentoAndProdutoAndTitularidade(
                    cadastrarContaPagPessoa.getIdProcessadora(),
                    cadastrarContaPagPessoa.getIdInstituicao(),
                    cadastrarContaPagPessoa.getIdRegional(),
                    cadastrarContaPagPessoa.getIdFilial(),
                    cadastrarContaPagPessoa.getIdPontoDeRelacionamento(),
                    cadastrarContaPagPessoa.getDocumento(),
                    valoresCargasProduto.getIdProdutoInstituicao())
            > 0) {
          throw new GenericServiceException(
              "Pessoa já possui conta no produto "
                  + valoresCargasProduto.getIdProdutoInstituicao()
                  + ".");
        }
      }
    }

    if (pessoa == null || pessoa.getIdPessoa() == null) {
      pessoa = createPessoa(cadastrarContaPagPessoa);
    }

    if (!UtilValidator.isNomeEmbossadoValid(cadastrarContaPagPessoa.getNomeEmbossado())) {
      throw new GenericServiceException("Nome Embossado inválido!");
    }

    if (cadastrarContaPagPessoa.getRazaoSocial() == null) {
      if (!validateEndObrigatorios(cadastrarContaPagPessoa)) {
        throw new GenericServiceException(
            "Existem tipos de endereços obrigatórios que não foram cadastrados!");
      }
      if (!validateIdadeMinima(cadastrarContaPagPessoa)) {
        throw new GenericServiceException(
            "A data de nascimento deve respeitar a idade mínima para o portador do produto selecionado.");
      }
    }

    ContaPagamento conta = null;

    // trava para garantir que não haverá conflito durante as verificações e inserções em tabela
    lock.lock();
    try {

      /*
       * cria a quantidade de contas e cartoes necessarios, conforme a
       * quantidade de produtos que vieram na requisicao
       */
      Boolean produtoIntegracao = false;
      List<Long> contasIntegracao = new ArrayList<>();
      for (ValorCargaProdutoInstituicao valorProduto :
          cadastrarContaPagPessoa.getValoresCargasProdutos()) {

        if (valorProduto.getIdProdutoPlataforma() != null
            && ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(
                valorProduto.getIdProdutoPlataforma())) {

          BigDecimal limiteUnico = new BigDecimal(valorProduto.getValorCargaPadrao());

          validarLimiteGlobal(limiteUnico, user, user.getIdPontoDeRelacionamento());
        }
        ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
            prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                cadastrarContaPagPessoa.getIdProcessadora(),
                valorProduto.getIdProdutoInstituicao(),
                cadastrarContaPagPessoa.getIdInstituicao());

        if (produtoInstituicaoConfiguracao.getIdGrupoProduto() != null) {
          List<CredencialConta> listaContasMesmoGrupoEHierarquia =
              credencialContaService.findCredencialContaByDocumentoAndGrupoProdutosAndHierarquia(
                  pessoa.getDocumento(),
                  produtoInstituicaoConfiguracao.getIdGrupoProduto(),
                  cadastrarContaPagPessoa.getIdInstituicao(),
                  cadastrarContaPagPessoa.getIdProcessadora(),
                  cadastrarContaPagPessoa.getIdFilial(),
                  cadastrarContaPagPessoa.getIdRegional(),
                  cadastrarContaPagPessoa.getIdPontoDeRelacionamento());

          if (!prodInstConfigService.obterPermiteMultiplasContas(
              valorProduto.getIdProdutoInstituicao()))
            if (getContaPagamentoFacade()
                .isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
                    cadastrarContaPagPessoa.getIdProcessadora(),
                    cadastrarContaPagPessoa.getIdInstituicao(),
                    cadastrarContaPagPessoa.getDocumento(),
                    valorProduto.getIdProdutoInstituicao(),
                    cadastrarContaPagPessoa.getIdPontoDeRelacionamento())) {
              throw new GenericServiceException(
                  "Conta já existente para esta instituição, documento, produto e ponto de relacionamento");
            }

          conta =
              getContaPagamentoFacade()
                  .createContaPagamento(
                      cadastrarContaPagPessoa,
                      pessoa,
                      valorProduto.getIdProdutoInstituicao(),
                      valorProduto);
          vincularContaPessoa(pessoa, conta);

          // tive que trazer a lógica de verificação se o cartão é virtual ou físico pra cá
          Boolean seraVirtual = setVirtualCartaoBaseadoConfiguracaoProduto(conta);

          // Filtra lista de credenciais-conta do mesmo grupo para deixar apenas status 0 (bloqueio
          // origem),
          // 1 (desbloqueado) e 5 (bloqueio temporário)
          int[] statusIncludeList = new int[] {0, 1, 5};
          listaContasMesmoGrupoEHierarquia =
              listaContasMesmoGrupoEHierarquia.stream()
                  .filter(
                      cred ->
                          ArrayUtils.contains(
                              statusIncludeList, cred.getCredencial().getIdStatusV2()))
                  .collect(Collectors.toList());

          // mapa de lista de contas -> map.get(true) = credenciais virtuais
          // map.get(false) = credenciais físicas
          Map<Boolean, List<CredencialConta>> contasMesmoGrupoVirtualMap =
              listaContasMesmoGrupoEHierarquia.stream()
                  .collect(Collectors.partitioningBy(cc -> cc.getCredencial().getVirtual()));

          if (contasMesmoGrupoVirtualMap.get(seraVirtual).isEmpty()) {
            createCredencial(cadastrarContaPagPessoa, pessoa, conta);
          } else {
            credencialContaService.vincularCredencialConta(
                conta,
                contasMesmoGrupoVirtualMap.get(seraVirtual).get(0).getCredencial(),
                produtoInstituicaoConfiguracao,
                pessoa);
          }

          if (!contasMesmoGrupoVirtualMap.get(!seraVirtual).isEmpty()) {
            credencialContaService.vincularCredencialConta(
                conta,
                contasMesmoGrupoVirtualMap.get(!seraVirtual).get(0).getCredencial(),
                produtoInstituicaoConfiguracao,
                pessoa);
          }

          if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
              conta, produtoInstituicaoConfiguracao)) {
            if (contasMesmoGrupoVirtualMap.get(false).isEmpty()) {
              createCredencialFisicaComplementar(pessoa, conta);
            } else {
              credencialContaService.vincularCredencialConta(
                  conta,
                  contasMesmoGrupoVirtualMap.get(false).get(0).getCredencial(),
                  produtoInstituicaoConfiguracao,
                  pessoa);
            }
          }
        } else {

          conta =
              getContaPagamentoFacade()
                  .createContaPagamento(
                      cadastrarContaPagPessoa,
                      pessoa,
                      valorProduto.getIdProdutoInstituicao(),
                      valorProduto);
          vincularContaPessoa(pessoa, conta);
          createCredencial(cadastrarContaPagPessoa, pessoa, conta);

          if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
              conta, produtoInstituicaoConfiguracao)) {
            createCredencialFisicaComplementar(pessoa, conta);
          }
        }

        if (Boolean.TRUE.equals(
            prodInstConfigService.obterCargaIntegracao(conta.getIdProdutoInstituicao()))) {
          produtoIntegracao = TRUE;
          contasIntegracao.add(conta.getIdConta());
        }

        ProdutoInstituicao produtoInstituicao =
            produtoInstituicaoService.findByIdProdInstituicao(
                valorProduto.getIdProdutoInstituicao());

        if (cadastrarContaPagPessoa.getSemEmail() == null
            || !cadastrarContaPagPessoa.getSemEmail()) {
          if (Constantes.ID_PRODUCAO_INSTITUICAO_KREDIT.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            emailService.sendBoasVindasKredit(pessoa, conta);

          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            if (user == null) {
              emailService.sendBoasVindasPaxPay(pessoa, null);
            }
          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_FANBANK.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            emailService.sendBoasVindasFanBank(pessoa, conta);

          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_1DBANK.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            emailService.sendBoasVindas1DBank(pessoa, conta);

          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_DAXPAY.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            // DAXPAY não recebe e-mail
          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            // INMAIS PRÊMIOS não recebe e-mail
          } else if (utilService
              .getProdutoInstituicaoBrbPay()
              .equals(produtoInstituicao.getIdProdInstituicao())) {
            // BRBPay ja recebe email em ContaPagamentoBRBService
          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_PRONTO_PAGUEI.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            emailService.sendBoasVindasProntoPaguei(pessoa, conta);
          } else if (Constantes.ID_PRODUCAO_INSTITUICAO_CLUBE_DIA_DIA.equals(
              cadastrarContaPagPessoa.getIdInstituicao())) {
            emailService.sendBoasVindasDiaDia(pessoa, null);
          } else if (produtoInstituicao.getB2b()) {
            emailService.sendBoasVindas(pessoa);
          }
        }
      }

      List<EnderecoPessoa> enderecoPessoa =
          enderecoPessoaService.findByIdPessoa(pessoa.getIdPessoa());

      if (enderecoPessoa.isEmpty() || enderecoPessoa == null) {
        if ((cadastrarContaPagPessoa.getEnderecosPessoaRequest() != null
            && cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos() != null
            && !cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().isEmpty())) {
          createEnderecoPessoa(cadastrarContaPagPessoa, pessoa);
        }
      } else if ((cadastrarContaPagPessoa.getEnderecosPessoaRequest() != null
          && cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos() != null
          && !cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().isEmpty())) {

        atualizarEnderecoPessoa(cadastrarContaPagPessoa, pessoa, enderecoPessoa);
      }

      updateCostumer(cadastrarContaPagPessoa, pessoa);

      if (conta != null
          && conta.getIdInstituicao() != null
          && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
              conta.getIdInstituicao())) {
        if (temCorrespList.contains(TRUE)) {
          produtoInstituicaoCorrespondenteService.preparaAgendamentoReplicar();
        }
        if (produtoIntegracao) {
          HashMap<String, List<Long>> cpfsContasPreCadastro = new HashMap<>();
          cpfsContasPreCadastro.put(pessoa.getDocumento(), contasIntegracao);
          preCadastroService.agendarPreCadastro(cpfsContasPreCadastro, user);
        }
      }

    } finally {
      lock.unlock();
    }

    return conta;
  }

  /**
   * Método transacional responsável por criar uma conta completa sem
   * authorization(pessoa,conta,contaPessoa,credencial) para uma pessoa
   *
   * @param cadastrarContaPagPessoa
   * @return número da conta gerada
   */
  @Transactional
  public ContaPagamento cadastrarContaPagamentoPessoaSemAuthorization(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa) {

    Pessoa pessoa = new Pessoa();

    try {
      if (cadastrarContaPagPessoa.getDocumento().length() > 12
          && cadastrarContaPagPessoa.getIdProcessadora() != null
          && cadastrarContaPagPessoa.getIdInstituicao() != null
          && cadastrarContaPagPessoa.getDocumento() != null
          && cadastrarContaPagPessoa.getIdRegional() != null
          && cadastrarContaPagPessoa.getIdFilial() != null
          && cadastrarContaPagPessoa.getIdPontoDeRelacionamento() != null) {
        pessoa =
            getContaPagamentoFacade()
                .findOneByHierarquia(
                    cadastrarContaPagPessoa.getIdProcessadora(),
                    cadastrarContaPagPessoa.getIdInstituicao(),
                    cadastrarContaPagPessoa.getDocumento(),
                    cadastrarContaPagPessoa.getRazaoSocial() != null
                        ? Integer.valueOf(Constantes.COD_DOC_CNPJ_2)
                        : cadastrarContaPagPessoa.getTipoPessoa(),
                    cadastrarContaPagPessoa.getIdRegional(),
                    cadastrarContaPagPessoa.getIdFilial(),
                    cadastrarContaPagPessoa.getIdPontoDeRelacionamento());
      }
    } catch (Exception e) {
      info("Erro ao buscar pessoa pela hierarquia e documento.");
    }

    String nomeEmbossado = cadastrarContaPagPessoa.getNomeEmbossado();
    if (StringUtils.isEmpty(nomeEmbossado)) {
      String nomeCompleto = cadastrarContaPagPessoa.getNomeCompleto();

      if (StringUtils.isEmpty(nomeCompleto)) {
        throw new GenericServiceException("Nome não registrado.");
      }

      String nomeAbreviado = Abreviador.abreviarNome(nomeCompleto);
      cadastrarContaPagPessoa.setNomeEmbossado(nomeAbreviado);
    } else {
      if (!UtilValidator.isNomeEmbossadoValid(nomeEmbossado)) {
        throw new GenericServiceException("Nome Embossado inválido!");
      }
    }

    if (pessoa == null || pessoa.getIdPessoa() == null) {
      pessoa = createPessoa(cadastrarContaPagPessoa);
    }

    if (cadastrarContaPagPessoa.getRazaoSocial() == null) {
      if (!validateEndObrigatorios(cadastrarContaPagPessoa)) {
        throw new GenericServiceException(
            "Existem tipos de endereços obrigatórios que não foram cadastrados!");
      }
      if (!validateIdadeMinima(cadastrarContaPagPessoa)) {
        throw new GenericServiceException(
            "A data de nascimento deve respeitar a idade mínima para o portador do produto selecionado.");
      }
    }

    ContaPagamento conta = null;

    // trava para garantir que não haverá conflito durante as verificações e inserções em tabela
    lock.lock();
    try {

      /*
       * cria a quantidade de contas e cartoes necessarios, conforme a
       * quantidade de produtos que vieram na requisicao
       */
      for (ValorCargaProdutoInstituicao valorProduto :
          cadastrarContaPagPessoa.getValoresCargasProdutos()) {
        if (valorProduto.getIdProdutoPlataforma() != null
            && ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(
                valorProduto.getIdProdutoPlataforma())) {

          // BigDecimal limiteUnico = new BigDecimal(valorProduto.getValorCargaPadrao());
          // TODO verificar a necessidade dessa validação pois usa um user e nessa transação não
          // teremos um user
          // validarLimiteGlobal(limiteUnico, user, user.getIdPontoDeRelacionamento())
        }
        conta =
            getContaPagamentoFacade()
                .createContaPagamento(
                    cadastrarContaPagPessoa,
                    pessoa,
                    valorProduto.getIdProdutoInstituicao(),
                    valorProduto);
        vincularContaPessoa(pessoa, conta);
        createCredencial(cadastrarContaPagPessoa, pessoa, conta);

        ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
            prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                conta.getIdProcessadora(),
                conta.getIdProdutoInstituicao(),
                conta.getIdInstituicao());
        if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
            conta, produtoInstituicaoConfiguracao)) {
          createCredencialFisicaComplementar(pessoa, conta);
        }
      }

      if ((cadastrarContaPagPessoa.getEnderecosPessoaRequest() != null
          && cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos() != null
          && !cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().isEmpty())) {
        createEnderecoPessoa(cadastrarContaPagPessoa, pessoa);
      }
      updateCostumer(cadastrarContaPagPessoa, pessoa);

    } finally {
      lock.unlock();
    }

    return conta;
  }

  public List<ContaPagamentoVO> findIdContaByIdInstituicaoAndDocumento(
      Integer idInstituicao, String documento) {
    return contaPagamentoRepository
        .findIdContaByIdInstituicaoAndDocumentoAndTipoStatus_idTipoStatusIsNot(
            idInstituicao, documento, Constantes.GRUPO_STATUS_CANCELADO);
  }

  @Transactional
  public CredencialGerada createCredencialFisicaComplementar(Pessoa pessoa, ContaPagamento conta) {
    GerarCredencialRequest gerarCredencial = new GerarCredencialRequest();
    gerarCredencial.setIdUsuario(pessoa.getIdUsuarioInclusao());
    gerarCredencial.setIdPessoa(pessoa.getIdPessoa());
    gerarCredencial.setIdConta(conta.getIdConta());
    gerarCredencial.setAdicional(false);
    gerarCredencial.setVirtual(NAO_CRIAR_CARTAO_VIRTUAL);

    return getContaPagamentoFacade().gerarCredencial(gerarCredencial);
  }

  /**
   * Método transacional responsável por criar uma conta
   * completa(pessoa,conta,contaPessoa,credencial) para uma credencial Pré-emitida
   *
   * @return número da conta gerada
   */
  @Transactional
  public CredencialGerada cadastrarContaPagamentoPessoaCredPreEmitida(
      CadastrarContaPagPessoaCredPreEmitidaRequest model,
      CredencialPreEmitida credPreEmi,
      Integer idProdInst) {

    CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa =
        new CadastrarContaPagamentoPessoaRequest();

    ContaPagamento conta = null;

    Pessoa pessoa = new Pessoa();
    Pessoa pessoaExiste = new Pessoa();

    // trava para garantir que não haverá conflito durante as verificações e inserções em tabela
    lock.lock();
    try {

      // busca conta que não é B2B
      boolean existContaPessoaNotB2b =
          existeContaPagamentoProduto(
              model.getIdProcessadora(),
              model.getIdInstituicao(),
              model.getTipoPessoa(),
              model.getDocumento());

      if (existContaPessoaNotB2b && Objects.isNull(model.getIdPessoa())) {
        // valida durante cadastro de nova pessoa
        throw new GenericServiceException("Está pessoa já possui conta para este produto.");
      }

      if (!existContaPessoaNotB2b && Objects.isNull(model.getIdPessoa())) {
        // copia prorpieades que serão usadas para criar nova pessoa

        BeanUtils.copyProperties(model, cadastrarContaPagPessoa, getNullPropertyNames(model));
        cadastrarContaPagPessoa.setNaturalidade(model.getNaturalidade());

      } else if (existContaPessoaNotB2b && Objects.nonNull(model.getIdPessoa())) {
        // encontra pessoa existente e copia propriedades para salvar conta
        // pagamento
        pessoaExiste = getContaPagamentoFacade().findOneByIdPessoa(model.getIdPessoa());
        BeanUtils.copyProperties(
            pessoaExiste, cadastrarContaPagPessoa, getNullPropertyNames(pessoaExiste));
        cadastrarContaPagPessoa.setDataNascimento(
            DateUtil.localDateTimeToDate(pessoaExiste.getDataNascimento()));
      }

      cadastrarContaPagPessoa.setIdProcessadora(model.getIdProcessadora());
      cadastrarContaPagPessoa.setIdInstituicao(model.getIdInstituicao());
      cadastrarContaPagPessoa.setIdRegional(model.getIdRegional());
      cadastrarContaPagPessoa.setIdFilial(model.getIdFilial());
      cadastrarContaPagPessoa.setIdPontoDeRelacionamento(model.getIdPontoDeRelacionamento());

      validarCadastroRazaoSocial(idProdInst, cadastrarContaPagPessoa);

      if (!existContaPessoaNotB2b && Objects.isNull(model.getIdPessoa())) {
        // cria nova pessoa
        pessoa = createPessoaCredPreEmitida(cadastrarContaPagPessoa, idProdInst);
      } else if (existContaPessoaNotB2b && Objects.nonNull(model.getIdPessoa())) {
        // copia propriedades de pessoa existente
        BeanUtils.copyProperties(pessoaExiste, pessoa, getNullPropertyNames(pessoaExiste));
      }

      validarProdutoInstituicao(model.getTipoPessoa(), idProdInst);

      if (Objects.nonNull(model.getEnderecosPessoaRequest())
          && Objects.nonNull(model.getEnderecosPessoaRequest().getEnderecos())
          && !model.getEnderecosPessoaRequest().getEnderecos().isEmpty()) {
        EnderecoPessoa[] enderecosEntity =
            new EnderecoPessoa[model.getEnderecosPessoaRequest().getEnderecos().size()];
        enderecosEntity =
            prepareEnderecos(model.getEnderecosPessoaRequest(), enderecosEntity, pessoa);
        enderecoPessoaService.cadastrarEnderecosPessoa(
            model.getIdUsuarioInclusao(), enderecosEntity);
      }

      vincularCartaoPreEmitidoJCard(pessoa, model.getCredencial());

      conta =
          getContaPagamentoFacade()
              .createContaPagamento(
                  cadastrarContaPagPessoa,
                  pessoa,
                  idProdInst,
                  new ValorCargaProdutoInstituicao(idProdInst, 0d));
      vincularContaPessoa(pessoa, conta);
    } finally {
      lock.unlock();
    }

    CredencialGerada credencialGerada =
        createCredencialPreEmitida(cadastrarContaPagPessoa, pessoa, conta, credPreEmi);
    conta.setIdContaPagamento(credPreEmi.getIdContaPagamento());
    updateCostumer(cadastrarContaPagPessoa, pessoa);

    return credencialGerada;
  }

  private void validarCadastroRazaoSocial(
      Integer idProdInst, CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa) {
    if (Objects.isNull(cadastrarContaPagPessoa.getRazaoSocial())) {
      if (!validateEndObrigatorios(cadastrarContaPagPessoa, idProdInst)) {
        throw new GenericServiceException(
            "Existem tipos de endereços obrigatórios que não foram cadastrados!");
      } else if (!validateIdadeMinima(cadastrarContaPagPessoa, idProdInst)) {
        throw new GenericServiceException(
            "A data de nascimento deve respeitar a idade mínima para o portador do produto selecionado.");
      }
    }
  }

  private void validarProdutoInstituicao(Integer idTipoPessoa, Integer idProdutoInstituicao) {
    validarProdutoInstituicao(idTipoPessoa, idProdutoInstituicao, FALSE);
  }

  private void validarProdutoInstituicao(
      Integer idTipoPessoa, Integer idProdutoInstituicao, boolean isB2B) {
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProdInstituicao(idProdutoInstituicao);
    if (Objects.isNull(produtoInstituicaoConfiguracao)) {
      throw new GenericServiceException("Configuração do produto não encontrada.");
    } else if (!isB2B && !produtoInstituicaoConfiguracao.getTipoPessoa().equals(idTipoPessoa)) {
      throw new GenericServiceException(
          "Tipo de pessoa do produto diferente do tipo de pessoa da conta.");
    }
  }

  private void vincularCartaoPreEmitidoJCard(Pessoa pessoa, String nomeCredencial) {
    GetCardResponse card = cardService.getToken(nomeCredencial.toUpperCase());

    JcardResponse responseJcard =
        cardService.addPreEmitido(
            new VincularCardAnonymousRequest(
                card.getCard().getToken(), pessoa.getIdPessoa().toString()));
    if (!responseJcard.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível vincular o portador ao cartão. Erro:" + responseJcard.getErrors());
    }
  }

  public ContaPagamento validaConta(Long idConta, SecurityUser user) {
    ContaPagamento conta = findContaByHierarquia(idConta, user);
    if (!Util.isNotNull(conta)) {
      throw new GenericServiceException("Conta não encontrada");
    }
    return conta;
  }

  private List<ContaPagamento> validaContasByCpfInMais(
      String cpf, Integer idProcessadora, String mensagem) {
    List<ContaPagamento> contas =
        findByCpf(cpf, idProcessadora, Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL);
    if (contas == null || contas.isEmpty()) {
      throw new GenericServiceException(mensagem);
    }
    return contas;
  }

  private ContaPagamento validaContaByIdContaInMais(Long idConta, String mensagem) {
    ContaPagamento conta = findById(idConta);
    if (conta == null
        || !Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(conta.getIdInstituicao())) {
      throw new GenericServiceException(mensagem);
    }
    return conta;
  }

  public ContaPagamento validaContaByUserInMais(SecurityUserPortador user, String mensagem) {
    return validaContasByCpfInMais(user.getCpf(), user.getIdProcessadora(), mensagem).get(0);
  }

  public ContaPagamento validaContaByUserInMais(
      SecurityUserPortador user, Long idConta, String mensagemExistencia, String mensagemPertence) {
    return validaContaByUserInMais(
        user.getIdInstituicao(), idConta, mensagemExistencia, mensagemPertence);
  }

  public ContaPagamento validaContaByUserInMais(
      SecurityUser user, Long idConta, String mensagemExistencia, String mensagemPertence) {
    return validaContaByUserInMais(
        user.getIdInstituicao(), idConta, mensagemExistencia, mensagemPertence);
  }

  public ContaPagamento validaContaByUserInMais(
      Integer idInstituicao, Long idConta, String mensagemExistencia, String mensagemPertence) {
    ContaPagamento conta = validaContaByIdContaInMais(idConta, mensagemExistencia);

    if (!Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(idInstituicao)
        || !conta.getIdInstituicao().equals(idInstituicao)) {
      throw new GenericServiceException(mensagemPertence);
    }
    return conta;
  }

  public List<ContaPagamento> findByProdutoNotB2b(
      Integer idProcessadora, Integer idInstituicao, Integer idTipoPessoa, String documento) {
    return contaPagamentoRepository.findByProdutoNotB2b(
        idProcessadora, idInstituicao, idTipoPessoa, documento);
  }

  private boolean validateEndObrigatorios(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Integer idProdInst) {
    List<TipoEnderecoProduto> tep = tipoEndProdService.findByIdProdInstituicao(idProdInst);

    if (Objects.isNull(cadastrarContaPagPessoa.getEnderecosPessoaRequest())) {
      return TRUE;
    }

    List<Integer> idTipoEnderecos =
        cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().stream()
            .map(EnderecoPessoaRequest::getIdTipoEndereco)
            .collect(Collectors.toList());

    for (TipoEnderecoProduto tmp : tep) {
      if (tmp.getExigivel() && !idTipoEnderecos.contains(tmp.getIdTipoEndereco())) {
        return FALSE;
      }
    }

    return TRUE;
  }

  private Boolean validateEndObrigatorios(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa) {
    List<TipoEnderecoProduto> tep =
        tipoEndProdService.findByIdProdInstituicao(
            cadastrarContaPagPessoa.getValoresCargasProdutos().get(0).getIdProdutoInstituicao());

    if (Objects.isNull(cadastrarContaPagPessoa.getEnderecosPessoaRequest())) {
      return TRUE;
    }

    List<Integer> list =
        cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().stream()
            .map(EnderecoPessoaRequest::getIdTipoEndereco)
            .collect(Collectors.toList());
    for (TipoEnderecoProduto tmp : tep) {
      if (tmp.getExigivel() && !list.contains(tmp.getIdTipoEndereco())) {
        return FALSE;
      }
    }
    return TRUE;
  }

  private Boolean validateIdadeMinima(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa) {
    if (Objects.isNull(cadastrarContaPagPessoa.getDataNascimento())) {
      return TRUE;
    }
    ProdutoInstituicaoConfiguracao prodConfig =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            cadastrarContaPagPessoa.getIdProcessadora(),
            cadastrarContaPagPessoa.getValoresCargasProdutos().get(0).getIdProdutoInstituicao(),
            cadastrarContaPagPessoa.getIdInstituicao());

    Calendar calendar = Calendar.getInstance();
    calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) - prodConfig.getIdadeMinimaPortador());
    Date dataMinima = calendar.getTime();
    return !cadastrarContaPagPessoa.getDataNascimento().after(dataMinima);
  }

  private boolean validateIdadeMinima(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Integer idProdInst) {
    ProdutoInstituicaoConfiguracao prodConfig =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            cadastrarContaPagPessoa.getIdProcessadora(),
            idProdInst,
            cadastrarContaPagPessoa.getIdInstituicao());

    if (Objects.isNull(prodConfig)) {
      throw new GenericServiceException(
          "Não foi possível encontrar produtoInstituicaoConfiguracao.");
    }

    Calendar c = Calendar.getInstance();
    c.set(Calendar.YEAR, c.get(Calendar.YEAR) - prodConfig.getIdadeMinimaPortador());
    Date dataMinima = c.getTime();

    return cadastrarContaPagPessoa.getDataNascimento() == null
        ? TRUE
        : !cadastrarContaPagPessoa.getDataNascimento().after(dataMinima);
  }

  @Transactional
  public ContaPagamento cadastrarContaPagamentoPessoaArquivo(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa) {

    Pessoa pessoaBuscada = null;
    Boolean permiteMultiplasContas =
        cadastrarContaPagPessoa.getValoresCargasProdutos().stream()
            .anyMatch(
                val ->
                    prodInstConfigService.obterPermiteMultiplasContas(
                        val.getIdProdutoInstituicao()));

    if (cadastrarContaPagPessoa.getTipoPessoa().equals(PESSOA_FISICA)) {
      if (!permiteMultiplasContas) {
        pessoaBuscada =
            contaPagamentoFacade.findOneByHierarquia(
                cadastrarContaPagPessoa.getIdProcessadora(),
                cadastrarContaPagPessoa.getIdInstituicao(),
                cadastrarContaPagPessoa.getDocumento(),
                cadastrarContaPagPessoa.getTipoPessoa(),
                cadastrarContaPagPessoa.getIdRegional(),
                cadastrarContaPagPessoa.getIdFilial(),
                cadastrarContaPagPessoa.getIdPontoDeRelacionamento());
      }
    }

    Pessoa pessoa = new Pessoa();

    if (pessoaBuscada == null) {
      pessoa = createPessoa(cadastrarContaPagPessoa);
    } else {
      if (cadastrarContaPagPessoa.getValoresCargasProdutos() != null
          && cadastrarContaPagPessoa.getValoresCargasProdutos().size() > 0
          && cadastrarContaPagPessoa.getValoresCargasProdutos().get(0).getIdProdutoPlataforma()
              != null
          && Constantes.PROD_PLATAFORMA_TRANSFERENCIA_BANCARIA.equals(
              cadastrarContaPagPessoa.getValoresCargasProdutos().get(0).getIdProdutoPlataforma())) {
        if (pessoaBuscada.getContaBancariaX() == null
            || pessoaBuscada.getTipoContaBancaria() == null
            || pessoaBuscada.getIdAgencia() == null
            || pessoaBuscada.getIdBanco() == null) {
          pessoaBuscada.setIdBanco(cadastrarContaPagPessoa.getIdBanco());
          pessoaBuscada.setIdAgencia(cadastrarContaPagPessoa.getIdAgencia());
          pessoaBuscada.setContaBancariaX(cadastrarContaPagPessoa.getContaBancariaX());
          pessoaBuscada.setTipoContaBancaria(cadastrarContaPagPessoa.getTipoContaBancaria());
          pessoaBuscada = getContaPagamentoFacade().savePessoa(pessoaBuscada);
        }
      }
      pessoa = pessoaBuscada;
    }
    ContaPagamento conta = null;

    /*
     * cria a quantidade de contas e cartoes necessarios, conforme a
     * quantidade de produtos que vieram na requisicao
     */

    List<Credencial> credencias = new ArrayList<Credencial>();

    Long idGrupoProdutoCadastrado = null;

    for (ValorCargaProdutoInstituicao valorProduto :
        cadastrarContaPagPessoa.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              cadastrarContaPagPessoa.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              cadastrarContaPagPessoa.getIdInstituicao());

      if (produtoInstituicaoConfiguracao.getProdutoSocialBrb()) {
        verificaECadastraComplementosProdutosSociais(cadastrarContaPagPessoa, pessoa);
      }

      idGrupoProdutoCadastrado = produtoInstituicaoConfiguracao.getIdGrupoProduto();

      /* Encontra contas já criadas caso o produto esteja inserido em um grupo de produtos */
      if (idGrupoProdutoCadastrado != null) {
        List<CredencialConta> listaContasMesmoGrupo =
            credencialContaService.findCredencialContaByDocumentoAndGrupoProdutosAndHierarquia(
                pessoa.getDocumento(),
                produtoInstituicaoConfiguracao.getIdGrupoProduto(),
                cadastrarContaPagPessoa.getIdInstituicao(),
                cadastrarContaPagPessoa.getIdProcessadora(),
                cadastrarContaPagPessoa.getIdFilial(),
                cadastrarContaPagPessoa.getIdRegional(),
                cadastrarContaPagPessoa.getIdPontoDeRelacionamento());

        if (getContaPagamentoFacade()
            .isContaPagamentoExisteByInstituicaoAndDocumentoAndProdutoAndPontoRelacionamentoAndTitularidade(
                cadastrarContaPagPessoa.getIdProcessadora(),
                cadastrarContaPagPessoa.getIdInstituicao(),
                cadastrarContaPagPessoa.getDocumento(),
                valorProduto.getIdProdutoInstituicao(),
                cadastrarContaPagPessoa.getIdPontoDeRelacionamento())) {
          throw new GenericServiceException(
              "Conta já existente para esta instituição, documento, produto e ponto de relacionamento");
        }

        conta =
            getContaPagamentoFacade()
                .createContaPagamento(
                    cadastrarContaPagPessoa,
                    pessoa,
                    valorProduto.getIdProdutoInstituicao(),
                    valorProduto);
        vincularContaPessoa(pessoa, conta);

        // tive que trazer a lógica de verificação se o cartão é virtual ou físico pra cá
        Boolean seraVirtual = setVirtualCartaoBaseadoConfiguracaoProduto(conta);

        // Filtra lista de credenciais-conta do mesmo grupo para deixar apenas status 0 (bloqueio
        // origem),
        // 1 (desbloqueado) e 5 (bloqueio temporário), para evitar associar uma conta nova a uma
        // credencial
        // bloqueada
        int[] statusIncludeList = new int[] {0, 1, 5};
        listaContasMesmoGrupo =
            listaContasMesmoGrupo.stream()
                .filter(
                    cred ->
                        ArrayUtils.contains(
                            statusIncludeList, cred.getCredencial().getIdStatusV2()))
                .collect(Collectors.toList());

        // mapa de lista de contas -> map.get(true) = credenciais virtuais
        // map.get(false) = credenciais físicas
        Map<Boolean, List<CredencialConta>> contasMesmoGrupoVirtualMap =
            listaContasMesmoGrupo.stream()
                .collect(Collectors.partitioningBy(cc -> cc.getCredencial().getVirtual()));

        if (contasMesmoGrupoVirtualMap.get(seraVirtual).isEmpty()) {
          createCredencial(cadastrarContaPagPessoa, pessoa, conta);
        } else {
          credencialContaService.vincularCredencialConta(
              conta,
              contasMesmoGrupoVirtualMap.get(seraVirtual).get(0).getCredencial(),
              produtoInstituicaoConfiguracao,
              pessoa);
        }

        if (!contasMesmoGrupoVirtualMap.get(!seraVirtual).isEmpty()) {
          credencialContaService.vincularCredencialConta(
              conta,
              contasMesmoGrupoVirtualMap.get(!seraVirtual).get(0).getCredencial(),
              produtoInstituicaoConfiguracao,
              pessoa);
        }

        if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
            conta, produtoInstituicaoConfiguracao)) {
          if (contasMesmoGrupoVirtualMap.get(false).isEmpty()) {
            createCredencialFisicaComplementar(pessoa, conta);
          } else {
            credencialContaService.vincularCredencialConta(
                conta,
                contasMesmoGrupoVirtualMap.get(false).get(0).getCredencial(),
                produtoInstituicaoConfiguracao,
                pessoa);
          }
        }
      } else {
        conta =
            getContaPagamentoFacade()
                .createContaPagamento(
                    cadastrarContaPagPessoa,
                    pessoa,
                    valorProduto.getIdProdutoInstituicao(),
                    valorProduto);
        vincularContaPessoa(pessoa, conta);
        CredencialGerada credencialGerada =
            createCredencial(cadastrarContaPagPessoa, pessoa, conta);
        credencias.add(credencialGerada.getCredencial());

        if (produtoContratadoService.isPrimeiroCartaoVirtualEFisico(
            conta, produtoInstituicaoConfiguracao)) {
          credencias.add(createCredencialFisicaComplementar(pessoa, conta).getCredencial());
        }
      }
    }

    if (cadastrarContaPagPessoa.getIsIntegracao() != null
        && cadastrarContaPagPessoa.getIsIntegracao()) {

      ContaPagamento contaOrigem =
          contaPagamentoRepository.findByIdConta(cadastrarContaPagPessoa.getIdContaOrigem());
      ProdutoContratado produtoContratado =
          produtoContratadoService.findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
              contaOrigem.getIdProcessadora(),
              contaOrigem.getIdInstituicao(),
              contaOrigem.getIdRegional(),
              contaOrigem.getIdFilial(),
              contaOrigem.getIdPontoDeRelacionamento(),
              contaOrigem.getIdProdutoInstituicao());
      SetorFilial setorFilial =
          setorFilialService.findSetorFilialByIdPessoa(cadastrarContaPagPessoa.getIdPessoaOrigem());
      HierarquiaPontoDeRelacionamento empresaEndereco =
          hierarquiaPontoRelacionamentoService.buscarB2bPorChavesEstrangeiras(
              setorFilial.getIdProcessadora(),
              setorFilial.getIdInstituicao(),
              setorFilial.getIdRegional(),
              setorFilial.getIdFilial(),
              setorFilial.getIdPontoRelacionamento());

      // Modelo Endereco Pessoa Request contendo o endereço.
      EnderecoPessoaRequest enderecoPessoaRequest = new EnderecoPessoaRequest();
      enderecoPessoaRequest.setIdUsuarioInclusao(cadastrarContaPagPessoa.getIdUsuarioInclusao());
      enderecoPessoaRequest.setIdPessoa(pessoa.getIdPessoa());
      enderecoPessoaRequest.setIdTipoEndereco(Constantes.ENDERECO_COMERCIAL);
      enderecoPessoaRequest.setCep(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getCep()
              : empresaEndereco.getCepSede());
      enderecoPessoaRequest.setLogradouro(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getLogradouro()
              : empresaEndereco.getLogradouroSede());
      enderecoPessoaRequest.setNumero(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getNumero()
              : empresaEndereco.getNumeroSede());
      enderecoPessoaRequest.setComplemento(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getComplemento()
              : empresaEndereco.getComplementoSede());
      enderecoPessoaRequest.setBairro(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getBairro()
              : empresaEndereco.getBairroSede());
      enderecoPessoaRequest.setCidade(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getCidade()
              : empresaEndereco.getCidadeSede());
      enderecoPessoaRequest.setUf(
          produtoContratado.getTipoPostagemProduto() == SETOR_FILIAL
              ? setorFilial.getUf()
              : empresaEndereco.getUfSede());

      // Adiciona o endereço a uma lista de Endereços Pessoa Request.
      List<EnderecoPessoaRequest> enderecoPessoaRequests = new ArrayList<>();
      enderecoPessoaRequests.add(enderecoPessoaRequest);

      // A lista de endereços é armazenada em um wrapper de Enderecos Pessoa Request
      EnderecosPessoaRequest enderecosPessoaRequest = new EnderecosPessoaRequest();
      enderecosPessoaRequest.setEnderecos(enderecoPessoaRequests);

      // O wrapper é incluído no pre cadastro da Conta Pag Pessoa
      cadastrarContaPagPessoa.setEnderecosPessoaRequest(enderecosPessoaRequest);
    }

    if ((cadastrarContaPagPessoa.getEnderecosPessoaRequest() != null
        && cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos() != null
        && !cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().isEmpty())) {
      createEnderecoPessoa(cadastrarContaPagPessoa, pessoa);
    }
    updateCostumer(cadastrarContaPagPessoa, pessoa);

    conta.setCredenciais(credencias);

    if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(conta.getIdInstituicao())
        || (idGrupoProdutoCadastrado != null
            && Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(conta.getIdInstituicao()))) {

      PortadorLogin portadorLogin =
          portadorLoginService.resgataLoginMultiBeneficios(
              cadastrarContaPagPessoa.getIdProcessadora(),
              cadastrarContaPagPessoa.getIdInstituicao(),
              cadastrarContaPagPessoa.getDocumento(),
              idGrupoProdutoCadastrado);

      if (portadorLogin == null) {

        Credencial credencial =
            !credencias.isEmpty()
                ? credencias.get(credencias.size() - 1)
                : credencialRepository.findUltimaCredencialTitularConta(conta.getIdConta());

        CadastrarPortadorLogin cadastrarPortadorLogin = new CadastrarPortadorLogin();
        cadastrarPortadorLogin.setCredencial(credencial.getIdCredencial().toString());
        cadastrarPortadorLogin.setDataNascimento(cadastrarContaPagPessoa.getDataNascimento());
        cadastrarPortadorLogin.setCpf(cadastrarContaPagPessoa.getDocumento());
        cadastrarPortadorLogin.setSenha(generatePassword());
        cadastrarPortadorLogin.setIdInstituicao(cadastrarContaPagPessoa.getIdInstituicao());
        cadastrarPortadorLogin.setIdProcessadora(cadastrarContaPagPessoa.getIdProcessadora());
        cadastrarPortadorLogin.setOrigemCadastroLogin(Constantes.ORIGEM_CADASTRO_MANUAL);
        cadastrarPortadorLogin.setEmail(cadastrarContaPagPessoa.getEmail());
        cadastrarPortadorLogin.setIsEsqueciSenha(false);
        cadastrarPortadorLogin.setGrupoAcesso(idGrupoProdutoCadastrado);
        cadastrarPortadorLogin.setTipoLogin(cadastrarContaPagPessoa.getTipoLogin());

        portadorLogin = portadorLoginService.preparePortadorLogin(cadastrarPortadorLogin);
        portadorLoginService.createPortadorLoginImediatamenteAposCriacaoConta(
            cadastrarPortadorLogin, portadorLogin, credencial);

        portadorLoginContaService.criaPortadoresConta(portadorLogin);

        if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(conta.getIdInstituicao())) {
          emailService.sendBoasVindasPaxPayB2B(pessoa);
        }
      } else {
        portadorLoginContaService.adicionaPortadorMultiConta(portadorLogin, conta.getIdConta());
      }
    }
    if (cadastrarContaPagPessoa.getParcelamento() != null) {

      ConfiguracaoParcelamentoVO configuracaoParcelamentoVO = new ConfiguracaoParcelamentoVO();
      configuracaoParcelamentoVO.setIdConta(conta.getIdConta().intValue());
      configuracaoParcelamentoVO.setParcelamento(
          Double.parseDouble(cadastrarContaPagPessoa.getParcelamento()));
      configuracaoParcelamentoVO.setIdPessoa(pessoa.getIdPessoa());
      configuracaoParcelamentoVO.setIdProduto(conta.getIdProdutoInstituicao());
      configuracaoParcelamentoVO.setPermiteLote(true);

      configuracaoParcelamentoPessoaB2BService.cadastrarConfiguracaoViaArquivo(
          configuracaoParcelamentoVO, conta);
    }

    return conta;
  }

  private void verificaECadastraComplementosProdutosSociais(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Pessoa pessoa) {
    if (cadastrarContaPagPessoa.getNumeroAgenciaBRB() != null) {
      ComplementoProdutoSocialBRB complementoProdutoSocialBRB =
          complementoProdutoSocialBRBService.findComplementoBRBByIdPessoa(pessoa.getIdPessoa());
      if (complementoProdutoSocialBRB == null) {
        complementoProdutoSocialBRB = new ComplementoProdutoSocialBRB();
        complementoProdutoSocialBRB.setPessoa(pessoa);
        complementoProdutoSocialBRB.setNumeroAgencia(cadastrarContaPagPessoa.getNumeroAgenciaBRB());
        complementoProdutoSocialBRB.setNomeAgencia(cadastrarContaPagPessoa.getNomeAgenciaBRB());
        complementoProdutoSocialBRB.setProdutoCreche(
            cadastrarContaPagPessoa.getNomeEscola() != null);
        complementoProdutoSocialBRB.setNomeEscola(
            cadastrarContaPagPessoa.getNomeEscola() != null
                ? cadastrarContaPagPessoa.getNomeEscola()
                : null);
        complementoProdutoSocialBRB.setCidadeEmbossing(
            cadastrarContaPagPessoa.getCidadeEmbossing());
        complementoProdutoSocialBRB.setUfEmbossing(cadastrarContaPagPessoa.getUfEmbossing());
        complementoProdutoSocialBRB.setDataHoraInclusao(LocalDateTime.now());
        complementoProdutoSocialBRBService.save(complementoProdutoSocialBRB);
      }
    }
  }

  private void updateCostumer(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Pessoa pessoa) {

    EnderecosPessoaRequest enderecosPessoaRequest =
        cadastrarContaPagPessoa.getEnderecosPessoaRequest();
    JcardResponse response = null;
    // verifico se existe pelo menos um endereco.
    if (enderecosPessoaRequest != null
        && enderecosPessoaRequest.getEnderecos() != null
        && !enderecosPessoaRequest.getEnderecos().isEmpty()) {

      List<EnderecoPessoaRequest> enderecos = enderecosPessoaRequest.getEnderecos();
      List<EnderecoPessoaRequest> enderecosResidenciais = new ArrayList<>();

      // percorro todos os enderecos verificando se e do tipo residencial
      enderecos.forEach(
          endereco -> {

            // se for residencial entao adiciona na lista de enderecos
            // residenciais
            if (endereco.getIdTipoEndereco().equals(ENDERECO_RESIDENCIAL)) {
              enderecosResidenciais.add(endereco);
            }
          });

      // se encontrou pelo menos um endereco residencial
      if (!enderecosResidenciais.isEmpty()) {

        EnderecoPessoa enderecoPessoa = new EnderecoPessoa();
        EnderecoPessoaRequest enderecoResidencial = enderecosResidenciais.get(PRIMEIRA_POSICAO);

        BeanUtils.copyProperties(
            enderecoResidencial, enderecoPessoa, getNullPropertyNames(enderecoResidencial));

        Customer customer = new Customer(pessoa, enderecoPessoa);
        response = customerService.updateCustomer(customer);
      }
    }

    if (Objects.nonNull(response) && !response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível atualizar Costumer: " + response.getErrors());
    }
  }

  public CriarContaPagamento adicionaHierarquia(
      CriarContaPagamento criarContaPagamento, SecurityUser user) {
    if (criarContaPagamento.getIdRegional().equals(0)) {
      criarContaPagamento.setIdRegional(user.getIdRegional());
      criarContaPagamento.setIdFilial(user.getIdFilial());
      criarContaPagamento.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    }
    return criarContaPagamento;
  }

  /**
   * Metodo responsavel por buscar todos os endereços do titular da conta
   *
   * @param idConta
   * @return List do EnderecoPessoa
   */
  public List<EnderecoPessoa> getEnderecosTitularConta(Long idConta) {
    ContaPagamento conta = findByIdNotNull(idConta);

    return enderecoPessoaService.findByIdPessoaAndStatus(
        getIdTitular(conta), Constantes.GRUPO_STATUS_ATIVO);
  }

  /**
   * @param idConta
   * @return
   */
  public List<DadosCredencial> getDadosCredenciaisConta(Long idConta) {
    return getDadosCredenciaisConta(idConta, null);
  }

  /**
   * @param idConta
   * @return
   */
  public List<DadosCredencial> getDadosCredenciaisConta(Long idConta, Long ultimosQuatro) {

    ContaPagamento conta = findByIdNotNull(idConta);

    List<DadosCredencial> dadosCredenciais = new ArrayList<>();

    List<Credencial> credenciais;

    credenciais = buscarCredenciais(idConta, ultimosQuatro);

    for (Credencial credencial : credenciais) {

      DadosCredencial dadosCredencial = new DadosCredencial();
      BeanUtils.copyProperties(credencial, dadosCredencial);
      dadosCredencial.setStatus(credencial.getIdStatusV2());
      TipoStatus status = getContaPagamentoFacade().findByIdTipoStatus(dadosCredencial.getStatus());
      dadosCredencial.setDescStatus(status.getDescStatus());
      dadosCredencial.setIdGrupoStatus(status.getIdGrupoStatus());
      dadosCredencial.setDataHoraReplicacao(conta.getDataHoraReplicacao());
      dadosCredencial.setDataHoraConfirmaReplica(conta.getDataHoraConfirmaReplica());
      dadosCredencial.setIdProdutoPlataforma(conta.getIdProdutoPlataforma());
      ContaPessoa contaPessoa =
          getContaPagamentoFacade()
              .findOneByIdPessoaAndIdConta(credencial.getIdPessoa(), credencial.getIdConta());

      if (contaPessoa == null) {
        throw new GenericServiceException(
            "Conta não encontrada.idPessoa: "
                + credencial.getIdPessoa()
                + "idConta: "
                + credencial.getIdConta());
      }

      dadosCredencial.setIdTitularidade(contaPessoa.getIdTitularidade());
      TipoTitularidade tipoTitularidade = contaPessoa.getTipoTitularidade();
      dadosCredencial.setDescTitularidade(
          tipoTitularidade != null ? tipoTitularidade.getDescTitularidade() : "");

      Pessoa pessoa = getContaPagamentoFacade().findOneByIdPessoa(credencial.getIdPessoa());

      if (pessoa == null) {
        throw new GenericServiceException(
            "Pessoa não encontrada.idPessoa: " + credencial.getIdPessoa());
      }
      dadosCredencial.setDocumento(pessoa.getDocumento());
      dadosCredencial.setTipo(
          credencial.getChip() ? "Chip" : (credencial.getVirtual() ? "Virtual" : "Tarja"));

      String ultimos4 =
          Strings.padStart(
              credencial.getUltimos4Digitos().toString(), QTD_DIG_ULTIMOS_4, CHAR_ZERO);

      dadosCredencial.setNumeroCredencial(
          CredencialService.getCredencialMascarada(
              credencial.getBin6().toString() + ultimos4,
              Constantes.PADRAO_CREDENCIAL,
              Constantes.MASK_2_PARTES_COMPLETA));
      dadosCredencial.setIdProdutoInstituicao(conta.getIdProdutoInstituicao());
      dadosCredencial.setIdContaExterna(conta.getIdContaExterna());

      // dadosCredencial.setNumeroCredencialCriptografado(numeroCredCripto)
      dadosCredenciais.add(dadosCredencial);
    }

    Collections.sort(dadosCredenciais);
    return dadosCredenciais;
  }

  /**
   * Função que busca as credenciais de uma conta
   *
   * @param idConta
   * @param ultimosQuatro
   * @return
   */
  private List<Credencial> buscarCredenciais(Long idConta, Long ultimosQuatro) {
    List<Credencial> credenciais;
    if (Objects.nonNull(ultimosQuatro)) {
      credenciais =
          getContaPagamentoFacade().findByIdContaAndUltimos4Digitos(idConta, ultimosQuatro);
    } else {
      credenciais = getContaPagamentoFacade().findByIdConta(idConta);
    }
    return credenciais;
  }

  public Boolean permissaoAcessarPropriaConta(DadosPortador dadosPortador, SecurityUser user) {

    if (Objects.nonNull(user.getIdInstituicao())) {
      HierarquiaInstituicao instituicao =
          instituicaoService.findById(
              new HierarquiaInstituicaoId(user.getIdProcessadora(), user.getIdInstituicao()));

      if (!instituicao.getAcessoPropriaConta()) {
        return (!user.getCpf().equals(dadosPortador.getDocumento()));
      }
    }
    return TRUE;
  }

  /**
   * Metodo responsavel por buscar os dados do titular da conta
   *
   * @param idConta
   * @return DadosPortador
   */
  public DadosPortador getDadosTitularConta(Long idConta, SecurityUser user) {
    ContaPagamento conta = findContaByHierarquia(idConta, user);

    if (conta == null) {
      return null;
    }
    DadosPortador dadosPortador = new DadosPortador();

    Long idPessoa = null;
    Pessoa pessoa = null;

    idPessoa = getIdTitular(conta);
    if (idPessoa != null) {
      pessoa = getContaPagamentoFacade().findOneByIdPessoa(idPessoa);
    }
    if (pessoa == null) {
      throw new GenericServiceException("Pessoa Não encontrada. idPessoa: " + idPessoa);
    }
    BeanUtils.copyProperties(pessoa, dadosPortador);

    if (pessoa.getIdBanco() != null && pessoa.getIdAgencia() != null) {
      AgenciaId agenciaId = new AgenciaId(pessoa.getIdAgencia(), pessoa.getIdBanco());
      Agencia agencia = agenciaService.findById(agenciaId);

      if (agencia != null) {
        dadosPortador.setDescAgencia(agencia.getDescAgencia());
        dadosPortador.setDescBanco(agencia.getBanco().getDescBanco());
      }
    }

    return dadosPortador;
  }

  /**
   * Método responsável por inativar um produto
   *
   * @param request
   */
  public void inativarProduto(AtivarInativarProdutoRequest request, String ipOrigem) {

    alterarEstadoConta(
        request.getIdConta(),
        Constantes.CONTA_CANCELADO_PELA_EMPRESA,
        request.getIdUsuario(),
        ipOrigem);
  }

  /**
   * Método responsável por ativar um produto
   *
   * @param request
   * @param ipOrigem
   */
  public void ativarProduto(AtivarInativarProdutoRequest request, String ipOrigem) {

    alterarEstadoConta(
        request.getIdConta(), Constantes.CONTA_DESBLOQUEADA, request.getIdUsuario(), ipOrigem);
  }

  /**
   * Método responsável por desbloquear credencial
   *
   * @param request
   */
  @Transactional
  public void desbloquearCredencialCompleta(DesbloquearCredencialCompleta request) {

    desbloquearCredencial(
        request.getIdCredencial(),
        getAutorAlteracao(request.getIdUsuario()),
        request.getIdUsuario());
  }

  public Integer getAutorAlteracao(Integer idUsuario) {
    if (idUsuario.equals(Constantes.ID_USUARIO_INCLUSAO_PORTADOR)) {
      return AUTOR_PORTADOR;
    } else if (idUsuario.equals(Constantes.ID_USUARIO_AUTOMATICO)
        || idUsuario.equals(Constantes.ID_USUARIO_PROC_7010)) {
      return AUTOR_AUTOMATICO;
    } else if (idUsuario.equals(Constantes.ID_USUARIO_URA_BAHAMAS)
        || idUsuario.equals(Constantes.ID_USUARIO_URA_ITSPAY)
        || idUsuario.equals(Constantes.ID_USUARIO_URA_BRB)) {
      return AUTOR_URA;
    } else {
      return AUTOR_SISTEMA;
    }
  }

  public void alterarStatusCredencialURA(
      Credencial credencial, SecurityUser user, Integer statusDestino) {

    try {

      if (credencial == null) {
        throw new GenericServiceException(
            ConstantesErro.URA_CREDENCIAL_NAO_ENCONTRADA.getMensagem());
      }

      ContaPagamento conta = contaPagamentoRepository.findByIdConta(credencial.getIdConta());

      Boolean alteracaoStatusPermitida =
          mapaStatusService.isAlteracaoStatusPermitida(
              conta.getIdProcessadora(),
              conta.getIdInstituicao(),
              conta.getIdProdutoInstituicao(),
              APLICABILIDADE_CREDENCIAL,
              getAutorAlteracao(user.getIdUsuario()),
              credencial.getIdStatusV2(),
              statusDestino);

      if (!alteracaoStatusPermitida) {
        throw new GenericServiceException(
            "Alteração de status da credencial não permitida. Status destino: "
                + statusDestino
                + " Status origem: "
                + credencial.getIdStatusV2()
                + " Produto: "
                + conta.getIdProdutoInstituicao());
      }

      TrocarEstadoCredencialJcard trocarEstadoCredencialJcard = new TrocarEstadoCredencialJcard();
      trocarEstadoCredencialJcard.setIdCredencial(credencial.getIdCredencial());
      trocarEstadoCredencialJcard.setStatusAntigo(credencial.getIdStatusV2());
      trocarEstadoCredencialJcard.setStatusNovo(
          Constantes.TIPO_STATUS_DESBLOQUEADO.equals(statusDestino)
              ? StatusJcardEnum.DESBLOQUEADO_ACTIVE_JCARD.getCodigo()
              : StatusJcardEnum.BLOQUEIO_PREV_TEMP_SUSPENDED_JCARD.getCodigo());
      trocarEstadoCredencialJcard.setIdUsuario(user.getIdUsuario());

      Boolean statusJcard = credencialService.alteraStatusJcard(trocarEstadoCredencialJcard, user);

      if (!statusJcard) {
        log.error(ConstantesErro.URA_ERRO_STATUS_JCARD.getMensagem());
        throw new GenericServiceException(
            ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.getMensagem());
      }

      credencial.setIdStatusV2(statusDestino);
      credencial.setDataHoraStatus(LocalDateTime.now());
      credencial.setIdUsuarioManutencao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);

      getContaPagamentoFacade().saveCredencial(credencial);
    } catch (Exception e) {
      log.error(ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.format(e.getMessage()));
      throw new GenericServiceException(
          ConstantesErro.URA_ERRO_ALTERAR_STATUS_CARTAO.getMensagem(),
          HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  public Credencial alterarStatusCredencial(
      Integer idUsuario, Integer statusDestino, Credencial credencial) {
    ContaPagamento conta = contaPagamentoRepository.findByIdConta(credencial.getIdConta());

    Boolean alteracaoStatusPermitida =
        mapaStatusService.isAlteracaoStatusPermitida(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            conta.getIdProdutoInstituicao(),
            APLICABILIDADE_CREDENCIAL,
            getAutorAlteracao(idUsuario),
            credencial.getIdStatusV2(),
            statusDestino);

    if (!alteracaoStatusPermitida) {
      throw new GenericServiceException(
          "Alteração de status da credencial não permitida. Status destino: "
              + statusDestino
              + " Status origem: "
              + credencial.getIdStatusV2()
              + " Produto: "
              + conta.getIdProdutoInstituicao());
    }

    credencial.setIdStatusV2(statusDestino);
    credencial.setDataHoraStatus(LocalDateTime.now());
    credencial.setIdUsuarioManutencao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);

    return getContaPagamentoFacade().saveCredencial(credencial);
  }

  public Credencial desbloquearCredencialViaPortador(Long idCredencial) {
    return desbloquearCredencial(
        idCredencial,
        getAutorAlteracao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR),
        Constantes.ID_USUARIO_INCLUSAO_PORTADOR);
  }

  public Credencial desbloquearCredencial(Long idCredencial, Integer idUsuario) {
    return desbloquearCredencial(idCredencial, getAutorAlteracao(idUsuario), idUsuario);
  }

  public Credencial desbloquearCredencial(Long idCredencial, Integer autor, Integer idUsuario) {

    Credencial credencial = getContaPagamentoFacade().findById(idCredencial);

    if (credencial == null) {
      throw new GenericServiceException("Não foi encontrada credencial para ser desbloqueada. ");
    }

    if (credencial.getTipoStatusV2().getTipoGrupoStatus().getPermiteDesbloquear()) {
      credencial =
          alterarStatusCredencial(idUsuario, Constantes.TIPO_STATUS_DESBLOQUEADO, credencial);
    } else {
      throw new GenericServiceException("Esta credencial não pode ser desbloqueada.");
    }

    if (!getContaPagamentoFacade().habilitarCredencial(credencial, idUsuario)) {
      throw new GenericServiceException(
          "Não foi possivel habilitar uso da credencial.",
          "IdCredencial: " + credencial.getIdCredencial());
    }

    getCredencialService()
        .cancelaCartoesAnteriores(
            credencial.getIdConta(), credencial.getIdPessoa(), credencial.getCsn(), idUsuario);

    if (credencial.getTitularidade().equals(TITULAR)) {
      desbloquearConta(credencial.getIdConta(), autor, idUsuario);
    }

    desbloquearContaPessoa(credencial.getIdConta(), credencial.getIdPessoa());

    Pessoa pessoa = findPessoaTitularDaConta(credencial.getIdConta());
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(pessoa.getIdInstituicao())) {
      credencialService.enviarSenhaPorSms(credencial);
    }

    return credencial;
  }

  private ContaPessoa desbloquearContaPessoa(Long idConta, Long idPessoa) {
    ContaPessoa contaPessoa =
        getContaPagamentoFacade().findOneByIdPessoaAndIdConta(idPessoa, idConta);

    if (contaPessoa == null) {
      throw new GenericServiceException(
          "ContaPessoa não encontrada. idPessoa = " + idPessoa + " , idConta = " + idConta);
    }

    return desbloquearContaPessoa(contaPessoa);
  }

  private ContaPessoa desbloquearContaPessoa(ContaPessoa contaPessoa) {
    if (contaPessoa == null) {
      throw new GenericServiceException("ContaPessoa não encontrada. ");
    }

    if (contaPessoa.getStatus().equals(BLOQUEIO_CRIACAO)) {

      contaPessoa.setStatus(STATUS_ATIVO);
      LocalDateTime now = LocalDateTime.now();
      contaPessoa.setDataHoraStatus(now);
      contaPessoa.setDataHoraDesbloqueio(now);

      return getContaPagamentoFacade().saveContaPessoa(contaPessoa);
    }
    return contaPessoa;
  }

  public ContaPagamento desbloquearConta(Long idConta, Integer autor, Integer idUsuario) {

    ContaPagamento contaPagamento = findById(idConta);

    // Verificando se a conta existe
    if (contaPagamento == null) {
      throw new GenericServiceException("A conta informada não foi encontrada: " + idConta);
    }
    if (!contaPagamento.getTipoStatusV2().getTipoGrupoStatus().getPermiteDesbloquear()) {
      throw new GenericServiceException("A conta informada não pode ser desbloqueda: " + idConta);
    }

    Integer idStatusOrigem = contaPagamento.getIdStatusV2();
    if (idStatusOrigem.equals(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM)) {

      MapaStatus mapaStatus =
          mapaStatusService.findOneMapaStatus(
              contaPagamento.getIdProcessadora(),
              contaPagamento.getIdInstituicao(),
              contaPagamento.getIdProdutoInstituicao(),
              APLICABILIDADE_CONTA,
              autor,
              idStatusOrigem,
              Constantes.TIPO_STATUS_DESBLOQUEADO);

      if (mapaStatus == null) {
        throw new GenericServiceException(
            "Alteração de Status da Conta não permitida. Status destino: "
                + Constantes.TIPO_STATUS_DESBLOQUEADO
                + " Status Origem: "
                + idStatusOrigem
                + " Autor: "
                + autor
                + " do produto: "
                + contaPagamento.getIdProdutoInstituicao());
      } else {
        if (mapaStatus.getStatusDestinoJcard() != null) {
          getContaPagamentoFacade()
              .alterarStatusConta(contaPagamento, mapaStatus.getStatusDestinoJcard());
        }

        contaPagamento.setDataHoraStatusConta(LocalDateTime.now());
        contaPagamento.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);
        contaPagamento.setIdUsuarioManutencao(Constantes.ID_USUARIO_INCLUSAO_PORTADOR);

        if (Constantes.ID_PRODUCAO_INSTITUICAO_CELER.equals(contaPagamento.getIdInstituicao())) {
          return save(contaPagamento, true, idStatusOrigem);
        } else {
          return save(contaPagamento);
        }
      }
    }

    return null;
  }

  public ContaPagamento findContaByHierarquia(Long idConta, SecurityUser user) {
    return contaPagamentoRepository.findContaByHierarquia(idConta, user);
  }

  public ContaPagamento findByIdNotNull(Long idConta) {
    ContaPagamento conta = findById(idConta);
    if (conta == null) {
      throw new GenericServiceException("Conta Não encontrada.");
    }
    return conta;
  }

  private Long getIdTitular(ContaPagamento conta) {
    Long idPessoa = null;
    List<ContaPessoa> contasPessoa = conta.getContasPessoa();

    if (contasPessoa == null) {
      contasPessoa = getContaPagamentoFacade().lisContaPessoafindByIdConta(conta.getIdConta());
    }

    for (ContaPessoa contaPessoa : contasPessoa) {
      if (contaPessoa.getIdTitularidade().equals(TITULAR)) {
        idPessoa = contaPessoa.getIdPessoa();
        break;
      }
    }
    return idPessoa;
  }

  public TransferenciaEntreConta prepareTranferenciaEntreContas(
      String pinCredencialOrigem,
      BigDecimal valorTransferencia,
      String tokenInterno,
      Credencial credencialDestino) {

    TransferenciaEntreConta transf = new TransferenciaEntreConta();
    transf.setPin(pinCredencialOrigem);
    transf.setTokenInternoDestino(credencialDestino.getTokenInterno());
    transf.setTokenInternoOrigem(tokenInterno);
    transf.setValor(valorTransferencia);
    return transf;
  }

  private void createEnderecoPessoa(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Pessoa pessoa) {
    List<EnderecoPessoaRequest> enderecosReq =
        cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos();
    if (enderecosReq != null && !enderecosReq.isEmpty()) {
      EnderecoPessoa[] enderecos = new EnderecoPessoa[enderecosReq.size()];
      prepareEnderecos(cadastrarContaPagPessoa.getEnderecosPessoaRequest(), enderecos, pessoa);
      enderecoPessoaService.cadastrarEnderecosPessoa(
          cadastrarContaPagPessoa.getIdUsuarioInclusao(), enderecos);
    }
  }

  @Transactional
  public CredencialGerada createCredencial(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      ContaPagamento conta) {
    GerarCredencialRequest gerarCredencial = new GerarCredencialRequest();
    gerarCredencial.setIdUsuario(cadastrarContaPagPessoa.getIdUsuarioInclusao());
    gerarCredencial.setIdPessoa(pessoa.getIdPessoa());
    gerarCredencial.setIdConta(conta.getIdConta());
    gerarCredencial.setIntegracao(cadastrarContaPagPessoa.getIsIntegracao());

    gerarCredencial.setVirtual(setVirtualCartaoBaseadoConfiguracaoProduto(conta));

    return getContaPagamentoFacade().gerarCredencial(gerarCredencial);
  }

  public Boolean setVirtualCartaoBaseadoConfiguracaoProduto(ContaPagamento conta) {
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    if (produtoContratadoService.isPrimeiroCartaoVirtual(conta, produtoInstituicaoConfiguracao)) {

      return credencialRepository.existsCredencialByIdConta(conta.getIdConta())
          ? NAO_CRIAR_CARTAO_VIRTUAL
          : CRIAR_CARTAO_VIRTUAL;
    } else {
      return produtoInstituicaoConfiguracao.getVirtual();
    }
  }

  private CredencialGerada createCredencialPreEmitida(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      ContaPagamento conta,
      CredencialPreEmitida credPreEmi) {
    GerarCredencialRequest gerarCredencial = new GerarCredencialRequest();
    gerarCredencial.setIdUsuario(cadastrarContaPagPessoa.getIdUsuarioInclusao());
    gerarCredencial.setIdPessoa(pessoa.getIdPessoa());
    gerarCredencial.setIdConta(conta.getIdConta());
    gerarCredencial.setVirtual(NAO_CRIAR_CARTAO_VIRTUAL);

    return getContaPagamentoFacade().gerarCredencialPreEmitida(gerarCredencial, credPreEmi);
  }

  public void vincularContaPessoa(Pessoa pessoa, ContaPagamento conta) {
    ContaPessoa contaPessoa = prepareContaPessoa(pessoa, conta);
    getContaPagamentoFacade().vincularConta(contaPessoa);
  }

  private ContaPessoa prepareContaPessoa(Pessoa pessoa, ContaPagamento conta) {
    ContaPessoa contaPessoa = ContaPagamentoUtil.prepareContaPessoa(pessoa, conta);
    if (Objects.nonNull(pessoa.getNomeCartaoImpresso())) {
      contaPessoa.setNomeCartaoImpresso(pessoa.getNomeCartaoImpresso());
    }
    return contaPessoa;
  }

  private Pessoa createPessoa(CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa) {
    Pessoa pessoa = null;

    if (ConstantesB2B.TIPO_PESSOA_FISICA.equals(cadastrarContaPagPessoa.getTipoPessoa())) {

      pessoa = new Pessoa();
      CadastrarPessoaFisica cadastrarPessoa = preparePessoaFisica(cadastrarContaPagPessoa, pessoa);

      pessoa = getContaPagamentoFacade().createPessoaFisica(cadastrarPessoa, pessoa);

    } else {
      pessoa = new Pessoa();
      CadastrarPessoaJuridica cadastrarPessoa =
          preparePessoaJuridica(cadastrarContaPagPessoa, pessoa);

      getContaPagamentoFacade().createPessoaJuridica(cadastrarPessoa, pessoa);
    }

    return pessoa;
  }

  private Pessoa createPessoaCredPreEmitida(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Integer idProdInst) {
    Pessoa pessoa = null;

    if (ConstantesB2B.TIPO_PESSOA_FISICA.equals(cadastrarContaPagPessoa.getTipoPessoa())) {
      pessoa = new Pessoa();

      CadastrarPessoaFisica cadastrarPessoa =
          preparePessoaFisica(cadastrarContaPagPessoa, pessoa, idProdInst);

      pessoa = getContaPagamentoFacade().createPessoaFisica(cadastrarPessoa, pessoa);
    } else if (ConstantesB2B.TIPO_PESSOA_JURIDICA.equals(cadastrarContaPagPessoa.getTipoPessoa())) {
      pessoa = new Pessoa();
      CadastrarPessoaJuridica cadastrarPessoa =
          preparePessoaJuridica(cadastrarContaPagPessoa, pessoa);

      getContaPagamentoFacade().createPessoaJuridica(cadastrarPessoa, pessoa);
    } else {
      throw new GenericServiceException("Tipo Pessoa Invalida");
    }

    return pessoa;
  }

  private CadastrarPessoaJuridica preparePessoaJuridica(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Pessoa pessoa) {
    try {
      BeanUtils.copyProperties(
          cadastrarContaPagPessoa, pessoa, getNullPropertyNames(cadastrarContaPagPessoa));

      CadastrarPessoaJuridica cadastrarPessoa = new CadastrarPessoaJuridica();
      BeanUtils.copyProperties(
          cadastrarContaPagPessoa, cadastrarPessoa, getNullPropertyNames(cadastrarContaPagPessoa));
      pessoa.setIdTipoPessoa(PESSOA_JURIDICA);
      LocalDateTime now = LocalDateTime.now();
      pessoa.setDataHoraInclusao(now);
      pessoa.setDataInicioRelacionamento(now);
      pessoa.setDataFundacao(
          DateUtil.dateToLocalDateTime(cadastrarContaPagPessoa.getDataFundacao()));
      cadastrarPessoa.setIdProdInstituicao(
          cadastrarContaPagPessoa.getValoresCargasProdutos().get(0).getIdProdutoInstituicao());

      return cadastrarPessoa;
    } catch (BeansException e) {
      throw new GenericServiceException(
          "Ocorreu um erro de cópia de dados no cadastro de pessoa jurídica ", e);
    }
  }

  private CadastrarPessoaFisica preparePessoaFisica(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa, Pessoa pessoa) {
    try {
      BeanUtils.copyProperties(
          cadastrarContaPagPessoa, pessoa, getNullPropertyNames(cadastrarContaPagPessoa));

      CadastrarPessoaFisica cadastrarPessoa = new CadastrarPessoaFisica();
      BeanUtils.copyProperties(
          cadastrarContaPagPessoa, cadastrarPessoa, getNullPropertyNames(cadastrarContaPagPessoa));

      if (Objects.nonNull(cadastrarContaPagPessoa.getDataNascimento())) {
        pessoa.setDataNascimento(
            DateUtil.dateToLocalDateTime(cadastrarContaPagPessoa.getDataNascimento()));
      }
      if (Objects.nonNull(cadastrarContaPagPessoa.getRgDataEmissao())) {
        pessoa.setRgDataEmissao(
            DateUtil.dateToLocalDateTime(cadastrarContaPagPessoa.getRgDataEmissao()));
      }
      pessoa.setIdTipoPessoa(ConstantesB2B.TIPO_PESSOA_FISICA);
      LocalDateTime now = LocalDateTime.now();
      pessoa.setDataHoraInclusao(now);
      pessoa.setDataInicioRelacionamento(now);
      cadastrarPessoa.setIdProdInstituicao(
          cadastrarContaPagPessoa.getValoresCargasProdutos().get(0).getIdProdutoInstituicao());

      return cadastrarPessoa;
    } catch (BeansException e) {
      throw new GenericServiceException(
          "Ocorreu um erro de cópia de dados no cadastro de pessoa física.", e);
    }
  }

  private CadastrarPessoaFisica preparePessoaFisica(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      Integer idProdInst) {
    try {
      BeanUtils.copyProperties(
          cadastrarContaPagPessoa, pessoa, getNullPropertyNames(cadastrarContaPagPessoa));

      CadastrarPessoaFisica cadastrarPessoa = new CadastrarPessoaFisica();
      BeanUtils.copyProperties(
          cadastrarContaPagPessoa, cadastrarPessoa, getNullPropertyNames(cadastrarContaPagPessoa));

      if (Objects.nonNull(cadastrarContaPagPessoa.getDataNascimento())) {
        pessoa.setDataNascimento(
            DateUtil.dateToLocalDateTime(cadastrarContaPagPessoa.getDataNascimento()));
      }
      if (Objects.nonNull(cadastrarContaPagPessoa.getRgDataEmissao())) {
        pessoa.setRgDataEmissao(
            DateUtil.dateToLocalDateTime(cadastrarContaPagPessoa.getRgDataEmissao()));
      }

      pessoa.setIdTipoPessoa(ConstantesB2B.TIPO_PESSOA_FISICA);
      LocalDateTime now = LocalDateTime.now();
      pessoa.setDataHoraInclusao(now);
      pessoa.setDataInicioRelacionamento(now);

      if (Objects.isNull(pessoa.getNomeEmbossado())) {

        if (Objects.isNull(pessoa.getNomeCompleto())) {
          throw new GenericServiceException("Nome não registrado.");
        }

        pessoa.setNomeEmbossado(Abreviador.abreviarNome(pessoa.getNomeCompleto()));
      }
      cadastrarPessoa.setIdProdInstituicao(idProdInst);

      return cadastrarPessoa;
    } catch (BeansException e) {
      throw new GenericServiceException(
          "Ocorreu um erro de cópia de dados no cadastro de pessoa física.", e);
    }
  }

  @Transactional
  public void prepareCadastrarConta(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      ContaPagamento conta,
      ContaPagamentoRequest createConta,
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao) {

    BeanUtils.copyProperties(
        cadastrarContaPagPessoa, createConta, getNullPropertyNames(cadastrarContaPagPessoa));
    createConta.setIdProdutoInstituicao(valorCargaProdutoInstituicao.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        obterProdutoInstituicaoConfiguracao(cadastrarContaPagPessoa, valorCargaProdutoInstituicao);

    ProdutoInstituicao produtoInstituicao = obterProdutoInstituicao(valorCargaProdutoInstituicao);

    if (produtoInstituicao.getB2b()) {
      if (produtoInstituicaoConfiguracao.getIdRelacionamento().equals(ID_RELACIONAMENTO_POS_PAGO)) {
        createConta.setLimiteUnico(valorCargaProdutoInstituicao.getValorCargaPadrao());
      } else {
        createConta.setValorCargaPadrao(valorCargaProdutoInstituicao.getValorCargaPadrao());
      }
    } else if (Objects.nonNull(valorCargaProdutoInstituicao.getValorCargaPadrao())
        && produtoInstituicaoConfiguracao
            .getIdRelacionamento()
            .equals(ID_RELACIONAMENTO_POS_PAGO)) {
      createConta.setLimiteUnico(valorCargaProdutoInstituicao.getValorCargaPadrao());
    }

    BeanUtils.copyProperties(createConta, pessoa, getNullPropertyNames(createConta));
    BeanUtils.copyProperties(createConta, conta, getNullPropertyNames(createConta));

    conta.setDataHoraInclusao(LocalDateTime.now());
    conta.setDataHoraStatusConta(LocalDateTime.now());
    conta.setIdStatusV2(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);
    conta.setSaldoDisponivel(VALOR_ABRIR_CONTA);
  }

  private ProdutoInstituicaoConfiguracao obterProdutoInstituicaoConfiguracao(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao) {
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            cadastrarContaPagPessoa.getIdProcessadora(),
            valorCargaProdutoInstituicao.getIdProdutoInstituicao(),
            cadastrarContaPagPessoa.getIdInstituicao());

    if (Objects.isNull(produtoInstituicaoConfiguracao)) {
      throw new GenericServiceException(
          "A configuração do ProdutoInstituicao não pode ser localizada com os parâmetros: "
              + cadastrarContaPagPessoa.getIdProcessadora()
              + " | "
              + cadastrarContaPagPessoa.getIdInstituicao()
              + " | "
              + valorCargaProdutoInstituicao.getIdProdutoInstituicao());
    }
    return produtoInstituicaoConfiguracao;
  }

  private ProdutoInstituicao obterProdutoInstituicao(
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao) {
    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(
            valorCargaProdutoInstituicao.getIdProdutoInstituicao());

    if (Objects.isNull(produtoInstituicao)) {
      throw new GenericServiceException(
          "O ProdutoInstituicao não pode ser localizada com id: "
              + valorCargaProdutoInstituicao.getIdProdutoInstituicao());
    }
    return produtoInstituicao;
  }

  private EnderecoPessoa prepareEndereco(
      EnderecoPessoaRequest model, EnderecoPessoa endereco, Pessoa pessoa) {
    BeanUtils.copyProperties(model, endereco, getNullPropertyNames(model));
    endereco.setIdPessoa(pessoa.getIdPessoa());
    return endereco;
  }

  private EnderecoPessoa[] prepareEnderecos(
      EnderecosPessoaRequest model, EnderecoPessoa[] enderecos, Pessoa pessoa) {
    for (int i = 0; i < model.getEnderecos().size(); i++) {
      EnderecoPessoa endereco = new EnderecoPessoa();
      enderecos[i] = prepareEndereco(model.getEnderecos().get(i), endereco, pessoa);
    }

    return enderecos;
  }

  public ContaPagamento findByCpfAndProduto(
      String cpf, Integer idProdutoInstituicao, Integer tipoPessoa) {
    return contaPagamentoRepository.findByCpfAndProduto(cpf, idProdutoInstituicao, tipoPessoa);
  }

  public List<String> findByListaCpfAndProduto(
      Set<String> linhasCpf, Integer idProdutoInstituicao) {
    return contaPagamentoRepository.findByListaCpfAndProduto(linhasCpf, idProdutoInstituicao);
  }

  public List<ContaPagamento> findContasPagamentoByListaCpfAndProduto(
      List<String> linhasCpf, Integer idProdutoInstituicao) {
    return contaPagamentoRepository.findContasPagamentoByListaCpfAndProduto(
        linhasCpf, idProdutoInstituicao);
  }

  public ContaPagamento findByCpfAndProdutoAndHierarquia(
      String cpf,
      Integer idProdutoInstituicao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contaPagamentoRepository.findByCpfAndProdutoAndHierarquia(
        cpf,
        idProdutoInstituicao,
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoDeRelacionamento);
  }

  public ContaPagamento findByIdConta(Long idConta) {
    return contaPagamentoRepository.findByIdConta(idConta);
  }

  public List<ContaPagamento> findByCpfAndHierarquia(
      String cpf,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contaPagamentoRepository.findByCpfAndHierarquia(
        cpf, idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  public Boolean isContaPagamentoExisteByHierarquiaAndDocumentoAndProdutoAndTitularidade(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      String documento,
      Integer idProdutoInstituicao) {
    Integer exists =
        contaPagamentoRepository
            .isContaPagamentoExisteByHierarquiaAndDocumentoAndProdutoAndTitularidade(
                idProcessadora,
                idInstituicao,
                idRegional,
                idFilial,
                idPontoDeRelacionamento,
                documento,
                idProdutoInstituicao);

    return exists > 0;
  }

  public List<ContaPagamento> findByCpf(String cpf, Integer idProcessadora, Integer idInstituicao) {
    return contaPagamentoRepository.findByCpf(cpf, idProcessadora, idInstituicao);
  }

  public List<ContaPagamento> findByCpf(
      String cpf, Integer idProcessadora, Integer idInstituicao, Integer idTipoPessoa) {
    return contaPagamentoRepository.findByCpf(cpf, idProcessadora, idInstituicao, idTipoPessoa);
  }

  public List<ContaPagamento> findByCpfAndTipoStatus_idGrupoStatus(
      String cpf, Integer idProcessadora, Integer idInstituicao, Integer tipoPessoa) {
    return contaPagamentoRepository.findByCpfAndTipoStatus_idGrupoStatus(
        cpf, idProcessadora, idInstituicao, tipoPessoa, DESBLOQUEADO);
  }

  public List<ContaPagamento> findByCpfAtivo(
      String cpf, Integer idProcessadora, Integer idInstituicao, Integer idTipoPessoa) {
    return contaPagamentoRepository.findByCpfAtivos(
        cpf, idProcessadora, idInstituicao, idTipoPessoa);
  }

  public ContaPagamento findByTipoDocAndDocAndProduto(
      String doc, Integer idProdutoInstituicao, SecurityUser user, Integer tipoDoc) {
    return contaPagamentoRepository.findByTipoDocAndDocAndProduto(
        doc,
        idProdutoInstituicao,
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdRegional(),
        user.getIdFilial(),
        user.getIdPontoDeRelacionamento(),
        tipoDoc);
  }

  public ContaPagamento findByTipoDocAndDocAndProduto(
      String doc,
      Integer idProdutoInstituicao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento,
      Integer tipoDoc) {
    return contaPagamentoRepository.findByTipoDocAndDocAndProduto(
        doc,
        idProdutoInstituicao,
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoRelacionamento,
        tipoDoc);
  }

  public List<ContaPagamento> findByMatriculaAndProduto(
      String matricula, Integer idProdutoInstituicao, SecurityUser user) {
    return contaPagamentoRepository.findByMatriculaAndProduto(
        matricula,
        idProdutoInstituicao,
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdRegional(),
        user.getIdFilial(),
        user.getIdPontoDeRelacionamento());
  }

  public List<ExisteMatriculaEmpresaVO> findByMatriculaExisteEmpresa(
      String matricula, SecurityUser user) {
    return contaPagamentoRepository.findByMatriculaExisteEmpresa(
        matricula,
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdRegional(),
        user.getIdFilial(),
        user.getIdPontoDeRelacionamento());
  }

  public void saveLogUltimasContas(LogUltimaContaModel model, SecurityUser user) {
    LogUltimasContas logContas =
        ultimasContasService.findByIdUsuarioAndIdConta(user.getIdUsuario(), model.getIdConta());

    if (logContas == null) {
      logContas = new LogUltimasContas();
      logContas.setIdUsuario(user.getIdUsuario());
      logContas.setIdConta(model.getIdConta());
    }

    logContas.setDtHrUltimaPesquisa(new Date());
    logContas.setDadosConta(
        DateUtil.dateFormat("dd/MM/yyyy HH:mm:ss", logContas.getDtHrUltimaPesquisa())
            + " - "
            + model.getNomeCompleto()
            + " - "
            + model.getDescProdutoInstituicao()
            + " - "
            + model.getIdConta());

    try {
      ultimasContasService.save(logContas);
    } catch (Exception e) {
      throw new GenericServiceException("Impossível de salvar o log da última conta acessada!");
    }
  }

  public List<LogUltimasContas> getUltimasContasAcessadas(SecurityUser user) {

    return ultimasContasService.findTop30ByIdUsuarioOrderByDtHrUltimaPesquisaDesc(
        user.getIdUsuario());
  }

  public List<GetExtratoCredencial> getExtratoUser(
      ExtratoRequestVo extratoRequestVo, SecurityUser user) {

    ContaPagamento conta = findByIdNotNull(extratoRequestVo.getIdConta());
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, conta);

    consultaRestritaService.checaPrivilegio(conta, user);

    return getExtrato(extratoRequestVo);
  }

  public List<GetExtratoCredencial> getExtratoPortador(
      ExtratoRequestVo extratoRequestVo, SecurityUserPortador userPortador) {

    validaIdContaPeloRequestEPortador(extratoRequestVo.getIdConta(), userPortador);

    return getExtrato(extratoRequestVo);
  }

  public List<GetExtratoCredencial> getExtratoUserEstabelecimento(
      ExtratoRequestVo extratoRequestVo, SecurityUserEstabelecimento userEstabelecimento) {

    ContaPagamento conta = findByIdNotNull(extratoRequestVo.getIdConta());
    UtilController.checkHierarquiaUsuarioEstabelecimentoLogado(userEstabelecimento, conta);

    return getExtrato(extratoRequestVo);
  }

  public List<GetExtratoCredencial> getExtrato(ExtratoRequestVo extratoRequestVo) {

    String dataInicio = DateUtil.dateFormat("yyyy-MM-dd", extratoRequestVo.getDataInicial());
    String dataFim = DateUtil.dateFormat("yyyy-MM-dd", extratoRequestVo.getDataFinal());

    ContaPagamento conta = findByIdNotNull(extratoRequestVo.getIdConta());

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();

    StringBuilder sb = new StringBuilder();
    sb.append(moeda + ",1" + moeda);
    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != null
        && produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == CREDITO) {
      sb.append(",368,1368");
    }
    moeda = sb.toString();
    List<GetExtratoCredencial> extrato = new ArrayList<>();

    if (accountCode != null
        && conta.getIdInstituicao() != null
        && ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(conta.getIdInstituicao())
        && conta.getIdContaExterna() != null) {
      Date date = DateUtil.parseDate("yyyy-MM-dd", "2018-06-01");

      if (extratoRequestVo.getDataInicial().before(date) && conta.getIdContaExterna() != null) {

        extrato =
            buscarExtratoMigracaoLancamentoBRB(
                conta.getIdContaExterna(),
                extratoRequestVo.getDataInicial(),
                extratoRequestVo.getDataFinal());
      }
    }

    // Busca no JCARD
    AccountStatementResponse asr =
        getAccountStatement(
            conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moeda,
            dataInicio,
            dataFim,
            extratoRequestVo.getPage(),
            extratoRequestVo.getPageSize(),
            extratoRequestVo.getAscendingOrder());

    extrato.addAll(
        prepareGetExtratoCredencial(
            asr.getEntries(), conta.getIdInstituicao(), conta.getIdConta()));

    if (Boolean.TRUE.equals(extratoRequestVo.getAscendingOrder())) {
      extrato.sort(Comparator.comparing(GetExtratoCredencial::getDataTransacao));
    } else {
      extrato.sort(Comparator.comparing(GetExtratoCredencial::getDataTransacao).reversed());
    }
    return extrato;
  }

  public Integer countExtrato(Long idConta, Date dataInicial, Date dataFinal) {
    String dataInicio = DateUtil.dateFormat("yyyy-MM-dd", dataInicial);
    String dataFim = DateUtil.dateFormat("yyyy-MM-dd", dataFinal);

    ContaPagamento conta = findByIdNotNull(idConta);

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();

    StringBuilder sb = new StringBuilder();
    sb.append(moeda + ",1" + moeda);
    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != null
        && produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == CREDITO) {
      sb.append(",368,1368");
    }
    moeda = sb.toString();

    // Identifica se existem transações a omitir do extrato para reduzir do count que retornará
    // completo do JCard.
    Integer omitir = omitirTransacoesCount(idConta, dataInicial, dataFinal);

    return (countAccountStatement(
            conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moeda,
            dataInicio,
            dataFim)
        - omitir);
  }

  public List<GetExtratoCredencial> buscarExtratoMigracaoLancamentoBRB(
      Long idConta, Date dataInicial, Date dataFinal) {

    return contaPagamentoRepository.findExtratoTransacoesBRB(dataInicial, dataFinal, idConta);
  }

  public List<GetExtratoCredencial> prepareGetExtratoCredencial(
      List<AccountStatementEntrie> accountStatementEntries, Integer idInstituicao, Long idConta) {
    List<GetExtratoCredencial> extrato = new ArrayList<>();
    if (Util.isNotNull(accountStatementEntries)) {
      for (AccountStatementEntrie entrie : accountStatementEntries) {
        if (isNotTransacaoComSaldoMigrado(entrie) && isNotTrasancaoGrupoAdmAndEstornada(entrie)) {
          GetExtratoCredencial transacao = prepareGetExtratoCredencial(entrie, idInstituicao);
          if (entrie.getIdTranlog() != null) {
            LogTransacoesControle controle =
                logTransacoesControleService.findById(entrie.getIdTranlog());
            if (controle != null) {
              transacao.setDataApresentacao(controle.getDataApresentacao());
            }
          }
          extrato.add(transacao);
        }
      }
    }
    omitirTransacoesExtrato(extrato, idConta);
    return extrato;
  }

  private void omitirTransacoesExtrato(List<GetExtratoCredencial> extrato, Long idConta) {
    List<Long> todosOsIds =
        extrato.stream().map(GetExtratoCredencial::getIdTranlog).collect(Collectors.toList());
    List<Long> idsARemover =
        transacoesOmitidasExtratoService.retornaIdsAOmitir(todosOsIds, idConta);
    if (idsARemover != null && !idsARemover.isEmpty()) {
      extrato.removeIf(elementoExtrato -> idsARemover.contains(elementoExtrato.getIdTranlog()));
    }
  }

  private Integer omitirTransacoesCount(Long idConta, Date dataInicial, Date dataFinal) {
    return transacoesOmitidasExtratoService.countIdsAOmitir(idConta, dataInicial, dataFinal);
  }

  private boolean isNotTransacaoComSaldoMigrado(AccountStatementEntrie entrie) {
    return !CODS_TRANSACAO_SALDO_MIGRADO_DEVEDOR_CREDOR.contains(
        entrie.getAccountStatementEntrieDetail().getFunctionCode());
  }

  private boolean isNotTrasancaoGrupoAdmAndEstornada(AccountStatementEntrie entrie) {
    return entrie.getAccountStatementEntrieDetail().getGroupType() != null
        && !entrie.getAccountStatementEntrieDetail().getGroupType().equalsIgnoreCase(ADMINISTRATIVE)
        && entrie.getReversalCount() != null
        && entrie.getReversalCount() <= 0
        && entrie.getCompletionCount() != null
        && entrie.getCompletionCount() <= 0;
  }

  private GetExtratoCredencial prepareGetExtratoCredencial(
      AccountStatementEntrie entrie, Integer idInstituicao) {
    GetExtratoCredencial transacao = new GetExtratoCredencial();
    transacao.setDataTransacao(DateUtil.dateToLocalDateTime(entrie.getPostDate()));
    transacao.setDescLocal(entrie.getAccountStatementEntrieDetail().getLocality());
    transacao.setDescSeguimento(entrie.getAccountStatementEntrieDetail().getMcc());
    transacao.setIdTransacao(entrie.getRrn());
    transacao.setIdTranlog(entrie.getIdTranlog());

    transacao.setTransacaoEstornada((entrie.getVoidCount() > 0 || entrie.getReversalCount() > 0));
    transacao.setTransacaoEstorno(
        entrie.getAccountStatementEntrieDetail().getGroupType().equals("REVERSAL"));

    transacao.setSs(entrie.getSs());

    transacao.setFunctionCode(entrie.getAccountStatementEntrieDetail().getFunctionCode());

    transacao.setQuatroUltimosNumeros(getQuatroUltimosCartao(entrie.getIdTranlog(), idInstituicao));

    StringBuilder descTransacao = new StringBuilder();
    StringBuilder descFunctionCode = new StringBuilder();
    StringBuilder descTransacaoReduzida = new StringBuilder();
    descTransacao.append(entrie.getAccountStatementEntrieDetail().getTransaction());
    descFunctionCode.append(entrie.getAccountStatementEntrieDetail().getTransaction());
    if (entrie.getAccountStatementEntrieDetail().getFreeData() != null) {
      descTransacao.append(" - " + entrie.getAccountStatementEntrieDetail().getFreeData());
      descTransacaoReduzida.append(entrie.getAccountStatementEntrieDetail().getFreeData());
    }

    if (entrie.getAccountStatementEntrieDetail().getOriginalAccount() != null) {
      descTransacao.append(" - " + entrie.getAccountStatementEntrieDetail().getOriginalAccount());
    }

    if (entrie.getAccountStatementEntrieDetail().getDestinationAccount() != null) {
      descTransacao.append(
          " - " + entrie.getAccountStatementEntrieDetail().getDestinationAccount());
    }

    if (entrie.getAccountStatementEntrieDetail().getFunctionCode() != null
        && COD_TRANSACAO_PIX_CARGA_AUTOMATIZADA.contains(
            entrie.getAccountStatementEntrieDetail().getFunctionCode())) {
      if (COD_TRANSACAO_ENVIO_PIX_CARGA_AUTOMATIZADA.contains(
          entrie.getAccountStatementEntrieDetail().getFunctionCode())) {
        String campoLivre =
            contaTransacionalPagamentoService.encontraLoteParaExtratoAutomatizacaoCargaPIXPorRRN(
                entrie.getRrn());
        if (campoLivre != null && !campoLivre.isEmpty()) {
          descTransacao.append(" - " + campoLivre);
        }
      }
      if (COD_TRANSACAO_RECEBIMENTO_PIX_CARGA_AUTOMATIZADA.contains(
          entrie.getAccountStatementEntrieDetail().getFunctionCode())) {
        Integer numeroPedido =
            b2bFaturaPixService.encontraNumeroPedidoParaExtratoPorRRN(entrie.getRrn());
        if (numeroPedido != null) {
          descTransacao.append(" - Número do pedido: " + numeroPedido);
        }
      }
    }
    transacao.setDescTransacao(descTransacao.toString());
    transacao.setDescFunctionCode(descFunctionCode.toString());
    transacao.setDescTransacaoMinima(
        descTransacaoReduzida.toString().equals("") ? "-" : descTransacaoReduzida.toString());

    transacao.setStatusTransacao(
        entrie.getAccountStatementEntrieDetail().getFunctionCode() == null
            ? "0"
            : entrie.getAccountStatementEntrieDetail().getFunctionCode());

    if (entrie.getCredit() != null && entrie.getCredit().doubleValue() > 0) {
      transacao.setSinal(CREDITADO);
      transacao.setValorTransacao(entrie.getCredit());
    } else if (entrie.getDebit() != null && entrie.getDebit().doubleValue() > 0) {
      transacao.setSinal(DEBITADO);
      transacao.setValorTransacao(entrie.getDebit());
    } else if (entrie.getCredit() != null && entrie.getCredit().doubleValue() < 0) {
      transacao.setSinal(DEBITADO);
      transacao.setValorTransacao(entrie.getCredit().multiply(new BigDecimal(NEGATIVO)));
    } else if (entrie.getDebit() != null && entrie.getDebit().doubleValue() < 0) {
      transacao.setSinal(CREDITADO);
      transacao.setValorTransacao(entrie.getDebit().multiply(new BigDecimal(NEGATIVO)));
    }

    transacao.setDataTransacaoFmt(DateUtil.dateFormat(FMT_DD_MM_YYYY, entrie.getPostDate()));
    transacao.setNumeroAutorizacao(entrie.getApprovalnumber());
    transacao.setNsu(entrie.getNsu());
    transacao.setDataTransacaoFmtMes(DateUtil.dateFormat(FMT_D_MMM, entrie.getPostDate()));
    transacao.setHasComprovante(isTransacaoComComprovante(transacao));
    return transacao;
  }

  private boolean isTransacaoComComprovante(GetExtratoCredencial transacao) {
    String comprovante = null;
    if (isTransacaoComFunctionCodePassivelDeEmitirComprovante(transacao.getStatusTransacao())) {
      if (CODS_TRANSACAO_PAGAMENTOS_COM_COMPROVANTE.contains(transacao.getStatusTransacao())) {
        comprovante =
            gatewayPagtoExternoService.getComprovantePagamentoByIdTransacao(
                transacao.getIdTransacao());
      } else if (CODS_TRANSACAO_TEDS_COM_COMPROVANTE.contains(transacao.getStatusTransacao())) {
        comprovante = tedService.getComprovanteByIdTransacao(transacao.getIdTransacao());
      } else if (CODS_TRANSACAO_RECARGAS_COM_COMPROVANTE.contains(transacao.getStatusTransacao())) {
        comprovante =
            gatewayPagtoExternoService.getComprovanteRecargaIdTransacao(transacao.getIdTransacao());
      }
    }
    return comprovante != null && !comprovante.isEmpty();
  }

  private boolean isTransacaoComFunctionCodePassivelDeEmitirComprovante(String codTransacao) {
    return CODS_TRANSACAO_PAGAMENTOS_COM_COMPROVANTE.contains(codTransacao)
        || CODS_TRANSACAO_TEDS_COM_COMPROVANTE.contains(codTransacao)
        || CODS_TRANSACAO_RECARGAS_COM_COMPROVANTE.contains(codTransacao);
  }

  public AccountStatementResponse getAccountStatement(
      String idInstituicao,
      String idAccountCode,
      String idMoeda,
      String startDate,
      String endDate) {

    AccountStatement accountStatement =
        AccountStatementService.prepareAccountStatement(
            idInstituicao, idAccountCode, idMoeda, startDate, endDate);

    GetAccountStatementResponse response =
        accountStatementService.findAccountStatement(accountStatement);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível encontrar o extrato solicitado: " + response.getErrors());
    }

    return response.getAccountStatement();
  }

  public AccountStatementResponse getAccountStatement(
      String idInstituicao,
      String idAccountCode,
      String idMoeda,
      String startDate,
      String endDate,
      Integer page,
      Integer pageSize,
      Boolean ascendingOrder) {

    AccountStatement accountStatement =
        AccountStatementService.prepareAccountStatement(
            idInstituicao, idAccountCode, idMoeda, startDate, endDate);
    accountStatement.setPage(page);
    accountStatement.setPageSize(pageSize);
    accountStatement.setAscendingOrder(ascendingOrder);

    GetAccountStatementResponse response =
        accountStatementService.findAccountStatement(accountStatement);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível encontrar o extrato solicitado: " + response.getErrors());
    }

    return response.getAccountStatement();
  }

  public Integer countAccountStatement(
      String idInstituicao,
      String idAccountCode,
      String idMoeda,
      String startDate,
      String endDate) {

    AccountStatement accountStatement =
        AccountStatementService.prepareAccountStatement(
            idInstituicao, idAccountCode, idMoeda, startDate, endDate);

    GetAccountStatementResponse response =
        accountStatementService.countAccountStatement(accountStatement);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível encontrar o extrato solicitado: " + response.getErrors());
    }

    return response.getCount();
  }

  @Transactional
  public void atualizarPerfilTarifarioConta(
      Long idConta, Integer idPerfilTarifario, SecurityUser user) {

    ContaPagamento conta = findById(idConta);

    if (idPerfilTarifario == 0) {
      idPerfilTarifario = null;
    }

    Integer idPerfilAnterior = conta.getIdPerfilTarifario();

    conta.setIdPerfilTarifario(idPerfilTarifario);
    conta.setIdUsuarioManutencao(user.getIdUsuario());
    conta = save(conta);

    ProdutoInstituicaoConfiguracao produto =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    if (produto == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar as configurações do produto da conta!");
    }

    String accountCode = getOrPrepareAccountCode(conta, produto);

    // se a conta estiver relacionada à um perfil tarifário de conta
    // anteriormente,
    // desativar estas tarifas no jcard
    if (idPerfilAnterior != null) {
      List<PerfilTarifarioTransacao> tarifasUpdate =
          perfilTarifarioTransacaoService.findByPerfilTarifarioIdPerfilTarifario(idPerfilAnterior);

      for (PerfilTarifarioTransacao tmp : tarifasUpdate) {
        updateTaxAccount(
            accountCode,
            produto.getProdutoInstituicao().getDescProdInstituicao(),
            tmp.getValorTarifa(),
            tmp.getId().getCodTransacao(),
            false,
            produto
                .getProdutoInstituicao()
                .getProdutoInstituicaoConfiguracao()
                .get(0)
                .getMoeda()
                .getIdMoeda());
      }
    }

    // adicionar tarifas do perfil atual à conta no jcard
    if (conta.getIdPerfilTarifario() != null) {
      List<PerfilTarifarioTransacao> tarifasAdd =
          perfilTarifarioTransacaoService.findByPerfilTarifarioIdPerfilTarifario(
              conta.getIdPerfilTarifario());

      for (PerfilTarifarioTransacao tmp : tarifasAdd) {
        prepareCreateTaxAccount(
            accountCode,
            produto.getProdutoInstituicao().getDescProdInstituicao(),
            tmp.getValorTarifa(),
            tmp.getId().getCodTransacao(),
            produto
                .getProdutoInstituicao()
                .getProdutoInstituicaoConfiguracao()
                .get(0)
                .getMoeda()
                .getIdMoeda());
      }
    }
  }

  public String getOrPrepareAccountCode(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produto) {

    if (conta.getIdAccountCode() != null && !conta.getIdAccountCode().isEmpty()) {
      return conta.getIdAccountCode();
    }

    HierarquiaInstituicaoId idHierarquiaInst =
        new HierarquiaInstituicaoId(conta.getIdProcessadora(), conta.getIdInstituicao());
    HierarquiaInstituicao instituicao = instituicaoService.findById(idHierarquiaInst);

    String cardHAccount = null;
    ProdutoInstituidor produtoInstituidor = produto.getProdutoInstituidor();

    if (produtoInstituidor != null) {
      Integer funcao =
          produtoInstituidor.getFuncao() == null ? DEBITO : produtoInstituidor.getFuncao();

      if (funcao.equals(DEBITO)) {
        cardHAccount = instituicao.getCardholderDebitAccount();
      } else {
        cardHAccount = instituicao.getCardholderCreditAccount();
      }
    }

    ContaPessoa pessoa =
        conta.getContasPessoa().stream()
            .filter(p -> p.getIdTitularidade().equals(1))
            .collect(Collectors.toList())
            .get(0);

    return AccountService.getValidCode(cardHAccount, pessoa.getIdPessoa(), conta.getIdConta());
  }

  private void prepareCreateTaxAccount(
      String accountCode,
      String productName,
      BigDecimal valorTarifa,
      Integer codTransacao,
      Integer idMoeda) {
    CreateTaxAccount busca =
        TaxAccountService.prepareBuscaTarifaToTaxAccount(
            accountCode, productName, valorTarifa, codTransacao, idMoeda);
    GetTaxAccountResponse resultado = taxAccountService.findTaxProduct(busca);

    if (resultado.getAccountfee() != null) {
      if (resultado.getAccountfee().getEnddate() != null) {
        updateTaxAccount(accountCode, productName, valorTarifa, codTransacao, true, idMoeda);
      }
    } else {
      createTaxAccount(accountCode, productName, valorTarifa, codTransacao, idMoeda);
    }
  }

  private void createTaxAccount(
      String accountCode,
      String productName,
      BigDecimal valorTarifa,
      Integer codTransacao,
      Integer idMoeda) {
    CreateTaxAccount create =
        TaxAccountService.prepareTarifaToCreateTaxAccount(
            accountCode, productName, valorTarifa, codTransacao, idMoeda);
    JcardResponse response = taxAccountService.createTaxAccount(create);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar Tax Account: " + response.getErrors());
    }
  }

  private void updateTaxAccount(
      String accountCode,
      String productName,
      BigDecimal valorTarifa,
      Integer codTransacao,
      Boolean ativa,
      Integer idMoeda) {
    TaxAccount update =
        TaxAccountService.prepareTarifaToTaxAccount(
            accountCode, productName, valorTarifa, codTransacao, ativa, idMoeda);
    JcardResponse response = taxAccountService.updateTaxAccount(update);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível atualizar Tax Account: " + response.getErrors());
    }
  }

  public Boolean existeContaWithCpfAndProduto(BuscaContaDocumentoProduto model) {
    Integer count =
        contaPagamentoRepository.existeContaWithCpfAndProduto(
            model.getDocumento(), model.getIdProdInstituicao());
    return count > 0;
  }

  public List<ContaPagamentoResponse> existeContaWithCpfProdutoNotB2b(
      BuscaContaDocumentoProduto model) {

    List<ContaPagamento> contas =
        contaPagamentoRepository.findByProdutoNotB2b(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getIdTipoPessoa(),
            model.getDocumento());
    List<ContaPagamentoResponse> contasResponse = new ArrayList<ContaPagamentoResponse>();
    if (contas == null) {
      ContaPagamentoResponse contaResponse = new ContaPagamentoResponse();
      contaResponse.setMensagem("Não possue conta NãoB2B.");
      contasResponse.add(contaResponse);
    } else {
      for (ContaPagamento contaPagamento : contas) {
        ContaPagamentoResponse contaResponse = new ContaPagamentoResponse();
        contaResponse.setIdConta(contaPagamento.getIdConta());
        contaResponse.setIdContaPagamento(contaPagamento.getIdContaPagamento());
        contasResponse.add(contaResponse);
      }
    }
    return contasResponse;
  }

  @Transactional
  public void alterarValor(AtualizarCargaOuLimiteRequest model, SecurityUser user) {

    ContaPagamento contaPagamento = findById(model.getIdConta());

    if (contaPagamento == null) {
      throw new GenericServiceException(
          "A conta informada não foi encontrada: " + model.getIdConta());
    }

    if (ConstantesB2B.ID_RELACIONAMENTO_POS_PAGO.equals(contaPagamento.getIdRelacionamento())) {

      BigDecimal limiteUnico = new BigDecimal(model.getLimiteUnico());

      validarLimiteGlobal(limiteUnico, user, user.getIdPontoDeRelacionamento());

      contaPagamento.setLimiteUnico(model.getLimiteUnico());
    } else {
      contaPagamento.setValorCargaPadrao(model.getValorCargaPadrao());
    }
    contaPagamento.setIdUsuarioManutencao(user.getIdUsuario());

    save(contaPagamento);

    // definir limite de contas-pós
    if (ConstantesB2B.ID_RELACIONAMENTO_POS_PAGO.equals(contaPagamento.getIdRelacionamento())) {
      if (contaPagamento.getLimiteUnico() != null) {
        editLimiteUnicoContaPos(contaPagamento);
      }
    }
  }

  public void validarLimiteGlobal(
      BigDecimal limiteUnico, SecurityUser user, Integer idPontoRelacionamento) {

    HierarquiaPontoDeRelacionamento pontoRelacionamento =
        hierarquiaPontoRelacioService.getB2BPorId(idPontoRelacionamento, user);

    BigDecimal valorTodosLimites = sumValorConveniosByPontoRelacionamento(user);

    valorTodosLimites = valorTodosLimites == null ? BigDecimal.ZERO : valorTodosLimites;

    BigDecimal valorRequerido = valorTodosLimites.add(limiteUnico);

    BigDecimal limiteMaxCredito =
        pontoRelacionamento.getLimiteMaxCredito() == null
            ? BigDecimal.ZERO
            : pontoRelacionamento.getLimiteMaxCredito();

    if (limiteMaxCredito.compareTo(valorRequerido) < 0
        && ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("msg", "Máximo de crédito atingido. Não foi possível alterar o limite.");
      map.put(
          "DTL",
          "Limite máximo: "
              + limiteMaxCredito
              + ". Limite Disponível: "
              + (limiteMaxCredito.subtract(valorTodosLimites)));
      map.put("permitirForcado", 1);
      map.put("inadimplente", "Empresa encontra-se inadimplente.");
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    } else if (limiteMaxCredito.compareTo(valorRequerido) < 0
        && !ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("msg", "Máximo de crédito atingido. Não foi possível alterar o limite.");
      map.put(
          "DTL",
          "Limite Máximo: "
              + limiteMaxCredito
              + ". Limite Disponível: "
              + (limiteMaxCredito.subtract(valorTodosLimites)));
      map.put("permitirForcado", 1);
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    } else if (limiteMaxCredito.compareTo(valorRequerido) > 0
        && ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {
      HashMap<String, Object> map = new HashMap<>();
      map.put("inadimplente", "1");
      map.put("msg", "Empresa encontra-se inadimplente.");
      throw new GenericServiceException(map, HttpStatus.UNAUTHORIZED);
    }
  }

  public BigDecimal sumValorConveniosByPontoRelacionamento(SecurityUser user) {
    BigDecimal soma =
        contaPagamentoRepository.findSomaLimiteGlobalEmpresaConvenio(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());

    return soma;
  }

  public BigDecimal getSaldoDisponivelContaJcard(ContaPagamento model) {
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            model.getIdProcessadora(), model.getIdProdutoInstituicao(), model.getIdInstituicao());

    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == null
        || produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != CREDITO) {
      throw new GenericServiceException(
          "Esta conta não é pós-paga ou não possui opção para definição de limite!");
    }

    String accountCode = getOrPrepareAccountCode(model, produtoInstituicaoConfiguracao);

    // layer
    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();
    String moedaLimite = "2" + moeda;

    // consultar limite atual no JCARD
    BigDecimal limiteAtual =
        getSaldoDisponivel(
            model.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moedaLimite);

    return limiteAtual;
  }

  @Transactional
  public void editLimiteUnicoContaPos(ContaPagamento model) {

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            model.getIdProcessadora(), model.getIdProdutoInstituicao(), model.getIdInstituicao());

    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == null
        || produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != CREDITO) {
      throw new GenericServiceException(
          "Esta conta não é pós-paga ou não possui opção para definição de limite!");
    }

    String accountCode = getOrPrepareAccountCode(model, produtoInstituicaoConfiguracao);

    // layer
    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();
    String moedaLimite = "2" + moeda;

    // consultar limite atual no JCARD
    BigDecimal limiteAtual =
        getSaldoDisponivel(
            model.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moedaLimite);

    BigDecimal resultadoLimiteNovo =
        (limiteAtual.subtract(new BigDecimal(model.getLimiteUnico())))
            .multiply(new BigDecimal(NEGATIVO));

    // type
    Integer type = (resultadoLimiteNovo.signum() == NEGATIVO ? TYPE_DEBITO : TYPE_CREDITO);

    // amount
    BigDecimal amount =
        (resultadoLimiteNovo.signum() == NEGATIVO
            ? resultadoLimiteNovo.multiply(new BigDecimal(NEGATIVO))
            : resultadoLimiteNovo);

    // journal
    HierarquiaInstituicaoId id =
        new HierarquiaInstituicaoId(model.getIdProcessadora(), model.getIdInstituicao());
    HierarquiaInstituicao instituicao = instituicaoService.findById(id);
    String journal = instituicao.getJournal();

    // serviço do JCARD
    createAccountBalance(
        amount.doubleValue(), accountCode, type, Integer.parseInt(moedaLimite), journal);
  }

  public GetSaldoConta getSaldoContaUser(Long idConta, SecurityUser user) {

    ContaPagamento conta = findByIdNotNull(idConta);
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, conta);

    if (consultaRestritaService.isRestringeConsulta(conta, user)) {
      GetSaldoConta saldo = new GetSaldoConta();
      saldo.setSaldoDisponivel(BigDecimal.ZERO);
      saldo.setSaldoDisponivelEmReais(BigDecimal.ZERO);
      saldo.setSaldoDisponivelEmPontos(BigDecimal.ZERO);
      saldo.setValorConversao(BigDecimal.ZERO);
      saldo.setValorDoPonto(BigDecimal.ZERO);
      saldo.setLimiteCredito(BigDecimal.ZERO);
      saldo.setDataLocalDateTime(LocalDateTime.now());
      String dataAtualStr = DateUtil.dateFormat("dd/MM/yyyy HH:mm:ss", new Date());
      saldo.setData(dataAtualStr.substring(0, dataAtualStr.indexOf(' ')));
      saldo.setHora(dataAtualStr.substring(dataAtualStr.indexOf(' ') + 1));
      saldo.setConsultaRestrita(TRUE);
      throw new AccessDeniedException(
          "Usuário logado não possui privilégios para visualizar estes dados.");
    }

    return getSaldoConta(conta.getIdConta());
  }

  public GetSaldoConta getSaldoContaPortador(Long idConta, SecurityUserPortador userPortador) {

    validaIdContaPeloRequestEPortador(idConta, userPortador);

    return getSaldoConta(idConta);
  }

  public GetSaldoConta getSaldoContaUserEstabelecimento(
      Long idConta, SecurityUserEstabelecimento userEstabelecimento) {

    ContaPagamento conta = findByIdNotNull(idConta);
    UtilController.checkHierarquiaUsuarioEstabelecimentoLogado(userEstabelecimento, conta);

    return getSaldoConta(conta.getIdConta());
  }

  public GetSaldoConta getSaldoConta(Long idConta) {

    ContaPagamento conta = findByIdNotNull(idConta);

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    // Descobre se a instituição possui cotação de pontos para posteriormente fazer o cálculo e
    // obter o valor do saldo em reais
    CotacaoPontos cotacaoPontos =
        cotacaoPontosRepository.findByIdInstituicao(conta.getIdInstituicao());

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    GetSaldoConta saldo = new GetSaldoConta();
    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();
    StringBuilder sb = new StringBuilder();

    sb.append(moeda + ",1" + moeda);
    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != null
        && produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == CREDITO) {

      String moedaLimite = "2" + moeda;

      saldo.setLimiteCredito(
          getSaldoDisponivel(
              conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
              accountCode,
              moedaLimite));
      sb.append(",2" + moeda);
    }
    moeda = sb.toString();

    saldo.setSaldoDisponivel(
        getSaldoDisponivel(
            conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moeda));

    String dataAtualStr = DateUtil.dateFormat("dd/MM/yyyy HH:mm:ss", new Date());

    saldo.setData(dataAtualStr.substring(0, dataAtualStr.indexOf(' ')));
    saldo.setHora(dataAtualStr.substring(dataAtualStr.indexOf(' ') + 1));

    if (cotacaoPontos != null) {
      // Calcula o valor do saldo em reais a partir da cotação de pontos da instituição
      if (saldo.getSaldoDisponivel() != null) {
        saldo.setSaldoDisponivelEmPontos(saldo.getSaldoDisponivel());
        saldo.setValorDoPonto(cotacaoPontos.getValorPonto());
        saldo.setValorConversao(cotacaoPontos.getValorConversao());
        saldo.setSaldoDisponivelEmReais(
            saldo
                .getSaldoDisponivelEmPontos()
                .divide(cotacaoPontos.getValorConversao(), 2, RoundingMode.HALF_UP));
      }
    }

    saldo.setConsultaRestrita(FALSE);

    return saldo;
  }

  public List<GetSaldoGrupoResponse> getSaldosGrupoPortador(SecurityUserPortador userPortador) {
    List<ContaPagamento> contas =
        obterContasDoPortador(userPortador).stream()
            .filter(
                conta ->
                    userPortador.getGrupoAcesso() == null
                        || STATUS_ATIVO_OU_ORIGEM_LIST.contains(conta.getIdStatusV2()))
            .collect(Collectors.toList());
    List<GetSaldoGrupoResponse> result = new ArrayList<>();
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    for (ContaPagamento conta : contas) {
      GetSaldoConta saldoConta = getSaldoConta(conta.getIdConta());
      GetSaldoGrupoResponse parIdContaSaldo =
          new GetSaldoGrupoResponse(conta.getIdConta(), saldoConta);
      String dataHora =
          parIdContaSaldo.getSaldo().getData() + " " + parIdContaSaldo.getSaldo().getHora();
      parIdContaSaldo.getSaldo().setDataLocalDateTime(LocalDateTime.parse(dataHora, formatter));
      result.add(parIdContaSaldo);
    }
    return result;
  }

  public BigDecimal getSaldoDisponivelPrePago(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();

    return getSaldoDisponivel(
        conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
        accountCode,
        moeda);
  }

  public BigDecimal getSaldoDisponivelPosPago(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();
    moeda = moeda + ",1" + moeda + ",2" + moeda;

    return getSaldoDisponivel(
        conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
        accountCode,
        moeda);
  }

  public BigDecimal getLimiteCredito(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    String moedaLimite = "2" + produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();

    return getSaldoDisponivel(
        conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
        accountCode,
        moedaLimite);
  }

  public ContaPagamento findContaTitular(Long idConta) {
    return contaPagamentoRepository.findContaTitular(idConta);
  }

  public Pessoa findPessoaTitularDaConta(Long idConta) {
    return contaPagamentoRepository.findPessoaTitularDaConta(idConta);
  }

  @Transactional
  public void alterarLimiteContaPos(AlterarLimiteContaPos model, SecurityUser user) {

    ContaPagamento conta = findByIdNotNull(model.getIdConta());

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == null
        || produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != CREDITO) {
      throw new GenericServiceException(
          "Esta conta não é pós-paga ou não possui opção para definição de limite!");
    }

    if (ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(
        produtoInstituicaoConfiguracao.getIdProdutoPlataforma())) {
      validarLimiteGlobal(model.getLimiteNovo(), user, model.getIdPontoRelacionamento());
    }

    String accountCode = getOrPrepareAccountCode(conta, produtoInstituicaoConfiguracao);

    // layer
    String moeda = produtoInstituicaoConfiguracao.getMoeda().getIdMoeda().toString();
    String moedaLimite = "2" + moeda;

    // consultar limite atual no JCARD
    BigDecimal limiteAtual =
        getSaldoDisponivel(
            conta.getHierarquiaPontoDeRelacionamento().getHierarquiaInstituicao().getJournal(),
            accountCode,
            moedaLimite);

    if (model.getLimiteNovo().equals(limiteAtual)) {
      throw new GenericServiceException("O limite escolhido é igual ao limite atual!");
    }

    BigDecimal resultadoLimiteNovo =
        (limiteAtual.subtract(model.getLimiteNovo())).multiply(new BigDecimal(NEGATIVO));

    // type
    Integer type = (resultadoLimiteNovo.signum() == NEGATIVO ? TYPE_DEBITO : TYPE_CREDITO);

    // amount
    BigDecimal amount =
        (resultadoLimiteNovo.signum() == NEGATIVO
            ? resultadoLimiteNovo.multiply(new BigDecimal(NEGATIVO))
            : resultadoLimiteNovo);

    // journal
    HierarquiaInstituicaoId id =
        new HierarquiaInstituicaoId(conta.getIdProcessadora(), conta.getIdInstituicao());
    HierarquiaInstituicao instituicao = instituicaoService.findById(id);
    String journal = instituicao.getJournal();

    // serviço do JCARD
    createAccountBalance(
        amount.doubleValue(), accountCode, type, Integer.parseInt(moedaLimite), journal);

    conta.setLimiteUnico(model.getLimiteNovo().doubleValue());
    save(conta);
  }

  private void createAccountBalance(
      Double amount, String accountCode, Integer type, Integer layer, String journalName) {
    CreateAccountBalance create =
        AccountBalanceService.prepareCreateAccountBalance(
            amount, accountCode, type, layer, journalName);
    JcardResponse response = accountBalanceService.createAccountBalance(create);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar alterar limite: " + response.getErrors());
    }
  }

  public void vincularPessoaAdicionalConta(
      Pessoa p, ContaPagamento conta, ContaPessoa contaPessoa) {
    getContaPagamentoFacade().vincularPessoaAdicionalConta(p, conta, contaPessoa);
  }

  public CredencialGerada createCredencialAdicional(
      Pessoa pessoa, ContaPagamento conta, BigDecimal limitePrioritario) {
    GerarCredencialRequest gerarCredencial = new GerarCredencialRequest();
    gerarCredencial.setIdUsuario(conta.getIdUsuarioInclusao());
    gerarCredencial.setIdPessoa(pessoa.getIdPessoa());
    gerarCredencial.setIdConta(conta.getIdConta());
    gerarCredencial.setAdicional(true);

    gerarCredencial.setVirtual(setVirtualCartaoBaseadoConfiguracaoProduto(conta));

    return getContaPagamentoFacade().gerarCredencialAdicional(gerarCredencial, limitePrioritario);
  }

  public List<AlterarAdicionalPessoa> buscarPessoasAdicionaisConta(Long idConta) {

    List<Pessoa> pessoasAdiconais = getContaPagamentoFacade().buscarPessoasAdicionaisConta(idConta);

    List<AlterarAdicionalPessoa> retorno = new ArrayList<AlterarAdicionalPessoa>();

    for (Pessoa tmp : pessoasAdiconais) {
      AlterarAdicionalPessoa target = new AlterarAdicionalPessoa();

      BeanUtils.copyProperties(tmp, target);
      if (tmp.getEnderecosPessoa() != null && !tmp.getEnderecosPessoa().isEmpty()) {
        EnderecoPessoa enderecoPessoa =
            tmp.getEnderecosPessoa().stream()
                .filter(enderecoPessoaTmp -> enderecoPessoaTmp.getStatus().equals(1))
                .findAny()
                .orElse(null);
        if (enderecoPessoa != null) {
          if (target.getCepRepresentanteLegal() == null
              || target.getCepRepresentanteLegal().isEmpty()) {
            target.setCepRepresentanteLegal(enderecoPessoa.getCep());
          }
          if (target.getBairroRepresentanteLegal() == null
              || target.getBairroRepresentanteLegal().isEmpty()) {
            target.setBairroRepresentanteLegal(enderecoPessoa.getBairro());
          }

          if (target.getLogradouroRepresentanteLegal() == null
              || target.getLogradouroRepresentanteLegal().isEmpty()) {
            target.setLogradouroRepresentanteLegal(enderecoPessoa.getLogradouro());
          }

          if (target.getCidadeRepresentanteLegal() == null
              || target.getCidadeRepresentanteLegal().isEmpty()) {
            target.setCidadeRepresentanteLegal(enderecoPessoa.getCidade());
          }

          if (target.getUfRepresentanteLegal() == null
              || target.getUfRepresentanteLegal().isEmpty()) {
            target.setUfRepresentanteLegal(enderecoPessoa.getUf());
          }

          if (target.getNumeroRepresentanteLegal() == null
              || target.getNumeroRepresentanteLegal().isEmpty()) {
            target.setNumeroRepresentanteLegal(enderecoPessoa.getNumero());
          }

          if (target.getComplementoRepresentanteLegal() == null
              || target.getComplementoRepresentanteLegal().isEmpty()) {
            target.setComplementoRepresentanteLegal(enderecoPessoa.getComplemento());
          }
        }
      }
      ContaPessoa contaPessoa =
          tmp.getContasPessoa().stream()
              .filter(cp -> cp.getContaPagamento().getIdConta().equals(idConta))
              .findAny()
              .orElse(null);

      target.setPercentualLimiteAdicional(
          contaPessoa != null ? contaPessoa.getPercentualLimiteAdicional() : null);

      target.setDataNascimento(tmp.getDataNascimento().toString());
      retorno.add(target);
    }

    return retorno;
  }

  @Transactional
  public PessoaAdicionalResponse gerarPessoaAndCartaoAdicionalByConta(
      CartaoAdicionalRequest adicional, Integer idUsuario) {

    ContaPagamento conta = findContaTitular(adicional.getIdConta());

    if (conta == null) {
      throw new GenericServiceException("Conta não encontrada!");
    }

    if (getContaPagamentoFacade()
        .isDocumentoCadastradoConta(adicional.getCpf(), adicional.getIdConta())) {
      throw new GenericServiceException(
          "Documento já cadastrado nesta conta: " + adicional.getCpf());
    }

    if (adicional.getPercentualLimiteAdicional() != null
        && BigDecimalUtils.is(adicional.getPercentualLimiteAdicional())
            .gt(ProdutoInstituicaoService.VALOR_MAXIMO_PERCENTUAL)) {
      throw new GenericServiceException(
          ProdutoInstituicaoService.MSG_ERRO_VALIDACAO_VALOR_PERCENTUAL_ADICIONAL);
    }
    Pessoa p = new Pessoa();
    p.setIdProcessadora(conta.getIdProcessadora());
    p.setIdInstituicao(conta.getIdInstituicao());
    p.setNomeCompleto(adicional.getNomeCompleto());
    p.setDocumento(adicional.getCpf());
    p.setTelefoneCelular(adicional.getTelefoneCelular());
    p.setDddTelefoneCelular(adicional.getDddTelefoneCelular());
    p.setEmail(adicional.getEmail());
    p.setNomeMaeRepresentanteLegal(adicional.getNomeMaeRepresentanteLegal());
    p.setCepRepresentanteLegal(adicional.getCepRepresentanteLegal());
    p.setLogradouroRepresentanteLegal(adicional.getLogradouroRepresentanteLegal());
    p.setNumeroRepresentanteLegal(adicional.getNumeroRepresentanteLegal());
    p.setComplementoRepresentanteLegal(adicional.getComplementoRepresentanteLegal());
    p.setBairroRepresentanteLegal(adicional.getBairroRepresentanteLegal());
    p.setCidadeRepresentanteLegal(adicional.getCidadeRepresentanteLegal());
    p.setUfRepresentanteLegal(adicional.getUfRepresentanteLegal());

    if (adicional.getDataNascimento() != null) {
      p.setDataNascimento(
          DateUtil.dateToLocalDateTime(
              DateUtil.parseDate("dd/MM/yyyy", adicional.getDataNascimento())));
    }

    p.setDataHoraInclusao(LocalDateTime.now());
    p.setNomeEmbossado(adicional.getNomeEmbossado());
    p.setIdTipoPessoa(PESSOA_FISICA);
    p.setDataInicioRelacionamento(LocalDateTime.now());
    p.setIdUsuarioInclusao(idUsuario);

    p = getContaPagamentoFacade().createPFAdicional(p);

    ContaPessoa contaPessoa = prepareContaPessoaAdicional(p, conta);
    contaPessoa.setPercentualLimiteAdicional(adicional.getPercentualLimiteAdicional());
    vincularPessoaAdicionalConta(p, conta, contaPessoa);
    createCredencialAdicional(p, conta, adicional.getPercentualLimiteAdicional());

    PessoaAdicionalResponse retorno = new PessoaAdicionalResponse();
    BeanUtils.copyProperties(p, retorno);
    retorno.setPercentualLimiteAdicional(adicional.getPercentualLimiteAdicional());

    return retorno;
  }

  @Transactional
  public void criarPessoaAndCartaoAdicionalByConta(
      CadastrarPessoaPDAFRequest model,
      SecurityUser user,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao,
      Integer idInstituicao,
      String nomeSetorFilial) {

    ContaPagamento conta =
        findByTipoDocAndDocAndProduto(
            model.getDocumento(),
            idProdutoInstituicao,
            ID_PROCESSADORA_ITS_PAY,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoDeRelacionamento,
            Constantes.PESSOA_JURIDICA);

    if (conta == null) {
      throw new GenericServiceException("Conta não encontrada!");
    }
    // Buscando/Salvando setor filial
    SetorFilial setorFilial = setorFilialService.saveByDescricao(nomeSetorFilial, user);

    List<PessoaAdicionalPDAFRequest> adicionais = new ArrayList<>();
    Map<String, Object> additionalProperties = model.getAdditionalProperties();
    for (int i = 1; i <= 3; i++) {
      String nomeKey = "nome_adc_" + i;
      String dataNascimentoKey = "dt_nascimento_adc_" + i;
      String cpfKey = "cpf_adc_" + i;
      String cargoKey = "cargo_adc_" + i;
      String telefoneKey = "telefone_adc_" + i;
      String emailKey = "email_adc_" + i;
      String nomeMaeKey = "nome_mae_adc_" + i;
      String cepKey = "cep_adc_" + i;
      String logradouroKey = "logradouro_adc_" + i;
      String numeroKey = "numero_adc_" + i;
      String complementoKey = "complemento_adc_" + i;
      String bairroKey = "bairro_adc_" + i;
      String cidadeKey = "cidade_adc_" + i;
      String ufKey = "uf_adc_" + i;
      if (additionalProperties.containsKey(cpfKey) && additionalProperties.containsKey(nomeKey)) {
        PessoaAdicionalPDAFRequest adicional = new PessoaAdicionalPDAFRequest();
        adicional.setNome((String) additionalProperties.get(nomeKey));
        adicional.setDataNascimentoFromMap(additionalProperties.get(dataNascimentoKey));
        adicional.setCpf((String) additionalProperties.get(cpfKey));
        adicional.setCargo((String) additionalProperties.get(cargoKey));
        adicional.setTelefone((String) additionalProperties.get(telefoneKey));
        adicional.setEmail((String) additionalProperties.get(emailKey));
        adicional.setNomeMae((String) additionalProperties.get(nomeMaeKey));
        adicional.setCep((String) additionalProperties.get(cepKey));
        adicional.setLogradouro((String) additionalProperties.get(logradouroKey));
        adicional.setNumero((String) additionalProperties.get(numeroKey));
        adicional.setComplemento((String) additionalProperties.get(complementoKey));
        adicional.setBairro((String) additionalProperties.get(bairroKey));
        adicional.setCidade((String) additionalProperties.get(cidadeKey));
        adicional.setUf((String) additionalProperties.get(ufKey));
        adicionais.add(adicional);
      }
    }

    for (PessoaAdicionalPDAFRequest adicional : adicionais) {
      if (getContaPagamentoFacade()
          .isDocumentoCadastradoConta(adicional.getCpf(), conta.getIdConta())) {
        throw new GenericServiceException(
            "Documento já cadastrado nesta conta: " + adicional.getCpf());
      }
      EnderecoPessoaRequest enderecoPessoaRequest = new EnderecoPessoaRequest();
      EnderecoPessoa enderecoPessoa = new EnderecoPessoa();
      Pessoa pessoa = new Pessoa();
      pessoa.setIdProcessadora(conta.getIdProcessadora());
      pessoa.setIdInstituicao(conta.getIdInstituicao());
      pessoa.setNomeCompleto(adicional.getNome());
      pessoa.setDocumento(adicional.getCpf());
      if (adicional.getTelefone() != null && !adicional.getTelefone().isEmpty()) {
        Integer ddd = Integer.parseInt(adicional.getTelefone().substring(0, 2));
        Integer celular = Integer.parseInt(adicional.getTelefone().substring(2));
        pessoa.setDddTelefoneCelular(ddd);
        pessoa.setTelefoneCelular(celular);
      }
      pessoa.setEmail(adicional.getEmail());
      pessoa.setNomeMae(adicional.getNomeMae());
      pessoa.setDataNascimento(DateUtil.dateToLocalDateTime(adicional.getDataNascimento()));
      pessoa.setIdSetorFilial(setorFilial.getIdSetorFilial());
      enderecoPessoaRequest.setCep(adicional.getCep());
      enderecoPessoaRequest.setLogradouro(adicional.getLogradouro());
      enderecoPessoaRequest.setCidade(adicional.getCidade());
      enderecoPessoaRequest.setComplemento(adicional.getComplemento());
      enderecoPessoaRequest.setBairro(adicional.getBairro());
      enderecoPessoaRequest.setNumero(adicional.getNumero());
      enderecoPessoaRequest.setUf(adicional.getUf());
      enderecoPessoaRequest.setIdUsuarioInclusao(user.getIdUsuario());
      pessoa.setIdUsuarioInclusao(user.getIdUsuario());
      pessoa.setDataHoraInclusao(LocalDateTime.now());
      pessoa.setNomeEmbossado(Abreviador.abreviarNome(adicional.getNome()));
      pessoa.setIdTipoPessoa(PESSOA_FISICA);
      pessoa.setDataInicioRelacionamento(LocalDateTime.now());
      pessoa = getContaPagamentoFacade().createPFAdicional(pessoa);
      ContaPessoa contaPessoaAdicional = prepareContaPessoaAdicional(pessoa, conta);
      vincularPessoaAdicionalConta(pessoa, conta, contaPessoaAdicional);
      createCredencialAdicional(pessoa, conta, null);
      enderecoPessoaRequest.setIdTipoEndereco(1);
      enderecoPessoaRequest.setIdPessoa(pessoa.getIdPessoa());
      prepareEndereco(enderecoPessoaRequest, enderecoPessoa, pessoa);
      enderecoPessoaService.cadastrarEnderecosPessoa(user.getIdUsuario(), enderecoPessoa);
    }
  }

  @Transactional
  public void criarPessoaAndCartaoAdicionalByConta(
      CadastrarPessoaPDAFRequest model,
      SecurityUser user,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao) {

    ContaPessoa contaPessoa =
        pessoaService.getContaPessoaPDAFByDocumentoAndHierarquia(
            model.getDocumento(),
            idRegional,
            idFilial,
            idPontoDeRelacionamento,
            idProdutoInstituicao);
    ContaPagamento conta = findContaTitular(contaPessoa.getIdConta());

    if (conta == null) {
      throw new GenericServiceException("Conta não encontrada!");
    }

    List<PessoaAdicionalPDAFRequest> adicionais = new ArrayList<>();
    Map<String, Object> additionalProperties = model.getAdditionalProperties();
    for (int i = 1; i <= 3; i++) {
      String nomeKey = "nome_adc_" + i;
      String dataNascimentoKey = "dt_nascimento_adc_" + i;
      String cpfKey = "cpf_adc_" + i;
      String cargoKey = "cargo_adc_" + i;
      String telefoneKey = "telefone_adc_" + i;
      String emailKey = "email_adc_" + i;
      String nomeMaeKey = "nome_mae_adc_" + i;
      String cepKey = "cep_adc_" + i;
      String logradouroKey = "logradouro_adc_" + i;
      String numeroKey = "numero_adc_" + i;
      String complementoKey = "complemento_adc_" + i;
      String bairroKey = "bairro_adc_" + i;
      String cidadeKey = "cidade_adc_" + i;
      String ufKey = "uf_adc_" + i;
      if (additionalProperties.containsKey(cpfKey) && additionalProperties.containsKey(nomeKey)) {
        PessoaAdicionalPDAFRequest adicional = new PessoaAdicionalPDAFRequest();
        adicional.setNome((String) additionalProperties.get(nomeKey));
        adicional.setDataNascimentoFromMap(additionalProperties.get(dataNascimentoKey));
        adicional.setCpf((String) additionalProperties.get(cpfKey));
        adicional.setCargo((String) additionalProperties.get(cargoKey));
        adicional.setTelefone((String) additionalProperties.get(telefoneKey));
        adicional.setEmail((String) additionalProperties.get(emailKey));
        adicional.setNomeMae((String) additionalProperties.get(nomeMaeKey));
        adicional.setCep((String) additionalProperties.get(cepKey));
        adicional.setLogradouro((String) additionalProperties.get(logradouroKey));
        adicional.setNumero((String) additionalProperties.get(numeroKey));
        adicional.setComplemento((String) additionalProperties.get(complementoKey));
        adicional.setBairro((String) additionalProperties.get(bairroKey));
        adicional.setCidade((String) additionalProperties.get(cidadeKey));
        adicional.setUf((String) additionalProperties.get(ufKey));
        adicionais.add(adicional);
      }
    }

    for (PessoaAdicionalPDAFRequest adicional : adicionais) {
      if (getContaPagamentoFacade()
          .isDocumentoCadastradoConta(adicional.getCpf(), contaPessoa.getIdConta())) {
        throw new GenericServiceException(
            "Documento já cadastrado nesta conta: " + adicional.getCpf());
      }
      EnderecoPessoaRequest enderecoPessoaRequest = new EnderecoPessoaRequest();
      EnderecoPessoa enderecoPessoa = new EnderecoPessoa();
      Pessoa pessoa = new Pessoa();
      pessoa.setIdProcessadora(conta.getIdProcessadora());
      pessoa.setIdInstituicao(conta.getIdInstituicao());
      pessoa.setNomeCompleto(adicional.getNome());
      pessoa.setDocumento(adicional.getCpf());
      pessoa.setTelefoneCelular(Integer.valueOf(adicional.getTelefone()));
      pessoa.setDddTelefoneCelular(Integer.valueOf(adicional.getDdd()));
      pessoa.setEmail(adicional.getEmail());
      pessoa.setNomeMae(adicional.getNomeMae());
      pessoa.setDataNascimento(DateUtil.dateToLocalDateTime(adicional.getDataNascimento()));
      enderecoPessoaRequest.setCep(adicional.getCep());
      enderecoPessoaRequest.setLogradouro(adicional.getLogradouro());
      enderecoPessoaRequest.setCidade(adicional.getCidade());
      enderecoPessoaRequest.setComplemento(adicional.getComplemento());
      enderecoPessoaRequest.setBairro(adicional.getBairro());
      enderecoPessoaRequest.setNumero(adicional.getNumero());
      enderecoPessoaRequest.setUf(adicional.getUf());
      enderecoPessoaRequest.setIdUsuarioInclusao(user.getIdUsuario());
      pessoa.setIdUsuarioInclusao(user.getIdUsuario());
      pessoa.setDataHoraInclusao(LocalDateTime.now());
      pessoa.setNomeEmbossado(Abreviador.abreviarNome(adicional.getNome()));
      pessoa.setIdTipoPessoa(PESSOA_FISICA);
      pessoa.setDataInicioRelacionamento(LocalDateTime.now());
      pessoa = getContaPagamentoFacade().createPFAdicional(pessoa);
      ContaPessoa contaPessoaAdicional = prepareContaPessoaAdicional(pessoa, conta);
      vincularPessoaAdicionalConta(pessoa, conta, contaPessoaAdicional);
      createCredencialAdicional(pessoa, conta, null);
      enderecoPessoaRequest.setIdTipoEndereco(1);
      enderecoPessoaRequest.setIdPessoa(pessoa.getIdPessoa());
      prepareEndereco(enderecoPessoaRequest, enderecoPessoa, pessoa);
      enderecoPessoaService.cadastrarEnderecosPessoa(user.getIdUsuario(), enderecoPessoa);
    }
  }

  public ContaPessoa prepareContaPessoaAdicional(Pessoa pessoa, ContaPagamento conta) {
    return ContaPagamentoUtil.prepareContaPessoa(pessoa, conta, ADICIONAL);
  }

  public List<ExtratoTransRejeitada> getExtratoTransacoesRejeitadasByMes(
      Long idConta, Integer mes, Integer ano, SecurityUser usuario) {

    consultaRestritaService.checaPrivilegio(idConta, usuario);

    Calendar dataInicio = Calendar.getInstance();
    dataInicio.set(Calendar.DAY_OF_MONTH, PRIMEIRO_DIA);
    dataInicio.set(Calendar.MONTH, mes);
    dataInicio.set(Calendar.YEAR, ano);
    dataInicio.set(Calendar.HOUR_OF_DAY, ZERO_HORA_MINUTO_SEGUNDO);
    dataInicio.set(Calendar.MINUTE, ZERO_HORA_MINUTO_SEGUNDO);
    dataInicio.set(Calendar.SECOND, ZERO_HORA_MINUTO_SEGUNDO);

    Calendar dataFim = Calendar.getInstance();
    dataFim.set(Calendar.MONTH, mes);
    dataFim.set(Calendar.YEAR, ano);
    dataFim.set(Calendar.DAY_OF_MONTH, dataFim.getActualMaximum(Calendar.DAY_OF_MONTH));
    dataFim.set(Calendar.HOUR_OF_DAY, ULTIMA_HORA_DIA);
    dataFim.set(Calendar.MINUTE, ULTIMO_MINUTO_SEGUNDO_DIA);
    dataFim.set(Calendar.SECOND, ULTIMO_MINUTO_SEGUNDO_DIA);

    return contaPagamentoRepository.findExtratoTransacoesRejeitadasPorPeriodo(
        dataInicio.getTime(), dataFim.getTime(), idConta);
  }

  public List<PortadorCartaoPreEmitidoTO> findCredencialParaPreEmissao(ArquivoCvRequest model) {

    return contaPagamentoRepository.existeContaPagamentoParaPreEmissaoExterna(
        model.getIdProdutoInstituicao(),
        model.getIdProcessadora(),
        model.getIdInstituicao(),
        model.getIdRegional(),
        model.getIdFilial(),
        model.getIdPontoDeRelacionamento());
  }

  public Integer countCredencialParaPreEmissao(ArquivoCvRequest model) {

    Integer qtd =
        contaPagamentoRepository.countContaPagamentoParaPreEmissaoExterna(
            model.getIdProdutoInstituicao(),
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getIdRegional(),
            model.getIdFilial(),
            model.getIdPontoDeRelacionamento());

    return qtd;
  }

  public List<ContaPagamento> findContasBuscadas(Long idPessoa, Integer idProdutoInstituicao) {
    List<ContaPagamento> contasBuscadas =
        contaPagamentoRepository.findByContasPessoaIdPessoaAndIdProdutoInstituicao(
            idPessoa, idProdutoInstituicao);
    return contasBuscadas;
  }

  public Integer getCodTransacaoByProdPlatAndProdInstituidorConta(
      Integer idProdutoPlataforma,
      Integer funcaoProdInstituidor,
      Integer quantidadeParcelas,
      Boolean estorno,
      Integer tipoTransacao) {

    if (tipoTransacao != null
        && tipoTransacao.equals(COMPRA)
        && idProdutoPlataforma != null
        && idProdutoPlataforma.equals(PROD_PLAT_CREDITO)
        && quantidadeParcelas == null) {
      throw new GenericServiceException(
          "Quantidade de parcelas não encontrada! O produto desta conta é de crédito e necessita do número de parcelas para realizar o lançamento.");
    }
    return FunctionCodeProdutoPlataformaEnum.getCodigoByProdPlatAndFuncao(
        idProdutoPlataforma, funcaoProdInstituidor, estorno, quantidadeParcelas, tipoTransacao);
  }

  public List<BuscaContaPagamentoRetorno> findContaPagamentoByFiltros(
      String busca, Boolean isBuscaNroCartao, SecurityUser user, Integer first, Integer max) {
    return (List<BuscaContaPagamentoRetorno>)
        contaPagamentoRepository.findOrCountContaPagamento(
            busca, isBuscaNroCartao, user, first, max, List.class);
  }

  public Long countContaPagamentoByFiltros(
      String busca, Boolean isBuscaNroCartao, SecurityUser user) {
    return contaPagamentoRepository.findOrCountContaPagamento(
        busca, isBuscaNroCartao, user, null, null, Long.class);
  }

  public List<BuscaContaPagamentoRetorno> filtroByContaPagamento(
      String res,
      Boolean isBuscaNroCartao,
      SecurityUser user,
      Integer first,
      Integer max,
      Integer idInstituicao) {

    return (List<BuscaContaPagamentoRetorno>)
        contaPagamentoRepository.findOrCountFiltroContaPagamento(
            res, isBuscaNroCartao, user, first, max, idInstituicao, List.class);
  }

  public Long countContaPagamentoFiltros(
      String busca, Boolean isBuscaNroCartao, SecurityUser user, Integer idInstituicao) {
    return contaPagamentoRepository.findOrCountFiltroContaPagamento(
        busca, isBuscaNroCartao, user, null, null, idInstituicao, Long.class);
  }

  public GetSaldoConta calcularValorTransacaoMaximo(
      SecurityUser user,
      Long idConta,
      HierarquiaInstituicao inst,
      ProdutoInstituicaoConfiguracaoCredito prodInstConfigCredito) {
    BigDecimal value = new BigDecimal(0);
    GetSaldoConta result = new GetSaldoConta();
    GetSaldoConta saldo = new GetSaldoConta();

    validaPessoa(idConta, user.getIdProcessadora(), inst.getIdInstituicao());

    GetSaldoConta saldoConta = getSaldoConta(idConta);

    saldo.setSaldoDisponivel(saldoConta.getSaldoDisponivel());
    saldo.setLimiteCredito(saldoConta.getLimiteCredito());

    // verifica se a instituição tem valor de primeira compra
    if (inst.getValorPrimeiraCompra() != null && inst.getValorPrimeiraCompra().intValue() > 0) {

      value = inst.getValorPrimeiraCompra();
      // verifica se a instituição tem percentual de primeira compra
    } else if (inst.getPercLimitePrimeiraCompra() != null) {

      BigDecimal cem = new BigDecimal(100);
      value = (inst.getPercLimitePrimeiraCompra().multiply(saldo.getLimiteCredito()).divide(cem));
    }
    // verifica qual o menor valor entre o limite disponível e o valor
    // encontrado na
    // verificação anterior
    if (saldo.getSaldoDisponivel().intValue() <= value.intValue()) {

      result.setSaldoDisponivel(saldo.getSaldoDisponivel());

    } else if (saldo.getSaldoDisponivel().intValue() >= value.intValue()) {

      result.setSaldoDisponivel(value);
    }
    LocalDateTime dt = dataStringComUltimaHora(LocalDateTime.now(), prodInstConfigCredito);
    String dataFormat = DateUtil.dateFormat("dd/MM/yyyy", DateUtil.localDateTimeToDate(dt));
    result.setData(dataFormat);
    result.setDataLocalDateTime(dt);

    return result;
  }

  private LocalDateTime dataStringComUltimaHora(
      LocalDateTime data, ProdutoInstituicaoConfiguracaoCredito prodInstConfigCredito) {
    Integer diasValidade = prodInstConfigCredito.getDiasValidadePrimeiraCompra();

    LocalDateTime dataAtualizada = data.plusDays(diasValidade.longValue());

    LocalDateTime dataFinal2359 = dataAtualizada.withHour(ConstantesB2B.MAX_HORA);
    dataFinal2359 = dataFinal2359.withMinute(ConstantesB2B.MAX_MINUTOS);
    dataFinal2359 = dataFinal2359.withSecond(ConstantesB2B.MAX_SEGUNDOS);

    return dataFinal2359;
  }

  private void validaPessoa(Long idConta, Integer idProcessadora, Integer idInstituicao) {
    ContaPagamento conta = findByIdNotNull(idConta);
    if (!conta.getIdProcessadora().equals(idProcessadora)
        || !conta.getIdInstituicao().equals(idInstituicao)) {
      throw new GenericServiceException(
          "O portador não pertence a hierarquia da instituição: " + idInstituicao);
    }
  }

  public TermoAdesaoContaVO getInfoTermoAdesao(Long idConta) {
    return contaPagamentoRepository.getInfoTermoAdesao(idConta);
  }

  public List<CredencialResumida> getCredenciaisContaToLancamento(Long idConta) {

    ContaPagamento conta = findByIdNotNull(idConta);

    if (conta == null) {
      throw new GenericServiceException("Não foi possível encontrar a conta: " + idConta);
    }

    List<CredencialResumida> dadosCredenciais = new ArrayList<>();
    List<Credencial> credenciais = getContaPagamentoFacade().findByIdConta(conta.getIdConta());

    for (Credencial credencial : credenciais) {

      if (credencial.getTipoStatusV2().getTipoGrupoStatus().getAceitarAjuste()) {
        CredencialResumida dadosCredencial = new CredencialResumida();
        BeanUtils.copyProperties(credencial, dadosCredencial);

        String ultimos4 =
            Strings.padStart(
                credencial.getUltimos4Digitos().toString(), QTD_DIG_ULTIMOS_4, CHAR_ZERO);

        dadosCredencial.setNumeroCredencial(
            CredencialService.getCredencialMascarada(
                credencial.getBin6().toString() + ultimos4,
                Constantes.PADRAO_CREDENCIAL,
                Constantes.MASK_2_PARTES_COMPLETA));

        dadosCredenciais.add(dadosCredencial);
      }
    }

    Collections.sort(dadosCredenciais);
    return dadosCredenciais;
  }

  public void createImgDocumentoConta(
      InputStream inputStream,
      String originalFilename,
      Long idConta,
      Integer idTipoDocumento,
      Integer idUsuario,
      Long idPortadorLogin) {
    ContaPagamento conta = contaPagamentoRepository.findOneByIdConta(idConta);

    if (conta == null) {
      throw new GenericServiceException(("A proposta não foi encontrada!"));
    }
    documentoContaService.createImgDocumentoConta(
        inputStream, originalFilename, conta, idTipoDocumento, idPortadorLogin, idUsuario);
  }

  public List<DocumentoConta> getDocumentosByConta(Long idConta) {
    return documentoContaService.getDocumentosByConta(idConta);
  }

  public InputStream getImgDocumentoConta(Long idDocumentoConta) {
    return documentoContaService.getImgDocumentoConta(idDocumentoConta);
  }

  public List<ContaPagamento> findBydocAndProdutoAndHierarquiaAndTipoJuridicaComVoucherPapel(
      String doc,
      Integer idProdutoInstituicao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contaPagamentoRepository.findBydocAndProdutoAndHierarquiaAndTipoJuridicaComVoucherPapel(
        doc,
        idProdutoInstituicao,
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoDeRelacionamento);
  }

  public List<TransacoesNaoApresentadas> findTransacoesNaoApresentadasByMes(
      Long idConta, Integer mes, Integer ano) {

    Calendar dataInicio = Calendar.getInstance();
    dataInicio.set(Calendar.DAY_OF_MONTH, PRIMEIRO_DIA);
    dataInicio.set(Calendar.MONTH, mes);
    dataInicio.set(Calendar.YEAR, ano);
    dataInicio.set(Calendar.HOUR_OF_DAY, ZERO_HORA_MINUTO_SEGUNDO);
    dataInicio.set(Calendar.MINUTE, ZERO_HORA_MINUTO_SEGUNDO);
    dataInicio.set(Calendar.SECOND, ZERO_HORA_MINUTO_SEGUNDO);

    Calendar dataFim = Calendar.getInstance();
    dataFim.set(Calendar.MONTH, mes);
    dataFim.set(Calendar.YEAR, ano);
    dataFim.set(Calendar.DAY_OF_MONTH, dataFim.getActualMaximum(Calendar.DAY_OF_MONTH));
    dataFim.set(Calendar.HOUR_OF_DAY, ULTIMA_HORA_DIA);
    dataFim.set(Calendar.MINUTE, ULTIMO_MINUTO_SEGUNDO_DIA);
    dataFim.set(Calendar.SECOND, ULTIMO_MINUTO_SEGUNDO_DIA);

    return contaPagamentoRepository.findExtratoTransacoesNaoApresentadasByMes(
        dataInicio.getTime(), dataFim.getTime(), idConta);
  }

  public List<TipoStatus> listaStatusIdConta(Long idConta) {

    ContaPagamento conta = findById(idConta);
    List<MapaStatus> statusMap =
        mapaStatusService.findMapaStatusDisponiveis(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            conta.getIdProdutoInstituicao(),
            APLICABILIDADE_CONTA,
            Constantes.AUTOR_SISTEMA,
            conta.getIdStatusV2());

    List<TipoStatus> listStatus = monstarListStatus(statusMap);
    return listStatus;
  }

  /**
   * Constroi lista de tipo status aparti de uma lista de mapaStatus
   *
   * @param statusMap
   * @return
   */
  private List<TipoStatus> monstarListStatus(List<MapaStatus> statusMap) {
    List<TipoStatus> listStatus = new ArrayList<>();
    for (MapaStatus mapaStatus : statusMap) {
      TipoStatus tipoStatusDestino = mapaStatus.getTipoStatusDestino();
      listStatus.add(tipoStatusDestino);
    }
    ordenaListaTipoStatus(listStatus);

    return listStatus;
  }

  /**
   * Recebe uma lista de status e ordena por id status
   *
   * @param listStatus
   */
  private void ordenaListaTipoStatus(List<TipoStatus> listStatus) {
    StatusCredencialComparator comparator = new StatusCredencialComparator();
    Collections.sort(listStatus, comparator);
  }

  /**
   * Altera um estatus de uma conta, caso o mapa de status da conta tenha um codTarifa e feito
   * integração com o jcard para fazer a cobrança
   *
   * @param idConta
   * @param statusDestino
   * @param idUsuario
   * @param ipOrigem
   * @return
   */
  public Boolean alterarEstadoConta(
      Long idConta, Integer statusDestino, Integer idUsuario, String ipOrigem) {
    ContaPagamento contaPagamento = findById(idConta);

    // Verificando se a conta existe
    if (contaPagamento == null) {
      throw new GenericServiceException("A conta informada não foi encontrada: " + idConta);
    }

    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdInstituicao(),
            contaPagamento.getIdRegional(),
            contaPagamento.getIdFilial(),
            contaPagamento.getIdPontoDeRelacionamento());

    contaPagamento.setHierarquiaPontoDeRelacionamento(
        hierarquiaPontoRelacionamentoService.findById(id));

    Integer statusOrigem = contaPagamento.getIdStatusV2();

    TipoStatus tipoStatusDestino = tipoStatusService.findById(statusDestino);

    if (Constantes.TIPO_STATUS_DESBLOQUEADO.equals(
            tipoStatusDestino.getTipoGrupoStatus().getIdGrupoStatus())
        && !contaPagamento.getTipoStatusV2().getTipoGrupoStatus().getPermiteDesbloquear()) {
      throw new GenericServiceException("A conta não pode ser desbloqueada: " + idConta);
    }

    MapaStatus mapaStatus =
        mapaStatusService.findOneMapaStatus(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdInstituicao(),
            contaPagamento.getIdProdutoInstituicao(),
            APLICABILIDADE_CONTA,
            getAutorAlteracao(idUsuario),
            statusOrigem,
            statusDestino);

    // Informar ao jcard
    if (mapaStatus != null) {
      if (mapaStatus.getStatusDestinoJcard() != null) {
        getContaPagamentoFacade()
            .alterarStatusConta(contaPagamento, mapaStatus.getStatusDestinoJcard());
      }

      if (mapaStatus.getCodTarifa() != null) {
        cobrarTarifaJcard(contaPagamento, idUsuario, ipOrigem, mapaStatus.getCodTarifa());
      }
    }

    contaPagamento.setDataHoraStatusConta(LocalDateTime.now());
    contaPagamento.setIdStatusV2(statusDestino);
    contaPagamento.setIdUsuarioManutencao(idUsuario);
    if (Constantes.ID_PRODUCAO_INSTITUICAO_CELER.equals(contaPagamento.getIdInstituicao())) {
      save(contaPagamento, true, statusOrigem);
    } else {
      save(contaPagamento);
    }

    return true;
  }

  private boolean isContaInadimplente(Long idConta) {
    ContaPagamentoFatura contaPagamentoFatura = contaPagamentoFaturaService.findByIdConta(idConta);

    return contaPagamentoFatura.getDataInadimplencia() != null;
  }

  /**
   * faz a integraçao com o jcard para fazer cobrança das tarifa
   *
   * @param contaPagamento
   * @param idUsuario
   * @param ipOrigem
   * @param codTarifa
   */
  private void cobrarTarifaJcard(
      ContaPagamento contaPagamento, Integer idUsuario, String ipOrigem, Integer codTarifa) {
    BigDecimal valorTarifa = null;
    try {
      Integer idPerfilTarifario =
          perfilTarifarioService.buscarIdPerfilTarifario(contaPagamento.getIdConta());
      if (idPerfilTarifario != null)
        valorTarifa = perfilTarifarioService.buscarTarifaPorId(idPerfilTarifario, codTarifa);
    } catch (InvalidRequestException e) {

    }

    if (valorTarifa != null) {
      CadastroLancamentoManual lancamento = new CadastroLancamentoManual();
      lancamento.setCodTransacao(codTarifa);
      lancamento.setIdConta(contaPagamento.getIdConta());
      lancamento.setTextoExtrato("TARIFA DE REATIVAÇÃO DE CONTA");
      lancamento.setValor(BigDecimal.ZERO);
      lancamento.setSinal(Constantes.DEBITADO);
      try {
        JcardResponse jcard =
            getContaPagamentoFacade().salvarLancamento(lancamento, idUsuario, ipOrigem, false);

        if (!jcard.getSuccess()) {
          throw new GenericServiceException(
              "Erro ao realizar a cobrança de tarifa de reativação de conta.");
        }
      } catch (JcardServiceException e) {
        if (e.getMensagem().contains(ERRO_SALDO_INSUFICIENTE)) {
          throw new GenericServiceException(
              "Não foi possível cobrar a tarifa de reativação da conta. Motivo: Saldo insuficiente! ");
        }
        throw new GenericServiceException(
            "Erro ao realizar a cobrança de tarifa de reativação de conta.");
      }
    }
  }

  public Long countSMSsByConta(Long idConta, SmsContaByFiltersRequest filter) {
    Date dataInicial =
        filter.getDataHoraInicio() == null
            ? DateUtil.diminuirDias(new Date(), 30)
            : DateUtil.parseDate("dd/MM/yyyy", filter.getDataHoraInicio());
    Date dataFim =
        filter.getDataHoraFim() == null
            ? new Date()
            : DateUtil.parseDate("dd/MM/yyyy", filter.getDataHoraFim());

    return contaPagamentoRepository.findOrCountSMSByConta(
        idConta, null, null, Long.class, dataInicial, dataFim);
  }

  public List<LogSMSContaResponse> getSMSsByConta(
      Long idConta, Integer first, Integer max, SmsContaByFiltersRequest filter) {

    Date dataInicial =
        filter.getDataHoraInicio() == null
            ? DateUtil.diminuirDias(new Date(), 30)
            : DateUtil.parseDate("dd/MM/yyyy", filter.getDataHoraInicio());
    Date dataFim =
        filter.getDataHoraFim() == null
            ? new Date()
            : DateUtil.parseDate("dd/MM/yyyy", filter.getDataHoraFim());
    return contaPagamentoRepository.findOrCountSMSByConta(
        idConta, first, max, List.class, dataInicial, dataFim);
  }

  public ContaPagamento buscarPorContaId(Long idConta) {
    return contaPagamentoRepository.findByIdConta(idConta);
  }

  public List<ContasCredenciaisReplicaveisReponse> getContasECredenciaisReplicaveis() {

    return contaPagamentoRepository.getContasECredenciaisResumido();
  }

  public List<ContasCredenciaisReplicaveisReponse> getTodasEmpresasReplicaveisBank10() {

    return contaPagamentoRepository.getTodasEmpresasReplicaveisBank10();
  }

  public List<ContaPagamento> findByIdContaIn(Collection<Long> idContas) {
    return contaPagamentoRepository.findByIdContaIn(idContas);
  }

  public List<ContaPagamento> obterContasDoPortador(PortadorLogin userPortador) {
    List<Long> idContasPortador = ObjectUtil.numberListToLong(userPortador.getContasPortador());
    List<ContaPagamento> contasPortador;
    if (idContasPortador != null && !idContasPortador.isEmpty()) {
      contasPortador = findByIdContaIn(idContasPortador);
    } else {
      contasPortador =
          portadorLoginContaService.buscarContasPagamentoAssociadosAoIdLogin(
              userPortador.getIdLogin());
    }
    return contasPortador;
  }

  public List<ContaPagamento> obterContasDoCorporativoPortador(CorporativoLogin corporativoLogin) {
    CorporativoResponsavel responsavel =
        this.corporativoService.findResponsavelAtivoByDocumento(
            corporativoLogin.getResponsavel().getDocumento());
    List<CorporativoResponsavelCredencial> responsavelCredenciais =
        responsavel.getCorporativoResponsavelCredencial();
    List<CorporativoResponsavelCredencial> responsavelCredenciaisAtivo =
        responsavelCredenciais.stream()
            .filter(x -> x.getDtHrFim() == null)
            .collect(Collectors.toList());
    List<ContaPagamento> contas = new ArrayList<>();
    if (responsavelCredenciaisAtivo.isEmpty()) {
      return contas;
    }
    for (CorporativoResponsavelCredencial responsavelCredencial : responsavelCredenciaisAtivo) {
      Credencial credencial =
          this.credencialService.findByIdCredencial(responsavelCredencial.getIdCredencial());
      ContaPagamento contaPagamento = this.findByIdConta(credencial.getIdConta());
      contas.add(contaPagamento);
    }
    return contas;
  }

  public List<Long> obterIdContasDoPortador(SecurityUserPortador userPortador) {
    List<Long> idContasPortador = ObjectUtil.numberListToLong(userPortador.getContasPortador());
    if (idContasPortador != null && !idContasPortador.isEmpty()) {
      return idContasPortador;
    } else {
      return portadorLoginContaService.buscarIdsContasPagamentoAssociadosAoLogin(
          userPortador.getIdLogin());
    }
  }

  public ContaPagamento validaEBuscaContaPeloRequestEPortador(
      Long idContaRequest, SecurityUserPortador userPortador) {
    List<ContaPagamento> contasPortador = obterContasDoPortador(userPortador);
    if (contasPortador == null || contasPortador.isEmpty()) {
      throw new AccessDeniedException(ConstantesErro.PTL_LOGADO_NAO_POSSUI_CONTAS.getMensagem());
    }

    return contasPortador.stream()
        .filter(c -> c.getIdConta().equals(idContaRequest))
        .findFirst()
        .orElseThrow(
            () ->
                new AccessDeniedException(
                    PTL_CONTA_NAO_PERTENCE_AO_PORTADOR.format(idContaRequest)));
  }

  public ContaPagamento buscarValidarContaPeloRequestECorporativo(
      Long idContaRequest, SecurityUserCorporativo userCorporativo) {
    List<ContaPagamento> contasPortador = obterContasDoCorporativoPortador(userCorporativo);
    if (contasPortador == null || contasPortador.isEmpty()) {
      throw new AccessDeniedException(ConstantesErro.PTL_LOGADO_NAO_POSSUI_CONTAS.getMensagem());
    }

    return contasPortador.stream()
        .filter(c -> c.getIdConta().equals(idContaRequest))
        .findFirst()
        .orElseThrow(
            () ->
                new AccessDeniedException(
                    PTL_CONTA_NAO_PERTENCE_AO_PORTADOR.format(idContaRequest)));
  }

  public void validaIdContaPeloRequestEPortador(
      Long idContaRequest, SecurityUserPortador userPortador) {
    List<Long> idContasPortador = obterIdContasDoPortador(userPortador);
    if (idContasPortador == null || idContasPortador.isEmpty()) {
      throw new AccessDeniedException(ConstantesErro.PTL_LOGADO_NAO_POSSUI_CONTAS.getMensagem());
    }

    if (!idContasPortador.contains(idContaRequest)) {
      throw new AccessDeniedException(PTL_CONTA_NAO_PERTENCE_AO_PORTADOR.format(idContaRequest));
    }
  }

  public void validaListContaPeloRequestEPortador(
      List<ContaPagamento> contasRequest, SecurityUserPortador userPortador) {
    List<Long> idContasPortador = obterIdContasDoPortador(userPortador);
    if (idContasPortador == null || idContasPortador.isEmpty()) {
      throw new AccessDeniedException(ConstantesErro.PTL_LOGADO_NAO_POSSUI_CONTAS.getMensagem());
    }

    List<Long> idContasRequest =
        contasRequest.stream().map(ContaPagamento::getIdConta).collect(Collectors.toList());
    boolean contaEncontrada = false;
    for (Long idContaPortador : idContasPortador) {
      if (idContasRequest.contains(idContaPortador)) {
        contaEncontrada = true;
        break;
      }
    }

    if (!contaEncontrada) {
      throw new AccessDeniedException(
          PTL_NENHUMA_CONTA_PERTENCE_AO_PORTADOR.format(idContasRequest));
    }
  }

  @SuppressWarnings("unnused")
  public void validaListIdContaPeloRequestEPortador(
      List<Long> idContasRequest, SecurityUserPortador userPortador) {
    List<Long> idContasPortador = obterIdContasDoPortador(userPortador);
    if (idContasPortador == null || idContasPortador.isEmpty()) {
      throw new AccessDeniedException(ConstantesErro.PTL_LOGADO_NAO_POSSUI_CONTAS.getMensagem());
    }

    boolean contaEncontrada = false;
    for (Long idContaPortador : idContasPortador) {
      if (idContasRequest.contains(idContaPortador)) {
        contaEncontrada = true;
        break;
      }
    }

    if (!contaEncontrada) {
      throw new AccessDeniedException(
          PTL_NENHUMA_CONTA_PERTENCE_AO_PORTADOR.format(idContasRequest));
    }
  }

  public List<ListarContaPagamentoGrupoResponse> obterContasGrupo(
      SecurityUserPortador userPortador) {
    List<ContaPagamento> contas =
        obterContasDoPortador(userPortador).stream()
            .filter(
                conta ->
                    userPortador.getGrupoAcesso() == null
                        || STATUS_ATIVO_OU_ORIGEM_LIST.contains(conta.getIdStatusV2()))
            .collect(Collectors.toList());

    List<ListarContaPagamentoGrupoResponse> result = new ArrayList<>();
    for (ContaPagamento conta : contas) {
      List<ProdutoMcc> listaProdutoMccs =
          produtoMccService.getProdutoMccByIdProduto(conta.getIdProdutoInstituicao());
      List<Integer> listaCodsMccs =
          listaProdutoMccs.stream()
              .map(produtoMcc -> produtoMcc.getCodigoMcc().getCodMcc())
              .collect(Collectors.toList());
      result.add(
          new ListarContaPagamentoGrupoResponse(
              conta.getIdConta(),
              conta.getProdutoInstituicao().getDescProdInstituicao(),
              listaCodsMccs));
    }
    return result;
  }

  public void validaContaAtiva(PortadorLogin portadorLogin) {

    Integer tipoPessoa =
        (portadorLogin.getDocumentoAcesso() == null
                && portadorLogin.getCpf().length() == TAMANHO_CPF)
            ? TIPO_PESSOA_FISICA
            : TIPO_PESSOA_JURIDICA;

    List<ContaPagamento> contas =
        findByCpfAndTipoStatus_idGrupoStatus(
            portadorLogin.getCpf(),
            portadorLogin.getIdProcessadora(),
            portadorLogin.getIdInstituicao(),
            tipoPessoa);

    if (contas.isEmpty()) {
      throw new GenericServiceException("Conta inexistente ou inativa.");
    }
  }

  public void validaContaAtivaCorporativo(CorporativoLogin corporativoLogin) {

    List<ContaPagamento> contas =
        findByCpfAndTipoStatus_idGrupoStatus(
            corporativoLogin.getResponsavel().getDocumento(),
            corporativoLogin.getIdProcessadora(),
            corporativoLogin.getIdInstituicao(),
            TIPO_PESSOA_JURIDICA);

    if (contas.isEmpty()) {
      throw new GenericServiceException("Conta inexistente ou inativa.");
    }
  }

  private ContaPagamento save(ContaPagamento conta, Boolean informarCeler, Integer statusOrigem) {
    try {

      if (informarCeler) {
        TipoStatus tipoStatusOrigem = tipoStatusService.findById(statusOrigem);
        TipoStatus tipoStatusDestino = tipoStatusService.findById(conta.getIdStatusV2());

        LogNotificacaoAlterStatusContaCeler notifCeler = new LogNotificacaoAlterStatusContaCeler();
        notifCeler.setDataAtualizacao(DateUtil.localDateTimeToDate(conta.getDataHoraStatusConta()));
        notifCeler.setDataHoraInclusao(new Date());

        if (tipoStatusOrigem != null && tipoStatusOrigem.getIdGrupoStatus() != null) {
          notifCeler.setGrupoStatusOrigem(tipoStatusOrigem.getIdGrupoStatus());
        }
        if (tipoStatusDestino != null && tipoStatusDestino.getIdGrupoStatus() != null) {
          notifCeler.setNovoGrupoStatusConta(tipoStatusDestino.getIdGrupoStatus());
        }
        notifCeler.setStatusContaOrigem(statusOrigem);
        notifCeler.setNovoStatusConta(conta.getIdStatusV2());
        notifCeler.setStatus(BLOQUEIO_CRIACAO);
        notifCeler.setIdConta(conta.getIdConta());

        logStatusContaCelerRepository.saveAndFlush(notifCeler);
      }

    } catch (Exception e) {
      // apenas para logar
      error(e.getMessage(), e);
    }
    return saveAndFlush(conta);
  }

  public PlanoSaudeContratadoVO getPlanoSaudeContratado(Long id, Long idPlanoSaude, Long idPessoa) {

    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdConta(id);

    PlanoSaudeProduto planoSaude = new PlanoSaudeProduto();
    planoSaude.setId(idPlanoSaude);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(idPessoa);

    return planoSaudeContratadoService.get(planoSaude, contaPagamento, pessoa);
  }

  public void contratarPlanoSaude(Long id, Long idPlanoSaude, Long idPessoa, Integer idUsuario) {

    ContaPagamento contaPagamento = new ContaPagamento();
    contaPagamento.setIdConta(id);

    PlanoSaudeProduto planoSaude = new PlanoSaudeProduto();
    planoSaude.setId(idPlanoSaude);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(idPessoa);

    planoSaudeContratadoService.add(planoSaude, contaPagamento, pessoa, idUsuario);
  }

  public void cancelarContratacaoPlanoSaude(
      Long id, Long idPlanoSaude, Long idPessoa, Integer idUsuario) {

    ContaPagamento contaPagamento = contaPagamentoRepository.findByIdConta(id);

    PlanoSaudeProduto planoSaude = new PlanoSaudeProduto();
    planoSaude.setId(idPlanoSaude);

    Pessoa pessoa = new Pessoa();
    pessoa.setIdPessoa(idPessoa);

    planoSaudeContratadoService.cancel(planoSaude, contaPagamento, pessoa, idUsuario);
  }

  public String getQuatroUltimosCartao(Long idTransacao, Integer idInstituicao) {
    LogTransacoes logTransacoes = logTransacoesRepositoryImpl.findByIdTranLog(idTransacao);
    if (logTransacoes != null) {
      Credencial credencial =
          getCredencialService()
              .getQuatroUltimosByTokenInternoTransacao(logTransacoes.getTokenInterno());
      if (credencial != null && credencial.getUltimos4Digitos() != null) {
        // para krediweb
        if (idInstituicao.equals(4001)) {
          if (logTransacoes.getSs() != null
              && ArranjoInstituicaoEnum.ELO.getSsList().contains(logTransacoes.getSs().trim())) {
            return Strings.padStart(
                credencial.getUltimos4Digitos().toString(), QTD_DIG_ULTIMOS_4, CHAR_ZERO);
          }
          return "";
        } else {
          return Strings.padStart(
              credencial.getUltimos4Digitos().toString(), QTD_DIG_ULTIMOS_4, CHAR_ZERO);
        }
      }
    }
    return "";
  }

  public ContaPagamento
      findByContasPessoaPessoaIdPessoaAndIdProdutoInstituicaoAndTipoStatus_IdGrupoStatusIsNot(
          Long idPessoa, Integer idProdutoInstituicao, Integer idGrupoStatusConta) {
    return contaPagamentoRepository
        .findByContasPessoaPessoaIdPessoaAndIdProdutoInstituicaoAndTipoStatus_IdGrupoStatusIsNot(
            idPessoa, idProdutoInstituicao, idGrupoStatusConta);
  }

  public boolean existeConta(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idProdutoInstituicao,
      Integer idTipoPessoa,
      String documento,
      boolean isB2B) {
    Integer contaPessoaNotB2b =
        contaPagamentoRepository.countConta(
            idProcessadora, idInstituicao, idProdutoInstituicao, idTipoPessoa, documento, isB2B);
    return Objects.nonNull(contaPessoaNotB2b) && contaPessoaNotB2b > 0;
  }

  public boolean existeContaPagamentoProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idTipoPessoa, String documento) {
    Integer contaPessoaNotB2b =
        contaPagamentoRepository.countByProdutoNotB2b(
            idProcessadora, idInstituicao, idTipoPessoa, documento);

    return Objects.nonNull(contaPessoaNotB2b) && contaPessoaNotB2b > 0;
  }

  @Transactional
  public ContaPagamento create(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao,
      boolean isB2B) {
    validarCadastroRazaoSocial(
        valorCargaProdutoInstituicao.getIdProdutoInstituicao(), cadastrarContaPagPessoa);

    validarProdutoInstituicao(
        pessoa.getIdTipoPessoa(), valorCargaProdutoInstituicao.getIdProdutoInstituicao(), isB2B);

    ContaPagamento conta = new ContaPagamento();
    ContaPagamentoRequest createConta = new ContaPagamentoRequest();

    prepareCadastrarConta(
        cadastrarContaPagPessoa, pessoa, conta, createConta, valorCargaProdutoInstituicao);

    geradorContaService.createContaPagamento(createConta, conta);

    ContaPessoa contaPessoa = prepareContaPessoa(pessoa, conta);
    contaPessoaService.vincularConta(contaPessoa);

    return conta;
  }

  @Transactional
  public void prepareCadastrarContaDigital(
      CadastrarContaDigital cadastrarContaPagPessoa,
      Pessoa pessoa,
      ContaPagamento conta,
      ContaPagamentoRequest createConta,
      ValorCargaProdutoInstituicao valorCargaProdutoInstituicao)
      throws IllegalAccessException, InvocationTargetException {

    BeanUtils.copyProperties(
        cadastrarContaPagPessoa, createConta, getNullPropertyNames(cadastrarContaPagPessoa));
    createConta.setIdProdutoInstituicao(valorCargaProdutoInstituicao.getIdProdutoInstituicao());

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            cadastrarContaPagPessoa.getIdProcessadora(),
            valorCargaProdutoInstituicao.getIdProdutoInstituicao(),
            cadastrarContaPagPessoa.getIdInstituicao());

    if (produtoInstituicaoConfiguracao == null) {
      throw new GenericServiceException(
          "A configuração do ProdutoInstituicao não pode ser localizada com os parâmetros: "
              + cadastrarContaPagPessoa.getIdProcessadora()
              + " | "
              + cadastrarContaPagPessoa.getIdInstituicao()
              + " | "
              + valorCargaProdutoInstituicao.getIdProdutoInstituicao());
    }

    ProdutoInstituicao produtoInstituicao =
        produtoInstituicaoService.findByIdProdInstituicao(
            valorCargaProdutoInstituicao.getIdProdutoInstituicao());

    if (produtoInstituicao == null) {
      throw new GenericServiceException(
          "O ProdutoInstituicao não pode ser localizada com id: "
              + valorCargaProdutoInstituicao.getIdProdutoInstituicao());
    }

    BeanUtils.copyProperties(createConta, pessoa, getNullPropertyNames(createConta));
    BeanUtils.copyProperties(createConta, conta, getNullPropertyNames(createConta));

    conta.setDataHoraInclusao(LocalDateTime.now());
    conta.setDataHoraStatusConta(LocalDateTime.now());
    conta.setIdUsuarioInclusao(pessoa.getIdUsuarioInclusao());
    conta.setIdStatusV2(Constantes.TIPO_STATUS_BLOQUEIO_ORIGEM);
    conta.setSaldoDisponivel(new Double(VALOR_ABRIR_CONTA));
  }

  public Optional<ContaPagamento> findByIdInstituicaoAndAndIdContaAndDocumento(
      Integer idInstituicao, Long idConta, String documento) {
    return contaPagamentoRepository.findByIdInstituicaoAndAndIdContaAndDocumento(
        idInstituicao, idConta, documento);
  }

  public void desbloquearContaPreCadastro(
      Long idPessoa, Integer idTitularidade, Integer idInstituicao) {

    ContaPagamento conta =
        encontraContaPorIdPessoaTitularidadeEInstituicao(idPessoa, idTitularidade, idInstituicao);

    conta.setIdStatusV2(Constantes.TIPO_STATUS_DESBLOQUEADO);

    save(conta);
  }

  private String generatePassword() {
    return Long.toHexString(Double.doubleToLongBits(Math.random())).substring(0, 8);
  }

  public void confirmaMigracao(String documento) {
    List<ContaPagamento> contaPagamentoList =
        findDadosContaByIdPessoa(documento, Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);

    Date dataHoraConfirmaReplica = new Date();

    for (ContaPagamento cp : contaPagamentoList) {
      try {
        if (cp.getDataHoraConfirmaReplica() == null) {
          cp.setDataHoraConfirmaReplica(dataHoraConfirmaReplica);
          contaPagamentoRepository.save(cp);
        }
      } catch (Exception e) {
        warn(
            "Tentativa de confirmar replica ja realizada anteriormente. idConta: "
                + cp.getIdConta());
        throw new GenericServiceException(
            "Tentativa de confirmar replica ja realizada anteriormente!");
      }
    }
  }

  public List<ContaPagamentoIntegracaoVO> buscarMigracao(Integer idInstituicao, String documento) {
    return contaPagamentoRepository.findDadosContaByDataHoraReplicacao(idInstituicao, documento);
  }

  public List<ContaPagamentoCadastroAvanceVO> buscarDadosContaByIdProdInstituicao(
      Integer idProdInstituicao, Integer idPontoDeRelacionamento) {
    return contaPagamentoRepository.findContaByIdProdInstituicao(
        idProdInstituicao, idPontoDeRelacionamento);
  }

  public List<ContasPorProdutoPortador> findAllContaPagamentoPorProdutoByDocumentoAndInstituicao(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    Collection<Integer> statusQueViraoNaPesquisa =
        Arrays.asList(DESBLOQUEADO, BLOQUEIO_DE_ORIGEM, BLOQUEIO_PREVENTIVO);
    List<ContasPorProdutoPortador> contasPorProduto =
        contaPagamentoRepository.findContasPagamentosByDocumentoAndInstituicaoAndStatusIn(
            idProcessadora, idInstituicao, documento, statusQueViraoNaPesquisa);

    for (ContasPorProdutoPortador contasPorProdutoPortador : contasPorProduto) {
      List<CredenciaisProdutoVirtualVO> credencial =
          getCredencialService()
              .findByIdPessoaAndIdContaAndStatusIn(
                  contasPorProdutoPortador.getIdPessoa(),
                  contasPorProdutoPortador.getIdConta(),
                  statusQueViraoNaPesquisa);

      for (CredenciaisProdutoVirtualVO credencialPortador : credencial) {
        if (credencialPortador.getVirtual()) {

          GetCardResponse response = cardService.getCard(credencialPortador.getTokenInterno());

          if (!response.getSuccess()) {
            throw new GenericServiceException(
                "Credencial não encontrada. Token: " + credencialPortador.getTokenInterno());
          }

          credencialPortador.setCredencialVirtual(response.getCard().getPan());
          credencialPortador.setCodigoSeguranca(response.getCard().getCvv2());

          String dataValidadeFmt =
              DateUtil.dateFormat(
                  FMT_MM_YY, DateUtil.localDateTimeToDate(credencialPortador.getDataValidade()));
          credencialPortador.setDataValidadeFmt(dataValidadeFmt);

          Plastico plastico = plasticoRepo.findOneByIdPlastico(credencialPortador.getIdPlastico());
          credencialPortador.setUrlImagemProduto(plastico.getUrlImagemProduto());
        }
        credencialPortador.setIdConta(contasPorProdutoPortador.getIdConta());
      }

      contasPorProdutoPortador.setCredenciais(credencial);
    }

    return contasPorProduto;
  }

  public void salvarDocumentosProposta(Proposta proposta) {
    HashMap<String, String> request = new HashMap<>();
    DocumentoOCRPropostaVO documentoOCRVO =
        antifraudeService.findDocumentoAntifraudeProposta(proposta);

    if (documentoOCRVO != null) {
      if (documentoOCRVO.getImageFront() != null && !documentoOCRVO.getImageFront().isEmpty()) {
        request.put("urlDocumento", documentoOCRVO.getImageFront());

        InputStream inputStream = antifraudeService.abrirDocumentoOCR(request);
        propostaService.createImgDocumentoProposta(
            inputStream, documentoOCRVO.getFileNameFront(), proposta.getNumeroProposta(), 1, null);
      }

      if (documentoOCRVO.getImageBack() != null && !documentoOCRVO.getImageBack().isEmpty()) {
        request.put("urlDocumento", documentoOCRVO.getImageBack());

        InputStream inputStream = antifraudeService.abrirDocumentoOCR(request);
        propostaService.createImgDocumentoProposta(
            inputStream, documentoOCRVO.getFileNameBack(), proposta.getNumeroProposta(), 1, null);
      }
    }
  }

  List<ContaPagamento> findContasByIdEndereco(Long idEndereco) {
    return contaPagamentoRepository.findContasByIdEndereco(idEndereco);
  }

  public void atualizarEnderecoPessoa(
      CadastrarContaPagamentoPessoaRequest cadastrarContaPagPessoa,
      Pessoa pessoa,
      List<EnderecoPessoa> enderecoPessoaList) {

    EnderecoPessoaRequest endereco =
        cadastrarContaPagPessoa.getEnderecosPessoaRequest().getEnderecos().get(0);

    for (EnderecoPessoa enderecoPessoa : enderecoPessoaList) {
      if (Objects.equals(enderecoPessoa.getIdTipoEndereco(), endereco.getIdTipoEndereco())) {
        BeanUtils.copyProperties(endereco, enderecoPessoa, getNullPropertyNames(endereco));
        enderecoPessoa.setIdPessoa(pessoa.getIdPessoa());
        enderecoPessoaService.save(enderecoPessoa);
      } else {
        enderecoPessoaService.cadastrarEnderecosPessoa(
            cadastrarContaPagPessoa.getIdUsuarioInclusao(), enderecoPessoa);
      }
    }
  }

  public Boolean findIfExistsContaCorresp(
      Integer idProdutoInstituicao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      String documento) {
    return contaPagamentoRepository.findIfExistsContaCorresp(
        idProdutoInstituicao,
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoDeRelacionamento,
        documento);
  }

  @Transactional
  public List<LogRegistroAlterarStatus> alterarStatusMultiplasContas(
      List<LogRegistroAlterarStatus> logs, Integer idUsuario, String ipOrigem) {
    List<LogRegistroAlterarStatus> listResponse = new ArrayList<>();

    for (LogRegistroAlterarStatus log : logs) {
      listResponse.add(alterarStatusPorLogRegistro(log, idUsuario, ipOrigem));
    }

    return listResponse;
  }

  @Transactional
  public LogRegistroAlterarStatus alterarStatusPorLogRegistro(
      LogRegistroAlterarStatus log, Integer idUsuario, String ipOrigem) {
    ContaPagamento conta = contaPagamentoRepository.findOneByIdConta(log.getIdConta());
    try {
      if (conta == null) {
        throw new GenericServiceException("Não foi encontrada conta para este ID.");
      } else {
        log.setStatusOriginal(conta.getIdStatusV2());
      }
      if (!conta.getIdInstituicao().equals(log.getLogArqAlterarStatus().getIdInstituicao())) {
        throw new GenericServiceException(
            "A conta não pertence à instituição informada no arquivo cadastrado.");
      }
      Boolean sucesso =
          this.verificaStatusEAlteraEstadoConta(
              conta, log.getStatusSolicitado(), idUsuario, ipOrigem);
      if (sucesso) {
        log.setDataHoraProcessamento(LocalDateTime.now());
        log.setStatusRegistro(StatusRegistroAlterarEnum.APROVADO);
      }
    } catch (GenericServiceException e) {
      log.setDataHoraProcessamento(LocalDateTime.now());
      log.setStatusRegistro(StatusRegistroAlterarEnum.REJEITADO);
      log.setMsgErro(e.getMensagem());
    }
    return log;
  }

  public Boolean verificaStatusEAlteraEstadoConta(
      ContaPagamento conta, Integer statusSolicitado, Integer idUsuario, String ipOrigem) {
    Boolean alteracaoStatusPermitida =
        mapaStatusService.isAlteracaoStatusPermitida(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            conta.getIdProdutoInstituicao(),
            APLICABILIDADE_CONTA,
            getAutorAlteracao(idUsuario),
            conta.getIdStatusV2(),
            statusSolicitado);
    if (!alteracaoStatusPermitida) {
      throw new GenericServiceException(
          "Alteração de status da conta não permitida. "
              + "Status Origem: "
              + conta.getIdStatusV2()
              + ", Status Destino: "
              + statusSolicitado);
    } else {
      return this.alterarEstadoConta(conta.getIdConta(), statusSolicitado, idUsuario, ipOrigem);
    }
  }

  public GetDadosConsumo buscarDadosProximaCarga(Long idConta, GetDadosConsumo dados) {
    return contaPagamentoRepository.buscarDadosProximaCarga(idConta, dados);
  }

  public GetDadosConsumo buscarDadosConsumoEstimado(Long idConta, GetDadosConsumo dados) {
    return contaPagamentoRepository.buscarDadosConsumoEstimado(idConta, dados);
  }

  public GetDadosConsumo buscarDadosConsumoReal(Long idConta, GetDadosConsumo dados) {
    return contaPagamentoRepository.buscarDadosConsumoReal(idConta, dados);
  }

  public List<DadosPortadorPontoRelacionamentoB2BVO> buscarContasByPontoDeRelacionamento(
      HierarquiaPontoRelacionamentoRequest hierarquiaPontoRelacionamentoRequest) {
    List<DadosPortadorPontoRelacionamentoB2BVO> listaFinal = new ArrayList<>();

    List<Object[]> dadosPortadorPontoRelacionamentoB2BVOList =
        contaPagamentoRepository.findContasByPontoRelacionamento(
            hierarquiaPontoRelacionamentoRequest.getIdPontoDeRelacionamento(),
            hierarquiaPontoRelacionamentoRequest.getIdInstituicao(),
            hierarquiaPontoRelacionamentoRequest.getIdRegional(),
            hierarquiaPontoRelacionamentoRequest.getIdFilial(),
            hierarquiaPontoRelacionamentoRequest.getIdEmpresaParceira(),
            hierarquiaPontoRelacionamentoRequest.getId());

    if (dadosPortadorPontoRelacionamentoB2BVOList.isEmpty()) {
      throw new GenericServiceException(
          "Não foi encontrado nenhum portador para o ponto de relacionamento "
              + hierarquiaPontoRelacionamentoRequest.getIdPontoDeRelacionamento()
              + " e para a empresa parceira "
              + hierarquiaPontoRelacionamentoRequest.getIdEmpresaParceira(),
          HttpStatus.NOT_FOUND);
    }

    for (Object[] obj : dadosPortadorPontoRelacionamentoB2BVOList) {
      DadosPortadorPontoRelacionamentoB2BVO dadosPortadorPontoRelacionamentoB2BVO =
          new DadosPortadorPontoRelacionamentoB2BVO();

      dadosPortadorPontoRelacionamentoB2BVO.setIdPessoa(ObjectUtil.objectToLong(obj[0]));
      dadosPortadorPontoRelacionamentoB2BVO.setIdConta(ObjectUtil.objectToLong(obj[1]));
      dadosPortadorPontoRelacionamentoB2BVO.setIdProcessadora(ObjectUtil.objectToInteger(obj[2]));
      dadosPortadorPontoRelacionamentoB2BVO.setIdInstituicao(ObjectUtil.objectToInteger(obj[3]));
      dadosPortadorPontoRelacionamentoB2BVO.setDocumento(ObjectUtil.objectToString(obj[4]));
      dadosPortadorPontoRelacionamentoB2BVO.setNomeCompleto(ObjectUtil.objectToString(obj[5]));
      dadosPortadorPontoRelacionamentoB2BVO.setBlAtivo(ObjectUtil.objectToBoolean(obj[6]));
      dadosPortadorPontoRelacionamentoB2BVO.setIdEmpresaParceiraInstituicao(
          ObjectUtil.objectToInteger(obj[7]));

      listaFinal.add(dadosPortadorPontoRelacionamentoB2BVO);
    }

    return listaFinal;
  }

  public Integer buscaMetodoSegurancaTransacaoConta(ContaPagamento conta) {

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());
    HierarquiaInstituicao instituicao =
        instituicaoService.findByIdProcessadoraAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdInstituicao());

    return retornaMetodoSegurancaTransacaoConta(conta, produtoInstituicaoConfiguracao, instituicao);
  }

  public Integer retornaMetodoSegurancaTransacaoConta(
      ContaPagamento conta,
      ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao,
      HierarquiaInstituicao instituicao) {
    if (conta.getMetodoSegurancaTransacao() != null) {
      return conta.getMetodoSegurancaTransacao();
    } else if (produtoInstituicaoConfiguracao.getMetodoSegurancaTransacao() != null) {
      return produtoInstituicaoConfiguracao.getMetodoSegurancaTransacao();
    } else {
      return instituicao.getMetodoSegurancaTransacao();
    }
  }

  public ContaPagamento encontraContaPorIdPessoaTitularidadeEInstituicao(
      Long idPessoa, Integer idTitularidade, Integer idInstituicao) {
    return contaPagamentoRepository
        .findByContasPessoaIdPessoaAndContasPessoaIdTitularidadeAndIdInstituicao(
            idPessoa, idTitularidade, idInstituicao);
  }

  public List<ContaPagamentoHistoricoStatusViewVO> getContaPagamentoHistoricoStatusView(
      SecurityUser user, Long idConta) {
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, findByIdConta(idConta));

    List<Object[]> historicoObjList =
        contaPagamentoHistoricoStatusViewRepository.findByIdConta(idConta);
    List<ContaPagamentoHistoricoStatusViewVO> contaPagamentoHistoricoStatusViewList =
        new ArrayList<>();

    for (Object[] obj : historicoObjList) {
      ContaPagamentoHistoricoStatusViewVO contaPagamentoHistoricoStatusView =
          new ContaPagamentoHistoricoStatusViewVO();
      contaPagamentoHistoricoStatusView.setIdConta(ObjectUtil.objectToLong(obj[0]));
      contaPagamentoHistoricoStatusView.setIdStatusAnterior(ObjectUtil.objectToLong(obj[1]));
      contaPagamentoHistoricoStatusView.setTxStatusAnterior(ObjectUtil.objectToString(obj[2]));
      contaPagamentoHistoricoStatusView.setIdStatusNovo(ObjectUtil.objectToLong(obj[3]));
      contaPagamentoHistoricoStatusView.setTxStatusNovo(ObjectUtil.objectToString(obj[4]));
      contaPagamentoHistoricoStatusView.setDtHrManutencao(
          ObjectUtil.objectToDateTimeStringPTBR(obj[5]));
      contaPagamentoHistoricoStatusView.setIdUsuario(ObjectUtil.objectToLong(obj[6]));
      contaPagamentoHistoricoStatusView.setTxNomeUsuario(ObjectUtil.objectToString(obj[7]));

      contaPagamentoHistoricoStatusViewList.add(contaPagamentoHistoricoStatusView);
    }

    return contaPagamentoHistoricoStatusViewList;
  }

  public ContaPagamento findByIdAcct(Long idAcct) {
    return contaPagamentoRepository.findByIdAcct(idAcct);
  }

  public List<ContaPagamentoCafVO> buscarDadosContaCaf(Integer idInstituicao, String documento) {
    return contaPagamentoRepository.findDadosContaByInstituicaoAndDocumento(
        idInstituicao, documento);
  }

  public ContaPagamento findByAccountCode(String code) {
    return contaPagamentoRepository.findByIdAccountCode(code);
  }

  public Boolean verificaPermiteCadastrarConta(CadastrarContaPagamentoPessoaRequest model) {
    Boolean permiteCadastrar = TRUE;
    ValorCargaProdutoInstituicao produtoContaLivre = null;
    boolean encontrouContaLivreSolicitada = false;
    boolean existeProdutoComGrupo = false;
    boolean pessoaJaPossuiSaldoLivre = false;

    // Verifica se a pessoa já possui uma conta do tipo Saldo Livre cadastrada
    for (ValorCargaProdutoInstituicao valorProduto : model.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoConfig =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              model.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              model.getIdInstituicao());

      if (produtoConfig.getIdGrupoProduto() != null) {
        existeProdutoComGrupo = true;

        ContaPagamento conta =
            findContasPagamentosComGrupoAcessoETipoProduto(
                model.getIdProcessadora(),
                model.getIdInstituicao(),
                model.getIdRegional(),
                model.getIdFilial(),
                model.getIdPontoDeRelacionamento(),
                model.getDocumento(),
                model.getTipoPessoa(),
                produtoConfig.getIdGrupoProduto(),
                TipoProdutoEnum.CONTA_LIVRE);
        pessoaJaPossuiSaldoLivre = (conta != null);
        break;
      }
    }

    // Percorre novamente para encontrar a solicitação de Saldo Livre, se houver
    for (ValorCargaProdutoInstituicao valorProduto : model.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoConfig =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              model.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              model.getIdInstituicao());

      // Verifica se existe uma solicitação de conta do tipo Saldo Livre
      if (produtoConfig.getIdGrupoProduto() != null
          && TipoProdutoEnum.CONTA_LIVRE.equals(produtoConfig.getTipoProduto())) {
        produtoContaLivre =
            valorProduto; // Armazena o produto CONTA_LIVRE para cadastro prioritário
        encontrouContaLivreSolicitada = true;
        break; // Podemos parar a busca após encontrar a conta Saldo Livre
      }
    }

    // Agora percorre novamente para validar o cadastro das contas com grupo
    for (ValorCargaProdutoInstituicao valorProduto : model.getValoresCargasProdutos()) {
      ProdutoInstituicaoConfiguracao produtoConfig =
          prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
              model.getIdProcessadora(),
              valorProduto.getIdProdutoInstituicao(),
              model.getIdInstituicao());

      if (produtoConfig.getIdGrupoProduto() != null) {
        // Para produtos com grupo, verifica a necessidade de Saldo Livre
        if (!pessoaJaPossuiSaldoLivre && !encontrouContaLivreSolicitada) {
          // Se a pessoa não tem Saldo Livre e não está sendo solicitado, bloqueia o cadastro
          permiteCadastrar = FALSE;
          break;
        }
      }
    }

    // Se o produto Saldo Livre foi solicitado, coloca ele no início da lista
    if (produtoContaLivre != null) {
      model.getValoresCargasProdutos().remove(produtoContaLivre);
      model.getValoresCargasProdutos().add(0, produtoContaLivre);
      permiteCadastrar = TRUE; // Permite o cadastro, pois a conta Saldo Livre será criada primeiro
    }

    // Se não houver produtos com grupo, o cadastro é permitido
    if (!existeProdutoComGrupo) {
      permiteCadastrar = TRUE;
    }

    return permiteCadastrar;
  }

  public ContaPagamento findContasPagamentosComGrupoAcessoETipoProduto(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      String documento,
      Integer idTipoPessoa,
      Long grupoAcesso,
      TipoProdutoEnum tipoProduto) {
    return contaPagamentoRepository.findContasPagamentosComGrupoAcessoETipoProduto(
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoDeRelacionamento,
        documento,
        idTipoPessoa,
        grupoAcesso,
        tipoProduto);
  }

  public GetSaldoConta getSaldoContaCorporativoPortador(
      Long idConta, SecurityUserCorporativo userCorporativo) {
    buscarValidarContaPeloRequestECorporativo(idConta, userCorporativo);
    return getSaldoConta(idConta);
  }

  public List<GetExtratoCredencial> getExtratoCorporativo(
      ExtratoRequestVo extratoRequestVo, SecurityUserCorporativo userCorporativo) {

    buscarValidarContaPeloRequestECorporativo(extratoRequestVo.getIdConta(), userCorporativo);

    return getExtrato(extratoRequestVo);
  }
}
