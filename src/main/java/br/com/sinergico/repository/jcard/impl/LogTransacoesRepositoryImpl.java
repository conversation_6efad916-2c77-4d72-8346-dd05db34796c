package br.com.sinergico.repository.jcard.impl;

import br.com.entity.jcard.LogTransacoes;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.jcard.AutorizacaoPendenteResponse;
import br.com.json.bean.jcard.TransacaoDePontoResponse;
import br.com.json.bean.transacional.TransacaoEstornadaResponse;
import br.com.json.bean.transacional.TransacoesByPeriodoResponse;
import br.com.json.bean.transacional.TransacoesPJB2BByPeriodoResponse;
import br.com.sinergico.repository.impl.AbstractDAO;
import br.com.sinergico.repository.jcard.LogTransacoesRepositoryCustom;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.ObjectUtil;
import br.com.sinergico.vo.LogTransacoesVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class LogTransacoesRepositoryImpl extends AbstractDAO<LogTransacoes, Long>
    implements LogTransacoesRepositoryCustom {

  @Override
  public BigDecimal findPontosDaJoyDisponiveisParaTransferencia(Long idConta) {
    StringBuilder query = new StringBuilder();
    query.append(" select ");
    query.append(
        "case when sum(lt.amount*ct.sinal_transacao)<0 then 0 else sum(lt.amount*ct.sinal_transacao) end as \"Saldo Tranferencia\" ");
    query.append(
        "from jcard.log_transacoes lt,transacional.codigo_transacao ct,cadastral.credencial cr, cadastral.conta_pagamento cpag ");

    query.append(" where ");
    query.append("ct.cod_transacao = cast(lt.functioncode as integer) ");
    query.append("and cr.token_interno = lt.token_interno ");
    query.append("and cpag.id_conta = cr.id_conta ");
    query.append("and ct.cod_transacao not in (682,683) ");
    query.append("and lt.irc='0000' ");
    query.append(" and lt.reversalcount=0 ");
    query.append("and cpag.id_instituicao= :idInstituicao ");
    query.append("and cpag.id_conta= :idConta ");

    Map<String, Object> params = new HashMap<String, Object>();

    params.put("idConta", idConta);
    params.put("idInstituicao", Constantes.ID_PRODUCAO_INSTITUICAO_JOY_POINTS);

    List<Object[]> ret = findNativeByParameters(query.toString(), params);

    if (ret == null || ret.isEmpty() || ret.get(0) == null) {
      throw new GenericServiceException("Valor não encontrado");
    }

    return ObjectUtil.objectToBigDecimal(ret.get(0));
  }

  @Override
  public List<AutorizacaoPendenteResponse> findByLogTransacoesAutorizacoesPendentes(Long idConta) {
    StringBuilder query = new StringBuilder();

    query.append(
        "select cp.id_instituicao, cp.id_conta, c.id_pessoa, c.id_credencial, c.bin_6, c.bin_estendido, c.ultimos_4_dig, c.nome_impresso,");
    query.append(
        " lt.date, ct.cod_transacao, pt.desc_reduzida, lt.amount,	ct.sinal_transacao,	pic.id_moeda, lt.mcc, lt.ca_name, lt.ca_city, lt.id, ");
    query.append(" (lt.amount*ct.sinal_transacao*-1) valor_com_sinal, lt.installments");
    query.append(" from jcard.log_transacoes lt");

    query.append(" inner join jcard.log_transacoes_controle ltc on ltc.id_transacao = lt.id");
    query.append(" inner join cadastral.credencial c on c.token_interno = lt.token_interno");
    query.append(" inner join cadastral.conta_pagamento cp on cp.id_conta = c.id_conta");
    query.append(
        " inner join cadastral.produto_instituicao_configuracao pic on pic.id_prod_instituicao = cp.id_prod_instituicao");
    query.append(
        " inner join transacional.codigo_transacao ct on ct.cod_transacao = cast(lt.functioncode as integer)");
    query.append(
        " inner join cadastral.produto_transacao pt on pt.id_prod_instituicao = cp.id_prod_instituicao and pt.cod_transacao = ct.cod_transacao");
    query.append(" where pic.id_prod_plat = 9 ");
    query.append(
        " and ( (lt.ss = 'CABAL' and lt.completioncount > 0) or (lt.ss = 'JCARD') or (lt.ss is null) ) ");
    query.append(" and lt.irc = '0000' ");
    query.append(" and lt.reversalcount = 0 ");
    query.append(" and lt.amount > 0  ");
    query.append(" and (lt.batchnumber = 0 or lt.batchnumber is null) ");
    query.append(" and ct.tipo_transacao != 3 ");
    query.append(" and ct.cod_transacao not in ('726', '727', '728','729') ");
    query.append(
        " and cp.id_conta= :idConta and ltc.id_job_fat_movtos is null and ltc.dt_hr_fat_movtos is null ");
    query.append(" order by lt.id ");

    Map<String, Object> params = new HashMap<String, Object>();

    params.put("idConta", idConta);

    List<AutorizacaoPendenteResponse> autorizacoes = new ArrayList<AutorizacaoPendenteResponse>();

    List<Object[]> ret = findNativeByParameters(query.toString(), params);

    for (Object[] o : ret) {
      AutorizacaoPendenteResponse auto = new AutorizacaoPendenteResponse();
      if (o[0] != null) auto.setIdInstituicao(Integer.valueOf(o[0].toString()));
      if (o[1] != null) {
        BigDecimal conta = new BigDecimal(o[1].toString());
        auto.setIdConta(conta.longValue());
      }
      if (o[2] != null) auto.setIdPessoa(Integer.valueOf(o[2].toString()));
      if (o[3] != null) auto.setIdCredencial(Integer.valueOf(o[3].toString()));
      if (o[4] != null) auto.setBinSeis(Integer.valueOf(o[4].toString()));

      if (o[5] != null) {
        BigDecimal binestendido = new BigDecimal(o[5].toString());
        auto.setBinEstendido(binestendido.longValue());
      }
      if (o[6] != null) auto.setUltimosQuatroDig(Integer.valueOf(o[6].toString()));
      if (o[7] != null) auto.setNomeImpresso((String) o[7]);
      if (o[8] != null) {
        auto.setDataLogTransacao(ObjectUtil.objectToDateTimeStringPTBR(o[8]).toString());
      }
      if (o[9] != null) auto.setCodTransacao(Integer.valueOf(o[9].toString()));
      if (o[10] != null) auto.setDescReduzidaProdutoTransacao((String) o[10]);
      if (o[11] != null) {
        BigDecimal amount = new BigDecimal(o[11].toString());
        auto.setAmount(amount);
      }
      if (o[12] != null) {
        BigDecimal sinal = new BigDecimal(o[12].toString());
        auto.setSinalTransacao(sinal);
      }
      if (o[13] != null) auto.setIdMoeda(Integer.valueOf(o[13].toString()));
      if (o[14] != null) auto.setMcc((String) o[14]);
      if (o[15] != null) auto.setCaName((String) o[15]);
      if (o[16] != null) auto.setCaCity((String) o[16]);
      if (o[17] != null) auto.setIdLogTransacao(Integer.valueOf(o[17].toString()));
      if (o[18] != null) {
        BigDecimal valorComSinal = new BigDecimal(o[18].toString());
        auto.setValorComSinal(valorComSinal.doubleValue());
      }
      if (o[19] != null) auto.setInstallments(Integer.valueOf(o[19].toString()));
      autorizacoes.add(auto);
    }
    return autorizacoes;
  }

  @Override
  public Long findIdLogTransacoesByRrn(String rrn) {

    StringBuilder query = new StringBuilder();
    query.append(" select lt.id, lt.functioncode");
    query.append(" from jcard.log_transacoes lt");
    query.append(" where lt.rrn = :rrn");

    Map<String, Object> params = new HashMap<String, Object>();
    params.put("rrn", rrn);

    List<Object[]> consulta = findNativeByParameters(query.toString(), params);

    if (consulta == null || consulta.isEmpty() || consulta.size() == 0) {
      throw new GenericServiceException("Resgate com RRN = " + rrn + " não encontrado.");
    }

    Long resultado = null;
    for (Object[] obj : consulta) {
      resultado = ObjectUtil.objectToLong(obj[0]);
    }

    return resultado;
  }

  @Override
  public Integer findFunctionCodeByRrn(String rrn) {

    StringBuilder query = new StringBuilder();
    query.append(" select lt.functioncode, lt.id");
    query.append(" from jcard.log_transacoes lt");
    query.append(" where lt.rrn = :rrn");

    Map<String, Object> params = new HashMap<String, Object>();
    params.put("rrn", rrn);

    List<Object[]> consulta = findNativeByParameters(query.toString(), params);

    if (consulta == null || consulta.isEmpty() || consulta.get(0) == null) {
      throw new GenericServiceException("Resgate com RRN = " + rrn + " não encontrado.");
    }

    Integer resultado = null;
    for (Object[] obj : consulta) {
      resultado = ObjectUtil.objectToInteger(obj[0]);
    }

    return resultado;
  }

  @Override
  public List<TransacoesByPeriodoResponse> findTransacoesByHierarquiaAndProdutoAndPeriodo(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdInstituicao,
      String documento,
      Date dataInicio,
      Date dataFim) {
    StringBuilder hql = new StringBuilder();
    Map<String, Object> params = new HashMap<>();

    hql.append(
        "select "
            + "cp.id_conta, "
            + "p.matricula, "
            + "p.documento, "
            + "(case when p.tipo_pessoa = 1 then p.nome_completo else p.razao_social end) nome_pessoa, "
            + "lt.date, "
            + "lt.amount, "
            + "(case when ct.sinal_transacao = -1 then 'DÉBITO' else 'CRÉDITO' end) as sinal, "
            + "cr.bin_6, "
            + "cr.ultimos_4_dig, "
            + "(case when ct.desc_transacao_reduzida is not null then ct.desc_transacao_reduzida else ct.desc_transacao_estendida end) as desc_transacao, "
            + "lt.ca_name, "
            + "cp.id_prod_instituicao, "
            + "pi.desc_prod_instituicao ");
    hql.append(
        "from cadastral.conta_pagamento cp, cadastral.credencial cr, jcard.log_transacoes lt, ");
    hql.append(
        "transacional.codigo_transacao ct, cadastral.pessoa p, cadastral.produto_instituicao pi ");
    hql.append("where cr.id_conta = cp.id_conta ");
    hql.append("and cr.id_pessoa = p.id_pessoa ");
    hql.append("and cp.id_processadora = pi.id_processadora ");
    hql.append("and cp.id_instituicao = pi.id_instituicao ");
    hql.append("and cp.id_prod_instituicao = pi.id_prod_instituicao ");
    hql.append("and lt.token_interno = cr.token_interno ");
    hql.append("and ct.cod_transacao = cast (lt.functioncode as integer) ");
    hql.append("and lt.irc = '0000' ");
    hql.append("and lt.reversalcount = 0 ");
    hql.append("and cp.id_processadora = :idProcessadora ");
    hql.append("and cp.id_instituicao =  :idInstituicao ");
    hql.append("and cp.id_regional =  :idRegional ");
    hql.append("and cp.id_filial =  :idFilial ");
    hql.append("and cp.id_ponto_de_relacionamento =  :idPontoDeRelacionamento ");
    hql.append("and cp.id_prod_instituicao = :idProdutoInstituicao ");
    hql.append("and date_trunc('day',lt.date) >= :dataInicio ");
    hql.append("and date_trunc('day',lt.date) <= :dataFim ");
    hql.append("and lt.functioncode not in('994','995','996','997') ");
    hql.append("and ct.classe_transacao not in (3,7,9) ");

    if (documento != null && !documento.isEmpty()) {
      hql.append("and p.documento = :documento ");
      params.put("documento", documento);
    }

    hql.append("order by cp.id_conta ");

    params.put("idProcessadora", idProcessadora);
    params.put("idInstituicao", idInstituicao);
    params.put("idRegional", idRegional);
    params.put("idFilial", idFilial);
    params.put("idPontoDeRelacionamento", idPontoDeRelacionamento);
    params.put("idProdutoInstituicao", idProdInstituicao);
    params.put("dataInicio", dataInicio);
    params.put("dataFim", dataFim);

    List<Object[]> result = findNativeByParameters(hql.toString(), params);

    return buildResultado(result);
  }

  private List<TransacoesByPeriodoResponse> buildResultado(List<Object[]> result) {
    List<TransacoesByPeriodoResponse> trans = new ArrayList<>();

    if (result != null && !result.isEmpty()) {
      result.forEach(
          objects -> {
            trans.add(
                new TransacoesByPeriodoResponse(
                    ObjectUtil.objectToLong(objects[0]),
                    ObjectUtil.objectToString(objects[1]),
                    ObjectUtil.objectToString(objects[2]),
                    ObjectUtil.objectToString(objects[3]),
                    ObjectUtil.objectToDate(objects[4]),
                    ObjectUtil.objectToBigDecimal(objects[5]),
                    ObjectUtil.objectToString(objects[6]),
                    ObjectUtil.objectToInteger(objects[7]),
                    ObjectUtil.objectToInteger(objects[8]),
                    ObjectUtil.objectToString(objects[9]),
                    ObjectUtil.objectToString(objects[10]),
                    ObjectUtil.objectToInteger(objects[11]),
                    ObjectUtil.objectToString(objects[12])));
          });
    }

    return trans;
  }

  private List<TransacoesPJB2BByPeriodoResponse> buildResultadoTransacoesPJB2B(
      List<Object[]> result) {
    List<TransacoesPJB2BByPeriodoResponse> trans = new ArrayList<>();

    if (result != null && !result.isEmpty()) {
      result.forEach(
          objects -> {
            trans.add(
                new TransacoesPJB2BByPeriodoResponse(
                    ObjectUtil.objectToLong(objects[0]),
                    ObjectUtil.objectToString(objects[1]),
                    ObjectUtil.objectToString(objects[2]),
                    ObjectUtil.objectToString(objects[3]),
                    ObjectUtil.objectToString(objects[4]),
                    ObjectUtil.objectToBigDecimal(objects[5]),
                    ObjectUtil.objectToString(objects[6]),
                    ObjectUtil.objectToString(objects[7]),
                    ObjectUtil.objectToString(objects[8]),
                    ObjectUtil.objectToString(objects[9]),
                    ObjectUtil.objectToString(objects[10]),
                    ObjectUtil.objectToString(objects[11]),
                    ObjectUtil.objectToString(objects[12]),
                    ObjectUtil.objectToString(objects[13])));
          });
    }

    return trans;
  }

  @Override
  public Long findIdLogTransacoesQueEstornouRrn(String rrn) {

    StringBuilder query = new StringBuilder();
    query.append(" select lt.voidid");
    query.append(" from jcard.log_transacoes lt");
    query.append(" where voidcount>0 and rrn = :rrn");

    Map<String, Object> params = new HashMap<String, Object>();
    params.put("rrn", rrn);

    List<Object[]> consulta = findNativeByParameters(query.toString(), params);

    Long resultado = 0L;

    if (consulta != null && !consulta.isEmpty() && consulta.size() != 0) {
      Object obj = consulta.get(0);
      resultado = ObjectUtil.objectToLong(obj);
    }

    return resultado;
  }

  @Override
  public TransacaoDePontoResponse findTransacaoDePontoByRrn(String rrn) {
    String clausulaWhere = " where lt.rrn=:rrn";

    Map<String, Object> param = new HashMap<String, Object>();
    param.put("rrn", rrn);

    return findTransacaoDePonto(clausulaWhere, param);
  }

  @Override
  public TransacaoDePontoResponse findTransacaoDePontoById(Long id) {

    String clausulaWhere = " where lt.id=:id";

    Map<String, Object> param = new HashMap<String, Object>();
    param.put("id", id);

    return findTransacaoDePonto(clausulaWhere, param);
  }

  private TransacaoDePontoResponse findTransacaoDePonto(
      String clausulaWhere, Map<String, Object> param) {

    StringBuilder query = new StringBuilder();
    query.append(
        " select lt.id, lm.data_hora_lancamento, lm.valor*ct.sinal_transacao, lm.id_conta, lm.rrn, lm.txt_extrato, lm.txt_comentario, lt.functioncode, ct.desc_transacao_estendida");
    query.append(" from jcard.log_transacoes lt");
    query.append("	inner join transacional.lancamento_manual lm on lm.rrn=lt.rrn");
    query.append(
        "	inner join transacional.codigo_transacao ct on ct.cod_transacao=cast(lt.functioncode as numeric)");
    query.append(clausulaWhere);

    List<Object[]> consulta = findNativeByParameters(query.toString(), param);

    TransacaoDePontoResponse transacao = new TransacaoDePontoResponse();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        //			transacao.setId(ObjectUtil.objectToLong(obj[0]));
        transacao.setDataLancamento(
            DateUtil.dateFormat(DateUtil.DD_MM_YYYY, ObjectUtil.objectToDate(obj[1])));
        transacao.setValor(ObjectUtil.objectToLong(obj[2]));
        transacao.setIdConta(ObjectUtil.objectToLong(obj[3]));
        transacao.setRrn(ObjectUtil.objectToString(obj[4]));
        transacao.setTextoExtrato(ObjectUtil.objectToString(obj[5]));
        transacao.setTextoComentario(ObjectUtil.objectToString(obj[6]));
        transacao.setFunctioncode(ObjectUtil.objectToString(obj[7]));
        transacao.setDescricaoFunctioncode(ObjectUtil.objectToString(obj[8]));
      }
    }
    return transacao;
  }

  @Override
  public TransacaoEstornadaResponse findTransacaoEstornadaByIdTranlog(Long idTranlog) {

    String clausulaWhere = " where lt.id = :idTranlog ";

    Map<String, Object> param = new HashMap<String, Object>();
    param.put("idTranlog", idTranlog);

    return findTransacaoEstornada(clausulaWhere, param);
  }

  private TransacaoEstornadaResponse findTransacaoEstornada(
      String clausulaWhere, Map<String, Object> param) {

    StringBuilder query = new StringBuilder();
    query.append(
        " select ltorig.\"date\" , c.ultimos_4_dig , ltorig.ca_name , ltorig.approvalnumber , ltorig.stan , ltorig.amount ");
    query.append(" from jcard.log_transacoes lt ");
    query.append(" inner join jcard.log_transacoes ltorig on ltorig.id = lt.refid ");
    query.append(" inner join cadastral.credencial c on ltorig.token_interno = c.token_interno ");
    query.append(clausulaWhere);

    List<Object[]> consulta = findNativeByParameters(query.toString(), param);

    TransacaoEstornadaResponse transacao = new TransacaoEstornadaResponse();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        transacao.setDataTransacao(ObjectUtil.objectToLocalDateTime(obj[0]));
        transacao.setQuatroUltimosNumeros(ObjectUtil.objectToString(obj[1]));
        transacao.setDescLocal(ObjectUtil.objectToString(obj[2]));
        transacao.setNumeroAutorizacao(ObjectUtil.objectToString(obj[3]));
        transacao.setNsu(ObjectUtil.objectToString(obj[4]));
        transacao.setValorTransacao(ObjectUtil.objectToBigDecimal(obj[5]));
      }
    }
    return transacao;
  }

  @Override
  public LogTransacoes findByRrnAndSsAndDia(String rrn, String ss, String dia) {

    String clausulaWhere =
        " where true \n "
            + " \t and lt.rrn = :rrn \n "
            + " \t and date_trunc('day', lt.\"date\") = cast( :dia as date ) \n "
            + (ss == null ? " \t and lt.ss is null \n " : " \t and lt.ss = :ss \n ");

    Map<String, Object> param = new HashMap<String, Object>();
    param.put("rrn", rrn);
    param.put("dia", dia);
    if (ss != null) {
      param.put("ss", ss);
    }

    return findByRrnAndDiaAndSsIfPresent(clausulaWhere, param);
  }

  private LogTransacoes findByRrnAndDiaAndSsIfPresent(
      String clausulaWhere, Map<String, Object> param) {

    StringBuilder query = new StringBuilder();
    query.append(
        " select lt.id , lt.rrn , lt.token_interno , lt.amount , lt.functioncode from jcard.log_transacoes lt \n ");
    query.append(clausulaWhere);

    List<Object[]> consulta = findNativeByParameters(query.toString(), param);

    LogTransacoes transacao = new LogTransacoes();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        transacao.setId(ObjectUtil.objectToLong(obj[0]));
        transacao.setRrn(ObjectUtil.objectToString(obj[1]));
        transacao.setTokenInterno(ObjectUtil.objectToString(obj[2]));
        transacao.setAmount(ObjectUtil.objectToBigDecimal(obj[3]));
        transacao.setFunctionCode(ObjectUtil.objectToString(obj[4]));
      }
    }
    return transacao;
  }

  @Override
  public LogTransacoes findByRrn(String rrn) {

    StringBuilder hql = new StringBuilder();

    hql.append(
        " select \n "
            + " \t lt.id, \n "
            + " \t lt.functioncode, \n "
            + " \t lt.amount, \n "
            + " \t lt.token_interno, \n "
            + " \t lt.date, \n "
            + " \t lt.additionaldata \n "
            + " from jcard.log_transacoes lt \n "
            + " where lt.rrn = :rrn ");

    Map<String, Object> param = new HashMap<String, Object>();

    param.put("rrn", rrn);

    List<LogTransacoes> logTransacoes = new ArrayList<LogTransacoes>();

    List<Object[]> consulta = findNativeByParameters(hql.toString(), param);

    LogTransacoes logTransacao = new LogTransacoes();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        logTransacao.setId(ObjectUtil.objectToLong(obj[0]));
        logTransacao.setFunctionCode(ObjectUtil.objectToString(obj[1]));
        logTransacao.setAmount(ObjectUtil.objectToBigDecimal(obj[2]));
        logTransacao.setTokenInterno(ObjectUtil.objectToString(obj[3]));
        logTransacao.setDate(ObjectUtil.objectToDate(obj[4]));
        logTransacao.setAdditionaldata(ObjectUtil.objectToString(obj[5]));

        logTransacoes.add(logTransacao);
      }

      return logTransacoes.get(0);
    }
    return null;
  }

  @Override
  public LogTransacoes findByIdTranlogOrQrCodeTransactionIdIsQrCodeElo(
      Long id, String transactionId) {

    StringBuilder hql = new StringBuilder();

    hql.append(
        " select \n "
            + " \t lt.id, \n "
            + " \t lt.functioncode, \n "
            + " \t lt.amount, \n "
            + " \t lt.token_interno, \n "
            + " \t lt.date, \n "
            + " \t lt.ca_name, \n "
            + " \t lt.ca_city, \n "
            + " \t lt.approvalnumber, \n "
            + " \t lt.displaymessage, \n "
            + " \t lt.ss, \n "
            + " \t tqe.id is not null \n "
            + " from jcard.log_transacoes lt \n "
            + " \t left join transacional.transacao_qrcode_elo tqe on tqe.token_interno = lt.token_interno \n "
            + " \t \t and abs(extract(epoch from tqe.approval_timestamp) - extract(epoch from lt.date)) <= 5 \n "
            + " \t \t and tqe.transaction_amount = lt.amount \n "
            + " where true ");

    Map<String, Object> param = new HashMap<String, Object>();

    if (id != null) {
      hql.append(" and lt.id = :id ");
      param.put("id", id);
    }

    if (transactionId != null && !transactionId.isEmpty()) {
      hql.append(" and tqe.transaction_id = :transactionId ");
      param.put("transactionId", transactionId);
    }

    List<LogTransacoes> logTransacoes = new ArrayList<LogTransacoes>();

    List<Object[]> consulta = findNativeByParameters(hql.toString(), param);

    LogTransacoes logTransacao = new LogTransacoes();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        logTransacao.setId(ObjectUtil.objectToLong(obj[0]));
        logTransacao.setFunctionCode(ObjectUtil.objectToString(obj[1]));
        logTransacao.setAmount(ObjectUtil.objectToBigDecimal(obj[2]));
        logTransacao.setTokenInterno(ObjectUtil.objectToString(obj[3]));
        logTransacao.setDate(ObjectUtil.objectToDate(obj[4]));
        logTransacao.setCaName(ObjectUtil.objectToString(obj[5]));
        logTransacao.setCaCity(ObjectUtil.objectToString(obj[6]));
        logTransacao.setApprovalNumber(ObjectUtil.objectToString(obj[7]));
        logTransacao.setDisplaymessage(ObjectUtil.objectToString(obj[8]));
        logTransacao.setSs(ObjectUtil.objectToString(obj[9]));
        logTransacao.setIsTransacaoQrCodeElo(ObjectUtil.objectToBoolean(obj[10]));

        logTransacoes.add(logTransacao);
      }

      return logTransacoes.get(0);
    }
    return null;
  }

  @Override
  public LogTransacoesVO findTransacaoByRrn(String rrn) {

    StringBuilder hql = new StringBuilder();

    hql.append("select lt.id,lt.rrn, lt.voidid from jcard.log_transacoes lt where lt.rrn = :rrn");

    Map<String, Object> param = new HashMap<String, Object>();

    param.put("rrn", rrn);

    List<LogTransacoesVO> logTransacoes = new ArrayList<LogTransacoesVO>();

    List<Object[]> consulta = findNativeByParameters(hql.toString(), param);

    LogTransacoesVO logTransacao = new LogTransacoesVO();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        logTransacao.setId(ObjectUtil.objectToLong(obj[0]));
        logTransacao.setRrn(ObjectUtil.objectToString(obj[1]));
        logTransacao.setVoidId(ObjectUtil.objectToLong(obj[2]));

        logTransacoes.add(logTransacao);
      }

      return logTransacoes.get(0);
    }
    return null;
  }

  public LogTransacoes findByIdTranLog(Long idLogTrans) {

    StringBuilder hql = new StringBuilder();

    hql.append(
        "select lt.id, lt.token_interno, lt.ss from jcard.log_transacoes lt where lt.id = :idLogTrans");

    Map<String, Object> param = new HashMap<String, Object>();

    param.put("idLogTrans", idLogTrans);

    List<LogTransacoes> logTransacoes = new ArrayList<LogTransacoes>();

    List<Object[]> consulta = findNativeByParameters(hql.toString(), param);

    LogTransacoes logTransacao = new LogTransacoes();

    if (consulta != null && !consulta.isEmpty() && consulta.size() > 0) {

      for (Object[] obj : consulta) {
        logTransacao.setId(ObjectUtil.objectToLong(obj[0]));
        logTransacao.setTokenInterno(ObjectUtil.objectToString(obj[1]));
        logTransacao.setSs(ObjectUtil.objectToString(obj[2]));

        logTransacoes.add(logTransacao);
      }

      return logTransacoes.get(0);
    }
    return null;
  }

  @Override
  public List<TransacoesPJB2BByPeriodoResponse> findTransacoesPJB2BByHierarquiaAndPeriodo(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Date dataInicio,
      Date dataFim) {

    StringBuilder hql = new StringBuilder();
    Map<String, Object> params = new HashMap<>();

    hql.append(
        " select "
            + "	t.id, "
            + "	pe.nome_completo \"Nome do Portador\", "
            + "	to_CHAR(t.date,'dd/MM/yyyy hh24:mi:ss') as \"Data Transação\", "
            + "	t.ca_name as \"estabelecimento\", "
            + "	t.ca_city as \"cidade\", "
            + "	t.amount as \"valor\", "
            + "	pt.desc_reduzida \"Transação\", "
            + "	upper(ct.desc_transacao_estendida) as \"Descrição estendida\", "
            + "	( "
            + "		case when(t.irc = '0000' and t.reversalcount = 0 ) "
            + "			 	then 'Aprovada' "
            + "			 when(t.irc = '0000' and t.reversalcount <> 0) "
            + "				then 'Aprovada (Desfeita)' "
            + "			 when(t.irc <> '0000' and t.reversalcount <> 0) "
            + "				then 'Negada (Desfeita)' "
            + "			 	else 'Negada' "
            + "		end "
            + "	) as \"Resultado\", "
            + "	(case when(t.irc != '0000') then t.displaymessage else '' end) as Mensagem, "
            + "	cp.id_conta_pagamento as \"Conta Pagamento\", "
            + "	prod.desc_prod_instituicao as \"Produto\", "
            + "	t.tid \"ID Terminal\", "
            + "	t.mcc \"MCC\" "
            + " from cadastral.conta_pagamento cp "
            + " inner join cadastral.credencial_conta cc on cc.id_conta = cp.id_conta "
            + " inner join cadastral.credencial c on cc.id_credencial = c.id_credencial "
            + " inner join cadastral.produto_instituicao prod on prod.id_prod_instituicao = cp.id_prod_instituicao "
            + " inner join cadastral.pessoa pe on pe.id_pessoa = c.id_pessoa "
            + " inner join jcard.log_transacoes t on c.token_interno = t.token_interno and cp.id_acct = t.account "
            + " inner join transacional.codigo_transacao ct on ct.cod_transacao = cast(t.functioncode as integer) "
            + " inner join cadastral.produto_transacao pt on pt.id_prod_instituicao = cp.id_prod_instituicao and ct.cod_transacao = pt.cod_transacao "
            + " where t.date >= :dataInicio "
            + "	and t.date <= :dataFim "
            + "	and pe.tipo_pessoa = 2 "
            + "	and ct.classe_transacao <> 7 "
            + "	and cp.id_processadora = :idProcessadora ");

    if (idInstituicao != null) {
      hql.append("	and cp.id_instituicao  = :idInstituicao ");
      hql.append(
          "	and pe.id_processadora = cp.id_processadora and pe.id_instituicao = cp.id_instituicao "); // Para performance
      params.put("idInstituicao", idInstituicao);
    }
    if (idRegional != null) {
      params.put("idRegional", idRegional);
      hql.append("	and cp.id_regional = :idRegional ");
    }
    if (idFilial != null) {
      params.put("idFilial", idFilial);
      hql.append("	and cp.id_filial = :idFilial ");
    }
    if (idPontoDeRelacionamento != null) {
      params.put("idPontoDeRelacionamento", idPontoDeRelacionamento);
      hql.append("	and cp.id_ponto_de_relacionamento = :idPontoDeRelacionamento ");
    }
    hql.append(" order by t.id desc ");

    params.put("idProcessadora", idProcessadora);

    params.put("dataInicio", dataInicio);
    params.put("dataFim", dataFim);

    List<Object[]> result = findNativeByParameters(hql.toString(), params);

    return buildResultadoTransacoesPJB2B(result);
  }

  @Override
  public List<LogTransacoes> findByIdInstituicaoPeriodo(
      Integer idInstituicao, List<String> functionCodes, Date dataHoraInicio, Date dataHoraFim) {
    StringBuilder hql = new StringBuilder();
    Map<String, Object> params = new HashMap<>();
    hql.append("select\n");
    hql.append("	lt.id, lt.date, lt.functioncode, lt.amount\n");
    hql.append("from\n");
    hql.append("	jcard.log_transacoes lt\n");
    hql.append("left join cadastral.credencial c on\n");
    hql.append("	lt.token_interno = c.token_interno \n");
    hql.append("left join cadastral.conta_pagamento cp on\n");
    hql.append("	c.id_conta = cp.id_conta \n");
    hql.append("where\n");
    hql.append("	1 = 1\n");
    hql.append("	and cp.id_instituicao = :idInstituicao\n");
    hql.append("	and lt.date between :dataHoraInicio and :dataHoraFim\n");
    hql.append("	and lt.functioncode in :listaFunctionCodes\n");
    hql.append("	and lt.displaymessage like 'APROVADO'\n");
    hql.append("order by\n");
    hql.append("	lt.date desc;");
    params.put("idInstituicao", idInstituicao);
    params.put("dataHoraInicio", dataHoraInicio);
    params.put("dataHoraFim", dataHoraFim);
    params.put("listaFunctionCodes", functionCodes);
    List<Object[]> query = findNativeByParameters(hql.toString(), params);
    return buildResultLogTransacoesToContaGarantia(query);
  }

  private List<LogTransacoes> buildResultLogTransacoesToContaGarantia(List<Object[]> result) {
    List<LogTransacoes> listaLogTransacoes = new ArrayList<>();
    if (result != null && !result.isEmpty()) {
      result.forEach(
          objects -> {
            LogTransacoes lt = new LogTransacoes();
            lt.setId(ObjectUtil.objectToLong(objects[0]));
            lt.setDate(ObjectUtil.objectToDate(objects[1]));
            lt.setFunctionCode(ObjectUtil.objectToString(objects[2]));
            lt.setAmount(ObjectUtil.objectToBigDecimal(objects[3]));
            listaLogTransacoes.add(lt);
          });
    }
    return listaLogTransacoes;
  }
}
