package br.com.sinergico.repository.suporte;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.when;

import br.com.entity.suporte.AcessoUsuario;
import br.com.json.bean.cadastral.BuscaGenericaFiltro;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

@ExtendWith(MockitoExtension.class)
public class AcessoUsuarioRepositoryTest {

  @Mock private AcessoUsuarioRepository acessoUsuarioRepository;

  @Test
  void testBuscarPessoasComFiltro() {
    // Setup
    Pageable pageable = PageRequest.of(0, 10);
    AcessoUsuario usuario = new AcessoUsuario();
    usuario.setIdInstituicao(1);
    usuario.setIdRegional(1);
    usuario.setIdFilial(1);
    usuario.setIdPontoDeRelacionamento(1);
    usuario.setNome("Test User");
    usuario.setCpf("12345678901");
    usuario.setStatus(1);
    usuario.setIdUsuario(1);

    Page<AcessoUsuario> page = new PageImpl<>(Collections.singletonList(usuario), pageable, 1);

    when(acessoUsuarioRepository.buscarPessoasComFiltros(
            any(Integer.class),
            any(Integer.class),
            any(Integer.class),
            any(Integer.class),
            any(String.class),
            any(String.class),
            any(Integer.class),
            any(Integer.class),
            any(Integer.class),
            any(Pageable.class)))
        .thenReturn(page);

    // Execute
    Page<AcessoUsuario> result =
        acessoUsuarioRepository.buscarPessoasComFiltros(
            1, 1, 1, 1, "Test User", "12345678901", 1, 1, 1, pageable);

    // Verify
    assertEquals(1, result.getTotalElements());
    assertEquals(usuario, result.getContent().get(0));
  }

  @Test
  void testBuscarPessoasComFiltrosTodosCamposNulos() {
    // Setup
    Pageable pageable = PageRequest.of(0, 10);
    AcessoUsuario acessoUsuario = new AcessoUsuario();
    BuscaGenericaFiltro usuario = new BuscaGenericaFiltro();

    Page<AcessoUsuario> page =
        new PageImpl<>(Collections.singletonList(acessoUsuario), pageable, 1);

    // Corrigir a configuração do mock para corresponder aos argumentos reais, incluindo valores
    // nulos
    when(acessoUsuarioRepository.buscarPessoasComFiltros(
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            eq(pageable)))
        .thenReturn(page);

    // Execute
    Page<AcessoUsuario> result =
        acessoUsuarioRepository.buscarPessoasComFiltros(
            usuario.getIdInstituicao(),
            usuario.getIdRegional(),
            usuario.getIdFilial(),
            usuario.getIdPontoDeRelacionamento(),
            usuario.getUsuario(),
            usuario.getDocumento(),
            usuario.getStatus(),
            usuario.getIdUsuario(),
            usuario.getIdGrupoAcesso(),
            pageable);

    // Verify
    assertEquals(1, result.getTotalElements());
    assertEquals(acessoUsuario, result.getContent().get(0));
  }

  @Test
  void testBuscarPessoasComFiltrosTodosCamposnNome() {
    // Setup
    Pageable pageable = PageRequest.of(0, 10);
    AcessoUsuario acessoUsuario = new AcessoUsuario();
    BuscaGenericaFiltro usuario = new BuscaGenericaFiltro();
    usuario.setUsuario("%Gabriel%".toUpperCase());

    Page<AcessoUsuario> page =
        new PageImpl<>(Collections.singletonList(acessoUsuario), pageable, 1);

    // Corrigir a configuração do mock para corresponder aos argumentos reais, incluindo valores
    // nulos
    when(acessoUsuarioRepository.buscarPessoasComFiltros(
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            eq(usuario.getUsuario()),
            isNull(),
            isNull(),
            isNull(),
            isNull(),
            eq(pageable)))
        .thenReturn(page);

    // Execute
    Page<AcessoUsuario> result =
        acessoUsuarioRepository.buscarPessoasComFiltros(
            usuario.getIdInstituicao(),
            usuario.getIdRegional(),
            usuario.getIdFilial(),
            usuario.getIdPontoDeRelacionamento(),
            usuario.getUsuario(),
            usuario.getDocumento(),
            usuario.getStatus(),
            usuario.getIdUsuario(),
            usuario.getIdGrupoAcesso(),
            pageable);

    // Verify
    assertEquals(1, result.getTotalElements());
    assertEquals(acessoUsuario, result.getContent().get(0));
  }

  @Test
  void testBuscarPessoasComFiltrosSomenteNome() {
    // Setup
    Pageable pageable = PageRequest.of(0, 10);
    AcessoUsuario usuario = new AcessoUsuario();
    usuario.setIdInstituicao(1);
    usuario.setIdRegional(1);
    usuario.setIdFilial(1);
    usuario.setIdPontoDeRelacionamento(1);
    usuario.setNome("Test User");
    usuario.setCpf("12345678901");
    usuario.setStatus(1);
    usuario.setIdUsuario(1);

    Page<AcessoUsuario> page = new PageImpl<>(Collections.singletonList(usuario), pageable, 1);

    when(acessoUsuarioRepository.buscarPessoasComFiltros(
            any(Integer.class),
            any(Integer.class),
            any(Integer.class),
            any(Integer.class),
            any(String.class),
            any(String.class),
            any(Integer.class),
            any(Integer.class),
            any(Integer.class),
            any(Pageable.class)))
        .thenReturn(page);

    // Execute
    Page<AcessoUsuario> result =
        acessoUsuarioRepository.buscarPessoasComFiltros(
            1, 1, 1, 1, "Test User", "12345678901", 1, 1, 1, pageable);

    // Verify
    assertEquals(1, result.getTotalElements());
    assertEquals(usuario, result.getContent().get(0));
  }
}
