package br.com.sinergico.controller.cadastral;

import br.com.entity.cadastral.B2bFaturaPix;
import br.com.entity.cadastral.CargaAutoProdutoContaBancaria;
import br.com.entity.cadastral.CargaAutoProdutoParametro;
import br.com.entity.cadastral.PreLancamentoLote;
import br.com.entity.suporte.CargaAutoParametros;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.adq.AtualizarParametrosCargaVO;
import br.com.json.bean.adq.AtualizarProdutoBancarioVO;
import br.com.json.bean.adq.CargasVO;
import br.com.json.bean.adq.ParametrosCargaProdutosBancariosVO;
import br.com.json.bean.adq.ParametrosCargaProdutosVO;
import br.com.json.bean.adq.ParametrosCargaVO;
import br.com.json.bean.adq.QrcodeFaturaVO;
import br.com.json.bean.cadastral.CountResponseLong;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.cadastral.B2bFaturaPixService;
import br.com.sinergico.service.cadastral.CargaAutoProdutoContaBancariaService;
import br.com.sinergico.service.cadastral.CargaAutoProdutoParametroService;
import br.com.sinergico.service.cadastral.ParametroCargaService;
import br.com.sinergico.service.cadastral.PreLancamentoLoteService;
import br.com.sinergico.service.suporte.CargaAutoParametrosService;
import br.com.sinergico.vo.FaturasEPedidosFiltroVO;
import br.com.sinergico.vo.LancamentoPedidosVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/parametro-carga")
public class ParametrosCargasController extends UtilController {

  @Autowired private ParametroCargaService service;

  @Autowired CargaAutoParametrosService cargaAutoParametrosService;

  @Autowired CargaAutoProdutoParametroService cargaAutoProdutoParametroService;

  @Autowired CargaAutoProdutoContaBancariaService cargaAutoProdutoContaBancariaService;

  @Autowired B2bFaturaPixService b2bFaturaPixService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @ApiOperation(value = "Listar faturas e Pedidos de um produto integração e replicacao")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/busca-pedidos/integracao/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_INTEGRACAO"})
  public ResponseEntity<?> buscaPedidos(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LancamentoPedidosVO> retorno =
        service.buscaPedidosIntegracao(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar faturas e Pedidos de um produto integração e replicacao")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_INTEGRACAO"})
  public ResponseEntity<CountResponseLong> countPedidosEFaturas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count = service.countPedidosEFaturas(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Listar cargas pagas agendadas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/cargas-pagas/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_INTEGRACAO"})
  public ResponseEntity<?> cargasPagasAgendadas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LancamentoPedidosVO> retorno =
        service.buscarCargas(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar cargas pagas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/cargas/pagas/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_INTEGRACAO"})
  public ResponseEntity<CountResponseLong> countCargasPagasAgendadas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count = service.countCargas(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Listar cargas processadas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/cargas-processadas/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_INTEGRACAO"})
  public ResponseEntity<?> cargasProcessadas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LancamentoPedidosVO> retorno =
        service.buscarCargas(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar cargas processadas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/cargas/processadas/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_INTEGRACAO"})
  public ResponseEntity<CountResponseLong> countCargasProcessadas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count = service.countCargas(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Reagendar Cargas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.PUT,
      path = "/reagendar-cargas",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_REAGENDAR_CARGAS_INTEGRACAO"})
  public ResponseEntity<?> reagendarCargas(@RequestBody CargasVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<PreLancamentoLote> retorno = service.reagendarCarga(model, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "cancelar lote por id", response = PreLancamentoLote.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cancelar",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CANCELAR_LOTE_BY_ID"})
  public ResponseEntity<?> cancelarLote(@RequestBody CargasVO model) {
    // Buscando o usuário logado e checando permissão
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    List<PreLancamentoLote> lote =
        service.alterarStatusLote(
            model, PreLancamentoLoteService.STATUS_CANCELADO, user.getIdUsuario());

    return new ResponseEntity<>(lote, HttpStatus.OK);
  }

  @ApiOperation(value = "Confirmar o pagamento manual da fatura")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/confirma-pagamento", method = RequestMethod.PUT)
  @Secured({"ROLE_CONFIRMAR_PGTO_MANUAL_FATURA"})
  public void confirmaPgtoManual(@RequestBody CargasVO model) throws GenericServiceException {
    SecurityUser user = getAuthenticatedUser(request, em);
    service.confirmarPagamento(model, user);
  }

  @ApiOperation(value = "Salvar parametros de carga")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/salvar-parametros",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_PARAMETROS_CARGA"})
  public ResponseEntity<?> salvarParametros(@RequestBody ParametrosCargaVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<CargaAutoParametros> parametros = cargaAutoParametrosService.salvarParametros(model, user);

    return new ResponseEntity<>(parametros, HttpStatus.OK);
  }

  @ApiOperation(value = "Salvar produtos de carga")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/salvar-produtos/carga",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_PRODUTOS_CARGA"})
  public ResponseEntity<?> salvarProdutosCarga(@RequestBody ParametrosCargaProdutosVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Boolean cargaAutoProdutoParametroList =
        cargaAutoProdutoParametroService.salvarProdutosCarga(model, user);

    return new ResponseEntity<>(cargaAutoProdutoParametroList, HttpStatus.OK);
  }

  @ApiOperation(value = "Salvar produtos conta bancaria")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/salvar-conta/bancaria",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_CONTA_BANCARIA"})
  public ResponseEntity<?> salvarProdutosContaBancaria(
      @RequestBody ParametrosCargaProdutosBancariosVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Boolean list = cargaAutoProdutoContaBancariaService.salvarProdutosContaBancaria(model, user);

    return new ResponseEntity<>(list, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar parametros")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-parametros/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PARAMETROS_CARGA"})
  public ResponseEntity<List<CargaAutoParametros>> buscarParametros(
      @PathVariable Integer idInstituicao) {

    List<CargaAutoParametros> cargaAutoParametrosList =
        cargaAutoParametrosService.buscarParametros(idInstituicao);

    return new ResponseEntity<>(cargaAutoParametrosList, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar parametros carga produto")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-parametros/produto/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PARAMETROS_CARGA_PRODUTOS"})
  public ResponseEntity<List<CargaAutoProdutoParametro>> buscarParametrosProduto(
      @PathVariable Integer idProdInstituicao) {

    List<CargaAutoProdutoParametro> list =
        cargaAutoProdutoParametroService.findIdProdutoInstituicao(idProdInstituicao);

    return new ResponseEntity<>(list, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar parametros carga produto dados bancarios")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-parametros/produto/banco/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PARAMETROS_CARGA_PRODUTOS_BANCARIOS"})
  public ResponseEntity<List<CargaAutoProdutoContaBancaria>> buscarProdutosContaBancaria(
      @PathVariable Integer idProdInstituicao) {

    List<CargaAutoProdutoContaBancaria> produtoContaBancariaList =
        cargaAutoProdutoContaBancariaService.buscarProdutosContaBancaria(idProdInstituicao);

    return new ResponseEntity<>(produtoContaBancariaList, HttpStatus.OK);
  }

  @ApiOperation(value = "Atualizar produtos de carga")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizar-produtos/carga",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATUALIZAR_PRODUTOS_CARGA"})
  public ResponseEntity<?> atualizarProdutosCarga(@RequestBody AtualizarParametrosCargaVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Boolean cargaAutoProdutoParametroList =
        cargaAutoProdutoParametroService.atualizarProdutosCarga(model, user);

    return new ResponseEntity<>(cargaAutoProdutoParametroList, HttpStatus.OK);
  }

  @ApiOperation(value = "Atualizar produtos de carga")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizar-produtos/bancarios/{id}",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATUALIZAR_PRODUTOS_BANCARIOS"})
  public ResponseEntity<?> atualizarProdutosBancarios(
      @PathVariable Long id, @RequestBody AtualizarProdutoBancarioVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Boolean cargaAutoProdutoParametroList =
        cargaAutoProdutoContaBancariaService.atualizarProdutoContaBancaria(id, model, user);

    return new ResponseEntity<>(cargaAutoProdutoParametroList, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar faturas integracao")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-faturas/{idFatura}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_FATURAS_CARGA"})
  public ResponseEntity<?> buscarFaturasIntegracao(@PathVariable Long idFatura) {

    String cargaAutoProdutoParametro =
        cargaAutoProdutoParametroService.buscarFaturasIntegracao(idFatura);

    return new ResponseEntity<>(cargaAutoProdutoParametro, HttpStatus.OK);
  }

  @ApiOperation(value = "Salvar qrcode pix ao gerar uma fatura")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/salvar-qrcode",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_QRCODE_PIX_FATURA"})
  public ResponseEntity<?> salvarQrcode(@RequestBody QrcodeFaturaVO model) {

    Boolean cargaAutoProdutoParametroList = b2bFaturaPixService.salvarQrcodeFatura(model);

    return new ResponseEntity<>(cargaAutoProdutoParametroList, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar pedidos transferência")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/buscar/pedito-transferencia/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PEDIDOS_TRANSFERENCIA"})
  public ResponseEntity<?> listarPedidosTransferencia(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LancamentoPedidosVO> retorno =
        service.buscarPedidosTransferencia(model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar pedidos transferência")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/pedido-transferencia/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PEDIDOS_TRANSFERENCIA"})
  public ResponseEntity<CountResponseLong> countPedidosTransferencia(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count = service.countPedidosTransferencia(model, idProcessadora, idInstituicao, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar cargas pagas de produtos transferência")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/listar/pedito-transferencia/pagas/{idProcessadora}/{idInstituicao}/{carga}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CARGAS_PAGAS_TRANSFERENCIA"})
  public ResponseEntity<?> listarPedidosTransferenciaPagas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Boolean carga) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LancamentoPedidosVO> retorno =
        service.buscarCargasPagasAgendadasTransferencia(
            model, idProcessadora, idInstituicao, carga, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar cargas pagas de produtos transferência")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/pedido-transferencia/pagas/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CARGAS_PAGAS_TRANSFERENCIA"})
  public ResponseEntity<CountResponseLong> countPedidosTransferenciaPagas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count = service.countPedidosTransferenciaPagas(model, idProcessadora, idInstituicao, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar faturas geradas por pix")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-faturas-pix/{idFatura}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_FATURAS_CARGA"})
  public ResponseEntity<?> buscarFaturasPix(@PathVariable Long idFatura) {

    B2bFaturaPix b2bFaturaPix = b2bFaturaPixService.encontraPorIdFatura(idFatura);

    return new ResponseEntity<>(b2bFaturaPix, HttpStatus.OK);
  }

  @ApiOperation(value = "Listar faturas e pedidos de confirmacao manual de pagamentos de carga")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/listar/confirmacao-pedido/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CONFIRMACAO_PEDIDO_CARGA"})
  public ResponseEntity<?> listarConfirmacaoPedido(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    List<LancamentoPedidosVO> retorno =
        service.listarConfirmacaoPedido(model, idProcessadora, idInstituicao);
    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar faturas e pedidos de confirmacao manual de pagamentos de carga")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_CONFIRMACAO_PEDIDO_CARGA"})
  public ResponseEntity<CountResponseLong> contarFaturasEPedidosConfirmacao(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    Long count = service.contarFaturasEPedidosConfirmacao(model, idProcessadora, idInstituicao);
    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Listar faturas pagas agendadas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "listar/faturas-pagas/agendadas/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_PAGAS_CARGA"})
  public ResponseEntity<?> listarFaturasPagasAgendadas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    List<LancamentoPedidosVO> retorno =
        service.listarFaturasEPedidosCargas(model, idProcessadora, idInstituicao);
    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar faturas pagas agendadas")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/faturas-pagas/agendadas/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_FATURAS_PAGAS_CARGA"})
  public ResponseEntity<CountResponseLong> contasFaturasPagasAgendadas(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    Long count = service.contarlistarFaturasEPedidosCargas(model, idProcessadora, idInstituicao);
    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Listar pedidos ja processados")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/pedidos-processados/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PEDIDOS_PROCESSADOS_CARGA"})
  public ResponseEntity<?> pedidosProcessados(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    List<LancamentoPedidosVO> retorno =
        service.listarFaturasEPedidosCargas(model, idProcessadora, idInstituicao);
    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar pedidos ja processados")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/count/pedidos-processados/{idProcessadora}/{idInstituicao}",
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PEDIDOS_PROCESSADOS_CARGA"})
  public ResponseEntity<CountResponseLong> contarPedidosProcessados(
      @RequestBody FaturasEPedidosFiltroVO model,
      @PathVariable Integer idProcessadora,
      @PathVariable Integer idInstituicao) {

    Long count = service.contarlistarFaturasEPedidosCargas(model, idProcessadora, idInstituicao);
    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }
}
