package br.com.sinergico.service.suporte;

import static br.com.sinergico.service.transacional.ComprovantePagamentoService.PAGAMENTO_COM_SUCESSO;
import static br.com.sinergico.service.transacional.ComprovantePagamentoService.PAGAMENTO_EM_PROCESSAMENTO;
import static br.com.sinergico.service.transacional.ComprovantePagamentoService.PAGAMENTO_ESTORNADA;
import static br.com.sinergico.service.transacional.ComprovantePagamentoService.SEM_COMPROVANTE;
import static br.com.sinergico.service.transacional.ComprovantePagamentoService.TED_COM_SUCESSO;
import static br.com.sinergico.service.transacional.ComprovantePagamentoService.TED_EM_PROCESSAMENTO;
import static br.com.sinergico.service.transacional.ComprovantePagamentoService.TED_ESTORNADA;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.gatewaypagto.LogPagtoTituloTransacao;
import br.com.entity.gatewaypagto.LogPagtoTituloValidacao;
import br.com.entity.gatewaypagto.LogRecargaTransacao;
import br.com.entity.jcard.LogTransacoes;
import br.com.entity.pix.ContaTransacional;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.suporte.MigracaoMulticontasExtrato;
import br.com.entity.transacional.TED;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.pix.response.vo.MovimentoInfoVO;
import br.com.json.bean.suporte.ExtratoMigradoVO;
import br.com.json.bean.suporte.ExtratoRequestVo;
import br.com.json.bean.transacional.ComprovantePagamentoMigradosRequest;
import br.com.json.bean.transacional.GetExtratoCredencialInmais;
import br.com.sinergico.enums.MotivoTEDEnum;
import br.com.sinergico.enums.StatusTransacao;
import br.com.sinergico.enums.TipoTransacaoEnum;
import br.com.sinergico.facade.cadastral.CredencialFacade;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloTransacaoRepository;
import br.com.sinergico.repository.gatewaypagto.LogPagtoTituloValidacaoRepository;
import br.com.sinergico.repository.suporte.MigracaoMultiContasExtratoRepository;
import br.com.sinergico.repository.transacional.TEDRepository;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.jcard.LogTransacoesService;
import br.com.sinergico.service.loyalty.LogRecargaTransacaoService;
import br.com.sinergico.service.pix.ContaTransacionalMovimentoService;
import br.com.sinergico.service.pix.ContaTransacionalService;
import br.com.sinergico.service.pix.InstituicaoPixService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.service.transacional.ComprovantePagamentoService;
import br.com.sinergico.service.transacional.MigracaoTransacaoInmaisService;
import br.com.sinergico.util.AnoMesUtil;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.ComprovanteMovimentoPixVO;
import br.com.sinergico.vo.ComprovantePagamentoResponseVO;
import br.com.sinergico.vo.CredenciaisMigradasRequest;
import br.com.sinergico.vo.CredenciaisMigradasVO;
import br.com.sinergico.vo.DadosExtratoVO;
import br.com.sinergico.vo.TransacaoBoletoResponseVO;
import br.com.sinergico.vo.TransacaoCompraEloResponseVO;
import br.com.sinergico.vo.TransacaoRecargaCelularResponseVO;
import br.com.sinergico.vo.TransacaoTedResponseVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
public class MigracaoMulticontasExtratoService
    extends GenericService<MigracaoMulticontasExtrato, Long> {

  private MigracaoMultiContasExtratoRepository migracaoMultiContasExtratoRepository;

  private static final String INMAIS_STATUS_MIGRACAO = "730";

  @Autowired private LogPagtoTituloTransacaoRepository logPagtoTituloTransacaoRepository;

  @Autowired private LogPagtoTituloValidacaoRepository logPagtoTituloValidacaoRepository;

  @Autowired private TEDRepository tedRepository;

  @Autowired ContaPagamentoService contaPagamentoService;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private ContaTransacionalService contaTransacionalService;

  @Autowired private ContaTransacionalMovimentoService contaTransacionalMovimentoService;

  @Autowired private LogTransacoesService logTransacoesService;

  @Autowired private LogRecargaTransacaoService logRecargaTransacaoService;

  @Autowired private ComprovantePagamentoService comprovantePagamentoService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private MigracaoTransacaoInmaisService migracaoTransacaoInmaisService;

  @Autowired private CredencialFacade credencialFacade;

  private final Logger logger = LoggerFactory.getLogger(ComprovantePagamentoService.class);

  @Autowired
  public MigracaoMulticontasExtratoService(MigracaoMultiContasExtratoRepository repo) {
    super(repo);
    migracaoMultiContasExtratoRepository = repo;
  }

  public List<ExtratoMigradoVO> buscarExtratoContasMigradas(
      ExtratoRequestVo extratoRequestVo, SecurityUserPortador userPortador) {

    contaPagamentoService.validaIdContaPeloRequestEPortador(
        extratoRequestVo.getIdConta(), userPortador);

    return getExtrato(extratoRequestVo);
  }

  public List<ExtratoMigradoVO> getExtrato(ExtratoRequestVo extratoRequestVo) {

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(extratoRequestVo.getIdConta());

    List<ExtratoMigradoVO> extrato =
        migracaoMultiContasExtratoRepository.buscarDadosContasMigradas(
            extratoRequestVo.getDataInicial(), extratoRequestVo.getDataFinal(), conta.getIdConta());

    return extrato;
  }

  public ComprovantePagamentoResponseVO buscarComprovanteContasMigradas(
      ComprovantePagamentoMigradosRequest comprovantePagamentoMigradosRequest,
      SecurityUserPortador userPortador) {

    if ((comprovantePagamentoMigradosRequest.getRrn() == null
            || comprovantePagamentoMigradosRequest.getRrn().isEmpty())
        && comprovantePagamentoMigradosRequest.getIdTranlog() == null
        && (comprovantePagamentoMigradosRequest.getTransactionId() == null
            || comprovantePagamentoMigradosRequest.getTransactionId().isEmpty())) {
      throw new GenericServiceException(
          "ID da Transação não informado", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    if (comprovantePagamentoMigradosRequest.getIdConta() == null) {
      throw new GenericServiceException("idConta não informado", HttpStatus.UNPROCESSABLE_ENTITY);
    }

    contaPagamentoService.validaIdContaPeloRequestEPortador(
        (comprovantePagamentoMigradosRequest.getIdConta()), userPortador);

    ComprovantePagamentoResponseVO comprovante = new ComprovantePagamentoResponseVO();

    try {
      if (Objects.equals(
          comprovantePagamentoMigradosRequest.getTipoTransacao(),
          TipoTransacaoEnum.BOLETO.getCodigo())) {
        LogPagtoTituloTransacao logPagtoTituloTransacao =
            logPagtoTituloTransacaoRepository.findPagamentosByIdContaAndTransacao(
                comprovantePagamentoMigradosRequest.getIdContaOrigem(),
                comprovantePagamentoMigradosRequest.getRrn());
        if (logPagtoTituloTransacao != null) {

          if (logPagtoTituloTransacao.getProtocoloIdRendimento() == null
              && logPagtoTituloTransacao.getProtocoloId() != null) {
            LogPagtoTituloValidacao logPagtoTituloValidacao =
                logPagtoTituloValidacaoRepository.findPagamentoTituloValidacaoByIdLogPag(
                    logPagtoTituloTransacao.getIdLogPagtoTitulo());

            comprovante.setBoleto(new TransacaoBoletoResponseVO());
            comprovante.setStatus(HttpStatus.OK.value());
            // comprovante.setStatusOperacao(StatusTransacao.TRANSFERIDO.getCodigo());
            comprovante.setTituloStatusModal(PAGAMENTO_COM_SUCESSO);
            // comprovante.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());

            comprovantePagamentoService.montarComprovanteBoletoResponse(
                comprovante,
                logPagtoTituloTransacao.getIdConta().toString(),
                logPagtoTituloValidacao.getCodigoBarras() != null
                    ? logPagtoTituloValidacao.getCodigoBarras()
                    : logPagtoTituloValidacao.getLinhaDigitavel(),
                logPagtoTituloValidacao.getNomeBeneficiario(),
                logPagtoTituloValidacao.getNomeSacado(),
                logPagtoTituloValidacao.getDataLiquidacao(),
                logPagtoTituloTransacao.getDataHoraInicioPagto(),
                logPagtoTituloTransacao.getProtocoloIdRendimento(),
                logPagtoTituloValidacao.getValor() != null
                    ? Util.currencyFormat(BigDecimal.valueOf(logPagtoTituloValidacao.getValor()))
                    : null,
                logPagtoTituloTransacao.getValor() != null
                    ? Util.currencyFormat(BigDecimal.valueOf(logPagtoTituloTransacao.getValor()))
                    : null,
                logPagtoTituloTransacao.getAutenticacaoAPI());
          } else {
            if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
              comprovante.setTituloStatusModal(PAGAMENTO_EM_PROCESSAMENTO);
              comprovante.setDescricaoStatus(StatusTransacao.AGUARDANDO_APROVACAO.getDescricao());
            } else if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.TRANSFERIDO.getCodigo())) {

              LogPagtoTituloValidacao logPagtoTituloValidacao =
                  logPagtoTituloValidacaoRepository.findPagamentoTituloValidacaoByIdLogPag(
                      logPagtoTituloTransacao.getIdLogPagtoTitulo());

              comprovante.setBoleto(new TransacaoBoletoResponseVO());
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.TRANSFERIDO.getCodigo());
              comprovante.setTituloStatusModal(PAGAMENTO_COM_SUCESSO);
              comprovante.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());

              if (logPagtoTituloTransacao.getProtocoloIdRendimento() != null) {

                comprovantePagamentoService.montarComprovanteBoletoResponse(
                    comprovante,
                    logPagtoTituloTransacao.getIdConta().toString(),
                    logPagtoTituloValidacao.getCodigoBarras() != null
                        ? logPagtoTituloValidacao.getCodigoBarras()
                        : logPagtoTituloValidacao.getLinhaDigitavel(),
                    logPagtoTituloValidacao.getNomeBeneficiario(),
                    logPagtoTituloValidacao.getNomeSacado(),
                    logPagtoTituloValidacao.getDataLiquidacao(),
                    logPagtoTituloTransacao.getDataHoraInicioPagto(),
                    logPagtoTituloTransacao.getProtocoloIdRendimento(),
                    logPagtoTituloValidacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloValidacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloTransacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getAutenticacaoRendimento());

              } else if (logPagtoTituloTransacao.getProtocoloId() != null) {
                comprovantePagamentoService.montarComprovanteBoletoResponse(
                    comprovante,
                    logPagtoTituloTransacao.getIdConta().toString(),
                    logPagtoTituloValidacao.getCodigoBarras() != null
                        ? logPagtoTituloValidacao.getCodigoBarras()
                        : logPagtoTituloValidacao.getLinhaDigitavel(),
                    logPagtoTituloValidacao.getNomeBeneficiario(),
                    logPagtoTituloValidacao.getNomeSacado(),
                    logPagtoTituloValidacao.getDataLiquidacao(),
                    logPagtoTituloTransacao.getDataHoraInicioPagto(),
                    logPagtoTituloTransacao.getProtocoloId().toString(),
                    logPagtoTituloValidacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloValidacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getValor() != null
                        ? Util.currencyFormat(
                            BigDecimal.valueOf(logPagtoTituloTransacao.getValor()))
                        : null,
                    logPagtoTituloTransacao.getAutenticacao().toString());
              }
            } else if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.REPROVADO.getCodigo())) {
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.REPROVADO.getCodigo());
              comprovante.setTituloStatusModal(PAGAMENTO_ESTORNADA);
              comprovante.setDescricaoStatus(StatusTransacao.REPROVADO.getDescricao());
              comprovante.setMotivo(logPagtoTituloTransacao.getTxMotivoRecusRend());
            } else if (logPagtoTituloTransacao
                .getStatusPagamento()
                .equals(StatusTransacao.REJEITADO.getCodigo())) {
              comprovante.setStatus(HttpStatus.OK.value());
              comprovante.setStatusOperacao(StatusTransacao.REJEITADO.getCodigo());
              comprovante.setTituloStatusModal(TED_ESTORNADA);
              comprovante.setDescricaoStatus(
                  "Não foi possível realizar a transação. A transação será estornada");
              comprovante.setMotivo(logPagtoTituloTransacao.getTxMotivoRecusRend());
            } else {
              comprovante.setStatus(HttpStatus.NOT_FOUND.value());
              comprovante.setTituloStatusModal(SEM_COMPROVANTE);
              comprovante.setDescricaoStatus(
                  "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
              logger.warn("Pagamento não encontrado para o idTransacao informado");
            }
          }
        }
      } else if (Objects.equals(
          comprovantePagamentoMigradosRequest.getTipoTransacao(),
          TipoTransacaoEnum.TED.getCodigo())) {
        TED ted =
            tedRepository.findTedByIdTransacaoAndIdConta(
                comprovantePagamentoMigradosRequest.getRrn(),
                comprovantePagamentoMigradosRequest.getIdContaOrigem());
        if (ted != null) {
          if (ted.getStatusTed().equals(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo())) {
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(StatusTransacao.AGUARDANDO_APROVACAO.getCodigo());
            comprovante.setTituloStatusModal(TED_EM_PROCESSAMENTO);
            comprovante.setDescricaoStatus(StatusTransacao.AGUARDANDO_APROVACAO.getDescricao());
          } else if (ted.getStatusTed().equals(StatusTransacao.TRANSFERIDO.getCodigo())) {
            comprovante.setTed(new TransacaoTedResponseVO());
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(ted.getStatusTed());
            comprovante.setTituloStatusModal(TED_COM_SUCESSO);
            comprovante.setDescricaoStatus(StatusTransacao.TRANSFERIDO.getDescricao());
            if (ted.getIdTransacaoRendimento() != null) {

              comprovantePagamentoService.montarComprovanteTedResponse(
                  comprovante,
                  ted.getRrn(),
                  DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, ted.getDataHoraInclusao()),
                  ted.getNomeRemetente(),
                  ted.getNomeFavorecido(),
                  ted.getBancoFavorecido(),
                  ted.getAgenciaFavorecido(),
                  ted.getContaFavorecido(),
                  Util.currencyFormat(ted.getValorTransferencia()));

            } else if (ted.getTransactionId() != null) {
              comprovantePagamentoService.montarComprovanteTedResponse(
                  comprovante,
                  ted.getRrn(),
                  DateUtil.dateFormat(DateUtil.DD_MM_YYYY_HH_MM_SS, ted.getDataHoraInclusao()),
                  ted.getNomeRemetente(),
                  ted.getNomeFavorecido(),
                  ted.getBancoFavorecido(),
                  ted.getAgenciaFavorecido(),
                  ted.getContaFavorecido(),
                  Util.currencyFormat(ted.getValorTransferencia()));
            }
          } else if (ted.getStatusTed().equals(StatusTransacao.REPROVADO.getCodigo())) {
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(ted.getStatusTed());
            comprovante.setTituloStatusModal(TED_ESTORNADA);
            comprovante.setDescricaoStatus(StatusTransacao.REPROVADO.getDescricao());
            comprovante.setMotivo(
                MotivoTEDEnum.valueOfCodigo(ted.getIdMotivoRecusado()).getDescricao());
          } else if (ted.getStatusTed().equals(StatusTransacao.REJEITADO.getCodigo())) {
            comprovante.setStatus(HttpStatus.OK.value());
            comprovante.setStatusOperacao(ted.getStatusTed());
            comprovante.setTituloStatusModal(TED_ESTORNADA);
            comprovante.setDescricaoStatus(
                "Não foi possível realizar a transação. A transação será estornada");
            comprovante.setMotivo(
                MotivoTEDEnum.valueOfCodigo(ted.getIdMotivoRecusado()).getDescricao());
          }
        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
          logger.warn("TED não encontrada para o idTransacao informado");
        }
      } else if (Objects.equals(
          comprovantePagamentoMigradosRequest.getTipoTransacao(),
          TipoTransacaoEnum.PIX.getCodigo())) {

        Long idConta = comprovantePagamentoMigradosRequest.getIdContaOrigem();
        ContaPagamento contaPagamento =
            contaPagamentoService.buscarPorContaId(
                comprovantePagamentoMigradosRequest.getIdContaOrigem());

        InstituicaoPix instituicaoPix =
            instituicaoPixService.buscarInstituicaoPixPorProduto(
                contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());

        ContaTransacional contaTransacional =
            contaTransacionalService
                .findByIdConta(idConta)
                .orElseGet(
                    () -> contaTransacionalService.criarContaTransacional(idConta, instituicaoPix));

        ComprovanteMovimentoPixVO comprovanteMovimentoPixVO =
            contaTransacionalMovimentoService.buscarExtratoPorRrn(
                contaTransacional.getConta().getIdConta() + contaTransacional.getDvConta(),
                contaTransacional.getAgencia().toString(),
                comprovantePagamentoMigradosRequest.getRrn());

        if (comprovanteMovimentoPixVO != null) {
          comprovante.setPix(new MovimentoInfoVO());
          comprovante.setStatus(HttpStatus.OK.value());
          comprovantePagamentoService.montarComprovantePixResponse(
              comprovante, comprovanteMovimentoPixVO);
        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante pix. Favor, entre em contato com o suporte.");
        }
      } else if (Objects.equals(
          comprovantePagamentoMigradosRequest.getTipoTransacao(),
          Servicos.TRANSFERENCIA_INTERNA_PORTADOR.getId())) {

        Long idConta = comprovantePagamentoMigradosRequest.getIdConta();
        contaPagamentoService.validaIdContaPeloRequestEPortador((idConta), userPortador);

        LogTransacoes logTransacoes =
            logTransacoesService.findByRrn(comprovantePagamentoMigradosRequest.getRrn());

        if (logTransacoes != null) {
          comprovante.setStatus(HttpStatus.OK.value());

          comprovantePagamentoService.montarComprovanteTransferenciaResponse(
              comprovante, logTransacoes);

        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        }

      } else if (Objects.equals(
          comprovantePagamentoMigradosRequest.getTipoTransacao(), Servicos.QRCODE_ELO.getId())) {

        Long idConta = comprovantePagamentoMigradosRequest.getIdContaOrigem();
        ContaPagamento contaPagamento = contaPagamentoService.findByIdConta(idConta);

        LogTransacoes logTransacoes =
            logTransacoesService.findByIdTranlogOrQrCodeTransactionIdIsQrCodeElo(
                comprovantePagamentoMigradosRequest.getIdTranlog(),
                comprovantePagamentoMigradosRequest.getTransactionId());

        if (logTransacoes != null) {
          comprovante.setStatus(HttpStatus.OK.value());

          comprovante.setCompraElo(new TransacaoCompraEloResponseVO());
          comprovante.getCompraElo().setRrn(comprovantePagamentoMigradosRequest.getRrn());

          comprovantePagamentoService.montarComprovanteCompraCartaoResponse(
              comprovante, logTransacoes, contaPagamento);

        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        }

      } else if (Objects.equals(
          comprovantePagamentoMigradosRequest.getTipoTransacao(), Servicos.RECARGA.getId())) {

        Long idConta = comprovantePagamentoMigradosRequest.getIdContaOrigem();
        ContaPagamento contaPagamento = contaPagamentoService.findByIdConta(idConta);

        LogRecargaTransacao logRecargaTransacao =
            logRecargaTransacaoService.findByIdTransacaoPagto(
                comprovantePagamentoMigradosRequest.getRrn());

        if (logRecargaTransacao != null) {
          comprovante.setStatus(HttpStatus.OK.value());

          comprovante.setRecargaCelular(new TransacaoRecargaCelularResponseVO());
          comprovante.getRecargaCelular().setRrn(comprovantePagamentoMigradosRequest.getRrn());

          comprovantePagamentoService.montarComprovanteRecargaCelularResponse(
              comprovante, logRecargaTransacao, contaPagamento);

        } else {
          comprovante.setStatus(HttpStatus.NOT_FOUND.value());
          comprovante.setTituloStatusModal(SEM_COMPROVANTE);
          comprovante.setDescricaoStatus(
              "Não foi possível recuperar o comprovante. Favor, entre em contato com o suporte.");
        }
      }
    } catch (Exception e) {
      logger.error(e.getMessage());
      e.printStackTrace();
      throw new GenericServiceException(e.getMessage());
    }
    return comprovante;
  }

  public List<GetExtratoCredencialInmais> baixarExtratoContasMigradas(
      DadosExtratoVO dadosExtrato, Boolean maisRecente) {

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(dadosExtrato.getIdContaOrigem());

    if (conta == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar a conta: " + dadosExtrato.getIdContaOrigem());
    }

    List<Credencial> credenciais =
        credencialService.recuperarCredenciaisPorIdConta(conta.getIdConta());

    Long idCredencial =
        credenciais.stream()
            .sorted(
                new Comparator<Credencial>() {

                  @Override
                  public int compare(Credencial o1, Credencial o2) {
                    return o1.getCsn().compareTo(o2.getCsn());
                  }
                })
            .findFirst()
            .get()
            .getIdCredencial();

    List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList = new ArrayList<>();
    if (dadosExtrato.getAnoMes() == AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {

      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpf(
                  dadosExtrato.getDocumento(),
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(dadosExtrato.getAnoMes()),
                  DateUtil.getCalendarUltimaMigracaoINMAIS_DATA().getTime())));
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idCredencial,
                      DateUtil.getCalendarMigracaoTerminadaINMAIS_DATA().getTime(),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(dadosExtrato.getAnoMes()),
                      null)));
    } else if (dadosExtrato.getAnoMes() > AnoMesUtil.ANO_MES_DATA_MIGRACAO_INMAIS) {
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convertExtratoCredencial(
              getCredencialService()
                  .getExtrato(
                      idCredencial,
                      AnoMesUtil.getPrimeiroDiaFromAnoMes(dadosExtrato.getAnoMes()),
                      AnoMesUtil.getUltimoDiaDateFromAnoMes(dadosExtrato.getAnoMes()),
                      dadosExtrato.getIdContaOrigem())));
    } else {
      getExtratoCredencialInmaisList.addAll(
          GetExtratoCredencialInmais.convert(
              migracaoTransacaoInmaisService.findByCpf(
                  dadosExtrato.getDocumento(),
                  AnoMesUtil.getPrimeiroDiaFromAnoMes(dadosExtrato.getAnoMes()),
                  AnoMesUtil.getUltimoDiaDateFromAnoMes(dadosExtrato.getAnoMes()))));
    }
    getExtratoCredencialInmaisList =
        removeStatusFromExtrato(getExtratoCredencialInmaisList, INMAIS_STATUS_MIGRACAO);

    if (Boolean.TRUE.equals(maisRecente)) {
      getExtratoCredencialInmaisList.sort(
          Comparator.comparing(GetExtratoCredencialInmais::getDataTransacao).reversed());
    } else {
      getExtratoCredencialInmaisList.sort(
          Comparator.comparing(GetExtratoCredencialInmais::getDataTransacao));
    }
    return getExtratoCredencialInmaisList;
  }

  private List<GetExtratoCredencialInmais> removeStatusFromExtrato(
      List<GetExtratoCredencialInmais> getExtratoCredencialInmaisList, String STATUS) {
    List<GetExtratoCredencialInmais> retorno = new ArrayList<>();
    for (GetExtratoCredencialInmais item : getExtratoCredencialInmaisList) {
      if (item.getStatusTransacao() == null || !item.getStatusTransacao().equals(STATUS)) {
        retorno.add(item);
      }
    }
    return retorno;
  }

  public Boolean alterarStatusCredenciais(CredenciaisMigradasVO credenciaisMigradasVO) {

    Boolean sucesso = null;
    for (CredenciaisMigradasRequest credencial : credenciaisMigradasVO.getCredenciais()) {
      sucesso =
          credencialService.alterarStatusCredencial(
              credencial.getIdCredencial(),
              credenciaisMigradasVO.getStatusDestino(),
              Constantes.ID_USUARIO_INCLUSAO_PORTADOR,
              false);
    }

    return sucesso;
  }
}
