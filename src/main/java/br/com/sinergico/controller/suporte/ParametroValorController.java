package br.com.sinergico.controller.suporte;

import br.com.entity.suporte.ParametroValor;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.service.suporte.ParametroValorService;
import br.com.sinergico.util.Constantes;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/parametro-valor")
public class ParametroValorController extends UtilController {

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private ParametroValorService parametroValorService;

  @ApiOperation(
      value = "Retorna parâmetro booleano 126 de configuração interna.",
      response = Boolean.class)
  @RequestMapping(
      value = "/aplicativo-ativo",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public String getParametro() {

    return parametroValorService.findValorParametroString(Constantes.PARAMETRO_ATIVACAO_APLICATIVO);
  }

  @ApiOperation(
      value = "Busca parametro valor da instituição para validar limites inicais da conta",
      response = ParametroValor.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/instituicao/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_PARAMETRO_VALOR_LIMITE"})
  public List<ParametroValor> getParametroValorInstituicao(@PathVariable Integer idInstituicao) {
    return parametroValorService.getParametroValorInstituicao(idInstituicao);
  }
}
