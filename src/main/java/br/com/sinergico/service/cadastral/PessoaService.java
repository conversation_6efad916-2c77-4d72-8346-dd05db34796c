package br.com.sinergico.service.cadastral;

import static br.com.sinergico.util.Constantes.*;
import static java.lang.Boolean.FALSE;
import static java.lang.Boolean.TRUE;

import br.com.client.rest.jcard.json.bean.Customer;
import br.com.client.rest.jcard.json.bean.GetCardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PessoaResponsavelDependente;
import br.com.entity.cadastral.PessoaResponsavelDependenteId;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoDependentes;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.cadastral.SetorFilial;
import br.com.entity.suporte.Agencia;
import br.com.entity.suporte.Banco;
import br.com.entity.suporte.HierarquiaFilial;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaRegional;
import br.com.entity.suporte.TipoStatus;
import br.com.entity.transacional.LogB2bCadastroViaArquivo;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.AlterarPessoa;
import br.com.json.bean.cadastral.CadastrarContaPagamentoPessoaRequest;
import br.com.json.bean.cadastral.CadastrarPessoaFisica;
import br.com.json.bean.cadastral.CadastrarPessoaJuridica;
import br.com.json.bean.cadastral.CadastrarPessoaPDAFRequest;
import br.com.json.bean.cadastral.CadastrarRepresentanteLegalRequest;
import br.com.json.bean.cadastral.CheckNivelHierarquiaEnum;
import br.com.json.bean.cadastral.ContaPessoaFisica;
import br.com.json.bean.cadastral.ContasCredenciaisReplicaveisReponse;
import br.com.json.bean.cadastral.EnderecoPessoaRequest;
import br.com.json.bean.cadastral.EnderecosPessoaRequest;
import br.com.json.bean.cadastral.FuncionarioCadastroCompletoTO;
import br.com.json.bean.cadastral.FuncionarioCargaTO;
import br.com.json.bean.cadastral.FuncionarioDependenteTO;
import br.com.json.bean.cadastral.FuncionarioPJTO;
import br.com.json.bean.cadastral.FuncionarioProdutoAtivoResponse;
import br.com.json.bean.cadastral.FuncionarioProdutosContas;
import br.com.json.bean.cadastral.FuncionarioProdutosResponse;
import br.com.json.bean.cadastral.FuncionarioProdutosSociaisTO;
import br.com.json.bean.cadastral.FuncionarioResponsavelTO;
import br.com.json.bean.cadastral.FuncionariosModel;
import br.com.json.bean.cadastral.FuncionariosProdutoAtivoModel;
import br.com.json.bean.cadastral.PessoaAdicionalPDAFRequest;
import br.com.json.bean.cadastral.PessoaFisica;
import br.com.json.bean.cadastral.PortadorVinculacaoTO;
import br.com.json.bean.cadastral.ProdutoInstituicaoResponse;
import br.com.json.bean.cadastral.ResponavelDependenteContas;
import br.com.json.bean.cadastral.TelefoneCelularPessoaResponse;
import br.com.json.bean.cadastral.VerificacaoCargaArquivoTO;
import br.com.json.bean.cadastral.VerificacaoCargaArquivoTO.VerificacaoLinhaTO;
import br.com.json.bean.loyalty.CadastrarPessoaLoyaltyRequest;
import br.com.json.bean.loyalty.DadosPreCadastroResponse;
import br.com.json.bean.suporte.ValidaTipoCredencial;
import br.com.json.bean.suporte.ValorCargaProdutoInstituicao;
import br.com.sinergico.enums.EmailBlacklist;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.facade.cadastral.PessoaFacade;
import br.com.sinergico.repository.cadastral.ContaPagamentoRepository;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.repository.cadastral.PessoaRepository;
import br.com.sinergico.repository.cadastral.PessoaResponsavelDependenteRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.jcard.CardService;
import br.com.sinergico.service.jcard.CustomerService;
import br.com.sinergico.service.suporte.AgenciaService;
import br.com.sinergico.service.suporte.BancoService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.LogradouroService;
import br.com.sinergico.service.suporte.SolicitacaoPreCadastroService;
import br.com.sinergico.service.suporte.TipoStatusService;
import br.com.sinergico.service.transacional.LogB2bCadastroViaArquivoService;
import br.com.sinergico.util.Abreviador;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.DocumentoUtil;
import br.com.sinergico.util.FileUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.validator.UtilValidator;
import br.com.sinergico.vo.ContaAtivaB2BFiltroVO;
import br.com.sinergico.vo.ExisteMatriculaEmpresaVO;
import br.com.sinergico.vo.FuncionarioB2BFiltroVO;
import br.com.sinergico.vo.FuncionarioB2BResponseVo;
import br.com.sinergico.vo.LogradouroVO;
import br.com.sinergico.vo.PessoaPortadorCafVO;
import br.com.sinergico.vo.PessoaVO;
import br.com.sinergico.vo.PortadorB2BResponseVO;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.locks.ReentrantLock;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.util.io.pem.PemGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class PessoaService extends GenericService<Pessoa, Long> {

  private static final Logger log = LoggerFactory.getLogger(PessoaService.class);

  private static final String VALOR_INCORRETO = "<<VALOR INCORRETO>>";
  private static final String NAO_INFORMADO = "<<NÃO INFORMADO>>";
  private static final String NAO_INFORMADO_OU_INVALIDO = "<<NÃO INFORMADO OU INVÁLIDO>>";
  private static final String NAO_INFORMADO_DDD = "<<DDD NÃO INFORMADO>>";
  private static final String NAO_INFORMADO_CELULAR = "<<CELULAR NÃO INFORMADO>>";

  private static final int CREDITO = 1;

  private static final Integer GRUPO_STATUS_ATIVO = 1;

  private static final Integer TIPO_INCORRETO = 0;

  private static final String EMAIL_PATTERN =
      "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@"
          + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";

  private static final String NOME_PATTERN = "[^0-9]+";

  private static final String DDD_PATTERN = "[0-9]{2}";

  private static final String TELEFONE_PATTERN = "[0-9]{9}";

  private static final String CEP_PATTERN = "[0-9]{8}";

  private static final String CANTO_BANCARIA_PATTERN = "[0-9]+";

  private static final String TIPO_CANTO_BANCARIA_PATTERN = "[1-4]{1}";

  private static final String ESTADO_CIVIL_PATTERN = "[1-6]{1}";

  private static final String TELEFONE_FIXO_PATTERN = "[0-9]+";

  private static final Pattern patternEmail =
      Pattern.compile(EMAIL_PATTERN, Pattern.CASE_INSENSITIVE);

  private static final Pattern paternDdd = Pattern.compile(DDD_PATTERN);

  private static final Pattern paternTelefone = Pattern.compile(TELEFONE_PATTERN);

  private static final Pattern paternTelefoneFixo = Pattern.compile(TELEFONE_FIXO_PATTERN);

  private static final Pattern paternCep = Pattern.compile(CEP_PATTERN);

  private static final Pattern paternTipoContaBancaria =
      Pattern.compile(TIPO_CANTO_BANCARIA_PATTERN);

  private static final Pattern paternEstadoCivil = Pattern.compile(ESTADO_CIVIL_PATTERN);

  private static final int TAMANHO_NOME_COMPLETO = 80;

  private static final int TAMANHO_NATURALIDADE_NATURALIDADE = 60;

  private static final int TAMANHO_NUMERO_RG = 15;
  private static final int TAMANHO_ORGAO_EMISSOR_RG = 10;
  private static final int TAMANHO_LOGRADOURO_BAIRRO_CIDADE = 100;
  private static final int TAMANHO_EMAIL = 80;
  private static final int TAMANHO_NOME_EXCLUSIVO_CARTAO = 80;
  private static final int TAMANHO_UF = 2;

  private static final Integer ATIVO = 1;
  private static final Integer INATIVO = 2;

  private static final Integer PJ = 2;

  private final ReentrantLock lock = new ReentrantLock();

  private Boolean erroRegistro = Boolean.FALSE;

  private PessoaRepository pessoaRepository;

  @Autowired private ContaPagamentoService contaService;

  @Autowired
  private ProdutoInstituicaoCorrespondenteService produtoInstituicaoCorrespondenteService;

  @Autowired private CardService cardService;

  @Autowired private EmailService emailService;

  @Autowired private CredencialRepository credencialRepository;

  @Lazy @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Lazy @Autowired private HierarquiaPontoRelacionamentoService hierarquiaPntRelacionamentoService;

  @Autowired private RepresentanteLegalService representanteLegalService;

  @Autowired private TipoStatusService tipoStatusService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private SetorFilialService setorFilialService;

  @Autowired private CustomerService customerService;

  @Autowired private ImportadorFuncionarioTxt importadorFuncionarioTxt;

  @Autowired private ImportadorFuncionarioXls importadorFuncionarioXls;

  @Autowired private ContaPessoaService contaPessoaService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private BancoService bancoService;

  @Autowired private AgenciaService agenciaService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private SolicitacaoPreCadastroService preCadastroService;

  @Autowired private ProdutoContratadoService produtoContratadoService;

  @Autowired private ProdutoInstituicaoService produtoInstituicaoService;

  @Autowired private LogradouroService logradouroService;

  @Autowired private LogB2bCadastroViaArquivoService logB2bCadastroViaArquivoService;

  @Autowired private ProdutoInstituicaoDependentesService produtoInstituicaoDependentesService;

  @Autowired private PessoaResponsavelDependenteService pessoaResponsavelDependenteService;

  @Autowired private CartaoQrCodeService cartaoQrCodeService;

  @Autowired private PessoaFacade pessoaFacade;

  @Autowired private PessoaResponsavelDependenteRepository pessoaResponsavelDependenteRepository;

  @Autowired private ContaPagamentoRepository contaPagamentoRepository;

  @Autowired private UtilService utilService;

  @Autowired private EnderecoPessoaService enderecoPessoaService;

  @Value("${issuer.dir.entrada}")
  private String issuerDirEntrada;

  @Autowired
  public PessoaService(PessoaRepository repo) {
    super(repo);
    pessoaRepository = repo;
  }

  public void save(List<Pessoa> pessoas) {
    if (pessoas != null) {
      saveAll(pessoas);
    }
  }

  /**
   * Método, em HQL, responsável por buscar todos os funcionários daquela hierarquia de ponto de
   * relacionamento
   *
   * @param model
   * @param user
   * @return
   */
  public List<FuncionarioProdutosResponse> findFuncionariosByhQL(
      FuncionariosModel model, SecurityUser user) {

    ContaAtivaB2BFiltroVO filtroVO = new ContaAtivaB2BFiltroVO();

    filtroVO.setIdProcessadora(user.getIdProcessadora());
    filtroVO.setIdInstituicao(user.getIdInstituicao());
    filtroVO.setIdRegional(user.getIdRegional());
    filtroVO.setIdFilial(user.getIdFilial());
    filtroVO.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());

    filtroVO.setIdPessoa(model.getIdInterno());
    if (model.getIdProdutoInstituicao() != null)
      filtroVO.setIdProdutoInstituicao(model.getIdProdutoInstituicao());
    if (model.getIdSetorFilial() != null) filtroVO.setIdSetorFilial(model.getIdSetorFilial());
    if (model.getCpf() != null) filtroVO.setCpf(model.getCpf());
    if (model.getMatricula() != null) filtroVO.setMatricula(model.getMatricula().toUpperCase());
    if (model.getNomeCompleto() != null)
      filtroVO.setNomeCompleto(model.getNomeCompleto().toUpperCase());

    filtroVO.setPagina(model.getPagina());
    filtroVO.setQtdRegistros(model.getQtdRegistros());

    filtroVO.setTipoPessoa(model.getTipoPessoa());
    filtroVO.setTitularidade(1);

    if (model.getIdStatusConta() != null) filtroVO.setIdStatusConta(model.getIdStatusConta());

    List<FuncionarioProdutosResponse> funsList = new ArrayList<FuncionarioProdutosResponse>();
    funsList = pessoaRepository.queryBuscaFuncionarioByContaPessoa(filtroVO);

    return funsList;
  }

  public Long countFuncionariosByhQL(FuncionariosModel model, SecurityUser user) {

    // Aproveita o mesmo VO
    ContaAtivaB2BFiltroVO filtroVO = new ContaAtivaB2BFiltroVO();

    filtroVO.setIdProcessadora(user.getIdProcessadora());
    filtroVO.setIdInstituicao(user.getIdInstituicao());
    filtroVO.setIdRegional(user.getIdRegional());
    filtroVO.setIdFilial(user.getIdFilial());
    filtroVO.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    filtroVO.setIdPessoa(model.getIdInterno());
    filtroVO.setTipoPessoa(model.getTipoPessoa());
    filtroVO.setTitularidade(1);
    if (model.getIdProdutoInstituicao() != null)
      filtroVO.setIdProdutoInstituicao(model.getIdProdutoInstituicao());
    if (model.getIdSetorFilial() != null) filtroVO.setIdSetorFilial(model.getIdSetorFilial());
    if (model.getCpf() != null) filtroVO.setCpf(model.getCpf());
    if (model.getMatricula() != null) filtroVO.setMatricula(model.getMatricula());
    if (model.getNomeCompleto() != null)
      filtroVO.setNomeCompleto(model.getNomeCompleto().toUpperCase());

    filtroVO.setPagina(model.getPagina());
    filtroVO.setQtdRegistros(model.getQtdRegistros());

    if (model.getIdStatusConta() != null) filtroVO.setIdStatusConta(model.getIdStatusConta());

    return pessoaRepository.contadorFuncionariosByhQL(filtroVO);
  }

  /**
   * Método, em HQL, responsável por buscar os funcionários com produto ativo
   *
   * @param model
   * @param user
   * @return
   */
  public List<FuncionarioProdutoAtivoResponse> findFuncionariosContaAtivaByhQL(
      FuncionariosProdutoAtivoModel model, SecurityUser user) {

    ContaAtivaB2BFiltroVO filtroVO = createContaAtivaB2BFiltroVO(model, user);

    List<FuncionarioProdutoAtivoResponse> funcionariosLista =
        pessoaRepository.findByContaAtiva(filtroVO);
    funcionariosLista.sort(Comparator.comparing(f -> f.getPessoa().getNomeCompleto()));

    return funcionariosLista;
  }

  public void updatePessoaFisicaPreCadastrada(PessoaFisica pessoaFisica) {
    Pessoa p = pessoaFisica.getPessoa();
    pessoaRepository.save(p);
  }

  public List<Pessoa> getPessoasAdicionaisPDAFByIdConta(Long idConta) {
    return pessoaRepository.findContasPessoasAdicionaisPDAFByIdConta(idConta);
  }

  public Pessoa getPessoaPDAFByDocumentoAndHierarquia(
      String documento,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao) {
    return pessoaRepository.findPessoaProdutoPDAFByDocumento(
        documento,
        ID_PRODUCAO_INSTITUICAO_BRBCARD,
        idRegional,
        idFilial,
        idPontoDeRelacionamento,
        idProdutoInstituicao,
        PESSOA_JURIDICA);
  }

  public ContaPessoa getContaPessoaPDAFByDocumentoAndHierarquia(
      String documento,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao) {
    return pessoaRepository.findContaPagamentoProdutoPDAFByDocumento(
        documento,
        ID_PRODUCAO_INSTITUICAO_BRBCARD,
        idRegional,
        idFilial,
        idPontoDeRelacionamento,
        idProdutoInstituicao,
        PESSOA_JURIDICA);
  }

  public void createPessoaPDAF(
      CadastrarPessoaPDAFRequest request,
      SecurityUser user,
      Integer idProdutoInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      String nomeSetorFilial) {
    List<PessoaAdicionalPDAFRequest> adicionais = new ArrayList<>();

    Map<String, Object> additionalProperties = request.getAdditionalProperties();
    for (int i = 1; i <= 3; i++) {
      String nomeKey = "nome_adc_" + i;
      String dataNascimentoKey = "dt_nascimento_adc_" + i;
      String cpfKey = "cpf_adc_" + i;
      String cargoKey = "cargo_adc_" + i;
      String telefoneKey = "telefone_adc_" + i;
      String emailKey = "email_adc_" + i;
      String nomeMaeKey = "nome_mae_adc_" + i;
      String cepKey = "cep_adc_" + i;
      String logradouroKey = "logradouro_adc_" + i;
      String numeroKey = "numero_adc_" + i;
      String complementoKey = "complemento_adc_" + i;
      String bairroKey = "bairro_adc_" + i;
      String cidadeKey = "cidade_adc_" + i;
      String ufKey = "uf_adc_" + i;
      if (additionalProperties.containsKey(cpfKey) && additionalProperties.containsKey(nomeKey)) {
        PessoaAdicionalPDAFRequest adicional = new PessoaAdicionalPDAFRequest();
        adicional.setNome((String) additionalProperties.get(nomeKey));
        adicional.setDataNascimentoFromMap(additionalProperties.get(dataNascimentoKey));
        adicional.setCpf((String) additionalProperties.get(cpfKey));
        adicional.setCargo((String) additionalProperties.get(cargoKey));
        adicional.setTelefone((String) additionalProperties.get(telefoneKey));
        adicional.setEmail((String) additionalProperties.get(emailKey));
        adicional.setNomeMae((String) additionalProperties.get(nomeMaeKey));
        adicional.setCep((String) additionalProperties.get(cepKey));
        adicional.setLogradouro((String) additionalProperties.get(logradouroKey));
        adicional.setNumero((String) additionalProperties.get(numeroKey));
        adicional.setComplemento((String) additionalProperties.get(complementoKey));
        adicional.setBairro((String) additionalProperties.get(bairroKey));
        adicional.setCidade((String) additionalProperties.get(cidadeKey));
        adicional.setUf((String) additionalProperties.get(ufKey));
        adicionais.add(adicional);
      }
    }

    ProdutoInstituicaoConfiguracao prodConfig =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
    if (prodConfig == null) {
      throw new GenericServiceException(
          ConstantesErro.ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_PRODUTO_SEM_CONFIGURACAO
              .getMensagem());
    }

    CadastrarContaPagamentoPessoaRequest cadastrarPJRequest =
        new CadastrarContaPagamentoPessoaRequest();
    cadastrarPJRequest.setDocumento(request.getDocumento());
    cadastrarPJRequest.setRazaoSocial(request.getRazaoSocial());
    cadastrarPJRequest.setDataFundacao(request.getDtFundacao());
    cadastrarPJRequest.setEmail(request.getEmail());
    cadastrarPJRequest.setTipoPessoa(PESSOA_JURIDICA);
    cadastrarPJRequest.setDddTelefoneCelular(
        request.getDddCelularEmpresa() != null
            ? Integer.parseInt(request.getDddCelularEmpresa())
            : null);
    cadastrarPJRequest.setTelefoneCelular(
        request.getNroCelularEmpresa() != null
            ? Integer.parseInt(request.getNroCelularEmpresa())
            : null);
    cadastrarPJRequest.setIdProcessadora(user.getIdProcessadora());
    cadastrarPJRequest.setIdInstituicao(ID_PRODUCAO_INSTITUICAO_BRBCARD);
    cadastrarPJRequest.setIdRegional(idRegional);
    cadastrarPJRequest.setIdFilial(idFilial);
    cadastrarPJRequest.setIdPontoDeRelacionamento(idPontoDeRelacionamento);
    cadastrarPJRequest.setIdUsuarioInclusao(user.getIdUsuario());
    cadastrarPJRequest.setNomeFantasia(request.getNomeFantasia());
    cadastrarPJRequest.setNomeEmbossado(Abreviador.abreviarNome(request.getRazaoSocial()));
    if (request.getCepEmpresa() != null && !request.getCepEmpresa().isEmpty()) {
      EnderecosPessoaRequest endPessoa = new EnderecosPessoaRequest();
      List<EnderecoPessoaRequest> enderecos = new ArrayList<>();
      EnderecoPessoaRequest end = new EnderecoPessoaRequest();
      end.setCep(Abreviador.regexEndereco(request.getCepEmpresa(), CEP_E_NUMEROS));
      if (request.getLogradouroEmpresa() != null && !request.getLogradouroEmpresa().isEmpty()) {
        end.setLogradouro(Abreviador.regexEndereco(request.getLogradouroEmpresa(), OUTROS));
      }
      if (request.getBairroEmpresa() != null && !request.getBairroEmpresa().isEmpty()) {
        end.setBairro(Abreviador.regexEndereco(request.getBairroEmpresa(), OUTROS));
      }
      if (request.getCidadeEmpresa() != null && !request.getCidadeEmpresa().isEmpty()) {
        end.setCidade(Abreviador.regexEndereco(request.getCidadeEmpresa(), OUTROS));
      }
      if (request.getNumeroEmpresa() != null && !request.getNumeroEmpresa().isEmpty()) {
        end.setNumero(Abreviador.regexEndereco(request.getNumeroEmpresa(), CEP_E_NUMEROS));
      }
      if (request.getComplementoEmpresa() != null && !request.getComplementoEmpresa().isEmpty()) {
        end.setComplemento(Abreviador.regexEndereco(request.getComplementoEmpresa(), OUTROS));
      }
      if (request.getUfEmpresa() != null && !request.getUfEmpresa().isEmpty()) {
        end.setUf(Abreviador.regexEndereco(request.getUfEmpresa(), UF));
      }
      end.setIdTipoEndereco(ENDERECO_FISCAL);
      end.setIdUsuarioInclusao(user.getIdUsuario());
      enderecos.add(end);
      endPessoa.setEnderecos(enderecos);
      cadastrarPJRequest.setEnderecosPessoaRequest(endPessoa);
    }
    List<ValorCargaProdutoInstituicao> listaProdutos = new ArrayList<>();
    ValorCargaProdutoInstituicao produtoInstituicao = new ValorCargaProdutoInstituicao();
    produtoInstituicao.setIdProdutoInstituicao(idProdutoInstituicao);
    produtoInstituicao.setIdProdutoPlataforma(prodConfig.getIdProdutoPlataforma());
    produtoInstituicao.setValorCargaPadrao(0d);
    listaProdutos.add(produtoInstituicao);

    cadastrarPJRequest.setValoresCargasProdutos(listaProdutos);
    // Buscando/Salvando setor filial
    SetorFilial setorFilial = setorFilialService.saveByDescricao(nomeSetorFilial, user);
    cadastrarPJRequest.setIdSetorFilial(setorFilial.getIdSetorFilial());
    ContaPagamento conta =
        contaPagamentoService.cadastrarContaPagamentoPessoa(cadastrarPJRequest, user);

    for (PessoaAdicionalPDAFRequest adicional : adicionais) {
      Pessoa pessoa = new Pessoa();
      CadastrarPessoaFisica model = new CadastrarPessoaFisica();
      model.setTipoPessoa(PESSOA_FISICA);
      model.setIdInstituicao(ID_PRODUCAO_INSTITUICAO_BRBCARD);
      model.setIdProcessadora(ID_PROCESSADORA_ITS_PAY);
      model.setIdProdInstituicao(idProdutoInstituicao);
      model.setDataNascimento(adicional.getDataNascimento());
      model.setNomeCompleto(adicional.getNome());
      model.setNomeEmbossado(Abreviador.abreviarNome(adicional.getNome()));
      model.setDocumento(adicional.getCpf());
      if (StringUtils.isNotBlank(adicional.getTelefone())) {
        Integer ddd = Integer.parseInt(adicional.getTelefone().substring(0, 2));
        Integer celular = Integer.parseInt(adicional.getTelefone().substring(2));
        model.setDddTelefoneCelular(ddd);
        model.setTelefoneCelular(celular);
      }
      model.setEmail(adicional.getEmail());
      model.setNomeMae(adicional.getNomeMae());
      model.setIdUsuarioInclusao(user.getIdUsuario());
      pessoa = preparePessoaFisica(model, pessoa);
      pessoa.setIdSetorFilial(setorFilial.getIdSetorFilial());
      pessoa = createPessoaFisica(model, pessoa);
      List<EnderecoPessoa> enderecoPessoas = new ArrayList<>();
      EnderecoPessoa enderecoPessoa = new EnderecoPessoa();
      enderecoPessoa.setCep(adicional.getCep());
      enderecoPessoa.setBairro(adicional.getBairro() == null ? "" : adicional.getBairro());
      enderecoPessoa.setComplemento(
          adicional.getComplemento() == null ? "" : adicional.getComplemento());
      enderecoPessoa.setLogradouro(
          adicional.getLogradouro() == null ? "" : adicional.getLogradouro());
      enderecoPessoa.setIdPessoa(pessoa.getIdPessoa());
      enderecoPessoa.setIdUsuarioInclusao(user.getIdUsuario());
      enderecoPessoa.setNumero(adicional.getNumero() == null ? "" : adicional.getNumero());
      enderecoPessoa.setIdTipoEndereco(ENDERECO_COMERCIAL);
      enderecoPessoa.setStatus(1);
      enderecoPessoa.setCidade(adicional.getCidade() == null ? "" : adicional.getCidade());
      enderecoPessoa.setUf(adicional.getUf() == null ? "" : adicional.getUf());
      enderecoPessoa.setDtHrInclusao(new Date());
      enderecoPessoas.add(enderecoPessoa);
      enderecoPessoaService.saveAll(enderecoPessoas);
      ContaPessoa contaPessoaAdicional =
          contaPagamentoService.prepareContaPessoaAdicional(pessoa, conta);
      contaPagamentoService.vincularPessoaAdicionalConta(pessoa, conta, contaPessoaAdicional);
      contaPagamentoService.createCredencialAdicional(pessoa, conta, null);
    }
  }

  private ContaAtivaB2BFiltroVO createContaAtivaB2BFiltroVO(
      FuncionariosProdutoAtivoModel model, SecurityUser user) {
    ContaAtivaB2BFiltroVO filtroVO = new ContaAtivaB2BFiltroVO();

    filtroVO.setIdProcessadora(user.getIdProcessadora());
    filtroVO.setIdInstituicao(user.getIdInstituicao());
    filtroVO.setIdRegional(user.getIdRegional());
    filtroVO.setIdFilial(user.getIdFilial());
    filtroVO.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    filtroVO.setIdProdutoInstituicao(model.getIdProdutoInstituicao());
    filtroVO.setIdSetorFilial(model.getIdSetorFilial());
    filtroVO.setIdStatusConta(model.getIdStatusConta());
    filtroVO.setIdPessoa(model.getIdInterno());
    filtroVO.setCpf(model.getCpf());
    filtroVO.setMatricula(model.getMatricula());
    filtroVO.setNomeCompleto(model.getNomeCompleto().toUpperCase());
    filtroVO.setPagina(model.getPagina());
    filtroVO.setQtdRegistros(model.getQtdRegistros());
    if (model.getValorCargaPadrao() != null) {
      filtroVO.setValorCargaPadrao(model.getValorCargaPadrao().doubleValue());
    } else {
      filtroVO.setValorCargaPadrao((Double) 0.0);
    }
    filtroVO.setTipoPessoa(model.getTipoPessoa());
    filtroVO.setTitularidade(1);
    return filtroVO;
  }

  public Long countFuncionariosContaAtivaByhQL(
      FuncionariosProdutoAtivoModel model, SecurityUser user) {

    ContaAtivaB2BFiltroVO filtroVO = createContaAtivaB2BFiltroVO(model, user);

    return pessoaRepository.contadorFindByContaAtiva(filtroVO);
  }

  @Transactional
  public void salvarProdutosSociaisByArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user) {
    List<FuncionarioProdutosSociaisTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoCadastros);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    to = importadorFuncionarioXls.getImportacaoProdutosSociais(copy);

    LogB2bCadastroViaArquivo logB2bCadastroViaArquivo = new LogB2bCadastroViaArquivo();
    logB2bCadastroViaArquivo.setDataExecucao(new Date());
    logB2bCadastroViaArquivo.setIdAcessoUsuario(user.getIdUsuario());
    logB2bCadastroViaArquivo.setIdInstituicao(user.getIdInstituicao());
    logB2bCadastroViaArquivo.setIdProcessadora(user.getIdProcessadora());
    logB2bCadastroViaArquivo.setStatus(Boolean.TRUE);
    logB2bCadastroViaArquivo.setIdHierarquia(user.getIdHierarquiaNivel());
    logB2bCadastroViaArquivo.setIdFilial(user.getIdFilial());
    logB2bCadastroViaArquivo.setIdRegional(user.getIdRegional());
    logB2bCadastroViaArquivo.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    logB2bCadastroViaArquivo.setHash(hash);
    logB2bCadastroViaArquivo = logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);

    BigDecimal valorAtualizadoLimite = new BigDecimal(0);

    try {
      salvarPortadorProdutosSociaisByArquivo(
          to, idProdutoInstituicao, nomeSetorFilial, user, valorAtualizadoLimite);
    } finally {
      logB2bCadastroViaArquivo.setStatus(Boolean.FALSE);
      logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);
    }
  }

  @Transactional
  public void salvarFuncionariosByArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user) {
    List<FuncionarioCargaTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoCadastros);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    // TODO: Melhorar este trecho de código
    if (ImportadorArquivo.isTxt(nomeArquivo)) {
      to = importadorFuncionarioTxt.getImportacao(copy, null);
    } else {
      to = importadorFuncionarioXls.getImportacao(copy, null);
    }

    LogB2bCadastroViaArquivo logB2bCadastroViaArquivo = new LogB2bCadastroViaArquivo();
    logB2bCadastroViaArquivo.setDataExecucao(new Date());
    logB2bCadastroViaArquivo.setIdAcessoUsuario(user.getIdUsuario());
    logB2bCadastroViaArquivo.setIdInstituicao(user.getIdInstituicao());
    logB2bCadastroViaArquivo.setIdProcessadora(user.getIdProcessadora());
    logB2bCadastroViaArquivo.setStatus(Boolean.TRUE);
    logB2bCadastroViaArquivo.setIdHierarquia(user.getIdHierarquiaNivel());
    logB2bCadastroViaArquivo.setIdFilial(user.getIdFilial());
    logB2bCadastroViaArquivo.setIdRegional(user.getIdRegional());
    logB2bCadastroViaArquivo.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    logB2bCadastroViaArquivo.setHash(hash);
    logB2bCadastroViaArquivo = logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);

    BigDecimal valorAtualizadoLimite = new BigDecimal(0);

    try {
      salvarFuncionarioByArquivo(
          to, idProdutoInstituicao, nomeSetorFilial, user, valorAtualizadoLimite);
    } finally {
      logB2bCadastroViaArquivo.setStatus(Boolean.FALSE);
      logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);
    }
  }

  public VerificacaoCargaArquivoTO verificaCadastroViaArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      String nomeSetorFilial,
      SecurityUser user,
      Integer arquivoCabecalho,
      List<Integer> idsProdutoInstituicao)
      throws FileNotFoundException {
    List<FuncionarioCargaTO> to = null;
    VerificacaoCargaArquivoTO verificaoTO = null;

    Map<String, VerificacaoLinhaTO> verificacoes = null;

    File copy = FileUtil.inputStreamToFile(is);

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    // TODO: Melhorar este trecho de código
    if (ImportadorArquivo.isTxt(nomeArquivo)) {
      to = importadorFuncionarioTxt.getImportacao(copy, null);
    } else {
      to =
          importadorFuncionarioXls.getImportacaoCadPortadores(
              new FileInputStream(copy), cabalAntigo, arquivoCabecalho);
    }

    verificaoTO = new VerificacaoCargaArquivoTO(nomeArquivo, to);

    verificaArquivoValoresRepetidosCadPortadores(verificaoTO, to, idProdutoInstituicao);

    verificacoes =
        getVerificacaoCadPortadores(
            to, idProdutoInstituicao, nomeSetorFilial, user, idsProdutoInstituicao);

    verificaoTO.loadVerificacoesCadPortadores(verificacoes);

    return sortVerificacoes(verificaoTO);
  }

  public VerificacaoCargaArquivoTO verificaFuncionariosByArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user) {
    List<FuncionarioCargaTO> to = null;
    VerificacaoCargaArquivoTO verificaoTO = null;

    Map<String, VerificacaoLinhaTO> verificacoes = null;

    File copy = FileUtil.inputStreamToFile(is);

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    // TODO: Melhorar este trecho de código
    if (ImportadorArquivo.isTxt(nomeArquivo)) {
      to = importadorFuncionarioTxt.getImportacao(copy, null);
    } else {
      to = importadorFuncionarioXls.getImportacao(copy, null);
    }

    verificaoTO = new VerificacaoCargaArquivoTO(nomeArquivo, to);

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(idProdutoInstituicao);
    if (produtoInstituicaoConfiguracao == null) {
      throw new GenericServiceException("Produto não existe");
    }

    if (!produtoInstituicaoConfiguracaoService.obterPermiteMultiplasContas(idProdutoInstituicao))
      verificaArquivoValoresRepetidos(verificaoTO, to);

    verificacoes = getVerificacao(to, idProdutoInstituicao, nomeSetorFilial, user);

    verificaoTO.loadVerificacoes(verificacoes);

    Boolean temCorresp =
        produtoInstituicaoCorrespondenteService.findIfExistsCorresp(idProdutoInstituicao);
    if (temCorresp) {
      ContasCredenciaisReplicaveisReponse empresa =
          produtoInstituicaoCorrespondenteService.montaVerificacaoEmpresa(
              idProdutoInstituicao, user);
      produtoInstituicaoCorrespondenteService.validarDadosReplicacao(empresa);
    }

    return sortVerificacoes(verificaoTO);
  }

  @Transactional
  public void salvarPortadorViaArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      String nomeSetorFilial,
      SecurityUser user,
      Integer arquivoCabecalho)
      throws FileNotFoundException {
    List<FuncionarioCargaTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoCadastros);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    // TODO: Melhorar este trecho de código
    if (ImportadorArquivo.isTxt(nomeArquivo)) {
      to = importadorFuncionarioTxt.getImportacao(copy, null);
    } else {
      to =
          importadorFuncionarioXls.getImportacaoCadPortadores(
              new FileInputStream(copy), cabalAntigo, arquivoCabecalho);
    }
    LogB2bCadastroViaArquivo logB2bCadastroViaArquivo = new LogB2bCadastroViaArquivo();
    logB2bCadastroViaArquivo.setDataExecucao(new Date());
    logB2bCadastroViaArquivo.setIdAcessoUsuario(user.getIdUsuario());
    logB2bCadastroViaArquivo.setIdInstituicao(user.getIdInstituicao());
    logB2bCadastroViaArquivo.setIdProcessadora(user.getIdProcessadora());
    logB2bCadastroViaArquivo.setStatus(Boolean.TRUE);
    logB2bCadastroViaArquivo.setIdHierarquia(user.getIdHierarquiaNivel());
    logB2bCadastroViaArquivo.setIdFilial(user.getIdFilial());
    logB2bCadastroViaArquivo.setIdRegional(user.getIdRegional());
    logB2bCadastroViaArquivo.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    logB2bCadastroViaArquivo.setHash(hash);
    logB2bCadastroViaArquivo = logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);

    BigDecimal valorAtualizadoLimite = new BigDecimal(0);

    try {
      salvarFuncionarioByArquivo(
          to, idProdutoInstituicao, nomeSetorFilial, user, valorAtualizadoLimite);
    } finally {
      logB2bCadastroViaArquivo.setStatus(Boolean.FALSE);
      logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);
    }
  }

  private VerificacaoCargaArquivoTO sortVerificacoes(VerificacaoCargaArquivoTO verificaoTO) {
    Collections.sort(
        verificaoTO.getVerificacoes(),
        new Comparator<String>() {
          /** Comparação dos primeiros caracteres entre aspa simples dentro de uma string */
          @Override
          public int compare(String a, String b) {
            String firstA = a.substring(a.indexOf("'") + 1, a.length() - 1);
            Integer indexA = firstA.indexOf("'") + 1;
            String firstB = b.substring(b.indexOf("'") + 1, b.length() - 1);
            Integer indexB = firstB.indexOf("'") + 1;
            return Integer.valueOf(a.substring(a.indexOf("'") + 1, a.indexOf("'") + indexA))
                    < Integer.valueOf(b.substring(b.indexOf("'") + 1, b.indexOf("'") + indexB))
                ? -1
                : 1;
          }
        });
    return verificaoTO;
  }

  public VerificacaoCargaArquivoTO verificaCadastroCompletoByArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      SecurityUser user,
      Integer arquivoCabecalho)
      throws FileNotFoundException {
    List<FuncionarioCadastroCompletoTO> to = null;
    VerificacaoCargaArquivoTO verificaoTO = null;

    Map<String, VerificacaoLinhaTO> verificacoes = null;

    File copy = FileUtil.inputStreamToFile(is);

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    if (ImportadorArquivo.isXlsx(nomeArquivo) || ImportadorArquivo.isXls(nomeArquivo)) {
      to =
          importadorFuncionarioXls.getImportacaoCadastroCompleto(
              new FileInputStream(copy), cabalAntigo, arquivoCabecalho);
    }

    verificaoTO = new VerificacaoCargaArquivoTO(nomeArquivo, to);

    verificaArquivoValoresRepetidosCadCompleto(verificaoTO, to, idProdutoInstituicao);

    verificacoes = getVerificacaoCadCompl(to, idProdutoInstituicao, cabalAntigo, user);

    verificaoTO.loadVerificacoesCadCompl(verificacoes);

    return sortVerificacoes(verificaoTO);
  }

  public List<String> verificaArquivoCpfRepetido(List<FuncionarioCargaTO> to) {
    List<String> novoArray = new ArrayList<String>();
    for (FuncionarioCargaTO tmp : to) {
      for (FuncionarioCargaTO chk : to) {
        if (!tmp.getCpf().isEmpty() && tmp.getCpf() != null) {
          if (!tmp.getIdControl().equals(chk.getIdControl())
              && tmp.getCpf().equals(chk.getCpf())
              && tmp.getIsDuplicatedRegister().equals(false)
              && chk.getIsDuplicatedRegister().equals(false)) {
            novoArray.add(
                "O CPF "
                    + chk.getCpf()
                    + ", do(a) funcionario(a) "
                    + chk.getNomeCompleto()
                    + " é idêntico ao CPF do(a) funcionário(a) "
                    + tmp.getNomeCompleto()
                    + "."
                    + "<< SOMENTE SERÁ REGISTRADO PARA: "
                    + tmp.getNomeCompleto()
                    + " >>");
            chk.setIsCpfDuplicated(true);
          }
        }
      }
    }
    return novoArray;
  }

  public List<String> verificaArquivoCpfRepetidoCadComp(List<FuncionarioCadastroCompletoTO> to) {
    List<String> novoArray = new ArrayList<String>();
    for (FuncionarioCadastroCompletoTO tmp : to) {
      for (FuncionarioCadastroCompletoTO chk : to) {
        if (!tmp.getCpf().isEmpty()
            && tmp.getCpf() != null
            && COD_DOC_CPF_1.equals(tmp.getTipoDocumento())) {
          if (!tmp.getIdControl().equals(chk.getIdControl())
              && tmp.getCpf().equals(chk.getCpf())
              && tmp.getIsDuplicatedRegister().equals(false)
              && chk.getIsDuplicatedRegister().equals(false)) {
            novoArray.add(
                "O CPF "
                    + chk.getCpf()
                    + ", do(a) funcionario(a) "
                    + chk.getNomeCompleto()
                    + " é idêntico ao CPF do(a) funcionário(a) "
                    + tmp.getNomeCompleto()
                    + "."
                    + "<< SOMENTE SERÁ REGISTRADO PARA: "
                    + tmp.getNomeCompleto()
                    + " >>");
            chk.setIsCpfDuplicated(true);
          }
        }
      }
    }
    return novoArray;
  }

  public List<String> verificaArquivoMatriculaRepetido(List<FuncionarioCargaTO> to) {
    List<String> novoArray = new ArrayList<String>();
    for (FuncionarioCargaTO tmp : to) {
      for (FuncionarioCargaTO chk : to) {
        if (!tmp.getMatricula().isEmpty()) {
          if (!tmp.getIdControl().equals(chk.getIdControl())
              && tmp.getMatricula().equals(chk.getMatricula())
              && tmp.getIsMatriculaDuplicated().equals(false)
              && chk.getIsMatriculaDuplicated().equals(false)) {
            novoArray.add(
                "A Matrícula "
                    + chk.getMatricula()
                    + ", do(a) funcionario(a) "
                    + chk.getNomeCompleto()
                    + " é idêntica à matrícula do(a) funcionário(a) "
                    + tmp.getNomeCompleto()
                    + "."
                    + "<< SOMENTE SERÁ REGISTRADO PARA: "
                    + tmp.getNomeCompleto()
                    + " >>");
            chk.setIsMatriculaDuplicated(true);
          }
        }
      }
    }
    return novoArray;
  }

  public List<String> verificaArquivoValoresRepetidos(
      VerificacaoCargaArquivoTO verificaoTO, List<FuncionarioCargaTO> to) {
    FuncionarioCargaTO it = null;
    for (int j = 0; j < to.size(); j++) {
      it = to.get(j);
      it.setIdControl(j);
    }

    List<String> lista1;
    List<String> lista2;

    lista1 = verificaArquivoCpfRepetido(to);
    lista2 = verificaArquivoMatriculaRepetido(to);
    lista1.addAll(lista2);

    verificaoTO.setListaValorRepetido(lista1);

    return verificaoTO.getListaValorRepetido();
  }

  public List<String> verificaArquivoValoresRepetidosCadPortadores(
      VerificacaoCargaArquivoTO verificaoTO,
      List<FuncionarioCargaTO> to,
      Integer idProdutoInstituicao) {
    FuncionarioCargaTO it = null;
    for (int j = 0; j < to.size(); j++) {
      it = to.get(j);
      it.setIdControl(j);
    }

    List<String> lista1;

    if (utilService.getCorporativoInfinancas().contains(idProdutoInstituicao)) {
      lista1 = new ArrayList<>();
    } else {
      lista1 = verificaArquivoCpfRepetido(to);
    }

    verificaoTO.setListaValorRepetido(lista1);

    return verificaoTO.getListaValorRepetido();
  }

  public List<String> verificaArquivoValoresRepetidosCadCompleto(
      VerificacaoCargaArquivoTO verificaoTO,
      List<FuncionarioCadastroCompletoTO> to,
      Integer idProdutoInstituicao) {
    FuncionarioCadastroCompletoTO it = null;
    for (int j = 0; j < to.size(); j++) {
      it = to.get(j);
      it.setIdControl(j);
    }

    List<String> lista1;

    if (utilService.getCorporativoInfinancas().contains(idProdutoInstituicao)) {
      lista1 = new ArrayList<>();
    } else {
      lista1 = verificaArquivoCpfRepetidoCadComp(to);
    }

    verificaoTO.setListaValorRepetido(lista1);

    return verificaoTO.getListaValorRepetido();
  }

  public boolean isFuncionarioArquivoValido(
      FuncionarioCargaTO registro,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user,
      Integer linha,
      BigDecimal valorAtualizadoLimite) {
    if (registro != null) {

      Map<String, VerificacaoLinhaTO> verificado =
          getVerificacao(
              registro, idProdutoInstituicao, nomeSetorFilial, user, linha, valorAtualizadoLimite);

      return !verificado.containsKey("ERRO:" + linha);
    }

    return Boolean.FALSE;
  }

  //	public PessoaPreCadastradaVO buscaPessoaPreCadastradaVO(Integer idProcessadora, Integer
  // idInstituicao, String documento){
  //  		List<Pessoa> pessoa =
  // this.pessoaRepository.findByIdProcessadoraAndIdInstituicaoAndDocumento(idProcessadora,
  // idInstituicao, documento);
  //		PessoaPreCadastradaVO pessoaPreCadastradaVO = new PessoaPreCadastradaVO();
  //		pessoaPreCadastradaVO.setIdPessoa(pessoa.get(0).getIdPessoa());
  //		pessoaPreCadastradaVO.setDocumento(pessoa.get(0).getDocumento());
  //		pessoaPreCadastradaVO.setEmail(pessoa.get(0).getEmail());
  //		pessoaPreCadastradaVO.setDataNascimento(pessoa.get(0).getDataNascimento() != null ?
  // pessoa.get(0).getDataNascimento().toLocalTime().toString() : null);
  //		pessoaPreCadastradaVO.setTelefone(pessoa.get(0).getDddTelefoneCelular() != null &&
  // pessoa.get(0).getTelefoneCelular() != null ? "(" +
  // pessoa.get(0).getDddTelefoneCelular().toString() + ")" +
  // pessoa.get(0).getTelefoneCelular().toString() : null);
  //		List<Credencial> credencial = credencialService.findByIdPessoa(pessoa.get(0).getIdPessoa());
  //		pessoaPreCadastradaVO.setCredencial(credencial.get(0).getTokenInterno());
  //
  //	pessoaPreCadastradaVO.setDataValidadeCredencial(credencial.get(0).getDataValidade().toLocalDate().toString());
  //  		return pessoaPreCadastradaVO;
  //	}

  @Transactional
  public void salvarPortadorProdutosSociaisByArquivo(
      List<FuncionarioProdutosSociaisTO> tos,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user,
      BigDecimal valorAtualizadoLimite) {
    CadastrarContaPagamentoPessoaRequest pessoa = null;
    FuncionarioProdutosSociaisTO to = null;
    SetorFilial setorFilial = null;
    List<String> cpfsPreCadastro = new ArrayList<>();

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      pessoa = new CadastrarContaPagamentoPessoaRequest();

      pessoa.setTipoPessoa(1);
      pessoa.setEstrangeiro(Boolean.FALSE);

      pessoa.setDocumento(to.getCpf());
      pessoa.setNomeCompleto(Abreviador.nomeNormalizado(to.getNomeCompleto(), PESSOA_FISICA));
      pessoa.setValoresCargasProdutos(new ArrayList<>());

      pessoa.setIdInstituicao(user.getIdInstituicao());
      pessoa.setIdProcessadora(user.getIdProcessadora());
      pessoa.setIdRegional(user.getIdRegional());
      pessoa.setIdFilial(user.getIdFilial());
      pessoa.setIdUsuarioInclusao(user.getIdUsuario());
      pessoa.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
      pessoa.setCadastroOrigem(ORIGEM_CADASTRO_VIA_ARQUIVO);

      // Verificando se o setor/filial foi informado
      if (nomeSetorFilial == null || !to.getSetorFilial().equalsIgnoreCase(nomeSetorFilial)) {
        nomeSetorFilial = to.getSetorFilial();
      }

      // Buscando/Salvando setor filial
      setorFilial = setorFilialService.saveByDescricao(nomeSetorFilial, user);
      pessoa.setIdSetorFilial(setorFilial.getIdSetorFilial());

      if (to.getValorCarga() != null) {
        pessoa
            .getValoresCargasProdutos()
            .add(
                new ValorCargaProdutoInstituicao(
                    idProdutoInstituicao, to.getValorCarga().doubleValue()));
      }

      pessoa.setDataNascimento(to.getDataNascimento());

      if (to.getNumeroRg() != null) {
        pessoa.setRg(to.getNumeroRg());
      }
      if (to.getRgOrgaoEmissor() != null) {
        pessoa.setRgOrgaoEmissor(to.getRgOrgaoEmissor());
      }
      if (to.getRgUFOrgaoEmissor() != null) {
        pessoa.setRgUfOrgaoEmissor(to.getRgUFOrgaoEmissor());
      }
      if (to.getRgDataEmissao() != null) {
        pessoa.setRgDataEmissao(to.getRgDataEmissao());
      }
      if (to.getNaturalidade() != null) {
        pessoa.setNaturalidade(to.getNaturalidade());
      }
      pessoa.setNacionalidade(to.getNacionalidade());
      if (to.getNomeMae() != null) {
        pessoa.setNomeMae(Abreviador.nomeNormalizado(to.getNomeMae(), PESSOA_FISICA));
      }
      if (to.getNomePai() != null) {
        pessoa.setNomePai(Abreviador.nomeNormalizado(to.getNomePai(), PESSOA_FISICA));
      }
      if (to.getCep() != null) {
        EnderecosPessoaRequest endPessoa = new EnderecosPessoaRequest();
        List<EnderecoPessoaRequest> enderecos = new ArrayList<>();
        EnderecoPessoaRequest end = new EnderecoPessoaRequest();
        end.setCep(Abreviador.regexEndereco(to.getCep(), CEP_E_NUMEROS));
        end.setLogradouro(Abreviador.regexEndereco(to.getLogradouro(), OUTROS));
        end.setNumero(Abreviador.regexEndereco(to.getNumero(), CEP_E_NUMEROS));
        end.setComplemento(Abreviador.regexEndereco(to.getComplemento(), OUTROS));
        end.setBairro(Abreviador.regexEndereco(to.getBairro(), OUTROS));
        end.setCidade(Abreviador.regexEndereco(to.getCidade(), OUTROS));
        end.setUf(Abreviador.regexEndereco(to.getUf(), UF));
        end.setIdTipoEndereco(ENDERECO_RESIDENCIAL);
        end.setIdUsuarioInclusao(user.getIdUsuario());
        enderecos.add(end);
        endPessoa.setEnderecos(enderecos);
        pessoa.setEnderecosPessoaRequest(endPessoa);
      }
      if (StringUtils.isNotBlank(to.getDddTelefoneResidencial())) {
        Integer dddTel = new Integer(to.getDddTelefoneResidencial());
        pessoa.setDddTelefoneResidencial(dddTel);
      }
      if (StringUtils.isNotBlank(to.getTelefoneResidencial())) {
        Integer tel = new Integer(to.getTelefoneResidencial());
        pessoa.setTelefoneResidencial(tel);
      }
      if (StringUtils.isNotBlank(to.getDddCelular())) {
        Integer dddCel = new Integer(to.getDddCelular());
        pessoa.setDddTelefoneCelular(dddCel);
      }
      if (StringUtils.isNotBlank(to.getTelefoneCelular())) {
        Integer cel = new Integer(to.getTelefoneCelular());
        pessoa.setTelefoneCelular(cel);
      }
      if (StringUtils.isNotBlank(to.getEmail())) {
        pessoa.setEmail(to.getEmail());
      }
      if (StringUtils.isNotBlank(to.getCpfResponsavel())) {
        pessoa.setCpfResponsavel(to.getCpfResponsavel());
      }
      if (StringUtils.isNotBlank(to.getNomeResponsavel())) {
        pessoa.setNomeResponsavel(to.getNomeResponsavel());
      }
      if (StringUtils.isNotBlank(to.getNomeEscola())) {
        pessoa.setNomeEscola(to.getNomeEscola());
      }
      if (StringUtils.isNotBlank(to.getCidadeEmbossing())) {
        pessoa.setCidadeEmbossing(to.getCidadeEmbossing());
      }
      if (StringUtils.isNotBlank(to.getUfEmbossing())) {
        pessoa.setUfEmbossing(to.getUfEmbossing());
      }
      pessoa.setNumeroAgenciaBRB(to.getNumeroAgencia());
      pessoa.setNomeAgenciaBRB(to.getNomeAgencia());
      pessoa.setNomeEmbossado(Abreviador.abreviarNome(pessoa.getNomeCompleto()));

      contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(pessoa);
    }
  }

  @Transactional
  public void salvarFuncionarioByArquivo(
      List<FuncionarioCargaTO> tos,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user,
      BigDecimal valorAtualizadoLimite) {
    CadastrarContaPagamentoPessoaRequest pessoa = null;
    FuncionarioCargaTO to = null;
    SetorFilial setorFilial = null;
    Boolean temCorresp = false;
    Boolean produtoIntegracao = false;
    HashMap<String, List<Long>> cpfsContasPreCadastro = new HashMap<>();

    if (ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(user.getIdInstituicao())) {
      temCorresp =
          produtoInstituicaoCorrespondenteService.findIfExistsCorresp(idProdutoInstituicao);
      if (temCorresp) {
        ContasCredenciaisReplicaveisReponse empresa =
            produtoInstituicaoCorrespondenteService.montaVerificacaoEmpresa(
                idProdutoInstituicao, user);
        produtoInstituicaoCorrespondenteService.validarDadosReplicacao(empresa);
      }
    }

    if (Boolean.TRUE.equals(
        produtoInstituicaoConfiguracaoService.obterCargaIntegracao(idProdutoInstituicao))) {
      produtoIntegracao = TRUE;
    }

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      if (isFuncionarioArquivoValido(
          to, idProdutoInstituicao, nomeSetorFilial, user, i, valorAtualizadoLimite)) {
        pessoa = new CadastrarContaPagamentoPessoaRequest();

        pessoa.setTipoPessoa(PESSOA_FISICA);
        pessoa.setEstrangeiro(Boolean.FALSE);

        pessoa.setDocumento(to.getCpf());
        pessoa.setMatricula(to.getMatricula());
        pessoa.setNomeCompleto(Abreviador.nomeNormalizado(to.getNomeCompleto(), PESSOA_FISICA));

        if (utilService.getCorporativoInfinancas().contains(idProdutoInstituicao)) {
          pessoa.setRazaoSocial(Abreviador.nomeNormalizado(to.getNomeCompleto(), PESSOA_JURIDICA));
          pessoa.setDataFundacao(to.getDataNascimento());
        } else {
          pessoa.setDataNascimento(to.getDataNascimento());
        }

        pessoa.setValoresCargasProdutos(new ArrayList<>());
        pessoa.setParcelamento(to.getParcelamento());

        pessoa.setIdInstituicao(user.getIdInstituicao());
        pessoa.setIdProcessadora(user.getIdProcessadora());
        pessoa.setIdRegional(user.getIdRegional());
        pessoa.setIdFilial(user.getIdFilial());
        pessoa.setIdUsuarioInclusao(user.getIdUsuario());
        pessoa.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
        pessoa.setCadastroOrigem(ORIGEM_CADASTRO_VIA_ARQUIVO);
        // Verificando se o setor/filial foi informado no arquivo
        if (nomeSetorFilial == null || !to.getSetorFilial().equalsIgnoreCase(nomeSetorFilial)) {
          nomeSetorFilial = to.getSetorFilial();
        }

        // Buscando/Salvando setor filial
        setorFilial = setorFilialService.saveByDescricao(nomeSetorFilial, user);
        pessoa.setIdSetorFilial(setorFilial.getIdSetorFilial());

        if (to.getValorCarga() != null) {
          pessoa
              .getValoresCargasProdutos()
              .add(
                  new ValorCargaProdutoInstituicao(
                      idProdutoInstituicao, to.getValorCarga().doubleValue()));
        }

        pessoa.setEmail(to.getEmail());
        if (StringUtils.isNotBlank(to.getDddCelular())) {
          Integer x = new Integer(to.getDddCelular());
          pessoa.setDddTelefoneCelular(x);
        }
        if (StringUtils.isNotBlank(to.getTelefoneCelular())) {
          Integer y = new Integer(to.getTelefoneCelular());
          pessoa.setTelefoneCelular(y);
        }
        // TODO ver depois
        pessoa.setNomeEmbossado(Abreviador.abreviarNome(pessoa.getNomeCompleto()));
        ContaPagamento conta = contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(pessoa);
        if (produtoIntegracao) {
          List<Long> contasIntegracao = new ArrayList<>();
          contasIntegracao.add(conta.getIdConta());
          cpfsContasPreCadastro.put(pessoa.getDocumento(), contasIntegracao);
        }
      }
    }
    if (temCorresp) {
      produtoInstituicaoCorrespondenteService.preparaAgendamentoReplicar();
    }
    if (produtoIntegracao) {
      preCadastroService.agendarPreCadastro(cpfsContasPreCadastro, user);
    }
  }

  private boolean isCadastroCompletoArquivoValido(
      FuncionarioCadastroCompletoTO registro,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      SecurityUser user,
      Integer linha) {
    if (Objects.nonNull(registro)) {
      return getVerificacaoCadComp(registro, idProdutoInstituicao, cabalAntigo, user, linha)
          .isEmpty();
    }

    return FALSE;
  }

  public void salvarCadastroCompletoByArquivo(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      SecurityUser user,
      Integer arquivoCabecalho)
      throws FileNotFoundException {
    List<FuncionarioCadastroCompletoTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoCadastros);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    String hash = FileUtil.getHashFromFile(copy);

    if (logB2bCadastroViaArquivoService.findIfExists(hash)) {
      throw new GenericServiceException(
          "Esse arquivo já esta sendo processado por outra pessoa, Aguarde um momento...");
    }

    if (logB2bCadastroViaArquivoService.findIfExists(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdHierarquiaNivel(),
        user.getIdFilial(),
        user.getIdRegional(),
        user.getIdPontoDeRelacionamento(),
        Boolean.TRUE)) {
      throw new GenericServiceException(
          "Já possui um processo em andamento dessa filial, Aguarde um momento...");
    }

    if (ImportadorArquivo.isXlsx(nomeArquivo) || ImportadorArquivo.isXls(nomeArquivo)) {
      to =
          importadorFuncionarioXls.getImportacaoCadastroCompleto(
              new FileInputStream(copy), cabalAntigo, arquivoCabecalho);
    }

    LogB2bCadastroViaArquivo logB2bCadastroViaArquivo = new LogB2bCadastroViaArquivo();
    logB2bCadastroViaArquivo.setDataExecucao(new Date());
    logB2bCadastroViaArquivo.setIdAcessoUsuario(user.getIdUsuario());
    logB2bCadastroViaArquivo.setIdInstituicao(user.getIdInstituicao());
    logB2bCadastroViaArquivo.setIdProcessadora(user.getIdProcessadora());
    logB2bCadastroViaArquivo.setStatus(Boolean.TRUE);
    logB2bCadastroViaArquivo.setIdHierarquia(user.getIdHierarquiaNivel());
    logB2bCadastroViaArquivo.setIdFilial(user.getIdFilial());
    logB2bCadastroViaArquivo.setIdRegional(user.getIdRegional());
    logB2bCadastroViaArquivo.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    logB2bCadastroViaArquivo.setHash(hash);
    logB2bCadastroViaArquivo = logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);

    try {
      salvarCadastroCompletoByArquivo(to, idProdutoInstituicao, cabalAntigo, user);
    } catch (GenericServiceException e) {
      throw e;
    } finally {
      logB2bCadastroViaArquivo.setStatus(Boolean.FALSE);
      logB2bCadastroViaArquivoService.save(logB2bCadastroViaArquivo);
    }
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoCadPortadores(
      List<FuncionarioCargaTO> tos,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user,
      List<Integer> idsProdutoInstituicao) {
    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Map<String, VerificacaoLinhaTO> tempVerificacoes = null;
    FuncionarioCargaTO to = null;
    BigDecimal valorAtualizadoLimite = new BigDecimal(0);

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      tempVerificacoes =
          getVerificacaoCadPortadores(
              to,
              idProdutoInstituicao,
              nomeSetorFilial,
              user,
              i,
              valorAtualizadoLimite,
              idsProdutoInstituicao);

      if (tempVerificacoes != null && !tempVerificacoes.isEmpty()) {
        verificacoes.putAll(tempVerificacoes);
        ProdutoInstituicaoConfiguracao prod =
            produtoInstituicaoConfiguracaoService
                .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                    user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
        if (ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(prod.getIdProdutoPlataforma())
            && tempVerificacoes.containsKey("valorAtualizado:")) {
          valorAtualizadoLimite =
              valorAtualizadoLimite.add(
                  tempVerificacoes.get("valorAtualizado:").getSomaLimiteFuncionarios());
        }
      }
    }

    return verificacoes;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoCadPortadores(
      FuncionarioCargaTO to,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user,
      Integer linha,
      BigDecimal valorAtualizadoLimite,
      List<Integer> idsProdutoInstituicao) {

    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    String data = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    String validaData = data.substring(6, 10);
    erroRegistro = false;

    ProdutoInstituicaoConfiguracao prod =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());

    // Verificando se o CPF foi informado
    if (to.getCpf() == null || to.getCpf().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } // Verificando se o cpf é válido
    else if (to.getCpf() != null
        && to.getCpf().length() < 12
        && !DocumentoUtil.isValidCPF(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    } else if (to.getCpf() != null
        && to.getCpf().length() > 12
        && !DocumentoUtil.isCNPJ(to.getCpf())
        && !DocumentoUtil.isValidCNPJ(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }
    if (to.getCpf().length() > 12 && !prod.getEmitePropriaEmpresa()) {
      throw new GenericServiceException("Produto não permite cadastro de CNPJ");
    }

    ContaPagamento conta = new ContaPagamento();
    if (idsProdutoInstituicao.isEmpty()) {
      if (to.getCpf().length() < 12) {
        conta =
            contaPagamentoService.findByCpfAndProdutoAndHierarquia(
                to.getCpf(),
                idProdutoInstituicao,
                user.getIdProcessadora(),
                user.getIdInstituicao(),
                user.getIdRegional(),
                user.getIdFilial(),
                user.getIdPontoDeRelacionamento());
      }
    }
    // Verificando a existência de uma conta
    if (conta != null && conta.getIdConta() != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    } else {
      // verifica limite se o cpf não existir para o produto
      if (ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(prod.getIdProdutoPlataforma())) {

        HashMap<String, Object> map =
            verificaLimiteGlobalEmpresa(verificacoes, user, to, valorAtualizadoLimite);

        if (map.containsKey("msg")) {
          String msg = map.get("msg").toString();
          String resultMap = map.get("DTL").toString();
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.LIMITE_MAXIMO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, msg + " " + resultMap));
          erroRegistro = true;
        }
      }
      List<ExisteMatriculaEmpresaVO> matriculaGlobal =
          contaPagamentoService.findByMatriculaExisteEmpresa(to.getMatricula(), user);
      List<ContaPagamento> contaMatricula =
          contaPagamentoService.findByMatriculaAndProduto(
              to.getMatricula(), idProdutoInstituicao, user);

      // Verificando se o nome foi preenchido
      if (to.getNomeCompleto() == null || to.getNomeCompleto().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
        // Verificando a validade do nome completo
      } else if (!isNomeCompletoValido(to.getNomeCompleto())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<< " + to.getNomeCompleto() + " >>"));
        erroRegistro = true;
        // Verificando setor filial
      }

      // se setor filial é nulo
      if (to.getSetorFilial() == null || to.getSetorFilial().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.SETOR_FILIAL.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      }

      // Verificando Data de Nascimento
      if (to.getDataNascimento() == null) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } else if (!isDataNascimentoValida(to.getDataNascimento())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getDataNascimento().toString()));
        erroRegistro = true;
      } else if (to.getDataNascimento() != null
          && to.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
        erroRegistro = true;
      }

      if (to.getEmail() != null && !to.getEmail().isEmpty() && !validarEmail(to.getEmail())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<" + to.getEmail() + ">>"));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(to.getDddCelular()) && !isDDDCelularValido(to.getDddCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getDddCelular() + VALOR_INCORRETO));
        erroRegistro = true;
      } else if ((utilService.getProdutoInsCampanha().equals(idProdutoInstituicao)
              || utilService.getProdutoInsInfinancasPagamento().equals(idProdutoInstituicao))
          && !isDDDCelularValido(to.getDddCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<" + to.getDddCelular() + ">>"));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(to.getTelefoneCelular())
          && !isCelularValido(to.getTelefoneCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getTelefoneCelular() + VALOR_INCORRETO));
        erroRegistro = true;
      } else if ((utilService.getProdutoInsCampanha().equals(idProdutoInstituicao)
              || utilService.getProdutoInsInfinancasPagamento().equals(idProdutoInstituicao))
          && !isCelularValido(to.getTelefoneCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<" + to.getTelefoneCelular() + ">>"));
        erroRegistro = true;
      }
    }

    if (to.getIsCpfDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM CPF DUPLICADO: " + to.getCpf() + ">>"));
    }

    if (to.getIsMatriculaDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.MATRICULA.toString() + ":" + linha,
          new VerificacaoLinhaTO(
              0, linha, "<<ARQUIVO COM MATRÍCULA DUPLICADA: " + to.getMatricula() + ">>"));
    }

    if (to.getIsDuplicatedRegister()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DUPLICADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM REGISTRO DUPLICADO>>"));
    }

    if (idsProdutoInstituicao.isEmpty()) {
      if (prod.getIdGrupoProduto() != null
          && TipoProdutoEnum.CONTA_LIVRE.equals(prod.getTipoProduto())
          && ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(user.getIdInstituicao())) {

        ContaPagamento contaLivre =
            findContasPagamentosComGrupoAcessoETipoProduto(
                user.getIdProcessadora(),
                user.getIdInstituicao(),
                user.getIdRegional(),
                user.getIdFilial(),
                user.getIdPontoDeRelacionamento(),
                to.getMatricula(),
                prod.getTipoPessoa(),
                prod.getIdGrupoProduto(),
                TipoProdutoEnum.CONTA_LIVRE);

        if (contaLivre != null) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_CADASTRADO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, to.getCpf()));
          erroRegistro = true;
        }

      } else if (prod.getIdGrupoProduto() != null
          && !TipoProdutoEnum.CONTA_LIVRE.equals(prod.getTipoProduto())
          && ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(user.getIdInstituicao())) {
        Integer contaPagamento =
            contaPagamentoRepository
                .isContaPagamentoExisteByHierarquiaAndDocumentoAndProdutoAndTitularidade(
                    prod.getIdProcessadora(),
                    prod.getIdInstituicao(),
                    user.getIdRegional(),
                    user.getIdFilial(),
                    user.getIdPontoDeRelacionamento(),
                    to.getMatricula(),
                    idProdutoInstituicao);
        if (contaPagamento == 0) {
          ContaPagamento contaPagamentoGrupo =
              findContasPagamentosComGrupoAcessoETipoProduto(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  user.getIdPontoDeRelacionamento(),
                  to.getMatricula(),
                  prod.getTipoPessoa(),
                  prod.getIdGrupoProduto(),
                  TipoProdutoEnum.CONTA_LIVRE);
          if (contaPagamentoGrupo == null) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_MULTI_CONTAS.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, to.getCpf()));
            erroRegistro = true;
          }
        }
      }
    }

    if (erroRegistro) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
    }

    return verificacoes;
  }

  @Transactional
  public void salvarCadastroCompletoByArquivo(
      List<FuncionarioCadastroCompletoTO> tos,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      SecurityUser user) {
    CadastrarContaPagamentoPessoaRequest pessoa = null;
    FuncionarioCadastroCompletoTO to = null;
    SetorFilial setorFilial = null;
    Boolean produtoIntegracao = false;
    HashMap<String, List<Long>> cpfsContasPreCadastro = new HashMap<>();
    Boolean temCorresp =
        produtoInstituicaoCorrespondenteService.findIfExistsCorresp(idProdutoInstituicao);
    if (temCorresp) {
      ContasCredenciaisReplicaveisReponse empresa =
          produtoInstituicaoCorrespondenteService.montaVerificacaoEmpresa(
              idProdutoInstituicao, user);
      produtoInstituicaoCorrespondenteService.validarDadosReplicacao(empresa);
    }
    if (Boolean.TRUE.equals(
        produtoInstituicaoConfiguracaoService.obterCargaIntegracao(idProdutoInstituicao))) {
      produtoIntegracao = TRUE;
    }

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      if (isCadastroCompletoArquivoValido(to, idProdutoInstituicao, cabalAntigo, user, i)) {
        pessoa = new CadastrarContaPagamentoPessoaRequest();

        pessoa.setIdInstituicao(user.getIdInstituicao());
        pessoa.setIdProcessadora(user.getIdProcessadora());
        pessoa.setIdRegional(user.getIdRegional());
        pessoa.setIdFilial(user.getIdFilial());
        pessoa.setIdUsuarioInclusao(user.getIdUsuario());
        pessoa.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
        pessoa.setCadastroOrigem(ORIGEM_CADASTRO_VIA_ARQUIVO);

        ProdutoInstituicaoConfiguracao prodConfig =
            produtoInstituicaoConfiguracaoService
                .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                    user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
        if (prodConfig == null) {
          throw new GenericServiceException(
              "Não foi possível completar a operação. Produto Instituição Configuração não encontrado.");
        }
        List<ValorCargaProdutoInstituicao> produtoList = new ArrayList<>();
        ValorCargaProdutoInstituicao produto = new ValorCargaProdutoInstituicao();
        produto.setIdProdutoInstituicao(idProdutoInstituicao);
        produto.setValorCargaPadrao(0.0);
        produto.setIdProdutoPlataforma(prodConfig.getIdProdutoPlataforma());
        produtoList.add(produto);
        pessoa.setValoresCargasProdutos(produtoList);
        pessoa.setTipoPessoa(PESSOA_FISICA);

        if (utilService.getCorporativoInfinancas().contains(idProdutoInstituicao)) {
          if (to.getNomeCompleto() != null)
            pessoa.setRazaoSocial(
                Abreviador.nomeNormalizado(to.getNomeCompleto(), PESSOA_JURIDICA));
          if (to.getDataNascimento() != null) pessoa.setDataFundacao(to.getDataNascimento());
        } else {
          if (to.getDataNascimento() != null) pessoa.setDataNascimento(to.getDataNascimento());
        }

        pessoa.setEstrangeiro(Boolean.FALSE);
        if (to.getSexo() != null && ConstantesB2B.SEXO_MASCULINO.equals(to.getSexo())) {
          pessoa.setIdSexo(1);
        } else if (to.getSexo() != null && ConstantesB2B.SEXO_FEMININO.equals(to.getSexo())) {
          pessoa.setIdSexo(2);
        }
        String descricao = null;
        if (to.getSetorFilial() != null) {
          descricao = to.getSetorFilial();
        } else {
          descricao = "MATRIZ";
        }
        setorFilial = setorFilialService.saveByDescricao(descricao, user);
        pessoa.setIdSetorFilial(setorFilial.getIdSetorFilial());
        if (to.getCpf() != null) {
          pessoa.setDocumento(to.getCpf());
          pessoa.setMatricula(to.getCpf());
        }
        if (to.getNomeCompleto() != null)
          pessoa.setNomeCompleto(Abreviador.nomeNormalizado(to.getNomeCompleto(), PESSOA_FISICA));
        if (to.getNomeCompleto() != null)
          pessoa.setNomeEmbossado(Abreviador.abreviarNome(pessoa.getNomeCompleto()));
        if (to.getIdEstadoCivil() != null) pessoa.setIdEstadoCivil(to.getIdEstadoCivil());
        if (to.getNumeroRg() != null) pessoa.setRg(to.getNumeroRg());
        if (to.getRgDataEmissao() != null) pessoa.setRgDataEmissao(to.getRgDataEmissao());
        if (to.getRgOrgaoEmissor() != null) pessoa.setRgOrgaoEmissor(to.getRgOrgaoEmissor());
        if (to.getRgUFOrgaoEmissor() != null) pessoa.setRgUfOrgaoEmissor(to.getRgUFOrgaoEmissor());
        if (to.getNomeMae() != null)
          pessoa.setNomeMae(Abreviador.nomeNormalizado(to.getNomeMae(), PESSOA_FISICA));
        if (to.getNomePai() != null)
          pessoa.setNomePai(Abreviador.nomeNormalizado(to.getNomePai(), PESSOA_FISICA));
        if (to.getNacionalidade() != null) pessoa.setNacionalidade(to.getNacionalidade());
        if (to.getNaturalidade() != null) pessoa.setNaturalidade(to.getNaturalidade());
        if (to.getEmail() != null) pessoa.setEmail(to.getEmail());
        if (to.getEmailProfissional() != null)
          pessoa.setEmailProfissional(to.getEmailProfissional());

        if (to.getDddTelefoneResidencial() != null) {
          Integer x = new Integer(to.getDddTelefoneResidencial());
          pessoa.setDddTelefoneResidencial(x);
        }
        if (to.getTelefoneResidencial() != null) {
          Integer y = new Integer(to.getTelefoneResidencial());
          pessoa.setTelefoneResidencial(y);
        }
        if (to.getDddCelular() != null) {
          Integer x = new Integer(to.getDddCelular());
          pessoa.setDddTelefoneCelular(x);
        }
        if (to.getTelefoneCelular() != null) {
          Integer y = new Integer(to.getTelefoneCelular());
          pessoa.setTelefoneCelular(y);
        }

        if (to.getContaBancaria() != null && !to.getContaBancaria().equals("")) {
          pessoa.setContaBancariaX(to.getContaBancaria());
        }
        if (to.getIdBanco() != null && !to.getIdBanco().equals("")) {
          Integer y = new Integer(to.getIdBanco());
          pessoa.setIdBanco(y);
        }
        if (to.getIdAgencia() != null && !to.getIdAgencia().equals("")) {
          Integer y = new Integer(to.getIdAgencia());
          pessoa.setIdAgencia(y);
        }
        if (to.getTipoContaBancaria() != null && !to.getTipoContaBancaria().equals("")) {
          Integer y = new Integer(to.getTipoContaBancaria());
          pessoa.setTipoContaBancaria(y);
        }
        if (to.getTipoChavePix() != null && !to.getTipoChavePix().equals("")) {
          Integer y = new Integer(to.getTipoChavePix());
          pessoa.setTipoChavePix(y);
        }
        if (to.getChavePix() != null && !to.getChavePix().equals("")) {
          pessoa.setChavePix(to.getChavePix());
        }
        if (to.getCep() != null) {
          EnderecosPessoaRequest endPessoa = new EnderecosPessoaRequest();
          List<EnderecoPessoaRequest> enderecos = new ArrayList<>();
          EnderecoPessoaRequest end = new EnderecoPessoaRequest();
          end.setCep(Abreviador.regexEndereco(to.getCep(), CEP_E_NUMEROS));
          end.setLogradouro(Abreviador.regexEndereco(to.getLogradouro(), OUTROS));
          end.setBairro(Abreviador.regexEndereco(to.getBairro(), OUTROS));
          end.setCidade(Abreviador.regexEndereco(to.getCidade(), OUTROS));
          end.setNumero(Abreviador.regexEndereco(to.getNumero(), CEP_E_NUMEROS));
          end.setComplemento(Abreviador.regexEndereco(to.getComplemento(), OUTROS));
          if (COD_DOC_CNPJ_2.equals(to.getTipoDocumento())) {
            end.setIdTipoEndereco(ENDERECO_CORRESPONDENCIA);
          } else {
            end.setIdTipoEndereco(ENDERECO_RESIDENCIAL);
          }
          end.setIdUsuarioInclusao(user.getIdUsuario());
          end.setUf(Abreviador.regexEndereco(to.getUf(), UF));
          enderecos.add(end);
          endPessoa.setEnderecos(enderecos);
          pessoa.setEnderecosPessoaRequest(endPessoa);
        }
        if (to.getNomeCartaoImpresso() != null) {
          pessoa.setNomeCartaoImpresso(
              to.getNomeCartaoImpresso()); // transportado em Pessoa e persistido em ContaPesssoa
        }

        ContaPagamento conta = contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(pessoa);
        if (produtoIntegracao) {
          List<Long> contasIntegracao = new ArrayList<>();
          contasIntegracao.add(conta.getIdConta());
          cpfsContasPreCadastro.put(pessoa.getDocumento(), contasIntegracao);
        }
      }
    }
    if (temCorresp) {
      produtoInstituicaoCorrespondenteService.preparaAgendamentoReplicar();
    }
    if (produtoIntegracao) {
      preCadastroService.agendarPreCadastro(cpfsContasPreCadastro, user);
    }
  }

  public Map<String, VerificacaoLinhaTO> getVerificacao(
      List<FuncionarioCargaTO> tos,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user) {
    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Map<String, VerificacaoLinhaTO> tempVerificacoes = null;
    FuncionarioCargaTO to = null;
    BigDecimal valorAtualizadoLimite = new BigDecimal(0);

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      tempVerificacoes =
          getVerificacao(to, idProdutoInstituicao, nomeSetorFilial, user, i, valorAtualizadoLimite);

      if (tempVerificacoes != null && !tempVerificacoes.isEmpty()) {
        verificacoes.putAll(tempVerificacoes);
        ProdutoInstituicaoConfiguracao prod =
            produtoInstituicaoConfiguracaoService
                .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                    user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
        if (ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(prod.getIdProdutoPlataforma())
            && tempVerificacoes.containsKey("valorAtualizado:")) {
          valorAtualizadoLimite =
              valorAtualizadoLimite.add(
                  tempVerificacoes.get("valorAtualizado:").getSomaLimiteFuncionarios());
        }
      }
    }

    return verificacoes;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacao(
      FuncionarioCargaTO to,
      Integer idProdutoInstituicao,
      String nomeSetorFilial,
      SecurityUser user,
      Integer linha,
      BigDecimal valorAtualizadoLimite) {

    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    String data = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    String validaData = data.substring(6, 10);
    erroRegistro = false;

    ProdutoInstituicaoConfiguracao prod =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());

    // Verificando se o CPF foi informado
    if (to.getCpf() == null || to.getCpf().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } // Verificando se o cpf é válido
    else if (to.getCpf() != null
        && to.getCpf().length() < 12
        && !DocumentoUtil.isValidCPF(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    } else if (to.getCpf() != null
        && to.getCpf().length() > 12
        && !DocumentoUtil.isCNPJ(to.getCpf())
        && !DocumentoUtil.isValidCNPJ(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }
    if (to.getCpf().length() > 12 && !prod.getEmitePropriaEmpresa()) {
      throw new GenericServiceException("Produto não permite cadastro de CNPJ");
    }

    if ((to.getParcelamento() != null
        && !Objects.equals(to.getParcelamento(), "")
        && Integer.parseInt(to.getParcelamento()) > 100)) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.PARCELAMENTO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getParcelamento()));
      erroRegistro = true;
    } else if (prod.getIdProdutoPlataforma() != 4
        && to.getParcelamento() != null
        && !Objects.equals(to.getParcelamento(), "")) {
      throw new GenericServiceException(
          "Coluna J: Percentual de Saldo para Parcelamento está condicionado a produtos Convênio");
    }

    ContaPagamento conta = new ContaPagamento();
    if (to.getCpf().length() < 12) {
      if (!produtoInstituicaoConfiguracaoService.obterPermiteMultiplasContas(
          idProdutoInstituicao)) {
        conta =
            contaPagamentoService.findByCpfAndProdutoAndHierarquia(
                to.getCpf(),
                idProdutoInstituicao,
                user.getIdProcessadora(),
                user.getIdInstituicao(),
                user.getIdRegional(),
                user.getIdFilial(),
                user.getIdPontoDeRelacionamento());
      }
    }

    // Verificando a existência de uma conta
    if (conta != null && conta.getIdConta() != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    } else {
      // verifica limite se o cpf não existir para o produto
      if (ConstantesB2B.PROD_PLATAFORMA_CONVENIO.equals(prod.getIdProdutoPlataforma())) {

        HashMap<String, Object> map =
            verificaLimiteGlobalEmpresa(verificacoes, user, to, valorAtualizadoLimite);

        if (map.containsKey("msg")) {
          String msg = map.get("msg").toString();
          String resultMap = map.get("DTL").toString();
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.LIMITE_MAXIMO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, msg + " " + resultMap));
          erroRegistro = true;
        }
      }
      List<ExisteMatriculaEmpresaVO> matriculaGlobal =
          contaPagamentoService.findByMatriculaExisteEmpresa(to.getMatricula(), user);
      List<ContaPagamento> contaMatricula =
          contaPagamentoService.findByMatriculaAndProduto(
              to.getMatricula(), idProdutoInstituicao, user);
      // Verificando se a matrícula foi informada
      if (to.getMatricula() == null || to.getMatricula().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.MATRICULA.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } // Verifica se já tem a matricula na empresa
      else if (!matriculaGlobal.isEmpty()) {
        for (ExisteMatriculaEmpresaVO existe : matriculaGlobal) {
          if (!to.getCpf().equals(existe.getDocumento())
              && to.getMatricula().equals(existe.getMatricula())) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.MATRICULA_CADASTRADA_SISTEMA.toString()
                    + ":"
                    + linha,
                new VerificacaoLinhaTO(0, linha, to.getMatricula()));
            erroRegistro = true;
          }
        }
        // Verifica se já tem a matricula com esse produto
      } else if (!contaMatricula.isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.MATRICULA_CADASTRADA.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getMatricula()));
        erroRegistro = true;
      }
      // Verificando se o nome foi preenchido
      if (to.getNomeCompleto() == null || to.getNomeCompleto().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
        // Verificando a validade do nome completo
      } else if (!isNomeCompletoValido(to.getNomeCompleto())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<< " + to.getNomeCompleto() + " >>"));
        erroRegistro = true;
        // Verificando setor filial
      }

      if (to.getValorCarga() == null) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.VALOR_CARGA.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      }

      // se setor filial é nulo
      if (to.getSetorFilial() == null || to.getSetorFilial().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.SETOR_FILIAL.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      }

      // Verificando Data de Nascimento
      if (to.getDataNascimento() == null) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } else if (!isDataNascimentoValida(to.getDataNascimento())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getDataNascimento().toString()));
        erroRegistro = true;
      } else if (to.getDataNascimento() != null
          && to.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
        erroRegistro = true;
      }

      if (to.getEmail() != null && !to.getEmail().isEmpty() && !validarEmail(to.getEmail())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<" + to.getEmail() + ">>"));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(to.getDddCelular()) && !isDDDCelularValido(to.getDddCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getDddCelular() + VALOR_INCORRETO));
        erroRegistro = true;
      } else if ((utilService.getProdutoInsCampanha().equals(idProdutoInstituicao)
              || utilService.getProdutoInsInfinancasPagamento().equals(idProdutoInstituicao))
          && !isDDDCelularValido(to.getDddCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<" + to.getDddCelular() + ">>"));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(to.getTelefoneCelular())
          && !isCelularValido(to.getTelefoneCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, to.getTelefoneCelular() + VALOR_INCORRETO));
        erroRegistro = true;
      } else if ((utilService.getProdutoInsCampanha().equals(idProdutoInstituicao)
              || utilService.getProdutoInsInfinancasPagamento().equals(idProdutoInstituicao))
          && !isCelularValido(to.getTelefoneCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<<" + to.getTelefoneCelular() + ">>"));
        erroRegistro = true;
      }
    }

    if (to.getIsCpfDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM CPF DUPLICADO: " + to.getCpf() + ">>"));
    }

    if (to.getIsMatriculaDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.MATRICULA.toString() + ":" + linha,
          new VerificacaoLinhaTO(
              0, linha, "<<ARQUIVO COM MATRÍCULA DUPLICADA: " + to.getMatricula() + ">>"));
    }

    if (to.getIsDuplicatedRegister()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DUPLICADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM REGISTRO DUPLICADO>>"));
    }

    if (erroRegistro) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
    }

    return verificacoes;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoCadCompl(
      List<FuncionarioCadastroCompletoTO> tos,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      SecurityUser user) {
    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Map<String, VerificacaoLinhaTO> tempVerificacoes = null;
    FuncionarioCadastroCompletoTO to = null;

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      tempVerificacoes = getVerificacaoCadComp(to, idProdutoInstituicao, cabalAntigo, user, i);

      if (tempVerificacoes != null && !tempVerificacoes.isEmpty()) {
        verificacoes.putAll(tempVerificacoes);
      }
    }

    return verificacoes;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoCadComp(
      FuncionarioCadastroCompletoTO to,
      Integer idProdutoInstituicao,
      Integer cabalAntigo,
      SecurityUser user,
      Integer linha) {
    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Date dataDt = new Date();
    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    dataDt = cal.getTime();
    String dataString = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    String validaData = dataString.substring(6, 10);
    erroRegistro = false;

    ProdutoInstituicaoConfiguracao prodConf =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
    if (prodConf == null) {
      throw new GenericServiceException(
          "Não foi possível completar a operação. Produto Instituição Configuração não encontrado.");
    }
    ProdutoContratado prodContrato =
        produtoContratadoService.findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento(),
            idProdutoInstituicao);
    if (prodContrato == null) {
      throw new GenericServiceException(
          "Não foi possível completar a operação. Produto Contratado não encontrado.");
    }
    // Verificando se o Tipo de Documento foi informado
    if (to.getTipoDocumento() == null || to.getTipoDocumento().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.TIPO_DOCUMENTO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    }
    if (to.getTipoDocumento() != null
        && !COD_DOC_CPF_1.equals(to.getTipoDocumento())
        && !COD_DOC_CNPJ_2.equals(to.getTipoDocumento())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.TIPO_DOCUMENTO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<TIPO INVÁLIDO>>"));
      erroRegistro = true;
    }
    // Verificando se o CPF foi informado
    if (to.getCpf() == null || to.getCpf().isEmpty() || to.getCpf().equals("00000000000")) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } // Verificando se o cpf é válido
    else if (to.getCpf() != null
        && COD_DOC_CPF_1.equals(to.getTipoDocumento())
        && !DocumentoUtil.isValidCPF(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    } else if (to.getCpf() != null
        && COD_DOC_CNPJ_2.equals(to.getTipoDocumento())
        && !DocumentoUtil.isCNPJ(to.getCpf())
        && !DocumentoUtil.isValidCNPJ(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }
    if (COD_DOC_CNPJ_2.equals(to.getTipoDocumento()) && !prodConf.getEmitePropriaEmpresa()) {
      throw new GenericServiceException("Produto não permite cadastro de CNPJ");
    }

    ContaPagamento conta = new ContaPagamento();
    if (COD_DOC_CPF_1.equals(to.getTipoDocumento())) {
      conta =
          contaPagamentoService.findByTipoDocAndDocAndProduto(
              to.getCpf(), idProdutoInstituicao, user, Integer.valueOf(COD_DOC_CPF_1));
    }

    // Verificando a existência de uma conta
    if (conta != null && conta.getIdConta() != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    } else {
      // Verificando se o nome foi preenchido
      if (to.getNomeCompleto() == null || to.getNomeCompleto().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
        // Verificando a validade do nome completo
      } else if (!isNomeCompletoValido(to.getNomeCompleto())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, "<< " + to.getNomeCompleto() + " >>"));
        erroRegistro = true;
      } else if (to.getNomeCompleto().length() > TAMANHO_NOME_COMPLETO) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoLinhaTO(
                0, linha, "<< LIMITE DE 80 CARACTERES -- Adapte para até 80 caracteres >>"));
        erroRegistro = true;
      }

      // VERIFICA SE TEM DDD
      if ((!isDDDCelularValido(to.getDddCelular()))) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO_OU_INVALIDO));
        erroRegistro = true;
      }
      if ((!isDDDCelularValido(to.getDddCelular()))
          && (to.getTelefoneCelular() != null && isCelularValido(to.getTelefoneCelular()))) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO_DDD));
        erroRegistro = true;
      }
      if ((to.getDddCelular() != null && isDDDCelularValido(to.getDddCelular()))
          && (to.getTelefoneCelular() == null || !isCelularValido(to.getTelefoneCelular()))) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO_CELULAR));
        erroRegistro = true;
      }

      // VERIFICA CELULAR
      if ((!isCelularValido(to.getTelefoneCelular()))) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO_OU_INVALIDO));
        erroRegistro = true;
      }
      if ((!isCelularValido(to.getTelefoneCelular()))
          && (to.getDddCelular() != null && isDDDCelularValido(to.getDddCelular()))) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO_CELULAR));
        erroRegistro = true;
      }
      if ((to.getTelefoneCelular() != null && isCelularValido(to.getTelefoneCelular()))
          && (to.getDddCelular() == null || !isDDDCelularValido(to.getDddCelular()))) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoLinhaTO(0, linha, NAO_INFORMADO_DDD));
        erroRegistro = true;
      }

      // se cabal ou visa
      if (ConstantesB2B.CALBAL_COM_CADASTRO_COMPLETO.equals(cabalAntigo)
          && !ConstantesB2B.PROD_PLATAFORMA_ORDEM_PAGAMENTO.equals(
              prodConf.getIdProdutoPlataforma())
          && !ConstantesB2B.PROD_PLATAFORMA_TRANSFER_BANCARIA.equals(
              prodConf.getIdProdutoPlataforma())) {

        if (COD_DOC_CNPJ_2.equals(to.getTipoDocumento()) && !prodConf.getEmitePropriaEmpresa()) {
          if (!isNomeCompletoValido(to.getNomeMae())) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NOME_MAE.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getNomeMae().length() > TAMANHO_NOME_COMPLETO) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NOME_MAE.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 80 CARACTERES -- Adapte para até 80 caracteres >>"));
            erroRegistro = true;
          }
          if (!isNomeCompletoValido(to.getNomePai())) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NOME_PAI.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getNomePai().length() > TAMANHO_NOME_COMPLETO) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NOME_PAI.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 80 CARACTERES -- Adapte para até 80 caracteres >>"));
            erroRegistro = true;
          }
          if (to.getNaturalidade() == null || to.getNaturalidade().isEmpty()) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NATURALIDADE.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getNaturalidade().length() > TAMANHO_NATURALIDADE_NATURALIDADE) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NATURALIDADE.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 60 CARACTERES -- Adapte para até 60 caracteres >>"));
            erroRegistro = true;
          }
          if (to.getNacionalidade() == null || to.getNacionalidade().isEmpty()) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NACIONALIDADE.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getNacionalidade().length() > TAMANHO_NATURALIDADE_NATURALIDADE) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NACIONALIDADE.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 60 CARACTERES -- Adapte para até 60 caracteres >>"));
            erroRegistro = true;
          }

          if (to.getNumeroRg() == null || to.getNumeroRg().isEmpty()) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NUMERO_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getNumeroRg().length() > TAMANHO_NUMERO_RG) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.NUMERO_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 15 CARACTERES -- Adapte para até 15 caracteres >>"));
            erroRegistro = true;
          }
          if (to.getRgDataEmissao() == null || to.getRgDataEmissao().equals("")) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.EMISSAO_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (!isDataNascimentoValida(to.getRgDataEmissao())
              && to.getRgDataEmissao() != null) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.EMISSAO_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, to.getRgDataEmissao().toString()));
            erroRegistro = true;
          } else if (to.getRgDataEmissao().compareTo(dataDt) == 0) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.EMISSAO_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getRgDataEmissao() != null) {
            if (to.getRgDataEmissao().toString().substring(24, 28).equals(validaData)) {
              verificacoes.put(
                  VerificacaoImportacaoArquivoEnum.EMISSAO_RG.toString() + ":" + linha,
                  new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
              erroRegistro = true;
            }
          }
          if (to.getRgOrgaoEmissor() == null || to.getRgOrgaoEmissor().isEmpty()) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.EMISSOR_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (to.getRgOrgaoEmissor().length() > TAMANHO_ORGAO_EMISSOR_RG) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.EMISSOR_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 10 CARACTERES -- Adapte para até 10 caracteres >>"));
            erroRegistro = true;
          }
          if (to.getSexo() == null || to.getSexo().isEmpty()) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.SEXO.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          } else if (!to.getSexo().toUpperCase().equals("M")
              && !to.getSexo().toUpperCase().equals("F")) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.SEXO.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, "<<INFORMAÇÃO INCORRETA>>"));
            erroRegistro = true;
          }
          if (to.getIdEstadoCivil() == null || to.getIdEstadoCivil().toString().isEmpty()) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.ESTADO_CIVIL.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
            erroRegistro = true;
          }
          if (to.getIdEstadoCivil() != null && !to.getIdEstadoCivil().toString().isEmpty()) {
            if (!isEstadoCivilValido(to.getIdEstadoCivil().toString())) {
              verificacoes.put(
                  VerificacaoImportacaoArquivoEnum.ESTADO_CIVIL.toString() + ":" + linha,
                  new VerificacaoLinhaTO(
                      0, linha, "<<" + to.getIdEstadoCivil() + " - CARACTER INVÁLIDO>>"));
              erroRegistro = true;
            }
          }
        }
        if (to.getDataNascimento() == null || to.getDataNascimento().equals("")) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        } else if (to.getDataNascimento() != null
            && !isDataNascimentoValida(to.getDataNascimento())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, to.getDataNascimento().toString()));
          erroRegistro = true;
        } else if (to.getDataNascimento() != null) {
          if (to.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
            erroRegistro = true;
          }
        }
        if (to.getLogradouro() == null || to.getLogradouro().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.LOGRADOURO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        } else if (to.getLogradouro().length() > TAMANHO_LOGRADOURO_BAIRRO_CIDADE) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.LOGRADOURO.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< LIMITE DE 100 CARACTERES -- Adapte para até 100 caracteres  >>"));
          erroRegistro = true;
        }
        if (to.getNumero() == null || to.getNumero().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.NUMERO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        }
        if (to.getBairro() == null || to.getBairro().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.BAIRRO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        } else if (to.getBairro().length() > TAMANHO_LOGRADOURO_BAIRRO_CIDADE) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.BAIRRO.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< LIMITE DE 100 CARACTERES -- Adapte para até 100 caracteres >>"));
          erroRegistro = true;
        }
        if (to.getCidade() == null || to.getCidade().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CIDADE.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        } else if (to.getCidade().length() > TAMANHO_LOGRADOURO_BAIRRO_CIDADE) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CIDADE.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< LIMITE DE 100 CARACTERES -- Adapte para até 100 caracteres >>"));
          erroRegistro = true;
        }
        if (to.getUf() == null || to.getUf().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.UF.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        }
        if (to.getCep() == null || to.getCep().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CEP.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        } else if (!isCepValidoSemException(to.getCep())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CEP.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, "<<CEP INVÁLIDO - " + to.getCep() + ">>"));
          erroRegistro = true;
        }
        if (to.getEmail() != null && !to.getEmail().isEmpty()) {
          if (!validarEmail(to.getEmail())) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, "<<" + to.getEmail() + ">>"));
            erroRegistro = true;
          }
        }
        if (to.getEmail() == null || to.getEmail().isEmpty()) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        } else if (to.getEmail().length() > TAMANHO_EMAIL) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< LIMITE DE 80 CARACTERES -- Adapte para até 80 caracteres >>"));
          erroRegistro = true;
        }
        if (to.getEmailProfissional() != null && !to.getEmailProfissional().isEmpty()) {
          if (!validarEmail(to.getEmailProfissional())) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, "<<" + to.getEmail() + ">>"));
            erroRegistro = true;
          } else if (to.getEmailProfissional().length() > TAMANHO_EMAIL) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 80 CARACTERES -- Adapte para até 80 caracteres >>"));
            erroRegistro = true;
          }
        }
        // VERIFICA SE TEM DDD
        if ((StringUtils.isBlank(to.getDddTelefoneResidencial())
                && !isDDDCelularValido(to.getDddTelefoneResidencial()))
            && (StringUtils.isNotBlank(to.getTelefoneResidencial())
                && isCelularValido(to.getTelefoneResidencial()))) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.DDD_RESIDENCIAL.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, "<<" + to.getDddTelefoneResidencial() + ">>"));
          erroRegistro = true;
          // VERIFICA SE TEM NUMERO TELEFONE
        }
        if ((StringUtils.isNotBlank(to.getDddTelefoneResidencial())
                && isDDDCelularValido(to.getDddTelefoneResidencial()))
            && (StringUtils.isBlank(to.getTelefoneResidencial())
                && !isCelularValido(to.getTelefoneResidencial()))) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.TELEFONE_RESIDENCIAL.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, "<<" + to.getTelefoneResidencial() + ">>"));
          erroRegistro = true;
        }
        // residencial
        if (to.getDddTelefoneResidencial() != null
            && !isDDDCelularValido(to.getDddTelefoneResidencial())
            && !to.getDddTelefoneResidencial().equals("")) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.DDD_RESIDENCIAL.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        }
        if (to.getTelefoneResidencial() != null
            && !isTelFixoValido(to.getTelefoneResidencial())
            && !to.getTelefoneResidencial().equals("")) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.TELEFONE_RESIDENCIAL.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
          erroRegistro = true;
        }
        if (COD_DOC_CNPJ_2.equals(to.getTipoDocumento()) && !prodConf.getEmitePropriaEmpresa()) {
          if (to.getRgUFOrgaoEmissor() == null) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.UF_EMISSOR_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(0, linha, "<<" + to.getRgUFOrgaoEmissor() + ">>"));
            erroRegistro = true;
          } else if (to.getRgUFOrgaoEmissor() != null
              && to.getRgUFOrgaoEmissor().length() > TAMANHO_UF) {
            verificacoes.put(
                VerificacaoImportacaoArquivoEnum.UF_EMISSOR_RG.toString() + ":" + linha,
                new VerificacaoLinhaTO(
                    0, linha, "<< LIMITE DE 2 CARACTERES -- Adapte para até 2 caracteres >>"));
            erroRegistro = true;
          }
        }
      }

      if (ConstantesB2B.PROD_PLATAFORMA_TRANSFER_BANCARIA.equals(
          prodConf.getIdProdutoPlataforma())) {

        if ((to.getIdBanco() == null || to.getIdBanco().isEmpty())
            || (to.getIdAgencia() == null || to.getIdAgencia().isEmpty())
            || (to.getContaBancaria() == null || to.getContaBancaria().isEmpty())
            || (to.getTipoContaBancaria() == null || to.getTipoContaBancaria().isEmpty())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.DADOS_BANCARIOS.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0,
                  linha,
                  "<<Banco: "
                      + to.getIdBanco()
                      + ">> <<Agência: "
                      + to.getIdAgencia()
                      + ">> <<Conta bancária: "
                      + to.getContaBancaria()
                      + ">> <<Tipo de Conta: "
                      + to.getTipoContaBancaria()
                      + ">>"));
          erroRegistro = true;
        }

        if (!isBancoValido(to.getIdBanco())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.BANCO.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< " + to.getIdBanco() + " - CÓDIGO DE BANCO NÃO IDENTIFICADO >>"));
          erroRegistro = true;
        }
        if (!isAgenciaValido(to.getIdAgencia(), to.getIdBanco())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.AGENCIA.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0,
                  linha,
                  "<< " + to.getIdAgencia() + " - CÓDIGO DE AGÊNCIA NÃO IDENTIFICADA >>"));
          erroRegistro = true;
        }
        if (StringUtils.isBlank(to.getContaBancaria())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CONTA.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< " + to.getContaBancaria() + " - CONTA NÃO IDENTIFICADA >>"));
          erroRegistro = true;
        }
        if (!isTipoContaValido(to.getTipoContaBancaria())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.TIPO_CONTA.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0,
                  linha,
                  "<< " + to.getTipoContaBancaria() + " - TIPO DE CONTA NÃO IDENTIFICADO >>"));
          erroRegistro = true;
        }
        if (TIPO_CONTA_BANCARIA_FACIL_023.equals(to.getTipoContaBancaria())
            && !COD_BANCO_CAIXA_ECONOMICA_FEDERAL.equals(to.getIdBanco())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.TIPO_CONTA.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0,
                  linha,
                  " "
                      + to.getTipoContaBancaria()
                      + " - << TIPO DE CONTA 'Fácil 023' SOMENTE PARA CONTAS DO BANCO CAIXA ECONÔMICA FEDERAL >>"));
          erroRegistro = true;
        }
        if ((to.getTipoChavePix() != null
                && (Objects.equals(to.getChavePix(), "") || to.getChavePix() == null))
            || to.getChavePix() != null
                && (to.getChavePix().equals("00000000000")
                    || to.getChavePix().equals("00000000000000")
                    || to.getChavePix().equals("+55"))) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CHAVE_PIX.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, "<< CHAVE PIX NÃO IDENTIFICADO >>"));
          erroRegistro = true;
        }
        // Verificando se o documento é válido
        if (to.getChavePix() != null
            && COD_PIX_CPF.equals(to.getTipoChavePix())
            && !DocumentoUtil.isValidCPF(to.getChavePix())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CHAVE_PIX_DOCUMENTO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, to.getChavePix()));
          erroRegistro = true;
        } else if (to.getChavePix() != null
            && COD_PIX_CNPJ.equals(to.getTipoChavePix())
            && !DocumentoUtil.isCNPJ(to.getChavePix())
            && !DocumentoUtil.isValidCNPJ(to.getChavePix())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.CHAVE_PIX_DOCUMENTO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, to.getChavePix()));
          erroRegistro = true;
        }
      }
      if (prodConf.getBandeiraProduto() != null) {
        if (!BANDEIRA_PRODUTO_VISA.equals(prodConf.getBandeiraProduto())
            && StringUtils.isNotBlank(to.getNomeCartaoImpresso())) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.NOME_CARTAO_EXCLUSIVO.toString() + ":" + linha,
              new VerificacaoLinhaTO(0, linha, "<<" + to.getNomeCartaoImpresso() + ">>"));
          erroRegistro = true;
        }
        if (StringUtils.isNotBlank(to.getNomeCartaoImpresso())
            && to.getNomeCartaoImpresso().length() > TAMANHO_NOME_EXCLUSIVO_CARTAO) {
          verificacoes.put(
              VerificacaoImportacaoArquivoEnum.NOME_CARTAO_EXCLUSIVO.toString() + ":" + linha,
              new VerificacaoLinhaTO(
                  0, linha, "<< LIMITE DE 26 CARACTERES -- Adapte para até 26 caracteres >>"));
          erroRegistro = true;
        }
      }
    }

    if (to.getIsCpfDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM CPF DUPLICADO: " + to.getCpf() + ">>"));
    }
    if (to.getIsMatriculaDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.MATRICULA.toString() + ":" + linha,
          new VerificacaoLinhaTO(
              0, linha, "<<ARQUIVO COM MATRÍCULA DUPLICADA: " + to.getMatricula() + ">>"));
    }
    if (to.getIsDuplicatedRegister()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DUPLICADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM REGISTRO DUPLICADO>>"));
    }
    if (erroRegistro) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
    }

    return verificacoes;
  }

  @Transactional
  public void salvarCadastroPJPorArquivo(
      InputStream is, String nomeArquivo, Integer idProdutoInstituicao, SecurityUser user)
      throws FileNotFoundException {
    List<FuncionarioPJTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoCadastros);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    if (ImportadorArquivo.isXlsx(nomeArquivo) || ImportadorArquivo.isXls(nomeArquivo)) {
      to = importadorFuncionarioXls.getImportacaoCadastroPJ(new FileInputStream(copy));
    }

    try {
      salvarCadastroPJ(to, idProdutoInstituicao, user);
    } catch (GenericServiceException e) {
      throw e;
    }
  }

  public void salvarCadastroPJ(
      List<FuncionarioPJTO> tos, Integer idProdutoInstituicao, SecurityUser user) {
    FuncionarioPJTO to;
    CadastrarContaPagamentoPessoaRequest cadastrarPJRequest;

    ProdutoInstituicaoConfiguracao prodConfig =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
    if (prodConfig == null) {
      throw new GenericServiceException(
          "Não foi possível completar a operação. Produto Instituição Configuração não encontrado.");
    }

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      if (isCadastroPJValido(to, prodConfig, user, i)) {
        cadastrarPJRequest = new CadastrarContaPagamentoPessoaRequest();
        cadastrarPJRequest.setDocumento(to.getCpf());
        cadastrarPJRequest.setRazaoSocial(to.getNomeCompleto());
        cadastrarPJRequest.setDataFundacao(to.getDataFundacao());
        cadastrarPJRequest.setEmail(to.getEmail());
        cadastrarPJRequest.setTipoPessoa(2);
        cadastrarPJRequest.setDddTelefoneCelular(Integer.parseInt(to.getDDD()));
        cadastrarPJRequest.setTelefoneCelular(Integer.parseInt(to.getCelular()));
        cadastrarPJRequest.setIdProcessadora(user.getIdProcessadora());
        cadastrarPJRequest.setIdInstituicao(user.getIdInstituicao());
        cadastrarPJRequest.setIdRegional(user.getIdRegional());
        cadastrarPJRequest.setIdFilial(user.getIdFilial());
        cadastrarPJRequest.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
        cadastrarPJRequest.setIdUsuarioInclusao(user.getIdUsuario());
        cadastrarPJRequest.setNomeFantasia(to.getNomeFantasia());
        cadastrarPJRequest.setNomeEmbossado(Abreviador.abreviarNome(to.getNomeCompleto()));

        List<ValorCargaProdutoInstituicao> listaProdutos = new ArrayList<>();
        ValorCargaProdutoInstituicao produtoInstituicao = new ValorCargaProdutoInstituicao();
        produtoInstituicao.setIdProdutoInstituicao(idProdutoInstituicao);
        produtoInstituicao.setIdProdutoPlataforma(prodConfig.getIdProdutoPlataforma());
        produtoInstituicao.setValorCargaPadrao(0.0);
        listaProdutos.add(produtoInstituicao);

        cadastrarPJRequest.setValoresCargasProdutos(listaProdutos);

        ContaPagamento conta =
            contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(cadastrarPJRequest);

        CadastrarRepresentanteLegalRequest cadastrarRepresentanteRequest =
            new CadastrarRepresentanteLegalRequest();
        cadastrarRepresentanteRequest.setNome(to.getNomeRepresentanteLegal());
        cadastrarRepresentanteRequest.setCpf(to.getCpfRepresentanteLegal());
        cadastrarRepresentanteRequest.setDddCelular(
            Integer.parseInt(to.getDDDRepresentanteLegal()));
        cadastrarRepresentanteRequest.setTelefoneCelular(
            Integer.parseInt(to.getCelularRepresentanteLegal()));
        cadastrarRepresentanteRequest.setEmail(to.getEmailRepresentanteLegal());
        cadastrarRepresentanteRequest.setDataNascimento(to.getDataNascimentoRepresentanteLegal());
        cadastrarRepresentanteRequest.setIdTipoRepresentanteLegal(1L);
        cadastrarRepresentanteRequest.setIdConta(conta.getIdConta());
        cadastrarRepresentanteRequest.setIdUsuario(user.getIdUsuario());
        cadastrarRepresentanteRequest.setBlPoliticamenteExposto(to.getBlPoliticamenteExposto());

        createRepresentanteLegal(user, cadastrarRepresentanteRequest);
      } else {
        throw new GenericServiceException("Não foi possível salvar registro.");
      }
    }
  }

  private boolean isCadastroPJValido(
      FuncionarioPJTO registro,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      SecurityUser user,
      Integer linha) {
    if (Objects.nonNull(registro)) {
      return getVerificacaoPJ(registro, prodInstConfig, user, linha).isEmpty();
    }

    return FALSE;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoPJ(
      FuncionarioPJTO to,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      SecurityUser user,
      Integer linha) {
    Map<String, VerificacaoLinhaTO> verificacoes = new HashMap<>();

    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    String data = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    String validaData = data.substring(6, 10);
    erroRegistro = false;

    // verificando se o CNPJ foi informado
    if (to.getCpf() == null || to.getCpf().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (to.getCpf().length() > 12
        && !DocumentoUtil.isCNPJ(to.getCpf())
        && !DocumentoUtil.isValidCNPJ(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }

    ContaPagamento conta = null;
    if (to.getCpf().length() > 12) {
      conta =
          contaPagamentoService.findByTipoDocAndDocAndProduto(
              to.getCpf(),
              prodInstConfig.getIdProdInstituicao(),
              user,
              Integer.valueOf(COD_DOC_CNPJ_2));
    }

    if (conta != null && conta.getIdConta() != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ_PRODUTO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }

    // Verificando se o nome foi preenchido
    if (to.getNomeCompleto() == null || to.getNomeCompleto().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
      // Verificando a validade do nome completo
    } else if (!isNomeCompletoValido(to.getNomeCompleto())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<< " + to.getNomeCompleto() + " >>"));
      erroRegistro = true;
    }

    // Verificando se o nome fantasia foi preenchido
    if (to.getNomeFantasia() == null || to.getNomeFantasia().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_FANTASIA.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
      // Verificando a validade do nome fantasia
    } else if (!isNomeCompletoValido(to.getNomeFantasia())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_FANTASIA.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<< " + to.getNomeFantasia() + " >>"));
      erroRegistro = true;
    }

    // Verificando Data de Nascimento
    if (to.getDataFundacao() == null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (!isDataNascimentoValida(to.getDataFundacao())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getDataFundacao().toString()));
      erroRegistro = true;
    } else if (to.getDataFundacao() != null
        && to.getDataFundacao().toString().substring(24, 28).equals(validaData)) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
      erroRegistro = true;
    }

    if (to.getEmail() != null && !to.getEmail().isEmpty() && !validarEmail(to.getEmail())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<" + to.getEmail() + ">>"));
      erroRegistro = true;
    }

    if (StringUtils.isNotBlank(to.getDDD()) && !isDDDCelularValido(to.getDDD())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getDDD() + VALOR_INCORRETO));
      erroRegistro = true;
    }

    if (StringUtils.isNotBlank(to.getCelular()) && !isCelularValido(to.getCelular())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCelular() + VALOR_INCORRETO));
      erroRegistro = true;
    }

    // verificando CPF do Representante Legal
    if (to.getCpfRepresentanteLegal() == null || to.getCpfRepresentanteLegal().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (to.getCpfRepresentanteLegal().length() < 12
        && !DocumentoUtil.isCPF(to.getCpfRepresentanteLegal())
        && !DocumentoUtil.isValidCPF(to.getCpfRepresentanteLegal())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCpfRepresentanteLegal()));
      erroRegistro = true;
    }

    if (to.getNomeRepresentanteLegal() == null || to.getNomeRepresentanteLegal().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_COMPLETO_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
      // Verificando a validade do nome completo do representante legal
    } else if (!isNomeCompletoValido(to.getNomeRepresentanteLegal())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_COMPLETO_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<< " + to.getNomeRepresentanteLegal() + " >>"));
      erroRegistro = true;
    }

    if (to.getEmailRepresentanteLegal() != null
        && !to.getEmailRepresentanteLegal().isEmpty()
        && !validarEmail(to.getEmailRepresentanteLegal())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.E_MAIL_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<" + to.getEmailRepresentanteLegal() + ">>"));
      erroRegistro = true;
    }

    if (StringUtils.isNotBlank(to.getDDDRepresentanteLegal())
        && !isDDDCelularValido(to.getDDDRepresentanteLegal())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DDD_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getDDDRepresentanteLegal() + VALOR_INCORRETO));
      erroRegistro = true;
    }

    if (StringUtils.isNotBlank(to.getCelularRepresentanteLegal())
        && !isCelularValido(to.getCelularRepresentanteLegal())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CELULAR_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getCelular() + VALOR_INCORRETO));
      erroRegistro = true;
    }

    if (to.getDataNascimentoRepresentanteLegal() == null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (!isDataNascimentoValida(to.getDataNascimentoRepresentanteLegal())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, to.getDataNascimentoRepresentanteLegal().toString()));
      erroRegistro = true;
    } else if (to.getDataNascimentoRepresentanteLegal() != null
        && to.getDataNascimentoRepresentanteLegal()
            .toString()
            .substring(24, 28)
            .equals(validaData)) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO_REPRESENTANTE.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
      erroRegistro = true;
    }

    if (to.getIsCpfDuplicated()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM CPF DUPLICADO: " + to.getCpf() + ">>"));
    }

    if (to.getIsDuplicatedRegister()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DUPLICADO.toString() + ":" + linha,
          new VerificacaoLinhaTO(0, linha, "<<ARQUIVO COM REGISTRO DUPLICADO>>"));
    }

    if (erroRegistro) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.ERRO.toString() + ":" + linha,
          new VerificacaoLinhaTO(1, linha, to.getCpf()));
    }

    return verificacoes;
  }

  @Transactional
  public void salvarCadastroRespDepPorArquivo(
      InputStream is, String nomeArquivo, Integer idProdutoInstituicao, SecurityUser user)
      throws FileNotFoundException {
    List<FuncionarioResponsavelTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoCadastros);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    if (ImportadorArquivo.isXlsx(nomeArquivo) || ImportadorArquivo.isXls(nomeArquivo)) {
      to = importadorFuncionarioXls.getImportacaoCadastroRespDep(new FileInputStream(copy));
    }

    try {
      salvarCadastroRespDep(to, idProdutoInstituicao, user);
    } catch (GenericServiceException e) {
      throw e;
    }
  }

  @Transactional
  public void salvarVinculoRespDepPorArquivo(
      InputStream is, String nomeArquivo, Integer idProdutoInstituicao, SecurityUser user)
      throws FileNotFoundException {
    List<FuncionarioResponsavelTO> to = null;

    File copy = FileUtil.inputStreamToFile(is);

    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      Pattern regexPattern = Pattern.compile(patternArquivoVinculos);
      Matcher matcher = regexPattern.matcher(nomeArquivo);
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        idInstituicao = Integer.parseInt(matcher.group(1));
        idRegional = Integer.parseInt(matcher.group(2));
        idFilial = Integer.parseInt(matcher.group(3));
        idPonto = Integer.parseInt(matcher.group(4));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }

    if (ImportadorArquivo.isXlsx(nomeArquivo) || ImportadorArquivo.isXls(nomeArquivo)) {
      to = importadorFuncionarioXls.getImportacaoCadastroRespDep(new FileInputStream(copy));
    }

    try {
      salvarVinculoRespDep(to, idProdutoInstituicao, user);
    } catch (GenericServiceException e) {
      throw e;
    }
  }

  @Transactional
  public void salvarCadastroRespDep(
      List<FuncionarioResponsavelTO> tos, Integer idProdutoInstituicao, SecurityUser user) {
    FuncionarioResponsavelTO to;
    CadastrarContaPagamentoPessoaRequest cadastrarResponsavelRequest;

    ProdutoInstituicaoConfiguracao prodConfig =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
    if (prodConfig == null) {
      throw new GenericServiceException(
          "Não foi possível completar a operação. Produto Instituição Configuração não encontrado.");
    }

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      if (isCadastroRespDepValido(to, prodConfig, user, i)) {
        ProdutoInstituicaoDependentes produtoInstituicaoDependentes =
            produtoInstituicaoDependentesService.findByProdutoDependente(
                prodConfig.getIdProdInstituicao());

        cadastrarResponsavelRequest = new CadastrarContaPagamentoPessoaRequest();
        cadastrarResponsavelRequest.setDocumento(to.getCpf());
        cadastrarResponsavelRequest.setNomeCompleto(to.getNomeCompleto());
        cadastrarResponsavelRequest.setDataNascimento(to.getDataNascimento());
        cadastrarResponsavelRequest.setEmail(to.getEmail());
        cadastrarResponsavelRequest.setTipoPessoa(1);
        cadastrarResponsavelRequest.setDddTelefoneCelular(Integer.parseInt(to.getDDD()));
        cadastrarResponsavelRequest.setTelefoneCelular(Integer.parseInt(to.getCelular()));
        cadastrarResponsavelRequest.setIdProcessadora(user.getIdProcessadora());
        cadastrarResponsavelRequest.setIdInstituicao(user.getIdInstituicao());
        cadastrarResponsavelRequest.setIdRegional(user.getIdRegional());
        cadastrarResponsavelRequest.setIdFilial(user.getIdFilial());
        cadastrarResponsavelRequest.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
        cadastrarResponsavelRequest.setIdUsuarioInclusao(user.getIdUsuario());
        cadastrarResponsavelRequest.setNomeEmbossado(Abreviador.abreviarNome(to.getNomeCompleto()));

        List<ValorCargaProdutoInstituicao> listaProdutos = new ArrayList<>();
        ValorCargaProdutoInstituicao produtoInstituicao = new ValorCargaProdutoInstituicao();
        produtoInstituicao.setIdProdutoInstituicao(
            produtoInstituicaoDependentes
                .getProdutoInstituicaoDependentesId()
                .getIdProdutoResponsavel());
        produtoInstituicao.setIdProdutoPlataforma(prodConfig.getIdProdutoPlataforma());
        produtoInstituicao.setValorCargaPadrao(0.0);
        listaProdutos.add(produtoInstituicao);

        cadastrarResponsavelRequest.setValoresCargasProdutos(listaProdutos);

        ContaPagamento conta =
            contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(cadastrarResponsavelRequest);

        try {
          cartaoQrCodeService.vincularCartaoQrCode(
              conta.getIdConta(),
              cadastrarResponsavelRequest.getDocumento(),
              (long) user.getIdUsuario(),
              null);
        } catch (GenericServiceException e) {
          log.info(
              ConstantesErro.QR_CODE_NAO_ENCONTRADO_PARA_CHAVE.format(
                  cadastrarResponsavelRequest.getDocumento()));
        }

        for (FuncionarioDependenteTO toDep : to.getDependentes()) {

          CadastrarContaPagamentoPessoaRequest cadastrarDependenteRequest =
              new CadastrarContaPagamentoPessoaRequest();
          cadastrarDependenteRequest.setDocumento(toDep.getCpf());
          cadastrarDependenteRequest.setNomeCompleto(toDep.getNomeCompleto());
          cadastrarDependenteRequest.setDataNascimento(toDep.getDataNascimento());
          cadastrarDependenteRequest.setEmail(toDep.getEmail());
          cadastrarDependenteRequest.setTipoPessoa(1);
          cadastrarDependenteRequest.setDddTelefoneCelular(Integer.parseInt(toDep.getDDD()));
          cadastrarDependenteRequest.setTelefoneCelular(Integer.parseInt(toDep.getCelular()));
          cadastrarDependenteRequest.setIdProcessadora(user.getIdProcessadora());
          cadastrarDependenteRequest.setIdInstituicao(user.getIdInstituicao());
          cadastrarDependenteRequest.setIdRegional(user.getIdRegional());
          cadastrarDependenteRequest.setIdFilial(user.getIdFilial());
          cadastrarDependenteRequest.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
          cadastrarDependenteRequest.setIdUsuarioInclusao(user.getIdUsuario());
          cadastrarDependenteRequest.setNomeEmbossado(
              Abreviador.abreviarNome(toDep.getNomeCompleto()));

          List<ValorCargaProdutoInstituicao> listaProdutosDependente = new ArrayList<>();
          ValorCargaProdutoInstituicao produtoDependente = new ValorCargaProdutoInstituicao();
          produtoDependente.setIdProdutoInstituicao(
              produtoInstituicaoDependentes
                  .getProdutoInstituicaoDependentesId()
                  .getIdProdutoDependente());
          produtoDependente.setIdProdutoPlataforma(prodConfig.getIdProdutoPlataforma());
          produtoDependente.setValorCargaPadrao(0.0);
          listaProdutosDependente.add(produtoDependente);
          cadastrarDependenteRequest.setValoresCargasProdutos(listaProdutosDependente);

          ContaPagamento contaDependente =
              contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(
                  cadastrarDependenteRequest);

          PessoaResponsavelDependente pessoaRespDep = new PessoaResponsavelDependente();

          Pessoa pessoaResp = findPessoaByIdConta(conta.getIdConta());
          Pessoa pessoaDep = findPessoaByIdConta(contaDependente.getIdConta());
          pessoaRespDep.setPessoaResponsavel(pessoaResp);
          pessoaRespDep.setPessoaDependente(pessoaDep);
          PessoaResponsavelDependenteId id = new PessoaResponsavelDependenteId();
          id.setIdPessoaResponsavel(pessoaResp.getIdPessoa());
          id.setIdPessoaDependente(pessoaDep.getIdPessoa());
          pessoaRespDep.setPessoaResponsavelDependenteId(id);
          pessoaRespDep.setIdStatus(1);
          pessoaRespDep.setIdRelacaoDependencia(to.getVinculoDependente());
          pessoaResponsavelDependenteService.save(pessoaRespDep);
          try {
            cartaoQrCodeService.vincularCartaoQrCode(
                contaDependente.getIdConta(),
                pessoaDep.getDocumento(),
                (long) user.getIdUsuario(),
                null);
          } catch (GenericServiceException e) {
            log.info(
                ConstantesErro.QR_CODE_NAO_ENCONTRADO_PARA_CHAVE.format(pessoaDep.getDocumento()));
          }
        }

      } else {
        throw new GenericServiceException("Não foi possível salvar registro.");
      }
    }
  }

  @Transactional
  public void salvarVinculoRespDep(
      List<FuncionarioResponsavelTO> tos, Integer idProdutoInstituicao, SecurityUser user) {
    FuncionarioResponsavelTO to;

    ProdutoInstituicaoConfiguracao prodConfig =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                user.getIdProcessadora(), idProdutoInstituicao, user.getIdInstituicao());
    if (prodConfig == null) {
      throw new GenericServiceException(
          "Não foi possível completar a operação. Produto Instituição Configuração não encontrado.");
    }

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);
      if (isVinculoRespDepValido(to, prodConfig, user, i)) {
        ProdutoInstituicaoDependentes produtoInstituicaoDependentes =
            produtoInstituicaoDependentesService.findByProdutoDependente(
                prodConfig.getIdProdInstituicao());

        Pessoa pessoaResponsavel =
            findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
                user.getIdProcessadora(), user.getIdInstituicao(), to.getCpf(), 1);

        for (FuncionarioDependenteTO toDep : to.getDependentes()) {

          CadastrarContaPagamentoPessoaRequest cadastrarDependenteRequest =
              new CadastrarContaPagamentoPessoaRequest();
          cadastrarDependenteRequest.setDocumento(toDep.getCpf());
          cadastrarDependenteRequest.setNomeCompleto(toDep.getNomeCompleto());
          cadastrarDependenteRequest.setDataNascimento(toDep.getDataNascimento());
          cadastrarDependenteRequest.setEmail(toDep.getEmail());
          cadastrarDependenteRequest.setTipoPessoa(1);
          cadastrarDependenteRequest.setDddTelefoneCelular(Integer.parseInt(toDep.getDDD()));
          cadastrarDependenteRequest.setTelefoneCelular(Integer.parseInt(toDep.getCelular()));
          cadastrarDependenteRequest.setIdProcessadora(user.getIdProcessadora());
          cadastrarDependenteRequest.setIdInstituicao(user.getIdInstituicao());
          cadastrarDependenteRequest.setIdRegional(user.getIdRegional());
          cadastrarDependenteRequest.setIdFilial(user.getIdFilial());
          cadastrarDependenteRequest.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
          cadastrarDependenteRequest.setIdUsuarioInclusao(user.getIdUsuario());
          cadastrarDependenteRequest.setNomeEmbossado(
              Abreviador.abreviarNome(toDep.getNomeCompleto()));

          List<ValorCargaProdutoInstituicao> listaProdutosDependente = new ArrayList<>();
          ValorCargaProdutoInstituicao produtoDependente = new ValorCargaProdutoInstituicao();
          produtoDependente.setIdProdutoInstituicao(
              produtoInstituicaoDependentes
                  .getProdutoInstituicaoDependentesId()
                  .getIdProdutoDependente());
          produtoDependente.setIdProdutoPlataforma(prodConfig.getIdProdutoPlataforma());
          produtoDependente.setValorCargaPadrao(0.0);
          listaProdutosDependente.add(produtoDependente);
          cadastrarDependenteRequest.setValoresCargasProdutos(listaProdutosDependente);

          ContaPagamento contaDependente =
              contaPagamentoService.cadastrarContaPagamentoPessoaArquivo(
                  cadastrarDependenteRequest);

          PessoaResponsavelDependente pessoaRespDep = new PessoaResponsavelDependente();

          Pessoa pessoaDep = findPessoaByIdConta(contaDependente.getIdConta());
          pessoaRespDep.setPessoaResponsavel(pessoaResponsavel);
          pessoaRespDep.setPessoaDependente(pessoaDep);
          PessoaResponsavelDependenteId id = new PessoaResponsavelDependenteId();
          id.setIdPessoaResponsavel(pessoaResponsavel.getIdPessoa());
          id.setIdPessoaDependente(pessoaDep.getIdPessoa());
          pessoaRespDep.setPessoaResponsavelDependenteId(id);
          pessoaRespDep.setIdStatus(1);
          pessoaRespDep.setIdRelacaoDependencia(to.getVinculoDependente());
          pessoaResponsavelDependenteService.save(pessoaRespDep);
          try {
            cartaoQrCodeService.vincularCartaoQrCode(
                contaDependente.getIdConta(),
                pessoaDep.getDocumento(),
                (long) user.getIdUsuario(),
                null);
          } catch (GenericServiceException e) {
            log.info(
                ConstantesErro.QR_CODE_NAO_ENCONTRADO_PARA_CHAVE.format(pessoaDep.getDocumento()));
          }
        }

      } else {
        throw new GenericServiceException("Não foi possível salvar registro.");
      }
    }
  }

  private boolean isCadastroRespDepValido(
      FuncionarioResponsavelTO registro,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      SecurityUser user,
      Integer linha) {
    if (Objects.nonNull(registro)) {
      return getVerificacaoRespDep(registro, prodInstConfig, user, linha).isEmpty();
    }

    return FALSE;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoRespDep(
      FuncionarioResponsavelTO to,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      SecurityUser user,
      Integer linha) {
    Map<String, VerificacaoCargaArquivoTO.VerificacaoLinhaTO> verificacoes = new HashMap<>();

    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    String data = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    String validaData = data.substring(6, 10);
    erroRegistro = false;

    if (to.getCpf() == null || to.getCpf().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (to.getCpf().length() > 12
        && !DocumentoUtil.isCNPJ(to.getCpf())
        && !DocumentoUtil.isValidCNPJ(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }

    ContaPagamento conta = new ContaPagamento();
    if (to.getCpf().length() > 12) {
      conta =
          contaPagamentoService.findByTipoDocAndDocAndProduto(
              to.getCpf(),
              prodInstConfig.getIdProdInstituicao(),
              user,
              Integer.valueOf(COD_DOC_CPF_1));
      ;
    }

    if (conta != null && conta.getIdConta() != null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_PRODUTO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }

    // Verificando se o nome foi preenchido
    if (to.getNomeCompleto() == null || to.getNomeCompleto().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
      // Verificando a validade do nome completo
    } else if (!isNomeCompletoValido(to.getNomeCompleto())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
              0, linha, "<< " + to.getNomeCompleto() + " >>"));
      erroRegistro = true;
    }

    // Verificando Data de Nascimento
    if (to.getDataNascimento() == null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (!isDataNascimentoValida(to.getDataNascimento())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
              0, linha, to.getDataNascimento().toString()));
      erroRegistro = true;
    } else if (to.getDataNascimento() != null
        && to.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
      erroRegistro = true;
    }

    if (to.getEmail() != null && !to.getEmail().isEmpty() && !validarEmail(to.getEmail())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, "<<" + to.getEmail() + ">>"));
      erroRegistro = true;
    }

    if (StringUtils.isNotBlank(to.getDDD()) && !isDDDCelularValido(to.getDDD())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
              0, linha, to.getDDD() + VALOR_INCORRETO));
      erroRegistro = true;
    }

    if (StringUtils.isNotBlank(to.getCelular()) && !isCelularValido(to.getCelular())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
              0, linha, to.getCelular() + VALOR_INCORRETO));
      erroRegistro = true;
    }

    for (FuncionarioDependenteTO dependente : to.getDependentes()) {

      if (dependente.getCpf() == null || dependente.getCpf().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } else if (dependente.getCpf().length() < 12
          && !DocumentoUtil.isCPF(dependente.getCpf())
          && !DocumentoUtil.isValidCPF(dependente.getCpf())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, dependente.getCpf()));
        erroRegistro = true;
      }

      // Verificando se o nome foi preenchido
      if (dependente.getNomeCompleto() == null || dependente.getNomeCompleto().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
        // Verificando a validade do nome completo
      } else if (!isNomeCompletoValido(dependente.getNomeCompleto())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, "<< " + dependente.getNomeCompleto() + " >>"));
        erroRegistro = true;
      }

      // Verificando Data de Nascimento
      if (dependente.getDataNascimento() == null) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } else if (!isDataNascimentoValida(dependente.getDataNascimento())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, dependente.getDataNascimento().toString()));
        erroRegistro = true;
      } else if (dependente.getDataNascimento() != null
          && dependente.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
        erroRegistro = true;
      }

      if (dependente.getEmail() != null
          && !dependente.getEmail().isEmpty()
          && !validarEmail(dependente.getEmail())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, "<<" + dependente.getEmail() + ">>"));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(dependente.getDDD()) && !isDDDCelularValido(dependente.getDDD())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, dependente.getDDD() + VALOR_INCORRETO));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(dependente.getCelular())
          && !isCelularValido(dependente.getCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, dependente.getCelular() + VALOR_INCORRETO));
        erroRegistro = true;
      }
    }

    return verificacoes;
  }

  private boolean isVinculoRespDepValido(
      FuncionarioResponsavelTO registro,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      SecurityUser user,
      Integer linha) {
    if (Objects.nonNull(registro)) {
      return getVerificacaoRespDepVinculo(registro, prodInstConfig, user, linha).isEmpty();
    }

    return FALSE;
  }

  public Map<String, VerificacaoLinhaTO> getVerificacaoRespDepVinculo(
      FuncionarioResponsavelTO to,
      ProdutoInstituicaoConfiguracao prodInstConfig,
      SecurityUser user,
      Integer linha) {
    Map<String, VerificacaoCargaArquivoTO.VerificacaoLinhaTO> verificacoes = new HashMap<>();

    Calendar cal = Calendar.getInstance();
    cal.set(Calendar.MONTH, 1);
    cal.set(Calendar.DATE, 1);
    cal.set(Calendar.YEAR, 1900);
    cal.set(Calendar.HOUR, 00);
    cal.set(Calendar.MINUTE, 00);
    cal.set(Calendar.SECOND, 00);
    String data = DateUtil.dateFormat("dd-MM-yyyy", cal.getTime());
    String validaData = data.substring(6, 10);
    erroRegistro = false;

    if (to.getCpf() == null || to.getCpf().isEmpty()) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
      erroRegistro = true;
    } else if (to.getCpf().length() > 12
        && !DocumentoUtil.isCNPJ(to.getCpf())
        && !DocumentoUtil.isValidCNPJ(to.getCpf())) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CNPJ.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }

    ContaPagamento conta = new ContaPagamento();

    if (conta == null) {
      verificacoes.put(
          VerificacaoImportacaoArquivoEnum.CPF_NAO_CADASTRADO.toString() + ":" + linha,
          new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, to.getCpf()));
      erroRegistro = true;
    }

    for (FuncionarioDependenteTO dependente : to.getDependentes()) {

      if (dependente.getCpf() == null || dependente.getCpf().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } else if (dependente.getCpf().length() < 12
          && !DocumentoUtil.isCPF(dependente.getCpf())
          && !DocumentoUtil.isValidCPF(dependente.getCpf())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CPF.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, dependente.getCpf()));
        erroRegistro = true;
      }

      // Verificando se o nome foi preenchido
      if (dependente.getNomeCompleto() == null || dependente.getNomeCompleto().isEmpty()) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
        // Verificando a validade do nome completo
      } else if (!isNomeCompletoValido(dependente.getNomeCompleto())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.NOME_COMPLETO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, "<< " + dependente.getNomeCompleto() + " >>"));
        erroRegistro = true;
      }

      // Verificando Data de Nascimento
      if (dependente.getDataNascimento() == null) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, NAO_INFORMADO));
        erroRegistro = true;
      } else if (!isDataNascimentoValida(dependente.getDataNascimento())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, dependente.getDataNascimento().toString()));
        erroRegistro = true;
      } else if (dependente.getDataNascimento() != null
          && dependente.getDataNascimento().toString().substring(24, 28).equals(validaData)) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DATA_NASCIMENTO.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(0, linha, "<<DATA INCORRETA>>"));
        erroRegistro = true;
      }

      if (dependente.getEmail() != null
          && !dependente.getEmail().isEmpty()
          && !validarEmail(dependente.getEmail())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.E_MAIL.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, "<<" + dependente.getEmail() + ">>"));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(dependente.getDDD()) && !isDDDCelularValido(dependente.getDDD())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.DDD.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, dependente.getDDD() + VALOR_INCORRETO));
        erroRegistro = true;
      }

      if (StringUtils.isNotBlank(dependente.getCelular())
          && !isCelularValido(dependente.getCelular())) {
        verificacoes.put(
            VerificacaoImportacaoArquivoEnum.CELULAR.toString() + ":" + linha,
            new VerificacaoCargaArquivoTO.VerificacaoLinhaTO(
                0, linha, dependente.getCelular() + VALOR_INCORRETO));
        erroRegistro = true;
      }
    }

    return verificacoes;
  }

  public static boolean validarEmail(String email) {
    return patternEmail.matcher(email).matches() && !EmailBlacklist.isBlacklistMail(email);
  }

  public boolean isDDDCelularValido(String ddd) {
    if (StringUtils.isNotBlank(ddd)) {
      Matcher matcher = paternDdd.matcher(ddd);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  public boolean isCelularValido(String celular) {
    if (StringUtils.isNotBlank(celular)) {
      Matcher matcher = paternTelefone.matcher(celular);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  public boolean isTelFixoValido(String celular) {
    if (StringUtils.isNotBlank(celular)) {
      Matcher matcher = paternTelefoneFixo.matcher(celular);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  public boolean isEstadoCivilValido(String estadoCivil) {
    if (StringUtils.isNotBlank(estadoCivil)) {
      Matcher matcher = paternEstadoCivil.matcher(estadoCivil);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  public boolean isCepValidoSemException(String cep) {
    if (StringUtils.isNotBlank(cep)) {
      Matcher matcher = paternCep.matcher(cep);
      if (matcher.matches()) {
        List<LogradouroVO> logradouro = logradouroService.findByCepSemException(cep);
        if (logradouro != null && logradouro.size() > 0 && !logradouro.isEmpty()) {
          return Boolean.TRUE;
        }
        return Boolean.FALSE;
      } else {
        return Boolean.FALSE;
      }
    }
    return Boolean.FALSE;
  }

  public boolean isCepValidoSemExceptionSemValidarNoBanco(String cep) {
    if (StringUtils.isNotBlank(cep)) {
      Matcher matcher = paternCep.matcher(cep);
      if (matcher.matches()) {
        return Boolean.TRUE;
      } else {
        return Boolean.FALSE;
      }
    }
    return Boolean.FALSE;
  }

  public boolean isBancoValido(String banco) {
    if (StringUtils.isNotBlank(banco)) {
      Banco b = bancoService.findByIdBanco(Integer.parseInt(banco));
      if (b != null) {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

  public boolean isAgenciaValido(String agencia, String banco) {
    if (StringUtils.isNotBlank(agencia)) {
      Agencia a =
          agenciaService.findByCodAgenciaAndCodBanco(
              Integer.parseInt(agencia), Integer.parseInt(banco));
      if (a != null) {
        return Boolean.TRUE;
      }
    }

    return Boolean.FALSE;
  }

  public boolean isContaValido(String conta) {
    if (StringUtils.isNotBlank(conta)) {
      try {
        Integer.parseInt(conta);
      } catch (NumberFormatException e) {
        return Boolean.FALSE;
      }
      return Boolean.TRUE;
    }

    return Boolean.FALSE;
  }

  public boolean isTipoContaValido(String tipoConta) {
    if (StringUtils.isNotBlank(tipoConta)) {
      Matcher matcher = paternTipoContaBancaria.matcher(tipoConta);
      return matcher.matches();
    }

    return Boolean.FALSE;
  }

  public boolean isDataNascimentoValida(Date dataNascimento) {
    if (dataNascimento != null) {
      return UtilValidator.isDataValida("EEE MMM dd kk:mm:ss z yyyy", dataNascimento.toString());
    }

    return Boolean.TRUE;
  }

  public boolean isNomeCompletoValido(String nomeCompleto) {

    return nomeCompleto != null && nomeCompleto.length() > 0 && !nomeCompleto.isEmpty();
  }

  public List<Pessoa> findByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
      Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa) {
    return pessoaRepository.findByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
        idProcessadora, idInstituicao, documento, idTipoPessoa);
  }

  public Pessoa findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
      Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa) {
    return pessoaRepository.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoa(
        idProcessadora, idInstituicao, documento, idTipoPessoa);
  }

  public Pessoa
      findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataNascimentoNotNullOrderByIdPessoaDesc(
          Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa) {
    return pessoaRepository
        .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataNascimentoNotNullOrderByIdPessoaDesc(
            idProcessadora, idInstituicao, documento, idTipoPessoa);
  }

  public Pessoa
      findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndGrupoAcessoAndDataNascimentoNotNull(
          Integer idProcessadora, Integer idInstituicao, String documento, Long grupoAcesso) {
    return pessoaRepository
        .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndGrupoAcessoAndDataNascimentoNotNull(
            idProcessadora, idInstituicao, documento, grupoAcesso);
  }

  public Pessoa encontraPessoaContaAtivaPorDocumentoProcessadoraEInstituicaoDataDeNascimentoNotNull(
      Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa) {
    return pessoaRepository
        .encontraPessoaContaAtivaPorDocumentoProcessadoraEInstituicaoDataDeNascimentoNotNull(
            idProcessadora, idInstituicao, documento, idTipoPessoa);
  }

  public Pessoa
      findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataFundacaoNotNullOrderByIdPessoaDesc(
          Integer idProcessadora, Integer idInstituicao, String documento, Integer idTipoPessoa) {
    return pessoaRepository
        .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoAndIdTipoPessoaAndDataFundacaoNotNullOrderByIdPessoaDesc(
            idProcessadora, idInstituicao, documento, idTipoPessoa);
  }

  public Pessoa findOneByIdProcessadoraAndIdInstituicaoAndDocumento(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return pessoaRepository.findOneByIdProcessadoraAndIdInstituicaoAndDocumento(
        idProcessadora, idInstituicao, documento);
  }

  public Pessoa findFirstByIdProcessadoraAndIdInstituicaoAndDocumento(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return pessoaRepository
        .findFirstByIdProcessadoraAndIdInstituicaoAndDocumentoOrderByIdPessoaDesc(
            idProcessadora, idInstituicao, documento);
  }

  public Pessoa encontraPessoaConsultaRestritaPorDocumento(String documento) {
    return pessoaRepository.encontraPessoaConsultaRestritaPorDocumento(documento);
  }

  public List<Pessoa> findByIdProcessadoraAndIdInstituicaoAndDocumento(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return pessoaRepository.findByIdProcessadoraAndIdInstituicaoAndDocumento(
        idProcessadora, idInstituicao, documento);
  }

  public List<Pessoa> findByIdProcessadoraAndDocumentoAndIdInstituicaoIn(
      Integer idProcessadora, String documento, Collection<Integer> idInstituicoes) {
    return pessoaRepository.findByIdProcessadoraAndDocumentoAndIdInstituicaoIn(
        idProcessadora, documento, idInstituicoes);
  }

  public Pessoa findOneByHierarquia(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      Integer idTipoPessoa,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    boolean idProcessadoraCheck = idProcessadora == null;
    boolean idInstituicaoCheck = idInstituicao == null;
    boolean documentoCheck = documento == null;
    boolean idTipoPessoaCheck = idTipoPessoa == null;
    boolean idRegionalCheck = idRegional == null;
    boolean idFilialCheck = idFilial == null;
    boolean idPontoRelacionamentoCheck = idPontoRelacionamento == null;

    if (idProcessadoraCheck
        || idInstituicaoCheck
        || documentoCheck
        || idTipoPessoaCheck
        || idRegionalCheck
        || idFilialCheck
        || idPontoRelacionamentoCheck) {
      ArrayList<String> campoNullo = new ArrayList<>();
      if (idProcessadoraCheck) {
        campoNullo.add("idProcessadora");
      }
      if (idInstituicaoCheck) {
        campoNullo.add("idInstituicao");
      }
      if (documentoCheck) {
        campoNullo.add("documento");
      }
      if (idTipoPessoaCheck) {
        campoNullo.add("idTipoPessoa");
      }
      if (idRegionalCheck) {
        campoNullo.add("idRegional");
      }
      if (idPontoRelacionamentoCheck) {
        if (idFilialCheck) {
          campoNullo.add("idFilial");
        }
        campoNullo.add("idPontoRelacionamento");
      }
      String mensagem = "Nao é possivel Consultar pessoa valor(es) vazio=";
      for (String campo : campoNullo) {
        mensagem = mensagem + campoNullo + ",";
      }
      throw new GenericServiceException(mensagem);
    }
    return pessoaRepository.findOneByHierarquia(
        idProcessadora,
        idInstituicao,
        documento,
        idTipoPessoa,
        idRegional,
        idFilial,
        idPontoRelacionamento);
  }

  public Pessoa findOneByHierarquiaAndMatricula(
      Integer idProcessadora,
      Integer idInstituicao,
      String matricula,
      Integer idTipoPessoa,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    return pessoaRepository.findOneByHierarquiaAndMatricula(
        idProcessadora,
        idInstituicao,
        matricula,
        idTipoPessoa,
        idRegional,
        idFilial,
        idPontoRelacionamento);
  }

  public Pessoa findOneByIdPessoa(Long idPessoa) {
    return pessoaRepository.findOneByIdPessoa(idPessoa);
  }

  public List<ContaPessoaFisica> findContasPessoaFisica(Long idPessoa) {

    Pessoa p = findOneByIdPessoa(idPessoa);

    if (p == null) {
      throw new GenericServiceException("Pessoa não encontrada. IdPessoa: " + idPessoa);
    }

    List<ContaPessoaFisica> contasPF = new ArrayList<>();

    List<ContaPagamento> contas = contaService.findByContasPessoaIdPessoa(idPessoa);

    if (contas != null) {

      for (ContaPagamento contaPagamento : contas) {
        ContaPessoaFisica contaPF = new ContaPessoaFisica();

        BeanUtils.copyProperties(contaPagamento, contaPF);

        ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
            produtoInstituicaoConfiguracaoService
                .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                    contaPagamento.getIdProcessadora(),
                    contaPagamento.getIdProdutoInstituicao(),
                    contaPagamento.getIdInstituicao());

        if (produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() != null
            && produtoInstituicaoConfiguracao.getProdutoInstituidor().getFuncao() == CREDITO) {
          contaPF.setSaldoDisponivel(
              contaPagamentoService
                  .getSaldoDisponivelPosPago(contaPagamento, produtoInstituicaoConfiguracao)
                  .doubleValue());
          contaPF.setLimiteUnico(
              contaPagamentoService
                  .getLimiteCredito(contaPagamento, produtoInstituicaoConfiguracao)
                  .doubleValue());
        } else {
          contaPF.setSaldoDisponivel(
              contaPagamentoService
                  .getSaldoDisponivelPrePago(contaPagamento, produtoInstituicaoConfiguracao)
                  .doubleValue());
          contaPF.setLimiteUnico(
              contaPagamentoService
                  .getLimiteCredito(contaPagamento, produtoInstituicaoConfiguracao)
                  .doubleValue());
        }

        contaPF.setIdProdutoPlataforma(contaPagamento.getIdProdutoPlataforma());

        contaPF.setSuportaContaBase(produtoInstituicaoConfiguracao.getSuportaContaBase());
        contaPF.setCorporativo(produtoInstituicaoConfiguracao.getBlCorporativo());

        if (produtoInstituicaoConfiguracao.getBandeiraProduto() != null) {
          contaPF.setBandeiraProduto(produtoInstituicaoConfiguracao.getBandeiraProduto());
        }

        ContaPessoa contaPessoa =
            contaPessoaService.findOneByIdPessoaAndIdConta(idPessoa, contaPagamento.getIdConta());

        ProdutoContratado prodContrato =
            produtoContratadoService.findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
                contaPagamento.getIdProcessadora(),
                contaPagamento.getIdInstituicao(),
                contaPagamento.getIdRegional(),
                contaPagamento.getIdFilial(),
                contaPagamento.getIdPontoDeRelacionamento(),
                contaPagamento.getIdProdutoInstituicao());

        if (StringUtils.isNotBlank(contaPessoa.getNomeCartaoImpresso())) {
          contaPF.setNomeCartaoImpresso(contaPessoa.getNomeCartaoImpresso());
        } else if (prodContrato != null
            && StringUtils.isNotBlank(prodContrato.getNomeCartaoImpresso())) {
          contaPF.setNomeCartaoImpresso(prodContrato.getNomeCartaoImpresso());
        }

        ProdutoInstituicao produtoInstituicao = contaPagamento.getProdutoInstituicao();

        contaPF.setDescProdutoInstituicao(
            produtoInstituicao != null ? produtoInstituicao.getDescProdInstituicao() : "");

        HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento =
            contaPagamento.getHierarquiaPontoDeRelacionamento();

        if (hierarquiaPontoDeRelacionamento != null) {

          HierarquiaInstituicao hierarquiaInstituicao =
              hierarquiaPontoDeRelacionamento.getHierarquiaInstituicao();
          contaPF.setDescInstituicao(
              hierarquiaInstituicao != null ? hierarquiaInstituicao.getDescInstituicao() : "");

          HierarquiaRegional hierarquiaRegional =
              hierarquiaPontoDeRelacionamento.getHierarquiaRegional();
          contaPF.setDescRegional(
              hierarquiaRegional != null ? hierarquiaRegional.getDescRegional() : "");

          HierarquiaFilial hierarquiaFilial = hierarquiaPontoDeRelacionamento.getHierarquiaFilial();
          contaPF.setDescFilial(hierarquiaFilial != null ? hierarquiaFilial.getDescFilial() : "");

          contaPF.setDescPontoDeRelacionamento(hierarquiaPontoDeRelacionamento.getDescricao());
        }

        TipoStatus tipoStatus = contaPagamento.getTipoStatus();

        if (tipoStatus == null) {
          tipoStatus = tipoStatusService.findById(contaPagamento.getIdStatusConta());
        }

        String status =
            tipoStatus.getIdGrupoStatus().equals(GRUPO_STATUS_ATIVO) ? "ATIVO" : "INATIVO";
        contaPF.setDescStatusConta(status);
        Pessoa pessoa = findById(contaPessoa.getIdPessoa());

        if (pessoa != null && pessoa.getIdTipoPessoa().equals(PJ)) {
          List<RepresentanteLegal> representantes =
              representanteLegalService.findByIdConta(contaPagamento.getIdConta());
          contaPF.setRepresentantes(representantes);
        }
        contasPF.add(contaPF);
      }
    }

    return contasPF;
  }

  public PessoaFisica findPessoaFisicaById(Long id) {
    Pessoa p = findOneByIdPessoa(id);

    if (p == null) {
      throw new GenericServiceException("Pessoa não encontrada. idPessoa " + id);
    }

    PessoaFisica pf = new PessoaFisica();
    pf.setPessoa(p);
    // BeanUtils.copyProperties(p, pf);
    return pf;
  }

  // contexto para garantir integridade entre os sistemas
  @Transactional
  public Pessoa createPessoaFisica(CadastrarPessoaFisica model, Pessoa pessoa) {
    HashMap<String, Object> map = new HashMap<>();

    Pessoa pessoaExistente = null;
    if (!hierarquiaInstituicaoExists(model.getIdInstituicao(), model.getIdProcessadora())) {
      map.put(
          "msg",
          "Não foi possivel encontrar Instituicao informada. idInstituicao = "
              + model.getIdInstituicao()
              + " idProcessadora = "
              + model.getIdProcessadora());
      map.put("created", false);
      throw new GenericServiceException(map);
    }

    if (EmailBlacklist.isBlacklistMail(model.getEmail())) {
      throw new GenericServiceException(
          "O domínio utilizado no email está na blacklist. Email: " + model.getEmail());
    }

    Boolean permiteMultiplasContas =
        produtoInstituicaoConfiguracaoService.obterPermiteMultiplasContas(
            model.getIdProdInstituicao());
    // trava para garantir que não haverá conflito durante as verificações e inserções em tabela
    lock.lock();
    try {
      // Se for B2B, este serviço só permite o cadastro de funcionários que ainda não foram
      // cadastrados.
      if (model.getIdSetorFilial() != null
          && !ConstantesB2B.PARAMETRO_ZERO.equals(model.getIdSetorFilial())) {
        SetorFilial setorFilial = setorFilialService.findById(model.getIdSetorFilial());

        if (setorFilial == null) {

          map.put(
              "msg",
              "Não foi possivel encontrar Setor Filial informado. idSetorFilial = "
                  + model.getIdSetorFilial());
          map.put("created", false);
          throw new GenericServiceException(map);
        }
        // se a conta nao suporta conta base, só pode existir uma pessoa com conta B2B por
        // documento, setor filial e tipo pessoa.
        if (!permiteMultiplasContas) {
          Pessoa pessoaBuscada =
              findOneByHierarquia(
                  setorFilial.getIdProcessadora(),
                  setorFilial.getIdInstituicao(),
                  model.getDocumento(),
                  model.getTipoPessoa(),
                  setorFilial.getIdRegional(),
                  setorFilial.getIdFilial(),
                  setorFilial.getIdPontoRelacionamento());

          if (pessoaBuscada != null) {
            //					map.put("msg", "Pessoa já cadastrada na Empresa B2B:" +
            // setorFilial.getIdPontoRelacionamento()
            //							+ " Documento:" + model.getDocumento());
            //					map.put("created", false);
            //					throw new GenericServiceException(map);

            /*Alterando para poder criar mais de uma pessoa B2B para empresa*/
            return pessoaBuscada;
          }

          // Só pode existir uma pessoa com conta B2B por matricula, tipo pessoa e setor filial.
          if (model.getMatricula() != null) {
            Pessoa pessoaBuscadaMatricula =
                findOneByHierarquiaAndMatricula(
                    setorFilial.getIdProcessadora(),
                    setorFilial.getIdInstituicao(),
                    model.getMatricula().toUpperCase(),
                    model.getTipoPessoa(),
                    setorFilial.getIdRegional(),
                    setorFilial.getIdFilial(),
                    setorFilial.getIdPontoRelacionamento());

            if (pessoaBuscadaMatricula != null) {

              //					map.put("msg", "Número de matrícula " + model.getMatricula() + ", já está
              // cadastrado na Empresa.");
              //					map.put("created", false);
              //					throw new GenericServiceException(map);

              /*Alterando para poder criar mais de uma pessoa B2B para empresa*/
              return pessoaBuscada;
            }
          }
        }
        // Se não for B2B = só pode existir uma de crédito e outra que não seja de crédito.
      } else {

        /*Se conta a ser criada for de crédito busca se já existe pessoa em conta NÃO B2B por hierarquia instituição, documento e tipo pessoa e de crédito.
         * Se existir, associar pessoa existente. Se não, criar nova pessoa.
         *
         * Se conta a ser criada NÃO for de crédito, busca se pessoa já existe em conta NÃO B2B por hierarquia instituição, documento e tipo pessoa e que NÃO seja de crédito.
         * Se existir, associar pessoa existente. Se não, criar nova pessoa.
         *
         * No caso da conta suportar conta base, deve permitir a criacao de pessoa nova repetida
         */
        if (isProdutoCredito(
            model.getIdProcessadora(), model.getIdInstituicao(), model.getIdProdInstituicao())) {
          pessoaExistente =
              findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsCredito(
                  model.getIdProcessadora(),
                  model.getIdInstituicao(),
                  model.getDocumento(),
                  model.getTipoPessoa());
        } else {
          pessoaExistente =
              findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsNotCredito(
                  model.getIdProcessadora(),
                  model.getIdInstituicao(),
                  model.getDocumento(),
                  model.getTipoPessoa());
        }

        if (pessoaExistente != null && (!permiteMultiplasContas)) {
          // VERIFICA SE JÁ EXISTE UMA CONTA TITULAR PARA O PRODUTO E DOCUMENTO INDICADOS.
          for (Integer i = 0; i < pessoaExistente.getContasPessoa().size(); i++) {
            if (pessoaExistente.getContasPessoa() != null
                && pessoaExistente.getContasPessoa().size() > 0
                && pessoaExistente.getContasPessoa().get(i).getContaPagamento() != null
                && pessoaExistente
                        .getContasPessoa()
                        .get(i)
                        .getContaPagamento()
                        .getIdProdutoInstituicao()
                    != null
                && pessoaExistente
                    .getContasPessoa()
                    .get(i)
                    .getContaPagamento()
                    .getIdProdutoInstituicao()
                    .equals(model.getIdProdInstituicao())
                && pessoaExistente.getContasPessoa().get(i).getIdTitularidade() != null
                && pessoaExistente
                    .getContasPessoa()
                    .get(i)
                    .getIdTitularidade()
                    .equals(model.getTipoPessoa())
                && pessoaExistente
                        .getContasPessoa()
                        .get(i)
                        .getContaPagamento()
                        .getHierarquiaPontoDeRelacionamento()
                    != null
                && !pessoaExistente
                    .getContasPessoa()
                    .get(i)
                    .getContaPagamento()
                    .getHierarquiaPontoDeRelacionamento()
                    .getB2b()) {
              map.put(
                  "DTL",
                  "Pessoa já cadastrada em um ponto de relacionamento não B2B para o Produto: "
                      + model.getIdProdInstituicao()
                      + ", Documento: "
                      + model.getDocumento()
                      + " e Titularidade: "
                      + model.getTipoPessoa());
              map.put(
                  "msg",
                  "O CPF já possui conta para o produto selecionado. Entre em contato com a instituição para maiores informações.");
              map.put("created", false);
              throw new GenericServiceException(map);
            }
          }

          BeanUtils.copyProperties(pessoa, pessoaExistente, getNullPropertyNames(pessoa));
          pessoa = pessoaExistente;
        }
      }

      if (model.getEstrangeiro() != null
          && model.getEstrangeiro()
          && (model.getPassaporte() == null || model.getPassaporte().isEmpty())) {
        map.put("msg", "Passaporte vazio." + model);
        map.put("created", false);
        throw new GenericServiceException(map);
      }

      if (pessoaExistente == null || (permiteMultiplasContas)) {
        if (pessoa.getRazaoSocial() != null) {
          pessoa.setIdTipoPessoa(ConstantesB2B.TIPO_PESSOA_JURIDICA);
        }
        pessoa = save(pessoa);
        createCostumer(pessoa);
      } else {
        pessoa = save(pessoa);
      }

    } finally {
      lock.unlock();
    }

    return pessoa;
  }

  public Pessoa findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsNotCredito(
      Integer idProcessadora, Integer idInstituicao, String documento, Integer tipoPessoa) {
    return pessoaRepository
        .findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsNotCredito(
            idProcessadora, idInstituicao, documento, tipoPessoa);
  }

  private Boolean isProdutoCredito(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return produtoInstituicaoConfiguracaoService.isProdutoCredito(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  public Pessoa findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsCredito(
      Integer idProcessadora, Integer idInstituicao, String documento, Integer tipoPessoa) {
    return pessoaRepository
        .findOneByHierarquiaAndDocumentoAndTipoPessoaAndProdutoNaoB2bAndIsCredito(
            idProcessadora, idInstituicao, documento, tipoPessoa);
  }

  @Transactional
  public void createCostumer(Pessoa pessoa) {
    Customer c = new Customer(pessoa);
    JcardResponse jcardResponse = customerService.createCustomer(c);

    if (!jcardResponse.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar Customer: " + jcardResponse.getErrors());
    }
  }

  @Transactional
  public void createPessoaJuridica(CadastrarPessoaJuridica model, Pessoa pessoa) {

    HashMap<String, Object> map = new HashMap<>();

    if (!hierarquiaInstituicaoExists(model.getIdInstituicao(), model.getIdProcessadora())) {
      map.put(
          "msg",
          "Não foi possivel encontrar Instituicao informada. idInstituicao = "
              + model.getIdInstituicao()
              + " idProcessadora = "
              + model.getIdProcessadora());
      map.put("created", false);
      throw new GenericServiceException(map);
    }

    // trava para garantir que não haverá conflito durante as verificações e inserções em tabela
    lock.lock();
    try {
      Pessoa pessoaEncontrada =
          findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
              model.getIdProcessadora(),
              model.getIdInstituicao(),
              model.getDocumento(),
              model.getTipoPessoa());

      if (pessoaEncontrada != null) {
        List<ContaPagamento> contaPessoa =
            contaPagamentoService.findByContasPessoaIdPessoa(pessoaEncontrada.getIdPessoa());

        List<ContaPagamento> contasMesmaInstituicao =
            contaPessoa.stream()
                .filter(p -> p.getIdProdutoInstituicao().equals(model.getIdProdInstituicao()))
                .collect(Collectors.toList());
        if (!produtoInstituicaoConfiguracaoService.obterPermiteMultiplasContas(
            model.getIdProdInstituicao())) {
          if (!contasMesmaInstituicao.isEmpty()) {
            map.put("msg", "Pessoa já cadastrada." + model);
            map.put("created", false);
            throw new GenericServiceException(map);
          }
        }
      }

      pessoa = save(pessoa);
      createCostumer(pessoa);
    } finally {
      lock.unlock();
    }
  }

  private boolean hierarquiaInstituicaoExists(Integer idInstituicao, Integer idProcessadora) {
    HierarquiaInstituicaoId id = new HierarquiaInstituicaoId(idProcessadora, idInstituicao);
    return hierarquiaInstituicaoService.findById(id) != null;
  }

  public HierarquiaInstituicao buscaHierarquiaInstituicao(
      Integer idInstituicao, Integer idProcessadora) {
    HierarquiaInstituicaoId id = new HierarquiaInstituicaoId(idProcessadora, idInstituicao);
    return hierarquiaInstituicaoService.findById(id);
  }

  public void updateEmailPessoaByModel(AlterarPessoa model) {
    if (model.getDocumento() == null || model.getDocumento().isEmpty()) {
      model.setDocumento(this.findById(model.getIdPessoa()).getDocumento());
    }
    pessoaRepository.updateByIdInstituicaoAndIdProcessadoraAndDocumento(
        model.getIdInstituicao(),
        model.getIdProcessadora(),
        model.getEmail(),
        model.getDocumento());
  }

  public void updateEmailPessoaByModel(PortadorLogin portadorLogin) {
    pessoaRepository.updateByIdInstituicaoAndIdProcessadoraAndDocumento(
        portadorLogin.getIdInstituicao(),
        portadorLogin.getIdProcessadora(),
        portadorLogin.getEmail(),
        portadorLogin.getCpf());
  }

  public void updateEmailPessoaByModel(CadastrarPessoaLoyaltyRequest model) {
    pessoaRepository.updateByIdInstituicaoAndIdProcessadoraAndDocumento(
        model.getIdInstituicao(),
        model.getIdProcessadora(),
        model.getEmail(),
        model.getDocumento());
  }

  // Email loyalty nao pode mais ser alterado
  //	public void updateEmailPessoaByModel(EditarPessoaLoyaltyRequest model,Integer
  // idInstituicao,Integer idProcessadora){
  //
  //	pessoaRepository.updateByIdInstituicaoAndIdProcessadoraAndDocumento(idInstituicao,idProcessadora,model.getEmail(),model.getDocumento());
  //	}

  public Pessoa preparePessoaFisica(CadastrarPessoaFisica model, Pessoa pessoa) {
    BeanUtils.copyProperties(model, pessoa, getNullPropertyNames(model));
    pessoa.setDataNascimento(
        model.getDataNascimento() != null
            ? DateUtil.dateToLocalDateTime(model.getDataNascimento())
            : null);
    pessoa.setDataHoraInclusao(LocalDateTime.now());
    pessoa.setDataInicioRelacionamento(LocalDateTime.now());
    pessoa.setIdTipoPessoa(model.getTipoPessoa());
    return pessoa;
  }

  public Pessoa preparePessoaJuridica(CadastrarPessoaJuridica model, Pessoa pessoa) {
    BeanUtils.copyProperties(model, pessoa, getNullPropertyNames(model));
    pessoa.setDataHoraInclusao(LocalDateTime.now());
    pessoa.setDataInicioRelacionamento(LocalDateTime.now());
    pessoa.setIdTipoPessoa(model.getTipoPessoa());
    return pessoa;
  }

  public List<FuncionarioB2BResponseVo> findByFuncionariosByPeriodo(
      SecurityUser user,
      Integer idPrioridade,
      Integer idProdInst,
      Integer idSetorFilial,
      Date dataInicio,
      Date dataFim,
      Integer statusConta) {

    FuncionarioB2BFiltroVO filtroVo = new FuncionarioB2BFiltroVO();

    LocalDateTime dataInicioLDT = DateUtil.dateToLocalDateTime(dataInicio);
    LocalDateTime dataFimLDT = DateUtil.dateToLocalDateTime(dataFim);
    LocalDateTime dataFinal2359 = dataFimLDT.withHour(ConstantesB2B.MAX_HORA);
    dataFinal2359 = dataFinal2359.withMinute(ConstantesB2B.MAX_MINUTOS);
    dataFinal2359 = dataFinal2359.withSecond(ConstantesB2B.MAX_SEGUNDOS);

    filtroVo.setIdProcessadora(user.getIdProcessadora());
    filtroVo.setIdInstituicao(user.getIdInstituicao());
    filtroVo.setIdRegional(user.getIdRegional());
    filtroVo.setIdFilial(user.getIdFilial());
    filtroVo.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    filtroVo.setDataInicial(dataInicioLDT);
    filtroVo.setDataFinal(dataFinal2359);
    filtroVo.setIdPrioridade(idPrioridade);
    filtroVo.setIdSetorFilial(idSetorFilial);
    filtroVo.setIdProdInst(idProdInst);
    filtroVo.setStatusConta(statusConta);
    return pessoaRepository.findByPeriodo(filtroVo);
  }

  public List<PortadorB2BResponseVO> findByPortadorCompletoByPeriodo(
      SecurityUser user,
      Integer idPrioridade,
      Integer idProdInst,
      Integer idSetorFilial,
      Date dataInicio,
      Date dataFim) {

    FuncionarioB2BFiltroVO filtroVo = new FuncionarioB2BFiltroVO();

    LocalDateTime dataInicioLDT = DateUtil.dateToLocalDateTime(dataInicio);
    LocalDateTime dataFimLDT = DateUtil.dateToLocalDateTime(dataFim);
    LocalDateTime dataFinal2359 = dataFimLDT.withHour(ConstantesB2B.MAX_HORA);
    dataFinal2359 = dataFinal2359.withMinute(ConstantesB2B.MAX_MINUTOS);
    dataFinal2359 = dataFinal2359.withSecond(ConstantesB2B.MAX_SEGUNDOS);

    filtroVo.setIdProcessadora(user.getIdProcessadora());
    filtroVo.setIdInstituicao(user.getIdInstituicao());
    filtroVo.setIdRegional(user.getIdRegional());
    filtroVo.setIdFilial(user.getIdFilial());
    filtroVo.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
    filtroVo.setDataInicial(dataInicioLDT);
    filtroVo.setDataFinal(dataFinal2359);
    filtroVo.setIdPrioridade(idPrioridade);
    filtroVo.setIdSetorFilial(idSetorFilial);
    filtroVo.setIdProdInst(idProdInst);

    return pessoaRepository.findPortadorCompletoByPeriodo(filtroVo);
  }

  public Pessoa createPFAdicional(Pessoa p) {

    Pessoa pessoaExistente =
        findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            p.getIdProcessadora(), p.getIdInstituicao(), p.getDocumento(), p.getIdTipoPessoa());

    if (pessoaExistente != null) {
      p = pessoaExistente;
    } else {
      p = save(p);
      createCostumer(p);
    }
    return p;
  }

  public PessoaVO findByIdConta(Long idConta) {
    PessoaVO p = pessoaRepository.findByIdConta(idConta);
    return p;
  }

  public PessoaVO findOneByIdConta(Long idConta) {
    Pageable top = PageRequest.of(0, 1);
    List<PessoaVO> list = pessoaRepository.findListByIdConta(idConta, top);
    if (!Util.isNotNull(list)) {
      throw new GenericServiceException("Pessoa Não Encontrada");
    }
    PessoaVO p = list.get(0);
    return p;
  }

  public Pessoa findPessoaByIdConta(Long idConta) {
    return pessoaRepository.findPessoaByIdConta(idConta);
  }

  public List<String> findAllByRg(String RG) {
    return pessoaRepository.findAllByRg(RG);
  }

  public List<Long> findIdPessoasByIdConta(Long idConta) {
    return pessoaRepository.findIdPessoaByIdConta(idConta);
  }

  public List<Pessoa> buscarPessoasAdicionaisConta(Long idConta) {
    return pessoaRepository.buscarPessoasAdicionaisConta(idConta);
  }

  public HashMap<String, Object> verificaLimiteGlobalEmpresa(
      Map<String, VerificacaoLinhaTO> verificacao,
      SecurityUser user,
      FuncionarioCargaTO to,
      BigDecimal valorAtualizado) {
    // verificar se o limite disponivel

    HierarquiaPontoDeRelacionamento pontoRelacionamento =
        hierarquiaPntRelacionamentoService.getB2BPorId(user.getIdPontoDeRelacionamento(), user);

    HashMap<String, Object> map = new HashMap<>();

    BigDecimal valorTodosLimites = contaService.sumValorConveniosByPontoRelacionamento(user);
    valorTodosLimites = valorTodosLimites == null ? BigDecimal.ZERO : valorTodosLimites;

    BigDecimal valorRequerido = new BigDecimal(0);

    valorAtualizado =
        valorAtualizado == null ? BigDecimal.ZERO : valorAtualizado.add(to.getValorCarga());

    valorRequerido = valorTodosLimites.add(valorAtualizado);

    verificacao.put("valorAtualizado:", new VerificacaoLinhaTO(to.getValorCarga()));

    BigDecimal limiteMaxCredito =
        pontoRelacionamento.getLimiteMaxCredito() == null
            ? BigDecimal.ZERO
            : pontoRelacionamento.getLimiteMaxCredito();

    if (limiteMaxCredito.compareTo(valorRequerido) < 0
        && ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {

      map.put("msg", "Máximo de crédito atingido. Não foi possível cadastrar.");
      map.put(
          "DTL",
          "Limite máximo: "
              + limiteMaxCredito
              + ". Limite Disponível: "
              + (limiteMaxCredito.subtract(valorTodosLimites)));
      map.put("permitirForcado", 1);
      map.put("inadimplente", "'Empresa encontra-se inadimplente.'");

    } else if (limiteMaxCredito.compareTo(valorRequerido) < 0
        && !ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {

      map.put("msg", "Máximo de crédito atingido. Não foi possível cadastrar.");
      map.put(
          "DTL",
          "Limite Máximo: "
              + limiteMaxCredito
              + ". Limite Disponível: "
              + (limiteMaxCredito.subtract(valorTodosLimites)));
      map.put("permitirForcado", 1);

    } else if (limiteMaxCredito.compareTo(valorRequerido) > 0
        && ConstantesB2B.STATUS_EMPRESA_INADIMPLENTE.equals(pontoRelacionamento.getStatus())) {

      map.put("inadimplente", "1");
      map.put("msg", "Empresa encontra-se inadimplente.");
    }

    return map;
  }

  public Boolean existeDocumentoPessoasConta(String documento, Long idConta) {
    return pessoaRepository.existeDocumentoPessoasConta(documento, idConta) > 0;
  }

  public List<PortadorVinculacaoTO> credencialNaoVinculadoExterno(
      Integer idProdInstituicao,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    List<PortadorVinculacaoTO> portadores =
        pessoaRepository.findByCredencialNaoVinculadoExterno(
            idProdInstituicao,
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoRelacionamento);
    return portadores;
  }

  public Pessoa findPessoaTitularConta(Long idConta) {
    return pessoaRepository.findPessoaTitularConta(idConta);
  }

  public Long findTipoConta(Long idConta) {
    return pessoaRepository.findTipoConta(idConta);
  }

  public ResponseEntity<?> findTipoCredencial(ValidaTipoCredencial cred) {

    HashMap<String, String> map = new HashMap<>();

    GetCardResponse card = cardService.getToken(cred.getCredencial().toUpperCase());

    if (!card.getSuccess()) {
      throw new GenericServiceException("Não foi possível encontrar o cartão informado.");
    }

    Credencial credencial = credencialRepository.getCredencialPorToken(card.getCard().getToken());

    if (credencial == null) {
      throw new GenericServiceException("Não foi encontrado informações da credencial.");
    }

    Pessoa pessoa = findById(credencial.getIdPessoa());

    if (pessoa == null) {
      throw new GenericServiceException("Não foi encontrado a Pessoa informada.");
    }

    return new ResponseEntity<>(pessoa.getIdTipoPessoa(), HttpStatus.OK);
  }

  public String getTelefoneCelularPessoa(Long idPessoa, Long idConta) {
    TelefoneCelularPessoaResponse telefone = pessoaRepository.getTelefonePessoa(idPessoa);

    // Busca o telefone da pessoa titular da conta se o telefone da pessoa escolhida estiver nulo.
    if (telefone.getTelefoneCelular() == null) {
      telefone = pessoaRepository.getTelefonePessoaTitularConta(idConta);
    }

    if (telefone.getTelefoneCelular() != null && telefone.getDddTelefoneCelular() != null) {
      StringBuilder sb = new StringBuilder();
      sb.append("(");
      sb.append(telefone.getDddTelefoneCelular().toString());
      sb.append(") ");
      sb.append(telefone.getTelefoneCelular().toString().substring(0, 5));
      sb.append("-");
      sb.append(telefone.getTelefoneCelular().toString().substring(5));

      return sb.toString();
    }
    return "";
  }

  public ResponseEntity<Map<String, Object>> verificaNomeEmbossado(String nome) {
    Map<String, Object> map = new HashMap<>();

    if (UtilValidator.isNomeEmbossadoValid(nome)) {
      map.put("valid", true);
      map.put("msg", "Nome embossado válido!.");
    } else {
      map.put("valid", false);
      map.put("msg", "Nome embossado inválido!.");
    }

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  public void updateEmailPessoaByPessoa(Pessoa pessoaAlterada) {
    if (pessoaAlterada.getDocumento() == null || pessoaAlterada.getDocumento().isEmpty()) {
      pessoaAlterada.setDocumento(this.findById(pessoaAlterada.getIdPessoa()).getDocumento());
    }
    pessoaRepository.updateByIdInstituicaoAndIdProcessadoraAndDocumento(
        pessoaAlterada.getIdInstituicao(),
        pessoaAlterada.getIdProcessadora(),
        pessoaAlterada.getEmail(),
        pessoaAlterada.getDocumento());
  }

  public DadosPreCadastroResponse buscarNomeEmailPorCPF(Integer idInstituicao, String cpf) {

    DadosPreCadastroResponse preCadastroResponse = new DadosPreCadastroResponse();

    Pessoa pessoa = pessoaRepository.buscarNomeEmailPorCPF(idInstituicao, cpf);

    if (pessoa == null) {
      return preCadastroResponse;
    }

    if (Util.isNotNull(pessoa.getEmail()) && !pessoa.getEmail().trim().equals("")) {
      preCadastroResponse.setEmail(pessoa.getEmail());
    }

    preCadastroResponse.setNome(pessoa.getNomeCompleto());

    return preCadastroResponse;
  }

  public Boolean findByIdInstituicaoAndDocumento(
      Integer idProcessadora, Integer idInstituicao, String documento) {

    Pessoa pessoa =
        pessoaRepository.findOneByIdProcessadoraAndIdInstituicaoAndDocumento(
            idProcessadora, idInstituicao, documento);

    if (pessoa != null) {

      return true;
    }

    return false;
  }

  public Boolean findPessoaInMaisPorDocumento(String documento) {

    Pessoa pessoa = pessoaRepository.findPessoaProdutoInMaisByDocumento(documento);

    if (pessoa != null) {

      return true;
    }

    return false;
  }

  public List<FuncionarioProdutosContas> findFuncionariosContaTransferivel(
      FuncionariosModel model, SecurityUser user) {

    List<FuncionarioProdutosContas> funcionarioProdutosContas =
        new ArrayList<FuncionarioProdutosContas>();

    if (user.getIdProcessadora() == null
        || user.getIdInstituicao() == null
        || user.getIdRegional() == null
        || user.getIdFilial() == null
        || user.getIdPontoDeRelacionamento() == null) {
      throw new GenericServiceException("Não foi possível recuperar hierarquia do usuário");
    }

    List<ContaPagamento> contasPagamento =
        contaPagamentoService.findByCpfAndHierarquia(
            model.getCpf(),
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());

    if (contasPagamento == null || contasPagamento.isEmpty()) {
      throw new GenericServiceException("Não foi encontrado conta para esse usuário.");
    }

    if (model.getIdProdutoInstituicao() == null) {
      throw new GenericServiceException("Erro ao buscar produto.");
    }

    List<ProdutoInstituicaoResponse> produtos = produtoInstituicaoService.findByUsuarioLogado(user);

    for (ContaPagamento contaPagamento : contasPagamento) {

      if (produtos != null) {

        ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
            produtoInstituicaoConfiguracaoService.findByIdConta(contaPagamento.getIdConta());

        if (produtoInstituicaoConfiguracao != null) {

          if (contaPagamento.getIdRelacionamento().equals(1)
              && produtoInstituicaoConfiguracao.getSuportaContaBase() == true
              && contaPagamento.getIdProdutoInstituicao().equals(model.getIdProdutoInstituicao())) {

            Pessoa pessoa = findPessoaByIdConta(contaPagamento.getIdConta());

            FuncionarioProdutosContas funcionarioConta = new FuncionarioProdutosContas();
            funcionarioConta.setContaPagamento(contaPagamento.getIdContaPagamento());
            funcionarioConta.setIdPessoa(pessoa.getIdPessoa());
            funcionarioConta.setIdConta(contaPagamento.getIdConta());
            funcionarioConta.setNomeCompleto(pessoa.getNomeCompleto());

            funcionarioProdutosContas.add(funcionarioConta);
          }
        }
      }
    }

    if (funcionarioProdutosContas.isEmpty()) {
      throw new GenericServiceException("O destinatário informado não possui produto transferível");
    }

    return funcionarioProdutosContas;
  }

  public Boolean pessoaHasContaCreditoNaoB2B(Long idPessoa) {
    return pessoaRepository.pessoaHasContaCreditoNaoB2B(idPessoa);
  }

  public TelefoneCelularPessoaResponse findTelefoneCelularByIdPessoa(Long idPessoa) {
    return pessoaRepository.getTelefonePessoa(idPessoa);
  }

  public Boolean existePessoaAdicional(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return pessoaRepository.existePessoaAdicional(idProcessadora, idInstituicao, documento);
  }

  public Pessoa
      buscarPessoaFisicaAdicionalPorProcessadoraInstituicaoDocumentoTitularDocumentoAdicional(
          Integer idProcessadora,
          Integer idInstituicao,
          String documentotitular,
          String documentoAdicional) {
    return pessoaRepository
        .buscarPessoaFisicaAdicionalPorProcessadoraInstituicaoDocumentoTitularDocumentoAdicional(
            idProcessadora, idInstituicao, documentotitular, documentoAdicional);
  }

  public List<Pessoa> findPessoaListWithLogin(PortadorLogin portadorLogin) {
    return findPessoaListWithLogin(
        portadorLogin.getIdProcessadora(),
        portadorLogin.getIdInstituicao(),
        portadorLogin.getCpf(),
        portadorLogin.getDocumentoAcesso(),
        portadorLogin.getGrupoAcesso());
  }

  public List<Pessoa> findPessoaListWithLogin(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso) {

    List<Pessoa> finalListPessoas = new ArrayList<>();
    Pessoa pessoaSemSetorFilial =
        findAnyPessoaWithLoginAndSetorFilialIsNull(
            idProcessadora, idInstituicao, documento, documentoAcesso, grupoAcesso);
    if (pessoaSemSetorFilial != null) {
      finalListPessoas.add(pessoaSemSetorFilial);
    }
    List<Pessoa> pessoasComSetorFilial =
        findPessoaListWithLoginAndSetorFilialIsNotNull(
            idProcessadora, idInstituicao, documento, documentoAcesso, grupoAcesso);
    if (pessoasComSetorFilial != null && !pessoasComSetorFilial.isEmpty()) {
      finalListPessoas.addAll(pessoasComSetorFilial);
    }
    return finalListPessoas;
  }

  @SuppressWarnings("unnused")
  public Pessoa findPessoaWithLoginAndSetorFilialIsNull(PortadorLogin portadorLogin) {
    return pessoaRepository.findPessoaWithLoginAndSetorFilialIsNull(
        portadorLogin.getIdProcessadora(),
        portadorLogin.getIdInstituicao(),
        portadorLogin.getCpf(),
        portadorLogin.getDocumentoAcesso(),
        portadorLogin.getGrupoAcesso());
  }

  // Metodo que deve ser substituido por findPessoaWithLoginAndSetorFilialIsNull
  // quando a constraint idProcessadora, idInstituicao, documento, setorFilial existir em Pessoa
  public Pessoa findAnyPessoaWithLoginAndSetorFilialIsNull(PortadorLogin portadorLogin) {
    return pessoaRepository.findAnyPessoaWithLoginAndSetorFilialIsNull(
        portadorLogin.getIdProcessadora(),
        portadorLogin.getIdInstituicao(),
        portadorLogin.getCpf(),
        portadorLogin.getDocumentoAcesso(),
        portadorLogin.getGrupoAcesso());
  }

  @SuppressWarnings("unnused")
  public Pessoa findPessoaWithLoginAndSetorFilialIsNull(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso) {
    return pessoaRepository.findPessoaWithLoginAndSetorFilialIsNull(
        idProcessadora, idInstituicao, documento, documentoAcesso, grupoAcesso);
  }

  // Metodo que deve ser substituido por findPessoaWithLoginAndSetorFilialIsNull
  // quando a constraint idProcessadora, idInstituicao, documento, setorFilial existir em Pessoa
  public Pessoa findAnyPessoaWithLoginAndSetorFilialIsNull(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso) {
    return pessoaRepository.findAnyPessoaWithLoginAndSetorFilialIsNull(
        idProcessadora, idInstituicao, documento, documentoAcesso, grupoAcesso);
  }

  @SuppressWarnings("unnused")
  public List<Pessoa> findPessoaListWithLoginAndSetorFilialIsNotNull(PortadorLogin portadorLogin) {
    return pessoaRepository.findPessoaListWithLoginAndSetorFilialIsNotNull(
        portadorLogin.getIdProcessadora(),
        portadorLogin.getIdInstituicao(),
        portadorLogin.getCpf(),
        portadorLogin.getDocumentoAcesso(),
        portadorLogin.getGrupoAcesso());
  }

  public List<Pessoa> findPessoaListWithLoginAndSetorFilialIsNotNull(
      Integer idProcessadora,
      Integer idInstituicao,
      String documento,
      String documentoAcesso,
      Long grupoAcesso) {
    return pessoaRepository.findPessoaListWithLoginAndSetorFilialIsNotNull(
        idProcessadora, idInstituicao, documento, documentoAcesso, grupoAcesso);
  }

  public Pessoa findPessoaAtualizadaRecente(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return this.pessoaRepository.findPessoaAtualizadaRecente(
        idProcessadora, idInstituicao, documento);
  }

  public Pessoa findPessoaAtualizadaRecenteComGrupoAcesso(
      Integer idProcessadora, Integer idInstituicao, String documento, Long grupoAcesso) {
    return this.pessoaRepository.findPessoaAtualizadaRecenteComGrupoAcesso(
        idProcessadora, idInstituicao, documento, grupoAcesso);
  }

  public ContaPagamento buscarPorCnpjECpjRepresentante(
      Integer idInstituicao, String cnpj, String cpf) {
    Pessoa pessoa = this.findPessoaAtualizadaRecente(ID_PROCESSADORA_ITS_PAY, idInstituicao, cnpj);
    List<ContaPagamento> contasPagamento =
        this.contaPagamentoService.findByIdPessoa(pessoa.getIdPessoa());
    ContaPagamento conta = null;
    for (ContaPagamento contaPagamento : contasPagamento) {
      RepresentanteLegal representanteLegal =
          this.representanteLegalService.findOneByIdContaAndCpfAndStatus(
              contaPagamento.getIdConta(),
              cpf,
              RepresentanteLegalService.REPRESENTANTE_LEGAL_ATIVO);
      if (representanteLegal != null) {
        conta = contaPagamento;
      }
    }
    return conta;
  }

  public void salvarArquivoCadastro(
      SecurityUser user,
      MultipartFile file,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao,
      List<Integer> idsProdutoInstituicao)
      throws IOException {
    if (idInstituicao == null) {
      idInstituicao = user.getIdInstituicao();
    }
    if (idRegional == null) {
      idRegional = user.getIdRegional();
    }
    if (idFilial == null) {
      idFilial = user.getIdFilial();
    }
    if (idPontoDeRelacionamento == null) {
      idPontoDeRelacionamento = user.getIdPontoDeRelacionamento();
    }

    if (!idsProdutoInstituicao.isEmpty()) {
      for (Integer produtoInstituicao : idsProdutoInstituicao) {
        InputStream is = file.getInputStream();
        int posicao = file.getOriginalFilename().lastIndexOf('.');
        String extensao = file.getOriginalFilename().substring(posicao);

        if (extensao.equalsIgnoreCase(".xlsx")) {
          Date data = new Date();
          String pasta = issuerDirEntrada;
          SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
          simpleDateFormat.applyPattern("yyyyMMddHHmmss");
          String dateString = simpleDateFormat.format(data);

          String caminho =
              "CADASTRO_"
                  + idInstituicao
                  + "_"
                  + idRegional
                  + "_"
                  + idFilial
                  + "_"
                  + idPontoDeRelacionamento
                  + "_"
                  + produtoInstituicao
                  + "_"
                  + dateString;
          String caminhoArquivo = pasta + caminho;

          Path destinoFinal = Paths.get(caminhoArquivo + extensao);

          if (pasta == null) {
            throw new GenericServiceException(
                "O diretório para salvar o anexo não foi encontrado!");
          }

          StringBuilder caminhoFinal = new StringBuilder();
          caminhoFinal.append(destinoFinal);

          try {
            FileOutputStream fout = new FileOutputStream(destinoFinal.toFile());
            while (is.available() != 0) {
              fout.write(is.read());
            }
            is.close();
            fout.close();
          } catch (Exception e) {
            throw new GenericServiceException(
                "Não foi possível salvar o arquivo no caminho indicado: " + caminhoFinal, e);
          }

        } else {
          throw new PemGenerationException(
              "O arquivo com o formato "
                  + extensao
                  + " não é suportado. Faz upload apenas de arquivos com formato .xlsx");
        }
      }
    } else {
      InputStream is = file.getInputStream();
      int posicao = file.getOriginalFilename().lastIndexOf('.');
      String extensao = file.getOriginalFilename().substring(posicao);

      if (extensao.equalsIgnoreCase(".xlsx")) {
        Date data = new Date();
        String pasta = issuerDirEntrada;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
        simpleDateFormat.applyPattern("yyyyMMddHHmmss");
        String dateString = simpleDateFormat.format(data);

        String caminho =
            "CADASTRO_"
                + idInstituicao
                + "_"
                + idRegional
                + "_"
                + idFilial
                + "_"
                + idPontoDeRelacionamento
                + "_"
                + idProdutoInstituicao
                + "_"
                + dateString;
        String caminhoArquivo = pasta + caminho;

        Path destinoFinal = Paths.get(caminhoArquivo + extensao);

        if (pasta == null) {
          throw new GenericServiceException("O diretório para salvar o anexo não foi encontrado!");
        }

        StringBuilder caminhoFinal = new StringBuilder();
        caminhoFinal.append(destinoFinal);

        try {
          FileOutputStream fout = new FileOutputStream(destinoFinal.toFile());
          while (is.available() != 0) {
            fout.write(is.read());
          }
          is.close();
          fout.close();
        } catch (Exception e) {
          throw new GenericServiceException(
              "Não foi possível salvar o arquivo no caminho indicado: " + caminhoFinal, e);
        }

      } else {
        throw new PemGenerationException(
            "O arquivo com o formato "
                + extensao
                + " não é suportado. Faz upload apenas de arquivos com formato .xlsx");
      }
    }
  }

  public PessoaPortadorCafVO buscarDadosPessoaCafByInstituicao(
      String documento, Integer idInstituicao, SecurityUser user) {

    if (!user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.PROCESSADORA.getIdNivelHierarquia())
        && !user.getIdHierarquiaNivel()
            .equals(CheckNivelHierarquiaEnum.INSTITUICAO.getIdNivelHierarquia())) {
      throw new AccessDeniedException("O usuário não possui permissão para ação");
    }

    return pessoaRepository.buscarDadosPessoaCafByInstituicao(documento, idInstituicao);
  }

  public Pessoa findPessoaByDocumentoAndIdInstituicao(String documento, Integer idInstituicao) {
    return pessoaRepository.findPessoaByDocumentoAndIdInstituicao(documento, idInstituicao);
  }

  public void salvarArquivoCadastroDependenteResponsavel(
      MultipartFile file,
      Integer idProdInstituicaoDependente,
      Integer idInstituicao,
      Integer idFilial,
      Integer idRegional,
      Integer idPontoDeRelacionamento)
      throws IOException {

    InputStream is = file.getInputStream();
    int posicao = file.getOriginalFilename().lastIndexOf('.');
    String extensao = file.getOriginalFilename().substring(posicao);

    if (extensao.equalsIgnoreCase(".xlsx")) {
      Date data = new Date();
      String pasta = issuerDirEntrada;
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
      simpleDateFormat.applyPattern("yyyyMMddHHmmss");
      String dateString = simpleDateFormat.format(data);

      String caminho =
          "CADASTRO_"
              + idInstituicao
              + "_"
              + idRegional
              + "_"
              + idFilial
              + "_"
              + idPontoDeRelacionamento
              + "_"
              + idProdInstituicaoDependente
              + "_"
              + dateString;
      String caminhoArquivo = pasta + caminho;

      Path destinoFinal = Paths.get(caminhoArquivo + extensao);

      if (pasta == null) {
        throw new GenericServiceException("O diretório para salvar o anexo não foi encontrado!");
      }

      StringBuilder caminhoFinal = new StringBuilder();
      caminhoFinal.append(destinoFinal);

      try {
        FileOutputStream fout = new FileOutputStream(destinoFinal.toFile());
        while (is.available() != 0) {
          fout.write(is.read());
        }
        is.close();
        fout.close();
      } catch (Exception e) {
        throw new GenericServiceException(
            "Não foi possível salvar o arquivo no caminho indicado: " + caminhoFinal.toString(), e);
      }

    } else {
      throw new PemGenerationException(
          "O arquivo com o formato "
              + extensao
              + " não é suportado. Faz upload apenas de arquivos com formato .xlsx");
    }
  }

  public void salvarArquivoVinculoDependenteResponsavel(
      MultipartFile file,
      Integer idProdInstituicaoDependente,
      Integer idInstituicao,
      Integer idFilial,
      Integer idRegional,
      Integer idPontoDeRelacionamento)
      throws IOException {

    InputStream is = file.getInputStream();
    int posicao = file.getOriginalFilename().lastIndexOf('.');
    String extensao = file.getOriginalFilename().substring(posicao);

    if (extensao.equalsIgnoreCase(".xlsx")) {
      Date data = new Date();
      String pasta = issuerDirEntrada;
      SimpleDateFormat simpleDateFormat = new SimpleDateFormat();
      simpleDateFormat.applyPattern("yyyyMMddHHmmss");
      String dateString = simpleDateFormat.format(data);

      String caminho =
          "VINCULO_"
              + idInstituicao
              + "_"
              + idRegional
              + "_"
              + idFilial
              + "_"
              + idPontoDeRelacionamento
              + "_"
              + idProdInstituicaoDependente
              + "_"
              + dateString;
      String caminhoArquivo = pasta + caminho;

      Path destinoFinal = Paths.get(caminhoArquivo + extensao);

      if (pasta == null) {
        throw new GenericServiceException("O diretório para salvar o anexo não foi encontrado!");
      }

      StringBuilder caminhoFinal = new StringBuilder();
      caminhoFinal.append(destinoFinal);

      try {
        FileOutputStream fout = new FileOutputStream(destinoFinal.toFile());
        while (is.available() != 0) {
          fout.write(is.read());
        }
        is.close();
        fout.close();
      } catch (Exception e) {
        throw new GenericServiceException(
            "Não foi possível salvar o arquivo no caminho indicado: " + caminhoFinal.toString(), e);
      }

    } else {
      throw new PemGenerationException(
          "O arquivo com o formato "
              + extensao
              + " não é suportado. Faz upload apenas de arquivos com formato .xlsx");
    }
  }

  public void salvarArquivoPDAFLote(MultipartFile file) throws GenericServiceException {

    String nomeArquivo = file.getOriginalFilename();
    validarFormatacaoNomeArquivoPdaf(nomeArquivo);
    String pasta = issuerDirEntrada;
    String caminhoArquivo = pasta + nomeArquivo;
    if (pasta == null) {
      throw new GenericServiceException(
          ConstantesErro.PDAF_ARQUIVO_RECEBIDO_FORA_DO_PADRAO.getMensagem());
    }

    StringBuilder caminhoFinal = new StringBuilder();
    caminhoFinal.append(caminhoArquivo);

    try {
      InputStream is = file.getInputStream();
      FileOutputStream fout = new FileOutputStream(caminhoArquivo);
      while (is.available() != 0) {
        fout.write(is.read());
      }
      is.close();
      fout.close();
    } catch (IOException e) {
      log.error("Ocorreu um erro ao salvar arquivo. " + e.getMessage(), e);
      throw new GenericServiceException(
          ConstantesErro.ERRO_SALVAR_ARQUIVO_PDAF_SERVIDOR.format(caminhoFinal));
    }
  }

  @Transactional
  public void createRepresentanteLegal(
      SecurityUser user, CadastrarRepresentanteLegalRequest model) {
    List<RepresentanteLegal> representantes =
        representanteLegalService.findByIdConta(model.getIdConta());

    evitarMesmoCPFRepresentanteConta(model, representantes);
    List<RepresentanteLegal> ativos = new ArrayList<>();

    if (representantes != null && !representantes.isEmpty()) {
      representantes.forEach(
          rep -> {
            if (rep.getStatus().equals(ATIVO)) {
              ativos.add(rep);
            }
          });
    }

    RepresentanteLegal representante = new RepresentanteLegal();
    BeanUtils.copyProperties(model, representante, Util.getNullPropertyNames(model));

    representante.setStatus(ATIVO);
    representante.setDataHoraStatus(new Date());
    representante.setIdUsuarioInclusao(model.getIdUsuario());
    representante.setIdUsuarioManutencao(model.getIdUsuario());
    representante.setDataHoraInclusao(new Date());

    representanteLegalService.saveAndFlush(representante);

    if (user != null) {
      if (ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(user.getIdInstituicao())) {
        Pessoa pessoa = findPessoaByIdConta(model.getIdConta());
        emailService.enviarEmailAtivacaoPaxPay(
            model.getEmail(),
            "<EMAIL>",
            model.getIdConta(),
            pessoa.getNomeFantasia());
      }
    }
  }

  private void evitarMesmoCPFRepresentanteConta(
      CadastrarRepresentanteLegalRequest model, List<RepresentanteLegal> representantes) {
    if (representantes != null && !representantes.isEmpty()) {
      representantes.forEach(
          rep -> {
            if (rep.getCpf().equals(model.getCpf())) {
              throw new GenericServiceException(
                  "CPF já cadastrado para um representante legal desta conta!");
            }
          });
    }
  }

  @Transactional
  public void salvarCadastroPDAF(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      SecurityUser user,
      String nomeSetorFilial)
      throws FileNotFoundException, GenericServiceException {

    File copy = validaNomeArquivoPreparaUserERetornaFile(is, nomeArquivo, user, "CADASTRO");

    List<CadastrarPessoaPDAFRequest> to;

    to = importadorFuncionarioXls.getImportacaoCadastroPDAF(new FileInputStream(copy));

    if (to == null) {
      throw new GenericServiceException(
          ConstantesErro.ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_CRIAR_OBJETO.getMensagem());
    }

    for (CadastrarPessoaPDAFRequest cadastrarPessoaPDAFRequest : to) {
      createPessoaPDAF(
          cadastrarPessoaPDAFRequest,
          user,
          idProdutoInstituicao,
          user.getIdRegional(),
          user.getIdFilial(),
          user.getIdPontoDeRelacionamento(),
          nomeSetorFilial);
    }
  }

  public void editarCadastroPDAF(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      SecurityUser user,
      String nomeSetorFilial)
      throws FileNotFoundException {

    File copy = validaNomeArquivoPreparaUserERetornaFile(is, nomeArquivo, user, "EDICAO");

    List<CadastrarPessoaPDAFRequest> to;

    to = importadorFuncionarioXls.getImportacaoCadastroPDAF(new FileInputStream(copy));

    if (to == null) {
      throw new GenericServiceException(
          ConstantesErro.ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_CRIAR_OBJETO.getMensagem());
    }

    for (CadastrarPessoaPDAFRequest cadastrarPessoaPDAFRequest : to) {
      pessoaFacade.editarPJPDAF(
          cadastrarPessoaPDAFRequest,
          user,
          user.getIdRegional(),
          user.getIdFilial(),
          user.getIdPontoDeRelacionamento(),
          idProdutoInstituicao,
          nomeSetorFilial);
    }
  }

  public void criaAdicionalPDAF(
      InputStream is,
      String nomeArquivo,
      Integer idProdutoInstituicao,
      SecurityUser user,
      String nomeSetorFilial)
      throws FileNotFoundException {

    File copy = validaNomeArquivoPreparaUserERetornaFile(is, nomeArquivo, user, "ADICIONAL");

    List<CadastrarPessoaPDAFRequest> to;

    to = importadorFuncionarioXls.getImportacaoCadastroPDAF(new FileInputStream(copy));

    if (to == null) {
      throw new GenericServiceException(
          ConstantesErro.ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_CRIAR_OBJETO.getMensagem());
    }

    if (ImportadorArquivo.isXlsx(nomeArquivo) || ImportadorArquivo.isXls(nomeArquivo)) {
      to = importadorFuncionarioXls.getImportacaoCadastroPDAF(new FileInputStream(copy));
    }

    for (CadastrarPessoaPDAFRequest cadastrarPessoaPDAFRequest : to) {
      contaPagamentoService.criarPessoaAndCartaoAdicionalByConta(
          cadastrarPessoaPDAFRequest,
          user,
          user.getIdRegional(),
          user.getIdFilial(),
          user.getIdPontoDeRelacionamento(),
          idProdutoInstituicao,
          ID_PRODUCAO_INSTITUICAO_BRBCARD,
          nomeSetorFilial);
    }
  }

  private File validaNomeArquivoPreparaUserERetornaFile(
      InputStream is, String nomeArquivo, SecurityUser user, String prefixoEsperado) {
    List<CadastrarPessoaPDAFRequest> to;
    File copy = FileUtil.inputStreamToFile(is);
    validarFormatacaoNomeArquivoPdaf(nomeArquivo);

    Pattern regexPattern = Pattern.compile(patternArquivoPdaf);
    Matcher matcher = regexPattern.matcher(nomeArquivo);
    if (ID_USUARIO_AUTOMATIZA_CADASTROS.equals(user.getIdUsuario())) {
      String prefixoArquivo = null;
      Integer idInstituicao = null;
      Integer idRegional = null;
      Integer idFilial = null;
      Integer idPonto = null;
      if (matcher.find()) {
        prefixoArquivo = matcher.group(N_1);
        idInstituicao = Integer.parseInt(matcher.group(N_2));
        idRegional = Integer.parseInt(matcher.group(N_3));
        idFilial = Integer.parseInt(matcher.group(N_4));
        idPonto = Integer.parseInt(matcher.group(N_5));
      }
      if (prefixoArquivo != null && !prefixoArquivo.startsWith(prefixoEsperado)) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_RECEBIDO_DIFERENTE_ESPERADO.format(
                prefixoArquivo, prefixoEsperado));
      }
      user.setIdInstituicao(idInstituicao);
      user.setIdRegional(idRegional);
      user.setIdFilial(idFilial);
      user.setIdPontoDeRelacionamento(idPonto);
    }
    return copy;
  }

  private void validarFormatacaoNomeArquivoPdaf(String nomeArquivo) throws GenericServiceException {
    if (!Pattern.matches(patternArquivoPdaf, nomeArquivo)) {
      throw new GenericServiceException(
          ConstantesErro.ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_PROC5000_PDAF.getMensagem());
    }
    if (!ImportadorArquivo.isXlsx(nomeArquivo) && !ImportadorArquivo.isXls(nomeArquivo)) {
      throw new GenericServiceException(
          ConstantesErro.ERRO_ARQUIVO_CADASTRO_EXTENSAO_NAO_RECONHECIDA_PROC5000_PDAF
              .getMensagem());
    }

    Pattern regexPattern = Pattern.compile(patternArquivoPdaf);
    Matcher matcher = regexPattern.matcher(nomeArquivo);
    if (matcher.find()) {
      if (matcher.group(N_1) == null) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_PREFIXO_PROC5000_PDAF
                .getMensagem());
      }
      if (matcher.group(N_2) == null) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_INSTITUICAO_PROC5000_PDAF
                .getMensagem());
      }
      if (matcher.group(N_3) == null) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_REGIONAL_PROC5000_PDAF
                .getMensagem());
      }
      if (matcher.group(N_4) == null) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_FILIAL_PROC5000_PDAF
                .getMensagem());
      }
      if (matcher.group(N_5) == null) {
        throw new GenericServiceException(
            ConstantesErro
                .ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_PONTO_RELACIONAMENTO_PROC5000_PDAF
                .getMensagem());
      }
      if (matcher.group(N_6) == null) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_CADASTRO_NOME_FORA_PADRAO_SEM_PRODUTO_PROC5000_PDAF
                .getMensagem());
      }
    }
  }

  public List<ResponavelDependenteContas> informacoesResponsavelEDependentes(Long idPessoa) {

    PessoaResponsavelDependente dependente =
        pessoaResponsavelDependenteRepository.findByIdPessoaDependente(idPessoa);
    List<ResponavelDependenteContas> newList = new ArrayList<>();

    if (dependente == null) {

      newList = pessoaRepository.findByPessoasDependentes(idPessoa);
    }

    if (dependente != null) {
      newList = pessoaRepository.findByPessoaResponsavel(idPessoa);
    }
    return newList;
  }

  public Boolean validaResponsavelOuDependente(Long idPessoa) {
    PessoaResponsavelDependente isPessoaDependente =
        pessoaResponsavelDependenteRepository.findByIdPessoaDependente(idPessoa);
    if (isPessoaDependente != null) {
      return true;
    } else {
      return false;
    }
  }

  public ContaPagamento findContasPagamentosComGrupoAcessoETipoProduto(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      String documento,
      Integer idTipoPessoa,
      Long grupoAcesso,
      TipoProdutoEnum tipoProduto) {
    return contaPagamentoRepository.findContasPagamentosComGrupoAcessoETipoProduto(
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoDeRelacionamento,
        documento,
        idTipoPessoa,
        grupoAcesso,
        tipoProduto);
  }

  public List<Pessoa> getPessoasPortador(PortadorLogin userPortador) {
    List<ContaPagamento> contasPortador = contaPagamentoService.obterContasDoPortador(userPortador);
    return contasPortador.stream()
        .flatMap(conta -> conta.getContasPessoa().stream())
        .map(ContaPessoa::getPessoa)
        .distinct()
        .collect(Collectors.toList());
  }
}
