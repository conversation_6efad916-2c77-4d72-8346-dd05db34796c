package br.com.json.bean.cadastral;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@ApiModel(value = "GerarCredencialRequest")
public class GerarCredencialRequest implements Serializable {

  private static final long serialVersionUID = -3477516691515311846L;

  @NotNull(message = "O campo ID Conta é obrigatório.")
  @ApiModelProperty(required = true)
  private Long idConta;

  private Long idPessoa;

  @NotNull(message = "O campo ID Usuário é obrigatório.")
  @ApiModelProperty(required = true)
  private Integer idUsuario;

  @Size(max = 20, message = "O Apelido Virtual deve conter, no máximo, 20 caracteres.")
  private String virtualApelido;

  private Integer virtualMesesValidade;

  @NotNull
  @ApiModelProperty(required = true)
  private Boolean virtual;

  private LocalDateTime dtHrLiberacaoEmissao;

  private Boolean isIntegracao;

  /**
   * Todas os sistemas que utilizarem as API que tem como base este objeto, estarao usando o valor
   * default para adicional como "false".
   */
  private boolean adicional;

  private Boolean multiConta;

  private Boolean isOnboard;

  public GerarCredencialRequest() {
    this(null, null);
  }

  public GerarCredencialRequest(Integer idUsuario, Boolean virtual) {
    super();
    this.idUsuario = idUsuario;
    this.virtual = virtual;
  }

  public Boolean getVirtual() {
    return virtual;
  }

  public void setVirtual(Boolean virtual) {
    this.virtual = virtual;
  }

  public Long getIdConta() {
    return idConta;
  }

  public void setIdConta(Long idConta) {
    this.idConta = idConta;
  }

  public Long getIdPessoa() {
    return idPessoa;
  }

  public void setIdPessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  public Integer getIdUsuario() {
    return idUsuario;
  }

  public void setIdUsuario(Integer idUsuario) {
    this.idUsuario = idUsuario;
  }

  public String getVirtualApelido() {
    return virtualApelido;
  }

  public void setVirtualApelido(String virtualApelido) {
    this.virtualApelido = virtualApelido;
  }

  public Integer getVirtualMesesValidade() {
    return virtualMesesValidade;
  }

  public void setVirtualMesesValidade(Integer virtualMesesValidade) {
    this.virtualMesesValidade = virtualMesesValidade;
  }

  public LocalDateTime getDtHrLiberacaoEmissao() {
    return dtHrLiberacaoEmissao;
  }

  public void setDtHrLiberacaoEmissao(LocalDateTime dtHrLiberacaoEmissao) {
    this.dtHrLiberacaoEmissao = dtHrLiberacaoEmissao;
  }

  public boolean getAdicional() {
    return adicional;
  }

  public void setAdicional(boolean adicional) {
    this.adicional = adicional;
  }

  public Boolean getIntegracao() {
    return isIntegracao;
  }

  public void setIntegracao(Boolean integracao) {
    isIntegracao = integracao;
  }

  public Boolean getMultiConta() {
    return multiConta;
  }

  public void setMultiConta(Boolean multiConta) {
    this.multiConta = multiConta;
  }

  public Boolean getOnboard() {
    return isOnboard;
  }

  public void setOnboard(Boolean onboard) {
    this.isOnboard = onboard;
  }

  @Override
  public String toString() {
    return "GerarCredencialRequest [idConta="
        + idConta
        + ", idPessoa="
        + idPessoa
        + ", idUsuario="
        + idUsuario
        + ", virtualApelido="
        + virtualApelido
        + ", virtualMesesValidade="
        + virtualMesesValidade
        + ", multiConta="
        + multiConta
        + "]";
  }
}
