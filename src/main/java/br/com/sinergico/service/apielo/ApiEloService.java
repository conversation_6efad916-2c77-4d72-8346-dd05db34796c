package br.com.sinergico.service.apielo;

import br.com.client.rest.jcard.json.bean.CardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.VcnElo;
import br.com.entity.suporte.QrCodeAccessToken;
import br.com.entity.transacional.TransacaoQrCodeElo;
import br.com.exceptions.GenericServiceException;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.QrCodeEloCallbackStatus;
import br.com.sinergico.enums.QrCodeEloTransactionTypeEnum;
import br.com.sinergico.enums.VcnEloStatusEnum;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoRepository;
import br.com.sinergico.repository.cadastral.VcnEloRepository;
import br.com.sinergico.repository.suporte.QrCodeAccessTokenRepository;
import br.com.sinergico.repository.transacional.TransacaoQrCodeEloRepository;
import br.com.sinergico.security.CriptoUtil;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.graphql.dataresponse.DataMutationCreateCardTokenForUser;
import br.com.sinergico.util.graphql.dataresponse.DataMutationCreateLoginSalt;
import br.com.sinergico.util.graphql.dataresponse.DataMutationLoginElo;
import br.com.sinergico.util.graphql.dataresponse.DataMutationSuspendCardToken;
import br.com.sinergico.util.graphql.dataresponse.DataQueryServerPublicKey;
import br.com.sinergico.util.graphql.enums.CardSuspendReason;
import br.com.sinergico.util.graphql.requests.CreateCardTokenForUserBySensitive;
import br.com.sinergico.util.graphql.requests.CreateCardTokenForUserResponse;
import br.com.sinergico.util.graphql.requests.CreateLoginSalt;
import br.com.sinergico.util.graphql.requests.CreateLoginSaltResponse;
import br.com.sinergico.util.graphql.requests.Login;
import br.com.sinergico.util.graphql.requests.LoginEloResponse;
import br.com.sinergico.util.graphql.requests.QueryServerPublicKey;
import br.com.sinergico.util.graphql.requests.QueryServerPublicKeyResponse;
import br.com.sinergico.util.graphql.requests.SuspendCardToken;
import br.com.sinergico.util.graphql.requests.SuspendCardTokenResponse;
import br.com.sinergico.vo.qrcode.PostQrCodeAuthorizationAndCode;
import br.com.sinergico.vo.qrcode.PostQrCodeCompleteTransaction;
import br.com.sinergico.vo.qrcode.QrCodeAccessTokenRequest;
import br.com.sinergico.vo.qrcode.QrCodeAccessTokenResponse;
import br.com.sinergico.vo.qrcode.QrCodeCallBackRequest;
import br.com.sinergico.vo.qrcode.QrCodeEloSendTransactionResponse;
import br.com.sinergico.vo.qrcode.QrCodeParseResponse;
import br.com.sinergico.vo.qrcode.QrCodePublicKeyResponse;
import br.com.sinergico.vo.qrcode.QrCodeTransactionRequest;
import br.com.sinergico.vo.qrcode.QrCodeTransactionsResponse;
import br.com.sinergico.vo.vcn.CardInformation;
import br.com.sinergico.vo.vcn.CardTokenElo;
import br.com.sinergico.vo.vcn.CardTokenEloSensitive;
import br.com.sinergico.vo.vcn.ChaveCurvasElipticas;
import br.com.sinergico.vo.vcn.MerchantAccountInformation;
import br.com.sinergico.vo.vcn.PostCreateNewVcnTokenAbstract;
import br.com.sinergico.vo.vcn.PostCreateNewVcnTokenV2;
import br.com.sinergico.vo.vcn.PostCreateNewVcnTokenV2Externo;
import br.com.sinergico.vo.vcn.TransactionInformation;
import br.com.sinergico.vo.vcn.UsageConstraints;
import br.com.sinergico.vo.vcn.VcnQuery;
import br.com.sinergico.vo.vcn.WrapperGraphQL;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.google.gson.Gson;
import com.nimbusds.jose.EncryptionMethod;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWEAlgorithm;
import com.nimbusds.jose.JWEHeader;
import com.nimbusds.jose.JWEObject;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.JWSObject;
import com.nimbusds.jose.JWSSigner;
import com.nimbusds.jose.Payload;
import com.nimbusds.jose.crypto.ECDHDecrypter;
import com.nimbusds.jose.crypto.ECDHEncrypter;
import com.nimbusds.jose.crypto.ECDSASigner;
import com.nimbusds.jose.crypto.ECDSAVerifier;
import com.nimbusds.jose.jwk.Curve;
import com.nimbusds.jose.jwk.ECKey;
import com.nimbusds.jose.util.Base64URL;
import com.nimbusds.jwt.SignedJWT;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.net.ssl.SSLContext;
import javax.persistence.EntityManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.bouncycastle.util.encoders.Hex;
import org.jpos.iso.ISODate;
import org.json.JSONArray;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

@Slf4j
@Service
public class ApiEloService {

  private static final String DATE_TIME_FORMAT_ISO_8601 = "yyyy-MM-dd HH:mm:ss";
  private static final String GMT_MINUS_THREE = "-03:00";
  private static final String DEFAULT_TIME_ZONE = "America/Sao_Paulo";
  private static final SimpleDateFormat APPROVAL_TIMESTAMP_DATE_FORMAT =
      new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX");

  public static final String GLOBAL_UNIQUE_IDENTIFIER_ELO = "BR.COM.ELO";

  private static final String CONTENT_HEADER_NAME = "Content-Type";
  private static final String CONTENT_HEADER_VALUE = "application/json";
  private static final String ELO_CLIENT_ID_HEADER_NAME = "client_id";
  private static final String ELO_AUTH_HEADER_NAME = "authorization";
  private static final String ELO_ACCESS_TOKEN_HEADER_NAME = "access_token";
  private static final String GRANT_TYPE_DEFAULT = "client_credentials";

  private static final String ELO_BCRYPT_SALT_PREFIX = "$2a$12$";
  private static final String PUBLIC_KEY_FORMAT = "JWK";
  private static final String KEY_ALGORITHM = "RSA";
  private static final String QRCODE_CYPHER_ALGORITHM = "RSA/ECB/OAEPWITHSHA-256ANDMGF1PADDING";

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
  // Configuracoes QrCode

  @Value("${elo.qrcode.keystore.path}")
  private String QRCODE_ELO_KEYSTORE_PATH;

  @Value("${elo.qrcode.keystore.password}")
  private String QRCODE_ELO_KEYSTORE_PASSWORD;

  @Value("${elo.qrcode.key.password}")
  private String QRCODE_ELO_KEY_PASSWORD;

  @Value("${elo.qrcode.truststore.path}")
  private String QRCODE_ELO_TRUSTSTORE_PATH;

  private static final String QRCODE_ELO_URL_BASE_PROPERTY = "";

  @Value("${elo.qrcode.url}")
  private String QRCODE_ELO_URL_BASE;

  private static final String QRCODE_POSTFIX_ACCESS_TOKEN = "/oauth/access-token";
  private static final String QRCODE_POSTFIX_PUBLIC_KEYS = "/public-keys";
  private static final String QRCODE_POSTFIX_PARSE = "/parse";
  private static final String QRCODE_POSTFIX_HEALTH = "/health";
  private static final String QRCODE_POSTFIX_TRANSACTIONS = "/transactions";
  private static final String QRCODE_POSTFIX_TRANSACTIONS_STATUS = "/transactions/";
  private static final String QRCODE_POSTFIX_CALLBACKS = "/callbacks/transactions/";

  private static final List<Integer> CALLBACK_TIMMINGS_LIST_IN_MILISECONDS =
      Arrays.asList(1000, 1000, 1500, 1500, 2000, 3000, 5000, 7000, 10000);

  private static final Integer SUM_CALLBACK_TIMMINGS_PLUS_MARGIN =
      CALLBACK_TIMMINGS_LIST_IN_MILISECONDS.stream().reduce(0, Integer::sum) + 1000;

  ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
  // Configurações VCN

  @Value("${elo.vcn.url}")
  private String VCN_ELO_URI;

  // Configurações client
  @Value("${elo.vcn.client.id}")
  private String VCN_CLIENT_ID;

  @Value("${elo.vcn.client.auth}")
  private String VCN_CLIENT_AUTH;

  // Configurações do usuario VCN responsável por fazer todas as requisições de Vcn

  @Value("${elo.vcn.user.key.kid}")
  private String VCN_USER_KID;

  @Value("${elo.vcn.user.key.x}")
  private String VCN_USER_X;

  @Value("${elo.vcn.user.key.y}")
  private String VCN_USER_Y;

  @Value("${elo.vcn.user.key.private}")
  private String VCN_USER_PRIVATE_KEY;

  private ECKey VCN_USER_EC_KEY;

  @Value("${elo.vcn.user.username}")
  private String VCN_USER_USERNAME;

  @Value("${elo.vcn.user.password}")
  private String VCN_USER_PASSWORD;

  private static final String VCN_INMAIS_REQUESTOR_ID = "57899800005";

  // FIM configurações VCN
  ////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

  private static final ObjectMapper mapper =
      new ObjectMapper()
          .enable(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS)
          .setDateFormat(new SimpleDateFormat("yyyy-MM-dd"))
          .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
          .configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false)
          .configure(JsonGenerator.Feature.QUOTE_FIELD_NAMES, false)
          .configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true)
          .setSerializationInclusion(JsonInclude.Include.NON_NULL)
          .configure(SerializationFeature.WRITE_NULL_MAP_VALUES, false);

  public static final String QR_CODE_NAO_SUPORTADO =
      "Aconteceu algum erro ao tentar ler informações do QR Code. Para realizar pagamentos solicite a geração do QR Code na função crédito.";
  private static ConcurrentHashMap<Integer, QrCodeAccessToken> mapCacheQrCodeAccessToken =
      new ConcurrentHashMap<>();
  private static final Object accessTokenProduceOrConsumeLock = new Object();
  private static LoginEloResponse cacheLoginVcn;
  private static Date dtExpiracaoCacheLoginVcn;
  private static String cacheVcnChallenge;
  private static QrCodePublicKeyResponse cacheChaveECQrCode;
  private static Date dtExpiracaoCacheChaveECQrCode;

  @Autowired EntityManager entityManager;

  @Autowired CredencialService credencialService;

  @Autowired PessoaService pessoaService;

  @Autowired QrCodeAccessTokenRepository qrCodeAccessTokenRepository;

  @Autowired TransacaoQrCodeEloRepository transacaoQrCodeEloRepository;

  @Autowired VcnEloRepository vcnEloRepository;

  @Autowired ContaPagamentoService contaPagamentoService;

  @Autowired ProdutoInstituicaoRepository produtoInstituicaoRepository;

  @Autowired TravaServicosService travaServicosService;

  @Autowired TravaContasService travaContasService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private RestTemplate restTemplate;

  private RestTemplate restTemplateSslElo;

  @PostConstruct
  public void init() {
    VCN_USER_EC_KEY = getEcKeyByParams(VCN_USER_KID, VCN_USER_X, VCN_USER_Y, VCN_USER_PRIVATE_KEY);

    try {
      SSLContext sslContext =
          SSLContextBuilder.create()
              .loadKeyMaterial(
                  new File(QRCODE_ELO_KEYSTORE_PATH),
                  QRCODE_ELO_KEYSTORE_PASSWORD.toCharArray(),
                  QRCODE_ELO_KEY_PASSWORD.toCharArray())
              .loadTrustMaterial(new File(QRCODE_ELO_TRUSTSTORE_PATH))
              .build();

      HttpClient client = HttpClients.custom().setSSLContext(sslContext).build();

      HttpComponentsClientHttpRequestFactory httpComponentsClientHttpRequestFactory =
          new HttpComponentsClientHttpRequestFactory();
      httpComponentsClientHttpRequestFactory.setHttpClient(client);
      httpComponentsClientHttpRequestFactory.setConnectTimeout(SUM_CALLBACK_TIMMINGS_PLUS_MARGIN);
      httpComponentsClientHttpRequestFactory.setReadTimeout(SUM_CALLBACK_TIMMINGS_PLUS_MARGIN);
      restTemplateSslElo = new RestTemplate(httpComponentsClientHttpRequestFactory);
    } catch (IOException
        | CertificateException
        | NoSuchAlgorithmException
        | UnrecoverableKeyException
        | KeyStoreException
        | KeyManagementException e) {
      log.error("Falha na comunicação certificada mTLS com API da ELO. {}", e.getMessage());
    }
  }

  /**
   * Requisita um loginSalt para geração do challenge a ser usado no Login da API ELO
   *
   * @param username o nome de usuário cadastrado na API ELO
   * @see #generateChallenge(String, String)
   */
  public ResponseEntity<CreateLoginSaltResponse> createLoginSalt(String username) {
    // Montar parâmetros do request
    CreateLoginSalt mutation = new CreateLoginSalt(username);

    // Montar request
    VcnQuery<CreateLoginSalt> vcnQueryCreateLoginSalt = new VcnQuery<>(mutation);
    HttpHeaders headers = getHeadersVCNElo(null);
    HttpEntity<String> requestEntity =
        new HttpEntity<>(vcnQueryCreateLoginSalt.createJsonForRequest(), headers);

    // Enviar request
    log.info("ACESSANDO URI ELO, CREATE SALT: " + VCN_ELO_URI);
    HttpEntity<WrapperGraphQL<DataMutationCreateLoginSalt>> responseEntity =
        restTemplate.exchange(
            VCN_ELO_URI,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<WrapperGraphQL<DataMutationCreateLoginSalt>>() {});

    // Pegar response
    WrapperGraphQL<DataMutationCreateLoginSalt> dataResponse = responseEntity.getBody();
    CreateLoginSaltResponse response;
    if (hasContent(dataResponse)) {
      response = dataResponse.getDataContent().getResponse();
    } else {
      log.error(
          "Erro retornado por API da Elo ({}) -> " + new JSONArray(dataResponse.getErrors()),
          dataResponse.getErrors()[0].getPath()[0]);
      throw new GenericServiceException(
          "Falha de iniciação de login em API externa. Tente novamente mais tarde.");
    }
    return new ResponseEntity<CreateLoginSaltResponse>(response, HttpStatus.OK);
  }

  /**
   * Realiza um login na API ELO, recebendo um novo accessToken e refreshToken
   *
   * @param username o nome de usuário cadastrado na API ELO
   * @param challenge o challenge gerado pelo método {@link #generateChallenge(String, String)}
   * @see #createLoginSalt(String)
   * @see #generateChallenge(String, String)
   */
  public ResponseEntity<LoginEloResponse> loginElo(String username, String challenge) {

    LoginEloResponse responseData;
    if (cacheLoginVcn == null
        || dtExpiracaoCacheLoginVcn == null
        || cacheLoginVcn.getAccessToken() == null
        || dtExpiracaoCacheLoginVcn.before(new Date())) {
      // Montar parâmetros do request
      Login mutation = new Login(generateRandomSHA256(), username, challenge);

      // Montar request
      VcnQuery<Login> vcnQueryLoginElo = new VcnQuery<>(mutation);
      HttpHeaders headers = getHeadersVCNElo(null);
      HttpEntity<String> requestEntity =
          new HttpEntity<>(vcnQueryLoginElo.createJsonForRequest(), headers);

      // Enviar request
      HttpEntity<WrapperGraphQL<DataMutationLoginElo>> responseEntity =
          restTemplate.exchange(
              VCN_ELO_URI,
              HttpMethod.POST,
              requestEntity,
              new ParameterizedTypeReference<WrapperGraphQL<DataMutationLoginElo>>() {});

      // Pegar response
      WrapperGraphQL<DataMutationLoginElo> response = responseEntity.getBody();
      if (hasContent(response)) {
        responseData = response.getDataContent().getResponse();
      } else {
        log.error(
            "Erro retornado por API da Elo ({}) -> " + new JSONArray(response.getErrors()),
            response.getErrors()[0].getPath()[0]);
        throw new GenericServiceException(
            "Falha de iniciação de login em API externa. Tente novamente mais tarde.");
      }
      cacheLoginVcn = responseData;
      dtExpiracaoCacheLoginVcn = DateUtil.aumentarMinutos(new Date(), 10);
    } else {
      responseData = cacheLoginVcn;
    }
    return new ResponseEntity<LoginEloResponse>(responseData, HttpStatus.OK);
  }

  public ResponseEntity<CardTokenElo> createCardTokenVCNUser(
      PostCreateNewVcnTokenV2 postCreateNewVcnTokenV2, SecurityUser user) {

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.VCN_ELO);

    if (postCreateNewVcnTokenV2.getIdCredencial() == null) {
      throw new GenericServiceException(
          "O campo idCredencial é obrigatório quando usado Token Issuer.");
    }

    Credencial credencialBaseParaVcn =
        credencialService.findById(postCreateNewVcnTokenV2.getIdCredencial());
    if (credencialBaseParaVcn == null) {
      throw new GenericServiceException("A credencial informada não foi encontrada.");
    }

    travaContasService.travaContas(credencialBaseParaVcn.getIdConta(), Servicos.VCN_ELO);

    Pessoa pessoa = pessoaService.findById(credencialBaseParaVcn.getIdPessoa());
    if (Objects.isNull(pessoa)) {
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar obter a pessoa relacionada à credencial.");
    }

    if (user.getIdInstituicao() == null
        || !user.getIdInstituicao().equals(pessoa.getIdInstituicao())) {
      throw new GenericServiceException(
          "Não é possível realizar transações QrCode em nome de uma pessoa de outra instituição."
              + "O evento será notificado!");
    }

    return createCardTokenVCN(postCreateNewVcnTokenV2, credencialBaseParaVcn);
  }

  public ResponseEntity<CardTokenElo> createCardTokenVCNPortador(
      PostCreateNewVcnTokenV2 postCreateNewVcnTokenV2, SecurityUserPortador userPortador) {
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.VCN_ELO);

    List<Credencial> credenciaisPortador = credencialService.obterCredenciaisPortador(userPortador);
    if (credenciaisPortador == null || credenciaisPortador.isEmpty()) {
      throw new AccessDeniedException("Portador não possui credenciais.");
    }

    List<Credencial> credenciaisAtivasPortador =
        credenciaisPortador.stream()
            .filter(c -> c.getStatus().equals(1))
            .collect(Collectors.toList());
    if (credenciaisAtivasPortador.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais ativas");
    }

    // Valida se foi passada credencial na requisição. Caso não tenha sido passada, utiliza a ultima
    // credencial ativa do portador
    Credencial credencialBaseParaVcn;
    if (postCreateNewVcnTokenV2.getIdCredencial() != null) {
      // Valida se a credencial passada pertence ao portador. Caso encontre a credencial utiliza o
      // objeto(credencial) já trazido anteriormente do banco de dados.
      credencialBaseParaVcn =
          credenciaisAtivasPortador.stream()
              .filter(c -> c.getIdCredencial().equals(postCreateNewVcnTokenV2.getIdCredencial()))
              .findFirst()
              .orElseThrow(
                  () ->
                      new GenericServiceException(
                          "A credencial passada não foi encontrada para o portador."));
    } else {
      // Ultima credencial ativa
      credencialBaseParaVcn =
          credenciaisAtivasPortador.stream().max(Comparator.comparingInt(Credencial::getCsn)).get();
    }

    travaContasService.travaContas(credencialBaseParaVcn.getIdConta(), Servicos.VCN_ELO);

    return createCardTokenVCN(postCreateNewVcnTokenV2, credencialBaseParaVcn);
  }

  public ResponseEntity<CardTokenElo> createCardTokenForUserBySensitiveV2Externo(
      PostCreateNewVcnTokenV2Externo postCreateNewVcnTokenV2Externo) {

    ContaPagamento conta =
        contaPagamentoService.findById(postCreateNewVcnTokenV2Externo.getIdConta());
    if (!Constantes.ID_PRODUCAO_INSTITUICAO_BITFY.equals(conta.getIdInstituicao())) {
      throw new GenericServiceException("Conta não pertence a instituição Bitfy.");
    }
    travaServicosService.travaServicos(conta.getIdInstituicao(), Servicos.VCN_ELO);

    List<Credencial> credenciaisPortador =
        credencialService.findByIdContaInCredencialConta(
            postCreateNewVcnTokenV2Externo.getIdConta());
    if (credenciaisPortador == null || credenciaisPortador.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais.");
    }

    List<Credencial> credenciaisAtivasPortador =
        credenciaisPortador.stream()
            .filter(c -> c.getStatus().equals(1))
            .collect(Collectors.toList());
    if (credenciaisAtivasPortador.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais ativas");
    }

    // Ultima credencial ativa
    Credencial credencialBaseParaVcn =
        credenciaisAtivasPortador.stream().max(Comparator.comparingInt(Credencial::getCsn)).get();

    travaContasService.travaContas(credencialBaseParaVcn.getIdConta(), Servicos.VCN_ELO);

    return createCardTokenVCN(postCreateNewVcnTokenV2Externo, credencialBaseParaVcn);
  }

  private ResponseEntity<CardTokenElo> createCardTokenVCN(
      PostCreateNewVcnTokenAbstract postCreateNewVcnTokenV2Externo,
      Credencial credencialBaseParaVcn) {

    ProdutoInstituicao produto =
        produtoInstituicaoRepository.findByIdConta(credencialBaseParaVcn.getIdConta());
    if (Objects.isNull(produto)) {
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar obter o produto relacionado à credencial.");
    }

    // Fazer login ELO
    String challenge = generateChallenge(VCN_USER_USERNAME, VCN_USER_PASSWORD);
    ResponseEntity<LoginEloResponse> loginEloResponse = loginElo(VCN_USER_USERNAME, challenge);

    // Tratamento das constraints do cartão
    UsageConstraints usageConstraints =
        postCreateNewVcnTokenV2Externo.getUsageConstraints() != null
            ? getNewUsageConstraintsWithExpiryParsed(
                postCreateNewVcnTokenV2Externo.getUsageConstraints())
            : null;

    // Checa se cartao ja foi utilizado anteriormente na criacao de um VCN.
    VcnElo vcnJaCriadoAnteriormente = vcnEloRepository.findTopByCredencial(credencialBaseParaVcn);

    // Montar parâmetros do request
    String clientMutationId = generateRandomSHA256();
    CreateCardTokenForUserBySensitive mutation;
    if (vcnJaCriadoAnteriormente != null) {
      mutation =
          new CreateCardTokenForUserBySensitive(
              clientMutationId,
              vcnJaCriadoAnteriormente.getCardOriginIdElo(),
              VCN_INMAIS_REQUESTOR_ID,
              usageConstraints);
    } else {
      mutation =
          new CreateCardTokenForUserBySensitive(
              clientMutationId,
              obterSensitive(
                  credencialBaseParaVcn), // Obtem dados sensiveis do cartao e formata conforme
              // campo sensitive da API da Elo caso nao haja
              // vcnJaCriadoAnteriormente
              VCN_INMAIS_REQUESTOR_ID,
              produto.getIdProdInstituicao().toString().equals("280103")
                  ? "281003"
                  : produto.getIdProdInstituicao().toString().equals("280108")
                      ? "281008"
                      : produto.getIdProdInstituicao().toString(),
              usageConstraints);
    }

    // Montar request
    VcnQuery<CreateCardTokenForUserBySensitive> vcnQueryCreateCardTokenForUser =
        new VcnQuery<>(mutation);
    HttpHeaders headers = getHeadersVCNElo(loginEloResponse.getBody().getAccessToken());
    String jsonForRequest = vcnQueryCreateCardTokenForUser.createJsonForRequest();
    HttpEntity<String> requestEntity = new HttpEntity<>(jsonForRequest, headers);

    // Enviar request
    log.info("ACESSANDO URI ELO, CREATE_CARD_TOKEN " + VCN_ELO_URI);
    HttpEntity<WrapperGraphQL<DataMutationCreateCardTokenForUser>> responseEntity =
        restTemplate.exchange(
            VCN_ELO_URI,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<
                WrapperGraphQL<DataMutationCreateCardTokenForUser>>() {});

    // Pegar response
    WrapperGraphQL<DataMutationCreateCardTokenForUser> response = responseEntity.getBody();
    CreateCardTokenForUserResponse responseData;
    CardTokenElo cardTokenElo = new CardTokenElo();
    if (hasContent(response)) {
      responseData = response.getDataContent().getResponse();
      cardTokenElo =
          cardTokenElo.fillCardInformation(responseData.getCardToken(), usageConstraints);
      String cardTokenJSON =
          decriptografarComJSONWebEncryption(
              responseData.getCardToken().getSensitive(), VCN_USER_EC_KEY);
      CardTokenEloSensitive tokenSensitive =
          new Gson().fromJson(cardTokenJSON, CardTokenEloSensitive.class);
      cardTokenElo.setSensitive(tokenSensitive);

      // Salva VCN
      VcnElo vcnElo =
          new VcnElo(
              credencialBaseParaVcn,
              cardTokenElo.getId(),
              cardTokenElo.getCardOrigin().getId(),
              LocalDateTime.now(),
              Integer.valueOf(
                  cardTokenElo
                      .getSensitive()
                      .getPan()
                      .substring(cardTokenElo.getSensitive().getPan().length() - 4)),
              Integer.valueOf(cardTokenElo.getSensitive().getPan().substring(0, 6)),
              Long.valueOf(cardTokenElo.getSensitive().getPan().substring(0, 8)),
              YearMonth.of(cardTokenElo.getExpiry().getYear(), cardTokenElo.getExpiry().getMonth())
                  .atEndOfMonth(),
              VcnEloStatusEnum.ATIVO.getStatus(),
              UtilController.encodeSenhaSHA512(cardTokenElo.getSensitive().getPan()));
      vcnEloRepository.save(vcnElo);

    } else {
      log.error(
          "IdCredencialBaseParaVcn = {}\n"
              + "Request enviada para API de geração de VCN da Elo {}\n"
              + "Erro retornado por API da Elo ({}) -> {}",
          credencialBaseParaVcn.getIdCredencial(),
          requestEntity.getBody(),
          response.getErrors()[0].getPath()[0],
          new JSONArray(response.getErrors()));
      throw new GenericServiceException(
          "Falha de criação do cartão VCN. Tente novamente mais tarde.");
    }
    return new ResponseEntity<>(cardTokenElo, HttpStatus.OK);
  }

  /**
   * Foi informado que o ciclo de vida completo dos cartões tokenizados não precisava ser
   * implementado. Como já havia começado a implementação da suspensão de token, acabei deixando
   * aqui pra facilitar no futuro, caso mudem de ideia e precisem da funcionalidade.
   */
  public CardTokenElo suspendCardToken(String userId, String cardTokenId) {

    // Montar parâmetros do request
    SuspendCardToken mutation = new SuspendCardToken(cardTokenId, true, CardSuspendReason.ISSUER);

    // Montar request
    VcnQuery<SuspendCardToken> query = new VcnQuery<>(mutation);
    HttpHeaders headers = getHeadersVCNElo(userId);
    HttpEntity<String> requestEntity = new HttpEntity<>(query.createJsonForRequest(), headers);

    // Enviar request
    HttpEntity<WrapperGraphQL<DataMutationSuspendCardToken>> responseEntity =
        restTemplate.exchange(
            VCN_ELO_URI,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<WrapperGraphQL<DataMutationSuspendCardToken>>() {});

    // Pegar response
    WrapperGraphQL<DataMutationSuspendCardToken> dataResponse = responseEntity.getBody();
    SuspendCardTokenResponse response;
    CardTokenElo cardTokenElo = null;
    if (hasContent(dataResponse)) {
      response = dataResponse.getDataContent().getResponse();
      cardTokenElo = new CardTokenElo(response.getCardToken());
      // Desativar token no banco
    }
    return cardTokenElo;
  }

  /********************
   * QUERIES GRAPH QL *
   ********************/

  public ChaveCurvasElipticas queryServerPublicKey() {

    // Montar parâmetros do request
    QueryServerPublicKey queryServerPublicKey = new QueryServerPublicKey();

    // Montar request
    VcnQuery<QueryServerPublicKey> vcnQueryServerPublicKey =
        new VcnQuery<QueryServerPublicKey>(queryServerPublicKey);
    HttpHeaders headers = getHeadersVCNElo(null);
    HttpEntity<String> requestEntity =
        new HttpEntity<>(vcnQueryServerPublicKey.createJsonForRequest(), headers);

    // Enviar Request
    log.info("ACESSANDO URI ELO, QUERY PUBLIC KEY " + VCN_ELO_URI);
    HttpEntity<WrapperGraphQL<DataQueryServerPublicKey>> responseEntity =
        restTemplate.exchange(
            VCN_ELO_URI,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<WrapperGraphQL<DataQueryServerPublicKey>>() {});

    WrapperGraphQL<DataQueryServerPublicKey> responseBody = responseEntity.getBody();
    QueryServerPublicKeyResponse responseData;
    if (hasContent(responseBody)) {
      responseData = responseBody.getDataContent().getResponse();
    } else {
      log.error(
          "Erro retornado por API da Elo ({}) -> " + new JSONArray(responseBody.getErrors()),
          responseBody.getErrors()[0].getPath()[0]);
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
    }

    // Montar objeto de resposta
    Gson gson = new Gson();
    ChaveCurvasElipticas chaveEC = gson.fromJson(responseData.getKey(), ChaveCurvasElipticas.class);
    return chaveEC;
  }

  /**********************
   * 	API QR CODE 	  *
   **********************/
  public QrCodeAccessToken gerarTokenAcessoApiQrCode(Integer idInstituicao) {

    // Montar parâmetros do request
    QrCodeAccessTokenRequest paramsForRequest = new QrCodeAccessTokenRequest(GRANT_TYPE_DEFAULT);

    QrCodeAccessToken qrCodeAccessTokenFromMap = mapCacheQrCodeAccessToken.get(idInstituicao);

    synchronized (accessTokenProduceOrConsumeLock) {
      // Checa se o acessToken obtido no mapCacheQrCodeAccessToken ainda é válido
      if (qrCodeAccessTokenFromMap != null
          && qrCodeAccessTokenFromMap.getDtHrExpiracao() != null
          && qrCodeAccessTokenFromMap.getAccessToken() != null
          && qrCodeAccessTokenFromMap.getDtHrExpiracao().after(new Date())) {
        return qrCodeAccessTokenFromMap;
      } else { // Caso o accessToken não seja válido. Buscar a informação no banco de dados.

        QrCodeAccessToken qrCodeAccessTokenFromDB =
            qrCodeAccessTokenRepository.findByIdProcessadoraAndIdInstituicao(
                Constantes.ID_PROCESSADORA_ITS_PAY, idInstituicao);
        if (Objects.isNull(qrCodeAccessTokenFromDB)) {
          log.error("ClientId Qrcode não configurado para a instituicao " + idInstituicao);
          throw new GenericServiceException(
              "Erro no servidor. Tente novamente mais tarde.",
              "ClientId Qrcode não configurado para a instituicao " + idInstituicao);
        }

        // Se o accessToken obtido no banco de dados não for válido. Chamar API de obtenção de de
        // accessToken da ELO
        if (Objects.isNull(qrCodeAccessTokenFromDB.getAccessToken())
            || Objects.isNull(qrCodeAccessTokenFromDB.getDtHrExpiracao())
            || qrCodeAccessTokenFromDB.getDtHrExpiracao().before(new Date())) {

          // Montar request
          HttpHeaders headers =
              getHeadersQrCodeFull(
                  qrCodeAccessTokenFromDB.getClientId(), qrCodeAccessTokenFromDB.getAuthToken());
          HttpEntity<String> requestEntity =
              new HttpEntity<>(getBodyForRequest(paramsForRequest), headers);

          // Enviar Request
          HttpEntity<QrCodeAccessTokenResponse> responseEntity =
              restExchangeWithException(
                  restTemplateSslElo,
                  QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_ACCESS_TOKEN,
                  HttpMethod.POST,
                  requestEntity,
                  new ParameterizedTypeReference<QrCodeAccessTokenResponse>() {});

          QrCodeAccessToken qrCodeAccessTokenNovo =
              new QrCodeAccessToken(
                  qrCodeAccessTokenFromDB.getId(),
                  responseEntity.getBody().getAccessToken(), // Valor atualizado de accessToken
                  DateUtil.aumentarSegundos(
                      new Date(),
                      responseEntity
                          .getBody()
                          .getSecondsToExpire()), // Valor atualizado de dtHrExpiracao
                  qrCodeAccessTokenFromDB.getIdProcessadora(),
                  qrCodeAccessTokenFromDB.getIdInstituicao(),
                  qrCodeAccessTokenFromDB.getClientId(),
                  qrCodeAccessTokenFromDB.getAuthToken());
          mapCacheQrCodeAccessToken.put(idInstituicao, qrCodeAccessTokenNovo);
          qrCodeAccessTokenRepository.saveAndFlush(qrCodeAccessTokenNovo);
          entityManager.detach(qrCodeAccessTokenNovo);

          return qrCodeAccessTokenNovo;
        } else { // O accessToken do banco de dados é válido. Usá-lo!
          mapCacheQrCodeAccessToken.put(idInstituicao, qrCodeAccessTokenFromDB);
          entityManager.detach(qrCodeAccessTokenFromDB);
          return qrCodeAccessTokenFromDB;
        }
      }
    }
  }

  public QrCodePublicKeyResponse getQrCodePublicKey(String authorization) {

    QrCodePublicKeyResponse response;
    if (cacheChaveECQrCode == null
        || cacheChaveECQrCode.getKeyData() == null
        || dtExpiracaoCacheChaveECQrCode.before(new Date())) {

      // Montar request
      HttpHeaders headers = getHeadersQrCodeAuthorization(authorization);
      HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

      // Enviar Request
      HttpEntity<QrCodePublicKeyResponse> responseEntity =
          restExchangeWithException(
              restTemplateSslElo,
              QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_PUBLIC_KEYS,
              HttpMethod.GET,
              requestEntity,
              new ParameterizedTypeReference<QrCodePublicKeyResponse>() {});

      // Montar objeto de resposta
      response = responseEntity.getBody();

      cacheChaveECQrCode = response;
      dtExpiracaoCacheChaveECQrCode =
          DateUtil.aumentarSegundos(new Date(), cacheChaveECQrCode.getKeyExpiration());
    } else {
      response = cacheChaveECQrCode;
    }

    return response;
  }

  public QrCodeParseResponse getQrCodeParsedWithExternalAccessCode(
      String accessToken, String qrCodeRecebido) {

    // Montar request
    HttpHeaders headers = getHeadersQrCodeAuthorization(accessToken);
    // Não consegui fazer o método de calcular CRC funcionar para todas as QrCode que ELO me passou.
    // Usarei o campo CRC da API da ELO para validar.
    // String crcCalculado = getCRC16_CCITT(qrCodeRecebido.substring(0,qrCodeRecebido.length() -
    // 4));
    String crcOriginal = qrCodeRecebido.substring(qrCodeRecebido.length() - 4);

    String body = "{\"qrCode\":\"" + qrCodeRecebido.replace("\"", "\\\"") + "\"}";
    HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);

    // Enviar Request
    HttpEntity<QrCodeParseResponse> responseEntity =
        restExchangeWithException(
            restTemplateSslElo,
            QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_PARSE,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<QrCodeParseResponse>() {});

    if (responseEntity.getBody() != null
        && responseEntity.getBody().getCrc() != null
        && !crcOriginal.equalsIgnoreCase(responseEntity.getBody().getCrc())) {
      log.error("CRC calculado não coincide com CRC existente no QrCode.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
    }

    // Montar objeto de resposta
    return responseEntity.getBody();
  }

  public QrCodeParseResponse getQrCodeParsedPortador(
      PostQrCodeAuthorizationAndCode postQrCodeAuthorizationAndCode,
      SecurityUserPortador userPortador) {
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.QRCODE_ELO);

    userPortador
        .getContasPortador()
        .forEach(idConta -> travaContasService.travaContas(idConta, Servicos.QRCODE_ELO));
    QrCodeAccessToken qrCodeAccessToken =
        gerarTokenAcessoApiQrCode(userPortador.getIdInstituicao());
    // TODO: REMOVER ESTA TRATATIVA ESPECÍFICA PARA ESCOTEIROS DO BRASIL E LEVAR PARA NÍVEL MAIS
    // PROFUNDO, DEIXANDO O RETORNO
    // MAIS ADEQUADO PARA CADA CASO DE RESPOSTA DA ELO (NECESSITA DOCUMENTAÇÃO).
    try {
      return getQrCodeParsed(postQrCodeAuthorizationAndCode.getQrCode(), qrCodeAccessToken);
    } catch (GenericServiceException e) {
      if (Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(userPortador.getIdInstituicao())
          && e.getDetalhes().contains("QR Code not supported")) {
        throw new GenericServiceException(QR_CODE_NAO_SUPORTADO, HttpStatus.UNPROCESSABLE_ENTITY);
      }
      throw e;
    }
  }

  public QrCodeParseResponse getQrCodeParsedCorporativo(
      PostQrCodeAuthorizationAndCode postQrCodeAuthorizationAndCode,
      SecurityUserCorporativo userCorporativo) {
    travaServicosService.travaServicos(userCorporativo.getIdInstituicao(), Servicos.QRCODE_ELO);

    userCorporativo
        .getContasPortador()
        .forEach(idConta -> travaContasService.travaContas(idConta, Servicos.QRCODE_ELO));

    QrCodeAccessToken qrCodeAccessToken =
        gerarTokenAcessoApiQrCode(userCorporativo.getIdInstituicao());
    return getQrCodeParsed(postQrCodeAuthorizationAndCode.getQrCode(), qrCodeAccessToken);
  }

  public QrCodeParseResponse getQrCodeParsedUsuarioIssuer(
      PostQrCodeAuthorizationAndCode postQrCodeAuthorizationAndCode, SecurityUser user) {
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.QRCODE_ELO);

    if (user.getIdInstituicao() == null) {
      throw new GenericServiceException(
          "Apenas usuários que possuem idInstituicao podem requisitar QrCode Elo.");
    }
    QrCodeAccessToken qrCodeAccessToken = gerarTokenAcessoApiQrCode(user.getIdInstituicao());
    return getQrCodeParsed(postQrCodeAuthorizationAndCode.getQrCode(), qrCodeAccessToken);
  }

  // Pretendemos tornar legado esse código. Substituí-lo pelo getQrCodeParsedUsuarioIssuer
  public QrCodeParseResponse getQrCodeParsedExterno(
      PostQrCodeAuthorizationAndCode postQrCodeAuthorizationAndCode) {

    QrCodeAccessToken qrCodeAccessToken =
        gerarTokenAcessoApiQrCode(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS);
    return getQrCodeParsed(postQrCodeAuthorizationAndCode.getQrCode(), qrCodeAccessToken);
  }

  private QrCodeParseResponse getQrCodeParsed(
      String qrCodeRecebido, QrCodeAccessToken qrCodeAccessToken) {

    if (qrCodeAccessToken == null || qrCodeAccessToken.getAccessToken() == null) {
      throw new GenericServiceException(
          "Ocorreu um erro na obtenção de token de acesso em API externa. Tente novamente mais tarde.");
    }
    String bearerAccessToken = "Bearer " + qrCodeAccessToken.getAccessToken();

    // Montar request
    HttpHeaders headers = getHeadersQrCodeAuthorization(bearerAccessToken);
    String body = "{\"qrCode\":\"" + qrCodeRecebido.replace("\"", "\\\"") + "\"}";
    HttpEntity<String> requestEntity = new HttpEntity<>(body, headers);

    // Enviar Request
    HttpEntity<QrCodeParseResponse> responseEntity =
        restExchangeWithException(
            restTemplateSslElo,
            QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_PARSE,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<QrCodeParseResponse>() {});

    // Não consegui fazer o método de calcular CRC funcionar para todas as QrCode que ELO me passou.
    // Usarei o campo CRC da API da ELO para validar.
    // String crcCalculado = getCRC16_CCITT(qrCodeRecebido.substring(0,qrCodeRecebido.length() -
    // 4));
    String crcOriginal = qrCodeRecebido.substring(qrCodeRecebido.length() - 4);
    if (responseEntity.getBody() != null
        && responseEntity.getBody().getCrc() != null
        && !crcOriginal.equalsIgnoreCase(responseEntity.getBody().getCrc())) {
      log.error("CRC calculado não coincide com CRC existente no QrCode.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
    }

    // Montar objeto de resposta
    return responseEntity.getBody();
  }

  // TODO Arrumar essa API para se adequar aos criterios de seguranca e modularidade
  public String getStatusApiEloQrCode(String authorization) {

    // Montar request
    HttpHeaders headers = getHeadersQrCodeAuthorization(authorization);
    HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

    // Enviar Request
    HttpEntity<String> responseEntity =
        restExchangeWithException(
            restTemplateSslElo,
            QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_HEALTH,
            HttpMethod.GET,
            requestEntity,
            new ParameterizedTypeReference<String>() {});

    // Montar objeto de resposta
    return responseEntity.getBody();
  }

  public ResponseEntity<QrCodeEloSendTransactionResponse>
      enviarTransacaoQrCodeCompletaParaProcessamentoPelaEloPortador(
          PostQrCodeCompleteTransaction postQrCodeCompleteTransaction,
          SecurityUserPortador userPortador) {

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.QRCODE_ELO);

    List<Credencial> credenciaisPortador = credencialService.obterCredenciaisPortador(userPortador);
    if (credenciaisPortador == null || credenciaisPortador.isEmpty()) {
      throw new AccessDeniedException("Portador não possui credenciais.");
    }

    List<Credencial> credenciaisAtivasPortador =
        credenciaisPortador.stream()
            .filter(c -> c.getStatus().equals(1))
            .collect(Collectors.toList());
    if (credenciaisAtivasPortador.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais ativas");
    }

    // Valida se foi passada credencial na requisição. Caso não tenha sido passada, utiliza a ultima
    // credencial ativa do portador
    Credencial credencialBaseParaQrCode;
    // TODO: REMOVER VALIDAÇÃO CONDICIONAL DA INSTITUIÇÃO ESCOTEIROS DO BRASIL POR SER UMA TRATATIVA
    // TEMPORÁRIA.
    if (!Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(userPortador.getIdInstituicao())
        && postQrCodeCompleteTransaction.getIdCredencial() != null) {
      // Valida se a credencial passada pertence ao portador. Caso encontre a credencial utiliza o
      // objeto(credencial) já trazido anteriormente do banco de dados.
      credencialBaseParaQrCode =
          credenciaisAtivasPortador.stream()
              .filter(
                  c -> c.getIdCredencial().equals(postQrCodeCompleteTransaction.getIdCredencial()))
              .findFirst()
              .orElseThrow(
                  () ->
                      new GenericServiceException(
                          "A credencial não foi encontrada para o portador."));
    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(
        userPortador.getIdInstituicao())) {
      // Ultima credencial ativa comparando a Pessoa da credencial com o Portador logado
      Optional<Credencial> credencialOptional =
          credenciaisAtivasPortador.stream()
              .filter(cred -> userPortador.getCpf().equals(cred.getPessoa().getDocumento()))
              .max(Comparator.comparingInt(Credencial::getCsn));

      credencialBaseParaQrCode =
          credencialOptional.orElseThrow(
              () ->
                  new GenericServiceException("A credencial não foi encontrada para o portador."));
    } else {
      // Ultima credencial ativa
      credencialBaseParaQrCode =
          credenciaisAtivasPortador.stream().max(Comparator.comparingInt(Credencial::getCsn)).get();
    }

    travaContasService.travaContas(credencialBaseParaQrCode.getIdConta(), Servicos.QRCODE_ELO);

    ContaPagamento conta =
        contaPagamentoService.findByIdNotNull(credencialBaseParaQrCode.getIdConta());
    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.QRCODE_ELO);

    Pessoa pessoa = pessoaService.findById(credencialBaseParaQrCode.getIdPessoa());
    if (Objects.isNull(pessoa)) {
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar obter a pessoa relacionada à credencial.");
    }

    return enviarTransacaoQrCodeCompletaParaProcessamento(
        postQrCodeCompleteTransaction, credencialBaseParaQrCode, pessoa);
  }

  public ResponseEntity<QrCodeEloSendTransactionResponse>
      enviarTransacaoQrCodeCompletaParaProcessamentoPelaEloUsuarioIssuer(
          PostQrCodeCompleteTransaction postQrCodeCompleteTransaction, SecurityUser user) {

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.QRCODE_ELO);

    if (postQrCodeCompleteTransaction.getIdCredencial() == null) {
      throw new GenericServiceException(
          "O campo idCredencial é obrigatório quando usado Token Issuer.");
    }

    Credencial credencialBaseParaQrCode =
        credencialService.findById(postQrCodeCompleteTransaction.getIdCredencial());
    if (credencialBaseParaQrCode == null) {
      throw new GenericServiceException("A credencial informada não foi encontrada.");
    }

    travaContasService.travaContas(credencialBaseParaQrCode.getIdConta(), Servicos.QRCODE_ELO);

    Pessoa pessoa = pessoaService.findById(credencialBaseParaQrCode.getIdPessoa());
    if (Objects.isNull(pessoa)) {
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar obter a pessoa relacionada à credencial.");
    }

    if (user.getIdInstituicao() == null
        || !user.getIdInstituicao().equals(pessoa.getIdInstituicao())) {
      throw new GenericServiceException(
          "Não é possível realizar transações QrCode em nome de uma pessoa de outra instituição."
              + "O evento será notificado!");
    }

    return enviarTransacaoQrCodeCompletaParaProcessamento(
        postQrCodeCompleteTransaction, credencialBaseParaQrCode, pessoa);
  }

  public ResponseEntity<QrCodeEloSendTransactionResponse>
      enviarTransacaoQrCodeCompletaParaProcessamentoEloCorporativo(
          PostQrCodeCompleteTransaction postQrCodeCompleteTransaction,
          SecurityUserCorporativo userCorporativo) {

    travaServicosService.travaServicos(userCorporativo.getIdInstituicao(), Servicos.QRCODE_ELO);

    List<Credencial> credencials = credencialService.obterCredenciaisCorporativo(userCorporativo);
    if (credencials == null || credencials.isEmpty()) {
      throw new AccessDeniedException("Portador não possui credenciais.");
    }

    List<Credencial> credenciaisAtivas =
        credencials.stream().filter(c -> c.getStatus().equals(1)).collect(Collectors.toList());
    if (credenciaisAtivas.isEmpty()) {
      throw new GenericServiceException("Portador não possui credenciais ativas");
    }

    // Valida se foi passada credencial na requisição. Caso não tenha sido passada, utiliza a ultima
    // credencial ativa do portador
    Credencial credencialBaseParaQrCode;
    if (!Constantes.ID_PRODUCAO_INSTITUICAO_ESCOTEIROS.equals(userCorporativo.getIdInstituicao())
        && postQrCodeCompleteTransaction.getIdCredencial() != null) {
      // Valida se a credencial passada pertence ao portador. Caso encontre a credencial utiliza o
      // objeto(credencial) já trazido anteriormente do banco de dados.
      credencialBaseParaQrCode =
          credenciaisAtivas.stream()
              .filter(
                  c -> c.getIdCredencial().equals(postQrCodeCompleteTransaction.getIdCredencial()))
              .findFirst()
              .orElseThrow(
                  () ->
                      new GenericServiceException(
                          "A credencial não foi encontrada para o portador."));
    } else {
      // Ultima credencial ativa
      credencialBaseParaQrCode =
          credenciaisAtivas.stream().max(Comparator.comparingInt(Credencial::getCsn)).get();
    }

    travaContasService.travaContas(credencialBaseParaQrCode.getIdConta(), Servicos.QRCODE_ELO);

    ContaPagamento conta =
        contaPagamentoService.findByIdNotNull(credencialBaseParaQrCode.getIdConta());
    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(conta, Servicos.QRCODE_ELO);

    Pessoa pessoa = pessoaService.findById(credencialBaseParaQrCode.getIdPessoa());
    if (Objects.isNull(pessoa)) {
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar obter a pessoa relacionada à credencial.");
    }

    return enviarTransacaoQrCodeCompletaParaProcessamento(
        postQrCodeCompleteTransaction, credencialBaseParaQrCode, pessoa);
  }

  private ResponseEntity<QrCodeEloSendTransactionResponse>
      enviarTransacaoQrCodeCompletaParaProcessamento(
          PostQrCodeCompleteTransaction postQrCodeCompleteTransaction,
          Credencial credencialBaseParaQrCode,
          Pessoa pessoa) {

    CardInformation cardInformation = getCardInformation(credencialBaseParaQrCode);
    if (cardInformation == null) {
      throw new GenericServiceException("Ocorreu um erro ao tentar obter dados do cartão.");
    }

    QrCodeAccessToken qrCodeAccessToken = gerarTokenAcessoApiQrCode(pessoa.getIdInstituicao());
    if (qrCodeAccessToken == null || qrCodeAccessToken.getAccessToken() == null) {
      throw new GenericServiceException(
          "Ocorreu um erro na obtenção de token de acesso em API externa. Tente novamente mais tarde.");
    }
    String bearerAccessToken = "Bearer " + qrCodeAccessToken.getAccessToken();

    QrCodeParseResponse qrCodeparsed =
        getQrCodeParsedWithExternalAccessCode(
            bearerAccessToken,
            postQrCodeCompleteTransaction
                .getQrCode()); // Passo opcional. Contem as informacoes da transação.
    if (qrCodeparsed == null
        || qrCodeparsed.getTransactionInformationList() == null
        || qrCodeparsed.getTransactionInformationList().isEmpty()) {
      log.error("Ocorreu um erro na obtenção dos dados provenientes do QrCode por API da ELO.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
    }

    // Carrega as informacoes da requisicao /parse da Elo, entre outras informacoes, no Objeto
    // transacaoQrCodeElo
    TransacaoQrCodeElo transacaoQrCodeElo =
        loadTransacaoQrCodeEloInfo(
            postQrCodeCompleteTransaction, credencialBaseParaQrCode, qrCodeparsed);

    // cifrar os dados sensíveis do cartão de crédito
    String dadosCartaoAsString =
        getDadosSensiveisCartaoAsStringParaQrCode(
            cardInformation.getPan(),
            StringUtils.leftPad(cardInformation.getMesValidade().toString(), 2, '0'),
            cardInformation.getAnoValidade().toString(),
            credencialBaseParaQrCode.getNomeImpresso(),
            cardInformation.getCsc(),
            pessoa.getDocumento());
    QrCodePublicKeyResponse publicKeyResponse = getQrCodePublicKey(bearerAccessToken);
    if (publicKeyResponse.getKeyData() == null || publicKeyResponse.getKeyData().isEmpty()) {
      log.error("Ocorreu um erro na obtenção de chave publica em API QrCode ELO.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
    }
    String cipheredInformation =
        getCipheredInformation(dadosCartaoAsString, publicKeyResponse.getKeyData());

    // Montar request
    QrCodeTransactionRequest paramsForRequest = new QrCodeTransactionRequest();
    paramsForRequest.setQrCode(
        postQrCodeCompleteTransaction
            .getQrCode() /*.replace("\"","\\\"")*/); // Nao é preciso escapar o QrCode antes de
    // enviar, pois o JSONmapper realiza a operação
    paramsForRequest.setCipheredInformation(cipheredInformation);
    // HttpHeaders headers = getHeadersQrCodeAuthorization(postParam.getAuthorization());
    HttpHeaders headers =
        getHeadersQrCodeEnviarTransacao(qrCodeAccessToken.getClientId(), bearerAccessToken);
    HttpEntity<String> requestEntity =
        new HttpEntity<>(getBodyForRequest(paramsForRequest), headers);

    // Enviar Request
    transacaoQrCodeElo.setDtHrEnvio(new Date());
    HttpEntity<QrCodeTransactionsResponse> responseEntity =
        restExchangeWithException(
            restTemplateSslElo,
            QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_TRANSACTIONS,
            HttpMethod.POST,
            requestEntity,
            new ParameterizedTypeReference<QrCodeTransactionsResponse>() {});

    long timeOfEloAPIResponse = System.currentTimeMillis();
    if (responseEntity.getHeaders().get("transactionid") != null) {
      // Persistir transacaoQrCodeElo no DB
      transacaoQrCodeElo.setTransactionId(responseEntity.getHeaders().get("transactionid").get(0));
      transacaoQrCodeElo.setDtHrInclusao(new Date());
      transacaoQrCodeElo.setCallbackStatus(
          QrCodeEloCallbackStatus.CALLBACK_STATUS_INICIAL.getStatus());
      transacaoQrCodeEloRepository.saveAndFlush(transacaoQrCodeElo);
      Long idTransacaoQrElo = transacaoQrCodeElo.getId();
      entityManager.detach(transacaoQrCodeElo);

      // Checar multiplas vezes se a chamada de Callback vinda da ELO alterou o status da transacao
      // no DB
      try {
        TransacaoQrCodeElo transacaoQrCodeEloAfterTime = null;
        long timeBeforeLastDBFetch = 0L;
        boolean firstTimming = true;
        for (Integer timming : CALLBACK_TIMMINGS_LIST_IN_MILISECONDS) {

          Thread.sleep(
              firstTimming
                  ? Math.max(timming - (System.currentTimeMillis() - timeOfEloAPIResponse), 0)
                  : Math.max(timming - (System.currentTimeMillis() - timeBeforeLastDBFetch), 0));

          timeBeforeLastDBFetch = System.currentTimeMillis();
          transacaoQrCodeEloAfterTime =
              transacaoQrCodeEloRepository.findById(idTransacaoQrElo).orElse(null);
          entityManager.detach(transacaoQrCodeEloAfterTime);

          if (isCallbackSuccess(transacaoQrCodeEloAfterTime)) {
            return markAndReturnTransactionSuccess(
                transacaoQrCodeEloAfterTime, credencialBaseParaQrCode);
          } else if (isCallbackFail(transacaoQrCodeEloAfterTime)) {
            return markAndReturnTransactionFail(transacaoQrCodeEloAfterTime);
          }

          firstTimming = false;
        }

        return markAndReturnTransactionTimeout(timeOfEloAPIResponse, transacaoQrCodeEloAfterTime);

      } catch (InterruptedException e) {
        e.printStackTrace();
        throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
      }
    }

    // A API transactions da Elo retornou um erro.
    log.error(
        "Retorno da API Elo /transactions: Headers: {} ; Body: {}",
        responseEntity.getHeaders().toString(),
        responseEntity.getBody().toString());
    throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
  }

  public String buscarStatusTransacaoQrCode(String authorization, String transactionId) {

    // Montar request
    HttpHeaders headers = getHeadersQrCodeAuthorization(authorization);
    HttpEntity<String> requestEntity = new HttpEntity<>(null, headers);

    // Enviar Request
    HttpEntity<String> responseEntity =
        restTemplate.exchange(
            QRCODE_ELO_URL_BASE + QRCODE_POSTFIX_TRANSACTIONS_STATUS + transactionId,
            HttpMethod.GET,
            requestEntity,
            new ParameterizedTypeReference<String>() {});

    // Montar objeto de resposta
    return responseEntity.getBody();
  }

  public ResponseEntity getCallbackByTransactionId(
      String transactionId, QrCodeCallBackRequest qrCodeCallBackRequest) {

    TransacaoQrCodeElo transacaoQrCodeElo =
        transacaoQrCodeEloRepository.findByTransactionId(transactionId);

    if (transacaoQrCodeElo == null) {
      log.error("Transação Qrcode Elo" + transactionId + " não encontrada.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.");
    }

    if (transacaoQrCodeElo
            .getCallbackStatus()
            .equals(QrCodeEloCallbackStatus.CALLBACK_STATUS_INICIAL.getStatus())
        || transacaoQrCodeElo
            .getCallbackStatus()
            .equals(QrCodeEloCallbackStatus.CALLBACK_STATUS_TIMEOUT.getStatus())) {
      saveCallbackInfoSuccessOrFailed(
          qrCodeCallBackRequest, transacaoQrCodeElo, transacaoQrCodeElo.getCallbackStatus());
    } else {
      throw new GenericServiceException("Callback já processado anteriormente.");
    }

    return ResponseEntity.ok("");
  }

  /**********************
   * MÉTODOS DE APOIO *
   **********************/

  private TransacaoQrCodeElo loadTransacaoQrCodeEloInfo(
      PostQrCodeCompleteTransaction postQrCodeCompleteTransaction,
      Credencial credencialBaseParaQrCode,
      QrCodeParseResponse qrCodeparsed) {
    TransacaoQrCodeElo transacaoQrCodeEloCommonInfo =
        loadTransacaoQrCodeCommonInfo(
            postQrCodeCompleteTransaction, credencialBaseParaQrCode, qrCodeparsed);
    return loadTransacaoQrCodeEloTransactionInfo(qrCodeparsed, transacaoQrCodeEloCommonInfo);
  }

  private TransacaoQrCodeElo loadTransacaoQrCodeEloTransactionInfo(
      QrCodeParseResponse qrCodeparsed, TransacaoQrCodeElo transacaoQrCodeEloCommonInfo) {

    TransacaoQrCodeElo transacaoQrCodeElo = new TransacaoQrCodeElo();
    BeanUtils.copyProperties(
        transacaoQrCodeEloCommonInfo,
        transacaoQrCodeElo,
        UtilController.getNullPropertyNames(transacaoQrCodeEloCommonInfo));

    if (qrCodeparsed.getMerchantAccountInformationList() != null
        && !qrCodeparsed.getMerchantAccountInformationList().isEmpty()) {
      // Pela estrutura do Json passado pela ELO poderiam haver mais de um
      // merchantAccountInformation, pois eles passam uma Lista de dados no Json.
      // Como as informações são conflitantes será implementado prevendo apenas um
      // merchantAccountInformation
      // Dar prioridade para dados que chegam no formato GLOBAL_UNIQUE_IDENTIFIER_ELO. Acredito que
      // esse seja o motivo
      //     para ser enviada uma lista no lugar de somente um elemento
      MerchantAccountInformation merchantAccountInformation =
          qrCodeparsed.getMerchantAccountInformationList().stream()
              .filter(
                  x -> x.getGlobalUniqueIdentifier().equalsIgnoreCase(GLOBAL_UNIQUE_IDENTIFIER_ELO))
              .findAny()
              .orElse(qrCodeparsed.getMerchantAccountInformationList().get(0));
      transacaoQrCodeElo.setAcquirerId(merchantAccountInformation.getAcquirerId());
      transacaoQrCodeElo.setMerchantAdditionalDataInformation(
          merchantAccountInformation.getAdditionalInformation());
      transacaoQrCodeElo.setGlobalUniqueIdentifier(
          merchantAccountInformation.getGlobalUniqueIdentifier());
      transacaoQrCodeElo.setLogicNumber(merchantAccountInformation.getLogicNumber());
      transacaoQrCodeElo.setMerchantAccountInfo(
          merchantAccountInformation.getMerchantAccountInformation());
      transacaoQrCodeElo.setSubAcquirerId(merchantAccountInformation.getSubAcquirerId());
      transacaoQrCodeElo.setSubAcquirerMerchantAccountInformation(
          merchantAccountInformation.getSubAcquirerMerchantAccountInformation());
      transacaoQrCodeElo.setTerminalDeviceType(merchantAccountInformation.getTerminalDeviceType());
      transacaoQrCodeElo.setTerminalId(merchantAccountInformation.getTerminalId());
    }

    // Pela estrutura do Json passado pela ELO poderiam haver mais de uma transacao, pois eles
    // passam uma Lista de dados no Json.
    // Porém há apenas um dado de valor. Como as informações são conflitantes será implementado
    // prevendo apenas uma transação
    // Dar prioridade para dados que chegam no formato GLOBAL_UNIQUE_IDENTIFIER_ELO. Acredito que
    // esse seja o motivo
    //     para ser enviada uma lista no lugar de somente um elemento
    TransactionInformation transactionInformation =
        qrCodeparsed.getTransactionInformationList().stream()
            .filter(
                x -> x.getGlobalUniqueIdentifier().equalsIgnoreCase(GLOBAL_UNIQUE_IDENTIFIER_ELO))
            .findAny()
            .orElse(qrCodeparsed.getTransactionInformationList().get(0));

    transacaoQrCodeElo.setGlobalUniqueIdentifier(
        transactionInformation.getGlobalUniqueIdentifier() != null
            ? transactionInformation.getGlobalUniqueIdentifier()
            : transacaoQrCodeElo.getGlobalUniqueIdentifier());
    transacaoQrCodeElo.setMainProduct(transactionInformation.getMainProduct());
    transacaoQrCodeElo.setSubProduct(transactionInformation.getSubProduct());
    if (transactionInformation.getOriginalTransactionDate() != null) { // Formato vem MMDD
      try {
        Calendar calendarOriginalTransactionDate = Calendar.getInstance();
        calendarOriginalTransactionDate.set(
            Calendar.MONTH,
            Integer.parseInt(transactionInformation.getOriginalTransactionDate().substring(0, 2)));
        calendarOriginalTransactionDate.set(
            Calendar.DAY_OF_MONTH,
            Integer.parseInt(transactionInformation.getOriginalTransactionDate().substring(2, 4)));
        if (calendarOriginalTransactionDate.getTime().after(new Date())) {
          calendarOriginalTransactionDate.set(
              Calendar.YEAR, calendarOriginalTransactionDate.get(Calendar.YEAR) - 1);
        }
        transacaoQrCodeElo.setOriginalTransactionDate(calendarOriginalTransactionDate.getTime());
      } catch (Exception e) {
        log.warn(
            "Não foi possível interpretar originalTransactionDate presente no QrCode. Adotando interpretação timestamp.",
            e);
        transacaoQrCodeElo.setOriginalTransactionDate(
            new Date(Long.valueOf(transactionInformation.getOriginalTransactionDate())));
      }
    }
    transacaoQrCodeElo.setOriginalTransactionId(transactionInformation.getOriginalTransactionId());
    transacaoQrCodeElo.setOriginalTransactionTerminalId(
        transactionInformation.getOriginalTransactionTerminalId());
    if (transactionInformation.getPaymentInstallments().length() == 3) {
      transacaoQrCodeElo.setProductType(
          transactionInformation.getPaymentInstallments().substring(0, 1));
      transacaoQrCodeElo.setInstallments(
          Integer.valueOf(transactionInformation.getPaymentInstallments().substring(1, 3)));
    } else {
      transacaoQrCodeElo.setInstallments(
          Integer.valueOf(transactionInformation.getPaymentInstallments()));
    }
    transacaoQrCodeElo.setPaymentSource(transactionInformation.getPaymentSource());
    transacaoQrCodeElo.setParseTransactionId(transactionInformation.getTransactionId());
    if (transactionInformation.getTransactionDate()
        != null) { // Formato vem MMDDHHmmss em um dos tipos de QrCode // Não sei como diferenciar
      // os tipos
      interpretaTransactionDateQrCode(transactionInformation, transacaoQrCodeElo);
    }
    transacaoQrCodeElo.setTransactionType(transactionInformation.getTransactionType());
    transacaoQrCodeElo.setQrcodeVersion(transactionInformation.getEloQrCodeVersion());
    return transacaoQrCodeElo;
  }

  private static void interpretaTransactionDateQrCode(
      TransactionInformation transactionInformation, TransacaoQrCodeElo transacaoQrCodeElo) {
    try {
      if (transactionInformation.getEloQrCodeVersion() != null
          && transactionInformation.getEloQrCodeVersion().equals("3")) {
        transacaoQrCodeElo.setTransactionDate(
            ISODate.parseISODate(transactionInformation.getTransactionDate())); // MMDDHHmmss
      } else {
        // DDMMYYHHmmss
        Calendar calendarTransactionDate = Calendar.getInstance();
        calendarTransactionDate.set(
            Calendar.DAY_OF_MONTH,
            Integer.parseInt(transactionInformation.getTransactionDate().substring(0, 2)));
        calendarTransactionDate.set(
            Calendar.MONTH,
            Integer.parseInt(transactionInformation.getTransactionDate().substring(2, 4))
                - 1); // Janeiro eh 0
        calendarTransactionDate.set(
            Calendar.YEAR,
            Integer.parseInt("20" + transactionInformation.getTransactionDate().substring(4, 6)));
        calendarTransactionDate.set(
            Calendar.HOUR_OF_DAY,
            Integer.parseInt(transactionInformation.getTransactionDate().substring(6, 8)));
        calendarTransactionDate.set(
            Calendar.MINUTE,
            Integer.parseInt(transactionInformation.getTransactionDate().substring(8, 10)));
        calendarTransactionDate.set(
            Calendar.SECOND,
            Integer.parseInt(transactionInformation.getTransactionDate().substring(10, 12)));
        calendarTransactionDate.set(Calendar.MILLISECOND, 0);
        transacaoQrCodeElo.setTransactionDate(calendarTransactionDate.getTime());
      }
    } catch (StringIndexOutOfBoundsException e) {
      log.warn(
          "Não foi possível interpretar transactionDate presente no QrCode. Adotando interpretação timestamp.",
          e);
      transacaoQrCodeElo.setTransactionDate(
          new Date(Long.parseLong(transactionInformation.getTransactionDate())));
    }
  }

  private TransacaoQrCodeElo loadTransacaoQrCodeCommonInfo(
      PostQrCodeCompleteTransaction postQrCodeCompleteTransaction,
      Credencial credencialBaseParaQrCode,
      QrCodeParseResponse qrCodeparsed) {
    TransacaoQrCodeElo transacaoQrCodeEloCommonInfo = new TransacaoQrCodeElo();
    if (qrCodeparsed.getAdditionalDataInformation() != null) {
      transacaoQrCodeEloCommonInfo.setTransactionReferenceLabel(
          qrCodeparsed.getAdditionalDataInformation().getReferenceLabel());
      // qrCodeparsed.getAdditionalDataInformation().getPaymentSpecificTemplate(); //Informacao nao
      // usada. Nao sei do que se trata
    }
    transacaoQrCodeEloCommonInfo.setQrCodePayloadFormatIndicator(
        qrCodeparsed.getPayloadFormatIndicator());
    transacaoQrCodeEloCommonInfo.setQrCodePointOfInitiationMethod(
        qrCodeparsed.getPointOfInitiationMethod());
    transacaoQrCodeEloCommonInfo.setMcc(qrCodeparsed.getMerchantCategoryCode());
    transacaoQrCodeEloCommonInfo.setTransactionCurrency(qrCodeparsed.getTransactionCurrency());
    transacaoQrCodeEloCommonInfo.setTransactionAmount(
        new BigDecimal(qrCodeparsed.getTransactionAmount()));
    transacaoQrCodeEloCommonInfo.setCountryCode(qrCodeparsed.getCountryCode());
    transacaoQrCodeEloCommonInfo.setMerchantName(qrCodeparsed.getMerchantName());
    transacaoQrCodeEloCommonInfo.setMerchantCity(qrCodeparsed.getMerchantCity());
    transacaoQrCodeEloCommonInfo.setPostalCode(qrCodeparsed.getPostalCode());
    transacaoQrCodeEloCommonInfo.setQrCodeUnreservedTemplates(
        qrCodeparsed.getUnreservedTemplates());
    transacaoQrCodeEloCommonInfo.setQrCodeCrc(qrCodeparsed.getCrc());

    transacaoQrCodeEloCommonInfo.setQrCode(postQrCodeCompleteTransaction.getQrCode());
    transacaoQrCodeEloCommonInfo.setIdConta(credencialBaseParaQrCode.getIdConta());
    transacaoQrCodeEloCommonInfo.setTokenInterno(credencialBaseParaQrCode.getTokenInterno());
    return transacaoQrCodeEloCommonInfo;
  }

  private boolean isCallbackSuccess(TransacaoQrCodeElo transacaoQrCodeEloAfterTime) {
    return transacaoQrCodeEloAfterTime != null
        && transacaoQrCodeEloAfterTime
            .getCallbackStatus()
            .equals(QrCodeEloCallbackStatus.CALLBACK_STATUS_RECEBIDO_SUCESSO.getStatus());
  }

  private ResponseEntity<QrCodeEloSendTransactionResponse> markAndReturnTransactionSuccess(
      TransacaoQrCodeElo transacaoQrCodeEloAfterTime, Credencial credencial) {

    transacaoQrCodeEloAfterTime.setCallbackStatus(
        QrCodeEloCallbackStatus.CALLBACK_STATUS_SUCESSO_SEM_TIMEOUT.getStatus());
    transacaoQrCodeEloRepository.saveAndFlush(transacaoQrCodeEloAfterTime);
    return ResponseEntity.ok(
        new QrCodeEloSendTransactionResponse(
            transacaoQrCodeEloAfterTime.getTransactionId(),
            QrCodeEloCallbackStatus.CALLBACK_STATUS_SUCESSO_SEM_TIMEOUT.getMensagemRetorno(),
            transacaoQrCodeEloAfterTime.getIdConta(),
            credencial.getIdCredencial(),
            QrCodeEloTransactionTypeEnum.getValorResponseFromCallbackRequest(
                    transacaoQrCodeEloAfterTime.getTransactionType())
                .getValorResponse(),
            APPROVAL_TIMESTAMP_DATE_FORMAT.format(
                transacaoQrCodeEloAfterTime.getApprovalTimestamp()),
            StringUtils.leftPad(
                transacaoQrCodeEloAfterTime.getTransactionAuthorizationCode(), 6, '0'),
            transacaoQrCodeEloAfterTime.getHostNsu(),
            transacaoQrCodeEloAfterTime.getTerminalNsu(),
            transacaoQrCodeEloAfterTime.getMerchantId(),
            transacaoQrCodeEloAfterTime.getTerminalId(),
            transacaoQrCodeEloAfterTime.getTransactionAmount().toString()));
  }

  private boolean isCallbackFail(TransacaoQrCodeElo transacaoQrCodeEloAfterTime) {
    return transacaoQrCodeEloAfterTime != null
        && transacaoQrCodeEloAfterTime
            .getCallbackStatus()
            .equals(QrCodeEloCallbackStatus.CALLBACK_STATUS_RECEBIDO_FALHA.getStatus());
  }

  private ResponseEntity<QrCodeEloSendTransactionResponse> markAndReturnTransactionFail(
      TransacaoQrCodeElo transacaoQrCodeEloAfterTime) {
    transacaoQrCodeEloAfterTime.setCallbackStatus(
        QrCodeEloCallbackStatus.CALLBACK_STATUS_FALHA_SEM_TIMEOUT.getStatus());
    transacaoQrCodeEloRepository.saveAndFlush(transacaoQrCodeEloAfterTime);
    log.warn(
        "Transação QrCode Elo id {} rejeitada no callback.", transacaoQrCodeEloAfterTime.getId());

    String msg;
    if (transacaoQrCodeEloAfterTime.getTransactionErrorDescription() != null) {
      if (transacaoQrCodeEloAfterTime
          .getTransactionErrorDescription()
          .contains("EXCEDE LIMITE|DO CARTAO")) {
        msg = "Transação excede limite do cartão";
      } else if (transacaoQrCodeEloAfterTime
          .getTransactionErrorDescription()
          .contains("QRCode expirado")) {
        msg = "QRCode expirado";
      } else if (transacaoQrCodeEloAfterTime
          .getTransactionErrorDescription()
          .contains("NAO PERMITIDA|PARA CARTAO")) {
        msg = "Transação não permitida para o cartão";
      } else {
        msg = QrCodeEloCallbackStatus.CALLBACK_STATUS_FALHA_SEM_TIMEOUT.getMensagemRetorno();
      }
    } else {
      msg = QrCodeEloCallbackStatus.CALLBACK_STATUS_FALHA_SEM_TIMEOUT.getMensagemRetorno();
    }

    return new ResponseEntity<>(
        new QrCodeEloSendTransactionResponse(transacaoQrCodeEloAfterTime.getTransactionId(), msg),
        HttpStatus.CONFLICT);
  }

  private ResponseEntity<QrCodeEloSendTransactionResponse> markAndReturnTransactionTimeout(
      long timeOfEloAPIResponse, TransacaoQrCodeElo transacaoQrCodeEloAfterTime) {
    transacaoQrCodeEloAfterTime.setCallbackStatus(
        QrCodeEloCallbackStatus.CALLBACK_STATUS_TIMEOUT.getStatus());
    transacaoQrCodeEloRepository.saveAndFlush(transacaoQrCodeEloAfterTime);
    log.warn(
        "Transação QrCode Elo id {} não recebeu callback em {} milisegundos.",
        transacaoQrCodeEloAfterTime.getId(),
        System.currentTimeMillis() - timeOfEloAPIResponse);

    return new ResponseEntity<>(
        new QrCodeEloSendTransactionResponse(
            transacaoQrCodeEloAfterTime.getTransactionId(),
            QrCodeEloCallbackStatus.CALLBACK_STATUS_TIMEOUT.getMensagemRetorno()),
        HttpStatus.INTERNAL_SERVER_ERROR);
  }

  private void saveCallbackInfoSuccessOrFailed(
      QrCodeCallBackRequest qrCodeCallBackRequest,
      TransacaoQrCodeElo transacaoQrCodeElo,
      Integer statusAnterior) {

    loadCallbackInfoIntoTransacaoQrCodeElo(qrCodeCallBackRequest, transacaoQrCodeElo);
    if (qrCodeCallBackRequest.getStatus().equals("SUCCESS")) {
      transacaoQrCodeElo.setCallbackStatus(
          statusAnterior.equals(QrCodeEloCallbackStatus.CALLBACK_STATUS_INICIAL.getStatus())
              ? QrCodeEloCallbackStatus.CALLBACK_STATUS_RECEBIDO_SUCESSO.getStatus()
              : QrCodeEloCallbackStatus.CALLBACK_STATUS_SUCESSO_APOS_TIMEOUT.getStatus());
    } else {
      transacaoQrCodeElo.setCallbackStatus(
          statusAnterior.equals(QrCodeEloCallbackStatus.CALLBACK_STATUS_INICIAL.getStatus())
              ? QrCodeEloCallbackStatus.CALLBACK_STATUS_RECEBIDO_FALHA.getStatus()
              : QrCodeEloCallbackStatus.CALLBACK_STATUS_FALHA_APOS_TIMEOUT.getStatus());
      if (qrCodeCallBackRequest.getQrCodeError() != null) {
        transacaoQrCodeElo.setTransactionErrorCode(
            qrCodeCallBackRequest.getQrCodeError().getCode());
        transacaoQrCodeElo.setTransactionErrorDescription(
            qrCodeCallBackRequest.getQrCodeError().getDescription());
      }
    }
    transacaoQrCodeEloRepository.saveAndFlush(transacaoQrCodeElo);
    entityManager.detach(transacaoQrCodeElo);
  }

  private void loadCallbackInfoIntoTransacaoQrCodeElo(
      QrCodeCallBackRequest qrCodeCallBackRequest, TransacaoQrCodeElo transacaoQrCodeElo) {
    QrCodeCallBackRequest.QrCodeTransactionInfo qrCodeTransactionInfo;
    qrCodeTransactionInfo = qrCodeCallBackRequest.getQrCodeTransactionInfo();
    transacaoQrCodeElo.setTransactionAuthenticationCode(
        qrCodeTransactionInfo.getAuthenticationCode());
    transacaoQrCodeElo.setTransactionAuthorizationCode(
        qrCodeTransactionInfo.getAuthorizationCode());
    transacaoQrCodeElo.setHostNsu(qrCodeTransactionInfo.getHostNsu());
    transacaoQrCodeElo.setMerchantId(qrCodeTransactionInfo.getMerchantId());
    transacaoQrCodeElo.setTransactionReferenceLabel(qrCodeTransactionInfo.getReferenceLabel());
    transacaoQrCodeElo.setTransactionStatus(qrCodeTransactionInfo.getStatus());
    transacaoQrCodeElo.setTerminalId(qrCodeTransactionInfo.getTerminalId());
    transacaoQrCodeElo.setTerminalNsu(qrCodeTransactionInfo.getTerminalNsu());
    transacaoQrCodeElo.setTransactionType(qrCodeTransactionInfo.getType());
    if (qrCodeTransactionInfo.getApprovalTimestamp() != null) {
      try {
        transacaoQrCodeElo.setApprovalTimestamp(
            APPROVAL_TIMESTAMP_DATE_FORMAT.parse(qrCodeTransactionInfo.getApprovalTimestamp()));
      } catch (ParseException e) {
        e.printStackTrace();
        throw new GenericServiceException("Erro interno no servidor.");
      }
    }
  }

  private String getCipheredInformation(String dadosSensiveis, String chavePublicaBase64) {
    java.security.Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());

    // get public key
    byte[] decodedPublic = new Base64().decode(chavePublicaBase64);
    X509EncodedKeySpec publicSpec = new X509EncodedKeySpec(decodedPublic);
    KeyFactory keyFactory;
    RSAPublicKey rsaPublicKey;
    try {
      keyFactory = KeyFactory.getInstance(KEY_ALGORITHM);
      rsaPublicKey = (RSAPublicKey) keyFactory.generatePublic(publicSpec);
    } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
      log.error("Ocorreu um erro no uso da chave publica da API QrCode ELO.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }

    // encrypt
    byte[] cipheredTextAsArray;
    try {
      Cipher oaepFromAlgorithm = Cipher.getInstance(QRCODE_CYPHER_ALGORITHM);
      oaepFromAlgorithm.init(Cipher.ENCRYPT_MODE, rsaPublicKey);
      cipheredTextAsArray =
          oaepFromAlgorithm.doFinal(dadosSensiveis.getBytes(StandardCharsets.UTF_8));
    } catch (NoSuchAlgorithmException
        | NoSuchPaddingException
        | InvalidKeyException
        | IllegalBlockSizeException
        | BadPaddingException e) {
      log.error(
          "Ocorreu um erro na transcrição dos dados usando a chave publica da API QrCode ELO.");
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }

    return Base64.encodeBase64String(cipheredTextAsArray);
  }

  private String getDadosSensiveisCartaoAsStringParaQrCode(
      String pan, String expiryMonth, String expiryYear, String name, String csc, String cpf) {
    String parte1 =
        "{\n"
            + "   \"cardHolder\":{\n"
            + "      \"card\":{\n"
            + "         \"pan\":\""
            + pan
            + "\",\n"
            + "         \"expiry\":{\n"
            + "            \"month\":\""
            + StringUtils.leftPad(expiryMonth, 2, '0')
            + "\",\n"
            + "            \"year\":\""
            + expiryYear
            + "\"\n"
            + "         },\n"
            + "         \"name\":\"";
    parte1 = parte1.replace("\n", "").replace(" ", "");

    String parte2 =
        "\",\n"
            + "         \"csc\":\""
            + csc
            + "\"\n"
            + "      },\n"
            + "      \"cpf\":\""
            + cpf
            + "\"\n"
            + "   }\n"
            + "}\n";
    parte2 = parte2.replace("\n", "").replace(" ", "");

    // feito assim para não se perder os espacos no name "ex: TESTE ELO, viraria TESTEELO"
    // mas concordo... pode ser melhorado
    return parte1 + name + parte2;
  }

  private String getCRC16_CCITT(String qrCodeRecebido) {
    int crc = 0xFFFF; // initial value
    int polynomial = 0x1021; // 0001 0000 0010 0001 (0, 5, 12)
    byte[] bytes = qrCodeRecebido.getBytes();
    for (byte b : bytes) {
      for (int i = 0; i < 8; i++) {
        boolean bit = ((b >> (7 - i) & 1) == 1);
        boolean c15 = ((crc >> 15 & 1) == 1);
        crc <<= 1;
        if (c15 ^ bit) crc ^= polynomial;
      }
    }
    crc &= 0xffff;
    return Integer.toHexString(crc);
  }

  private <T> HttpEntity<T> restExchangeWithException(
      RestTemplate restTemplate,
      String url,
      HttpMethod httpMethod,
      HttpEntity<?> requestEntity,
      ParameterizedTypeReference<T> responseType) {
    try {
      return restTemplate.exchange(url, httpMethod, requestEntity, responseType);
    } catch (HttpClientErrorException | HttpServerErrorException e) {
      log.error(
          "Erro na chamada da API "
              + url
              + ". Request: "
              + requestEntity.toString()
              + " Response Message: "
              + e.getResponseBodyAsString());
      throw new GenericServiceException(
          "Erro no servidor. Tente novamente mais tarde.",
          e.getResponseBodyAsString(),
          e.getStatusCode());
    }
  }

  private String getBodyForRequest(Object object) {
    StringBuilder sb = new StringBuilder();
    sb.append("{");
    try {
      Field[] fields = object.getClass().getDeclaredFields();
      int index = 0;
      for (Field f : fields) {
        f.setAccessible(true);
        if (f.get(object) != null) {
          sb.append("\"");
          sb.append(f.getName());
          sb.append("\"");
          sb.append(":");
          String unescapedValue = mapper.writeValueAsString(f.get(object));
          sb.append(unescapedValue);
          if (isNotLastElement(index, fields.length - 1)) {
            sb.append(",");
          }
        }
        index++;
      }
    } catch (JsonProcessingException | IllegalAccessException e) {
      e.printStackTrace();
    }
    sb.append("}");
    return sb.toString();
  }

  private boolean isNotLastElement(Integer index, Integer size) {
    return index < size;
  }

  private HttpHeaders getHeadersVCNElo(String accessToken) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(CONTENT_HEADER_NAME, CONTENT_HEADER_VALUE);
    headers.add(ELO_CLIENT_ID_HEADER_NAME, VCN_CLIENT_ID);
    headers.add(ELO_AUTH_HEADER_NAME, VCN_CLIENT_AUTH);
    if (accessToken != null) {
      headers.add(ELO_ACCESS_TOKEN_HEADER_NAME, accessToken);
    }
    return headers;
  }

  private HttpHeaders getHeadersQrCodeFull(String clientId, String basicAuthToken) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(CONTENT_HEADER_NAME, CONTENT_HEADER_VALUE);
    headers.add(ELO_CLIENT_ID_HEADER_NAME, clientId);
    headers.add(ELO_AUTH_HEADER_NAME, basicAuthToken);
    return headers;
  }

  private HttpHeaders getHeadersQrCodeEnviarTransacao(String clientId, String bearerToken) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(CONTENT_HEADER_NAME, CONTENT_HEADER_VALUE);
    headers.add(ELO_CLIENT_ID_HEADER_NAME, clientId);
    headers.add(ELO_AUTH_HEADER_NAME, bearerToken);
    return headers;
  }

  private HttpHeaders getHeadersQrCodeAuthorization(String bearerToken) {
    HttpHeaders headers = new HttpHeaders();
    headers.add(CONTENT_HEADER_NAME, CONTENT_HEADER_VALUE);
    headers.add(ELO_AUTH_HEADER_NAME, bearerToken);
    return headers;
  }

  private String obterSensitive(Credencial credencialBaseParaVcn) {
    CardInformation cardInformation = getCardInformation(credencialBaseParaVcn);
    if (cardInformation == null) {
      throw new GenericServiceException("Ocorreu um erro ao tentar obter dados do cartão.");
    }

    String dadosCartaoStringified = getDadosCartaoJsonStringified(cardInformation);
    // A chave privada é uma informação sensível e deve ser registrada com cuidado
    // Armazenados os dados nas propriedades, pois a segurança do sistema é definido por sua maior
    // vulnerabilidade, que não será essa.
    return generateSensitive(dadosCartaoStringified, VCN_USER_EC_KEY);
  }

  @SuppressWarnings("SameParameterValue")
  private String generateSensitive(String dadosCartaoStringified, ECKey ecKeyUser) {
    JWSObject dadosAssinados = assinarDadosComJSONWebSignature(dadosCartaoStringified, ecKeyUser);
    String dadosAssinadosSerialized = serializeJWSObject(dadosAssinados);
    ECKey ecKeyPublicaElo = getECKeyByChaveEC(queryServerPublicKey());
    return criptografarComJSONWebEncryption(dadosAssinadosSerialized, ecKeyPublicaElo);
  }

  private UsageConstraints getNewUsageConstraintsWithExpiryParsed(
      UsageConstraints usageConstraints) {
    Long daysOffset;
    if (usageConstraints.getExpiry() != null) {
      try {
        daysOffset = Long.valueOf(usageConstraints.getExpiry());
      } catch (NumberFormatException e) {
        log.error(
            "Erro ao converter o tempo de validade do cartão. Envie o campo \"expiry\" de acordo com o padrão (número da validade, em dias)");
        throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
      }
      usageConstraints.setExpiry(getTimeNowPlusOffsetInDaysWithTimeZoneAsString(daysOffset));
    }
    return usageConstraints;
  }

  private String getTimeNowPlusOffsetInDaysWithTimeZoneAsString(Long daysOffset) {
    LocalDateTime ldt = LocalDateTime.now().plusDays(daysOffset);
    DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    Instant instant = Instant.now();
    ZoneId zoneId = ZoneId.of(DEFAULT_TIME_ZONE);
    ZonedDateTime zdt = instant.atZone(zoneId);
    String timeZone = zdt.getOffset().toString();
    return dtf.format(ldt).replace(" ", "T").concat(timeZone);
  }

  private String getDadosCartaoJsonStringified(CardInformation cardInfo) {
    String dadosCartaoJsonStringified =
        "{\"pan\":\""
            + cardInfo.getPan()
            + "\","
            + "\"expiry\":{"
            + "\"month\":"
            + cardInfo.getMesValidade().toString()
            + ","
            + "\"year\":"
            + cardInfo.getAnoValidade().toString()
            + "},"
            + "\"name\":\""
            + cardInfo.getNomeCartao()
            + "\","
            + "\"csc\":\""
            + cardInfo.getCsc()
            + "\"}"; // + "\"," +
    //					"\"cscEntryTime\":\"" + cardInfo.getCscEntryTime() + "\"}";
    return dadosCartaoJsonStringified;
  }

  private JWSObject assinarDadosComJSONWebSignature(String dadosCartaoStringified, ECKey keyPair) {
    JWSSigner signer;
    try {
      signer = new ECDSASigner(keyPair);
    } catch (JOSEException e) {
      log.error("Ocorreu um erro ao criar o JSON Web Signature Signer.");
      e.printStackTrace();
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }

    JWSObject jwsObject =
        new JWSObject(
            new JWSHeader.Builder(JWSAlgorithm.ES256).keyID(keyPair.getKeyID()).build(),
            new Payload(dadosCartaoStringified));

    try {
      jwsObject.sign(signer);
    } catch (JOSEException e) {
      log.error("Ocorreu um erro ao assinar o objeto JWS.");
      e.printStackTrace();
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }

    try {
      jwsObject.verify(new ECDSAVerifier(keyPair.toPublicJWK()));
    } catch (JOSEException e) {
      log.error("Ocorreu um erro ao verificar a assinatura do JWT.");
      e.printStackTrace();
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }
    return jwsObject;
  }

  private String serializeJWSObject(JWSObject jwsObject) {
    return jwsObject.serialize();
  }

  private String criptografarComJSONWebEncryption(
      String dadosAssinadosSerialized, ECKey chavePublicaElo) {
    JWEHeader header = new JWEHeader(JWEAlgorithm.ECDH_ES, EncryptionMethod.A128CBC_HS256);
    Payload payload = new Payload(dadosAssinadosSerialized);

    JWEObject jweObject = new JWEObject(header, payload);

    try {
      jweObject.encrypt(new ECDHEncrypter(chavePublicaElo));
    } catch (JOSEException e) {
      e.printStackTrace();
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }
    return jweObject.serialize();
  }

  @SuppressWarnings("SameParameterValue")
  private String decriptografarComJSONWebEncryption(String sensitive, ECKey ecKey) {

    try {
      JWEObject jweObject = JWEObject.parse(sensitive);
      jweObject.decrypt(new ECDHDecrypter(ecKey));
      SignedJWT signedJWT = SignedJWT.parse(jweObject.getPayload().toString());
      return signedJWT.getPayload().toString();
    } catch (java.text.ParseException | JOSEException e) {
      e.printStackTrace();
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }
  }

  /**
   * Gera o password Bcrypt a ser usado no challenge (que por sua vez é usado no Login da API ELO)
   *
   * @param username o nome de usuário cadastrado na API ELO
   * @param senha a senha do usuário cadastrado na API ELO
   * @see #generateChallenge(String, String)
   */
  private String generateBcryptPassword(String username, String senha) {
    // 1º Passo: Gerar um salt a partir do nome de usuário

    // Aplicar o algoritmo SHA256 no nome do usuário e reservar os 16 primeiros
    // caracteres.
    String primeiros16Digitos = GenericService.encodeSenhaSHA256(username).substring(0, 16);

    // Aplicar a codificação Base64 sobre o resultado do item anterior utilizando o
    // alfabeto customizado do algoritmo bcrypt. O alfabeto utilizado pelo bcrypt
    // utiliza "." ao invés de "+" e começa com "./". Segue abaixo
    // ./ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789
    String digitosTratados = primeiros16Digitos.replace("+", ".");
    String base64NosDigitos = new String(Base64.encodeBase64(digitosTratados.getBytes()));

    // Concatenar o resultado do item anterior com o texto "$2a$12$". Exemplo de
    // resultado: $2a$12$N9qo8uLOickgx2ZMRZoMye
    String salt = ELO_BCRYPT_SALT_PREFIX + base64NosDigitos;

    // 2º Passo: Aplicar o algoritmo bcrypt utilizando a senha do usuário e o salt
    // gerado no primeiro passo

    // Aplicar o algoritmo SHA256 na senha do usuário
    String sha256NaSenha = GenericService.encodeSenhaSHA256(senha);

    // Aplicar a codificação Base64 sobre o resultado do item anterior
    String base64NoSha256NaSenha = new String(Base64.encodeBase64(sha256NaSenha.getBytes()));

    // Aplicar o algoritmo bcrypt utilizando o resultado do item anterior e o salt
    // gerado no primeiro passo.
    return org.springframework.security.crypto.bcrypt.BCrypt.hashpw(base64NoSha256NaSenha, salt);
  }

  /**
   * Gera um challenge a ser usado noo login da API ELO (geração de novos accessToken e
   * refreshToken)
   *
   * @param username o nome de usuário cadastrado na API ELO
   * @param senha a senha do usuário cadastrado na API ELO
   * @see #loginElo(String, String)
   */
  public String generateChallenge(String username, String senha) {

    String challenge;
    if (cacheLoginVcn == null
        || dtExpiracaoCacheLoginVcn == null
        || cacheVcnChallenge == null
        || cacheLoginVcn.getAccessToken() == null
        || dtExpiracaoCacheLoginVcn.before(new Date())) {
      // 1 - Gerar o bcryptPassword
      String bcryptPassword = generateBcryptPassword(username, senha);
      // 2 - Gerar um salt a partir da mutation createLoginSalt
      ResponseEntity<CreateLoginSaltResponse> saltResponse = createLoginSalt(username);
      // 3 - Aplicar o algoritmo bcrypt utilizando o bcryptPassword e o salt gerado no
      // item anterior
      challenge =
          org.springframework.security.crypto.bcrypt.BCrypt.hashpw(
              bcryptPassword, saltResponse.getBody().getSalt());
      // NOTA: O resultado final deve estar no formato padrão, com prefixo, custo,
      // salt e por fim o valor computado em Base64, totalizando 60 caracteres.
      // Exemplo de challenge:
      // $2a$10$N9qo8uLOickgx2ZMRZoMyeIjZAgcfl7p92ldGxad68LJZdL17lhWy
      cacheVcnChallenge = challenge;
    } else {
      challenge = cacheVcnChallenge;
    }
    return challenge;
  }

  private String getChavePublicaStringified(ChaveCurvasElipticas chavePublica) {
    Gson gson = new Gson();
    return gson.toJson(chavePublica, ChaveCurvasElipticas.class);
  }

  private String generateRandomSHA256() {
    MessageDigest digest;
    try {
      digest = MessageDigest.getInstance("SHA-256");
    } catch (NoSuchAlgorithmException e) {
      e.printStackTrace();
      throw new GenericServiceException("Erro no servidor. Tente novamente mais tarde.", e);
    }
    byte[] hash = digest.digest(generateRandomString().getBytes(StandardCharsets.UTF_8));
    return new String(Hex.encode(hash));
  }

  private String generateRandomString() {
    int leftLimit = 48;
    int rightLimit = 122;
    int targetStringLength = 10;
    Random random = new Random();
    return random
        .ints(leftLimit, rightLimit + 1)
        .filter(i -> (i <= 57 || i >= 65) && (i <= 90 || i >= 97))
        .limit(targetStringLength)
        .collect(StringBuilder::new, StringBuilder::appendCodePoint, StringBuilder::append)
        .toString();
  }

  public ChaveCurvasElipticas getChaveECByECKey(ECKey key, boolean comPrivateKey) {
    return new ChaveCurvasElipticas(
        generateRandomSHA256(),
        key.getX().toString(),
        key.getY().toString(),
        comPrivateKey ? key.getD().toString() : null);
  }

  private ECKey getECKeyByChaveEC(ChaveCurvasElipticas chaveEC) {
    ECKey ecKey;
    String kid = chaveEC.getKid();
    Base64URL coordinate_X = Base64URL.from(chaveEC.getX());
    Base64URL coordinate_Y = Base64URL.from(chaveEC.getY());
    if (chaveEC.getD() != null) {
      Base64URL privateKey = Base64URL.from(chaveEC.getD());
      ecKey =
          new ECKey.Builder(Curve.P_256, coordinate_X, coordinate_Y)
              .d(privateKey)
              .algorithm(JWSAlgorithm.ES256)
              .keyID(kid)
              .build();
    } else {
      ecKey =
          new ECKey.Builder(Curve.P_256, coordinate_X, coordinate_Y)
              .algorithm(JWSAlgorithm.ES256)
              .keyID(kid)
              .build();
    }
    return ecKey;
  }

  private CardInformation getCardInformation(Credencial credencial) {
    CardResponse cardResponse = credencialService.callGetDataFromPrintingJcard(credencial);
    if (cardResponse != null) {
      CardInformation cardInfo = new CardInformation();
      cardInfo.setNomeCartao(credencial.getNomeImpresso());
      cardInfo.setPan(getPanDecriptografado(cardResponse.getPancripto()));
      cardInfo.setCsc(getCvvDecriptografado(cardResponse.getCvv2cripto()));
      cardInfo.setCscEntryTime(getTimeFormatted(credencial.getDataHoraInclusao()));
      cardInfo.setAnoValidade(credencial.getDataValidade().getYear());
      cardInfo.setMesValidade(credencial.getDataValidade().getMonth().getValue());
      return cardInfo;
    }
    return null;
  }

  private String getTimeFormatted(LocalDateTime localDateTime) {
    DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_TIME_FORMAT_ISO_8601);
    return formatter.format(localDateTime).replace(" ", "T").concat(GMT_MINUS_THREE);
  }

  private String getCvvDecriptografado(String cvvCriptografado) {
    return CriptoUtil.descriptografarDados(cvvCriptografado).substring(0, 3);
  }

  private String getPanDecriptografado(String panCriptografado) {
    return CriptoUtil.descriptografarDados(panCriptografado).substring(0, 16);
  }

  @SuppressWarnings("SameParameterValue")
  private ECKey getEcKeyByParams(String keyId, String coord_X, String coord_Y, String privKey) {
    ECKey keiPair;
    if (privKey != null) {
      keiPair =
          new ECKey.Builder(Curve.P_256, Base64URL.from(coord_X), Base64URL.from(coord_Y))
              .d(Base64URL.from(privKey))
              .algorithm(JWSAlgorithm.ES256)
              .keyID(keyId)
              .build();
    } else {
      keiPair =
          new ECKey.Builder(Curve.P_256, Base64URL.from(coord_X), Base64URL.from(coord_Y))
              .algorithm(JWSAlgorithm.ES256)
              .build();
    }
    return keiPair;
  }

  private boolean hasContent(WrapperGraphQL<?> response) {
    return response.getDataContent() != null;
  }
}
