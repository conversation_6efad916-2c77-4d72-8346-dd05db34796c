package br.com.sinergico.events;

import br.com.entity.suporte.CorporativoPortadorDispositivo;
import br.com.entity.suporte.PortadorDispositivo;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.events.dispositivo.TrocaDispositivoDTO;
import br.com.sinergico.events.dispositivo.TrocaDispositivoEvent;
import br.com.sinergico.events.movimentacaoFinanceira.MovimentacaoFinanceiraDTO;
import br.com.sinergico.events.movimentacaoFinanceira.MovimentacaoFinanceiraEvent;
import br.com.sinergico.security.PortadorAuthentication;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserEstabelecimento;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.security.SecurityUserSwagger;
import br.com.sinergico.security.UserAuthentication;
import br.com.sinergico.security.UserEstabelecimentoAuthentication;
import br.com.sinergico.security.UserSwaggerAuthentication;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

@Service
public class EventoService {

  private static final Logger log = LoggerFactory.getLogger(EventoService.class);

  @Autowired private EntityManager em;

  @Autowired private ApplicationEventPublisher eventPublisher;

  public void publicarMovimentacaoFinanceiraEvent(
      String tipo,
      Integer idInstituicaoOrigem,
      Long idContaOrigem,
      Integer codTransacaoCredito,
      BigDecimal valor,
      String documentoDestino) {

    try {
      ServletRequestAttributes attr =
          (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
      if (attr == null) {
        log.error("RequestContextHolder.currentRequestAttributes() retornou null");
        return;
      }

      HttpServletRequest httpRequest = attr.getRequest();
      if (httpRequest == null) {
        log.error("Não é possível obter o objeto HttpServletRequest");
        return;
      }

      String ip =
          (httpRequest.getHeader("X-Forwarded-For") == null)
              ? httpRequest.getRemoteAddr()
              : httpRequest.getHeader("X-Forwarded-For");

      String userName = null;
      UtilController utilController = new UtilController();
      SecurityUserPortador user = utilController.getAuthenticatedPortador(httpRequest, em);

      if (user != null) {
        userName = user.getUsername().toLowerCase();
      }

      SecurityUser securityUser = utilController.getAuthenticatedUser(httpRequest, em);
      if (securityUser != null) {
        userName = securityUser.getUsername().toLowerCase();
      }

      SecurityUserEstabelecimento securityUserEstabelecimento =
          utilController.getAuthenticatedUserEstabelecimento(httpRequest, em);
      if (securityUserEstabelecimento != null) {
        userName = securityUserEstabelecimento.getUsername().toLowerCase();
      }

      if (userName == null) {
        log.warn("Não foi possível autenticar o usuário");
      }

      MovimentacaoFinanceiraDTO movimentacaoFinanceiraDTO =
          new MovimentacaoFinanceiraDTO(
              userName,
              ip,
              tipo,
              idInstituicaoOrigem,
              null,
              idContaOrigem,
              null,
              codTransacaoCredito,
              null,
              valor,
              documentoDestino);

      if (movimentacaoFinanceiraDTO == null) {
        log.error("Não foi possível criar o objeto MovimentacaoFinanceiraDTO");
        return;
      }

      log.info("Publicando o evento MovimentacaoFinanceiraEvent...");

      MovimentacaoFinanceiraEvent event =
          new MovimentacaoFinanceiraEvent(this, movimentacaoFinanceiraDTO);
      if (eventPublisher != null) {
        eventPublisher.publishEvent(event);
        log.info("Evento MovimentacaoFinanceiraEvent publicado com sucesso!");
      } else {
        log.error("Não foi possível publicar o evento porque eventPublisher é null");
      }

    } catch (Exception e) {
      log.error("Erro ao publicar o evento MovimentacaoFinanceiraEvent", e);
    }
  }

  private String getUsername() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    if (authentication != null) {
      if (authentication instanceof UserAuthentication) {
        UserAuthentication userAuthentication = (UserAuthentication) authentication;
        if (userAuthentication != null) {
          SecurityUser securityUser = userAuthentication.getDetails();
          return securityUser.getUsername();
        }
      } else if (authentication instanceof PortadorAuthentication) {
        PortadorAuthentication portadorAuthentication = (PortadorAuthentication) authentication;
        if (portadorAuthentication != null) {
          SecurityUserPortador securityUserPortador = portadorAuthentication.getDetails();
          return securityUserPortador.getUsername();
        }
      } else if (authentication instanceof UserEstabelecimentoAuthentication) {
        UserEstabelecimentoAuthentication userEstabelecimentoAuthentication =
            (UserEstabelecimentoAuthentication) authentication;
        if (userEstabelecimentoAuthentication != null) {
          SecurityUserEstabelecimento securityUserEstabelecimento =
              userEstabelecimentoAuthentication.getDetails();
          return securityUserEstabelecimento.getUsername();
        }
      } else if (authentication instanceof UserSwaggerAuthentication) {
        UserSwaggerAuthentication userSwaggerAuthentication =
            (UserSwaggerAuthentication) authentication;
        if (userSwaggerAuthentication != null) {
          SecurityUserSwagger securityUserSwagger = userSwaggerAuthentication.getDetails();
          return securityUserSwagger.getUsername();
        }
      }
    }
    return "";
  }

  public void publicarMovimentacaoFinanceiraEvent(
      String tipo,
      Integer idInstituicaoOrigem,
      Integer idInstituicaoDestino,
      Long idContaOrigem,
      Long idContaDestino,
      Integer codTransacaoCredito,
      Integer codTransacaoDebido,
      BigDecimal valor,
      String documentoDestino) {

    MovimentacaoFinanceiraDTO movimentacaoFinanceiraDTO = null;

    try {
      ServletRequestAttributes attr =
          (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
      if (attr == null) {
        log.error("RequestContextHolder.currentRequestAttributes() retornou null");
        return;
      }

      HttpServletRequest httpRequest = attr.getRequest();
      if (httpRequest == null) {
        log.error("Não é possível obter o objeto HttpServletRequest");
        return;
      }

      String ip =
          (httpRequest.getHeader("X-Forwarded-For") == null)
              ? httpRequest.getRemoteAddr()
              : httpRequest.getHeader("X-Forwarded-For");
      String userName = this.getUsername();

      movimentacaoFinanceiraDTO =
          new MovimentacaoFinanceiraDTO(
              userName,
              ip,
              tipo,
              idInstituicaoOrigem,
              idInstituicaoDestino,
              idContaOrigem,
              idContaDestino,
              codTransacaoCredito,
              codTransacaoDebido,
              valor,
              documentoDestino);

      if (movimentacaoFinanceiraDTO == null) {
        log.error("Não foi possível criar o objeto MovimentacaoFinanceiraDTO");
        return;
      }

      log.info("Publicando o evento MovimentacaoFinanceiraEvent...");

      MovimentacaoFinanceiraEvent event =
          new MovimentacaoFinanceiraEvent(this, movimentacaoFinanceiraDTO);
      if (eventPublisher != null) {
        eventPublisher.publishEvent(event);
        log.info("Evento MovimentacaoFinanceiraEvent publicado com sucesso!");
      } else {
        log.error("Não foi possível publicar o evento porque eventPublisher é null");
      }

    } catch (Exception e) {
      log.error("Erro ao publicar o evento MovimentacaoFinanceiraEvent: " + e.getMessage());
      e.printStackTrace();
    }
  }

  public void publicarTrocaDispositivoEvent(PortadorDispositivo dispositivo) {

    try {
      String username = this.getUsername();
      TrocaDispositivoDTO trocaDispositivoDTO = new TrocaDispositivoDTO();
      trocaDispositivoDTO.setIdLogin(dispositivo.getIdLogin());
      trocaDispositivoDTO.setIdDispositivo(dispositivo.getIdPortadorDispositivo());
      trocaDispositivoDTO.setUsername(username);
      trocaDispositivoDTO.setDeviceId(dispositivo.getDeviceId());
      trocaDispositivoDTO.setModel(dispositivo.getModel());
      trocaDispositivoDTO.setPlatformName(dispositivo.getPlatformName());
      trocaDispositivoDTO.setArchitectureInfo(dispositivo.getArchitectureInfo());
      trocaDispositivoDTO.setPlatformName(dispositivo.getPlatformName());
      trocaDispositivoDTO.setVersaoAplicativo(dispositivo.getVersaoAplicativo());
      trocaDispositivoDTO.setTimestamp(LocalDateTime.now());
      trocaDispositivoDTO.setTipo("PORTADOR");
      log.info("Publicando o evento Troca de dispositivo...");

      TrocaDispositivoEvent event = new TrocaDispositivoEvent(this, trocaDispositivoDTO);
      if (eventPublisher != null) {
        eventPublisher.publishEvent(event);
        log.info("Evento Troca de dispositivo publicado com sucesso!");
      } else {
        log.error("Não foi possível publicar o evento porque eventPublisher é null");
      }

    } catch (Exception e) {
      log.error("Erro ao publicar o evento Troca de dispositivo: " + e.getMessage());
      e.printStackTrace();
    }
  }

  public void publicarCorporativoTrocaDispositivoEvent(CorporativoPortadorDispositivo dispositivo) {

    try {
      String username = this.getUsername();
      TrocaDispositivoDTO trocaDispositivoDTO = new TrocaDispositivoDTO();
      trocaDispositivoDTO.setIdDispositivo(dispositivo.getId());
      trocaDispositivoDTO.setIdLogin(dispositivo.getIdCorporativoLogin());
      trocaDispositivoDTO.setUsername(username);
      trocaDispositivoDTO.setDeviceId(dispositivo.getDeviceId());
      trocaDispositivoDTO.setModel(dispositivo.getModel());
      trocaDispositivoDTO.setPlatformName(dispositivo.getPlatformName());
      trocaDispositivoDTO.setArchitectureInfo(dispositivo.getArchitectureInfo());
      trocaDispositivoDTO.setPlatformName(dispositivo.getPlatformName());
      trocaDispositivoDTO.setVersaoAplicativo(dispositivo.getVersaoAplicativo());
      trocaDispositivoDTO.setTimestamp(LocalDateTime.now());
      trocaDispositivoDTO.setTipo("CORPORATIVO");
      log.info("Publicando o evento Corporativo Troca de dispositivo...");

      TrocaDispositivoEvent event = new TrocaDispositivoEvent(this, trocaDispositivoDTO);
      if (eventPublisher != null) {
        eventPublisher.publishEvent(event);
        log.info("Evento Corporativo Troca de dispositivo publicado com sucesso!");
      } else {
        log.error("Não foi possível publicar o evento porque eventPublisher é null");
      }

    } catch (Exception e) {
      log.error("Erro ao publicar o evento Corporativo Troca de dispositivo: " + e.getMessage());
      e.printStackTrace();
    }
  }
}
