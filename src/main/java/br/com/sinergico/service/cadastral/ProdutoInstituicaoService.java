package br.com.sinergico.service.cadastral;

import static br.com.sinergico.util.ConstantesErro.PRD_PRODUTO_NAO_ENCONTRADO;
import static br.com.sinergico.util.MyHibernateUtils.listAndCast;

import br.com.client.rest.jcard.json.bean.CardProduct;
import br.com.client.rest.jcard.json.bean.CardProductInstallmentsConfiguration;
import br.com.client.rest.jcard.json.bean.CreateCardProduct;
import br.com.client.rest.jcard.json.bean.CreateCardProductProductGroup;
import br.com.client.rest.jcard.json.bean.CreateCardProductResponse;
import br.com.client.rest.jcard.json.bean.CreateTaxProduct;
import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ArranjoInstituidor;
import br.com.entity.cadastral.ArranjoPagamento;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.DeglosePlanoSaudeProduto;
import br.com.entity.cadastral.GrupoProdutos;
import br.com.entity.cadastral.LimiteTransacaoSolicitacao;
import br.com.entity.cadastral.ParcelamentoFaturaProduto;
import br.com.entity.cadastral.PerfilTarifario;
import br.com.entity.cadastral.PerfilTarifarioTransacao;
import br.com.entity.cadastral.PerfilTarifarioTransacaoId;
import br.com.entity.cadastral.PlanoSaudeProduto;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracaoCredito;
import br.com.entity.cadastral.ProdutoInstituidor;
import br.com.entity.cadastral.ProdutoPerfilTarifario;
import br.com.entity.cadastral.ProdutoPerfilTarifarioId;
import br.com.entity.cadastral.ProdutoPlataforma;
import br.com.entity.cadastral.RenegociacaoDividaProduto;
import br.com.entity.cadastral.TaxasRenegociacaoDividaProduto;
import br.com.entity.cadastral.TipoLoginProduto;
import br.com.entity.cadastral.TipoPortadorLogin;
import br.com.entity.suporte.Agencia;
import br.com.entity.suporte.AgenciaId;
import br.com.entity.suporte.EventoFaturavelB2B;
import br.com.entity.suporte.EventoNotificavel;
import br.com.entity.suporte.EventoNotificavelProduto;
import br.com.entity.suporte.HierarquiaFilial;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaRegional;
import br.com.entity.suporte.ItemFaturaProdutoB2B;
import br.com.entity.suporte.MapaStatus;
import br.com.entity.suporte.MapaStatusPlataforma;
import br.com.entity.suporte.Plastico;
import br.com.entity.suporte.TipoEnderecoProduto;
import br.com.entity.suporte.TravaBinRangeInstituicao;
import br.com.entity.transacional.FatCalendario;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.AddItemFaturavelProduto;
import br.com.json.bean.cadastral.AddPerfilTarifarioTransacao;
import br.com.json.bean.cadastral.AddRenegociacaoDividaProdutoVO;
import br.com.json.bean.cadastral.AlteraStatusRequest;
import br.com.json.bean.cadastral.BuscaProdutoNaoB2b;
import br.com.json.bean.cadastral.BuscarProdutoByFilter;
import br.com.json.bean.cadastral.CadastrarProdutoInstituicao;
import br.com.json.bean.cadastral.CadastroProdutoModel;
import br.com.json.bean.cadastral.CheckNivelHierarquiaEnum;
import br.com.json.bean.cadastral.ContaBancariaProdutoInstituicao;
import br.com.json.bean.cadastral.ContaProdutoLoginDetalheVO;
import br.com.json.bean.cadastral.ContaProdutoLoginResponseVO;
import br.com.json.bean.cadastral.ContaProdutoLoginVO;
import br.com.json.bean.cadastral.CreatePerfilTarifario;
import br.com.json.bean.cadastral.DadosProdutoInstituicao;
import br.com.json.bean.cadastral.DeglosePlanoSaudeVO;
import br.com.json.bean.cadastral.DetalhesProduto;
import br.com.json.bean.cadastral.GetDetalhesProdInstituicao;
import br.com.json.bean.cadastral.GetPerfilTariafarioResponse;
import br.com.json.bean.cadastral.GetPerfilTarifario;
import br.com.json.bean.cadastral.GetPerfisTarifarios;
import br.com.json.bean.cadastral.HistoricoArranjo;
import br.com.json.bean.cadastral.ParcelamentoFaturaProdutoVO;
import br.com.json.bean.cadastral.PlanoSaudeVO;
import br.com.json.bean.cadastral.ProdInstConfigCreditoRequest;
import br.com.json.bean.cadastral.ProdutoInstituicaoParametrizadoVO;
import br.com.json.bean.cadastral.ProdutoInstituicaoResponse;
import br.com.json.bean.cadastral.ResponseProdutosInstituicao;
import br.com.json.bean.cadastral.TaxasRenegociacaoDividaProdutoVO;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.criteria.ProdutoInstituicaoCriteria;
import br.com.sinergico.enums.BandeiraEnum;
import br.com.sinergico.enums.LimiteTransacaoTipoEnum;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.repository.cadastral.ProdutoInstituicaoRepository;
import br.com.sinergico.repository.cadastral.ProdutoPerfilTarifarioRepository;
import br.com.sinergico.repository.cadastral.SolicitarLimiteContaRepository;
import br.com.sinergico.repository.pix.SolicitarLimitePixRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.jcard.CardProductInstallmentsConfigurationService;
import br.com.sinergico.service.jcard.CardProductService;
import br.com.sinergico.service.jcard.ProductGroupService;
import br.com.sinergico.service.jcard.TaxProductService;
import br.com.sinergico.service.suporte.AgenciaService;
import br.com.sinergico.service.suporte.ConfiguracaoParcelamentoProdutoB2BService;
import br.com.sinergico.service.suporte.EventoFaturavelB2BService;
import br.com.sinergico.service.suporte.EventoNotificavelProdutoService;
import br.com.sinergico.service.suporte.FilialService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.HierarquiaRegionalService;
import br.com.sinergico.service.suporte.ItemFaturaProdutoB2BService;
import br.com.sinergico.service.suporte.MapaStatusPlataformaService;
import br.com.sinergico.service.suporte.MapaStatusService;
import br.com.sinergico.service.suporte.PlasticoService;
import br.com.sinergico.service.suporte.TipoEnderecoProdutoService;
import br.com.sinergico.service.suporte.TravaBinRangeInstituicaoService;
import br.com.sinergico.service.transacional.FatCalendarioService;
import br.com.sinergico.service.transacional.TipoProdutoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.bigdecimal.BigDecimalUtils;
import com.google.common.collect.ImmutableList;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import org.hibernate.Criteria;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;

@Service
public class ProdutoInstituicaoService extends GenericService<ProdutoInstituicao, Integer> {

  private static final String NAO_FOI_POSSIVEL_LOCALIZAR_O_PRODUTO_INSTITUICAO =
      "Não foi possível localizar o produto instituição!";

  private static final int PESSOA_FISICA = 1;

  private static final int PESSOA_JURIDICA = 2;

  private static final int PRODUTO_PLATAFORMA_CREDITO = 9;

  private static final boolean ATIVA = true;

  private static final String PROCESSADORA = "Processadora";

  private static final String INSTITUICAO = "Instituição";

  private static final String REGIONAL = "Regional";

  private static final String FILIAL = "Filial";

  private static final String PONTO_DE_RELACIONAMENTO = "Ponto de Relacionamento";

  private static final String CONTA = "Conta";

  private static final Integer CARTAO_MULTIPLO = 1;

  private static final int FATURADO = 1;

  public static final String MSG_ERRO_VALIDACAO_VALOR_PERCENTUAL_ADICIONAL =
      "O percentual de limite adicional não pode ser maior que 0,50.";

  public static final String VALOR_MAXIMO_PERCENTUAL = "0.50";

  @Autowired private ProdutoInstituicaoCriteria produtoInstituicaoCriteria;

  @Autowired private ProdutoCondicoesService service;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfigService;

  @Autowired private HierarquiaInstituicaoService instService;

  @Autowired private HierarquiaRegionalService hierarquiaRegionalService;

  @Autowired private ProdutoPlataformaService prodPlatService;

  @Autowired private ArranjoPagamentoService arranjoPagamentoService;

  @Autowired private ArranjoInstituidorService arranjoInstService;

  @Autowired private ProdutoInstituidorService produtoInstituidorService;

  @Autowired private AgenciaService agenciaService;

  @Autowired private PlasticoService plasticoService;

  @Autowired private CardProductService cardProductService;

  @Autowired private TaxProductService taxProductService;

  @Autowired private ProdutoPerfilTarifarioService prodPerfilTarifarioService;

  @Autowired private MapaStatusService mapaStatusService;

  @Autowired private TipoEnderecoProdutoService tipoEnderecoProdutoService;

  @Autowired private ProdutoPerfilTarifarioService produtoPerfilTarifarioService;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private PerfilTarifarioTransacaoService perfilTarifarioTransacaoService;

  @Autowired private ProdutoPerfilTarifarioRepository produtoPerfilTarifarioRepository;

  @Autowired private ItemFaturaProdutoB2BService itemFaturaProdutoB2BService;

  @Autowired private EventoFaturavelB2BService eventoFaturavelB2BService;

  @Autowired private MapaStatusPlataformaService mapaStatusPlataformaService;

  @Autowired private ProdutoInstituicaoConfiguracaoCreditoService prodInstConfigCreditoService;

  @Autowired private EventoNotificavelProdutoService eventoNotificavelProdutoService;

  @Autowired private ParcelamentoFaturaProdutoService parcelamentoFaturaProdutoService;

  @Autowired private RenegociacaoDividaProdutoService renegociacaoDividaProdutoService;

  @Autowired private PlanoSaudeService planoSaudeService;

  @Autowired private DeglosePlanoSaudeService deglosePlanoSaudeService;

  @Autowired private TaxasRenegociacaoDividaProdutoService taxasRenegociacaoDividaProdutoService;

  @Autowired private ProdutoContratadoMccService produtoContratadoMccService;

  @Autowired private FatCalendarioService fatCalendarioService;

  @Autowired private ProdutoInstituicaoGrupoProdutosService produtoInstituicaoGrupoProdutosService;

  @Autowired private DateUtil dateUtil;

  @Autowired private FilialService filialService;

  @Lazy @Autowired private HierarquiaPontoRelacionamentoService pontoRelacionamentoService;

  private CardProductInstallmentsConfiguration cardProductInstallmentsConfiguration;

  private CardProductInstallmentsConfigurationService cardProductInstallmentsConfigurationService;

  @Autowired private TipoLoginProdutoService tipoLoginProdutoService;

  @Autowired private TipoProdutoService tipoProdutoService;

  @Autowired
  private ConfiguracaoParcelamentoProdutoB2BService configuracaoParcelamentoProdutoB2BService;

  @Autowired private ProdutoTransacaoService produtoTransacaoService;

  @Autowired private ProductGroupService productGroupService;

  @Autowired private GrupoProdutosService grupoProdutosService;

  @Autowired private TravaBinRangeInstituicaoService travaBinRangeInstituicaoService;

  @Autowired
  public ProdutoInstituicaoService(ProdutoInstituicaoRepository repo) {
    super(repo);
  }

  @Autowired private SolicitarLimiteContaRepository solicitarLimiteContaRepository;

  @Autowired private SolicitarLimitePixRepository solicitarLimitePixRepository;

  @Override
  protected ProdutoInstituicaoRepository getRepository() {
    return (ProdutoInstituicaoRepository) super.getRepository();
  }

  public ProdutoInstituicao prepareProdutoInstituicao(
      CadastrarProdutoInstituicao model, ProdutoInstituicao produtoInstituicao, SecurityUser user) {
    BeanUtils.copyProperties(model, produtoInstituicao, getNullPropertyNames(model));
    produtoInstituicao.setDtHrInclusao(new Date());
    produtoInstituicao.setIdUsuarioInclusao(user.getIdUsuario());

    produtoInstituicao = save(produtoInstituicao);
    return produtoInstituicao;
  }

  public List<ProdutoInstituicao> findByIntituicao(Integer idInstituicao, Integer idProcessadora) {
    Criteria criteria = produtoInstituicaoCriteria.getHierarquiaCriteria(ProdutoInstituicao.class);
    produtoInstituicaoCriteria.setInstituicao(idInstituicao);
    produtoInstituicaoCriteria.setProcessadora(idProcessadora);
    List<ProdutoInstituicao> produtos = listAndCast(criteria);

    List<ProdutoInstituicao> toRemove = new ArrayList();

    for (ProdutoInstituicao tmp : produtos) {
      if (service.condicoesCadastradas(
          tmp.getIdProdInstituicao(), tmp.getIdInstituicao(), tmp.getIdProcessadora())) {
        toRemove.add(tmp);
      }
    }
    for (ProdutoInstituicao pdt : toRemove) {
      produtos.remove(pdt);
    }

    return produtos;
  }

  public ProdutoInstituicao findByIdProdInstituicao(Integer idProdInstituicao) {
    return getRepository().findByIdProdInstituicao(idProdInstituicao);
  }

  public Boolean existeProdutoInstituicao(Integer idProdInstituicao) {
    return getRepository().existeProdutoInstituicao(idProdInstituicao);
  }

  public ProdutoInstituicao findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return getRepository()
        .findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
            idProcessadora, idInstituicao, idProdInstituicao);
  }

  public Integer countByIntituicao(Integer idProcessadora, Integer idInstituicao) {
    return getRepository().countByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);
  }

  @Transactional
  public void salvarProdutoInstAndConfig(CadastroProdutoModel model, SecurityUser user) {

    if (model.getDiferenciacaoLimiteAdicional()
        && BigDecimalUtils.is(model.getPercentualLimiteAdicional()).gt(VALOR_MAXIMO_PERCENTUAL)) {
      throw new GenericServiceException(MSG_ERRO_VALIDACAO_VALOR_PERCENTUAL_ADICIONAL);
    }

    ProdutoInstituicao proInst = new ProdutoInstituicao();
    copyNonNullProperties(model, proInst);
    proInst.setDtHrInclusao(new Date());
    proInst.setIdUsuarioInclusao(user.getIdUsuario());

    ProdutoInstituicaoConfiguracao prodInstConfig = new ProdutoInstituicaoConfiguracao();
    copyNonNullProperties(model, prodInstConfig);
    prodInstConfig.setIdProdutoPlataforma(model.getIdProdPlat());
    prodInstConfig.setSequencial(0);
    prodInstConfig.setSequencialVirtual(0);
    prodInstConfig.setDtHrInclusao(new Date());
    prodInstConfig.setIdUsuarioInclusao(user.getIdUsuario());
    prodInstConfig.setQtdDiasCorte(model.getQtdDiasCorte());
    prodInstConfig.setSegundaViaBoleto(model.getSegundaViaBoleto());

    prodInstConfig.setTipoProduto(model.getTipoProduto());
    prodInstConfig.setBlCorporativo(model.getBlCorporativo());

    ArranjoPagamento arranjoPagamento =
        arranjoPagamentoService.findByIdArranjo(model.getIdArranjo());
    if (arranjoPagamento == null) {
      throw new GenericServiceException("Arranjo informado não existe!");
    }
    prodInstConfig.setArranjoPagamento(arranjoPagamento);
    prodInstConfig.setHistoricoIdArranjos(ImmutableList.of(model.getIdArranjo()));
    Integer idBandeira = arranjoPagamento.getArranjoInstituidor().getIdBandeira();
    prodInstConfig.setIdBandeira(idBandeira);

    validaTravaBinElo(
        model.getIdInstituicao(),
        model.getBinEstendido(),
        model.getBinEstendidoVirtual(),
        idBandeira);

    if (model.getVencimentoFaturaList() != null && !model.getVencimentoFaturaList().isEmpty()) {
      StringBuilder prazos = new StringBuilder();
      for (Integer prazo : model.getVencimentoFaturaList()) {
        prazos.append(prazo + ";");
      }
      prodInstConfig.setVencimentosFatura(prazos.toString());
    }

    proInst = saveAndFlush(proInst);
    prodInstConfig = prodInstConfigService.saveAndFlush(prodInstConfig);

    GrupoProdutos grupo = null;
    if (model.getIdGrupo() != null) {
      grupo = grupoProdutosService.findById(model.getIdGrupo());
      if (grupo == null) {
        throw new GenericServiceException("Grupo de produtos informado não existe");
      }

      prodInstConfig.setIdGrupoProduto(model.getIdGrupo());
      produtoInstituicaoGrupoProdutosService.inserirProdutoEmGrupoProdutos(
          model.getIdProdInstituicao(), model.getIdGrupo());
    }

    salvarTipoEnderecoProduto(model.getTiposEnderecosProdutos());

    createMapaStatus(prodInstConfig);

    if (model.getIdProdPlat().equals(PRODUTO_PLATAFORMA_CREDITO)) {
      gerarFatCalendariosByVencimentos(
          model.getIdProcessadora(),
          model.getIdInstituicao(),
          model.getIdProdInstituicao(),
          model.getQtdDiasCorte(),
          model.getVencimentoFaturaList(),
          user.getIdUsuario());
    }

    tipoLoginProdutoService.associaTipoLoginProduto(
        model.getTipoLogin(), model.getIdProdInstituicao());

    createCardProduct(prodInstConfig, proInst.getDescProdInstituicao());
    if (grupo != null) {
      JcardResponse response =
          productGroupService.createCardProductProductGroup(
              new CreateCardProductProductGroup(
                  grupo.getIdProductGroup(), prodInstConfig.getIdCardProduct()));

      if (!Boolean.TRUE.equals(response.getSuccess())) {
        throw new GenericServiceException(
            "Erro ao vincular produto a grupo de produtos no JCard", response.getErrors());
      }
    }
    prodInstConfigService.saveAndFlush(prodInstConfig);
  }

  public void validaTravaBinElo(
      Integer idInstituicao, Long binEstendido, Long binEstendidoVirtual, Integer idBandeira) {

    if (BandeiraEnum.ELO.getIdBandeira().equals(idBandeira)) {
      if (binEstendido != null
          && !travaBinRangeInstituicaoService.isBinFromBandeiraAndInstituicaoInRange(
              idBandeira, idInstituicao, binEstendido.toString())) {
        List<TravaBinRangeInstituicao> travaBinRangeInstituicaoList =
            travaBinRangeInstituicaoService.findByIdBandeiraAndIdInstituicao(
                idBandeira, idInstituicao);
        throw new GenericServiceException(
            String.format(
                "Bin %s não disponível para cadastro. Os ranges de BINs disponíveis são: %s.",
                binEstendido,
                travaBinRangeInstituicaoList.stream()
                    .map(TravaBinRangeInstituicao::binrangeToString)
                    .collect(Collectors.joining(";"))));
      }

      if (binEstendidoVirtual != null
          && !travaBinRangeInstituicaoService.isBinFromBandeiraAndInstituicaoInRange(
              idBandeira, idInstituicao, binEstendidoVirtual.toString())) {
        List<TravaBinRangeInstituicao> travaBinRangeInstituicaoList =
            travaBinRangeInstituicaoService.findByIdBandeiraAndIdInstituicao(
                idBandeira, idInstituicao);
        throw new GenericServiceException(
            String.format(
                "Bin virtual %s não disponível para cadastro. Os ranges de BINs disponíveis são: %s.",
                binEstendidoVirtual,
                travaBinRangeInstituicaoList.stream()
                    .map(TravaBinRangeInstituicao::binrangeToString)
                    .collect(Collectors.joining(";"))));
      }
    }
  }

  private void gerarFatCalendariosByVencimentos(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idProdInstituicao,
      Integer qtdDiasCorte,
      List<Integer> diasVencimentosFatura,
      Integer idUsuario) {

    // Calcular o primeiro mês com referência à data de hoje
    Calendar primeiroMes = Calendar.getInstance();
    primeiroMes.add(Calendar.MONTH, -1);
    primeiroMes.set(Calendar.DAY_OF_MONTH, 1);
    primeiroMes.set(Calendar.HOUR_OF_DAY, 0);
    primeiroMes.set(Calendar.MINUTE, 0);
    primeiroMes.set(Calendar.SECOND, 0);
    primeiroMes.set(Calendar.MILLISECOND, 0);

    // Calcular o ultimo mês com referência à data de hoje (sempre o primeiro mês do ano seguinte)
    Calendar ultimoMes = Calendar.getInstance();
    ultimoMes.add(Calendar.YEAR, +1);
    ultimoMes.set(Calendar.DAY_OF_YEAR, 1);
    ultimoMes.set(Calendar.HOUR_OF_DAY, 0);
    ultimoMes.set(Calendar.MINUTE, 0);
    ultimoMes.set(Calendar.SECOND, 0);
    ultimoMes.set(Calendar.MILLISECOND, 0);

    List<Calendar> mesesFatCalendario = gerarAnosMesFat(primeiroMes, ultimoMes);

    for (Integer diaVenc : diasVencimentosFatura) {

      criarFatCalendarioByListAnoMes(
          mesesFatCalendario,
          diaVenc,
          idProcessadora,
          idInstituicao,
          idProdInstituicao,
          qtdDiasCorte,
          idUsuario);
    }
  }

  private void criarFatCalendarioByListAnoMes(
      List<Calendar> mesesFatCalendario,
      Integer diaVenc,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idProdInstituicao,
      Integer qtdDiasCorte,
      Integer idUsuario) {

    SimpleDateFormat formatar = new SimpleDateFormat("yyyyMM");

    for (Calendar mes : mesesFatCalendario) {

      // Calculando anoMesFat
      Integer anoMesFat = Integer.parseInt(formatar.format(mes.getTime()));

      // Calculando data de vencimento
      Calendar dataDeVencimento = Calendar.getInstance();
      dataDeVencimento.setTime(mes.getTime());
      dataDeVencimento.set(Calendar.DAY_OF_MONTH, diaVenc);

      if (dataDeVencimento.get(Calendar.MONTH) > mes.get(Calendar.MONTH)) {
        dataDeVencimento.add(Calendar.MONTH, -1);
        dataDeVencimento.set(Calendar.DATE, dataDeVencimento.getActualMaximum(Calendar.DATE));
      }

      criarFatCalendario(
          diaVenc,
          anoMesFat,
          dataDeVencimento.getTime(),
          idProcessadora,
          idInstituicao,
          idProdInstituicao,
          qtdDiasCorte,
          idUsuario);
    }
  }

  public void criarFatCalendario(
      Integer diaDeVencimento,
      Integer anoMesFat,
      Date dtVencimento,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idProdInstituicao,
      Integer qtdDiasCorte,
      Integer idUsuario) {

    // Criar FatCalendario
    FatCalendario fatCalendario = new FatCalendario();
    fatCalendario.setIdUsuarioInclusao(idUsuario);
    fatCalendario.setDataHoraInclusao(LocalDateTime.now());
    fatCalendario.setIdProcessadora(idProcessadora);
    fatCalendario.setIdInstituicao(idInstituicao);
    fatCalendario.setIdProdInstituicao(idProdInstituicao);
    fatCalendario.setDataVencimento(dtVencimento);

    // Calcular dia de corte
    Date dtCorteCalculada = DateUtil.addDayDate(dtVencimento, -qtdDiasCorte);
    Date dtCorteUtil = dateUtil.getDiaUtilAnterior(dtCorteCalculada);

    fatCalendario.setDataCorte(dtCorteUtil);
    fatCalendario.setDiaVEncimento(diaDeVencimento);
    fatCalendario.setAnoMesFat(anoMesFat);

    Calendar hoje = Calendar.getInstance();
    hoje.set(Calendar.HOUR_OF_DAY, 0);
    hoje.set(Calendar.MINUTE, 0);
    hoje.set(Calendar.SECOND, 0);
    hoje.set(Calendar.MILLISECOND, 0);

    Date date = hoje.getTime();

    if (dtVencimento.before(date) || dtVencimento.equals(date)) {
      fatCalendario.setIdJobFaturamento(FATURADO);
      fatCalendario.setDataHoraProcFat(DateUtil.dateToLocalDateTime(date));
    }

    fatCalendarioService.saveAndFlush(fatCalendario);
  }

  private List<Calendar> gerarAnosMesFat(Calendar primeiroMes, Calendar ultimoMes) {

    List<Calendar> mesesFatCalendario = new ArrayList<Calendar>();

    for (Calendar tmp = primeiroMes; tmp.compareTo(ultimoMes) <= 0; tmp.add(Calendar.MONTH, +1)) {
      Calendar temporario = Calendar.getInstance();
      temporario.setTime(tmp.getTime());
      mesesFatCalendario.add(temporario);
    }
    return mesesFatCalendario;
  }

  private void createMapaStatus(ProdutoInstituicaoConfiguracao prodInstConfig) {
    List<MapaStatusPlataforma> msp =
        mapaStatusPlataformaService.findByIdProdutoPlataforma(
            prodInstConfig.getIdProdutoPlataforma());

    for (MapaStatusPlataforma tmp : msp) {
      MapaStatus target = new MapaStatus();
      BeanUtils.copyProperties(tmp, target);
      target.setIdProcessadora(prodInstConfig.getIdProcessadora());
      target.setIdInstituicao(prodInstConfig.getIdInstituicao());
      target.setIdProdutoInstituicao(prodInstConfig.getIdProdInstituicao());
      mapaStatusService.saveAndFlush(target);
    }
  }

  private void salvarTipoEnderecoProduto(List<TipoEnderecoProduto> tiposEnderecosProdutos) {
    for (TipoEnderecoProduto tmp : tiposEnderecosProdutos) {
      tipoEnderecoProdutoService.save(tmp);
    }
  }

  private List<ProdutoInstituicao> findByIdProdInstituicaoNot(Integer idProdInstituicao) {
    return getRepository().findByIdProdInstituicaoNot(idProdInstituicao);
  }

  private void createCardProduct(
      ProdutoInstituicaoConfiguracao prodInstConfig, String descProdInstituicao) {
    CreateCardProduct create =
        CardProductService.prepareProdInstToCreateCardProduct(prodInstConfig, descProdInstituicao);
    CreateCardProductResponse response = cardProductService.createCardProduct(create);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar Card Product: " + response.getErrors());
    }
    prodInstConfig.setIdCardProduct(response.getId());
  }

  private void updateCardProduct(
      ProdutoInstituicaoConfiguracao prodInstConfig, String descProdInstituicao) {
    updateCardProduct(prodInstConfig, descProdInstituicao, null);
  }

  public void updateCardProduct(
      ProdutoInstituicaoConfiguracao prodInstConfig,
      String descProdInstituicao,
      ProdutoInstituicaoConfiguracaoCredito prodInstConfigCredito) {

    CardProduct create =
        CardProductService.prepareProdInstToCardProduct(
            prodInstConfig,
            descProdInstituicao,
            prodInstConfigCredito != null
                ? prodInstConfigCredito.getPercentualLimiteSaque()
                : new BigDecimal("0.0"));
    JcardResponse response = cardProductService.updateCardProduct(create);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar Card Product: " + response.getErrors());
    }
  }

  public List<GetDetalhesProdInstituicao> findByIdProcessadora(
      Integer idProcessadora, String termo, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max, Direction.ASC, "idProdInstituicao");
    List<ProdutoInstituicao> produtos = new ArrayList<>();
    if (!Objects.equals(termo, "")) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        produtos =
            getRepository()
                .findByIdProcessadoraAndIdProdInstituicaoIn(idProcessadora, termoListInteiros, top);
      } else {
        produtos =
            getRepository()
                .findByIdProcessadoraAndDescProdInstituicaoContainingIgnoreCase(
                    idProcessadora, top, termo);
      }
    } else {
      produtos = getRepository().findByIdProcessadora(idProcessadora, top);
    }
    return getDetalhesListaProdutos(produtos);
  }

  public List<GetDetalhesProdInstituicao> findByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao, String termo, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max, Direction.ASC, "idProdInstituicao");
    List<ProdutoInstituicao> produtos = new ArrayList<>();
    if (!Objects.equals(termo, "")) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        produtos =
            getRepository()
                .findByIdProcessadoraAndIdInstituicaoAndIdProdInstituicaoIn(
                    idProcessadora, idInstituicao, termoListInteiros, top);
      } else {
        produtos =
            getRepository()
                .findByIdProcessadoraAndIdInstituicaoAndDescProdInstituicaoContainingIgnoreCase(
                    idProcessadora, idInstituicao, termo, top);
      }
    } else {
      produtos =
          getRepository().findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao, top);
    }
    return getDetalhesListaProdutos(produtos);
  }

  public List<ProdutoInstituicaoParametrizadoVO> buscarProdutosParametrizados(
      Integer idProcessadora, Integer idInstituicao, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max, Direction.ASC, "idProdInstituicao");
    List<ProdutoInstituicaoParametrizadoVO> produtos =
        getRepository().buscaProdutosParametrizados(idProcessadora, idInstituicao, top);
    return produtos;
  }

  public List<GetDetalhesProdInstituicao> getDetalhesListaProdutos(
      List<ProdutoInstituicao> produtos) {
    List<GetDetalhesProdInstituicao> lista = new ArrayList();

    for (ProdutoInstituicao tmp : produtos) {
      GetDetalhesProdInstituicao target = new GetDetalhesProdInstituicao();
      BeanUtils.copyProperties(tmp, target);
      target.setDescProdutoInstituidor(
          tmp.getProdutoInstituicaoConfiguracao()
              .get(0)
              .getProdutoInstituidor()
              .getDescProdInstituidor());
      target.setDescInstituicao(tmp.getInstituicao().getDescInstituicao());
      ProdutoPlataforma plataforma =
          prodPlatService.findByIdProdPlat(
              tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
      target.setDescProdutoPlataforma(plataforma.getDescProdPlat());
      target.setTipoConta(plataforma.getArranjoRelacionamento().getDescRelacionamento());
      lista.add(target);
    }
    Collections.sort(lista);
    return lista;
  }

  public DetalhesProduto buscarDetalhesProduto(Integer id) {

    DetalhesProduto detalhes = new DetalhesProduto();

    DadosProdutoInstituicao dados = new DadosProdutoInstituicao();
    CartaoVirtualProdutoInstituicao cartaoVirtual = new CartaoVirtualProdutoInstituicao();
    ContaBancariaProdutoInstituicao contaBancaria = new ContaBancariaProdutoInstituicao();

    ProdutoInstituicao prodInst = findByIdProdInstituicao(id);

    if (prodInst == null) {
      throw new GenericServiceException("Produto Instituição não encontrado!");
    }

    BeanUtils.copyProperties(prodInst, dados);
    dados.setDescInstituicao(
        instService
            .findByIdProcessadoraAndIdInstituicao(
                prodInst.getIdProcessadora(), prodInst.getIdInstituicao())
            .getDescInstituicao());
    if (prodInst.getIdRegional() != null) {
      HierarquiaRegional hierarquiaRegional =
          hierarquiaRegionalService.findByProcessadoraAndInstituicaoAndRegional(
              prodInst.getIdProcessadora(), prodInst.getIdInstituicao(), prodInst.getIdRegional());
      if (hierarquiaRegional != null) {
        dados.setDescRegional(hierarquiaRegional.getDescRegional());
      }
    }
    if (prodInst.getIdFilial() != null) {
      HierarquiaFilial hierarquiaFilial =
          filialService.findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndFilial(
              prodInst.getIdProcessadora(),
              prodInst.getIdInstituicao(),
              prodInst.getIdRegional(),
              prodInst.getIdFilial());
      if (hierarquiaFilial != null) {
        dados.setDescFilial(hierarquiaFilial.getDescFilial());
      }
    }
    if (prodInst.getIdPontoDeRelacionamento() != null) {
      HierarquiaPontoDeRelacionamento hierarquiaPontoDeRel =
          pontoRelacionamentoService.getPontoRelacPorChave(
              prodInst.getIdProcessadora(),
              prodInst.getIdInstituicao(),
              prodInst.getIdRegional(),
              prodInst.getIdFilial(),
              prodInst.getIdPontoDeRelacionamento());
      if (hierarquiaPontoDeRel != null) {
        dados.setDescPontoDeRelacionamento(hierarquiaPontoDeRel.getDescricao());
      }
    }

    ProdutoInstituicaoConfiguracao prodInstConfig =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            prodInst.getIdProcessadora(),
            prodInst.getIdProdInstituicao(),
            prodInst.getIdInstituicao());

    if (prodInstConfig == null) {
      throw new GenericServiceException("A configuração deste produto não foi encontrada!");
    }

    BeanUtils.copyProperties(prodInstConfig, dados);
    BeanUtils.copyProperties(prodInstConfig, cartaoVirtual);
    ProdutoPlataforma plataforma =
        prodPlatService.findByIdProdPlat(prodInstConfig.getIdProdutoPlataforma());

    if (plataforma == null) {
      throw new GenericServiceException(
          "O produto da plataforma deste produto não foi encontrado!");
    }

    dados.setIdProdPlat(plataforma.getIdProdPlat());
    dados.setDescProdPlataforma(plataforma.getDescProdPlat());
    dados.setTipoConta(plataforma.getArranjoRelacionamento().getDescRelacionamento());
    dados.setServiceCode(Integer.valueOf(prodInstConfig.getServiceCode()));

    ArranjoInstituidor arranjoInstituidor =
        arranjoInstService.findByIdInstituidor(
            prodInstConfig.getArranjoPagamento().getIdInstituidor());

    if (arranjoInstituidor == null) {
      throw new GenericServiceException(
          "O instituidor do arranjo deste produto não foi encontrado!");
    }

    dados.setDescArranjoInstituidor(arranjoInstituidor.getMarcaArranjo());

    ProdutoInstituidor produtoInstituidor =
        produtoInstituidorService.findById(prodInstConfig.getIdProdutoInstituidor());

    if (produtoInstituidor == null) {
      throw new GenericServiceException("O produto instituidor deste produto não foi encontrado!");
    }

    if (prodInstConfig.getHistoricoIdArranjos() != null
        && !prodInstConfig.getHistoricoIdArranjos().isEmpty()) {

      List<HistoricoArranjo> arranjoList =
          prodInstConfig.getHistoricoIdArranjos().stream()
              .map(arranjoPagamentoService::findByIdArranjo)
              .filter(Objects::nonNull)
              .map(
                  arranjo ->
                      new HistoricoArranjo(arranjo.getIdArranjo(), arranjo.getDescricaoArranjo()))
              .collect(Collectors.toList());

      dados.setHistoricoArranjo(arranjoList);
    }

    dados.setDescProdutoInstituidor(produtoInstituidor.getDescProdInstituidor());
    dados.setDescMoeda(prodInstConfig.getMoeda().getDescMoeda());
    dados.setMoeda(prodInstConfig.getMoeda().getIdMoeda());
    dados.setSimboloMoeda(prodInstConfig.getMoeda().getSimbolo());
    dados.setDescArranjo(prodInstConfig.getArranjoPagamento().getDescricaoArranjo());
    dados.setIdInstituidor(prodInstConfig.getArranjoPagamento().getIdInstituidor());
    dados.setPadrao(prodInstConfig.getPadrao());
    dados.setIdGrupoProduto(prodInstConfig.getIdGrupoProduto());
    dados.setRedefinirSenhaCaf(prodInstConfig.getRedefinirSenhaCaf());
    dados.setBoletoRegistrado(prodInstConfig.getBoletoRegistrado());
    dados.setMetodoSegurancaTransacao(prodInstConfig.getMetodoSegurancaTransacao());
    dados.setQtdContasDocumentoProduto(prodInstConfig.getQtdContasDocumentoProduto());

    BeanUtils.copyProperties(prodInstConfig, contaBancaria);
    if (prodInstConfig.getBoletoAgencia() != null) {
      AgenciaId agenciaId =
          new AgenciaId(
              Integer.parseInt(prodInstConfig.getBoletoAgencia()), prodInstConfig.getBoletoBanco());
      Agencia agencia = agenciaService.findById(agenciaId);

      if (agencia == null) {
        throw new GenericServiceException(
            "As informações de banco e agência das configurações de conta bancária deste produto não foram encontradas!");
      }

      contaBancaria.setDescAgencia(agencia.getDescAgencia());
      contaBancaria.setDescBanco(agencia.getBanco().getDescBanco());
    }

    detalhes.setDados(dados);
    detalhes.setCartaoVirtual(cartaoVirtual);
    detalhes.setContaBancaria(contaBancaria);

    // TODO. No momento espera-se somente um resultado. Expandir caso algum dia seja necessário
    detalhes
        .getDados()
        .setTipoLogin(
            tipoLoginProdutoService.encontraAssociadoPorProduto(id).stream()
                .findFirst()
                .map(TipoLoginProduto::getTipoLogin)
                .map(TipoPortadorLogin::getTipoLogin)
                .orElseThrow(
                    () ->
                        new GenericServiceException(
                            "Nenhum tipo de login encontrado para este produto!")));
    detalhes
        .getDados()
        .setTipoLoginTela(
            tipoLoginProdutoService.encontraAssociadoPorProduto(id).stream()
                .findFirst()
                .map(TipoLoginProduto::getTipoLogin)
                .map(TipoPortadorLogin::getTipoLoginTela)
                .orElseThrow(
                    () ->
                        new GenericServiceException(
                            "Nenhum tipo de login encontrado para este produto!")));
    detalhes
        .getDados()
        .setTipoProdutoVo(
            tipoProdutoService.buscarTipoProdutoInstituicao(
                prodInstConfig.getTipoProduto().ordinal(), prodInstConfig.getIdInstituicao()));

    return detalhes;
  }

  @Transactional
  public Map<String, Object> atualizarDadosProduto(
      DadosProdutoInstituicao model, SecurityUser user) {
    HashMap<String, Object> map = new HashMap<>();

    if (model.getDescProdInstituicao() != null) {
      model.setDescProdInstituicao(model.getDescProdInstituicao().toUpperCase());
    }

    if (model.getDiferenciacaoLimiteAdicional()
        && BigDecimalUtils.is(model.getPercentualLimiteAdicional()).gt(VALOR_MAXIMO_PERCENTUAL)) {
      throw new GenericServiceException(MSG_ERRO_VALIDACAO_VALOR_PERCENTUAL_ADICIONAL);
    }

    ProdutoInstituicaoConfiguracao prodInstConfig =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            model.getIdProcessadora(), model.getIdProdInstituicao(), model.getIdInstituicao());

    if (!produtoContratadoMccService.permiteMudancaDeConfiguracaoRestricao(
        model.getPermiteRestricaoMcc(), prodInstConfig)) {
      throw new GenericServiceException(
          "Existem restrições de MCC que impedem a atualização do campo Permite Restrição MCC");
    }

    if (model.getIdArranjo() != null) {
      ArranjoPagamento arranjoPagamento =
          arranjoPagamentoService.findByIdArranjo(model.getIdArranjo());
      if (arranjoPagamento == null) {
        throw new GenericServiceException("O Arranjo escolhido não foi encontrado.");
      }

      Integer idBandeiraNova = arranjoPagamento.getArranjoInstituidor().getIdBandeira();
      validaTravaBinElo(model.getIdInstituicao(), model.getBinEstendido(), null, idBandeiraNova);

      if (!model.getIdArranjo().equals(prodInstConfig.getIdArranjo())) {
        List<ProdutoInstituidor> produtoInstituidorDisponiveisNoArranjo =
            produtoInstituidorService.findByArranjo(model.getIdArranjo());
        ProdutoInstituidor produtoInstituidorEdicao =
            model.getIdProdutoInstituidor() != null
                ? produtoInstituidorService.findByIdProdInstituidor(model.getIdProdutoInstituidor())
                : prodInstConfig.getProdutoInstituidor();
        if (produtoInstituidorDisponiveisNoArranjo == null
            || produtoInstituidorDisponiveisNoArranjo.isEmpty()
            || !produtoInstituidorDisponiveisNoArranjo.contains(produtoInstituidorEdicao)) {
          throw new GenericServiceException(
              "O Produto Instituidor deve ser condizente com o novo Arranjo escolhido.");
        }
        prodInstConfig.setArranjoPagamento(arranjoPagamento);
        prodInstConfig.setIdArranjo(model.getIdArranjo());
        trataHistoricoDeArranjos(prodInstConfig, model.getIdArranjo());
        prodInstConfig.setIdBandeira(arranjoPagamento.getArranjoInstituidor().getIdBandeira());
        prodInstConfig.setProdutoInstituidor(produtoInstituidorEdicao);
        prodInstConfig.setIdProdutoInstituidor(produtoInstituidorEdicao.getIdProdInstituidor());
      }
    } else {
      Integer idBandeiraAntiga =
          prodInstConfig.getArranjoPagamento().getArranjoInstituidor().getIdBandeira();
      validaTravaBinElo(model.getIdInstituicao(), model.getBinEstendido(), null, idBandeiraAntiga);
    }

    ProdutoInstituicao proInst =
        findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
            model.getIdProcessadora(), model.getIdInstituicao(), model.getIdProdInstituicao());
    try {

      if (prodInstConfig.getQtdDiasCorte() != null
          && model.getQtdDiasCorte() != null
          && !prodInstConfig.getQtdDiasCorte().equals(model.getQtdDiasCorte())) {
        map.put(
            "msgDTL",
            "A quantidade de dias de corte foi modificada. Esta ação não afetará os cortes já agendados, "
                + "por mais que o dia de vencimento seja excluído e incluído novamente. A quantidade escolhida só entrará em vigor "
                + "na inclusão de dias que ainda não tenham sido inclusos anteriormente ou nos agendamentos do ano seguinte.");
      }

      copyNonNullProperties(model, proInst);
      copyNonNullProperties(model, prodInstConfig);
      prodInstConfig.setBoletoRegistrado(model.getBoletoRegistrado());
      prodInstConfig.setTipoProduto(
          TipoProdutoEnum.findTipoProdutoEnumByIdTipoProduto(
              model.getTipoProdutoVo().getIdTipoProduto()));
      if (model.getServiceCode() != null && model.getServiceCode().toString().length() == 3) {
        prodInstConfig.setServiceCode(model.getServiceCode().toString());
      }
      proInst.setIdUsuarioManutencao(user.getIdUsuario());
      proInst = save(proInst);

      if (model.getVencimentoFaturaList() != null && !model.getVencimentoFaturaList().isEmpty()) {
        StringBuilder prazos = new StringBuilder();
        for (Integer prazo : model.getVencimentoFaturaList()) {
          prazos.append(prazo + ";");
        }
        prodInstConfig.setVencimentosFatura(prazos.toString());
      }

      prodInstConfig.setIdUsuarioManutencao(user.getIdUsuario());
      prodInstConfig = prodInstConfigService.save(prodInstConfig);
    } catch (Exception e) {
      throw new GenericServiceException("Não foi possível atualizar o produto!");
    }

    if (model.getIdProdPlat().equals(PRODUTO_PLATAFORMA_CREDITO)) {

      List<Integer> diaVencimentoFaturaList = model.getVencimentoFaturaList();

      List<Integer> listaDeDiaVencimentoNovos =
          listaDeDiaVencimentoNovos(model.getIdProdInstituicao(), diaVencimentoFaturaList);
      if (!listaDeDiaVencimentoNovos.isEmpty()) {

        gerarFatCalendariosByVencimentos(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getIdProdInstituicao(),
            model.getQtdDiasCorte(),
            listaDeDiaVencimentoNovos,
            user.getIdUsuario());
      }
    }

    updateCardProduct(prodInstConfig, proInst.getDescProdInstituicao());

    map.put("msg", "Dados atualizados com sucesso");

    return map;
  }

  private void trataHistoricoDeArranjos(
      ProdutoInstituicaoConfiguracao prodInstConfig, Integer idArranjo) {
    if (prodInstConfig.getHistoricoIdArranjos() != null
        && !prodInstConfig.getHistoricoIdArranjos().isEmpty()) {
      if (!prodInstConfig.getHistoricoIdArranjos().contains(idArranjo)) {
        List<Integer> historicoArranjosAtualizado = prodInstConfig.getHistoricoIdArranjos();
        historicoArranjosAtualizado.add(idArranjo);
        prodInstConfig.setHistoricoIdArranjos(historicoArranjosAtualizado);
      }
    }
  }

  private List<Integer> listaDeDiaVencimentoNovos(
      Integer idProdInstituicao, List<Integer> diaVencimentoFaturaList) {

    ArrayList<Integer> listaDeDiasVencimentoNovos = new ArrayList();

    for (Integer diaVencimento : diaVencimentoFaturaList) {

      List<FatCalendario> buscarDiaDeVencimentosDoProduto =
          fatCalendarioService.buscarDiaDeVencimentosDoProduto(idProdInstituicao, diaVencimento);

      if (buscarDiaDeVencimentosDoProduto == null || buscarDiaDeVencimentosDoProduto.isEmpty()) {
        listaDeDiasVencimentoNovos.add(diaVencimento);
      }
    }
    return listaDeDiasVencimentoNovos;
  }

  public List<ProdutoInstituicaoResponse> findAllAndProdPlataforma(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    List<ProdutoInstituicaoResponse> prodList =
        getRepository()
            .findByHierarquia(
                idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
    return prodList;
  }

  public List<ResponseProdutosInstituicao> findAllByInstituicao(
      Integer idProcessadora, Integer idInstituicao) {
    List<ProdutoInstituicao> lista =
        getRepository().findAllByProcessadoraAndInstituicao(idProcessadora, idInstituicao);
    return getResponseProdutosInstituicao(lista);
  }

  public List<ResponseProdutosInstituicao> findAllNotB2b(
      Integer idProcessadora, Integer idInstituicao) {
    List<ProdutoInstituicao> lista =
        getRepository().findAllNotB2bPorTipoPessoa(idProcessadora, idInstituicao, PESSOA_FISICA);
    return getResponseProdutosInstituicao(lista);
  }

  public List<ResponseProdutosInstituicao> findAllNotB2bJuridico(
      Integer idProcessadora, Integer idInstituicao) {
    List<ProdutoInstituicao> lista =
        getRepository().findAllNotB2bPorTipoPessoa(idProcessadora, idInstituicao, PESSOA_JURIDICA);
    return getResponseProdutosInstituicao(lista);
  }

  public List<ResponseProdutosInstituicao> findAllNotB2bFiltroObj(BuscaProdutoNaoB2b filtro) {
    if (filtro.getIdPontoDeRelacionamento() != null && filtro.getIdPontoDeRelacionamento() == 0) {
      filtro.setIdPontoDeRelacionamento(null);
    }
    List<ResponseProdutosInstituicao> reps =
        getRepository().getProdutoNotB2bByInstituicaoAndTipoPessoaAndFiltro(filtro, true);
    if (reps == null || reps.isEmpty() && filtro.getIdRegional() != null) {
      filtro.setIdRegional(null);
      filtro.setIdPontoDeRelacionamento(null);
      filtro.setIdFilial(null);
      reps = getRepository().getProdutoNotB2bByInstituicaoAndTipoPessoaAndFiltro(filtro, true);
    }
    return getResponseProdutosInstituicaoResp(reps);
  }

  private List<ResponseProdutosInstituicao> getResponseProdutosInstituicao(
      List<ProdutoInstituicao> lista) {
    List<ResponseProdutosInstituicao> response = new ArrayList<>();
    for (ProdutoInstituicao tmp : lista) {
      ResponseProdutosInstituicao target = new ResponseProdutosInstituicao();
      List<Plastico> plasticos =
          plasticoService.findPlasticos(
              tmp.getIdProcessadora(), tmp.getIdProdInstituicao(), tmp.getIdInstituicao());
      if (!plasticos.isEmpty()) {
        BeanUtils.copyProperties(tmp, target);
        target.setIdadeMinimaPortador(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaPortador());
        target.setIdadeMinimaAdicional(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaAdicional());
        target.setTipoPessoa(tmp.getProdutoInstituicaoConfiguracao().get(0).getTipoPessoa());
        target.setIdProdPlataforma(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
        response.add(target);
      }
    }
    return response;
  }

  public List<ProdutoInstituicaoResponse> findByIntituicaoAndB2b(
      Integer idProcessadora, Integer idInstituicao) {

    List<ProdutoInstituicao> produtos =
        getRepository()
            .findByIdProcessadoraAndIdInstituicaoAndB2bTrue(idProcessadora, idInstituicao);
    List<ProdutoInstituicao> toRemove = new ArrayList();

    for (ProdutoInstituicao tmp : produtos) {
      if (service.condicoesCadastradas(
          tmp.getIdProdInstituicao(), tmp.getIdInstituicao(), tmp.getIdProcessadora())) {
        toRemove.add(tmp);
      }
    }
    for (ProdutoInstituicao pdt : toRemove) {
      produtos.remove(pdt);
    }

    List<ProdutoInstituicaoResponse> response = new ArrayList();
    for (ProdutoInstituicao tmp : produtos) {
      ProdutoInstituicaoResponse target = new ProdutoInstituicaoResponse();
      BeanUtils.copyProperties(tmp, target);
      target.setIdProdutoPlataforma(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
      response.add(target);
    }

    return response;
  }

  public Integer countByIdProcessadora(Integer idProcessadora, String termo) {
    if (!Objects.equals(termo, "")) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        return getRepository()
            .countByIdProcessadoraAndIdProdInstituicaoIn(idProcessadora, termoListInteiros);
      } else {
        return getRepository()
            .countByIdProcessadoraAndDescProdInstituicaoContainingIgnoreCase(idProcessadora, termo);
      }
    } else {
      return getRepository().countByIdProcessadora(idProcessadora);
    }
  }

  public Integer countByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao, String termo) {
    if (!Objects.equals(termo, "")) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        return getRepository()
            .countByIdProcessadoraAndIdInstituicaoAndIdProdInstituicaoIn(
                idProcessadora, idInstituicao, termoListInteiros);
      } else {
        return getRepository()
            .countByIdProcessadoraAndIdInstituicaoAndDescProdInstituicaoContainingIgnoreCase(
                idProcessadora, idInstituicao, termo);
      }
    } else {
      return getRepository().countByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);
    }
  }

  public List<GetPerfisTarifarios> getPerfisTarifariosProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {

    List<GetPerfisTarifarios> retorno = new ArrayList();

    List<ProdutoPerfilTarifario> perfis =
        produtoPerfilTarifarioService.buscarById(idProcessadora, idInstituicao, idProdInstituicao);

    for (ProdutoPerfilTarifario tmp : perfis) {
      GetPerfisTarifarios target = new GetPerfisTarifarios();
      target.setIdPerfilTarifario(tmp.getIdPefilTarifario());
      GetPerfilTariafarioResponse tarifasProduto =
          getByIdPerfilTarifarioAndIdProduto(
              tmp.getPerfilTarifario(), idProdInstituicao, tmp.getIdPefilTarifario());
      target.setTarifasProduto(tarifasProduto);
      target.setDescPerfilTarifario(tmp.getPerfilTarifario().getDescPerfil());
      BeanUtils.copyProperties(tmp, target);
      target.setNivelHierarquia(getNivelHierarquiaPerfil(tmp));
      retorno.add(target);
    }

    return retorno;
  }

  public GetPerfilTariafarioResponse getByIdPerfilTarifarioAndIdProduto(
      PerfilTarifario perfil, Integer idProdInstituicao, Integer idPerfilTarifario) {
    GetPerfilTariafarioResponse response = new GetPerfilTariafarioResponse();

    List<PerfilTarifarioTransacao> perfisTransacao =
        perfilTarifarioTransacaoService.findByPerfilTarifarioIdPerfilTarifario(idPerfilTarifario);
    Set<PerfilTarifarioTransacao> perfilTarifarioTransacoes = new HashSet<>(perfisTransacao);

    for (PerfilTarifarioTransacao perfilTarifarioTransacao : perfilTarifarioTransacoes) {
      GetPerfilTarifario getPerfilTarifario = new GetPerfilTarifario();

      getPerfilTarifario.setIdPerfilTarifario(perfil.getIdPerfilTarifario());
      getPerfilTarifario.setDescPerfil(perfil.getDescPerfil());
      getPerfilTarifario.setValorTarifa(perfilTarifarioTransacao.getValorTarifa());
      getPerfilTarifario.setDescTransacaoReduzida(
          perfilTarifarioTransacao.getProdutoTransacao().getDescReduzida());
      getPerfilTarifario.setCodTransacao(perfilTarifarioTransacao.getId().getCodTransacao());

      response.getPerfilsTarifarios().add(getPerfilTarifario);
    }
    return response;
  }

  private String getNivelHierarquiaPerfil(ProdutoPerfilTarifario tmp) {
    if (tmp.getConta()) {
      return CONTA;
    } else if (tmp.getIdPontoDeRelacionamento() != null) {
      return PONTO_DE_RELACIONAMENTO;
    } else if (tmp.getIdFilial() != null) {
      return FILIAL;
    } else if (tmp.getIdRegional() != null) {
      return REGIONAL;
    } else if (tmp.getIdInstituicao() != null) {
      return INSTITUICAO;
    } else {
      return PROCESSADORA;
    }
  }

  @Transactional
  public ProdutoPerfilTarifario createPerfilTarifarioProduto(
      CreatePerfilTarifario model, SecurityUser user) {

    checkHierarquiaWithNivelHierarquia(
        model.getIdProcessadora(),
        model.getIdInstituicao(),
        model.getIdRegional(),
        model.getIdFilial(),
        model.getIdPontoDeRelacionamento(),
        model.getIdNivelHierarquia());

    if (!model.getConta()) {
      ProdutoPerfilTarifario perfilTarifario = buscarPerfilTarifarioExistente(model);
      if (perfilTarifario != null) {
        throw new GenericServiceException(
            "Já existe um perfil tarifário para a hierarquia adicionada. Descrição do perfil existente: "
                + perfilTarifario.getPerfilTarifario().getDescPerfil());
      }
    }

    PerfilTarifario perfil = new PerfilTarifario();
    perfil.setDescPerfil(model.getDescPerfil());
    perfil.setDtHrInclusao(new Date());
    perfil.setIdUsuarioInclusao(user.getIdUsuario());
    perfil = perfilTarifarioService.saveAndFlush(perfil);

    ProdutoPerfilTarifarioId produtoPerfilId =
        new ProdutoPerfilTarifarioId(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getIdProdInstituicao(),
            perfil.getIdPerfilTarifario());

    ProdutoPerfilTarifario produtoPerfil = new ProdutoPerfilTarifario();
    produtoPerfil.setId(produtoPerfilId);
    produtoPerfil.setIdRegional(model.getIdRegional());
    produtoPerfil.setIdFilial(model.getIdFilial());
    produtoPerfil.setIdPontoDeRelacionamento(model.getIdPontoDeRelacionamento());
    produtoPerfil.setConta(model.getConta());
    produtoPerfil.setDtHrInclusao(new Date());
    produtoPerfil.setIdUsuarioInclusao(user.getIdUsuario());
    produtoPerfil.setIdNivelHierarquia(model.getIdNivelHierarquia());

    produtoPerfil = prodPerfilTarifarioService.saveAndFlush(produtoPerfil);

    List<PerfilTarifarioTransacao> tarifas = new ArrayList();

    // salvar associação entre código transação e perfil tarifário.
    for (AddPerfilTarifarioTransacao tmp : model.getPerfilTransacao()) {
      PerfilTarifarioTransacaoId id = new PerfilTarifarioTransacaoId();
      id.setCodTransacao(tmp.getCodTransacao());
      id.setIdPerfilTarifario(perfil.getIdPerfilTarifario());
      PerfilTarifarioTransacao target = new PerfilTarifarioTransacao();
      target.setId(id);
      target.setValorTarifa(tmp.getValorTarifa());
      target.setIdProdInstituicao(tmp.getIdProdInstituicao());
      target.setAtiva(ATIVA);
      target.setDtHrInclusao(new Date());
      target.setIdUsuarioInclusao(user.getIdUsuario());
      target = perfilTarifarioTransacaoService.saveAndFlush(target);
      tarifas.add(target);
    }

    // criar tarifas no jcard.
    if (!produtoPerfil.getConta()) {
      for (PerfilTarifarioTransacao ptt : tarifas) {
        createTaxProduct(produtoPerfil, ptt.getValorTarifa(), ptt.getId().getCodTransacao());
      }
    }

    return produtoPerfil;
  }

  private void checkHierarquiaWithNivelHierarquia(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idNivelHierarquia) {
    if (!CheckNivelHierarquiaEnum.checkHierarquiaByNivelIsValida(
        idNivelHierarquia,
        idProcessadora != null,
        idInstituicao != null,
        idRegional != null,
        idFilial != null,
        idPontoDeRelacionamento != null)) {
      throw new GenericServiceException(
          "A hierarquia enviada não condiz com o nível hierárquico escolhido.");
    }
  }

  private void createTaxProduct(
      ProdutoPerfilTarifario perfilProduto, BigDecimal valorTarifa, Integer codTransacao) {

    if (perfilProduto.getProdutoInstituicao() == null) {
      perfilProduto.setProdutoInstituicao(
          findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
              perfilProduto.getId().getIdProcessadora(),
              perfilProduto.getId().getIdInstituicao(),
              perfilProduto.getId().getIdProdInstituicao()));
    }

    CreateTaxProduct create =
        TaxProductService.prepareTarifaToCreateTaxProduct(perfilProduto, valorTarifa, codTransacao);
    JcardResponse response = taxProductService.createTaxProduct(create);

    if (!response.getSuccess()) {
      throw new GenericServiceException(
          "Não foi possível criar Tax Product: " + response.getErrors());
    }
  }

  public ProdutoPerfilTarifario buscarPerfilTarifarioExistente(CreatePerfilTarifario model) {

    List<ProdutoPerfilTarifario> perfilTarifarios =
        produtoPerfilTarifarioRepository
            .findByIdProcessadoraAndIdInstituicaoAndIdProdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndContaFalse(
                model.getIdProcessadora(),
                model.getIdInstituicao(),
                model.getIdProdInstituicao(),
                model.getIdRegional(),
                model.getIdFilial(),
                model.getIdPontoDeRelacionamento());

    return !perfilTarifarios.isEmpty() ? perfilTarifarios.get(0) : null;
  }

  public List<ItemFaturaProdutoB2B> getItensFaturaveisProduto(Integer idProdInstituicao) {

    ProdutoInstituicao produto = findByIdProdInstituicao(idProdInstituicao);

    if (produto == null) {
      throw new GenericServiceException(NAO_FOI_POSSIVEL_LOCALIZAR_O_PRODUTO_INSTITUICAO);
    }

    return itemFaturaProdutoB2BService.getItensFaturaveisProduto(
        idProdInstituicao, produto.getIdProcessadora(), produto.getIdInstituicao());
  }

  public List<EventoFaturavelB2B> getEventosFaturaveisDisponiveisProduto(
      Integer idProdInstituicao) {

    ProdutoInstituicao produto = findByIdProdInstituicao(idProdInstituicao);

    if (produto == null) {
      throw new GenericServiceException(NAO_FOI_POSSIVEL_LOCALIZAR_O_PRODUTO_INSTITUICAO);
    }

    List<Integer> exclude =
        itemFaturaProdutoB2BService.getItensFaturaveisProdutoAdicionados(
            idProdInstituicao, produto.getIdProcessadora(), produto.getIdInstituicao());

    return !exclude.isEmpty()
        ? eventoFaturavelB2BService.getEventosFaturaveisDisponiveis(exclude)
        : eventoFaturavelB2BService.getEventosFaturaveis();
  }

  public ItemFaturaProdutoB2B addItemFaturavelProduto(AddItemFaturavelProduto model) {

    ProdutoInstituicao produto = findByIdProdInstituicao(model.getIdProdInstituicao());

    if (produto == null) {
      throw new GenericServiceException(NAO_FOI_POSSIVEL_LOCALIZAR_O_PRODUTO_INSTITUICAO);
    }

    ItemFaturaProdutoB2B item = new ItemFaturaProdutoB2B();
    item.setIdProdutoInstituicao(model.getIdProdInstituicao());
    item.setCodEvento(model.getCodEvento());
    item.setDescItem(model.getDescItem());
    item.setIdProcessadora(produto.getIdProcessadora());
    item.setIdInstituicao(produto.getIdInstituicao());

    item = itemFaturaProdutoB2BService.save(item);

    return item;
  }

  public ItemFaturaProdutoB2B updateItemFaturavelProduto(AddItemFaturavelProduto model) {

    ItemFaturaProdutoB2B item =
        itemFaturaProdutoB2BService.findByIdItemFaturaProduto(model.getIdItemFaturaProduto());

    if (item == null) {
      throw new GenericServiceException("Item faturável não encontrado!");
    }

    item.setDescItem(model.getDescItem());
    item = itemFaturaProdutoB2BService.save(item);

    return item;
  }

  public List<ProdutoInstituicaoResponse> findByUsuarioLogado(SecurityUser user) {
    return getRepository()
        .findByHierarquia(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());
  }

  public List<ProdutoInstituicaoResponse> findByUsuarioLogadoAndTipoPessoaPJ(SecurityUser user) {
    return getRepository()
        .findByHierarquiaAndTipoPessoaPJ(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());
  }

  public List<ProdutoInstituicaoResponse> findByUsuarioLogadoAndEmitePropriaEmpresa(
      SecurityUser user) {
    return getRepository()
        .findByHierarquiaAndEmitePropriaEmpresa(
            user.getIdProcessadora(),
            user.getIdInstituicao(),
            user.getIdRegional(),
            user.getIdFilial(),
            user.getIdPontoDeRelacionamento());
  }

  public List<ProdutoInstituicaoResponse> findByUsuarioLogadoAndQualquerVigencia(
      SecurityUser user) {

    List<ProdutoInstituicao> prodList =
        getRepository()
            .findByHierarquiaAndQualquerVigencia(
                user.getIdProcessadora(),
                user.getIdInstituicao(),
                user.getIdRegional(),
                user.getIdFilial(),
                user.getIdPontoDeRelacionamento());

    List<ProdutoInstituicaoResponse> response = new ArrayList();

    for (ProdutoInstituicao tmp : prodList) {

      ProdutoInstituicaoResponse target = new ProdutoInstituicaoResponse();
      BeanUtils.copyProperties(tmp, target);
      target.setIdProdutoPlataforma(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
      target.setIdRelacionamento(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdRelacionamento());
      response.add(target);
    }

    return response;
  }

  public Integer gerarIdProdutoInstituicao(Integer idProcessadora, Integer idInstituicao) {
    Integer maxIdProdInstituicao =
        getRepository().findMaxIdProdutoByProcessadoraAndInstituicao(idProcessadora, idInstituicao);

    if (maxIdProdInstituicao == null) {
      StringBuilder sb = new StringBuilder();

      sb.append(idInstituicao);
      sb.append("01");

      maxIdProdInstituicao = Integer.valueOf(sb.toString());
    } else {

      maxIdProdInstituicao += 1;
    }

    return maxIdProdInstituicao;
  }

  public List<ResponseProdutosInstituicao> getAllPosPfAndPj(
      Integer idProcessadora, Integer idInstituicao) {
    List<ProdutoInstituicao> lista =
        getRepository().getAllPosPfAndPj(idProcessadora, idInstituicao);
    return prepareReturnProdutoPosPjAndPf(lista);
  }

  public List<ResponseProdutosInstituicao> prepareReturnProdutoPosPjAndPf(
      List<ProdutoInstituicao> lista) {

    List<ResponseProdutosInstituicao> response = new ArrayList();
    for (ProdutoInstituicao tmp : lista) {
      ResponseProdutosInstituicao target = new ResponseProdutosInstituicao();
      BeanUtils.copyProperties(tmp, target);
      target.setTipoPessoa(tmp.getProdutoInstituicaoConfiguracao().get(0).getTipoPessoa());
      target.setIdProdPlataforma(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
      response.add(target);
    }

    return response;
  }

  public List<ResponseProdutosInstituicao> findAllPos(
      Integer idProcessadora, Integer idInstituicao) {

    List<ProdutoInstituicao> lista =
        getRepository().findAllPos(idProcessadora, idInstituicao, PESSOA_FISICA);
    return prepareReturnProdutoPos(lista);
  }

  public List<ResponseProdutosInstituicao> findAllPosPJ(
      Integer idProcessadora, Integer idInstituicao) {

    List<ProdutoInstituicao> lista =
        getRepository().findAllPos(idProcessadora, idInstituicao, PESSOA_JURIDICA);
    return prepareReturnProdutoPos(lista);
  }

  public List<ResponseProdutosInstituicao> prepareReturnProdutoPos(List<ProdutoInstituicao> lista) {

    List<ResponseProdutosInstituicao> response = new ArrayList();
    for (ProdutoInstituicao tmp : lista) {
      ResponseProdutosInstituicao target = new ResponseProdutosInstituicao();
      List<Plastico> plasticos =
          plasticoService.findPlasticos(
              tmp.getIdProcessadora(), tmp.getIdProdInstituicao(), tmp.getIdInstituicao());
      if (!plasticos.isEmpty()) {
        BeanUtils.copyProperties(tmp, target);
        target.setIdadeMinimaPortador(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaPortador());
        target.setIdadeMinimaAdicional(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaAdicional());
        target.setTipoPessoa(tmp.getProdutoInstituicaoConfiguracao().get(0).getTipoPessoa());
        target.setIdProdPlataforma(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
        target.setVencimentoFaturaList(
            tmp.getProdutoInstituicaoConfiguracao().get(0).getVencimentoFaturaList());
        response.add(target);
      }
    }

    return response;
  }

  public ResponseProdutosInstituicao findResumoProdutoById(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {

    ProdutoInstituicao prod =
        getRepository()
            .findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
                idProcessadora, idInstituicao, idProdInstituicao);

    ResponseProdutosInstituicao response = new ResponseProdutosInstituicao();
    BeanUtils.copyProperties(prod, response);
    response.setIdadeMinimaPortador(
        prod.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaPortador());
    response.setIdadeMinimaAdicional(
        prod.getProdutoInstituicaoConfiguracao().get(0).getIdadeMinimaAdicional());
    response.setTipoPessoa(prod.getProdutoInstituicaoConfiguracao().get(0).getTipoPessoa());
    response.setIdProdPlataforma(
        prod.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
    response.setVencimentoFaturaList(
        prod.getProdutoInstituicaoConfiguracao().get(0).getVencimentoFaturaList());
    response.setDiferenciacaoLimiteAdicional(
        prod.getProdutoInstituicaoConfiguracao().get(0).getDiferenciacaoLimiteAdicional());
    response.setPercentualLimiteAdicional(
        prod.getProdutoInstituicaoConfiguracao().get(0).getPercentualLimiteAdicional());

    return response;
  }

  public ProdutoInstituicaoConfiguracaoCredito getConfiguracaoProdutoCredito(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return prodInstConfigCreditoService.getConfiguracaoProdutoCredito(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  public ProdutoInstituicaoConfiguracaoCredito saveConfiguracaoProdutoCredito(
      ProdInstConfigCreditoRequest model) {
    ProdutoInstituicaoConfiguracao prod =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            model.getIdProcessadora(), model.getIdProdInstituicao(), model.getIdInstituicao());

    if (prod.getIdProdutoPlataforma() != PRODUTO_PLATAFORMA_CREDITO) {
      throw new GenericServiceException(
          "Esta configuração aplica-se apenas à produtos de crédito!");
    }
    return prodInstConfigCreditoService.saveConfiguracaoProdutoCredito(model);
  }

  public List<EventoNotificavelProduto> getEventosNotificaveisProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return eventoNotificavelProdutoService.getEventosNotificaveisProduto(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  public void saveEventoNotificavelProduto(EventoNotificavelProduto model) {
    eventoNotificavelProdutoService.saveEventoNotificavelProduto(model);
  }

  public void updateEventoNotificavelProduto(EventoNotificavelProduto model) {
    eventoNotificavelProdutoService.updateEventoNotificavelProduto(model);
  }

  public void deleteEventoNotificavelProduto(Integer id) {
    eventoNotificavelProdutoService.deleteEventoNotificavelProduto(id);
  }

  public List<EventoNotificavel> getEventosNotificaveisDisponiveisToProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {

    ProdutoInstituicaoConfiguracao prod =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            idProcessadora, idProdInstituicao, idInstituicao);

    if (prod == null) {
      throw new GenericServiceException("Produto Instituição não encontrado!");
    }

    return eventoNotificavelProdutoService.getEventosNotificaveisDisponiveisToProduto(
        idProcessadora, idInstituicao, idProdInstituicao, prod);
  }

  public List<ProdutoInstituicaoResponse> findByInstituicaoComVigencia(
      Integer idProcessadora, Integer idInstituicao) {

    List<ProdutoInstituicao> prodList =
        getRepository().findAllByInstituicao(idProcessadora, idInstituicao);

    List<ProdutoInstituicaoResponse> response = new ArrayList();

    for (ProdutoInstituicao tmp : prodList) {
      ProdutoInstituicaoResponse target = new ProdutoInstituicaoResponse();
      BeanUtils.copyProperties(tmp, target);
      target.setIdProdutoPlataforma(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
      target.setIdRelacionamento(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdRelacionamento());
      response.add(target);
    }

    return response;
  }

  public List<ParcelamentoFaturaProduto> getConfigParcFatProdutoCredito(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return parcelamentoFaturaProdutoService.getConfigParcFatProdutoCredito(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  public ParcelamentoFaturaProduto editConfigParcFatProdutoCredito(
      ParcelamentoFaturaProdutoVO model, Integer idUsuario) {
    return parcelamentoFaturaProdutoService.editConfigParcFatProdutoCredito(model, idUsuario);
  }

  public ParcelamentoFaturaProduto addConfigParcFatProdutoCredito(
      ParcelamentoFaturaProdutoVO model, Integer idUsuario) {
    return parcelamentoFaturaProdutoService.addConfigParcFatProdutoCredito(model, idUsuario);
  }

  public List<RenegociacaoDividaProduto> getConfigRenegocDividaProduto(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return renegociacaoDividaProdutoService.getConfigRenegocDividaProduto(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  public List<PlanoSaudeVO> getPlanoSaude(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return planoSaudeService.get(idProcessadora, idInstituicao, idProdInstituicao);
  }

  public void addConfigRenegocDividaProduto(
      AddRenegociacaoDividaProdutoVO model, Integer idUsuario) {
    renegociacaoDividaProdutoService.addConfigRenegocDividaProduto(model, idUsuario);
  }

  public ResponseEntity<String> addPlanoSaude(
      Integer idProcessadora,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idProdInstituicao,
      PlanoSaudeVO model,
      Integer idUsuario) {

    model.setIdProcessadora(idProcessadora);
    model.setIdInstituicao(idInstituicao);
    model.setIdProdInstituicao(idProdInstituicao);
    model.setIdUsuario(idUsuario);

    return planoSaudeService.add(model);
  }

  public List<DeglosePlanoSaudeVO> getDeglosesPlanoSaude(PlanoSaudeProduto planoSaude) {
    return deglosePlanoSaudeService.get(planoSaude);
  }

  public DeglosePlanoSaudeProduto editDesglosePlanoPlanoSaude(DeglosePlanoSaudeVO desglose) {
    return deglosePlanoSaudeService.editDesgloseCodigoContabil(desglose);
  }

  public TaxasRenegociacaoDividaProduto addTaxaRenegocDividaProduto(
      TaxasRenegociacaoDividaProdutoVO model) {
    return taxasRenegociacaoDividaProdutoService.addTaxaRenegocDividaProduto(model);
  }

  public TaxasRenegociacaoDividaProduto editTaxaRenegocDividaProduto(
      TaxasRenegociacaoDividaProdutoVO model) {
    return taxasRenegociacaoDividaProdutoService.editTaxaRenegocDividaProduto(model);
  }

  public List<TaxasRenegociacaoDividaProduto> getTaxasRenegocDividaProduto(Integer idRenegociacao) {
    return taxasRenegociacaoDividaProdutoService.getTaxasRenegocDividaProduto(idRenegociacao);
  }

  public Integer getNextDiaInicialPeriodoRenegocDivida(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    return renegociacaoDividaProdutoService.getNextDiaInicialPeriodoRenegocDivida(
        idProcessadora, idInstituicao, idProdInstituicao);
  }

  public List<ProdutoInstituicaoResponse> findConvenioByUsuarioLogado(SecurityUser user) {

    List<ProdutoInstituicao> prodList =
        getRepository()
            .findConvenioByUsuarioLogado(user.getIdProcessadora(), user.getIdInstituicao());

    List<ProdutoInstituicaoResponse> response = new ArrayList();

    for (ProdutoInstituicao tmp : prodList) {

      ProdutoInstituicaoResponse target = new ProdutoInstituicaoResponse();
      BeanUtils.copyProperties(tmp, target);
      target.setIdProdutoPlataforma(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdProdutoPlataforma());
      target.setIdRelacionamento(
          tmp.getProdutoInstituicaoConfiguracao().get(0).getIdRelacionamento());
      response.add(target);
    }

    return response;
  }

  public Boolean isProdutoB2bByHierarquiaConta(Long idConta) {
    ProdutoInstituicao produto = getRepository().findByIdConta(idConta);

    return produto.getB2b();
  }

  public List<ResponseProdutosInstituicao> findAllNotB2bByFiltros(
      BuscaProdutoNaoB2b model, boolean idRelacionamento) {
    if (model.getIdPontoDeRelacionamento() != null && model.getIdPontoDeRelacionamento() == 0) {
      model.setIdPontoDeRelacionamento(null);
    }
    List<ResponseProdutosInstituicao> reps =
        getRepository()
            .getProdutoNotB2bByInstituicaoAndTipoPessoaAndFiltro(model, idRelacionamento);
    if (reps == null || reps.isEmpty() && model.getIdRegional() != null) {
      model.setIdRegional(null);
      model.setIdPontoDeRelacionamento(null);
      model.setIdFilial(null);
      return getRepository()
          .getProdutoNotB2bByInstituicaoAndTipoPessoaAndFiltro(model, idRelacionamento);
    }
    return reps;
  }

  public List<GetDetalhesProdInstituicao> findByFilterPaginate(
      SecurityUser user, BuscarProdutoByFilter model, Integer first, Integer max) {
    return getRepository().findByFilterPaginate(user, model, first, max);
  }

  public Integer countByFilterPaginate(SecurityUser user, BuscarProdutoByFilter model) {
    return getRepository().countByFilterPaginate(user, model);
  }

  private List<ResponseProdutosInstituicao> getResponseProdutosInstituicaoResp(
      List<ResponseProdutosInstituicao> lista) {
    List<ResponseProdutosInstituicao> response = new ArrayList<>();
    for (ResponseProdutosInstituicao tmp : lista) {
      ResponseProdutosInstituicao target = new ResponseProdutosInstituicao();
      List<Plastico> plasticos =
          plasticoService.findPlasticos(
              tmp.getIdProcessadora(), tmp.getIdProdInstituicao(), tmp.getIdInstituicao());
      if (!plasticos.isEmpty()) {
        BeanUtils.copyProperties(tmp, target);
        target.setIdadeMinimaPortador(tmp.getIdadeMinimaPortador());
        target.setIdadeMinimaAdicional(tmp.getIdadeMinimaAdicional());
        target.setTipoPessoa(tmp.getTipoPessoa());
        target.setIdProdPlataforma(tmp.getIdProdPlataforma());
        response.add(target);
      }
    }
    return response;
  }

  public List<ProdutoInstituicaoResponse> findProdutoByIdGrupo(Long idGrupo) {
    return getRepository().findProdutoByIdGrupo(idGrupo);
  }

  public List<ProdutoInstituicao> listarProdutosSolicitacoes(
      Integer idInstituicao, SecurityUser acessoUsuario) {
    if (idInstituicao == null || acessoUsuario == null) {
      throw new GenericServiceException(
          ConstantesErro.ID_INSTITUICAO_E_ACESSO_USUARIO_NAO_PODE_SER_NULO.getMensagem(),
          HttpStatus.BAD_REQUEST);
    }
    List<ProdutoInstituicao> produtosInstituicao =
        getRepository()
            .findAllByProcessadoraAndInstituicao(acessoUsuario.getIdProcessadora(), idInstituicao);

    if (produtosInstituicao != null) {
      for (ProdutoInstituicao produtoInstituicao : produtosInstituicao) {
        List<LimiteTransacaoSolicitacao> solicitacoes =
            solicitarLimiteContaRepository.getByIdProdInstituicaoAndSituacaoStatusSolicitacao(
                produtoInstituicao.getIdProdInstituicao(), "PENDENTE");
        produtoInstituicao.setQtdSolicitacoesBoleto(
            (solicitacoes.stream()
                .filter(
                    c -> c.getLimiteTransacaoTipo().getId().equals(LimiteTransacaoTipoEnum.BOLETO))
                .count()));
        produtoInstituicao.setQtdSolicitacoesTed(
            (solicitacoes.stream()
                .filter(c -> c.getLimiteTransacaoTipo().getId().equals(LimiteTransacaoTipoEnum.TED))
                .count()));
        produtoInstituicao.setQtdSolicitacoesPix(
            this.solicitarLimitePixRepository
                .findByIdProdutoOrderByDtHrSolicitacaoAsc(produtoInstituicao.getIdProdInstituicao())
                .stream()
                .filter(s -> s.getSituacao().equals("PENDENTE"))
                .count());
      }
      produtosInstituicao =
          produtosInstituicao.stream()
              .filter(
                  c ->
                      c.getQtdSolicitacoesBoleto() > 0
                          || c.getQtdSolicitacoesTed() > 0
                          || c.getQtdSolicitacoesPix() > 0)
              .collect(Collectors.toList());
    }
    return produtosInstituicao;
  }

  public Boolean encontraConfiguracaoRedefinicaoSenhaComCaf(Integer idInstituicao) {
    HierarquiaInstituicao instituicao =
        instService.findByIdProcessadoraAndIdInstituicao(
            Constantes.ID_PROCESSADORA_ITS_PAY, idInstituicao);
    if (instituicao != null) {
      return encontraConfiguracaoRedefinicaoSenhaComCaf(null, instituicao);
    }
    return false;
  }

  public Boolean encontraConfiguracaoRedefinicaoSenhaComCaf(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao) {
    ProdutoInstituicaoConfiguracao produto =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            idProcessadora, idProdInstituicao, idInstituicao);
    if (produto != null) {
      return encontraConfiguracaoRedefinicaoSenhaComCaf(produto);
    }
    return false;
  }

  public Boolean encontraConfiguracaoRedefinicaoSenhaComCaf(ContaPagamento conta) {
    ProdutoInstituicaoConfiguracao produto =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());
    if (produto != null) {
      return encontraConfiguracaoRedefinicaoSenhaComCaf(produto);
    }
    return false;
  }

  public Boolean encontraConfiguracaoRedefinicaoSenhaComCaf(
      ProdutoInstituicaoConfiguracao produto) {
    HierarquiaInstituicao instituicao =
        instService.findByIdProcessadoraAndIdInstituicao(
            produto.getIdProcessadora(), produto.getIdInstituicao());
    if (instituicao != null) {
      return encontraConfiguracaoRedefinicaoSenhaComCaf(produto, instituicao);
    }
    return false;
  }

  public Boolean encontraConfiguracaoRedefinicaoSenhaComCaf(
      ProdutoInstituicaoConfiguracao produto, HierarquiaInstituicao instituicao) {
    if (produto != null && produto.getRedefinirSenhaCaf() != null) {
      return produto.getRedefinirSenhaCaf();
    } else {
      return instituicao.getRedefinirSenhaCaf();
    }
  }

  public List<ContaProdutoLoginResponseVO> listarProdutosELoginsCadastrados(
      Integer idInstituicao, String documento, SecurityUser acessoUsuario) {
    List<ContaProdutoLoginVO> listaLogin =
        getRepository().listarProdutosELoginsCadastrados(idInstituicao, documento);
    if (listaLogin.isEmpty()) {
      throw new GenericServiceException(
          ConstantesErro.PRD_INFORMACOES_LOGIN_CONTAS_NAO_ENCONTRADAS.format(documento));
    }
    Map<String, List<ContaProdutoLoginVO>> contasPorLogin =
        listaLogin.stream().collect(Collectors.groupingBy(ContaProdutoLoginVO::getTipoLogin));
    return contasPorLogin.entrySet().stream()
        .map(
            entry -> {
              String tipoLogin = entry.getKey();
              List<ContaProdutoLoginDetalheVO> contasProdutoVOS =
                  entry.getValue().stream()
                      .map(
                          contaProduto -> {
                            ContaProdutoLoginDetalheVO contasProdutoVO =
                                new ContaProdutoLoginDetalheVO();
                            contasProdutoVO.setIdConta(contaProduto.getIdConta());
                            contasProdutoVO.setIdStatusConta(contaProduto.getIdStatusConta());
                            contasProdutoVO.setDescStatus(contaProduto.getDescStatus());
                            contasProdutoVO.setIdProdInstituicao(
                                contaProduto.getIdProdInstituicao());
                            contasProdutoVO.setDescProdInstituicao(
                                contaProduto.getDescProdInstituicao());
                            return contasProdutoVO;
                          })
                      .collect(Collectors.toList());
              ContaProdutoLoginResponseVO contaProdutoLoginResponseVO =
                  new ContaProdutoLoginResponseVO();
              contaProdutoLoginResponseVO.setTipoLogin(tipoLogin);
              contaProdutoLoginResponseVO.setLoginExiste(
                  entry.getValue().stream().anyMatch(ContaProdutoLoginVO::getLoginExiste));
              contaProdutoLoginResponseVO.setContasProdutos(contasProdutoVOS);
              return contaProdutoLoginResponseVO;
            })
        .collect(Collectors.toList());
  }

  public void alteraStatusProduto(SecurityUser user, AlteraStatusRequest model) {
    ProdutoInstituicao produtoToAlterar = findByIdProdInstituicao(model.getIdProdInstituicao());
    if (produtoToAlterar == null) {
      throw new GenericServiceException(PRD_PRODUTO_NAO_ENCONTRADO.getMensagem());
    }
    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user,
        produtoToAlterar.getIdProcessadora(),
        produtoToAlterar.getIdInstituicao(),
        produtoToAlterar.getIdRegional(),
        produtoToAlterar.getIdFilial(),
        produtoToAlterar.getIdPontoDeRelacionamento());
    produtoToAlterar.setIdStatus(model.getIdStatus());
    save(produtoToAlterar);
  }
}
