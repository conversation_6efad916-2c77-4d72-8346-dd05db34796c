package br.com.sinergico.service.zenvia;

import br.com.client.rest.zenvia.json.bean.GetSmsStatusRespWrapper;
import br.com.client.rest.zenvia.json.bean.SendSmsRequestWrapper;
import br.com.client.rest.zenvia.json.bean.SendSmsResponse;
import br.com.client.rest.zenvia.json.bean.SendSmsResponseWrapper;
import br.com.client.rest.zenvia.json.bean.v2.SendSmsV2Request;
import br.com.client.rest.zenvia.json.bean.v2.SendSmsV2Response;
import br.com.entity.suporte.GatewaySMS;
import br.com.sinergico.service.UtilService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

@Service
public class SmsService extends ZenviaService {

  private static final String URL = "https://api-rest.zenvia.com/services";
  private static final String ENVIAR_SMS = URL + "/send-sms";
  private static final String URL_V2 = "https://api.zenvia.com/v2";
  private static final String ENVIAR_SMS_V2 = URL_V2 + "/channels/sms/messages";
  private static final String CONSULTAR_SMS = URL + "/get-sms-status/";

  @Value("${gateway.zenvia.permiteenviar}")
  private String gatewayZenviaPermiteEnviar;

  @Value("${token.zenvia.v2}")
  private String tokenZenviaV2;

  @Autowired private UtilService utilService;

  private static final Logger log = LoggerFactory.getLogger(SmsService.class);

  public SendSmsResponseWrapper sendSms(SendSmsRequestWrapper request, GatewaySMS gateway) {
    String property = gatewayZenviaPermiteEnviar;

    Boolean permiteEnviar = new Boolean(property);

    log.debug(
        "*******************************************************************************properidadeSMS="
            + permiteEnviar);

    String urlDestino =
        gateway.getUrl() != null && !gateway.getUrl().isEmpty() ? gateway.getUrl() : ENVIAR_SMS;
    log.info("url destino: " + urlDestino);

    if (permiteEnviar) {
      return doPost(
          request,
          new SendSmsResponseWrapper(),
          urlDestino,
          getDefaultHeaders(gateway.getUsuario(), gateway.getSenha()));
    }

    return getDefaultSendSmsResponse(request);
  }

  public SendSmsV2Response sendSmsV2(SendSmsV2Request request, GatewaySMS gateway) {
    String property = gatewayZenviaPermiteEnviar;

    Boolean permiteEnviar = new Boolean(property);

    log.debug("*************************propriedadeSMS=" + permiteEnviar);

    String urlDestino =
        gateway != null && gateway.getUrl() != null && !gateway.getUrl().isEmpty()
            ? gateway.getUrl()
            : ENVIAR_SMS_V2;
    log.info("url destino: " + urlDestino);

    if (permiteEnviar) {
      if (utilService.isAmbienteHomologacao()) {}

      return doPostV2(
          request, new SendSmsV2Response(), urlDestino, getDefaultHeadersV2(tokenZenviaV2));
    }

    return null;
  }

  private SendSmsResponseWrapper getDefaultSendSmsResponse(SendSmsRequestWrapper request) {
    SendSmsResponseWrapper smsW = new SendSmsResponseWrapper();
    SendSmsResponse sendSmsResponse = new SendSmsResponse();
    sendSmsResponse.setStatusCode("01");
    sendSmsResponse.setStatusDescription("Enviado somente para testes");
    smsW.setSendSmsResponse(sendSmsResponse);

    return smsW;
  }

  public GetSmsStatusRespWrapper consultarSms(String id, GatewaySMS gateway) {

    return doGet(
        new GetSmsStatusRespWrapper(),
        CONSULTAR_SMS + id,
        getDefaultHeaders(gateway.getUsuario(), gateway.getSenha()));
  }

  public HttpHeaders getDefaultHeaders(String user, String password) {
    return createHeaders(user, password);
  }

  public HttpHeaders getDefaultHeadersV2(String tokenZenvia) {
    return createHeadersV2(tokenZenvia);
  }
}
