package br.com.sinergico.controller.cadastral;

import static br.com.sinergico.util.Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD;

import br.com.entity.cadastral.Pessoa;
import br.com.entity.suporte.TipoRepresentanteLegal;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.AbreviarNome;
import br.com.json.bean.cadastral.AlterarAdicionalPessoa;
import br.com.json.bean.cadastral.AlterarPessoa;
import br.com.json.bean.cadastral.AlterarPessoaContato;
import br.com.json.bean.cadastral.AlterarPessoaPortador;
import br.com.json.bean.cadastral.AlterarRepresentanteLegal;
import br.com.json.bean.cadastral.CadastrarPessoaFisica;
import br.com.json.bean.cadastral.CadastrarPessoaJuridica;
import br.com.json.bean.cadastral.CadastrarPessoaPDAFRequest;
import br.com.json.bean.cadastral.CadastrarRepresentanteLegalRequest;
import br.com.json.bean.cadastral.ContaPessoaFisica;
import br.com.json.bean.cadastral.CountResponseLong;
import br.com.json.bean.cadastral.FuncionarioProdutoAtivoResponse;
import br.com.json.bean.cadastral.FuncionarioProdutosContas;
import br.com.json.bean.cadastral.FuncionarioProdutosResponse;
import br.com.json.bean.cadastral.FuncionariosModel;
import br.com.json.bean.cadastral.FuncionariosProdutoAtivoModel;
import br.com.json.bean.cadastral.GetPessoaPortadorResponse;
import br.com.json.bean.cadastral.PessoaAdicionalResponse;
import br.com.json.bean.cadastral.PessoaFisica;
import br.com.json.bean.cadastral.ResponavelDependenteContas;
import br.com.json.bean.cadastral.VerificacaoCargaArquivoTO;
import br.com.json.bean.suporte.ValidaTipoCredencial;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.facade.cadastral.PessoaFacade;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.LogCadastroViaArquivoRegistrosErroService;
import br.com.sinergico.service.cadastral.LogCadastroViaArquivoService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.suporte.BuscaGenericaContaPagamentoService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Abreviador;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.vo.LogCadastroViaArquivoFiltroVO;
import br.com.sinergico.vo.LogCadastroViaArquivoReenvioVO;
import br.com.sinergico.vo.LogCadastroViaArquivoRegistrosErroVO;
import br.com.sinergico.vo.LogCadastroViaArquivoVO;
import br.com.sinergico.vo.PessoaPortadorCafVO;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/pessoa")
public class PessoaController extends UtilController {

  @Autowired private PessoaService pessoaService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired private PessoaFacade pessoaFacade;

  @Autowired private BuscaGenericaContaPagamentoService buscaService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private LogCadastroViaArquivoService logCadastroViaArquivoService;

  @Autowired
  private LogCadastroViaArquivoRegistrosErroService logCadastroViaArquivoRegistrosErroService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  private static Logger log = LoggerFactory.getLogger(PessoaController.class);

  @ApiOperation(value = "Serviço responsável por cadastrar uma pessoa física")
  @RequestMapping(
      value = "/fisica",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_CADASTRAR_PESSOA_FISICA"})
  public ResponseEntity<HashMap<String, Object>> createPessoaFisica(
      @RequestBody @Valid CadastrarPessoaFisica model, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    Pessoa pessoa = new Pessoa();
    pessoa = pessoaService.preparePessoaFisica(model, pessoa);

    pessoaService.createPessoaFisica(model, pessoa);
    map.put("msg", "Pessoa cadastrada com sucesso");
    map.put("created", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por contar a quantidade de pessoas fisicas através dos filtros")
  @RequestMapping(
      value = "/fisica/funcionarios/count",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_CONTAR_PESSOAS_FISICAS"})
  public ResponseEntity<CountResponseLong> countFuncionario(
      @RequestBody @Valid FuncionariosModel model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }
    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    Long count = pessoaService.countFuncionariosByhQL(model, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por buscar pessoas fisicas através dos filtros")
  @RequestMapping(
      value = "/fisica/funcionarios",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_BUSCAR_PESSOAS_FISICAS_FILTROS"})
  public ResponseEntity<List<FuncionarioProdutosResponse>> getFuncionario(
      @RequestBody @Valid FuncionariosModel model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    return new ResponseEntity<>(pessoaService.findFuncionariosByhQL(model, user), HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Contar a quantidade de pessoas fisicas, por produto e conta ativa, através dos filtros - HQL")
  @RequestMapping(
      value = "/fisica/funcionarios/ativo/count",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_CONTAR_PESSOAS_FISICAS_CONTA_ATIVA_FILTROS"})
  public ResponseEntity<CountResponseLong> countFuncionarioContaAtivaHql(
      @RequestBody @Valid FuncionariosProdutoAtivoModel model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }
    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    Long count = pessoaService.countFuncionariosContaAtivaByhQL(model, user);

    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar pessoas fisicas com conta ativa por produto - HQL")
  @RequestMapping(
      value = "/fisica/funcionarios/ativo",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_BUSCAR_PESSOAS_FISICAS_CONTA_ATIVA_PRODUTO"})
  public ResponseEntity<List<FuncionarioProdutoAtivoResponse>> getFuncionarioAtivoHql(
      @RequestBody @Valid FuncionariosProdutoAtivoModel model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    return new ResponseEntity<>(
        pessoaService.findFuncionariosContaAtivaByhQL(model, user), HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Salva arquivo enviado via Connect:Direct e verificado pela proc 5000 para cadastro de pessoa física nos Produtos Sociais BRB")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/produtos-sociais/salva/arquivo/{idProdutoInstituicao}")
  @Secured({"ROLE_CADASTRAR_PESSOAS_FISICAS_ARQUIVO"})
  public ResponseEntity<HttpStatus> cadastroProdutosSociais(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer idProdutoInstituicao,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarProdutosSociaisByArquivo(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          nomeSetorFilial,
          user);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar portador por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(value = "Salva arquivo verificado para cadastro de pessoa fisisca")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(method = RequestMethod.POST, path = "/fisica/arquivo/{idProdutoInstituicao}")
  @Secured({"ROLE_CADASTRAR_PESSOAS_FISICAS_ARQUIVO"})
  public ResponseEntity<HttpStatus> createByFile(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer idProdutoInstituicao,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarFuncionariosByArquivo(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          nomeSetorFilial,
          user);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar funcionário por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(value = "Verifica o arquivo para cadastro de pessoa fisica")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/fisica/arquivo/verificacao/{idProdutoInstituicao}")
  @Secured({"ROLE_VERIFICAR_CADASTRO_PESSOAS_FISICAS_ARQUIVO"})
  public ResponseEntity<VerificacaoCargaArquivoTO> verificaImportacaoByFile(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer idProdutoInstituicao,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {

    SecurityUser user = getAuthenticatedUser(request, em);

    VerificacaoCargaArquivoTO to = null;

    try {
      to =
          pessoaService.verificaFuncionariosByArquivo(
              file.getInputStream(),
              file.getOriginalFilename(),
              idProdutoInstituicao,
              nomeSetorFilial,
              user);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar verificar importacao by file. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(to, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Salva arquivo verificado para cadastro completo de de pessoa fisisca, exceto carga padrão")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path =
          "/fisica/arquivo/{arquivoCabecalho}/salvar/{cabalAntigo}/cadastro-completo/{idProdutoInstituicao}")
  @Secured({"ROLE_CADASTRAR_PESSOAS_FISICAS_ARQUIVO"})
  public ResponseEntity<HttpStatus> createCadastroCompletoByFile(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer arquivoCabecalho,
      @PathVariable Integer idProdutoInstituicao,
      @PathVariable Integer cabalAntigo) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarCadastroCompletoByArquivo(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          cabalAntigo,
          user,
          arquivoCabecalho);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar funcionário por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Verifica o arquivo para cadastro completo de pessoa fisica, exceto carga padrão")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path =
          "/fisica/arquivo/{arquivoCabecalho}/verificacao/{cabalAntigo}/cadastro-completo/{idProdutoInstituicao}")
  @Secured({"ROLE_VERIFICAR_CADASTRO_PESSOAS_FISICAS_ARQUIVO"})
  public ResponseEntity<VerificacaoCargaArquivoTO> verificaImportacaoCadastroCompletoByFile(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer arquivoCabecalho,
      @PathVariable Integer idProdutoInstituicao,
      @PathVariable Integer cabalAntigo) {
    // arquivoCabecalho: é 1 para uso de arquivo com cabeçalho e 2 para arquivo sem cabeçalho
    // cabalAntigo:  é 1 para qualquer cabal anterior a data de 30-06-2017 e é 0 para cabal depois
    // desta data ou qualquer visa
    SecurityUser user = getAuthenticatedUser(request, em);

    VerificacaoCargaArquivoTO to = null;

    try {
      to =
          pessoaService.verificaCadastroCompletoByArquivo(
              file.getInputStream(),
              file.getOriginalFilename(),
              idProdutoInstituicao,
              cabalAntigo,
              user,
              arquivoCabecalho);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar verificar importacao by file. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(to, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por cadastrar pessoas jurídicas e representantes legais via arquivo")
  @RequestMapping(value = "/juridica/produto/{idProdutoInstituicao}", method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<HttpStatus> createPessoaJuridicaByArquivo(
      @RequestParam("file") MultipartFile file, @PathVariable Integer idProdutoInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarCadastroPJPorArquivo(
          file.getInputStream(), file.getOriginalFilename(), idProdutoInstituicao, user);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar funcionário por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por cadastrar uma pessoa jurídica")
  @RequestMapping(
      value = "/juridica",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_CADASTRAR_PESSOA_JURIDICA"})
  public ResponseEntity<HashMap<String, Object>> createPessoaJuridica(
      @RequestBody @Valid CadastrarPessoaJuridica model, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    Pessoa pessoa = new Pessoa();
    pessoa = pessoaService.preparePessoaJuridica(model, pessoa);

    pessoaService.createPessoaJuridica(model, pessoa);
    map.put("msg", "Pessoa cadastrada com sucesso");
    map.put("created", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value =
          "Serviço responsável por cadastrar pessoas responsáveis e seus respectivos dependentes por arquivo")
  @RequestMapping(
      value = "/responsavel/produto/{idProdutoInstituicao}",
      method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<HttpStatus> createPessoaResponsavelDependenteByArquivo(
      @RequestParam("file") MultipartFile file, @PathVariable Integer idProdutoInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarCadastroRespDepPorArquivo(
          file.getInputStream(), file.getOriginalFilename(), idProdutoInstituicao, user);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar funcionário por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(
      value =
          "Serviço responsável por vincular pessoas responsáveis já existentes e seus respectivos dependentes por arquivo")
  @RequestMapping(
      value = "/responsavel/vinculo/produto/{idProdutoInstituicao}",
      method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<HttpStatus> vinculoPessoaResponsavelDependenteByArquivo(
      @RequestParam("file") MultipartFile file, @PathVariable Integer idProdutoInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarVinculoRespDepPorArquivo(
          file.getInputStream(), file.getOriginalFilename(), idProdutoInstituicao, user);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar funcionário por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por buscar uma pessoa fisica ",
      response = PessoaFisica.class)
  @RequestMapping(
      value = "/fisica/id/{idPessoa}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_BUSCAR_PESSOA_FISICA_ID"})
  public ResponseEntity<PessoaFisica> findPessoaById(@PathVariable Long idPessoa) {

    PessoaFisica pessoaFisica = pessoaService.findPessoaFisicaById(idPessoa);

    return new ResponseEntity<>(pessoaFisica, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por buscar contas de uma pessoa fisica",
      response = ContaPessoaFisica.class,
      responseContainer = "List")
  @RequestMapping(
      value = "/fisica/id/{idPessoa}/contas",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_BUSCAR_CONTAS_PESSOA_FISICA"})
  public ResponseEntity<List<ContaPessoaFisica>> findContasPessoaFisica(
      @PathVariable Long idPessoa) {

    List<ContaPessoaFisica> contas = pessoaService.findContasPessoaFisica(idPessoa);

    return new ResponseEntity<>(contas, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por alterar uma pessoa")
  @RequestMapping(
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_ALTERAR_PESSOA"})
  public ResponseEntity<HashMap<String, Object>> updatePessoa(
      @RequestBody @Valid AlterarPessoa model, BindingResult result) {

    HashMap<String, Object> map = new HashMap<>();
    SecurityUser user = getAuthenticatedUser(request, em);
    model.setIdUsuarioManutencao(user.getIdUsuario());

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    Pessoa pessoa = pessoaFacade.updatePessoa(model, user, true);

    portadorLoginService.updateEmailPortadorLoginByPessoa(model.getEmail(), pessoa);

    map.put("msg", "Pessoa alterada com sucesso");
    map.put("updated", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value =
          "Serviço responsável por alterar dados permitidos de uma pessoa a partir de um portadorLogin")
  @RequestMapping(
      value = "/alterar-dados-cadastrais",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<HashMap<String, Object>> updatePessoaPortador(
      @RequestBody @Valid AlterarPessoaPortador model, BindingResult result) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    model.setDocumento(userPortador.getCpf());

    pessoaFacade.alterarPessoaPortador(model, userPortador);

    HashMap<String, Object> map = new HashMap<>();

    map.put("msg", "Pessoa alterada com sucesso");
    map.put("updated", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por buscar dados da pessoa associada ao portador")
  @RequestMapping(value = "/buscar-dados-cadastrais", method = RequestMethod.GET)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<HashMap<String, GetPessoaPortadorResponse>> getPessoaPortador() {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    GetPessoaPortadorResponse pessoaPortadorResponse = pessoaFacade.getPessoaLogada(userPortador);

    HashMap<String, GetPessoaPortadorResponse> map = new HashMap<>();
    map.put("dados", pessoaPortadorResponse);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por alterar uma pessoa")
  @RequestMapping(
      value = "/alterar/contato/pessoa",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_ALTERAR_PESSOA"})
  public ResponseEntity<HashMap<String, Object>> updatePessoaContato(
      @RequestBody @Valid AlterarPessoaContato model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_EMAIL);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_CELULAR);

    model.setIdUsuarioManutencao(user.getIdUsuario());
    Pessoa pessoaAlterada = pessoaFacade.updateContatoPessoa(model, user);

    pessoaService.updateEmailPessoaByPessoa(pessoaAlterada);

    portadorLoginService.updateEmailPortadorLoginByPessoa(
        pessoaAlterada.getEmail(), pessoaAlterada);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Pessoa alterada com sucesso");
    map.put("updated", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  /***
   * Focado para o issuerFront
   * @param nome
   * @return
   */
  @ApiOperation(
      value = "Serviço responsável por abreviar o nome de uma pessoa",
      response = String.class)
  @RequestMapping(value = "/abreviar-nome/{nome}", method = RequestMethod.GET)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_ABREVIAR_NOME_PESSOA"})
  public String abreviarNome(@PathVariable(value = "nome") String nome) {
    String nomeAbreviado = Abreviador.abreviarNome(nome);
    return nomeAbreviado;
  }

  /***
   * Focado para o b2bClient/Infinancas
   * @param model
   * @return
   */
  @ApiOperation(
      value = "Serviço responsável por abreviar o nome de uma pessoa",
      response = String.class)
  @RequestMapping(
      value = "/v2/abreviar-nome",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @Secured({"ROLE_ABREVIAR_NOME_PESSOA"})
  public ResponseEntity<String> abreviarNome(@RequestBody AbreviarNome model) {
    String nomeAbreviado = Abreviador.abreviarNome(model.getNome());
    return new ResponseEntity<>(nomeAbreviado, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por buscar o telefone celular de uma pessoa",
      response = String.class)
  @RequestMapping(
      value = "/buscar-telefone-celular/{idPessoa}/{idConta}",
      method = RequestMethod.GET)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public String getTelefoneCelularPessoa(@PathVariable Long idPessoa, @PathVariable Long idConta) {
    return pessoaService.getTelefoneCelularPessoa(idPessoa, idConta);
  }

  @ApiOperation(value = "Serviço responsável por verificar se o nome embossado é válido")
  @RequestMapping(value = "/verificar-nome-embosssado/{nome}", method = RequestMethod.GET)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API key")
  public ResponseEntity<Map<String, Object>> verificaNomeEmbossado(
      @PathVariable(value = "nome") String nome) {
    return pessoaService.verificaNomeEmbossado(nome);
  }

  @ApiOperation(value = "Serviço responsável por alterar uma pessoa")
  @RequestMapping(
      value = "/atualizar-adc",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<HashMap<String, Object>> updatePessoaAdc(
      @RequestBody @Valid AlterarAdicionalPessoa model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_EMAIL);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_CELULAR);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TROCAR_ENDERECO);

    pessoaFacade.updateAdcPessoa(model);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Pessoa Alterada com sucesso");
    map.put("updated", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  // parte de cadastro e alteração de responsaveis legais da conta

  @ApiOperation(value = "Serviço responsável por alterar um representante legal de uma conta")
  @RequestMapping(
      value = "/representante-legal",
      method = RequestMethod.PUT,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_ALTERAR_PESSOA"})
  public ResponseEntity<HashMap<String, Object>> updateRepresentante(
      @RequestBody @Valid AlterarRepresentanteLegal model, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();
    SecurityUser user = getAuthenticatedUser(request, em);
    model.setIdUsuarioManutencao(user.getIdUsuario());

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }
    pessoaFacade.updateRepresentanteLegal(model);

    map.put("msg", "Representante Legal alterada com sucesso");
    map.put("updated", true);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por cadastrar um representante legal de uma conta")
  @RequestMapping(
      value = "/representante-legal",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_ALTERAR_PESSOA"})
  public ResponseEntity<HashMap<String, Object>> createRepresentanteLegal(
      @RequestBody @Valid CadastrarRepresentanteLegalRequest model, BindingResult result) {
    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    if (model.getIdTipoRepresentanteLegal() == null) {
      throw new InvalidRequestException(
          "Dados inválidos: Tipo de representante legal obrigatório.", result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    model.setIdUsuario(user.getIdUsuario());

    pessoaService.createRepresentanteLegal(user, model);
    map.put("msg", "Representante Legal cadastrado com sucesso");
    map.put("created", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por buscar os tipos de representantes legais")
  @RequestMapping(
      value = "/representante-legal/tipo-representante",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  public ResponseEntity<List<TipoRepresentanteLegal>> getTipoRepresentanteLegal() {
    //		HashMap<String, Object> map = new HashMap<>();

    List<TipoRepresentanteLegal> tipos = pessoaFacade.getTiposRepresentanteLegal();

    return new ResponseEntity<>(tipos, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por buscar pessoas e Contas transferíveis")
  @RequestMapping(
      value = "/contas/transferivel/",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_BUSCAR_PESSOAS_FISICAS_FILTROS"})
  public ResponseEntity<List<FuncionarioProdutosContas>> getContasTransferivel(
      @RequestBody @Valid FuncionariosModel model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    return new ResponseEntity<>(
        pessoaService.findFuncionariosContaTransferivel(model, user), HttpStatus.OK);
  }

  @ApiOperation(value = "Verifica o arquivo para cadastro de portadores com produtos integração")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path =
          "/arquivo/{arquivoCabecalho}/portador/{cabalAntigo}/verificacao/{idProdutoInstituicao}")
  public ResponseEntity<VerificacaoCargaArquivoTO> verificarImportacaoCadastroPortador(
      @RequestParam("file") MultipartFile file,
      @RequestParam("idsProdutoInstituicao") List<Integer> idsProdutoInstituicao,
      @PathVariable Integer arquivoCabecalho,
      @PathVariable Integer idProdutoInstituicao,
      @PathVariable Integer cabalAntigo,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {

    SecurityUser user = getAuthenticatedUser(request, em);

    VerificacaoCargaArquivoTO to = null;

    try {
      to =
          pessoaService.verificaCadastroViaArquivo(
              file.getInputStream(),
              file.getOriginalFilename(),
              idProdutoInstituicao,
              cabalAntigo,
              nomeSetorFilial,
              user,
              arquivoCabecalho,
              idsProdutoInstituicao);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar verificar importacao. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(to, HttpStatus.OK);
  }

  @ApiOperation(value = "Salva arquivo para cadastro de portadores para produtos integração")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/salva/{arquivoCabecalho}/cadastro/{cabalAntigo}/arquivo/{idProdutoInstituicao}")
  public ResponseEntity<HttpStatus> salvarPortadorViaArquivo(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer arquivoCabecalho,
      @PathVariable Integer idProdutoInstituicao,
      @PathVariable Integer cabalAntigo,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarPortadorViaArquivo(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          cabalAntigo,
          nomeSetorFilial,
          user,
          arquivoCabecalho);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao tentar salvar funcionário por arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por verifica tipo de pessoa da Conta.")
  @RequestMapping(
      value = "/verifica-tipo-conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public Long getTipoConta(@PathVariable Long idConta) {
    return pessoaService.findTipoConta(idConta);
  }

  @ApiOperation(value = "Serviço responsável por verifica tipo de pessoa da Credencial.")
  @RequestMapping(
      value = "/verifica-tipo-credencial/",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> getTipoCredencial(
      @RequestBody ValidaTipoCredencial credencial, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    return pessoaService.findTipoCredencial(credencial);
  }

  @ApiOperation(value = "Serviço responsável por salvar arquivo de cadastro portador no servidor")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/salva/cadastro/arquivo/{idProdutoInstituicao}",
      method = RequestMethod.POST)
  public ResponseEntity<HttpStatus> salvarArquivoCadastro(
      @RequestParam("file") MultipartFile file,
      @RequestParam("idsProdutoInstituicao") List<Integer> idsProdutoInstituicao,
      @RequestParam(value = "idInstituicao", required = false) Integer idInstituicao,
      @RequestParam(value = "idRegional", required = false) Integer idRegional,
      @RequestParam(value = "idFilial", required = false) Integer idFilial,
      @RequestParam(value = "idPontoDeRelacionamento", required = false)
          Integer idPontoDeRelacionamento,
      @PathVariable Integer idProdutoInstituicao) {

    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarArquivoCadastro(
          user,
          file,
          idInstituicao,
          idRegional,
          idFilial,
          idPontoDeRelacionamento,
          idProdutoInstituicao,
          idsProdutoInstituicao);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao salvar arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Arquivo salvo com sucesso.");
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por salvar arquivo de cadastro Responsavel e Dependente no servidor")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/salva/cadastro/arquivo-responsavel-dependente/"
              + "{idProdInstituicaoDependente}/{idInstituicao}/{idFilial}/{idRegional}/{idPontoDeRelacionamento}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_ARQUIVO_CADASTRO_VINCULO_LOTE"})
  public ResponseEntity<HttpStatus> salvarArquivoCadastroResponsavelDependente(
      @RequestBody MultipartFile file,
      @PathVariable Integer idProdInstituicaoDependente,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idFilial,
      @PathVariable Integer idRegional,
      @PathVariable Integer idPontoDeRelacionamento)
      throws IOException {

    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarArquivoCadastroDependenteResponsavel(
          file,
          idProdInstituicaoDependente,
          idInstituicao,
          idFilial,
          idRegional,
          idPontoDeRelacionamento);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao salvar arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Arquivo salvo com sucesso.");
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por salvar arquivo de vinculo dependente ao responsavel existente no servidor")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value =
          "/salva/vinculo/arquivo-responsavel-dependente/"
              + "{idProdInstituicaoDependente}/{idInstituicao}/{idFilial}/{idRegional}/{idPontoDeRelacionamento}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_ARQUIVO_CADASTRO_VINCULO_LOTE"})
  public ResponseEntity<HttpStatus> salvarArquivoVinculoResponsavelDependente(
      @RequestBody MultipartFile file,
      @PathVariable Integer idProdInstituicaoDependente,
      @PathVariable Integer idInstituicao,
      @PathVariable Integer idFilial,
      @PathVariable Integer idRegional,
      @PathVariable Integer idPontoDeRelacionamento) {

    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarArquivoVinculoDependenteResponsavel(
          file,
          idProdInstituicaoDependente,
          idInstituicao,
          idFilial,
          idRegional,
          idPontoDeRelacionamento);
    } catch (IOException e) {
      log.error("Ocorreu um erro ao salvar arquivo. " + e.getMessage(), e);
      return new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Arquivo salvo com sucesso.");
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por consultar cadastros via arquivo ja processados")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(value = "/consulta/arquivos/processados", method = RequestMethod.POST)
  public ResponseEntity<List<LogCadastroViaArquivoVO>> consultarCadastrosViaArquivo(
      @RequestBody LogCadastroViaArquivoFiltroVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LogCadastroViaArquivoVO> retorno =
        logCadastroViaArquivoService.buscarCadastrosViaArquivo(model, user);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por contar cadastros via arquivo ja processados")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(value = "/count/arquivos/processados", method = RequestMethod.POST)
  public ResponseEntity<CountResponseLong> countCadastrosViaArquivo(
      @RequestBody LogCadastroViaArquivoFiltroVO model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    Long count = logCadastroViaArquivoService.countCadastrosViaArquivo(model, user);
    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsável por consultar cadastros via arquivo já processados por instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/consulta/arquivos/processados/{idInstituicao}",
      method = RequestMethod.POST)
  public ResponseEntity<List<LogCadastroViaArquivoVO>> consultarCadastrosViaArquivoByInstituicao(
      @RequestBody LogCadastroViaArquivoFiltroVO model, @PathVariable Integer idInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);
    List<LogCadastroViaArquivoVO> retorno =
        logCadastroViaArquivoService.buscarCadastrosViaArquivoByInstituicao(
            model, user, idInstituicao);
    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por contar cadastros via arquivo já processados por instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/count/arquivos/processados/{idInstituicao}",
      method = RequestMethod.POST)
  public ResponseEntity<CountResponseLong> countCadastrosViaArquivoByInstituicao(
      @RequestBody LogCadastroViaArquivoFiltroVO model, @PathVariable Integer idInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);
    Long count =
        logCadastroViaArquivoService.countCadastrosViaArquivoByInstituicao(
            model, user, idInstituicao);
    return new ResponseEntity<>(new CountResponseLong(count), HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por consultar cadastros via arquivo com erros")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(value = "/consulta/arquivos/registros/erros/{id}", method = RequestMethod.POST)
  public ResponseEntity<List<LogCadastroViaArquivoRegistrosErroVO>> consultarCadastrosViaArquivo(
      @RequestBody LogCadastroViaArquivoFiltroVO model, @PathVariable Integer id) {

    SecurityUser user = getAuthenticatedUser(request, em);
    List<LogCadastroViaArquivoRegistrosErroVO> retorno =
        logCadastroViaArquivoRegistrosErroService.buscarCadastrosViaArquivoComErros(
            model, user, id);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por reenviar para processamento arquivos que falharam cadastros")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(value = "/reenvia/arquivos/registros/falhas", method = RequestMethod.POST)
  public ResponseEntity<Map<String, Object>> reenviarArquivoComFalha(
      @RequestBody LogCadastroViaArquivoReenvioVO model) {
    Map<String, Object> map = new HashMap<>();
    SecurityUser user = getAuthenticatedUser(request, em);
    map = logCadastroViaArquivoService.reenviarArquivoComFalha(model);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por buscar dados da pessoa em determinada instituicao")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/buscar/pessoa/{documento}/{idInstituicao}", method = RequestMethod.GET)
  public PessoaPortadorCafVO buscarDadosPessoaCafByInstituicao(
      @PathVariable("documento") String documento,
      @PathVariable("idInstituicao") Integer idInstituicao) {
    SecurityUser user = getAuthenticatedUser(request, em);
    return pessoaService.buscarDadosPessoaCafByInstituicao(documento, idInstituicao, user);
  }

  // API para chamada PROC 5000 - Cadastro PDAF
  @ApiOperation(value = "Serviço responsável por cadastrar pessoas e seus adicionais por arquivo")
  @RequestMapping(value = "/pdaf/cadastro-lote/{idProdutoInstituicao}", method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_PROCESSAR_ARQUIVO_CADASTRO_PDAF"})
  public ResponseEntity<Void> createPessoaAndAdicionalByArquivo(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer idProdutoInstituicao,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.salvarCadastroPDAF(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          user,
          nomeSetorFilial);
    } catch (IOException e) {
      log.error(
          ConstantesErro.ERRO_RECEBER_ARQUIVO_CADASTRO_PROC5000_PDAF.getMensagem()
              + e.getMessage());
      throw new GenericServiceException(
          ConstantesErro.ERRO_RECEBER_ARQUIVO_CADASTRO_PROC5000_PDAF.getMensagem());
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  // API para chamada PROC 5000 - Edição PDAF
  @ApiOperation(value = "Serviço responsável por editar pessoas e seus adicionais por arquivo")
  @RequestMapping(value = "/pdaf/edicao-lote/{idProdutoInstituicao}", method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_PROCESSAR_ARQUIVO_CADASTRO_PDAF"})
  public ResponseEntity<Void> editPessoaAndAdicionalByArquivo(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer idProdutoInstituicao,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.editarCadastroPDAF(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          user,
          nomeSetorFilial);
    } catch (IOException e) {
      log.error(
          ConstantesErro.ERRO_RECEBER_ARQUIVO_EDICAO_PROC5000_PDAF.getMensagem() + e.getMessage());
      throw new GenericServiceException(
          ConstantesErro.ERRO_RECEBER_ARQUIVO_EDICAO_PROC5000_PDAF.getMensagem());
    }

    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por inserir pessoas adicionais por arquivo")
  @RequestMapping(
      value = "/pdaf/insere-adicional-lote/{idProdutoInstituicao}",
      method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_PROCESSAR_ARQUIVO_CADASTRO_PDAF"})
  public ResponseEntity<Void> createPessoaAdicionalByArquivo(
      @RequestParam("file") MultipartFile file,
      @PathVariable Integer idProdutoInstituicao,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {
    SecurityUser user = getAuthenticatedUser(request, em);

    try {
      pessoaService.criaAdicionalPDAF(
          file.getInputStream(),
          file.getOriginalFilename(),
          idProdutoInstituicao,
          user,
          nomeSetorFilial);
    } catch (IOException e) {
      log.error(
          ConstantesErro.ERRO_RECEBER_ARQUIVO_ADICIONAL_PROC5000_PDAF.getMensagem()
              + e.getMessage());
      throw new GenericServiceException(
          ConstantesErro.ERRO_RECEBER_ARQUIVO_ADICIONAL_PROC5000_PDAF.getMensagem());
    }
    return new ResponseEntity<>(HttpStatus.CREATED);
  }

  // API chamada pelo serviço PDAF Web para Cadastro Individual
  @ApiOperation(value = "API PDAF disponibilizada para o BRB para criar pessoa e conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/pdaf/criar/{idProduto}/{idFilial}/{idRegional}/{idPontoDeRelacionamento}")
  public ResponseEntity<HashMap<String, Object>> cadastroPessoaPDAF(
      @RequestBody CadastrarPessoaPDAFRequest cadastrar,
      @PathVariable("idProduto") Integer idProdutoInstituicao,
      @PathVariable("idRegional") Integer idRegional,
      @PathVariable("idFilial") Integer idFilial,
      @PathVariable("idPontoDeRelacionamento") Integer idPontoDeRelacionamento) {

    SecurityUser user = getAuthenticatedUser(request, em);
    HashMap<String, Object> map = new HashMap<>();
    try {
      pessoaService.createPessoaPDAF(
          cadastrar,
          user,
          idProdutoInstituicao,
          idRegional,
          idFilial,
          idPontoDeRelacionamento,
          null);
    } catch (Exception e) {
      map.put("msg", "Falha ao criar pessoa");
      map.put("insert", false);
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    map.put("msg", "Pessoa criada com sucesso");
    map.put("insert", true);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  // API chamada pelo serviço PDAF Web para Edição Individual
  @ApiOperation(value = "Serviço responsável por alterar uma pessoa")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      method = RequestMethod.PUT,
      path = "/pdaf/editar/{idProduto}/{idFilial}/{idRegional}/{idPontoDeRelacionamento}")
  @Secured({"ROLE_ALTERAR_PESSOA"})
  public ResponseEntity<HashMap<String, Object>> editarPessoaPDAF(
      @RequestBody CadastrarPessoaPDAFRequest alterarPessoa,
      @PathVariable("idProduto") Integer idProdutoInstituicao,
      @PathVariable("idRegional") Integer idRegional,
      @PathVariable("idFilial") Integer idFilial,
      @PathVariable("idPontoDeRelacionamento") Integer idPontoDeRelacionamento) {
    HashMap<String, Object> map = new HashMap<>();
    SecurityUser user = getAuthenticatedUser(request, em);
    try {
      pessoaFacade.editarPJPDAF(
          alterarPessoa,
          user,
          idRegional,
          idFilial,
          idPontoDeRelacionamento,
          idProdutoInstituicao,
          null);
    } catch (Exception e) {
      map.put("msg", "Falha ao editar pessoa");
      map.put("updated", false);
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    map.put("msg", "Pessoa editada com sucesso");
    map.put("updated", true);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  // API chamada pelo serviço PDAF Web para Inserção de Adicional Individual
  @ApiOperation(
      value = "Insere pessoa adicional para uma conta",
      response = PessoaAdicionalResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cria-adicional/{idProduto}/{idFilial}/{idRegional}/{idPontoDeRelacionamento}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SOLICITAR_CARTAO_ADICIONAL_CONTA"})
  public ResponseEntity<HashMap<String, Object>> insertPessoaAdicionalConta(
      @RequestBody CadastrarPessoaPDAFRequest to,
      @PathVariable("idProduto") Integer idProdutoInstituicao,
      @PathVariable("idRegional") Integer idRegional,
      @PathVariable("idFilial") Integer idFilial,
      @PathVariable("idPontoDeRelacionamento") Integer idPontoDeRelacionamento,
      @RequestParam(name = "nomeSetorFilial", required = false) String nomeSetorFilial) {

    SecurityUser user = getAuthenticatedUser(request, em);
    HashMap<String, Object> map = new HashMap<>();
    try {
      contaPagamentoService.criarPessoaAndCartaoAdicionalByConta(
          to,
          user,
          idRegional,
          idFilial,
          idPontoDeRelacionamento,
          idProdutoInstituicao,
          ID_PRODUCAO_INSTITUICAO_BRBCARD,
          nomeSetorFilial);
    } catch (Exception e) {
      map.put("msg", "Falha ao criar pessoa adicional");
      map.put("insert", false);
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    map.put("msg", "Pessoa Adicional criada com sucesso");
    map.put("insert", true);
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por salvar arquivo PDAF no servidor para a proc 5000")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/salva/arquivo/pdaf-lote",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SALVAR_ARQUIVO_LOTE_PDAF"})
  public ResponseEntity<Void> salvarArquivoPDAF(@RequestParam("file") MultipartFile file) {

    pessoaService.salvarArquivoPDAFLote(file);

    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Serviço responsavel por buscar informações da Conta do Responsavel ou Dependentes vinculados",
      response = ResponavelDependenteContas.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/busca/informacoes/responsavel-dependentes/{idPessoa}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ResponavelDependenteContas> buscaInformacoesResponsavelEDependentes(
      @PathVariable Long idPessoa) {

    List<ResponavelDependenteContas> retorno =
        pessoaService.informacoesResponsavelEDependentes(idPessoa);
    return retorno;
  }

  @ApiOperation(
      value = "Serviço responsavel validar se é Conta do Responsavel ou Dependente ",
      response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/valida/responsavel-dependentes/{idPessoa}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public Boolean confirmaSeResponsavelOuDependente(@PathVariable Long idPessoa) {

    return pessoaService.validaResponsavelOuDependente(idPessoa);
  }
}
