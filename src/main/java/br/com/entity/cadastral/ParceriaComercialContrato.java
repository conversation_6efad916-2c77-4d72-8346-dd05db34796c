package br.com.entity.cadastral;

import br.com.entity.suporte.TipoParceriaComercial;
import java.io.Serializable;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import lombok.ToString;

@Entity
@ToString
@Table(name = "parceria_comercial_contrato", schema = "cadastral")
@SequenceGenerator(
    name = "id_contrato_id_seq",
    sequenceName = "cadastral.id_contrato_id_seq",
    allocationSize = 1)
public class ParceriaComercialContrato implements Serializable {

  @EmbeddedId private ParceriaComercialContratoId parceriaComercialContratoId;

  @ManyToOne
  @JoinColumn(
      name = "id_contrato",
      referencedColumnName = "id_contrato",
      insertable = false,
      updatable = false)
  private ProdutoContratado produtoContratado;

  @ManyToOne
  @JoinColumn(
      name = "id_parceria",
      referencedColumnName = "id",
      insertable = false,
      updatable = false)
  private ParceriaComercial parceria;

  @ManyToOne
  @JoinColumn(name = "id_tipo", referencedColumnName = "id", insertable = false, updatable = false)
  private TipoParceriaComercial tipoParceria;

  //  @Column(name = "id_empresa")
  @Transient private Long idEmpresa;

  @Column(name = "id_status")
  private Integer status;

  public ParceriaComercialContrato() {
    this.setStatus(1);
  }

  public ParceriaComercialContrato(
      ParceriaComercialContratoId parceriaComercialContratoId,
      ProdutoContratado produtoContratado,
      ParceriaComercial parceria,
      TipoParceriaComercial tipoParceria) {
    this.parceriaComercialContratoId = parceriaComercialContratoId;
    this.produtoContratado = produtoContratado;
    this.parceria = parceria;
    this.tipoParceria = tipoParceria;
    this.setStatus(1);
  }

  public ParceriaComercialContrato(
      ParceriaComercialContratoId parceriaComercialContratoId,
      ProdutoContratado produtoContratado,
      ParceriaComercial parceria,
      TipoParceriaComercial tipoParceria,
      Long idEmpresa) {
    this.parceriaComercialContratoId = parceriaComercialContratoId;
    this.produtoContratado = produtoContratado;
    this.parceria = parceria;
    this.tipoParceria = tipoParceria;
    this.idEmpresa = idEmpresa;
    this.setStatus(1);
  }

  public ParceriaComercialContratoId getParceriaComercialContratoId() {
    return parceriaComercialContratoId;
  }

  public void setParceriaComercialContratoId(
      ParceriaComercialContratoId parceriaComercialContratoId) {
    this.parceriaComercialContratoId = parceriaComercialContratoId;
  }

  public ProdutoContratado getProdutoContratado() {
    return produtoContratado;
  }

  public void setProdutoContratado(ProdutoContratado produtoContratado) {
    this.produtoContratado = produtoContratado;
  }

  public ParceriaComercial getParceria() {
    return parceria;
  }

  public void setParceria(ParceriaComercial parceria) {
    this.parceria = parceria;
  }

  public TipoParceriaComercial getTipoParceria() {
    return tipoParceria;
  }

  public void setTipoParceria(TipoParceriaComercial tipoParceria) {
    this.tipoParceria = tipoParceria;
  }

  public Long getIdEmpresa() {
    return idEmpresa;
  }

  public void setIdEmpresa(Long idEmpresa) {
    this.idEmpresa = idEmpresa;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }
}
