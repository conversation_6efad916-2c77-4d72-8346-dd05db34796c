package br.com.sinergico.repository.cadastral.impl;

import br.com.entity.cadastral.PreLancamentoLote;
import br.com.json.bean.cadastral.DetalhePreLancamentoLote;
import br.com.json.bean.cadastral.PreLancamentoLoteResponse;
import br.com.json.bean.cadastral.ReplicacaoLoteAgendadaResponse;
import br.com.json.bean.cadastral.RetornoCargaVO;
import br.com.sinergico.repository.cadastral.PreLancamentoLotesRepositoryCustom;
import br.com.sinergico.repository.impl.AbstractDAO;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.ObjectUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.PedidoB2BFiltroVO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class PreLancamentoLotesRepositoryImpl extends AbstractDAO<PreLancamentoLote, Integer>
    implements PreLancamentoLotesRepositoryCustom {

  private static final Integer QTD_DIAS_ANTES = 46;

  public List<PreLancamentoLote> findByPeriodoAndProdutoAndStatus(PedidoB2BFiltroVO filtroVo) {

    Map<String, Object> params = new HashMap<String, Object>();

    StringBuilder hqlQuery = new StringBuilder();
    hqlQuery.append(
        "from PreLancamentoLote p where p.idProcessadora = :idProcessadora and p.idInstituicao = :idInstituicao ");
    hqlQuery.append(
        "and p.idRegional = :idRegional and p.idFilial = :idFilial and p.idPontoDeRelacionamento = :idPontoRelacionamento ");
    hqlQuery.append(" and (p.tipoPedido is null or p.tipoPedido=0) ");

    params.put("idProcessadora", filtroVo.getIdProcessadora());
    params.put("idInstituicao", filtroVo.getIdInstituicao());
    params.put("idRegional", filtroVo.getIdRegional());
    params.put("idFilial", filtroVo.getIdFilial());
    params.put("idPontoRelacionamento", filtroVo.getIdPontoDeRelacionamento());
    params.put("dataInicial", filtroVo.getDataInicial());
    params.put("dataFinal", filtroVo.getDataFinal());

    if (!ConstantesB2B.PARAMETRO_ZERO.equals(filtroVo.getIdProdInst())) {
      params.put("idProdInst", filtroVo.getIdProdInst());
      hqlQuery.append(" and p.idProdutoInstituicao = :idProdInst ");
    }
    if (!ConstantesB2B.STATUS_INDEFINIDO.equals(filtroVo.getIdStatus())) {
      params.put("idStatus", filtroVo.getIdStatus());
      hqlQuery.append(" and p.status = :idStatus ");
    }
    if (ConstantesB2B.PARAMETRO_ZERO.equals(filtroVo.getIdPrioridade())) {
      hqlQuery.append(" and p.dataHoraInclusao ");
    } else {
      hqlQuery.append(" and p.dataAgendamento ");
    }
    hqlQuery.append("BETWEEN :dataInicial AND :dataFinal ");
    hqlQuery.append("ORDER BY p.dataHoraInclusao DESC");

    return findByParameters(hqlQuery.toString(), params);
  }

  @SuppressWarnings("unchecked")
  @Override
  public <E> E findByProdTransferenciaAndExportacao(PedidoB2BFiltroVO filtroVo, Class<E> clazz) {

    StringBuilder sql = new StringBuilder();
    Map<String, Object> params = new HashMap<>();

    if (filtroVo.getIsCount()) {
      sql.append("select count(pll.id_lote_lanc)");
    } else {
      sql.append(
          "select distinct(pll.id_lote_lanc), pll.dt_hr_inclusao, pll.dt_agendamento, pinst.desc_prod_instituicao, pll.qtd_lanc, pll.vlr_tot_lanc, pll.status, pll.dt_hr_status, pll.dt_hr_ultima_exportacao  ");
    }

    sql.append("from cadastral.pre_lanca_lotes pll ");
    sql.append(
        "inner join cadastral.produto_instituicao pinst on pll.id_prod_instituicao = pinst.id_prod_instituicao ");
    sql.append(
        "inner join cadastral.produto_instituicao_configuracao pic on pinst.id_prod_instituicao = pic.id_prod_instituicao ");
    sql.append("where pll.id_processadora = :processadora ");
    sql.append("and pic.id_prod_plat = :prodPlat ");
    sql.append("and pll.status in (1,2) ");

    params.put("processadora", filtroVo.getIdProcessadora());
    params.put("prodPlat", filtroVo.getIdProdutoPlataforma());
    if (filtroVo.getIdInstituicao() != null) {
      sql.append("and pll.id_instituicao = :instituicao ");
      params.put("instituicao", filtroVo.getIdInstituicao());
    }
    if (filtroVo.getIdRegional() != null
        && !Constantes.ZERO_INTEGER.equals(filtroVo.getIdRegional())) {
      sql.append("and pll.id_regional = :regional ");
      params.put("regional", filtroVo.getIdRegional());
    }
    if (filtroVo.getIdFilial() != null && !Constantes.ZERO_INTEGER.equals(filtroVo.getIdFilial())) {
      sql.append("and pll.id_filial = :filial ");
      params.put("filial", filtroVo.getIdFilial());
    }
    if (filtroVo.getIdPontoDeRelacionamento() != null
        && !Constantes.ZERO_INTEGER.equals(filtroVo.getIdPontoDeRelacionamento())) {
      sql.append("and pll.id_ponto_de_relacionamento = :pontoRelacionamento ");
      params.put("pontoRelacionamento", filtroVo.getIdPontoDeRelacionamento());
    }
    // tipo de pedido que nunca foi exportado
    if (!filtroVo.getFoiExportado()) {
      sql.append("and pll.dt_hr_ultima_exportacao is null ");
    }
    // tipo de pedido que já passou por exportação
    if (filtroVo.getFoiExportado()) {
      sql.append("and pll.dt_hr_ultima_exportacao is not null ");
    }
    // tipo de pedido que já passoun por exportação com período de data
    if (filtroVo.getIsUltimaExportacao()
        && filtroVo.getFoiExportado()
        && filtroVo.getDataFinal() != null
        && filtroVo.getDataInicial() != null) {
      sql.append("and pll.dt_hr_ultima_exportacao <= :dataFim ");
      sql.append("and pll.dt_hr_ultima_exportacao >= :dataInicio ");
      params.put("dataFim", filtroVo.getDataFinal());
      params.put("dataInicio", filtroVo.getDataInicial());
    }

    if (filtroVo.getIsDataInclusao()
        && filtroVo.getDataFinal() != null
        && filtroVo.getDataInicial() != null) {
      sql.append("and pll.dt_hr_inclusao <= :dataFim ");
      sql.append("and pll.dt_hr_inclusao >= :dataInicio ");
      params.put("dataFim", filtroVo.getDataFinal());
      params.put("dataInicio", filtroVo.getDataInicial());
    }
    if (filtroVo.getIsDataAgendamento()
        && filtroVo.getDataFinal() != null
        && filtroVo.getDataInicial() != null) {
      sql.append("and pll.dt_agendamento <= :dataFim ");
      sql.append("and pll.dt_agendamento >= :dataInicio ");
      params.put("dataFim", filtroVo.getDataFinal());
      params.put("dataInicio", filtroVo.getDataInicial());
    }
    if (filtroVo.getNumeroPedidos() != null && !filtroVo.getNumeroPedidos().isEmpty()) {
      sql.append("and pll.id_lote_lanc in :numeroPedidos ");
      List<Integer> listaPedidos =
          filtroVo.getNumeroPedidos().stream()
              .map(s -> Integer.parseInt(s))
              .collect(Collectors.toList());
      params.put("numeroPedidos", listaPedidos);
    }
    if (!filtroVo.getIsCount()) {
      sql.append(" order by id_lote_lanc desc");
    }

    if (filtroVo.getIsCount()) {
      Long resultado = countNativeByParameters(sql.toString(), params);
      return (E) resultado;
    } else {
      int pag = filtroVo.getPagina();
      int qtdReg = filtroVo.getQtdRegistros();
      List<?> result = new ArrayList<>();
      result = findNativeByParametersPaginator(sql.toString(), params, pag, qtdReg);
      return (E) resultPedidos(result);
    }
  }

  private List<PreLancamentoLote> resultPedidos(List<?> results) {
    List<PreLancamentoLote> listPedidos = new ArrayList<>();

    for (int i = 0; i < results.size(); i++) {
      PreLancamentoLote pedido = new PreLancamentoLote();
      Object[] objects = (Object[]) results.get(i);
      pedido.setIdLote(ObjectUtil.objectToInteger(objects[0]));
      pedido.setDataHoraInclusaoString(ObjectUtil.objectToDateTimeStringPTBR((objects[1])));
      pedido.setDataAgendamento(ObjectUtil.objectToDateWithoutHour(objects[2]));
      pedido.setDescProdInstituicao(ObjectUtil.objectToString(objects[3]));
      pedido.setQtdLancamento(ObjectUtil.objectToInteger(objects[4]));
      pedido.setValorTotalLancamento(ObjectUtil.objectToBigDecimal(objects[5]));
      if (ObjectUtil.objectToInteger(objects[6]) == 0) {
        pedido.setDescStatus("Salvo");
      } else if (ObjectUtil.objectToInteger(objects[6]) == 1) {
        pedido.setDescStatus("Confirmado");
      } else if (ObjectUtil.objectToInteger(objects[6]) == 2) {
        pedido.setDescStatus("Processado");
      } else if (ObjectUtil.objectToInteger(objects[6]) == 9) {
        pedido.setDescStatus("Cancelado");
      }
      pedido.setDataHoraStatusString(ObjectUtil.objectToDateTimeStringPTBR((objects[7])));
      pedido.setDataHoraUltimaExportacao(ObjectUtil.objectToLocalDateTime(objects[8]));
      listPedidos.add(pedido);
    }
    ;
    return listPedidos;
  }

  @SuppressWarnings("unchecked")
  @Override
  public List<DetalhePreLancamentoLote> findDetalhePreLancamentoLoteById(
      Integer idLote, Boolean isDetalheCompleto) {

    Map<String, Object> params = new HashMap<>();

    StringBuilder sql = new StringBuilder();

    sql.append("select pl.id_lote_lanc as idLote");
    sql.append(
        " , pll.tipo_emissao_pedido as tipoEmissaoPedido, pll.tipo_pedido as tipoPedido, pll.tipo_carga as idTipoCarga");
    sql.append(
        " , pll.status as status, pll.id_prod_instituicao as idProdInstituicao, pll.dt_agendamento as dataAgendamento");
    sql.append(
        " , pll.qtd_lanc as qtdLancamento, pll.vlr_tot_lanc as valorTotalLancamento, pl.valor_lancamento as valorLancamento");
    if (isDetalheCompleto) {
      sql.append(
          " , p.nome_completo as nomeCompleto, p.documento as documento, pl.id_conta as idConta, p.matricula as matricula");
      sql.append(
          " , pll.id_usuario_inclusao as idUsuarioInclusao, pll.dt_hr_inclusao as dataHoraInclusao");
      sql.append(
          " , pll.dt_hr_manutencao as dataHoraManutencao,  pll.id_usuario_manutencao as idUsuarioManutancao");
      sql.append(
          " , st.desc_setor_filial as descSetorFilial, p.id_setor_filial as idSetorFilial , pinst.desc_prod_instituicao,pinst.id_instituicao,pic.carga_integracao");
      sql.append(
          " , p.chave_pix, p.id_banco, p.id_agencia, p.conta_bancaria_x, p.tipo_conta_bancaria, pl.status as statuslancamento, p.consulta_restrita");
    }
    sql.append(" from cadastral.pre_lancamento as pl");
    sql.append(
        " inner join cadastral.pre_lanca_lotes as pll on (pl.id_lote_lanc = pll.id_lote_lanc)");
    if (isDetalheCompleto) {
      sql.append(
          " inner join cadastral.produto_instituicao as pinst  on  (pinst.id_prod_instituicao = pll.id_prod_instituicao)");
      sql.append(
          " inner join cadastral.produto_instituicao_configuracao as pic on (pic.id_prod_instituicao = pinst.id_prod_instituicao )");
      sql.append(" inner join cadastral.conta_pessoa as cp on (cp.id_conta = pl.id_conta)");
      sql.append(" inner join cadastral.pessoa as p on (p.id_pessoa = cp.id_pessoa)");
      sql.append(
          " inner join cadastral.setor_filial as st on (st.id_setor_filial = p.id_setor_filial)");
    }
    sql.append(" where pl.id_lote_lanc = :idLote");
    params.put("idLote", idLote);

    //		String query = "select p.nome_completo as nomeCompleto," + " p.documento as documento,"
    //                + " p.id_setor_filial as idSetorFilial, " + " pl.id_lote_lanc as idLote," + "
    // pl.id_conta as idConta,"
    //                + " pl.valor_lancamento as valorLancamento," + " pll.dt_hr_inclusao as
    // dataHoraInclusao,"
    //                + " pll.id_usuario_inclusao as idUsuarioInclusao, " + " pll.dt_hr_manutencao
    // as dataHoraManutencao,"
    //                + " pll.id_usuario_manutencao as idUsuarioManutancao, "
    //                + " pll.id_prod_instituicao as idProdInstituicao," + " pll.dt_agendamento as
    // dataAgendamento,"
    //                + " pll.qtd_lanc as qtdLancamento," + " pll.vlr_tot_lanc as
    // valorTotalLancamento,"
    //                + " pll.status as status, pll.tipo_carga as idTipoCarga," + "
    // pinst.desc_prod_instituicao as descProdInstituicao,"
    //                + " p.matricula as matricula," + " st.desc_setor_filial as descSetorFilial, "
    //                + " pll.tipo_emissao_pedido as tipoEmissaoPedido, pll.tipo_pedido as
    // tipoPedido "
    //                + " from cadastral.pre_lancamento as pl"
    //                + " inner join cadastral.pre_lanca_lotes as pll on (pl.id_lote_lanc =
    // pll.id_lote_lanc)"
    //                + " inner join cadastral.produto_instituicao as pinst  on
    // (pinst.id_prod_instituicao = pll.id_prod_instituicao)"
    //                + " inner join cadastral.conta_pessoa as cp on (cp.id_conta = pl.id_conta)"
    //                + " inner join cadastral.pessoa as p on (p.id_pessoa = cp.id_pessoa)"
    //                + " inner join cadastral.setor_filial as st on (st.id_setor_filial =
    // p.id_setor_filial) "
    //
    //                + "	where pll.id_lote_lanc = :idLote";
    List<Object[]> ret = findNativeByParameters(sql.toString(), params);
    ArrayList<DetalhePreLancamentoLote> lotes = new ArrayList<DetalhePreLancamentoLote>();
    for (Object[] o : ret) {
      DetalhePreLancamentoLote detalhe = new DetalhePreLancamentoLote();
      if (o[0] != null) detalhe.setIdLote(ObjectUtil.objectToInteger(o[0]));
      if (o[1] != null) {
        Integer tipoEmissaoCarga = ((BigDecimal) o[1]).intValue();
        detalhe.setTipoEmissaoPedido(tipoEmissaoCarga);
      }
      if (o[2] != null) {
        Integer tipoPedido = ((BigDecimal) o[2]).intValue();
        detalhe.setTipoPedido(tipoPedido);
      }
      if (o[3] != null) {
        Integer tipoCarga = ((BigDecimal) o[3]).intValue();
        detalhe.setIdTipoCarga(tipoCarga);
      }
      if (o[4] != null) detalhe.setStatus((BigDecimal) o[4]);
      if (o[5] != null) detalhe.setIdProdInstituicao((BigDecimal) o[5]);
      if (o[6] != null) detalhe.setDataAgendamento((Date) o[6]);
      if (o[7] != null) detalhe.setQtdLancamento((BigDecimal) o[7]);
      if (o[8] != null) detalhe.setValorTotalLancamento((BigDecimal) o[8]);
      if (o[9] != null) detalhe.setValorLancamento((BigDecimal) o[9]);
      if (isDetalheCompleto) {
        if (o[10] != null) detalhe.setNomeCompleto((String) o[10]);
        if (o[11] != null) detalhe.setDocumento((String) o[11]);
        if (o[12] != null) detalhe.setIdConta((BigDecimal) o[12]);
        if (o[13] != null) detalhe.setMatricula((String) o[13]);
        if (o[14] != null) detalhe.setIdUsuarioInclusao((BigDecimal) o[14]);
        if (o[15] != null) detalhe.setDataHoraInclusao((Date) o[15]);
        if (o[16] != null) detalhe.setDataHoraManutencao((Date) o[16]);
        if (o[17] != null) detalhe.setIdUsuarioManutencao((BigDecimal) o[17]);
        if (o[18] != null) detalhe.setDescSetorFilial((String) o[18]);
        if (o[19] != null) detalhe.setIdSetorFilial((BigDecimal) o[19]);
        if (o[20] != null) detalhe.setDescProdInstituicao((String) o[20]);
        if (o[21] != null) detalhe.setIdInstituicao((BigDecimal) o[21]);
        if (o[22] != null) detalhe.setCargaIntegracao((Boolean) o[22]);
        if (o[23] != null) detalhe.setChavePix((String) o[23]);
        if (o[24] != null) {
          Integer idBanco = ((BigDecimal) o[24]).intValue();
          detalhe.setIdBanco(idBanco);
        }
        if (o[25] != null) {
          Integer idAgencia = ((BigDecimal) o[25]).intValue();
          detalhe.setIdAgencia(idAgencia);
        }
        if (o[26] != null) detalhe.setContaBancariaX((String) o[26]);
        if (o[27] != null) {
          Integer idTipoContaBancaria = ((BigDecimal) o[27]).intValue();
          detalhe.setTipoContaBancaria(idTipoContaBancaria);
        }
        if (o[28] != null) {
          Integer statuslancamento = ((BigDecimal) o[28]).intValue();
          detalhe.setStatuslancamento(statuslancamento);
        }
        if (o[29] != null) {
          detalhe.setConsultaRestrita(ObjectUtil.objectToInteger(o[29]));
        }
      }

      lotes.add(detalhe);
    }
    return lotes;
  }

  @Override
  public List<DetalhePreLancamentoLote> findByPeriodoAndDocumentoAndProdutoAndStatusProcessado(
      PedidoB2BFiltroVO filtroVo) {

    StringBuilder queryStr = new StringBuilder();
    Map<String, Object> params = new HashMap<>();

    queryStr.append(
        "SELECT p.nome_completo, p.documento, pll.id_lote_lanc, sf.desc_setor_filial, prod.desc_prod_instituicao, pll.id_prod_instituicao, pl.id_conta, pl.valor_lancamento, pll.dt_agendamento ");
    queryStr.append("FROM cadastral.pre_lanca_lotes pll ");
    queryStr.append(
        "INNER JOIN cadastral.pre_lancamento pl on (pll.id_lote_lanc = pl.id_lote_lanc) ");
    queryStr.append("INNER JOIN cadastral.conta_pagamento cpag on (pl.id_conta = cpag.id_conta) ");
    queryStr.append(
        "INNER JOIN cadastral.produto_instituicao prod on (prod.id_prod_instituicao = cpag.id_prod_instituicao) ");
    queryStr.append("INNER JOIN cadastral.conta_pessoa cp on (cpag.id_conta = cp.id_conta) ");
    queryStr.append("INNER JOIN cadastral.pessoa p on (p.id_pessoa = cp.id_pessoa) ");
    queryStr.append(
        "INNER JOIN cadastral.setor_filial sf on (p.id_setor_filial = sf.id_setor_filial) ");
    queryStr.append(
        "WHERE p.id_pessoa = cp.id_pessoa AND cpag.id_conta = cp.id_conta AND pl.id_conta = cpag.id_conta ");
    if (Util.isNotNull(filtroVo.getDocumento())) {
      queryStr.append(" AND p.documento = :documento ");
    }
    queryStr.append(" AND pll.status = :statusProcessado ");
    queryStr.append(
        "AND cpag.id_processadora = :idProcessadora and cpag.id_instituicao = :idInstituicao and cpag.id_regional = :idRegional and cpag.id_filial = :idFilial and cpag.id_ponto_de_relacionamento = :idPontoRelacionamento ");
    if (!ConstantesB2B.PARAMETRO_ZERO.equals(filtroVo.getIdProdInst())) {
      queryStr.append(" AND cpag.id_prod_instituicao = :idProdPlat ");
    }
    queryStr.append(
        "AND pll.dt_hr_status BETWEEN :dataInicio AND :dataFim ORDER BY pll.dt_hr_status desc ");
    String consulta = queryStr.toString();

    params.put("dataInicio", filtroVo.getDataInicial());
    params.put("dataFim", filtroVo.getDataFinal());
    params.put("idProcessadora", filtroVo.getIdProcessadora());
    params.put("idInstituicao", filtroVo.getIdInstituicao());
    params.put("idRegional", filtroVo.getIdRegional());
    params.put("idFilial", filtroVo.getIdFilial());
    params.put("idPontoRelacionamento", filtroVo.getIdPontoDeRelacionamento());
    if (Util.isNotNull(filtroVo.getDocumento())) {
      params.put("documento", filtroVo.getDocumento());
    }

    params.put("statusProcessado", ConstantesB2B.STATUS_PROCESSADO);

    if (!ConstantesB2B.PARAMETRO_ZERO.equals(filtroVo.getIdProdInst())) {
      params.put("idProdPlat", filtroVo.getIdProdInst());
    }
    ArrayList<DetalhePreLancamentoLote> lotes = new ArrayList<DetalhePreLancamentoLote>();

    for (Object[] o : findNativeByParameters(consulta, params)) {
      DetalhePreLancamentoLote detalhe = new DetalhePreLancamentoLote();
      detalhe.setNomeCompleto((String) o[0]);
      detalhe.setDocumento((String) o[1]);
      detalhe.setIdLote(ObjectUtil.objectToInteger(o[2]));
      detalhe.setDescSetorFilial((String) o[3]);
      detalhe.setDescProdInstituicao((String) o[4]);
      detalhe.setIdProdInstituicao((BigDecimal) o[5]);
      detalhe.setIdConta((BigDecimal) o[6]);
      detalhe.setValorLancamento((BigDecimal) o[7]);
      detalhe.setDataAgendamento(ObjectUtil.objectToDateWithoutHour(o[8]));

      lotes.add(detalhe);
    }

    return lotes;
  }

  @Override
  public <E> E findOrCountPreLancamentosLotes(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer first,
      Integer max,
      Class<E> clazz) {
    StringBuilder sql = new StringBuilder();
    Map<String, Object> params = new HashMap<>();

    if (first == null && max == null) {
      sql.append("SELECT count(l.id_lote_lanc) ");
      sql.append("FROM cadastral.pre_lanca_lotes l ");
    } else {
      sql.append("SELECT ");
      sql.append("l.id_lote_lanc, ");
      sql.append("l.dt_hr_inclusao, ");
      sql.append("l.id_processadora, ");
      sql.append("l.id_instituicao, ");
      sql.append("l.id_regional, ");
      sql.append("l.id_filial, ");
      sql.append("l.id_ponto_de_relacionamento, ");
      sql.append("l.id_prod_instituicao, ");
      sql.append("l.dt_agendamento, ");
      sql.append("l.cod_transacao, ");
      sql.append("l.qtd_lanc, ");
      sql.append("l.vlr_tot_lanc, ");
      sql.append("l.status, ");
      sql.append("l.dt_hr_status, ");
      sql.append("l.tipo_pagamento, ");
      sql.append("l.id_fatura, ");
      sql.append("l.tipo_carga, ");
      sql.append("l.pgto_manual, ");
      sql.append("l.pgto_normal, ");
      sql.append("l.tipo_emissao_pedido, ");
      sql.append("l.tipo_pedido, ");
      sql.append("l.dt_hr_proc_antes_pgto, ");
      sql.append("p.desc_prod_instituicao ");
      sql.append("FROM cadastral.pre_lanca_lotes l ");
      sql.append(
          "INNER JOIN cadastral.produto_instituicao p on p.id_prod_instituicao = l.id_prod_instituicao ");
    }

    sql.append("WHERE ");
    sql.append("l.id_ponto_de_relacionamento = :idPontoDeRelacionamento ");
    sql.append("AND l.id_filial = :idFilial ");
    sql.append("AND l.id_regional = :idRegional ");
    sql.append("AND l.id_instituicao = :idInstituicao ");
    sql.append("AND l.id_processadora = :idProcessadora ");

    params.put("idPontoDeRelacionamento", idPontoDeRelacionamento);
    params.put("idFilial", idFilial);
    params.put("idRegional", idRegional);
    params.put("idInstituicao", idInstituicao);
    params.put("idProcessadora", idProcessadora);

    if (first == null && max == null) {
      Long resultado = countNativeByParameters(sql.toString(), params);
      return (E) resultado;
    } else {
      sql.append(" ORDER BY l.dt_hr_inclusao DESC , l.id_lote_lanc DESC");
      List<Object[]> resultado =
          (List<Object[]>)
              findNativeByParametersPaginatorObject(sql.toString(), params, first, max);

      return (E) convertNativeQueryObjectToPreLancamentoHistoricoPedidoResponse(resultado);
    }
  }

  public List<PreLancamentoLoteResponse> convertNativeQueryObjectToPreLancamentoResponse(
      List<Object[]> lista) {
    List<PreLancamentoLoteResponse> convertido = new ArrayList<PreLancamentoLoteResponse>();
    for (Object[] obj : lista) {
      PreLancamentoLoteResponse pl =
          new PreLancamentoLoteResponse(
              ObjectUtil.objectToLong(obj[0]),
              ObjectUtil.objectToDate(obj[1]),
              ObjectUtil.objectToInteger(obj[2]),
              ObjectUtil.objectToInteger(obj[3]),
              ObjectUtil.objectToInteger(obj[4]),
              ObjectUtil.objectToInteger(obj[5]),
              ObjectUtil.objectToInteger(obj[6]),
              ObjectUtil.objectToInteger(obj[7]),
              ObjectUtil.objectToDateStringPTBR(obj[8]),
              ObjectUtil.objectToInteger(obj[9]),
              ObjectUtil.objectToInteger(obj[10]),
              ObjectUtil.objectToBigDecimal(obj[11]),
              ObjectUtil.objectToInteger(obj[12]),
              ObjectUtil.objectToDate(obj[13]),
              ObjectUtil.objectToInteger(obj[14]),
              ObjectUtil.objectToLong(obj[15]),
              ObjectUtil.objectToInteger(obj[16]),
              ObjectUtil.objectToInteger(obj[17]),
              ObjectUtil.objectToInteger(obj[18]),
              ObjectUtil.objectToInteger(obj[19]),
              ObjectUtil.objectToInteger(obj[20]),
              ObjectUtil.objectToDate(obj[21]),
              ObjectUtil.objectToString(obj[22]));
      pl.setDescPontoDeRelacionamento(ObjectUtil.objectToString(obj[23]));
      convertido.add(pl);
    }
    return convertido;
  }

  public List<PreLancamentoLoteResponse>
      convertNativeQueryObjectToPreLancamentoHistoricoPedidoResponse(List<Object[]> lista) {
    List<PreLancamentoLoteResponse> convertido = new ArrayList<PreLancamentoLoteResponse>();
    for (Object[] obj : lista) {
      convertido.add(
          new PreLancamentoLoteResponse(
              ObjectUtil.objectToLong(obj[0]),
              ObjectUtil.objectToDate(obj[1]),
              ObjectUtil.objectToInteger(obj[2]),
              ObjectUtil.objectToInteger(obj[3]),
              ObjectUtil.objectToInteger(obj[4]),
              ObjectUtil.objectToInteger(obj[5]),
              ObjectUtil.objectToInteger(obj[6]),
              ObjectUtil.objectToInteger(obj[7]),
              ObjectUtil.objectToDateStringPTBR(obj[8]),
              ObjectUtil.objectToInteger(obj[9]),
              ObjectUtil.objectToInteger(obj[10]),
              ObjectUtil.objectToBigDecimal(obj[11]),
              ObjectUtil.objectToInteger(obj[12]),
              ObjectUtil.objectToDate(obj[13]),
              ObjectUtil.objectToInteger(obj[14]),
              ObjectUtil.objectToLong(obj[15]),
              ObjectUtil.objectToInteger(obj[16]),
              ObjectUtil.objectToInteger(obj[17]),
              ObjectUtil.objectToInteger(obj[18]),
              ObjectUtil.objectToInteger(obj[19]),
              ObjectUtil.objectToInteger(obj[20]),
              ObjectUtil.objectToDate(obj[21]),
              ObjectUtil.objectToString(obj[22])));
    }
    return convertido;
  }

  @Override
  public List<PreLancamentoLoteResponse> findLotesReplicaveis() {
    StringBuilder query = new StringBuilder();

    query.append("SELECT ");
    query.append("		pll.id_lote_lanc as idLote, ");
    query.append("		pll.dt_hr_inclusao as dtInc, ");
    query.append("		pll.id_processadora as idProc, ");
    query.append("		pll.id_instituicao as idInst, ");
    query.append("		pll.id_regional as idReg, ");
    query.append("		pll.id_filial as idFil, ");
    query.append("		pll.id_ponto_de_relacionamento as idPonRel, ");
    query.append("		pll.id_prod_instituicao as idProdInst, ");
    query.append("		pll.dt_agendamento as dtAgend, ");
    query.append("		pll.cod_transacao as codTran, ");
    query.append("		pll.qtd_lanc as qtdLan, ");
    query.append("		pll.vlr_tot_lanc as vlrLan, ");
    query.append("		pll.status as status, ");
    query.append("		pll.dt_hr_status as dtStatus, ");
    query.append("		pll.tipo_pagamento as tipPagto, ");
    query.append("		pll.id_fatura as idFat, ");
    query.append("		pll.tipo_carga as tipoCarga, ");
    query.append("		pll.pgto_manual as pagtoMan, ");
    query.append("		pll.pgto_normal as pagtoNorm, ");
    query.append("		pll.tipo_emissao_pedido as tipEmi, ");
    query.append("		pll.tipo_pedido as tipPed, ");
    query.append("		pll.dt_hr_proc_antes_pgto as dtProcAnPagto, ");
    query.append("		p.desc_prod_instituicao as descProdInst, ");
    query.append("		hpr.desc_ponto_de_relacionamento as descPonRel");

    query.append("		from cadastral.pre_lanca_lotes pll  ");
    query.append("		 	inner join cadastral.produto_instituicao_correspondente picc  ");
    query.append("		 		on picc.id_prod_instituicao_orig = pll.id_prod_instituicao ");
    query.append("		 	inner join cadastral.produto_instituicao p  ");
    query.append("		 		on picc.id_prod_instituicao_orig = p.id_prod_instituicao  ");
    query.append("		 	  inner join  suporte.hierarquia_ponto_de_relacionamento hpr    ");
    query.append("		    			                 on hpr.id_instituicao =pll.id_instituicao   ");
    query.append("		    			                 and  hpr.id_regional = pll.id_regional   ");
    query.append("		    			                 and  hpr.id_filial=pll.id_filial  ");
    query.append(
        "		    			                 and  hpr.id_ponto_de_relacionamento = pll.id_ponto_de_relacionamento ");
    query.append("		    			        inner join suporte.hierarquia_ponto_de_relacionamento hpr2   ");
    query.append(
        "		    			                 on hpr2.id_instituicao = hpr.id_instituicao_corresp   ");
    query.append("		    			                 and  hpr2.id_regional = hpr.id_regional_corresp   ");
    query.append("		    			                 and  hpr2.id_filial = hpr.id_filial_corresp   ");
    query.append(
        "		    			                 and  hpr2.id_ponto_de_relacionamento = hpr.id_ponto_de_relacionamento_corresp   ");
    query.append("		    			        inner join cadastral.produto_contratado pc on    ");
    query.append("		    			        pc.id_prod_instituicao = pll.id_prod_instituicao   ");
    query.append("		    			        and pc.id_instituicao = pll.id_instituicao  ");
    query.append("		    			                 and  pc.id_regional = pll.id_regional   ");
    query.append("		    			                 and  pc.id_filial=pll.id_filial  ");
    query.append(
        "		    			                 and  pc.id_ponto_de_relacionamento=pll.id_ponto_de_relacionamento ");
    query.append("		   where hpr.id_processadora_corresp is not null   ");
    query.append("		    			      and hpr.id_instituicao_corresp is not null   ");
    query.append("		    			      and hpr.id_regional_corresp is not null   ");
    query.append("		    			      and hpr.id_filial_corresp is not null   ");
    query.append("		    			      and hpr.id_ponto_de_relacionamento_corresp is not null   ");
    query.append("		    			      and hpr2.status = 1  ");
    query.append("		    			      and hpr2.id_instituicao = 2401  ");
    query.append("		    			      and pll.status = 1  ");
    query.append("		    			      and pll.pgto_manual=1  ");
    query.append("		    			      and pll.id_lote_lanc_corresp is null ");
    query.append("		    			      and pll.dt_hr_inclusao > :dataInicial ");
    query.append("		    			      and pc.id_contrato_corresp is not null ");
    query.append("		    			      and pll.id_lote_lanc not in( ");
    query.append(
        "		    			      select src.id_lote_lanc from suporte.solicitacao_replicacao_carga_resumo src");
    query.append(" where src.id_job_replicacao is null and src.status=0");
    query.append("		    			      )  and picc.id_instituicao_dest = 2401 ");
    query.append("		    			         and pc.dt_hr_cancelamento is null ");
    query.append("union all");
    query.append("	SELECT ");
    query.append("		pll.id_lote_lanc as idLote, ");
    query.append("		pll.dt_hr_inclusao as dtInc, ");
    query.append("		pll.id_processadora as idProc, ");
    query.append("		pll.id_instituicao as idInst, ");
    query.append("		pll.id_regional as idReg, ");
    query.append("		pll.id_filial as idFil, ");
    query.append("		pll.id_ponto_de_relacionamento as idPonRel, ");
    query.append("		pll.id_prod_instituicao as idProdInst, ");
    query.append("		pll.dt_agendamento as dtAgend, ");
    query.append("		pll.cod_transacao as codTran, ");
    query.append("		pll.qtd_lanc as qtdLan, ");
    query.append("		pll.vlr_tot_lanc as vlrLan, ");
    query.append("		pll.status as status, ");
    query.append("		pll.dt_hr_status as dtStatus, ");
    query.append("		pll.tipo_pagamento as tipPagto, ");
    query.append("		pll.id_fatura as idFat, ");
    query.append("		pll.tipo_carga as tipoCarga, ");
    query.append("		pll.pgto_manual as pagtoMan, ");
    query.append("		pll.pgto_normal as pagtoNorm, ");
    query.append("		pll.tipo_emissao_pedido as tipEmi, ");
    query.append("		pll.tipo_pedido as tipPed, ");
    query.append("		pll.dt_hr_proc_antes_pgto as dtProcAnPagto, ");
    query.append("		p.desc_prod_instituicao as descProdInst, ");
    query.append("		hpr.desc_ponto_de_relacionamento as descPonRel");

    query.append("		from cadastral.pre_lanca_lotes pll  ");
    query.append("		 	inner join cadastral.produto_instituicao_correspondente picc  ");
    query.append("		 		on picc.id_prod_instituicao_orig = pll.id_prod_instituicao ");
    query.append("		 	inner join cadastral.produto_instituicao p  ");
    query.append("		 		on picc.id_prod_instituicao_orig = p.id_prod_instituicao  ");
    query.append("		 	  inner join  suporte.hierarquia_ponto_de_relacionamento hpr   ");
    query.append("		    			                 on hpr.id_instituicao =pll.id_instituicao   ");
    query.append("		    			                 and  hpr.id_regional = pll.id_regional   ");
    query.append("		    			                 and  hpr.id_filial=pll.id_filial  ");
    query.append(
        "		    			                 and  hpr.id_ponto_de_relacionamento = pll.id_ponto_de_relacionamento ");
    query.append("		    			        inner join suporte.hierarquia_ponto_de_relacionamento hpr2   ");
    query.append(
        "		    			                 on hpr2.id_instituicao = hpr.id_instituicao_corresp_2  ");
    query.append("		    			                 and  hpr2.id_regional = hpr.id_regional_corresp_2   ");
    query.append("		    			                 and  hpr2.id_filial = hpr.id_filial_corresp_2   ");
    query.append(
        "		    			                 and  hpr2.id_ponto_de_relacionamento = hpr.id_ponto_de_relacionamento_corresp_2 ");
    query.append("		    			        inner join cadastral.produto_contratado pc on    ");
    query.append("		    			        pc.id_prod_instituicao = pll.id_prod_instituicao  ");
    query.append("		    			        and pc.id_instituicao = pll.id_instituicao  ");
    query.append("		    			                 and  pc.id_regional = pll.id_regional   ");
    query.append("		    			                 and  pc.id_filial=pll.id_filial  ");
    query.append(
        "		    			                 and  pc.id_ponto_de_relacionamento=pll.id_ponto_de_relacionamento ");
    query.append(
        "		      left join cadastral.b2b_fatura_pix bbfp on pll.id_lote_lanc = bbfp.id_lote_lanc ");

    query.append("		   where hpr.id_processadora_corresp_2 is not null   ");
    query.append("		    			      and hpr.id_instituicao_corresp_2 is not null   ");
    query.append("		    			      and hpr.id_regional_corresp_2 is not null   ");
    query.append("		    			      and hpr.id_filial_corresp_2 is not null   ");
    query.append("		    			      and hpr.id_ponto_de_relacionamento_corresp_2 is not null   ");
    query.append("		    			      and hpr2.status in (1,8)  ");
    query.append("		    			      and hpr2.id_instituicao = 2801  ");
    query.append("		    			      and pll.status in (0,1)  ");
    query.append("		    			      and (pll.pgto_manual=1 or bbfp.id_status = 1)  ");
    query.append("		    			      and pll.id_lote_lanc_corresp is null ");
    query.append("		    			      and pll.dt_hr_inclusao > :dataInicial ");
    query.append("		    			      and pc.id_contrato_corresp is not null ");
    query.append("		    			      and pll.id_lote_lanc not in( ");
    query.append("		    			      select src.id_lote_lanc ");
    query.append(
        "		    			      from suporte.solicitacao_replicacao_carga_resumo src where src.id_job_replicacao is null and src.status=0");
    query.append("		    			      )  and picc.id_instituicao_dest = 2801");
    query.append("		    			         and pc.dt_hr_cancelamento is null ");

    Date dataInicial = DateUtil.diminuirDias(new Date(), 45);

    Map params = new HashMap<>();
    params.put("dataInicial", dataInicial);

    List<Object[]> resultado = (List<Object[]>) findNativeByParameters(query.toString(), params);

    return (List<PreLancamentoLoteResponse>)
        convertNativeQueryObjectToPreLancamentoResponse(resultado);
  }

  @Override
  public List<ReplicacaoLoteAgendadaResponse> findAgendamentosReplicacao() {
    StringBuilder query = new StringBuilder();

    query.append("SELECT ");
    query.append("		pll.id_lote_lanc as idExistente, ");
    query.append("		pll.dt_hr_inclusao as dt_hr_lote, ");
    query.append("		pll.id_processadora as idProc, ");
    query.append("		pll.id_instituicao as idInst, ");
    query.append("		pll.id_regional as idReg, ");
    query.append("		pll.id_filial as idFil,");
    query.append("		pll.id_ponto_de_relacionamento as idPonRel, ");
    query.append("		pll.id_prod_instituicao as idProdInst, ");
    query.append("		pll.dt_agendamento as dtAgen, ");
    query.append("		pll.cod_transacao as codTran,");
    query.append("		pll.qtd_lanc as qtdLan, ");
    query.append("		pll.vlr_tot_lanc as vlrLan, ");
    query.append("		pll.status as statusLote, ");
    query.append("		pll.dt_hr_status as dt_hr_status_lote, ");
    query.append("		pll.tipo_pagamento as tipPagto, ");
    query.append("		pll.id_fatura as idFat, ");
    query.append("		pll.tipo_carga as tipCarg, ");
    query.append("		pll.pgto_manual as pgtoMan, ");
    query.append("		pll.pgto_normal as pgtoNorm, ");
    query.append("		pll.tipo_emissao_pedido as tipEmPed, ");
    query.append("		pll.tipo_pedido as tipPed, ");
    query.append("		pll.dt_hr_proc_antes_pgto as dtPag, ");
    query.append("		p.desc_prod_instituicao as descProdInst, ");
    query.append("		src.id_solicitacao_replicacao_carga_resumo as idSolRepl,");
    query.append("		src.id_lote_lanc as idLotLanc,");
    query.append("		src.id_lote_lanc_corresp as idLotLancCorresp,");
    query.append("		src.id_usuario_inclusao as idUsuInc,");
    query.append("		src.id_usuario_manutencao as idUsManu,");
    query.append("		src.id_job_replicacao as idJobRepl,");
    query.append("		src.status as statusResumo,");
    query.append("		src.dt_hr_inclusao as dtHrIncl,");
    query.append("		src.dt_hr_status as dtStat,");
    query.append("		src.dt_hr_inicio as dtIni,");
    query.append("		src.dt_hr_fim as dtFim,");
    query.append("		src.cod_resultado as codRes,");
    query.append("		src.desc_resultado as descRes, ");
    query.append("		hpr.desc_ponto_de_relacionamento as desPonRel, ");
    query.append("		plldestino.status as statusDestino, ");
    query.append("		cb.id_cobranca_bancaria as idCobrBanc, ");
    query.append("		cb.data_pagamento as dtPagto, ");
    query.append("		cb.valor_pago as vlPagto ");
    query.append("		 from suporte.solicitacao_replicacao_carga_resumo src   ");
    query.append(
        "		  inner join cadastral.pre_lanca_lotes pll on src.id_lote_lanc = pll.id_lote_lanc   ");
    query.append(
        "		  left join  cadastral.pre_lanca_lotes plldestino on src.id_lote_lanc_corresp = plldestino.id_lote_lanc ");
    query.append("		 	inner join cadastral.produto_instituicao_correspondente picc  ");
    query.append("		 		on picc.id_prod_instituicao_orig = pll.id_prod_instituicao ");
    query.append("		 	inner join cadastral.produto_instituicao p  ");
    query.append("		 		on picc.id_prod_instituicao_orig = p.id_prod_instituicao  ");
    query.append("		 	  inner join  suporte.hierarquia_ponto_de_relacionamento hpr   ");
    query.append("		    			                 on hpr.id_instituicao =pll.id_instituicao   ");
    query.append("		    			                 and  hpr.id_regional = pll.id_regional   ");
    query.append("		    			                 and  hpr.id_filial=pll.id_filial  ");
    query.append(
        "		    			                 and  hpr.id_ponto_de_relacionamento = pll.id_ponto_de_relacionamento ");
    query.append("		    			        inner join suporte.hierarquia_ponto_de_relacionamento hpr2   ");
    query.append(
        "		    			                 on hpr2.id_instituicao = hpr.id_instituicao_corresp   ");
    query.append("		    			                 and  hpr2.id_regional = hpr.id_regional_corresp  ");
    query.append("		    			                 and  hpr2.id_filial = hpr.id_filial_corresp  ");
    query.append(
        "		    			                 and  hpr2.id_ponto_de_relacionamento = hpr.id_ponto_de_relacionamento_corresp    ");
    query.append(
        "		      left join cadastral.b2b_fatura fat on fat.id_fatura =  plldestino.id_fatura  ");
    query.append(
        "				left join transacional.cobranca_bancaria cb on fat.id_cobranca_bancaria = cb.id_cobranca_bancaria  ");
    query.append("		   where  pll.id_lote_lanc in(  ");
    query.append(
        "		    			      select srcc.id_lote_lanc as idExistente from suporte.solicitacao_replicacao_carga_resumo srcc ");
    query.append("		    			      where srcc.dt_hr_inclusao >= :diasAtras ");
    query.append("		    			      )  and picc.id_instituicao_dest = 2401  ");
    query.append(" ");
    query.append("		    			union all  ");
    query.append(" ");
    query.append("		    			SELECT  ");
    query.append("		pll.id_lote_lanc as idExistente,  ");
    query.append("		pll.dt_hr_inclusao as dt_hr_lote,  ");
    query.append("		pll.id_processadora as idProc,  ");
    query.append("		pll.id_instituicao as idInst,  ");
    query.append("		pll.id_regional as idReg,  ");
    query.append("		pll.id_filial as idFil,  ");
    query.append("		pll.id_ponto_de_relacionamento as idPonRel,  ");
    query.append("		pll.id_prod_instituicao as idProdInst,  ");
    query.append("		pll.dt_agendamento as dtAgen,  ");
    query.append("		pll.cod_transacao as codTran,  ");
    query.append("		pll.qtd_lanc as qtdLan,  ");
    query.append("		pll.vlr_tot_lanc as vlrLan,  ");
    query.append("		pll.status as statusLote,  ");
    query.append("		pll.dt_hr_status as dt_hr_status_lote,  ");
    query.append("		pll.tipo_pagamento as tipPagto,  ");
    query.append("		pll.id_fatura as idFat,  ");
    query.append("		pll.tipo_carga as tipCarg,  ");
    query.append("		pll.pgto_manual as pgtoMan,  ");
    query.append("		pll.pgto_normal as pgtoNorm,  ");
    query.append("		pll.tipo_emissao_pedido as tipEmPed,  ");
    query.append("		pll.tipo_pedido as tipPed,  ");
    query.append("		pll.dt_hr_proc_antes_pgto as dtPag,  ");
    query.append("		p.desc_prod_instituicao as descProdInst,  ");
    query.append("		src.id_solicitacao_replicacao_carga_resumo as idSolRepl, ");
    query.append("		src.id_lote_lanc as idLotLanc, ");
    query.append("		src.id_lote_lanc_corresp as idLotLancCorresp, ");
    query.append("		src.id_usuario_inclusao as idUsuInc, ");
    query.append("		src.id_usuario_manutencao as idUsManu, ");
    query.append("		src.id_job_replicacao as idJobRepl, ");
    query.append("		src.status as statusResumo, ");
    query.append("		src.dt_hr_inclusao as dtHrIncl, ");
    query.append("		src.dt_hr_status as dtStat, ");
    query.append("		src.dt_hr_inicio as dtIni, ");
    query.append("		src.dt_hr_fim as dtFim, ");
    query.append("		src.cod_resultado as codRes, ");
    query.append("		src.desc_resultado as descRes,  ");
    query.append("		hpr.desc_ponto_de_relacionamento as descPonRel,  ");
    query.append("		plldestino.status as statusDestino,  ");
    query.append("		cb.id_cobranca_bancaria as idCobrBanc,  ");
    query.append("		cb.data_pagamento as dtPagto,  ");
    query.append("		cb.valor_pago as vlPagto  ");
    query.append("		 from suporte.solicitacao_replicacao_carga_resumo src    ");
    query.append(
        "		  inner join cadastral.pre_lanca_lotes pll on src.id_lote_lanc = pll.id_lote_lanc   ");
    query.append(
        "		  left join  cadastral.pre_lanca_lotes plldestino on src.id_lote_lanc_corresp = plldestino.id_lote_lanc    ");
    query.append("		 	inner join cadastral.produto_instituicao_correspondente picc   ");
    query.append("		 		on picc.id_prod_instituicao_orig = pll.id_prod_instituicao  ");
    query.append("		 	inner join cadastral.produto_instituicao p   ");
    query.append("		 		on picc.id_prod_instituicao_orig = p.id_prod_instituicao   ");
    query.append("		 	  inner join  suporte.hierarquia_ponto_de_relacionamento hpr    ");
    query.append("		    			                 on hpr.id_instituicao =pll.id_instituicao    ");
    query.append("		    			                 and  hpr.id_regional = pll.id_regional    ");
    query.append("		    			                 and  hpr.id_filial=pll.id_filial   ");
    query.append(
        "		    			                 and  hpr.id_ponto_de_relacionamento = pll.id_ponto_de_relacionamento  ");
    query.append("		    			        inner join suporte.hierarquia_ponto_de_relacionamento hpr2    ");
    query.append(
        "		    			                 on hpr2.id_instituicao = hpr.id_instituicao_corresp_2    ");
    query.append("		    			                 and  hpr2.id_regional = hpr.id_regional_corresp_2    ");
    query.append("		    			                 and  hpr2.id_filial = hpr.id_filial_corresp_2    ");
    query.append(
        "		    			                 and  hpr2.id_ponto_de_relacionamento = hpr.id_ponto_de_relacionamento_corresp_2  ");
    query.append(
        "		      left join cadastral.b2b_fatura fat on fat.id_fatura =  plldestino.id_fatura  ");
    query.append(
        "				left join transacional.cobranca_bancaria cb on fat.id_cobranca_bancaria = cb.id_cobranca_bancaria  ");
    query.append("		   where  pll.id_lote_lanc in(  ");
    query.append(
        "		    			      select srcc.id_lote_lanc as idExistente from suporte.solicitacao_replicacao_carga_resumo srcc  ");
    query.append("		    			      where srcc.dt_hr_inclusao >= :diasAtras ");
    query.append("		    			      )  and  picc.id_instituicao_dest = 2801");
    query.append("		    			 order by dtHrIncl desc ");

    Map<String, Object> params = new HashMap<>();
    Date diasAtras = DateUtil.diminuirDias(new Date(), QTD_DIAS_ANTES);

    params.put("diasAtras", diasAtras);
    List<Object[]> resultado = (List<Object[]>) findNativeByParameters(query.toString(), params);

    return (List<ReplicacaoLoteAgendadaResponse>)
        convertNativeQueryObjectToReplicacaoLoteAgendadaResponse(resultado);
  }

  private List<ReplicacaoLoteAgendadaResponse>
      convertNativeQueryObjectToReplicacaoLoteAgendadaResponse(List<Object[]> resultado) {
    List<ReplicacaoLoteAgendadaResponse> convertido =
        new ArrayList<ReplicacaoLoteAgendadaResponse>();
    for (Object[] obj : resultado) {
      ReplicacaoLoteAgendadaResponse rep =
          new ReplicacaoLoteAgendadaResponse(
              ObjectUtil.objectToLong(obj[0]),
              ObjectUtil.objectToDate(obj[1]),
              ObjectUtil.objectToInteger(obj[2]),
              ObjectUtil.objectToInteger(obj[3]),
              ObjectUtil.objectToInteger(obj[4]),
              ObjectUtil.objectToInteger(obj[5]),
              ObjectUtil.objectToInteger(obj[6]),
              ObjectUtil.objectToInteger(obj[7]),
              ObjectUtil.objectToDateStringPTBR(obj[8]),
              ObjectUtil.objectToInteger(obj[9]),
              ObjectUtil.objectToInteger(obj[10]),
              ObjectUtil.objectToBigDecimal(obj[11]),
              ObjectUtil.objectToInteger(obj[12]),
              ObjectUtil.objectToDate(obj[13]),
              ObjectUtil.objectToInteger(obj[14]),
              ObjectUtil.objectToLong(obj[15]),
              ObjectUtil.objectToInteger(obj[16]),
              ObjectUtil.objectToInteger(obj[17]),
              ObjectUtil.objectToInteger(obj[18]),
              ObjectUtil.objectToInteger(obj[19]),
              ObjectUtil.objectToInteger(obj[20]),
              ObjectUtil.objectToDate(obj[21]),
              ObjectUtil.objectToString(obj[22]));

      // 23 id_solicitacao_replicacao
      rep.setIdSolicitacaoReplicacaoCargaResumo(ObjectUtil.objectToLong(obj[23]));
      rep.setIdLoteLanc(ObjectUtil.objectToInteger(obj[24]));
      rep.setIdLoteLancCorresp(ObjectUtil.objectToInteger(obj[25]));
      rep.setIdUsuarioInclusao(ObjectUtil.objectToInteger(obj[26]));
      rep.setIdUsuarioManutencao(ObjectUtil.objectToInteger(obj[27]));
      rep.setIdJobReplicacao(ObjectUtil.objectToLong(obj[28]));
      rep.setStatus(ObjectUtil.objectToInteger(obj[29]));
      rep.setDataHoraInclusao(ObjectUtil.objectToDate(obj[30]));
      rep.setDataHoraStatus(ObjectUtil.objectToDate(obj[31]));
      rep.setDataHoraInicio(ObjectUtil.objectToDate(obj[32]));
      rep.setDataHoraFim(ObjectUtil.objectToDate(obj[33]));
      rep.setCodResultado(ObjectUtil.objectToInteger(obj[34]));
      rep.setDescResultado(ObjectUtil.objectToString(obj[35]));
      rep.setDescPontoDeRelacionamento(ObjectUtil.objectToString(obj[36]));
      rep.setStatusPedidoDestino(ObjectUtil.objectToInteger(obj[37]));
      rep.setIdCobrancaDestino(ObjectUtil.objectToLong(obj[38]));
      rep.setDataPagamento(ObjectUtil.objectToDateWithoutHour(obj[39]));
      rep.setValorPago(ObjectUtil.objectToBigDecimal(obj[40]));

      if (rep.getDataPagamento() != null && rep.getValorPago() != null) {
        rep.setExtraInfo("Boleto Pago");
      }
      if (rep.getStatusPedidoDestino() != null) {
        switch (rep.getStatusPedidoDestino()) {
          case 1:
            rep.setExtraInfo("Pedido Confirmado");
            break;
          case 2:
            rep.setExtraInfo("Pedido Processado");
            break;
          case 9:
            rep.setExtraInfo("Pedido Cancelado");
            break;

          default:
            rep.setExtraInfo("-");
            break;
        }
      }
      convertido.add(rep);
    }
    return convertido;
  }

  @Override
  public List<RetornoCargaVO> findInformacoesCargaParaRetornar(Integer idLote) {
    StringBuilder query = new StringBuilder();
    query.append(
        "select \n"
            + "  rapl.documento as \"Documento\"\n"
            + "  , rapl.nome_portador as \"Nome\"\n"
            + "  , rapl.valor_carga as \"Valor\"\n"
            + "  , rapl.uuid_externo as \"ID Externo\"\n"
            + "  , pll.dt_hr_status as \"Data Carga\"\n"
            + "  , case \n"
            + "      when pl.status = 1 then 'PG'\n"
            + "      else 'ER'\n"
            + "  end as \"Status\"\n"
            + "  , '\"'||trim(both ' | ' from replace(\n"
            + "  \t\tregexp_replace(rapl.erros_validacao,'(Linha: \\d+,?\\s?|Coluna: \\d+,?\\s?)','','g'),';',' | '))||'\"' as \"ERRO\"\n"
            + "  , pl.rrn as \"RRN\"\n"
            + "  , lapl.id_instituicao as \"Instituicao\"\n"
            + "  , rapl.id_registro \n"
            + "from suporte.log_arquivo_pre_lancamento lapl \n"
            + "  inner join suporte.registro_arq_pre_lancamento rapl on rapl.id_arquivo_pre_lancamento = lapl.id_arquivo_pre_lancamento\n"
            + "  left join cadastral.conta_pagamento cpag on cpag.id_conta = rapl.id_conta\n"
            + "  left join cadastral.conta_pessoa cp on cp.id_conta = cpag.id_conta and cp.id_titularidade = 1\n"
            + "  left join cadastral.pessoa p on p.id_pessoa = cp.id_pessoa\n"
            + "  left join cadastral.pre_lanca_lotes pll on pll.id_lote_lanc = lapl.id_pedido_criado \n"
            + "  left join cadastral.pre_lancamento pl on pl.id_lote_lanc = pll.id_lote_lanc and pl.id_conta = rapl.id_conta and rapl.status_validacao = 1\n"
            + "where true \n"
            + "  and pll.id_lote_lanc = :idLote\n"
            + "group by 1, 2, 3, 4, 5, 6, 8, 9, 10\n"
            + "order by 10");
    Map<String, Object> params = new HashMap<>();
    params.put("idLote", idLote);
    List<Object[]> resultado = (List<Object[]>) findNativeByParameters(query.toString(), params);

    return (List<RetornoCargaVO>) convertFindInformacoesCargaParaRetornar2VO(resultado);
  }

  private List<RetornoCargaVO> convertFindInformacoesCargaParaRetornar2VO(
      List<Object[]> resultado) {
    return resultado.stream()
        .map(
            res -> {
              RetornoCargaVO retornoCargaVO = new RetornoCargaVO();
              int i = 0;
              retornoCargaVO.setDocumento(ObjectUtil.objectToString(res[i++]));
              retornoCargaVO.setNome(ObjectUtil.objectToString(res[i++]));
              retornoCargaVO.setValor(ObjectUtil.objectToBigDecimal(res[i++]));
              retornoCargaVO.setIdExterno(ObjectUtil.objectToString(res[i++]));
              retornoCargaVO.setDataCarga(ObjectUtil.objectToLocalDateTime(res[i++]));
              retornoCargaVO.setStatus(ObjectUtil.objectToString(res[i++]));
              retornoCargaVO.setErros(ObjectUtil.objectToString(res[i++]));
              retornoCargaVO.setRrn(ObjectUtil.objectToString(res[i++]));
              retornoCargaVO.setIdInstituicao(ObjectUtil.objectToInteger(res[i++]));
              return retornoCargaVO;
            })
        .collect(Collectors.toList());
  }
}
