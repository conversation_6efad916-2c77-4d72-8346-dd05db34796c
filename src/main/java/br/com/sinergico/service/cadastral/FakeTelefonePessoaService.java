package br.com.sinergico.service.cadastral;

import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.PortadorLogin;
import br.com.json.bean.cadastral.FakeTelefonePessoaVo;
import br.com.sinergico.enums.TipoTelefonePessoaEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FakeTelefonePessoaService {

  public static final int DDI_BRASIL = 55;

  @Autowired private PessoaService pessoaService;

  public List<FakeTelefonePessoaVo> consultarTelefonePessoaFake(PortadorLogin portadorLogin) {
    List<Pessoa> pessoasPortador = pessoaService.getPessoasPortador(portadorLogin);

    List<FakeTelefonePessoaVo> resultado = new ArrayList<>();

    /*
     * Implementação ruim que introduz lixo no código devido a pedido do BRB
     * que foi atendido pelo relacionamento em 29/05/2025.
     *
     * Este trecho originalmente estava em uma implementação do projeto
     * Unificação de Cadastros de pessoas, que se encontra em ambiente de
     * homologação desde 13/08/2024 e até hoje (29/05/2025) não obteve aprovação
     * para produção.
     *
     * Com isto, o cliente vinha utilizando as APIs criadas especificamente para
     * este projeto, disponibilizadas somente em ambiente homologatório, para integrar
     * outras demandas e projetos por conta própria.
     * Ao se depararem com a inexistência de ditas APIs em ambiente produtivo,
     * uma incrível comoção se instaurou, levando o caso a instâncias hierárquicas finais
     * resultando na condescendência por parte da Valloo em "dar um jeito" e botar as
     * ditas APIs em ambiente produtivo "o quanto antes".
     *
     * Em termos técnicos, esta Service, bem como a Controller que a aciona, deveriam
     * estar associadas a uma nova estrutura de banco de dados, cuja entidade seria
     * TelefonePessoa.
     * Uma vez que esta entidade não existe em produção e não se faz viável criar a estrutura
     * de banco sem que as demais APIs (criação, edição, inativação, etc...) estejam presentes,
     * ou seja, o projeto completo vá para ambiente produtivo, a alternativa foi a criação de
     * um VO que simulará a estrutura originalmente arquitetada no projeto e devolverá os dados
     * existentes na tabela "pessoa". Desta forma, ainda retornaremos a consulta de telefones
     * cadastrados para a Pessoa, porém não será possível cadastrar diversos telefones e fazer
     * uso da funcionalidade "preferencial" inicialmente desenhada.
     * Contornaremos a inexistência do campo "preferencial", atribuindo TRUE ao primeiro telefone celular
     * resgatado e, se não houver, o primeiro residencial resgatado.
     * Quanto ao campo "idTelefonePessoa", devido à inexistência da tabela, este será sempre nulo.
     *
     * Recomendações para o futuro:
     *
     * Antes de subir, SE FOR APROVADO PARA PRODUÇÃO, o projeto de unificação de cadastros, é importante
     * que toda esta joça seja completamente removida para que a implementação original tome o lugar.
     */
    boolean preferencialAtribuido = false;
    for (Pessoa p : pessoasPortador) {
      if (p.getTelefoneCelular() != null) {
        resultado.add(
            FakeTelefonePessoaVo.of(
                p,
                TipoTelefonePessoaEnum.CELULAR,
                p.getDdiTelefoneCelular() != null ? p.getDdiTelefoneCelular() : DDI_BRASIL,
                p.getDddTelefoneCelular(),
                p.getTelefoneCelular(),
                !preferencialAtribuido));
        if (!preferencialAtribuido) {
          preferencialAtribuido = true;
        }
      }
      if (p.getTelefoneResidencial() != null) {
        resultado.add(
            FakeTelefonePessoaVo.of(
                p,
                TipoTelefonePessoaEnum.RESIDENCIAL,
                DDI_BRASIL,
                p.getDddTelefoneResidencial(),
                p.getTelefoneResidencial(),
                Boolean.FALSE));
      }
      if (p.getTelefoneComercial() != null) {
        resultado.add(
            FakeTelefonePessoaVo.of(
                p,
                TipoTelefonePessoaEnum.COMERCIAL,
                DDI_BRASIL,
                p.getDddTelefoneComercial(),
                p.getTelefoneComercial(),
                Boolean.FALSE));
      }
    }

    if (!preferencialAtribuido) {
      resultado.stream()
          .filter(vo -> vo.getTipoTelefone() == TipoTelefonePessoaEnum.RESIDENCIAL)
          .findFirst()
          .ifPresent(vo -> vo.setPreferencial(Boolean.TRUE));
    }

    return new ArrayList<>(
        resultado.stream()
            .collect(
                Collectors.toMap(
                    vo ->
                        Arrays.asList(
                            vo.getTipoTelefone(), vo.getDdi(), vo.getDdd(), vo.getNumeroTelefone()),
                    vo -> vo,
                    (primeiro, duplicado) -> primeiro))
            .values());
  }
}
