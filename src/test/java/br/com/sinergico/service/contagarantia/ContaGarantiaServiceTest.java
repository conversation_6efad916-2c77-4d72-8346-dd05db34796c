package br.com.sinergico.service.contagarantia;

import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.contagarantia.ContaGarantia;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaInstituicaoId;
import br.com.entity.suporte.HierarquiaNivel;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.contagarantia.BalanceResponse;
import br.com.json.bean.contagarantia.ContaGarantiaBalanceResponse;
import br.com.json.bean.contagarantia.ContaGarantiaBalanceResponseWrapper;
import br.com.json.bean.contagarantia.ContaGarantiaResponse;
import br.com.json.bean.contagarantia.ContaGarantiaValorReferenciaJcardRequest;
import br.com.sinergico.repository.contagarantia.ContaGarantiaRepository;
import br.com.sinergico.repository.suporte.HierarquiaInstituicaoRepositoryMockImpl;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.jcard.ContaGarantiaJcardService;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesErro;
import br.com.util.InstituicaoEnum;
import br.com.util.MockUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

@ExtendWith(MockitoExtension.class)
class ContaGarantiaServiceTest {

  @Mock private ContaGarantiaRepository mockContaGarantiaRepository;

  @Spy
  private HierarquiaInstituicaoService mockHierarquiaInstituicaoService =
      new HierarquiaInstituicaoService(new HierarquiaInstituicaoRepositoryMockImpl());

  @Mock private ContaGarantiaJcardService mockContaGarantiaJcardService;
  private ContaGarantiaService contaGarantiaServiceUnderTest;

  private static final SecurityUser userMulvi;
  private static final HierarquiaNivel hierarquiaNivelInstituicao;
  private static final HierarquiaInstituicao hierarquiaInstituicaoMulvi;
  private static final ContaGarantia contaGarantiaMulvi;

  private static final Integer idInstituicaoInexistente = Integer.MAX_VALUE;
  private static final HierarquiaInstituicaoId hierarquiaInstituicaoIdInexistente =
      new HierarquiaInstituicaoId(Constantes.ID_PROCESSADORA_ITS_PAY, idInstituicaoInexistente);
  private static final Long idContaGarantiaInexistente = Long.MAX_VALUE;

  static {
    hierarquiaNivelInstituicao = new HierarquiaNivel(2, "Instituicao");

    HierarquiaInstituicaoId hierarquiaInstituicaoId =
        new HierarquiaInstituicaoId(
            Constantes.ID_PROCESSADORA_ITS_PAY, InstituicaoEnum.MULVI.getIdInstituicao());
    hierarquiaInstituicaoMulvi =
        new HierarquiaInstituicao(hierarquiaInstituicaoId, InstituicaoEnum.MULVI.name());

    final AcessoUsuario acessoUsuario = new AcessoUsuario();
    acessoUsuario.setIdUsuario(MockUtil.getIdUsuarioIssuerInteger());
    acessoUsuario.setIdProcessadora(hierarquiaInstituicaoMulvi.getIdProcessadora());
    acessoUsuario.setIdInstituicao(hierarquiaInstituicaoId.getIdInstituicao());
    acessoUsuario.setIdHierarquiaNivel(hierarquiaNivelInstituicao.getIdNivelHierarquia());
    acessoUsuario.setNome("Usuario Mulvi");
    userMulvi = new SecurityUser(acessoUsuario);

    contaGarantiaMulvi = new ContaGarantia();
    contaGarantiaMulvi.setId(1L);
    contaGarantiaMulvi.setHierarquiaInstituicao(hierarquiaInstituicaoMulvi);
    contaGarantiaMulvi.setIdAccountCodeGarantia("15.1");
    contaGarantiaMulvi.setIdAccountCodeAporte("15.1.1");
    contaGarantiaMulvi.setIdAccountCodeContrapartida("16.1");
    contaGarantiaMulvi.setIdAccountCodeTransacoesControladas("15.1.2");
    contaGarantiaMulvi.setIdAccountCodeNaoControlada("15.1.3");
    contaGarantiaMulvi.setValorReferencia(new BigDecimal("50000.00"));
    contaGarantiaMulvi.setUsuarioInclusao(userMulvi);
    contaGarantiaMulvi.setDtHrInclusao(LocalDateTime.of(2024, 11, 4, 18, 7, 43, 286254));
  }

  @BeforeEach
  public void setUp() {

    contaGarantiaServiceUnderTest =
        new ContaGarantiaService(
            mockContaGarantiaRepository,
            mockHierarquiaInstituicaoService,
            mockContaGarantiaJcardService);
  }

  @Test
  public void testBuscarContasGarantiaInstituicao() {

    final List<ContaGarantia> contaGarantiaList = List.of(contaGarantiaMulvi);
    when(mockContaGarantiaRepository.findAllByHierarquiaInstituicao(hierarquiaInstituicaoMulvi))
        .thenReturn(contaGarantiaList);

    // Run the test
    final ResponseEntity<List<ContaGarantiaResponse>> result =
        contaGarantiaServiceUnderTest.buscarContasGarantiaInstituicao(
            hierarquiaInstituicaoMulvi.getIdInstituicao(), userMulvi);
  }

  @Test
  public void testBuscarContasGarantiaInstituicao_HierarquiaInstituicaoServiceReturnsNull() {

    Assertions.assertThrows(
        GenericServiceException.class,
        () ->
            contaGarantiaServiceUnderTest.buscarContasGarantiaInstituicao(
                idInstituicaoInexistente, userMulvi),
        ConstantesErro.INS_INSTITUICAO_NAO_ENCONTRADA.format(idInstituicaoInexistente));
  }

  @Test
  public void testBuscarContasGarantiaInstituicao_ContaGarantiaRepositoryReturnsNoItems() {

    when(mockContaGarantiaRepository.findAllByHierarquiaInstituicao(hierarquiaInstituicaoMulvi))
        .thenReturn(Collections.emptyList());

    // Run the test
    Assertions.assertThrows(
        GenericServiceException.class,
        () ->
            contaGarantiaServiceUnderTest.buscarContasGarantiaInstituicao(
                hierarquiaInstituicaoMulvi.getIdInstituicao(), userMulvi),
        ConstantesErro.PCG_GARANTIA_INSTITUICAO_NAO_ENCONTRADA.format(
            hierarquiaInstituicaoMulvi.getIdInstituicao()));
  }

  @Test
  public void testBuscarSaldoContaGarantia() {

    when(mockContaGarantiaRepository.findById(contaGarantiaMulvi.getId()))
        .thenReturn(Optional.of(contaGarantiaMulvi));

    final ContaGarantiaBalanceResponseWrapper contaGarantiaBalanceResponseWrapper =
        new ContaGarantiaBalanceResponseWrapper();
    contaGarantiaBalanceResponseWrapper.setSuccess(true);

    final ContaGarantiaBalanceResponse response = new ContaGarantiaBalanceResponse();
    response.setIdContaGarantia(contaGarantiaMulvi.getId());
    final BalanceResponse balanceContaGarantia = new BalanceResponse();
    response.setBalanceContaGarantia(balanceContaGarantia);
    contaGarantiaBalanceResponseWrapper.setResponse(response);
    when(mockContaGarantiaJcardService.getBalanceContaGarantia(contaGarantiaMulvi.getId()))
        .thenReturn(contaGarantiaBalanceResponseWrapper);

    final ResponseEntity<ContaGarantiaBalanceResponse> result =
        contaGarantiaServiceUnderTest.buscarSaldoContaGarantia(
            contaGarantiaMulvi.getId(), userMulvi);
  }

  @Test
  public void testBuscarSaldoContaGarantia_JcardServiceFails() {

    when(mockContaGarantiaRepository.findById(contaGarantiaMulvi.getId()))
        .thenReturn(Optional.of(contaGarantiaMulvi));

    final ContaGarantiaBalanceResponseWrapper contaGarantiaBalanceResponseWrapper =
        new ContaGarantiaBalanceResponseWrapper();
    contaGarantiaBalanceResponseWrapper.setSuccess(false);
    contaGarantiaBalanceResponseWrapper.setErrors("Erros Jcard");

    final ContaGarantiaBalanceResponse response = new ContaGarantiaBalanceResponse();
    response.setIdContaGarantia(contaGarantiaMulvi.getId());
    final BalanceResponse balanceContaGarantia = new BalanceResponse();
    response.setBalanceContaGarantia(balanceContaGarantia);
    contaGarantiaBalanceResponseWrapper.setResponse(response);
    when(mockContaGarantiaJcardService.getBalanceContaGarantia(contaGarantiaMulvi.getId()))
        .thenReturn(contaGarantiaBalanceResponseWrapper);

    Assertions.assertThrows(
        GenericServiceException.class,
        () ->
            contaGarantiaServiceUnderTest.buscarSaldoContaGarantia(
                contaGarantiaMulvi.getId(), userMulvi),
        ConstantesErro.JCARD_GARANTIA_SALDO.format(
            contaGarantiaBalanceResponseWrapper.getErrors()));
  }

  @Test
  public void testBuscarSaldoContaGarantia_JpaRepositoryReturnsAbsent() {

    when(mockContaGarantiaRepository.findById(idContaGarantiaInexistente))
        .thenReturn(Optional.empty());

    Assertions.assertThrows(
        GenericServiceException.class,
        () ->
            contaGarantiaServiceUnderTest.buscarSaldoContaGarantia(
                idContaGarantiaInexistente, userMulvi),
        ConstantesErro.PCG_GARANTIA_ID_NAO_ENCONTRADA.format(idContaGarantiaInexistente));
  }

  @Test
  public void testAlterarValorReferenciaContaGarantia() {

    when(mockContaGarantiaRepository.findById(contaGarantiaMulvi.getId()))
        .thenReturn(Optional.of(contaGarantiaMulvi));

    BigDecimal valorReferenciaNovo = new BigDecimal("1234.56");

    ContaGarantiaValorReferenciaJcardRequest contaGarantiaValorReferenciaJcardRequest =
        new ContaGarantiaValorReferenciaJcardRequest(valorReferenciaNovo);
    JcardResponse jcardResponse = new JcardResponse();
    jcardResponse.setSuccess(true);
    when(mockContaGarantiaJcardService.alterarValorReferenciaJcard(
            contaGarantiaMulvi.getId(), contaGarantiaValorReferenciaJcardRequest))
        .thenReturn(jcardResponse);

    final ResponseEntity<ContaGarantiaResponse> result =
        contaGarantiaServiceUnderTest.alterarValorReferenciaContaGarantia(
            contaGarantiaMulvi.getId(), valorReferenciaNovo, userMulvi);

    Assertions.assertNotNull(result.getBody());
    Assertions.assertNotNull(result.getBody().getValorReferencia());
    Assertions.assertEquals(
        0, result.getBody().getValorReferencia().compareTo(valorReferenciaNovo));
    verify(mockContaGarantiaRepository, times(1)).save(contaGarantiaMulvi);
  }

  @Test
  public void testAlterarValorReferenciaContaGarantia_JpaRepositoryFindByIdReturnsAbsent() {

    when(mockContaGarantiaRepository.findById(idContaGarantiaInexistente))
        .thenReturn(Optional.empty());

    Assertions.assertThrows(
        GenericServiceException.class,
        () ->
            contaGarantiaServiceUnderTest.alterarValorReferenciaContaGarantia(
                idContaGarantiaInexistente, new BigDecimal("9876.54"), userMulvi),
        ConstantesErro.PCG_GARANTIA_ID_NAO_ENCONTRADA.format(idContaGarantiaInexistente));

    verify(mockContaGarantiaRepository, never()).save(contaGarantiaMulvi);
  }

  @Test
  public void testAlterarValorReferenciaContaGarantia_JcardResponseError() {

    when(mockContaGarantiaRepository.findById(contaGarantiaMulvi.getId()))
        .thenReturn(Optional.of(contaGarantiaMulvi));

    BigDecimal valorReferenciaNovo = new BigDecimal("1234.56");

    ContaGarantiaValorReferenciaJcardRequest contaGarantiaValorReferenciaJcardRequest =
        new ContaGarantiaValorReferenciaJcardRequest(valorReferenciaNovo);
    JcardResponse jcardResponse = new JcardResponse();
    jcardResponse.setSuccess(false);
    jcardResponse.setErrors("Erros Jcard");
    when(mockContaGarantiaJcardService.alterarValorReferenciaJcard(
            contaGarantiaMulvi.getId(), contaGarantiaValorReferenciaJcardRequest))
        .thenReturn(jcardResponse);

    Assertions.assertThrows(
        GenericServiceException.class,
        () ->
            contaGarantiaServiceUnderTest.alterarValorReferenciaContaGarantia(
                contaGarantiaMulvi.getId(), valorReferenciaNovo, userMulvi),
        ConstantesErro.JCARD_GARANTIA_FALHA_ALTERAR_VALOR_REFERENCIA.format(
            jcardResponse.getErrors()));

    // Não há como verificar o comportamento de rollback, somente o de chamada do save
    // Deduz-se do assertThrows acima que o rollback é realizado
    verify(mockContaGarantiaRepository, times(1)).save(contaGarantiaMulvi);
  }
}
