package br.com.sinergico.repository.cadastral;

import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.json.bean.cadastral.ProdutoInstituicaoParametrizadoVO;
import br.com.json.bean.cadastral.ProdutoInstituicaoResponse;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ProdutoInstituicaoRepository
    extends JpaRepository<ProdutoInstituicao, Integer>, ProdutoInstituicaoRepositoryCustom {
  ProdutoInstituicao findOneByIdProcessadoraAndIdInstituicaoAndIdProdInstituicao(
      Integer idProcessadora, Integer idInstituicao, Integer idProdInstituicao);

  Integer countByIdProcessadoraAndIdInstituicao(Integer idProcessadora, Integer idInstituicao);

  Integer countByIdProcessadoraAndIdInstituicaoAndIdProdInstituicaoIn(
      Integer idProcessadora, Integer idInstituicao, List<Integer> termo);

  Integer countByIdProcessadoraAndIdInstituicaoAndDescProdInstituicaoContainingIgnoreCase(
      Integer idProcessadora, Integer idInstituicao, String termo);

  List<ProdutoInstituicao> findByIdProcessadoraAndIdInstituicao(
      Integer idProcessadora, Integer idInstituicao, Pageable top);

  List<ProdutoInstituicao>
      findByIdProcessadoraAndIdInstituicaoAndDescProdInstituicaoContainingIgnoreCase(
          Integer idProcessadora, Integer idInstituicao, String termo, Pageable top);

  List<ProdutoInstituicao> findByIdProcessadoraAndIdInstituicaoAndIdProdInstituicaoIn(
      Integer idProcessadora, Integer idInstituicao, List<Integer> termo, Pageable top);

  List<ProdutoInstituicao> findByIdProcessadora(Integer idProcessadora, Pageable top);

  List<ProdutoInstituicao> findByIdProcessadoraAndIdProdInstituicaoIn(
      Integer idProcessadora, List<Integer> termo, Pageable top);

  List<ProdutoInstituicao> findByIdProcessadoraAndDescProdInstituicaoContainingIgnoreCase(
      Integer idProcessadora, Pageable top, String termo);

  @Query(
      "select pi from ProdutoInstituicaoConfiguracao pic "
          + "inner join pic.produtoInstituicao pi "
          + "where pic.idRelacionamento = 1 "
          + "and pic.idProcessadora = :idProcessadora "
          + "and pic.idInstituicao = :idInstituicao "
          + "order by pic.idProdInstituicao ")
  List<ProdutoInstituicao> findAllByProcessadoraAndInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      "select pi from ProdutoInstituicaoConfiguracao pic "
          + "inner join pic.produtoInstituicao pi "
          + "where pic.idRelacionamento = 1 "
          + "and pi.b2b = false "
          + "and pic.tipoPessoa = :idTipoPessoa "
          + "and pic.idProcessadora = :idProcessadora "
          + "and pic.idInstituicao = :idInstituicao "
          + "order by pic.idProdInstituicao ")
  List<ProdutoInstituicao> findAllNotB2bPorTipoPessoa(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idTipoPessoa") Integer idTipoPessoa);

  ProdutoInstituicao findByIdProdInstituicao(Integer idProdInstituicao);

  @Query("select (count(p) > 0) from ProdutoInstituicao p where p.idProdInstituicao = ?1")
  Boolean existeProdutoInstituicao(Integer idProdInstituicao);

  List<ProdutoInstituicao> findByIdProcessadoraAndIdInstituicaoAndB2bTrue(
      Integer idProcessadora, Integer idInstituicao);

  List<ProdutoInstituicao> findByIdProdInstituicaoNot(Integer idProdInstituicao);

  @Query(
      "select new br.com.json.bean.cadastral.ProdutoInstituicaoResponse("
          + "pi.descProdInstituicao, "
          + "pi.idInstituicao, "
          + "pi.idProcessadora, "
          + "pi.idProdInstituicao, "
          + "pigp.posicaoHierarquia, "
          + "pigp.produtoInstituicaoGrupoProdutosId.idGrupo, "
          + "pigp.ordemApresentacao"
          + ") "
          + "from ProdutoInstituicao pi "
          + "inner join pi.produtosInstituicaoGrupoProdutos pigp "
          + "where pigp.produtoInstituicaoGrupoProdutosId.idGrupo = :idGrupo "
          + "order by pigp.ordemApresentacao asc ")
  List<ProdutoInstituicaoResponse> findProdutoByIdGrupo(@Param("idGrupo") Long idGrupo);

  Integer countByIdProcessadora(Integer idProcessadora);

  Integer countByIdProcessadoraAndDescProdInstituicaoContainingIgnoreCase(
      Integer idProcessadora, String termo);

  Integer countByIdProcessadoraAndIdProdInstituicaoIn(Integer idProcessadora, List<Integer> termo);

  /**
   * Método responsável por buscar os produtos contratados por um ponto de relacionamento
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  @Query(
      "select new br.com.json.bean.cadastral.ProdutoInstituicaoResponse(pi.descProdInstituicao, pc.idProcessadora, pc.idInstituicao, pi.idProdInstituicao, pic.idProdutoPlataforma, pi.b2b, pic.emitePropriaEmpresa, pic.idRelacionamento, pc.dtContrato, pc.permiteCarga, pic.tipoPessoa, pic.tipoFormulario, pic.cargaIntegracao, pic.idGrupoProduto, pic.tipoProduto, pc.permiteCargaB2B ) "
          + " from ProdutoInstituicao pi inner join pi.produtosContratados pc inner join pi.produtoInstituicaoConfiguracao pic "
          + "where pc.idProcessadora = :idProcessadora "
          + "and pc.idInstituicao = :idInstituicao "
          + "and pc.idRegional = :idRegional "
          + "and pc.idFilial = :idFilial "
          + "and pc.idPontoRelacionamento = :idPontoRelacionamento "
          + "and pc.dtHrCancelamento = null "
          + "order by pi.descProdInstituicao")
  public List<ProdutoInstituicaoResponse> findByHierarquia(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "select new br.com.json.bean.cadastral.ProdutoInstituicaoResponse( pi.descProdInstituicao, pc.idProcessadora, pc.idInstituicao, pc.idProdInstituicao,  pic.idProdutoPlataforma, pi.b2b, pic.emitePropriaEmpresa, pic.idRelacionamento, pc.dtContrato, pic.suportaContaBase, pc.permiteCarga, pic.tipoPessoa)"
          + " from ProdutoInstituicao pi inner join pi.produtosContratados pc inner join pi.produtoInstituicaoConfiguracao pic "
          + "where pic.emitePropriaEmpresa = 1 and pc.dtHrCancelamento = null and pc.idProcessadora = :idProcessadora and pc.idInstituicao = :idInstituicao and pc.idRegional = :idRegional and pc.idFilial = :idFilial and pc.idPontoRelacionamento = :idPontoRelacionamento order by pi.descProdInstituicao")
  public List<ProdutoInstituicaoResponse> findByHierarquiaAndEmitePropriaEmpresa(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      " select new br.com.json.bean.cadastral.ProdutoInstituicaoResponse( pi.descProdInstituicao, pc.idProcessadora, pc.idInstituicao, pc.idProdInstituicao,  pic.idProdutoPlataforma, pi.b2b, pic.emitePropriaEmpresa, pic.idRelacionamento, pc.dtContrato, pic.suportaContaBase, pc.permiteCarga, pic.tipoPessoa)"
          + " from ProdutoInstituicao pi inner join pi.produtosContratados pc inner join pi.produtoInstituicaoConfiguracao pic "
          + " where pc.dtHrCancelamento is null "
          + " and pc.idProcessadora = :idProcessadora "
          + " and pc.idInstituicao = :idInstituicao "
          + " and pc.idRegional = :idRegional "
          + " and pc.idFilial = :idFilial "
          + " and pc.idPontoRelacionamento = :idPontoRelacionamento "
          + " order by pi.descProdInstituicao ")
  public List<ProdutoInstituicaoResponse> findByHierarquiaAndTipoPessoaPJ(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "select pi  from ProdutoInstituicao pi inner join pi.produtosContratados pc where pc.idProcessadora = :idProcessadora and pc.idInstituicao = :idInstituicao and pc.idRegional = :idRegional and pc.idFilial = :idFilial and pc.idPontoRelacionamento = :idPontoRelacionamento order by pi.descProdInstituicao")
  public List<ProdutoInstituicao> findByHierarquiaAndQualquerVigencia(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoRelacionamento") Integer idPontoRelacionamento);

  @Query(
      "select max(pi.idProdInstituicao) from ProdutoInstituicao pi "
          + "where pi.idProcessadora = :idProcessadora "
          + "and pi.idInstituicao = :idInstituicao")
  Integer findMaxIdProdutoByProcessadoraAndInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      "select pi from ProdutoInstituicaoConfiguracao pic "
          + "inner join pic.produtoInstituicao pi "
          + "where pic.idRelacionamento = 2 "
          + "and pic.idProdutoPlataforma = 9 "
          + "and pi.b2b = false "
          + "and pic.tipoPessoa = :tipoPessoa "
          + "and pic.idProcessadora = :idProcessadora "
          + "and pic.idInstituicao = :idInstituicao")
  List<ProdutoInstituicao> findAllPos(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa);

  @Query(
      "select DISTINCT(pin) from ProdutoInstituicao pin "
          + "inner join pin.produtosContratados pc "
          + "inner join pin.produtoInstituicaoConfiguracao pic "
          + "where pc.dtHrCancelamento = null "
          + "and pic.idProcessadora = :idProcessadora "
          + "and pic.idInstituicao = :idInstituicao")
  List<ProdutoInstituicao> findAllByInstituicao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      "select pi from ProdutoInstituicaoConfiguracao pic "
          + "inner join pic.produtoInstituicao pi "
          + "where pic.idRelacionamento = 2 "
          + "and pic.idProdutoPlataforma = 9 "
          + "and pic.idProcessadora = :idProcessadora "
          + "and pic.idInstituicao = :idInstituicao")
  List<ProdutoInstituicao> getAllPosPfAndPj(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      "select pi from ProdutoInstituicao pi "
          + "inner join pi.produtoInstituicaoConfiguracao pc "
          + "where pi.idProcessadora = :idProcessadora "
          + "and pi.idInstituicao = :idInstituicao "
          + "and pc.idProdutoPlataforma = 4 "
          + "and pc.idRelacionamento = 2 "
          + "and pi.b2b = true "
          + "order by pi.idProdInstituicao")
  List<ProdutoInstituicao> findConvenioByUsuarioLogado(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao);

  @Query(
      value =
          "select p.* from cadastral.produto_instituicao p "
              + "inner join cadastral.conta_pagamento cp on cp.id_processadora = p.id_processadora and cp.id_instituicao = p.id_instituicao and cp.id_prod_instituicao = p.id_prod_instituicao "
              + "where cp.id_conta = :idConta ",
      nativeQuery = true)
  ProdutoInstituicao findByIdConta(@Param("idConta") Long idConta);

  @Query(
      "select pi from ProdutoInstituicaoConfiguracao pic "
          + "inner join pic.produtoInstituicao pi "
          + "where pi.b2b = false "
          + "and pic.tipoPessoa = :tipoPessoa "
          + "and pic.idProcessadora = :idProcessadora "
          + "and pic.idInstituicao = :idInstituicao")
  List<ProdutoInstituicao> findAllNotB2bByInstituicaoAndTipoPessoa(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("tipoPessoa") Integer tipoPessoa);

  @Query(
      value =
          "select distinct new br.com.json.bean.cadastral.ProdutoInstituicaoParametrizadoVO(pi2.descProdInstituicao, pp.descProdPlat, "
              + "pi2.idInstituicao, pi2.idProcessadora, pi2.idProdInstituicao, pi2.idPontoDeRelacionamento, "
              + "pi2.habilitaTodos, pi2.b2b, capp.id is not null as parametrizado, pic.idProdutoPlataforma ) "
              + "from ProdutoInstituicao pi2 "
              + "left join pi2.cargaAutoProdutoParametros capp "
              + "inner join pi2.produtoInstituicaoConfiguracao pic "
              + "inner join pic.produtoPlataforma pp "
              + "where pi2.idProcessadora = :idProcessadora "
              + "and pi2.idInstituicao = :idInstituicao "
              + "order by pi2.idProdInstituicao")
  List<ProdutoInstituicaoParametrizadoVO> buscaProdutosParametrizados(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      Pageable pageable);
}
