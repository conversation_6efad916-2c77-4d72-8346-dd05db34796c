package br.com.sinergico.service.pix;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.entity.cadastral.CorporativoResponsavelCredencial;
import br.com.entity.cadastral.Credencial;
import br.com.entity.pix.ContaTransacional;
import br.com.entity.pix.ContaTransacionalChaves;
import br.com.entity.pix.ContatoPix;
import br.com.entity.pix.InstituicaoPix;
import br.com.entity.pix.LogInclusaoChave;
import br.com.entity.suporte.TipoChavePix;
import br.com.entity.suporte.TipoContaPix;
import br.com.entity.suporte.TipoPessoa;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.enums.pix.SituacaoReivindicacaoEnum;
import br.com.json.bean.pix.enums.StatusTentativaInclusaoChave;
import br.com.json.bean.pix.request.DeletarChaveDTO;
import br.com.json.bean.pix.request.IncluirChaveRequest;
import br.com.json.bean.pix.request.MotivoExclusaoChaveRequest;
import br.com.json.bean.pix.request.webhook.NotificacaoReivindicacaoForm;
import br.com.json.bean.pix.response.ChaveInfoReduzidaResponse;
import br.com.json.bean.pix.response.ChaveInfoResponse;
import br.com.json.bean.pix.response.ConsultarChaveEnderecamentoResponse;
import br.com.json.bean.pix.response.DeleteInscricaoResponse;
import br.com.json.bean.pix.response.EstatisticasChaveResponse;
import br.com.json.bean.pix.response.vo.ChaveInfoVO;
import br.com.sinergico.client.BancoRendimentoClient;
import br.com.sinergico.repository.pix.ContaTransacionalChavesRepository;
import br.com.sinergico.repository.pix.LogInclusaoChaveRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.corporativo.CorporativoService;
import br.com.sinergico.service.suporte.TipoChavePixService;
import br.com.sinergico.service.suporte.TipoContaPixService;
import br.com.sinergico.service.suporte.TipoPessoaService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import java.lang.reflect.InvocationTargetException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.ResourceAccessException;

@Service
@Slf4j
public class ContaTransacionalChavesService {

  public static final String NENHUMA_CHAVE_ENCONTRADA = "Nenhuma chave encontrada";
  public static final String DETALHE = "DTL";

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private InstituicaoPixService instituicaoPixService;

  @Autowired private BancoRendimentoClient bancoRendimentoClient;

  @Autowired private TipoChavePixService tipoChavePixService;

  @Autowired private TipoContaPixService tipoContaPixService;

  @Autowired private TipoPessoaService tipoPessoaService;

  @Autowired private ContaTransacionalService contaTransacionalService;

  @Autowired private ContatoPixService contatoPixService;

  @Autowired private ContaTransacionalChavesRepository contaTransacionalChavesRepository;

  @Autowired private LogInclusaoChaveRepository logInclusaoChaveRepository;

  @Autowired private TravaContasService travaContasService;

  @Autowired private PessoaService pessoaService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private CorporativoService corporativoService;

  @Autowired @Lazy private CredencialService credencialService;

  @Autowired private ContaPessoaService contaPessoaService;

  public ResponseEntity<ConsultarChaveEnderecamentoResponse> consultarChavePorInscricao(
      Long idConta, SecurityUserPortador userPortador) {
    ContaPagamento contaPagamento =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(idConta, userPortador);
    return consultarChavePorInscricao(contaPagamento, userPortador.getCpf(), idConta.toString());
  }

  public ResponseEntity<ConsultarChaveEnderecamentoResponse> consultarChavePorInscricao(
      Long idConta, SecurityUserCorporativo userCorporativo) {
    ContaPagamento contaPagamento =
        contaPagamentoService.buscarValidarContaPeloRequestECorporativo(idConta, userCorporativo);
    ContaPessoa contaPessoa =
        this.contaPessoaService.findOneByIdContaAndIdTitularidade(idConta, Constantes.TITULAR);
    return consultarChavePorInscricao(
        contaPagamento, contaPessoa.getPessoa().getDocumento(), idConta.toString());
  }

  public ResponseEntity<ConsultarChaveEnderecamentoResponse> consultarChavePorInscricaoUser(
      Long idConta, SecurityUser user) {

    ContaPagamento conta = contaPagamentoService.findByIdNotNull(idConta);

    // Retira validação de usuário logado para consultar chave pix de outra conta especifica.
    //        UtilController.checkHierarquiaUsuarioLogado(user, conta);

    String documento = pessoaService.findPessoaTitularConta(conta.getIdConta()).getDocumento();
    return consultarChavePorInscricao(conta, documento, idConta.toString());
  }

  private ResponseEntity<ConsultarChaveEnderecamentoResponse> consultarChavePorInscricao(
      ContaPagamento contaPagamento, String documento, String idConta) {

    InstituicaoPix instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            contaPagamento.getIdProdutoInstituicao(), contaPagamento.getIdInstituicao());
    Integer idInstituicao = contaPagamento.getIdInstituicao();

    ResponseEntity<ConsultarChaveEnderecamentoResponse> chavesResponse = null;
    try {
      chavesResponse =
          bancoRendimentoClient.listarChavesPorInscricaoNacional(instituicaoPix, documento);
    } catch (GenericServiceException e) {
      if (e.getErros() != null
          && e.getErros().get(DETALHE) != null
          && e.getErros().get(DETALHE).toString().contains(NENHUMA_CHAVE_ENCONTRADA)) {
        ConsultarChaveEnderecamentoResponse response = new ConsultarChaveEnderecamentoResponse();
        List<ChaveInfoVO> lista = new ArrayList<>();
        response.setValue(lista);
        return new ResponseEntity<>(response, HttpStatus.NO_CONTENT);
      }
      throw e;
    }
    ConsultarChaveEnderecamentoResponse chaves = chavesResponse.getBody();

    if (chavesResponse.getStatusCode().equals(HttpStatus.OK)
        && chaves != null
        && chaves.getValue() != null
        && !chaves.getValue().isEmpty()) {
      chaves.setValue(
          chaves.getValue().stream()
              .filter(
                  chave ->
                      (chave.getConta().getAgencia().equals(idInstituicao.toString()))
                          && (chave
                              .getConta()
                              .getNumero()
                              .substring(0, chave.getConta().getNumero().length() - 1)
                              .equals(idConta)))
              .collect(Collectors.toList()));
    }
    return chavesResponse;
  }

  public ResponseEntity<ChaveInfoResponse> prepararIncluirChave(
      IncluirChaveRequest incluirChaveRequest, SecurityUserPortador userPortador) {

    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.PIX);
    travaContasService.travaContas(
        Long.valueOf(incluirChaveRequest.getConta().getIdConta()), Servicos.PIX);

    InstituicaoPix instituicaoPix;
    instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            incluirChaveRequest.getIdProdInstituicao(), userPortador.getIdInstituicao());
    if (Constantes.STATUS_PIX_DESABILITADO.equals(instituicaoPix.getHabilitado())) {
      throw new AccessDeniedException(
          "Pix desabilitado para o produto: " + instituicaoPix.getIdProdutoInstituicao());
    }
    Long numeroConta = Long.valueOf(incluirChaveRequest.getConta().getIdConta());
    contaPagamentoService.validaIdContaPeloRequestEPortador(numeroConta, userPortador);

    return this.incluirChave(incluirChaveRequest, instituicaoPix);
  }

  public ResponseEntity<ChaveInfoResponse> prepararIncluirChave(
      IncluirChaveRequest incluirChaveRequest, SecurityUserCorporativo userCorporativo) {

    travaServicosService.travaServicos(userCorporativo.getIdInstituicao(), Servicos.PIX);
    travaContasService.travaContas(
        Long.valueOf(incluirChaveRequest.getConta().getIdConta()), Servicos.PIX);

    InstituicaoPix instituicaoPix;
    instituicaoPix =
        instituicaoPixService.buscarInstituicaoPixPorProduto(
            incluirChaveRequest.getIdProdInstituicao(), userCorporativo.getIdInstituicao());
    if (Constantes.STATUS_PIX_DESABILITADO.equals(instituicaoPix.getHabilitado())) {
      throw new AccessDeniedException(
          "Pix desabilitado para o produto: " + instituicaoPix.getIdProdutoInstituicao());
    }
    Long numeroConta = Long.valueOf(incluirChaveRequest.getConta().getIdConta());
    contaPagamentoService.buscarValidarContaPeloRequestECorporativo(numeroConta, userCorporativo);
    incluirChaveRequest.getConta().setTipoId("1");
    return this.incluirChave(incluirChaveRequest, instituicaoPix);
  }

  public ResponseEntity<ChaveInfoResponse> incluirChave(
      IncluirChaveRequest incluirChaveRequest, InstituicaoPix instituicaoPix) {
    long t0 = System.currentTimeMillis();

    Long numeroConta = Long.valueOf(incluirChaveRequest.getConta().getIdConta());
    ContaTransacional contaTransacional =
        contaTransacionalService
            .findByIdConta(numeroConta)
            .orElseGet(
                () -> contaTransacionalService.criarContaTransacional(numeroConta, instituicaoPix));

    if (incluirChaveRequest.getPessoa().getInscricaoNacional().length() > Constantes.TAMANHO_CPF) {
      if (incluirChaveRequest.getChave().getTipoId().equals("1")) {
        incluirChaveRequest.getChave().setTipoId("2");
      }
      incluirChaveRequest.getPessoa().setTipoId("2");
    }

    // importante para adicionar com o digito verificador
    incluirChaveRequest.setNumeroConta(
        incluirChaveRequest.getConta().getIdConta() + contaTransacional.getDvConta());

    LogInclusaoChave logInclusaoChave =
        montarHistoricoTentativa(contaTransacional, incluirChaveRequest);
    logInclusaoChave = logInclusaoChaveRepository.save(logInclusaoChave);

    try {
      ResponseEntity<ChaveInfoResponse> chaveInfoResponseResponseEntity =
          bancoRendimentoClient.incluirChave(instituicaoPix, incluirChaveRequest);

      logInclusaoChave.setTempoRespostaMs((int) (System.currentTimeMillis() - t0));
      logInclusaoChave.setHttpStatus(chaveInfoResponseResponseEntity.getStatusCodeValue());

      if (chaveInfoResponseResponseEntity.getStatusCode().equals(HttpStatus.OK)
          && chaveInfoResponseResponseEntity.getBody() != null) {
        ChaveInfoResponse body = chaveInfoResponseResponseEntity.getBody();
        logInclusaoChave.setStatusTentativa(StatusTentativaInclusaoChave.SUCESSO);
        logInclusaoChave.setTransactionId(body.getTransactionId());
        logInclusaoChave.setChaveId(body.getValue().getChaveId());
        if (logInclusaoChave.getValorChave() == null
            || logInclusaoChave.getValorChave().isEmpty()) {
          logInclusaoChave.setValorChave(body.getValue().getChave().getValor());
        }
        log.info("Chave criada com sucesso: " + chaveInfoResponseResponseEntity);
        salvarChave(body, contaTransacional);
      } else {
        logInclusaoChave.setStatusTentativa(StatusTentativaInclusaoChave.ERRO_HTTP);
        logInclusaoChave.setMensagemErro(String.valueOf(chaveInfoResponseResponseEntity.getBody()));
        log.info("Erro ao criar chave " + chaveInfoResponseResponseEntity.getBody());
      }
      return chaveInfoResponseResponseEntity;
    } catch (ResourceAccessException timeout) {
      logInclusaoChave.setStatusTentativa(StatusTentativaInclusaoChave.TIMEOUT);
      logInclusaoChave.setMensagemErro(timeout.getMessage());
      throw timeout;
    } catch (Exception e) {
      logInclusaoChave.setStatusTentativa(StatusTentativaInclusaoChave.EXCECAO);
      logInclusaoChave.setMensagemErro(e.toString());
      throw e;
    } finally {
      logInclusaoChaveRepository.save(logInclusaoChave);
    }
  }

  private LogInclusaoChave montarHistoricoTentativa(
      ContaTransacional contaTransacional, IncluirChaveRequest req) {

    LogInclusaoChave logInclusaoChave = new LogInclusaoChave();

    logInclusaoChave.setContaTransacional(contaTransacional);
    logInclusaoChave.setValorChave(req.getChave().getValor());
    logInclusaoChave.setTipoChave(getTipoChaveById(req.getChave().getTipoId()));
    logInclusaoChave.setTipoPessoa(getTipoPessoaById(req.getPessoa().getTipoId()));
    logInclusaoChave.setStatusTentativa(
        StatusTentativaInclusaoChave.EXCECAO); // valor default até provar o contrário
    logInclusaoChave.setNumeroConta(req.getConta().getIdConta());
    logInclusaoChave.setInscricaoNacional(req.getPessoa().getInscricaoNacional());
    logInclusaoChave.setNome(req.getPessoa().getNome());

    return logInclusaoChave;
  }

  private void salvarChave(
      ChaveInfoResponse chaveInfoResponse, ContaTransacional contaTransacional) {

    ContaTransacionalChaves contaTransacionalChaves = new ContaTransacionalChaves();
    ChaveInfoVO chaveInfoVO = chaveInfoResponse.getValue();

    contaTransacionalChaves.setContaTransacional(contaTransacional);
    contaTransacionalChaves.setChaveId(chaveInfoVO.getChaveId());
    contaTransacionalChaves.setIspbContaParticipante(chaveInfoVO.getConta().getIspbParticipante());
    contaTransacionalChaves.setAgencia(chaveInfoVO.getConta().getAgencia());
    contaTransacionalChaves.setTipoChave(
        getTipoChaveByDescricao(chaveInfoVO.getChave().getTipo().getDescricao()));
    contaTransacionalChaves.setTipoPessoa(
        getTipoPessoaByDescricao(chaveInfoVO.getPessoa().getTipo().getDescricao()));
    contaTransacionalChaves.setNumeroConta(chaveInfoVO.getConta().getNumero());
    contaTransacionalChaves.setTipoConta(
        getTipoContaByDescricao(chaveInfoVO.getConta().getTipo().getDescricao()));
    contaTransacionalChaves.setNome(chaveInfoVO.getPessoa().getNome());
    contaTransacionalChaves.setInscricaoNacional(chaveInfoVO.getPessoa().getInscricaoNacional());
    contaTransacionalChaves.setValorChave(chaveInfoVO.getChave().getValor());
    contaTransacionalChaves.setTransactionId(chaveInfoResponse.getTransactionId());
    contaTransacionalChaves.setDataInclusao(LocalDateTime.now());
    contaTransacionalChavesRepository.save(contaTransacionalChaves);
  }

  private TipoChavePix getTipoChaveById(String idTipoChave) {
    TipoChavePix tipoChave = tipoChavePixService.findById(Integer.valueOf(idTipoChave));
    if (tipoChave != null) {
      return tipoChave;
    } else {
      throw new GenericServiceException("Não foi possível encontrar tipo de chave");
    }
  }

  private TipoChavePix getTipoChaveByDescricao(String descricao) {
    Optional<TipoChavePix> tipoChavePixOptional =
        tipoChavePixService.findFirstByDescricao(descricao);
    if (tipoChavePixOptional.isPresent()) {
      return tipoChavePixOptional.get();
    } else {
      throw new GenericServiceException("Não foi possível encontrar tipo de chave");
    }
  }

  private TipoPessoa getTipoPessoaById(String idTipoPessoa) {
    return tipoPessoaService.findById(Integer.valueOf(idTipoPessoa));
  }

  private TipoPessoa getTipoPessoaByDescricao(String tipoPessoaDescricao) {
    switch (tipoPessoaDescricao) {
      case "Fisica":
        return tipoPessoaService.findById(1);
      case "Juridica":
        return tipoPessoaService.findById(2);
      default:
        return null;
    }
  }

  private TipoContaPix getTipoContaByDescricao(String descricao) {
    Optional<TipoContaPix> tipoContaPixOptional =
        tipoContaPixService.findFirstByDescricao(descricao);
    return tipoContaPixOptional.orElseThrow(
        () -> new GenericServiceException("Não foi possível encontrar tipo de conta"));
  }

  public ResponseEntity<ChaveInfoReduzidaResponse> consultarChave(
      String chaveDict, SecurityUserPortador userPortador)
      throws InvocationTargetException, IllegalAccessException {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());
    String documento =
        userPortador.getDocumentoAcesso() != null
            ? userPortador.getDocumentoAcesso()
            : userPortador.getCpf();
    return bancoRendimentoClient.consultarChave(chaveDict, instituicaoPix, documento);
  }

  public ResponseEntity<ChaveInfoReduzidaResponse> consultarChave(
      String chaveDict, SecurityUserCorporativo userCorporativo)
      throws InvocationTargetException, IllegalAccessException {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userCorporativo.getIdInstituicao());
    return bancoRendimentoClient.consultarChave(
        chaveDict, instituicaoPix, userCorporativo.getUsername());
  }

  public ResponseEntity<EstatisticasChaveResponse> consultarEstatisticasChave(
      String chaveDict, SecurityUserPortador userPortador) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());

    return bancoRendimentoClient.consultarEstatisticasChave(chaveDict, instituicaoPix);
  }

  public List<ContaTransacionalChaves> findByValorChaveAndInscricaoNacionalAndDeletedFalse(
      String chave, String documento) {
    return contaTransacionalChavesRepository.findByValorChaveAndInscricaoNacionalAndDeletedFalse(
        chave, documento);
  }

  public ResponseEntity<DeleteInscricaoResponse> validarDeletarChave(
      DeletarChaveDTO deletarChaveDTO, SecurityUserPortador userPortador) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userPortador.getIdInstituicao());
    return this.deletarChave(
        deletarChaveDTO.getChave(),
        deletarChaveDTO.getMotivoExclusaoChaveRequest(),
        instituicaoPix,
        userPortador.getCpf());
  }

  public ResponseEntity<DeleteInscricaoResponse> validarDeletarChave(
      DeletarChaveDTO deletarChaveDTO, SecurityUserCorporativo userCorporativo) {
    InstituicaoPix instituicaoPix =
        instituicaoPixService.findFirstByIdInstituicaoOrderByIdWithException(
            userCorporativo.getIdInstituicao());
    CorporativoResponsavel responsavel =
        this.corporativoService.findResponsavelAtivoByDocumento(userCorporativo.getUsername());
    CorporativoResponsavelCredencial responsavelCredencial =
        this.corporativoService.findResponsavelCredencial(responsavel.getId());
    Credencial credencial =
        this.credencialService.findByIdCredencial(responsavelCredencial.getIdCredencial());
    return this.deletarChave(
        deletarChaveDTO.getChave(),
        deletarChaveDTO.getMotivoExclusaoChaveRequest(),
        instituicaoPix,
        credencial.getPessoa().getDocumento());
  }

  private ResponseEntity<DeleteInscricaoResponse> deletarChave(
      String chaveDict,
      MotivoExclusaoChaveRequest motivoExclusaoChaveRequest,
      InstituicaoPix instituicaoPix,
      String documento) {

    List<ContaTransacionalChaves> chaves =
        findByValorChaveAndInscricaoNacionalAndDeletedFalse(chaveDict, documento);
    if (chaves == null || chaves.isEmpty()) {
      throw new GenericServiceException(
          "Chave \"" + chaveDict + "\" não encontrada para o portador de documento " + documento);
    }
    if (chaves.size() > 1) {
      throw new GenericServiceException(
          "Mais de uma chave \""
              + chaveDict
              + "\" ativa encontrada "
              + "para o portador de documento "
              + documento);
    }
    ContaTransacionalChaves chave = chaves.get(0);

    travaContasService.travaContas(
        chave.getContaTransacional().getConta().getIdConta(), Servicos.PIX);

    ResponseEntity<DeleteInscricaoResponse> response =
        bancoRendimentoClient.deletarChave(chaveDict, motivoExclusaoChaveRequest, instituicaoPix);

    if (response.getStatusCode().is2xxSuccessful()) {
      chave.setDeleted(true);
      contaTransacionalChavesRepository.save(chave);
    }
    return response;
  }

  @Transactional
  public void atualizarStatusDeReivindicacao(
      NotificacaoReivindicacaoForm notificacaoReivindicacaoForm) {

    if (notificacaoReivindicacaoForm.getStatus().equals(SituacaoReivindicacaoEnum.CONFIRMED)
        && notificacaoReivindicacaoForm.getIsDonorPerson() != null
        && notificacaoReivindicacaoForm.getIsDonorPerson().equals(Boolean.TRUE)) {

      Optional<ContaTransacionalChaves> chaveOptional =
          contaTransacionalChavesRepository
              .findFirstByValorChaveAndInscricaoNacionalAndReivindicadaFalseOrderByIdDesc(
                  notificacaoReivindicacaoForm.getKey(),
                  notificacaoReivindicacaoForm.getDonor().getTaxIdNumber());

      if (chaveOptional.isPresent()) {
        ContaTransacionalChaves chave = chaveOptional.get();
        chave.setReivindicada(true);
        contaTransacionalChavesRepository.save(chave);
      }

      trataContatosDaChaveReivindicada(notificacaoReivindicacaoForm);
    }
  }

  private void trataContatosDaChaveReivindicada(
      NotificacaoReivindicacaoForm notificacaoReivindicacaoForm) {
    List<ContatoPix> contatos =
        contatoPixService.encontraContatosPorValorChave(notificacaoReivindicacaoForm.getKey());
    if (contatos != null && !contatos.isEmpty()) {
      contatoPixService.removerContatosDeChaveReivindicada(contatos);
    }
  }

  public ContaTransacionalChaves findByContaTransacionalAndValorChave(
      ContaTransacional conta, String valor) {
    return contaTransacionalChavesRepository.findByContaTransacionalAndValorChave(conta, valor);
  }

  public Optional<ContaTransacionalChaves> descobreChavePixAssociadaAConta(
      String valorChave, Long idConta) {
    return contaTransacionalChavesRepository.descobreChavePixAssociadaAConta(valorChave, idConta);
  }
}
