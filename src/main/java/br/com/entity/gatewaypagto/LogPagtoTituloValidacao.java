package br.com.entity.gatewaypagto;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Transient;
import org.hibernate.annotations.Type;

@Entity
@Table(name = "log_pagto_tit_validacao", schema = "gatewaypagto")
@SequenceGenerator(
    name = "seq_id_log_pagto_tit",
    sequenceName = "gatewaypagto.seq_id_log_pagto_tit",
    allocationSize = 1,
    initialValue = 1)
public class LogPagtoTituloValidacao implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_id_log_pagto_tit")
  @Column(name = "id_log_pagto_tit")
  private Long idLogPagtoTitulo;

  @Column(name = "id_conta")
  private Long idConta;

  /*
  * 	LinhaDigitavel String(48) Obrigatório Informa a linha digitável.

  */
  @Column(name = "linha_digitavel")
  private String linhaDigitavel;

  /*
   * inicio campos resposta
   */

  /** StatusTransacao String(30) Obrigatório Define o status da operação. */
  @Column(name = "status_transacao")
  private String statusTransacao;

  /*
  Cedente String(50) Obrigatório Informa o nome do convênio da linha digitável
  informada.
  */
  private String cedente;

  /*
  DataVencimento DateTime Não obrigatório Informa a data vencimento contida na linha digitável.
  *
  */

  @Column(name = "data_vencimento")
  private Date dataVencimento;

  /*
  *
  HoraRecebimentoFim String(5) Não obrigatório Informa a Hora recebimento Inicial do convênio.(HH:MM)
  */
  @Column(name = "hora_recebimento_fim")
  private String horaRecebimentoFim;

  /*
  HoraRecebimentoInicio String(5) Não obrigatório Informa a Hora corte do convênio. (HH:MM)
  *
  */
  @Column(name = "hora_recebimento_inicio")
  private String horaRecebimentoInicio;

  /*
  	Valor Double Obrigatório Retorna o valor do pagamento contido na linha digitável.
  */
  private Double valor;

  /*
   * ValorMaximo Double Não obrigatório Informa o valor máximo para pagamento.
   */

  @Column(name = "valor_maximo")
  private Double valorMaximo;

  /*
  ValorMinimo Double Não obrigatório Informa o valor mínimo para pagamento.
  */
  @Column(name = "valor_minimo")
  private Double valorMinimo;

  /*
  CpfCnpjBeneficiario String(18) Obrigatório Cpf ou Cnpj
  */
  @Column(name = "documento_beneficiario")
  private String documentoBeneficiario;

  /*
  CpfCnpjSacado String(18) Obrigatório Cpf ou Cnpj do Sacado.

  */
  @Column(name = "documento_sacado")
  private String documentoSacado;

  /*
  DataLimitePagamento Datetime Obrigatório Data da baixa do boleto.
  */
  @Column(name = "data_limite_pagamento")
  private Date dataLimitePagamento;

  /*
  DataProximoDiaUtil Datetime Não obrigatório Próximo dia Útil.
  */
  @Column(name = "data_proximo_dia_util")
  private Date dataProximoDiaUtil;

  /*
  DataVencimentoRegistro Datetime Obrigatório Data Vencimento.
  */
  @Column(name = "data_vencimento_registro")
  private Date dataVencimentoRegistro;

  /*
  IndPermiteAlterarValor Bool Obrigatório Permissão de alteração do valor do boleto.

  */
  @Column(name = "permite_alterar_valor")
  @Type(type = "numeric_boolean")
  private Boolean permiteAlterarValor;

  /*
   * NomeBeneficiario String(100) Obrigatório Nome do Beneficiario.
   */
  @Column(name = "nome_beneficiario")
  private String nomeBeneficiario;

  /*
  	NomeSacado String(100) Obrigatório Nome do Sacado.
  */
  @Column(name = "nome_sacado")
  private String nomeSacado;

  /*
  ValorDescontoCalculado Double Obrigatório Valor do desconto já calculado.
  */
  @Column(name = "valor_desconto_calculado")
  private Double valorDescontoCalculado;

  /*
  ValorJurosCalculado Double Obrigatório Valor juros já calculado.

  */
  @Column(name = "valor_juros_calculado")
  private Double valorJurosCalculado;

  /*
  ValorMaximoPagamento Double Obrigatório Valor máximo permitido para pagamento do título.
  */
  @Column(name = "valor_maximo_pagamento")
  private Double valorMaximoPagamento;

  /*
  ValorMinimoPagamento Double Obrigatório Valor mínimo permitido para pagamento do título.
  */
  @Column(name = "valor_minimo_pagamento")
  private Double valorMinimoPagamento;

  /*
  ValorMultaCalculado Double Obrigatório Valor multa já calculado.

  */
  @Column(name = "valor_multa_calculado")
  private Double valorMultaCalculado;

  /*
  ValorNominal Double Obrigatório Valor nominal do título.
  */
  @Column(name = "valor_nominal")
  private Double valorNominal;

  /*
  ValorPagamentoAtualizado Double Obrigatório Valor atualizado a ser pago do titulo.
  */
  @Column(name = "valor_pagamento_atualizado")
  private Double valorPagamentoAtualizado;

  /*
  ValorTotalAbatimento Double Obrigatório Valor total de descontos/Abatimentos.
  */
  @Column(name = "valor_total_abatimento")
  private Double valorTotalAbatimento;

  /*
  ValorTotalAcrescimo Double Obrigatório Valor total de juros/multa
  */
  @Column(name = "valor_total_acrescimo")
  private Double valorTotalAcrescimo;

  /*
   * fim campos resposta
   */
  @Column(name = "dt_hr_inicio")
  private Date dataInicio;

  @Column(name = "dt_hr_fim")
  private Date dataFim;

  @Column(name = "id_usuario_inclusao")
  private Long idUsuarioInclusao;

  //
  @Column(name = "data_liquidacao")
  private Date dataLiquidacao;

  @Column(name = "ind_proxima_liquidacao")
  private String indProximaLiquidacao;

  @Column(name = "protocolo_id_consulta")
  private Long protocoloIdConsulta;

  @Column(name = "tipo_servico")
  private String tipoServico;

  @Column(name = "mensagem_erro")
  private String mensagemErro;

  // codigo de barras da conta
  @Column(name = "cod_barras")
  private String codigoBarras;

  /** Codigo para identificar o tipo de situacao do boleto (Rendimento) */
  @Column(name = "codigo_especie_titulo")
  private String codigoEspecieTitulo;

  /** Descrição da situação/motivo do boleto */
  @Column(name = "situacao")
  private String situacao;

  @Column(name = "codigo_situacao")
  private String codigoSituacao;

  @Column(name = "tipo_autorizacao_recebimento_valor_divergente")
  private Integer tipoAutorizacaoRecebimentoValorDivergente;

  @Column(name = "valor_total")
  private Double valorTotal;

  @Transient private String codResultado;

  public Long getIdLogPagtoTitulo() {
    return idLogPagtoTitulo;
  }

  public void setIdLogPagtoTitulo(Long idLogPagtoTitulo) {
    this.idLogPagtoTitulo = idLogPagtoTitulo;
  }

  public Long getIdConta() {
    return idConta;
  }

  public void setIdConta(Long idConta) {
    this.idConta = idConta;
  }

  public String getLinhaDigitavel() {
    return linhaDigitavel;
  }

  public void setLinhaDigitavel(String linhaDigitavel) {
    this.linhaDigitavel = linhaDigitavel;
  }

  public String getStatusTransacao() {
    return statusTransacao;
  }

  public void setStatusTransacao(String statusTransacao) {
    this.statusTransacao = statusTransacao;
  }

  public String getCedente() {
    return cedente;
  }

  public void setCedente(String cedente) {
    this.cedente = cedente;
  }

  public Date getDataVencimento() {
    return dataVencimento;
  }

  public void setDataVencimento(Date dataVencimento) {
    this.dataVencimento = dataVencimento;
  }

  public String getHoraRecebimentoFim() {
    return horaRecebimentoFim;
  }

  public void setHoraRecebimentoFim(String horaRecebimentoFim) {
    this.horaRecebimentoFim = horaRecebimentoFim;
  }

  public String getHoraRecebimentoInicio() {
    return horaRecebimentoInicio;
  }

  public void setHoraRecebimentoInicio(String horaRecebimentoInicio) {
    this.horaRecebimentoInicio = horaRecebimentoInicio;
  }

  public Double getValor() {
    return valor;
  }

  public void setValor(Double valor) {
    this.valor = valor;
  }

  public Double getValorMaximo() {
    return valorMaximo;
  }

  public void setValorMaximo(Double valorMaximo) {
    this.valorMaximo = valorMaximo;
  }

  public Double getValorMinimo() {
    return valorMinimo;
  }

  public void setValorMinimo(Double valorMinimo) {
    this.valorMinimo = valorMinimo;
  }

  public String getDocumentoBeneficiario() {
    return documentoBeneficiario;
  }

  public void setDocumentoBeneficiario(String documentoBeneficiario) {
    this.documentoBeneficiario = documentoBeneficiario;
  }

  public String getDocumentoSacado() {
    return documentoSacado;
  }

  public void setDocumentoSacado(String documentoSacado) {
    this.documentoSacado = documentoSacado;
  }

  public Date getDataLimitePagamento() {
    return dataLimitePagamento;
  }

  public void setDataLimitePagamento(Date dataLimitePagamento) {
    this.dataLimitePagamento = dataLimitePagamento;
  }

  public Date getDataProximoDiaUtil() {
    return dataProximoDiaUtil;
  }

  public void setDataProximoDiaUtil(Date dataProximoDiaUtil) {
    this.dataProximoDiaUtil = dataProximoDiaUtil;
  }

  public Date getDataVencimentoRegistro() {
    return dataVencimentoRegistro;
  }

  public void setDataVencimentoRegistro(Date dataVencimentoRegistro) {
    this.dataVencimentoRegistro = dataVencimentoRegistro;
  }

  public Boolean getPermiteAlterarValor() {
    return permiteAlterarValor;
  }

  public void setPermiteAlterarValor(Boolean permiteAlterarValor) {
    this.permiteAlterarValor = permiteAlterarValor;
  }

  public String getNomeBeneficiario() {
    return nomeBeneficiario;
  }

  public void setNomeBeneficiario(String nomeBeneficiario) {
    this.nomeBeneficiario = nomeBeneficiario;
  }

  public String getNomeSacado() {
    return nomeSacado;
  }

  public void setNomeSacado(String nomeSacado) {
    this.nomeSacado = nomeSacado;
  }

  public Double getValorDescontoCalculado() {
    return valorDescontoCalculado;
  }

  public void setValorDescontoCalculado(Double valorDescontoCalculado) {
    this.valorDescontoCalculado = valorDescontoCalculado;
  }

  public Double getValorJurosCalculado() {
    return valorJurosCalculado;
  }

  public void setValorJurosCalculado(Double valorJurosCalculado) {
    this.valorJurosCalculado = valorJurosCalculado;
  }

  public Double getValorMaximoPagamento() {
    return valorMaximoPagamento;
  }

  public void setValorMaximoPagamento(Double valorMaximoPagamento) {
    this.valorMaximoPagamento = valorMaximoPagamento;
  }

  public Double getValorMinimoPagamento() {
    return valorMinimoPagamento;
  }

  public void setValorMinimoPagamento(Double valorMinimoPagamento) {
    this.valorMinimoPagamento = valorMinimoPagamento;
  }

  public Double getValorMultaCalculado() {
    return valorMultaCalculado;
  }

  public void setValorMultaCalculado(Double valorMultaCalculado) {
    this.valorMultaCalculado = valorMultaCalculado;
  }

  public Double getValorNominal() {
    return valorNominal;
  }

  public void setValorNominal(Double valorNominal) {
    this.valorNominal = valorNominal;
  }

  public Double getValorPagamentoAtualizado() {
    return valorPagamentoAtualizado;
  }

  public void setValorPagamentoAtualizado(Double valorPagamentoAtualizado) {
    this.valorPagamentoAtualizado = valorPagamentoAtualizado;
  }

  public Double getValorTotalAbatimento() {
    return valorTotalAbatimento;
  }

  public void setValorTotalAbatimento(Double valorTotalAbatimento) {
    this.valorTotalAbatimento = valorTotalAbatimento;
  }

  public Double getValorTotalAcrescimo() {
    return valorTotalAcrescimo;
  }

  public void setValorTotalAcrescimo(Double valorTotalAcrescimo) {
    this.valorTotalAcrescimo = valorTotalAcrescimo;
  }

  public Date getDataInicio() {
    return dataInicio;
  }

  public void setDataInicio(Date dataInicio) {
    this.dataInicio = dataInicio;
  }

  public Date getDataFim() {
    return dataFim;
  }

  public void setDataFim(Date dataFim) {
    this.dataFim = dataFim;
  }

  public Long getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Long idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Date getDataLiquidacao() {
    return dataLiquidacao;
  }

  public void setDataLiquidacao(Date dataLiquidacao) {
    this.dataLiquidacao = dataLiquidacao;
  }

  public String getIndProximaLiquidacao() {
    return indProximaLiquidacao;
  }

  public void setIndProximaLiquidacao(String indProximaLiquidacao) {
    this.indProximaLiquidacao = indProximaLiquidacao;
  }

  public Long getProtocoloIdConsulta() {
    return protocoloIdConsulta;
  }

  public void setProtocoloIdConsulta(Long protocoloIdConsulta) {
    this.protocoloIdConsulta = protocoloIdConsulta;
  }

  public String getTipoServico() {
    return tipoServico;
  }

  public void setTipoServico(String tipoServico) {
    this.tipoServico = tipoServico;
  }

  public String getMensagemErro() {
    return mensagemErro;
  }

  public void setMensagemErro(String mensagemErro) {
    this.mensagemErro = mensagemErro;
  }

  @Override
  public String toString() {
    return "LogPagtoTituloValidacao{"
        + "idLogPagtoTitulo="
        + idLogPagtoTitulo
        + ", idConta="
        + idConta
        + ", linhaDigitavel='"
        + linhaDigitavel
        + '\''
        + ", statusTransacao='"
        + statusTransacao
        + '\''
        + ", cedente='"
        + cedente
        + '\''
        + ", dataVencimento="
        + dataVencimento
        + ", horaRecebimentoFim='"
        + horaRecebimentoFim
        + '\''
        + ", horaRecebimentoInicio='"
        + horaRecebimentoInicio
        + '\''
        + ", valor="
        + valor
        + ", valorMaximo="
        + valorMaximo
        + ", valorMinimo="
        + valorMinimo
        + ", documentoBeneficiario='"
        + documentoBeneficiario
        + '\''
        + ", documentoSacado='"
        + documentoSacado
        + '\''
        + ", dataLimitePagamento="
        + dataLimitePagamento
        + ", dataProximoDiaUtil="
        + dataProximoDiaUtil
        + ", dataVencimentoRegistro="
        + dataVencimentoRegistro
        + ", permiteAlterarValor="
        + permiteAlterarValor
        + ", nomeBeneficiario='"
        + nomeBeneficiario
        + '\''
        + ", nomeSacado='"
        + nomeSacado
        + '\''
        + ", valorDescontoCalculado="
        + valorDescontoCalculado
        + ", valorJurosCalculado="
        + valorJurosCalculado
        + ", valorMaximoPagamento="
        + valorMaximoPagamento
        + ", valorMinimoPagamento="
        + valorMinimoPagamento
        + ", valorMultaCalculado="
        + valorMultaCalculado
        + ", valorNominal="
        + valorNominal
        + ", valorPagamentoAtualizado="
        + valorPagamentoAtualizado
        + ", valorTotalAbatimento="
        + valorTotalAbatimento
        + ", valorTotalAcrescimo="
        + valorTotalAcrescimo
        + ", dataInicio="
        + dataInicio
        + ", dataFim="
        + dataFim
        + ", idUsuarioInclusao="
        + idUsuarioInclusao
        + ", dataLiquidacao="
        + dataLiquidacao
        + ", indProximaLiquidacao='"
        + indProximaLiquidacao
        + '\''
        + ", protocoloIdConsulta="
        + protocoloIdConsulta
        + ", tipoServico='"
        + tipoServico
        + '\''
        + ", mensagemErro='"
        + mensagemErro
        + '\''
        + ", codigoBarras='"
        + codigoBarras
        + '\''
        + ", codigoEspecieTitulo='"
        + codigoEspecieTitulo
        + '\''
        + ", situacao='"
        + situacao
        + '\''
        + ", codigoSituacao='"
        + codigoSituacao
        + '\''
        + ", tipoAutorizacaoRecebimentoValorDivergente="
        + tipoAutorizacaoRecebimentoValorDivergente
        + ", valorTotal="
        + valorTotal
        + ", codResultado='"
        + codResultado
        + '\''
        + '}';
  }

  public String getCodResultado() {
    return codResultado;
  }

  public void setCodResultado(String codResultado) {
    this.codResultado = codResultado;
  }

  public String getCodigoBarras() {
    return codigoBarras;
  }

  public void setCodigoBarras(String codigoBarras) {
    this.codigoBarras = codigoBarras;
  }

  public String getCodigoEspecieTitulo() {
    return codigoEspecieTitulo;
  }

  public void setCodigoEspecieTitulo(String codigoEspecieTitulo) {
    this.codigoEspecieTitulo = codigoEspecieTitulo;
  }

  public String getSituacao() {
    return situacao;
  }

  public void setSituacao(String situacao) {
    this.situacao = situacao;
  }

  public Integer getTipoAutorizacaoRecebimentoValorDivergente() {
    return tipoAutorizacaoRecebimentoValorDivergente;
  }

  public void setTipoAutorizacaoRecebimentoValorDivergente(
      Integer tipoAutorizacaoRecebimentoValorDivergente) {
    this.tipoAutorizacaoRecebimentoValorDivergente = tipoAutorizacaoRecebimentoValorDivergente;
  }

  public Double getValorTotal() {
    return valorTotal;
  }

  public void setValorTotal(Double valorTotal) {
    this.valorTotal = valorTotal;
  }

  public String getCodigoSituacao() {
    return codigoSituacao;
  }

  public void setCodigoSituacao(String codigoSituacao) {
    this.codigoSituacao = codigoSituacao;
  }

  @Override
  public boolean equals(Object o) {
    if (o == null || getClass() != o.getClass()) return false;

    LogPagtoTituloValidacao that = (LogPagtoTituloValidacao) o;
    return idConta.equals(that.idConta);
  }

  @Override
  public int hashCode() {
    return idConta.hashCode();
  }
}
