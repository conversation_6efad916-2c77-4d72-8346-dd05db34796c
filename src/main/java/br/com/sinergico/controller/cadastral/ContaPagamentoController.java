package br.com.sinergico.controller.cadastral;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPagamentoHistoricoStatusView;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.DocumentoConta;
import br.com.entity.cadastral.PerfilTarifario;
import br.com.entity.cadastral.PortadorLogin;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoPerfilTarifario;
import br.com.entity.cadastral.Proposta;
import br.com.entity.suporte.LogRegistroAlterarStatus;
import br.com.entity.suporte.LogUltimasContas;
import br.com.entity.suporte.TipoStatus;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.itspay.acquirer.iso.client.AcquirerClientException;
import br.com.json.bean.brb.BuscaSaldoResponse;
import br.com.json.bean.brb.BuscarAgenciaContaResponse;
import br.com.json.bean.brb.TransferenciaContasBRBRequest;
import br.com.json.bean.cadastral.AlterarAdicionalPessoa;
import br.com.json.bean.cadastral.AtivarInativarProdutoRequest;
import br.com.json.bean.cadastral.BuscaContaDocumentoProduto;
import br.com.json.bean.cadastral.CadastrarContaPagamentoPessoaELoginPortadorRequest;
import br.com.json.bean.cadastral.CadastrarContaPagamentoPessoaRequest;
import br.com.json.bean.cadastral.CadastrarPessoaPDAFRequest;
import br.com.json.bean.cadastral.CadastrarPortadorLogin;
import br.com.json.bean.cadastral.CartaoAdicionalRequest;
import br.com.json.bean.cadastral.ContaPagamentoResponse;
import br.com.json.bean.cadastral.CredencialResumida;
import br.com.json.bean.cadastral.CriarContaPagamento;
import br.com.json.bean.cadastral.DadosConta;
import br.com.json.bean.cadastral.DadosCredencial;
import br.com.json.bean.cadastral.DadosPortador;
import br.com.json.bean.cadastral.DesbloquearCredencialCompleta;
import br.com.json.bean.cadastral.DetalhesConta;
import br.com.json.bean.cadastral.ExtratoTransRejeitada;
import br.com.json.bean.cadastral.GetDadosConsumo;
import br.com.json.bean.cadastral.GetDataHoraExtrato;
import br.com.json.bean.cadastral.GetPerfilTariafarioResponse;
import br.com.json.bean.cadastral.HierarquiaPontoRelacionamentoRequest;
import br.com.json.bean.cadastral.LancamentoContaEstabelecimentoVo;
import br.com.json.bean.cadastral.ListarContaPagamentoGrupoResponse;
import br.com.json.bean.cadastral.PerfilTarifas;
import br.com.json.bean.cadastral.PessoaAdicionalResponse;
import br.com.json.bean.cadastral.SolicitacaoReplicacaoCredencialResumoResponse;
import br.com.json.bean.cadastral.TransMesmaInstituicaoIdCredencial;
import br.com.json.bean.cadastral.TransacoesNaoApresentadas;
import br.com.json.bean.cadastral.TransferenciaCobrarComQrCode;
import br.com.json.bean.cadastral.TransferenciaContaCorrenteRequest;
import br.com.json.bean.cadastral.TransferenciaMesmaInstituicao;
import br.com.json.bean.cadastral.TransferenciaMesmaInstituicaoViaWeb;
import br.com.json.bean.cadastral.TransferenciaMesmoBolso;
import br.com.json.bean.cadastral.TrocarStatusDestinoRequest;
import br.com.json.bean.cadastral.ValidarCobrarTranferenciaQrCodeDTO;
import br.com.json.bean.cadastral.ValidarTransferenciaMesmaInstituicaoDTO;
import br.com.json.bean.suporte.AlterarStatusCredencialResponseVO;
import br.com.json.bean.suporte.ExtratoRequestVo;
import br.com.json.bean.suporte.GetDadosCredito;
import br.com.json.bean.suporte.GetExtratoCredencial;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.json.bean.suporte.GetSaldoGrupoResponse;
import br.com.json.bean.suporte.Instituicao;
import br.com.json.bean.suporte.LogSMSContaResponse;
import br.com.json.bean.suporte.LogUltimaContaModel;
import br.com.json.bean.suporte.SmsContaByFiltersRequest;
import br.com.json.bean.transacional.TransferenciaEntreContasB2b;
import br.com.sinergico.annotation.DesabilitarVerificacaoPortador;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.enums.EmailBlacklist;
import br.com.sinergico.enums.HierarquiaType;
import br.com.sinergico.enums.TipoRelatorio;
import br.com.sinergico.facade.cadastral.ContaPagamentoFacade;
import br.com.sinergico.repository.cadastral.CredencialRepository;
import br.com.sinergico.security.MetodoSegurancaTransacaoService;
import br.com.sinergico.security.SecHierarquia;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.AlterarLimiteContaPos;
import br.com.sinergico.service.cadastral.AtualizarCargaOuLimiteRequest;
import br.com.sinergico.service.cadastral.ContaPagamentoBRBService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaService;
import br.com.sinergico.service.cadastral.PerfilTarifarioService;
import br.com.sinergico.service.cadastral.PortadorLoginContaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.PropostaService;
import br.com.sinergico.service.suporte.ExclusaoContaService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.AlterarStatusExclusaoVO;
import br.com.sinergico.vo.ContaPagamentoCadastroAvanceVO;
import br.com.sinergico.vo.ContaPagamentoHistoricoStatusViewVO;
import br.com.sinergico.vo.ContaPagamentoIntegracaoVO;
import br.com.sinergico.vo.ContaPagamentoVO;
import br.com.sinergico.vo.ContasPorProdutoPortador;
import br.com.sinergico.vo.DadosPortadorPontoRelacionamentoB2BVO;
import br.com.sinergico.vo.ExclusaoContaVO;
import br.com.sinergico.vo.SolicitacaoCancelamentoVO;
import com.itextpdf.text.DocumentException;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Date;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/portador/conta")
public class ContaPagamentoController extends UtilController {

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private ContaPagamentoFacade facade;

  @Autowired private PerfilTarifarioService perfilTarifarioService;

  @Autowired private HttpServletRequest request;

  @Autowired private EntityManager em;

  @Autowired private ContaService contaService;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired private PortadorLoginContaService portadorLoginContaService;

  @Autowired private ContaPagamentoBRBService contaPagamentoBRBService;

  @Autowired private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacioService;

  @Autowired private CredencialRepository credencialRepository;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private MetodoSegurancaTransacaoService metodoSegurancaTransacaoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodInstConfigService;

  @Autowired private ExclusaoContaService exclusaoContaService;

  @Autowired private PropostaService propostaService;

  private static final String MSG_INADIPLENCIA = "Conta bloqueada por inadimplência";

  @ApiOperation(
      value = "Transferência entre contas da mesma empresa, efetuado por usuário B2B",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/transferencia/b2b",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_CONTAS_MESMA_EMPRESA_B2B"})
  public ResponseEntity<Map<String, Object>> transferenciaEntreContasB2b(
      @RequestBody @Valid TransferenciaEntreContasB2b model, BindingResult result) {

    Map<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);

    travaServicosService.travaServicos(
        user.getIdInstituicao(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);
    travaContasService.travaContas(
        model.getIdContaOrigem(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);
    travaContasService.travaContas(
        model.getIdContaDestino(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    Boolean isOk = facade.transferenciaEntreContasB2b(model, getTokenJWT(request), user, ipOrigem);
    if (isOk) {
      map.put("msg", "Transferência realizada com Sucesso!");
    } else {
      map.put(
          "erro",
          "Ocorreu algum erro durante a operação. Entre em contato com a instituição emissora para verificar o log desta operação.");
    }
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Realiza transferencia entre contas da Mesma Instituicao - portador",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/transferencia",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_MESMA_INSTITUICAO"})
  public ResponseEntity<Map<String, Object>> transferenciaMesmaInstituicao(
      @RequestBody @Valid TransferenciaMesmaInstituicao model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    travaServicosService.travaServicos(
        userPortador.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    ContaPagamento conta =
        contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
            Long.valueOf(model.getContaOrigem()), userPortador);
    travaContasService.travaContas(conta.getIdConta(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
    metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
        conta, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    facade.transferenciaMesmaInstituicao(
        model, getTokenJWT(request), Boolean.TRUE, userPortador.getDocumentoAcesso());
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Transferência realizada com Sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Realiza transferencia entre contas da Mesma Instituicao - BRB - Portador",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/transferencia-entre-contas-brb",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({ "ROLE_TRANFERENCIA_ENTRE_CONTAS_BRB" })
  public ResponseEntity<Map<String, Object>> transferenciaEntreContasBRB(
      @RequestBody @Valid TransferenciaContasBRBRequest model,
      HttpServletRequest request,
      BindingResult result) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }
    travaServicosService.travaServicos(
        user != null
            ? user.getIdInstituicao()
            : Objects.requireNonNull(userPortador).getIdInstituicao(),
        Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }
    boolean sucesso =
        contaPagamentoBRBService.transferenciaEntreContasBRB(
            model, getTokenJWT(request), userPortador.getCpf(), Boolean.TRUE);
    Map<String, Object> map = new HashMap<>();
    if (!sucesso) {
      map.put("msg", "Ocorreu um erro ao realizar a transferência!");
    }
    map.put("msg", "Transferência realizada com Sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Realiza transferencia entre contas da Mesma Instituicao utilizando apenas ids das Contas- portador-",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/transferencia/web-mobile",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({ "ROLE_TRANFERENCIA_ENTRE_CONTAS_MESMA_INSTITUICAO" })
  public ResponseEntity<Map<String, Object>> transferenciaMesmaInstituicaoWebMob(
      @RequestBody @Valid TransferenciaMesmaInstituicaoViaWeb model, BindingResult result) {

    SecurityUser user = null;
    SecurityUserPortador userPortador = null;
    try {
      user = getAuthenticatedUser(request, em);
    } catch (Exception e) {
      userPortador = getAuthenticatedPortador(request, em);
    }

    String servico = null;
    if (user != null) {
      travaServicosService.travaServicos(
          user.getIdInstituicao(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);
      travaContasService.travaContas(
          model.getIdContaOrigem(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);
      travaContasService.travaContas(
          model.getIdContaDestino(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);
      servico = Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE.getDescricao();
      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
          user, contaPagamentoService.findByIdNotNull(model.getIdContaOrigem()));
    }
    if (userPortador != null) {
      travaServicosService.travaServicos(
          userPortador.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
      travaContasService.travaContas(
          model.getIdContaOrigem(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
      travaContasService.travaContas(
          model.getIdContaDestino(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
      servico = Servicos.TRANSFERENCIA_INTERNA_PORTADOR.getDescricao();
      ContaPagamento conta =
          contaPagamentoService.validaEBuscaContaPeloRequestEPortador(
              model.getIdContaOrigem(), userPortador);
      metodoSegurancaTransacaoService.validaMetodoDeSeguranca(
          conta, Servicos.TRANSFERENCIA_INTERNA_PORTADOR);
    }

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    JcardResponse resp = facade.transferenciaMesmaInstituicaoViaWebMob(model, ipOrigem, servico);

    return getJcardResponse(resp, "Transferência realizada com Sucesso!");
  }

  @ApiOperation(
      value = "Realiza transferencia entre contas da Mesma Instituicao - Instituicao",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/transferencia/instituicao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_MESMA_INSTITUICAO"})
  public ResponseEntity<Map<String, Object>> transferenciaMesmaInstituicaoViaIssuer(
      @RequestBody @Valid TransferenciaMesmaInstituicao model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(
        user.getIdInstituicao(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);

    UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(
        user, contaPagamentoService.findByIdNotNull(Long.valueOf(model.getContaOrigem())));

    facade.transferenciaMesmaInstituicao(model, getTokenJWT(request), Boolean.FALSE, null);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Transferência realizada com Sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Realiza Transferência para outra conta corrente em outra Instituição - Portador",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/transferencia/conta/corrente",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_OUTRA_INSTITUICAO"})
  public ResponseEntity<Map<String, Object>> transferenciaContaCorrente(
      @RequestBody @Valid TransferenciaContaCorrenteRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUserPortador user = getAuthenticatedPortador(request, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.TED);
    travaContasService.travaContas(model.getContaCorrenteDestino(), Servicos.TED);

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    facade.transferenciaContaCorrente(
        model.getIdBancoDestino(),
        model.getIdAgenciaDestino(),
        model.getContaCorrenteDestino(),
        model.getIdInstituicaoOrigem(),
        model.getPinCredencialOrigem(),
        model.getIdCredencialOrigem(),
        model.getValorTransferencia(),
        getTokenJWT(request),
        Constantes.ID_USUARIO_INCLUSAO_PORTADOR,
        ipOrigem,
        Boolean.TRUE,
        model.getTipoContaBancaria());

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Transferência realizada com Sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Realiza Transferência para outra conta corrente em outra Instituição - Instituicao",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/transferencia/conta/corrente/instituicao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_OUTRA_INSTITUICAO"})
  public ResponseEntity<Map<String, Object>> transferenciaContaCorrenteViaIssuer(
      @RequestBody @Valid TransferenciaContaCorrenteRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUser user = getAuthenticatedUser(request, em);

    travaServicosService.travaServicos(
        user.getIdInstituicao(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);
    travaContasService.travaContas(
        model.getContaCorrenteDestino(), Servicos.TRANSFERENCIA_INTTERNA_BACKOFFICE);

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    facade.transferenciaContaCorrente(
        model.getIdBancoDestino(),
        model.getIdAgenciaDestino(),
        model.getContaCorrenteDestino(),
        model.getIdInstituicaoOrigem(),
        model.getPinCredencialOrigem(),
        model.getIdCredencialOrigem(),
        model.getValorTransferencia(),
        getTokenJWT(request),
        user.getIdUsuario(),
        ipOrigem,
        Boolean.FALSE,
        model.getTipoContaBancaria());

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Transferência realizada com Sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Cria uma contaPagamento completa para a pessoa",
      response = String.class,
      notes = "Retorna o numero da conta criada")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/criar-conta",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_CONTA_PAGAMENTO_COMPLETA"})
  public ResponseEntity<Map<String, Object>> cadastrarContaPagamentoPessoa(
      @RequestBody @Valid CadastrarContaPagamentoPessoaRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    travaServicosService.travaServicos(model.getIdInstituicao(), Servicos.CRIAR_CONTA_ISSUER);

    if (EmailBlacklist.isBlacklistMail(model.getEmail())) {
      throw new GenericServiceException(
          "O domínio utilizado no email está na blacklist. Email: " + model.getEmail());
    }

    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    // verificar se usuário é do tipo de ponto de relacionamento, aí vamos ver até
    if (HierarquiaType.PONTO_RELACIONAMENTO.value() == user.getHierarquiaType().value()) {

      model.setIdProcessadora(user.getIdProcessadora());
      model.setIdInstituicao(user.getIdInstituicao());
      model.setIdRegional(user.getIdRegional());
      model.setIdFilial(user.getIdFilial());
      model.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
      model.setCadastroOrigem(Constantes.ORIGEM_CADASTRO_MANUAL);
      if (model.getIdUsuarioInclusao() == null) model.setIdUsuarioInclusao(user.getIdUsuario());
    }

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(model.getIdInstituicao())) {
      Boolean permiteCadastrar = contaPagamentoService.verificaPermiteCadastrarConta(model);
      if (!permiteCadastrar) {
        throw new GenericServiceException(
            "É necessário possuir uma conta do Saldo Livre antes de cadastrar outra conta do Multiconta.");
      }
    }

    ContaPagamento conta = contaPagamentoService.cadastrarContaPagamentoPessoa(model, user);

    Long idGrupoProdutoCadastrado =
        prodInstConfigService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                conta.getIdProcessadora(),
                conta.getIdProdutoInstituicao(),
                conta.getIdInstituicao())
            .getIdGrupoProduto();

    if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(conta.getIdInstituicao())
        || (idGrupoProdutoCadastrado != null
            && Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(conta.getIdInstituicao()))) {

      PortadorLogin portadorLogin =
          portadorLoginService.resgataLoginMultiBeneficios(
              model.getIdProcessadora(),
              model.getIdInstituicao(),
              model.getDocumento(),
              idGrupoProdutoCadastrado);

      if (portadorLogin == null) {
        String senha = generatePassword();

        Credencial credencial =
            credencialRepository.findUltimaCredencialTitularConta(conta.getIdConta());

        CadastrarPortadorLogin cadastrarPortadorLogin = new CadastrarPortadorLogin();
        cadastrarPortadorLogin.setCredencial(credencial.getIdCredencial().toString());
        cadastrarPortadorLogin.setDataNascimento(model.getDataNascimento());
        cadastrarPortadorLogin.setCnpj(
            model.getDocumentoAcesso() != null ? model.getDocumento() : null);
        cadastrarPortadorLogin.setCpf(
            model.getDocumentoAcesso() != null ? model.getDocumentoAcesso() : model.getDocumento());
        cadastrarPortadorLogin.setSenha(senha);
        cadastrarPortadorLogin.setIdInstituicao(model.getIdInstituicao());
        cadastrarPortadorLogin.setIdProcessadora(model.getIdProcessadora());
        cadastrarPortadorLogin.setOrigemCadastroLogin(Constantes.ORIGEM_CADASTRO_MANUAL);
        cadastrarPortadorLogin.setEmail(model.getEmail());
        cadastrarPortadorLogin.setIsEsqueciSenha(false);
        cadastrarPortadorLogin.setGrupoAcesso(idGrupoProdutoCadastrado);
        cadastrarPortadorLogin.setTipoLogin(model.getTipoLogin());

        portadorLogin = portadorLoginService.preparePortadorLogin(cadastrarPortadorLogin);

        portadorLoginService.createPortadorLoginImediatamenteAposCriacaoConta(
            cadastrarPortadorLogin, portadorLogin, credencial);

        portadorLoginContaService.criaPortadoresConta(portadorLogin);
      } else {
        portadorLoginContaService.adicionaPortadorMultiConta(portadorLogin, conta.getIdConta());
      }
    }

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Conta Cadastrada com Sucesso!");
    map.put("numeroConta", conta);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Cria uma contaPagamento e portadorLogin",
      response = String.class,
      notes = "Retorna o numero da conta criada")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/criar-conta-issuer",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CRIAR_CONTA_E_PORTADOR_LOGIN"})
  public ResponseEntity<Map<String, Object>> cadastrarContaPagamentoEPortadorLogin(
      @RequestBody @Valid CadastrarContaPagamentoPessoaRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    travaServicosService.travaServicos(model.getIdInstituicao(), Servicos.CRIAR_CONTA_ISSUER);

    if (EmailBlacklist.isBlacklistMail(model.getEmail())) {
      throw new GenericServiceException(
          "O domínio utilizado no email está na blacklist. Email: " + model.getEmail());
    }

    // Buscando credenciais de acesso
    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    // verificar se usuário é do tipo de ponto de relacionamento, aí vamos ver até
    if (HierarquiaType.PONTO_RELACIONAMENTO.value() == user.getHierarquiaType().value()) {

      model.setIdProcessadora(user.getIdProcessadora());
      model.setIdInstituicao(user.getIdInstituicao());
      model.setIdRegional(user.getIdRegional());
      model.setIdFilial(user.getIdFilial());
      model.setIdPontoDeRelacionamento(user.getIdPontoDeRelacionamento());
      model.setCadastroOrigem(Constantes.ORIGEM_CADASTRO_MANUAL);
      if (model.getIdUsuarioInclusao() == null) model.setIdUsuarioInclusao(user.getIdUsuario());
    }

    ContaPagamento conta = contaPagamentoService.cadastrarContaPagamentoPessoa(model, user);

    String senha = generatePassword();

    Credencial credencial =
        credencialRepository.findUltimaCredencialTitularConta(conta.getIdConta());
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodInstConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            conta.getIdProcessadora(), conta.getIdProdutoInstituicao(), conta.getIdInstituicao());

    CadastrarPortadorLogin cadastrarPortadorLogin = new CadastrarPortadorLogin();
    cadastrarPortadorLogin.setCredencial(credencial.getIdCredencial().toString());
    cadastrarPortadorLogin.setDataNascimento(model.getDataNascimento());
    cadastrarPortadorLogin.setCnpj(
        model.getDocumentoAcesso() != null ? model.getDocumento() : null);
    cadastrarPortadorLogin.setCpf(
        model.getDocumentoAcesso() != null ? model.getDocumentoAcesso() : model.getDocumento());
    cadastrarPortadorLogin.setSenha(senha);
    cadastrarPortadorLogin.setIdInstituicao(model.getIdInstituicao());
    cadastrarPortadorLogin.setIdProcessadora(model.getIdProcessadora());
    cadastrarPortadorLogin.setOrigemCadastroLogin(Constantes.ORIGEM_CADASTRO_MANUAL);
    cadastrarPortadorLogin.setEmail(model.getEmail());
    cadastrarPortadorLogin.setIsEsqueciSenha(false);
    cadastrarPortadorLogin.setGrupoAcesso(model.getGrupoAcesso());
    cadastrarPortadorLogin.setTipoLogin(model.getTipoLogin());
    PortadorLogin portadorLogin = portadorLoginService.preparePortadorLogin(cadastrarPortadorLogin);

    portadorLoginService.createPortadorLoginImediatamenteAposCriacaoConta(
        cadastrarPortadorLogin, portadorLogin, credencial);

    portadorLoginContaService.criaPortadoresConta(portadorLogin);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Conta Cadastrada com Sucesso!");
    map.put("numeroConta", conta);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  private String generatePassword() {
    return Long.toHexString(Double.doubleToLongBits(Math.random())).substring(0, 8);
  }

  @ApiOperation(
      value = "Cria uma contaPagamento completa para a pessoa sem authorization",
      response = String.class,
      notes = "Retorna o numero da conta criada")
  @RequestMapping(
      value = "/criar-conta-sem-authorization",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_CONTA_PAGAMENTO_COMPLETA"})
  public ResponseEntity<Map<String, Object>> cadastrarContaPagamentoPessoaSemAuthorization(
      @RequestBody @Valid CadastrarContaPagamentoPessoaRequest model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    travaServicosService.travaServicos(model.getIdInstituicao(), Servicos.CRIAR_CONTA_PORTADOR);

    ContaPagamento conta =
        contaPagamentoService.cadastrarContaPagamentoPessoaSemAuthorization(model);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Conta Cadastrada com Sucesso!");
    map.put("numeroConta", conta);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Cria uma contaPagamento e credencial para uma pessoa ja cadastrada")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/criar-conta/pessoa",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CADASTRAR_CONTA_PAGAMENTO_AND_CREDENCIAL"})
  public ResponseEntity<Map<String, Object>> criarContasPagamentoECredenciais(
      @RequestBody @Valid CriarContaPagamento criarContaPagamento, BindingResult result) {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, HierarquiaType.PONTO_RELACIONAMENTO);

    criarContaPagamento = contaPagamentoService.adicionaHierarquia(criarContaPagamento, user);

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }
    facade.criarContasPagamentoECredenciais(criarContaPagamento, user);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Conta Cadastrada com Sucesso!");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Busca as tarifas de uma conta",
      response = GetPerfilTariafarioResponse.class,
      notes = "Busca as tarifas de uma conta")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-tarifas/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_TARIFAS_CONTA"})
  public ResponseEntity<GetPerfilTariafarioResponse> buscarTarifasConta(
      @PathVariable Long idConta) {

    GetPerfilTariafarioResponse buscarTarifasConta = facade.buscarTarifasConta(idConta);

    return new ResponseEntity<>(buscarTarifasConta, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca dados de uma conta",
      response = DadosConta.class,
      notes = "Busca dados gerais de uma conta")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-dados/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DADOS_CONTA"})
  public ResponseEntity<DadosConta> buscarDadosConta(@PathVariable Long idConta) {

    DadosConta dadosConta = facade.getDadosConta(idConta);

    return new ResponseEntity<>(dadosConta, HttpStatus.OK);
  }

  @ApiOperation(value = "Lista status de uma conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/listarStatusConta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_ESTADO_CONTA"})
  public ResponseEntity<List<TipoStatus>> listarStatusConta(@PathVariable Long idConta) {

    List<TipoStatus> listTipoStatus = contaPagamentoService.listaStatusIdConta(idConta);
    if (listTipoStatus == null) {
      listTipoStatus = new ArrayList<>();
    }
    return new ResponseEntity<>(listTipoStatus, HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar status conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/alterar-status-conta",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_ESTADO_CONTA"})
  public ResponseEntity<Boolean> alterarEstadoConta(
      @RequestBody @Valid TrocarStatusDestinoRequest model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    contaPagamentoService.alterarEstadoConta(
        model.getIdConta(), model.getStatusDestino(), user.getIdUsuario(), ipOrigem);

    return new ResponseEntity<>(true, HttpStatus.OK);
  }

  @ApiOperation(value = "Alterar status conta V2")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/v2/alterar-status-conta",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_ESTADO_CONTA"})
  public ResponseEntity<Map<String, Boolean>> alterarEstadoContaV2(
      @RequestBody @Valid TrocarStatusDestinoRequest model) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    contaPagamentoService.validaIdContaPeloRequestEPortador(model.getIdConta(), userPortador);
    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    contaPagamentoService.alterarEstadoConta(
        model.getIdConta(),
        model.getStatusDestino(),
        Constantes.ID_USUARIO_INCLUSAO_PORTADOR,
        ipOrigem);

    return new ResponseEntity<>(Collections.singletonMap("dados", true), HttpStatus.OK);
  }

  @ApiOperation(value = "Altera status de multiplas contas de uma vez")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/alterar-contas-arquivo",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_ESTADO_CONTA"})
  public ResponseEntity<AlterarStatusCredencialResponseVO> alterarStatusMultiplasContas(
      @RequestBody List<LogRegistroAlterarStatus> logs, HttpServletRequest request) {
    SecurityUser user = getAuthenticatedUser(request, em);
    AlterarStatusCredencialResponseVO responseVO = new AlterarStatusCredencialResponseVO();
    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");
    responseVO.setList(
        contaPagamentoService.alterarStatusMultiplasContas(logs, user.getIdUsuario(), ipOrigem));
    return new ResponseEntity<>(responseVO, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca dados titular da conta",
      response = DadosConta.class,
      notes = "Busca dados gerais de uma conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-dados-titular/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_TITULAR_CONTA"})
  public ResponseEntity<DadosPortador> buscarDadosTitularConta(@PathVariable Long idConta) {

    SecurityUser user = getAuthenticatedUser(request, em);

    DadosPortador dadosTitularConta = contaPagamentoService.getDadosTitularConta(idConta, user);

    return new ResponseEntity<>(dadosTitularConta, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca detalhes da conta",
      response = DetalhesConta.class,
      notes = "Busca detalhes da conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-detalhes/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DETALHES_COMPLETOS_CONTA"})
  public ResponseEntity<Map<String, Object>> buscarDetalhesConta(@PathVariable Long idConta) {

    HashMap<String, Object> map = new HashMap<>();

    SecurityUser user = getAuthenticatedUser(request, em);

    DetalhesConta detalhesConta = facade.getDetalhesConta(idConta, user);
    if (detalhesConta == null) {
      map.put("msg", "Conta inexistente!");
      return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
    }

    map.put("detalhesConta", detalhesConta);

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Gravar a ultima conta acessada pelo usuário",
      notes = "Salva a ultima conta acessada pelo usuario")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/salvar-log",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_GRAVAR_ULTIMA_CONTA_ACESSADA"})
  public ResponseEntity<Boolean> saveLogConta(
      @RequestBody @Valid LogUltimaContaModel model, BindingResult result) {

    SecurityUser user = getAuthenticatedUser(request, em);

    contaPagamentoService.saveLogUltimasContas(model, user);

    return new ResponseEntity<>(true, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca as ultimas contas acessadas",
      response = LogUltimasContas.class,
      notes = "Busca as ultimas contas acessadas")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-ultimas-contas",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_ULTIMAS_CONTAS_ACESSADAS"})
  public ResponseEntity<List<LogUltimasContas>> buscarUltimasContasAcessadas() {

    SecurityUser user = getAuthenticatedUser(request, em);

    List<LogUltimasContas> logs = contaPagamentoService.getUltimasContasAcessadas(user);
    return new ResponseEntity<>(logs, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca perfis tarifários que podem ser associados a conta",
      response = DetalhesConta.class,
      notes = "Busca perfis tarifários que podem ser associados a conta")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-detalhes/conta/perfis/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_PERFIS_TARIFARIOS_PARA_CONTA"})
  public ResponseEntity<List<PerfilTarifas>> buscarPerfisConta(@PathVariable Long idConta) {

    List<ProdutoPerfilTarifario> perfisDisponiveis =
        perfilTarifarioService.buscarPerfisTarifariosDisponiveis(idConta);
    List<PerfilTarifas> perfilTarifas = new ArrayList<PerfilTarifas>();

    for (ProdutoPerfilTarifario temp : perfisDisponiveis) {
      PerfilTarifas pf = new PerfilTarifas();
      pf.setIdPerfilTarifario(temp.getIdPefilTarifario());
      PerfilTarifario pfp = perfilTarifarioService.findById(temp.getIdPefilTarifario());
      pf.setDescPerfilTarifarioProduto(pfp.getDescPerfil());
      perfilTarifas.add(pf);
    }

    return new ResponseEntity<>(perfilTarifas, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca dados das credenciais da conta",
      response = DadosCredencial.class,
      notes = "Busca dados das credenciais da conta",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-dados-credenciais/conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_CONTA"})
  public ResponseEntity<List<DadosCredencial>> buscarDadosCredenciaisConta(
      @PathVariable Long idConta) {

    List<DadosCredencial> dadosCredenciais =
        contaPagamentoService.getDadosCredenciaisConta(idConta);

    return new ResponseEntity<>(dadosCredenciais, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca dados de uma credencial da conta",
      response = DadosCredencial.class,
      notes = "Busca dados das credenciais da conta",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-dados-credenciais/conta/{idConta}/{ultimosQuatro}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_CONTA"})
  public ResponseEntity<List<DadosCredencial>> buscarDadosCredencialConta(
      @PathVariable Long idConta, @PathVariable Long ultimosQuatro) {

    List<DadosCredencial> dadosCredenciais =
        contaPagamentoService.getDadosCredenciaisConta(idConta, ultimosQuatro);

    return new ResponseEntity<>(dadosCredenciais, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Busca dados dos endereços do titular da conta", response =
  // EnderecoPessoa.class, notes = "Busca dados dos endereços do titular da conta",
  // responseContainer = "List")
  //	@ApiImplicitParams({ @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value
  // = "API Key"),
  //			@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key") })
  //	@RequestMapping(value = "/buscar-dados-enderecos-titular/conta/{idConta}", method =
  // RequestMethod.GET, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({ "ROLE_BUSCAR_ENDERECOS_CONTA" })
  //	public ResponseEntity<List<EnderecoPessoa>> buscarDadosEnderecosConta(@PathVariable Long
  // idConta) {
  //
  //		List<EnderecoPessoa> enderecos = contaPagamentoService.getEnderecosTitularConta(idConta);
  //
  //		return new ResponseEntity<>(enderecos, HttpStatus.OK);
  //	}

  @ApiOperation(value = "Ativar uma conta (produto)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/ativar",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATIVAR_CONTA"})
  public ResponseEntity<HashMap<String, Object>> ativarConta(
      @RequestBody @Valid AtivarInativarProdutoRequest model, BindingResult result) {

    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    contaPagamentoService.ativarProduto(model, ipOrigem);

    map.put("msg", "Produto ativado com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Inativar uma conta (produto)")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/inativar",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESATIVAR_CONTA"})
  public ResponseEntity<HashMap<String, Object>> inativarConta(
      @RequestBody @Valid AtivarInativarProdutoRequest model, BindingResult result) {

    HashMap<String, Object> map = new HashMap<>();

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    String ipOrigem =
        (request.getHeader("X-Forwarded-For")) == null
            ? request.getRemoteAddr()
            : request.getHeader("X-Forwarded-For");

    contaPagamentoService.inativarProduto(model, ipOrigem);

    map.put("msg", "Produto desativado com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(value = "Desbloquear uma credencial")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/desbloquear-credencial",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_DESBLOQUEAR_CREDENCIAL"})
  public ResponseEntity<HashMap<String, Object>> desbloquearCredencial(
      @RequestBody @Valid DesbloquearCredencialCompleta model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    contaPagamentoService.desbloquearCredencialCompleta(model);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Credencial desbloqueada com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Atualizar perfil tarifario da conta",
      response = DetalhesConta.class,
      notes = "Atualizar perfil tarifario da conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizar/{idConta}/{idPerfilTarifario}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ATUALIZAR_PERFIL_TARIFARIO_CONTA"})
  public ResponseEntity<HashMap<String, Object>> atualizarPerfilTarifario(
      @PathVariable Long idConta, @PathVariable Integer idPerfilTarifario) {

    SecurityUser user = getAuthenticatedUser(request, em);

    contaPagamentoService.atualizarPerfilTarifarioConta(idConta, idPerfilTarifario, user);

    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Alteração realizada com sucesso!");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o extrato da conta por data",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da conta",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/{idConta}/extrato/data_inicial/{dataInicial}/data_final/{dataFinal}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_DATA"})
  public ResponseEntity<List<GetExtratoCredencial>> getExtratoContaPorDatas(
      @PathVariable Long idConta,
      @PathVariable Date dataInicial,
      @PathVariable Date dataFinal,
      @RequestParam(required = false) Integer page,
      @RequestParam(required = false) Integer pageSize,
      @RequestParam(required = false) Boolean ascendingOrder,
      HttpServletRequest httpServletRequest) {

    List<GetExtratoCredencial> extrato =
        callFunctionUserOrFunctionPortadorOrFunctionEstabelecimentoOrFunctionCorporativo(
            httpServletRequest,
            new ExtratoRequestVo(idConta, dataInicial, dataFinal, page, pageSize, ascendingOrder),
            contaPagamentoService::getExtratoUser,
            contaPagamentoService::getExtratoPortador,
            contaPagamentoService::getExtratoUserEstabelecimento,
            contaPagamentoService::getExtratoCorporativo,
            em);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o extrato da conta por data v2",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da conta",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key")
  })
  @RequestMapping(
      value = "v2/{idConta}/extrato/data_inicial/{dataInicial}/data_final/{dataFinal}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_DATA"})
  public ResponseEntity<Map<String, List<GetExtratoCredencial>>> getExtratoContaPorDatasV2(
      @PathVariable Long idConta,
      @PathVariable Date dataInicial,
      @PathVariable Date dataFinal,
      @RequestParam(required = false) Integer page,
      @RequestParam(required = false) Integer pageSize,
      @RequestParam(required = false) Boolean ascendingOrder,
      HttpServletRequest httpServletRequest) {

    List<GetExtratoCredencial> extrato =
        callFunctionUserOrFunctionPortadorOrFunctionEstabelecimento(
            httpServletRequest,
            new ExtratoRequestVo(idConta, dataInicial, dataFinal, page, pageSize, ascendingOrder),
            contaPagamentoService::getExtratoUser,
            contaPagamentoService::getExtratoPortador,
            contaPagamentoService::getExtratoUserEstabelecimento,
            em);

    return new ResponseEntity<>(Collections.singletonMap("dados", extrato), HttpStatus.OK);
  }

  @ApiOperation(
      value = "Contar transações do extrato da conta por data",
      response = GetExtratoCredencial.class,
      notes = "Buscar o extrato da conta",
      responseContainer = "List")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/{idConta}/extrato/data_inicial/{dataInicial}/data_final/{dataFinal}/count",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_EXTRATO_CONTA_BY_DATA"})
  public ResponseEntity<Integer> countExtratoContaPorDatas(
      @PathVariable Long idConta, @PathVariable Date dataInicial, @PathVariable Date dataFinal) {

    Integer count = contaPagamentoService.countExtrato(idConta, dataInicial, dataFinal);

    return new ResponseEntity<>(count, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar data hora do extrato",
      response = GetDataHoraExtrato.class,
      notes = "Buscar data hora do extrato")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/get-data-saldo",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DATA_HORA_EXTRATO"})
  public GetDataHoraExtrato getDataHoraExtrato() {

    GetDataHoraExtrato data = new GetDataHoraExtrato();
    data.setPreparaDataSaldo(new java.util.Date());

    return data;
  }

  @ApiOperation(
      value = "Buscar se o cpf já existe em uma conta pagamento do produto selecionado",
      response = Boolean.class,
      notes = "Busca se o cpf já existe em uma conta pagamento do produto selecionado")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/cpf-produto-existe",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public Boolean existeContaWithCpfAndProduto(
      @RequestBody @Valid BuscaContaDocumentoProduto model) {

    return contaPagamentoService.existeContaWithCpfAndProduto(model);
  }

  @ApiOperation(
      value = "Buscar se o cpf já existe em uma conta pagamento por instituição de pessoa não b2b",
      response = Boolean.class,
      notes = "Busca se o cpf já existe em uma conta pagamento do produto selecionado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cpf-existe",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<ContaPagamentoResponse>> existeContaWithCpf(
      @RequestBody @Valid BuscaContaDocumentoProduto model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    if (model.getIdProcessadora() == null) {
      model.setIdProcessadora(user.getIdProcessadora());
    }
    if (model.getIdInstituicao() == null) {
      model.setIdInstituicao(user.getIdInstituicao());
    }
    return new ResponseEntity<>(
        contaPagamentoService.existeContaWithCpfProdutoNotB2b(model), HttpStatus.OK);
  }

  @ApiOperation(value = "Atualizar valor de Carga Padrão ou Limite Único")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizar/carga-limite",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, Object>> atualizarCargaOuLimite(
      @RequestBody @Valid AtualizarCargaOuLimiteRequest model, BindingResult result) {

    HashMap<String, Object> map = new HashMap<>();

    SecurityUser user = getAuthenticatedUser(request, em);

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    contaPagamentoService.alterarValor(model, user);

    map.put("msg", "Produto atualizado com sucesso!");

    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar o saldo da conta",
      response = GetSaldoConta.class,
      notes = "Buscar o saldo da conta")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_ESTABELECIMENTO, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/buscar-saldo-conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_BUSCAR_SALDO_CONTA"})
  public GetSaldoConta getSaldoConta(
      @PathVariable Long idConta, HttpServletRequest httpServletRequest) {
    return callFunctionUserOrFunctionPortadorOrFunctionEstabelecimentoOrFunctionCorporativo(
        httpServletRequest,
        idConta,
        contaPagamentoService::getSaldoContaUser,
        contaPagamentoService::getSaldoContaPortador,
        contaPagamentoService::getSaldoContaUserEstabelecimento,
        contaPagamentoService::getSaldoContaCorporativoPortador,
        em);
  }

  @ApiOperation(value = "Buscar o saldo das contas de um portador pertencentes ao mesmo grupo")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/grupo/buscar-saldos",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<HashMap<String, List<GetSaldoGrupoResponse>>> GetSaldosGrupoPortador() {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    HashMap<String, List<GetSaldoGrupoResponse>> map = new HashMap<>();
    List<GetSaldoGrupoResponse> result = contaPagamentoService.getSaldosGrupoPortador(userPortador);

    map.put("dados", result);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar dados para aba dados credito",
      response = GetSaldoConta.class,
      notes = "Buscar dados para aba dados credito")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-dados-credito/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_BUSCAR_SALDO_CONTA"})
  public GetDadosCredito getDadosCredito(@PathVariable Long idConta) {

    SecurityUser user = getAuthenticatedUser(request, em);

    GetDadosCredito dadosCredito = facade.getDadosCredito(idConta, user);

    return dadosCredito;
  }

  @ApiOperation(value = "Serviço responsável por buscar saldo de conta digital pay")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar-saldo-pay/{conta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_SALDO_BRB_PAY"}) Por algum motivo nao funciona mesmo com a funcionalidade
  // adicionada no banco
  public ResponseEntity<BuscaSaldoResponse> buscarSaldo(
      @PathVariable Long conta, HttpServletRequest request) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    try {
      BuscaSaldoResponse buscaSaldoResponse =
          contaPagamentoBRBService.buscarSaldo(conta, userPortador.getCpf());
      return new ResponseEntity<>(buscaSaldoResponse, HttpStatus.OK);
    } catch (Exception e) {
      e.printStackTrace();
      return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @ApiOperation(value = "Serviço responsável por buscar dados de agência e conta BRB Pay")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar-dados-agencia-conta-brb-pay/{conta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_SALDO_BRB_PAY"})
  public ResponseEntity<BuscarAgenciaContaResponse> buscarDadosAgenciaContaBRBPay(
      @PathVariable Long conta, HttpServletRequest request) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    try {
      BuscarAgenciaContaResponse buscaSaldoResponse =
          contaPagamentoBRBService.buscarAgenciaConta(conta, userPortador.getCpf());
      return new ResponseEntity<>(buscaSaldoResponse, HttpStatus.OK);
    } catch (Exception e) {
      e.printStackTrace();
      return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @ApiOperation(value = "Definir o limite de uma conta pós-paga", response = Boolean.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/conta-pos/alterar-limite",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_ALTERAR_LIMITE_CONTA"})
  public void alterarLimiteContaPos(@RequestBody @Valid AlterarLimiteContaPos model) {
    SecurityUser user = getAuthenticatedUser(request, em);
    contaPagamentoService.alterarLimiteContaPos(model, user);
  }

  @ApiOperation(
      value = "Busca pessoas adicionais de uma conta",
      response = PessoaAdicionalResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-pessoas-adicionais/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_BUSCAR_PESSOAS_ADICIONAL_CONTA"})
  public ResponseEntity<List<AlterarAdicionalPessoa>> buscarPessoasAdicionaisConta(
      @PathVariable Long idConta) {

    List<AlterarAdicionalPessoa> pessoasAdicionais =
        contaPagamentoService.buscarPessoasAdicionaisConta(idConta);
    return new ResponseEntity<>(pessoasAdicionais, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Solicitar um cartao adicional para uma conta",
      response = PessoaAdicionalResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/create-adicional",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SOLICITAR_CARTAO_ADICIONAL_CONTA"})
  public PessoaAdicionalResponse createCartaoAdicionalConta(
      @RequestBody CartaoAdicionalRequest model) {

    SecurityUser user = getAuthenticatedUser(request, em);
    return contaPagamentoService.gerarPessoaAndCartaoAdicionalByConta(model, user.getIdUsuario());
  }

  @ApiOperation(
      value = "Solicitar um cartao adicional para uma conta",
      response = PessoaAdicionalResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/cria-adicional/{idProduto}/{idFilial}/{idRegional}/{idPontoDeRelacionamento}",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_SOLICITAR_CARTAO_ADICIONAL_CONTA"})
  public ResponseEntity<HashMap<String, Object>> insertPessoaAdicionalConta(
      @RequestBody CadastrarPessoaPDAFRequest to,
      @PathVariable("idProduto") Integer idProdutoInstituicao,
      @PathVariable("idRegional") Integer idRegional,
      @PathVariable("idFilial") Integer idFilial,
      @PathVariable("idPontoDeRelacionamento") Integer idPontoDeRelacionamento) {

    SecurityUser user = getAuthenticatedUser(request, em);
    contaPagamentoService.criarPessoaAndCartaoAdicionalByConta(
        to, user, idRegional, idFilial, idPontoDeRelacionamento, idProdutoInstituicao);
    HashMap<String, Object> map = new HashMap<>();
    map.put("msg", "Pessoa Adicional criada com sucesso");
    map.put("insert", true);

    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Busca contas relacionadas a uma instituição com o documento")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar-contas/lancamento/{idInstituicao}/{documento}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_CONTA"})
  public List<ContaPagamentoVO> buscarContasPorCpfeInstituicao(
      @PathVariable Integer idInstituicao, @PathVariable String documento) {

    List<ContaPagamentoVO> contas =
        contaPagamentoService.findIdContaByIdInstituicaoAndDocumento(idInstituicao, documento);

    if (contas.size() == 0) {
      throw new GenericServiceException("Conta inexistente ou inativa.");
    }

    return contas;
  }

  @ApiOperation(
      value = "Buscar o extrato de transações rejeitadas por mês",
      response = ExtratoTransRejeitada.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/extrato-transacoes-rejeitadas/{idConta}/{mes}/{ano}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<ExtratoTransRejeitada>> getExtratoTransacoesRejeitadasByMes(
      @PathVariable Long idConta, @PathVariable Integer mes, @PathVariable Integer ano) {

    SecurityUser user = getAuthenticatedUser(request, em);

    List<ExtratoTransRejeitada> extrato =
        contaPagamentoService.getExtratoTransacoesRejeitadasByMes(idConta, mes, ano, user);

    return new ResponseEntity<>(extrato, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Lança uma transação/autorização de compra em um estabelecimento filial da mesma hierarquia da conta ")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/lancamento-estabelecimento/autorizacao-compra",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LANCAMENTO_AUTORIZACAO_ESTABELECIMENTO_CONTA"})
  public ResponseEntity<HashMap<String, Object>> doLancamentoTransacaoCompraFilialEstab(
      @RequestBody LancamentoContaEstabelecimentoVo model) throws AcquirerClientException {

    SecurityUser user = getAuthenticatedUser(request, em);

    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.LANCAMENTO);
    travaContasService.travaContas(model.getIdConta(), Servicos.LANCAMENTO);

    return facade.doLancamentoTransacaoCompraFilialEstab(user, model);
  }

  @ApiOperation(
      value =
          "Calcular o valor máximo e data de validade para autorizar cartões virtuias de primeira compra",
      response = Instituicao.class,
      notes = "")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/valor/autorizacao-primeira-compra/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CALCULAR_VALOR_PRIMEIRA_COMPRA"})
  public ResponseEntity<GetSaldoConta> getValorLimitePrimeiraCompraByUsuarioLogado(
      @PathVariable Long idConta) {

    SecurityUser user = getAuthenticatedUser(request, em);
    SecHierarquia.checkHierarquia(user, user.getHierarquiaType());

    return new ResponseEntity<GetSaldoConta>(
        facade.calcularValorTransacaoMaximoDataValidade(user, idConta), HttpStatus.OK);
  }

  @ApiOperation(value = "Exportar termo de adesão em pdf")
  @RequestMapping(path = "/termo-de-adesao/{idConta}", method = RequestMethod.POST)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @Secured({"ROLE_EMITIR_TAD_CREDITO"})
  public ResponseEntity<byte[]> exportarTermoAdesaoPdf(@PathVariable Long idConta)
      throws DocumentException, IOException {

    byte[] array = facade.exportarTermoAdesaoPDF(idConta);

    HttpHeaders header = new HttpHeaders();
    header.set("ContentType", TipoRelatorio.PDF.getContentType());
    header.set("Content-Disposition", "inline; filename=" + "relatorio");
    header.setCacheControl("must-revalidate, post-check=0, pre-check=0");
    header.setContentLength(array.length);

    return new ResponseEntity<byte[]>(array, header, HttpStatus.OK);
  }

  @ApiOperation(value = "Busca dados das credenciais da conta para fazer um lancamento")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-credenciais/lancamento/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_CREDENCIAIS_CONTA"})
  public ResponseEntity<List<CredencialResumida>> buscarCredenciaisContaToLancamento(
      @PathVariable Long idConta) {

    List<CredencialResumida> dadosCredenciais =
        contaPagamentoService.getCredenciaisContaToLancamento(idConta);

    return new ResponseEntity<>(dadosCredenciais, HttpStatus.OK);
  }

  @ApiOperation(value = "Salva a imagem de uma conta")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      method = RequestMethod.POST,
      path = "/arquivo/documento/{idConta}/{idTipoDocumento}")
  public ResponseEntity<HttpStatus> createImgDocumentoConta(
      @RequestParam("file") MultipartFile file,
      @PathVariable Long idConta,
      @PathVariable Integer idTipoDocumento)
      throws IOException {

    HashMap<String, Object> map = new HashMap<>();

    Integer idUsuario = null;
    Long idPortadorLogin = null;

    try {
      SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
      idPortadorLogin = userPortador.getIdLogin();

    } catch (Exception e) {
      SecurityUser user = getAuthenticatedUser(request, em);
      idUsuario = user.getIdUsuario();
    }
    contaPagamentoService.createImgDocumentoConta(
        file.getInputStream(),
        file.getOriginalFilename(),
        idConta,
        idTipoDocumento,
        idUsuario,
        idPortadorLogin);

    map.put("msg", "Upload efetuado com sucesso!");
    return new ResponseEntity<>(HttpStatus.OK);
  }

  @ApiOperation(value = "Abre a imagem de um documento de uma conta")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  })
  @RequestMapping(
      value = "/abrir/documento/{idDocumentoConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
  public ResponseEntity<InputStreamResource> getImgDocumentoConta(
      @PathVariable Long idDocumentoConta) {

    InputStream input = contaPagamentoService.getImgDocumentoConta(idDocumentoConta);

    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.parseMediaType("application/octet-stream"));

    return new ResponseEntity<InputStreamResource>(
        new InputStreamResource(input), headers, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável recuperar os documentos de uma conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/find-documentos-conta/{idConta}",
      method = RequestMethod.GET,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<DocumentoConta> getDocumentosByConta(@PathVariable Long idConta) {

    return contaPagamentoService.getDocumentosByConta(idConta);
  }

  @ApiOperation(
      value = "Verifica inadiplencia de uma conta",
      response = PessoaAdicionalResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/verifica/inadiplencia/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  // @Secured({"ROLE_VERIFICA__INADIPLENCIA_CONTA"})
  public ResponseEntity<HashMap<String, Object>> verificaInadiplencia(@PathVariable Long idConta) {

    HashMap<String, Object> response = new HashMap<String, Object>();
    SecurityUser user = getAuthenticatedUser(request, em);
    response.put("status", contaService.isContaInadiplente(idConta, user));
    response.put(
        "msg",
        response.get("status") != null && Boolean.valueOf(response.get("status").toString())
            ? MSG_INADIPLENCIA
            : null);

    return new ResponseEntity<HashMap<String, Object>>(response, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Buscar transações aprovadas e não apresentadas por mês",
      response = TransacoesNaoApresentadas.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/find-transacoes-nao-apresentadas/{idConta}/{mes}/{ano}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_TRANSACAO_NAO_APRESENTADAS"})
  public ResponseEntity<List<TransacoesNaoApresentadas>> findTransacoesNaoApresentadasByMes(
      @PathVariable Long idConta, @PathVariable Integer mes, @PathVariable Integer ano) {

    List<TransacoesNaoApresentadas> retorno =
        contaPagamentoService.findTransacoesNaoApresentadasByMes(idConta, mes, ano);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  private ResponseEntity<Map<String, Object>> getJcardResponse(
      JcardResponse resp, String mensagem) {
    HashMap<String, Object> response = new HashMap<String, Object>();
    Boolean success =
        resp.getSuccess() == null
            ? resp.getErrors() == null || resp.getErrors().isEmpty()
            : resp.getSuccess();
    response.put("sucesso", success);
    response.put("erros", resp.getErrors());
    response.put("msg", mensagem);
    response.put("rrn", resp.getRrn());

    if (!success) {
      response.put("msg", "Não foi possível efetuar transferência.Tente Novamente mais tarde.");
      response.put("erros", resp.getErrors());
      return new ResponseEntity<>(response, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    return new ResponseEntity<>(response, HttpStatus.OK);
  }

  @ApiOperation(value = "Buscar os SMS's de uma conta", response = LogSMSContaResponse.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/sms/buscar/{idConta}/{first}/{max}", method = RequestMethod.POST)
  @Secured({"ROLE_BUSCAR_SMS_CONTA"})
  public ResponseEntity<?> getSMSsByConta(
      @PathVariable Long idConta,
      @PathVariable Integer first,
      @PathVariable Integer max,
      @RequestBody SmsContaByFiltersRequest filters) {

    List<LogSMSContaResponse> retorno =
        contaPagamentoService.getSMSsByConta(idConta, first, max, filters);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  @ApiOperation(value = "Contar os SMS's de uma conta", response = Long.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/sms/count/{idConta}", method = RequestMethod.POST)
  @Secured({"ROLE_BUSCAR_SMS_CONTA"})
  public ResponseEntity<?> countSMSsByConta(
      @PathVariable Long idConta, @RequestBody @Valid SmsContaByFiltersRequest filters) {

    Long retorno = contaPagamentoService.countSMSsByConta(idConta, filters);

    return new ResponseEntity<>(retorno, HttpStatus.OK);
  }

  //	@ApiOperation(value = "Contar os SMS's de uma conta", response = Long.class)
  //	@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  //	@RequestMapping(value = "/b2b/agendar-replicacao", method = RequestMethod.POST)
  //	@Secured({ "ROLE_REPLICAR_PORTADOR_IN" })
  //	public ResponseEntity<?> agendarReplicacaoCredenciais(@RequestBody @Valid
  // AgendarReplicacaoContaB2BRequest input) {
  //
  //		SecurityUser user = getAuthenticatedUser(request, em);
  //		Boolean result = facade.agendarReplicacaoCredenciaisContas(input,user);
  //
  //		return new ResponseEntity<>(result, HttpStatus.OK);
  //	}

  //	@ApiOperation(value = "Busca agrupamento de credenciais/contas para replicacao", response =
  // ContasCredenciaisReplicaveisReponse.class,responseContainer="list")
  //	@ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  //	@RequestMapping(value = "/b2b/replicaveis", method = RequestMethod.GET)
  //	@Secured({ "ROLE_REPLICAR_PORTADOR_IN" })
  //	public ResponseEntity<?> getContasReplicaveis() {
  //
  //		List<ContasCredenciaisReplicaveisReponse> retorno =
  // contaPagamentoService.getContasECredenciaisReplicaveis();
  //
  //		return new ResponseEntity<>(retorno, HttpStatus.OK);
  //	}

  @ApiOperation(
      value = "Busca agrupamento de credenciais/contas que foram agendadas",
      response = SolicitacaoReplicacaoCredencialResumoResponse.class,
      responseContainer = "list")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/b2b/replicacao-agendada", method = RequestMethod.GET)
  @Secured({"ROLE_REPLICAR_PORTADOR_IN"})
  public ResponseEntity<?> getReplicacoesAgendadas() {

    List<SolicitacaoReplicacaoCredencialResumoResponse> solicitacoes =
        facade.getAgendamentosReplicacaoConta();

    return new ResponseEntity<>(solicitacoes, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Realiza transferencia entre contas da Mesma Instituicao - portador",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/transferencia/mesma-instituicao",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_MESMA_INSTITUICAO"})
  public ResponseEntity<Map<String, Object>> transferenciaMesmaInstituicaoIdCredencial(
      @RequestBody @Valid TransMesmaInstituicaoIdCredencial model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(
          Objects.requireNonNull(result.getFieldError()).getDefaultMessage(), result);
    }

    boolean success =
        callFunctionPortadorOrFunctionCorporativo(
            request,
            new ValidarTransferenciaMesmaInstituicaoDTO(model, getTokenJWT(request)),
            facade::validarTransferenciaMesmaInstituicao,
            facade::validarTransferenciaMesmaInstituicao,
            em);

    Map<String, Object> map = new HashMap<>();
    if (success) {
      map.put("msg", "Transferência realizada com Sucesso!");
      return new ResponseEntity<>(map, HttpStatus.OK);
    } else {
      map.put("msg", "Erro na transferencia, tente novamente mais tarde!");
      return new ResponseEntity<>(map, HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  @ApiOperation(
      value =
          "Realiza transferencia entre contas da mesma pessoa porém de produtos diferentes em programas Multicontas",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transferência.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/v2/transferencia/mesmo-bolso",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_MESMA_INSTITUICAO"})
  public ResponseEntity<Map<String, Object>> transferenciaMesmoBolso(
      @RequestBody @Valid TransferenciaMesmoBolso model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    travaServicosService.travaServicos(
        userPortador.getIdInstituicao(), Servicos.TRANSFERENCIA_INTERNA_PORTADOR);

    JcardResponse success =
        facade.transferenciaMesmoBolso(model, getTokenJWT(request), userPortador);
    if (success.getSuccess()) {
      return new ResponseEntity<>(
          Collections.singletonMap("dados", "Transferência realizada com Sucesso!"), HttpStatus.OK);
    } else {
      return new ResponseEntity<>(
          Collections.singletonMap("dados", success.getErrors()), HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }

  @ApiOperation(
      value = "Realiza a cobrança de um valor para transferência a partir de uma conta para outra.",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não a transfêrencia.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/transferencia/cobrar-com-qrcode",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_TRANFERENCIA_ENTRE_CONTAS_MESMA_INSTITUICAO"})
  @DesabilitarVerificacaoPortador(camposExcluidos = {"idCredencial"})
  public ResponseEntity<Map<String, Object>> cobraTransferenciaQrCode(
      @RequestBody @Valid TransferenciaCobrarComQrCode model, BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    JcardResponse success =
        callFunctionPortadorOrFunctionCorporativo(
            request,
            new ValidarCobrarTranferenciaQrCodeDTO(model, getTokenJWT(request)),
            facade::validarCobrancaTransferenciaQrCode,
            facade::validarCobrancaTransferenciaQrCode,
            em);

    if (success.getSuccess()) {
      return new ResponseEntity<>(
          Collections.singletonMap("msg", "Cobrança realizada com Sucesso!"), HttpStatus.OK);
    } else {
      return new ResponseEntity<>(
          Collections.singletonMap("msg", success.getErrors()), HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }

  @ApiOperation(value = "Verificar contratação plano de saúde")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{id}/plano-saude/{idPlanoSaude}/{idPessoa}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_LISTAR_PLANO_SAUDE"})
  public ResponseEntity<?> getPlanoSaudeContratado(
      @PathVariable("id") Long id,
      @PathVariable("idPlanoSaude") Long idPlanoSaude,
      @PathVariable("idPessoa") Long idPessoa) {
    return new ResponseEntity<>(
        contaPagamentoService.getPlanoSaudeContratado(id, idPlanoSaude, idPessoa), HttpStatus.OK);
  }

  @ApiOperation(value = "Contratar plano de saúde")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{id}/plano-saude/{idPlanoSaude}/{idPessoa}",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONTRATAR_PLANO_SAUDE"})
  public void contratarPlanoSaude(
      @PathVariable("id") Long id,
      @PathVariable("idPlanoSaude") Long idPlanoSaude,
      @PathVariable("idPessoa") Long idPessoa) {
    contaPagamentoService.contratarPlanoSaude(
        id, idPlanoSaude, idPessoa, getAuthenticatedUser(request, em).getIdUsuario());
  }

  @ApiOperation(value = "Cancelar contratação plano de saúde")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/{id}/plano-saude/{idPlanoSaude}/{idPessoa}",
      method = RequestMethod.DELETE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_CONTRATAR_PLANO_SAUDE"})
  public void cancelarContratacaoPlanoSaude(
      @PathVariable("id") Long id,
      @PathVariable("idPlanoSaude") Long idPlanoSaude,
      @PathVariable("idPessoa") Long idPessoa) {
    contaPagamentoService.cancelarContratacaoPlanoSaude(
        id, idPlanoSaude, idPessoa, getAuthenticatedUser(request, em).getIdUsuario());
  }

  @ApiOperation(
      value = "Verifica se existe portador login registrado para um CNPJ, CPF e instituição",
      response = boolean.class,
      notes = "Retorna existencia ou não de portador login")
  @RequestMapping(
      value =
          "/buscar-portador/{documento}/{documentoRepresentante}/{idInstituicao}/{idProcessadora}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> buscarPortadorLoginExistentePJ(
      @PathVariable("documento") String documento,
      @PathVariable("documentoRepresentante") String documentoRepresentante,
      @PathVariable("idInstituicao") Integer idInstituicao,
      @PathVariable("idProcessadora") Integer idProcessadora) {
    boolean portadorExistente =
        portadorLoginService.loginExistsRepresentanteLegal(
            idProcessadora, idInstituicao, documento, documentoRepresentante);

    Map<String, Object> map = new HashMap<>();
    map.put("portadorExiste", portadorExistente);

    return new ResponseEntity<>(portadorExistente, HttpStatus.OK);
  }

  /**
   * Api criada para juntar as funcionalidades de cadastro de um login do portado juntamente com o
   * cadastro de uma conta pagamento sem authorization
   *
   * @param model
   * @param result
   * @return
   */
  @ApiOperation(
      value =
          "Cria uma contaPagamento completa para a pessoa sem authorization e cria um login de um portador",
      response = String.class,
      notes = "Retorna o numero da conta criada")
  @RequestMapping(
      value = "/criar-conta-completa",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  //	@Secured({ "ROLE_CADASTRAR_CONTA_PAGAMENTO_COMPLETA" })
  public ResponseEntity<Map<String, Object>>
      cadastrarContaPagamentoPessoaSemAuthorizationELoginPortador(
          @RequestBody @Valid CadastrarContaPagamentoPessoaELoginPortadorRequest model,
          BindingResult result) {

    if (result.hasErrors()) {
      throw new InvalidRequestException(result.getFieldError().getDefaultMessage(), result);
    }

    travaServicosService.travaServicos(
        model.getCadastrarContaPagamentoPessoaRequest().getIdInstituicao(),
        Servicos.CRIAR_CONTA_PORTADOR);

    Map<String, Object> map = new HashMap<>();

    model.getCadastrarContaPagamentoPessoaRequest().setIdSetorFilial(null);

    try {

      Proposta proposta = propostaService.createPropostaPF(model);
      contaPagamentoService.salvarDocumentosProposta(proposta);

      map.put("msg", "Proposta realizada com sucesso");
    } catch (GenericServiceException e) {
      throw new GenericServiceException(
          "Não foi possível realizar o cadastramento da proposta. " + e.getErros().get("msg"), e);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível realizar o cadastramento da proposta. " + e.getMessage(), e);
    }
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por confirmar migração")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/confirmar/migracao/{documento}",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, Object>> confirmarMigracao(
      @PathVariable("documento") String documento) {

    contaPagamentoService.confirmaMigracao(documento);

    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Confirmado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Serviço responsável por buscar todas contas migradas",
      response = ContaPagamentoIntegracaoVO.class,
      responseContainer = "List")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar/migracao/{documento}/{idInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<ContaPagamentoIntegracaoVO>> buscarMigracao(
      @PathVariable("documento") String documento,
      @PathVariable("idInstituicao") Integer idInstituicao) {

    List<ContaPagamentoIntegracaoVO> contaPagamentoIntegracaoVO = new ArrayList<>();
    contaPagamentoIntegracaoVO = contaPagamentoService.buscarMigracao(idInstituicao, documento);

    return new ResponseEntity<>(contaPagamentoIntegracaoVO, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por buscar contas pelo produto instituição")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar/conta/{idProdInstituicao}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<ContaPagamentoCadastroAvanceVO>> buscarDadosContaByIdProdInstituicao(
      @PathVariable("idProdInstituicao") Integer idProdInstituicao,
      HttpServletRequest httpServletRequest) {

    SecurityUser user = getAuthenticatedUser(httpServletRequest, em);

    List<ContaPagamentoCadastroAvanceVO> dadosConta =
        contaPagamentoService.buscarDadosContaByIdProdInstituicao(
            idProdInstituicao, user.getIdPontoDeRelacionamento());

    return new ResponseEntity<>(dadosConta, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Serviço responsável por buscar credenciais de mesma instituição do mesmo portador.")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar/contas/portador",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<List<ContasPorProdutoPortador>>
      findAllCredenciaisPorProdutoByDocumentoAndInstituicao() {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    assert userPortador != null;
    List<ContasPorProdutoPortador> credenciaisPortador =
        contaPagamentoService.findAllContaPagamentoPorProdutoByDocumentoAndInstituicao(
            userPortador.getIdProcessadora(),
            userPortador.getIdInstituicao(),
            userPortador.getCpf());

    return new ResponseEntity<>(credenciaisPortador, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          "Endpoint para trazer de volta as contas pertencentes a um portador em grupo de produtos")
  @RequestMapping(
      value = "/grupo/listar",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  public ResponseEntity<HashMap<String, HashMap>> buscarContasPortadorGrupo() {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    List<ListarContaPagamentoGrupoResponse> contas =
        this.contaPagamentoService.obterContasGrupo(userPortador);
    HashMap<String, List<ListarContaPagamentoGrupoResponse>> innerMap = new HashMap<>();
    innerMap.put("contas", contas);
    HashMap<String, HashMap> map = new HashMap<>();

    map.put("dados", innerMap);
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Exclusão de conta pelo aplicativo",
      response = Boolean.class,
      notes = "Retorna um booleano para dizer se deu certo ou não o envio da solicitação.")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key"),
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_CORPORATIVO, value = "API Key")
  })
  @RequestMapping(
      value = "/exclusao-conta",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<?> exclusaoContaAplicativo(@RequestBody ExclusaoContaVO exclusaoContaVO) {
    callFunctionPortadorOrFunctionCorporativo(
        request,
        exclusaoContaVO,
        exclusaoContaService::validarExclusaoConta,
        exclusaoContaService::validarExclusaoConta,
        em);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Solicitação enviada com sucesso.");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @ApiOperation(
      value =
          " Serviço responsável por buscar lista de solicitações de exclusão de conta enviadas ")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/buscar-exclusao-conta", method = RequestMethod.GET)
  @Secured({"ROLE_EXCLUSAO_CONTA"})
  public ResponseEntity<List<SolicitacaoCancelamentoVO>> getSolicitacoesExclusaoConta() {
    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.EXCLUIR_CONTA);

    List<SolicitacaoCancelamentoVO> resp = null;
    resp = exclusaoContaService.buscarSolicitacoesExclusao();
    return new ResponseEntity<>(resp, HttpStatus.OK);
  }

  @ApiOperation(value = " Serviço responsável por buscar solicitação de exclusão de conta por ID ")
  @ApiImplicitParams({
    @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  })
  @RequestMapping(value = "/buscar-exclusao-conta/{id}", method = RequestMethod.GET)
  public ResponseEntity<SolicitacaoCancelamentoVO> getExclusaoContaById(
      @PathVariable("id") Long id) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    travaServicosService.travaServicos(userPortador.getIdInstituicao(), Servicos.EXCLUIR_CONTA);

    SolicitacaoCancelamentoVO resp = null;
    resp = exclusaoContaService.buscarExclusaoById(id);
    return new ResponseEntity<>(resp, HttpStatus.OK);
  }

  @ApiOperation(value = "Serviço responsável por alterar status de exclusão")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/atualizar-status-exclusao/{id}",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_EXCLUSAO_CONTA"})
  public ResponseEntity<Map<String, Object>> atualizarStatusExclusao(
      @PathVariable("id") Long idConta, @RequestBody AlterarStatusExclusaoVO status) {
    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(user.getIdInstituicao(), Servicos.EXCLUIR_CONTA);

    exclusaoContaService.atualizarStatusExclusao(idConta, status);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Confirmado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(value = "Serviço responsável por alterar status de visualização de modal")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/visualizar-modal-exclusao/{id}",
      method = RequestMethod.PUT,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public ResponseEntity<Map<String, Object>> visualizarModalExclusao(@PathVariable("id") Long id) {
    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);

    exclusaoContaService.visualizarModalExclusao(id);
    Map<String, Object> map = new HashMap<>();
    map.put("msg", "Confirmado com sucesso");
    return new ResponseEntity<>(map, HttpStatus.CREATED);
  }

  @ApiOperation(
      value = "Resgata dados de consumo e carga de uma conta",
      response = GetPerfilTariafarioResponse.class,
      notes = "Resgata dados de consumo e carga de uma conta")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_PORTADOR, value = "API Key")
  @RequestMapping(
      value = "/buscar-dados-consumo/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_BUSCAR_DADOS_CONSUMO"})
  public ResponseEntity<GetDadosConsumo> buscarDadosConsumo(@PathVariable Long idConta) {

    SecurityUserPortador userPortador = getAuthenticatedPortador(request, em);
    GetDadosConsumo buscarDadosConsumo = facade.buscarDadosConsumo(idConta, userPortador);

    return new ResponseEntity<>(buscarDadosConsumo, HttpStatus.OK);
  }

  @ApiOperation(
      value = "Busca dados de contas que estão cadastradas no ponto de relacionamento informado",
      response = DadosPortadorPontoRelacionamentoB2BVO.class,
      notes = "Busca dados de contas que estão cadastradas no ponto de relacionamento informado")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/buscar-portadores-ponto-relacionamento",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @Secured({"ROLE_VINCULAR_PORTADOR_PARCEIRO_B2B"})
  public List<DadosPortadorPontoRelacionamentoB2BVO> buscarContasByPontoDeRelacionamento(
      @RequestBody HierarquiaPontoRelacionamentoRequest hierarquiaPontoRelacionamentoRequest) {

    //		SecurityUser user = getAuthenticatedUser(request,em);

    if (hierarquiaPontoRelacionamentoRequest == null) {
      throw new GenericServiceException(
          "Dados da requisição não informados!", HttpStatus.BAD_REQUEST);
    }

    List<DadosPortadorPontoRelacionamentoB2BVO> dadosPortadorPontoRelacionamentoB2BVO =
        new ArrayList<>();
    dadosPortadorPontoRelacionamentoB2BVO =
        contaPagamentoService.buscarContasByPontoDeRelacionamento(
            hierarquiaPontoRelacionamentoRequest);

    return dadosPortadorPontoRelacionamentoB2BVO;
  }

  @ApiOperation(
      value = "Busca status anterior e atual da conta",
      response = ContaPagamentoHistoricoStatusView.class)
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(
      value = "/historico-status-conta/{idConta}",
      method = RequestMethod.GET,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  public List<ContaPagamentoHistoricoStatusViewVO> getAuditStatusConta(
      @PathVariable("idConta") Long idConta) {
    SecurityUser user = getAuthenticatedUser(request, em);

    return contaPagamentoService.getContaPagamentoHistoricoStatusView(user, idConta);
  }
}
