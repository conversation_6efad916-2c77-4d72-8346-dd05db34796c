package br.com.sinergico.service.suporte;

import br.com.entity.suporte.AntifraudeCafPortador;
import br.com.entity.suporte.LogRegistroValidacaoFacialCaf;
import br.com.entity.suporte.RegistroValidacaoFacialCaf;
import br.com.entity.suporte.TokenCafDTO;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.antifraude.TokenCaf;
import br.com.sinergico.enums.AntifraudeCafFacialObjetivosEnum;
import br.com.sinergico.repository.suporte.LogRegistroValidacaoFacialCafRepository;
import br.com.sinergico.repository.suporte.RegistroValidacaoFacialCafRepository;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.AntifraudeService;
import com.google.gson.Gson;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureException;
import java.time.LocalDateTime;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class RegistroValidacaoFacialCafService
    extends GenericService<RegistroValidacaoFacialCaf, Long> {

  @Autowired private AntifraudeService antifraudeService;

  @Autowired
  private LogRegistroValidacaoFacialCafRepository logRegistroValidacaoFacialCafRepository;

  @Value("${secret.key.caf}")
  private String CHAVE_SECRETA;

  public static final long TEMPO_PARA_EXPIRAR_VALIDACAO_FACIAL = 120L;
  private final RegistroValidacaoFacialCafRepository registroValidacaoFacialCafRepository;

  @Autowired
  public RegistroValidacaoFacialCafService(RegistroValidacaoFacialCafRepository repo) {
    super(repo);
    registroValidacaoFacialCafRepository = repo;
  }

  public void registraValidacaoBemSucedida(
      String documento,
      Integer idInstituicao,
      AntifraudeCafFacialObjetivosEnum objetivo,
      Optional<String> optCpfRepresentante,
      TokenCaf tokenCaf) {

    AntifraudeCafPortador antifraudeCafPortador = null;
    if (optCpfRepresentante.isPresent()) {
      antifraudeCafPortador =
          antifraudeService.encontraCafAprovadoPorDocumentoRepresentanteEInstituicao(
              documento, optCpfRepresentante.get(), idInstituicao);
    } else {
      antifraudeCafPortador =
          antifraudeService.encontraCafAprovadoPorDocumentoEInstituicao(documento, idInstituicao);
    }

    if (antifraudeCafPortador == null) {
      trataAntifraudeCafPortadorNulo(documento, idInstituicao, objetivo);
    }
    this.salvarLog(documento, idInstituicao, tokenCaf, objetivo.getDescObjetivo());
    verificarValidacaoCaf(tokenCaf, objetivo);

    RegistroValidacaoFacialCaf validacaoAntiga =
        registroValidacaoFacialCafRepository
            .findByDocumentoAndIdInstituicaoAndObjetivoAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoIsNull(
                documento, idInstituicao, objetivo.getDescObjetivo());
    if (validacaoAntiga != null) {
      validacaoAntiga.setDataHoraCancelamento(LocalDateTime.now());
      registroValidacaoFacialCafRepository.saveAndFlush(validacaoAntiga);
    }
    RegistroValidacaoFacialCaf validacaoSenha = new RegistroValidacaoFacialCaf();
    validacaoSenha.setDocumento(documento);
    validacaoSenha.setIdInstituicao(idInstituicao);
    validacaoSenha.setObjetivo(objetivo.getDescObjetivo());
    validacaoSenha.setDataHoraValidacao(LocalDateTime.now());
    validacaoSenha.setDataHoraExpiracao(
        LocalDateTime.now().plusSeconds(TEMPO_PARA_EXPIRAR_VALIDACAO_FACIAL));
    registroValidacaoFacialCafRepository.save(validacaoSenha);
  }

  private void trataAntifraudeCafPortadorNulo(
      String documento, Integer idInstituicao, AntifraudeCafFacialObjetivosEnum objetivo) {
    if (!isOnboardingCafForcado(objetivo)) {
      HttpStatus status =
          isTrocaSenhaLogin(objetivo) ? HttpStatus.UNAUTHORIZED : HttpStatus.FORBIDDEN;
      throw new GenericServiceException("Incapaz de validar reconhecimento facial.", status);
    }

    // Tentativa de encontrar um registro aprovado ou pendente se for ONBOARDING_CAF_FORCADO
    AntifraudeCafPortador antifraudeCafPortador =
        antifraudeService.encontraCafAprovadoOuPendentePorDocumentoEInstituicao(
            documento, idInstituicao);
    if (antifraudeCafPortador == null) {
      throw new GenericServiceException(
          "Incapaz de validar reconhecimento facial.", HttpStatus.UNAUTHORIZED);
    }
  }

  private boolean isOnboardingCafForcado(AntifraudeCafFacialObjetivosEnum objetivo) {
    return AntifraudeCafFacialObjetivosEnum.ONBOARDING_CAF_FORCADO
        .getDescObjetivo()
        .equals(objetivo.getDescObjetivo());
  }

  private boolean isTrocaSenhaLogin(AntifraudeCafFacialObjetivosEnum objetivo) {
    return AntifraudeCafFacialObjetivosEnum.TROCA_SENHA_LOGIN
        .getDescObjetivo()
        .equals(objetivo.getDescObjetivo());
  }

  public Boolean checaEEfetivaValidacao(
      String documento, Integer idInstituicao, AntifraudeCafFacialObjetivosEnum objetivo) {
    RegistroValidacaoFacialCaf registro =
        registroValidacaoFacialCafRepository
            .findByDocumentoAndIdInstituicaoAndObjetivoAndDataHoraExpiracaoIsAfterAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoIsNull(
                documento, idInstituicao, objetivo.getDescObjetivo(), LocalDateTime.now());
    if (registro != null) {
      registro.setDataHoraUtilizacao(LocalDateTime.now());
      registroValidacaoFacialCafRepository.save(registro);
      return Boolean.TRUE;
    }
    return Boolean.FALSE;
  }

  public Long checaValidacao(
      String documento, Integer idInstituicao, AntifraudeCafFacialObjetivosEnum... objetivos) {
    LocalDateTime agora = LocalDateTime.now();
    for (AntifraudeCafFacialObjetivosEnum objetivo : objetivos) {
      RegistroValidacaoFacialCaf registro =
          registroValidacaoFacialCafRepository
              .findByDocumentoAndIdInstituicaoAndObjetivoAndDataHoraExpiracaoIsAfterAndDataHoraUtilizacaoIsNullAndDataHoraCancelamentoIsNull(
                  documento, idInstituicao, objetivo.getDescObjetivo(), agora);
      if (registro != null) {
        return registro.getId();
      }
    }
    return null;
  }

  public void efetivaValidacao(Long idValidacao) {
    RegistroValidacaoFacialCaf registro =
        registroValidacaoFacialCafRepository.findById(idValidacao).orElse(null);
    if (registro != null) {
      registro.setDataHoraUtilizacao(LocalDateTime.now());
      registroValidacaoFacialCafRepository.save(registro);
    }
  }

  public void verificarValidacaoCaf(TokenCaf tokenCaf, AntifraudeCafFacialObjetivosEnum objetivo) {

    if (tokenCaf == null) {
      throw new GenericServiceException("Sem informações para validar o reconhecimento facial.");
    }

    try {
      Claims claimsJWT =
          Jwts.parser()
              .setSigningKey(CHAVE_SECRETA.getBytes())
              .parseClaimsJws(tokenCaf.getJwt())
              .getBody();

      // Converte o Map para JSON usando Gson
      Gson gson = new Gson();
      String claims = gson.toJson(claimsJWT);

      // Desserializa o JSON em um objeto TokenCafDTO
      TokenCafDTO payload = gson.fromJson(claims, TokenCafDTO.class);

      // Verifica os campos isAlive e isMatch
      if (!Boolean.TRUE.equals(payload.getIsAlive())
          || (!AntifraudeCafFacialObjetivosEnum.ONBOARDING_CAF_FORCADO.equals(objetivo)
              && !Boolean.TRUE.equals(payload.getIsMatch()))) {
        throw new GenericServiceException("Validação de reconhecimento facial reprovado.");
      }
    } catch (SignatureException e) {
      throw new GenericServiceException("Não foi possível validar o reconhecimento facial.");
    } catch (GenericServiceException e) {
      throw e;
    } catch (Exception e) {
      throw new GenericServiceException("Erro ao processar o reconhecimento facial.");
    }
  }

  private void salvarLog(
      String documento, Integer idInstituicao, TokenCaf tokenCaf, String objetivo) {
    LogRegistroValidacaoFacialCaf logRegistroValidacaoFacialCaf =
        new LogRegistroValidacaoFacialCaf();
    logRegistroValidacaoFacialCaf.setToken(tokenCaf.getJwt());
    logRegistroValidacaoFacialCaf.setObjetivo(objetivo);
    logRegistroValidacaoFacialCaf.setDocumento(documento);
    logRegistroValidacaoFacialCaf.setIdInstituicao(idInstituicao);
    logRegistroValidacaoFacialCaf.setDataHoraVerificacao(LocalDateTime.now());
    this.logRegistroValidacaoFacialCafRepository.save(logRegistroValidacaoFacialCaf);
  }
}
