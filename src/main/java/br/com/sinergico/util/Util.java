package br.com.sinergico.util;

import br.com.exceptions.GenericServiceException;
import br.com.sinergico.annotation.NotUpperCase;
import br.com.sinergico.security.LegacyPasswordEncoder;
import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.text.Normalizer;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.Base64;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

public class Util {

  private static final String DE_ZERO_A_Z = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  private static final String STRING_VAZIA = "";
  public static final String LETRAS_NUMEROS_CARACTERERS_ESPECIAIS =
      "1234567890qwertyuiopasdfghjklçzxcvbnmQWERTYUIOPASDFGHJKLÇZXCVBNM!@#!@#$%&*+=";

  /**
   * Metodo responsavel por colocar campos de objetos para uppercase
   *
   * @param objeto
   * @throws IllegalArgumentException
   * @throws IllegalAccessException
   */
  public static <Qualquer extends Object> void toUpperCase(Qualquer objeto)
      throws IllegalArgumentException, IllegalAccessException {
    Class class1 = objeto.getClass();
    Field[] declaredFields = class1.getDeclaredFields();
    for (Field field : declaredFields) {
      field.setAccessible(true);

      if (field.getType().isAssignableFrom(String.class)) {
        if (!field.isAnnotationPresent(NotUpperCase.class)) {
          String value = (String) field.get(objeto);

          if (value != null) {
            value = value.toUpperCase();
            field.set(objeto, value);
          }
        }
      }
    }
  }

  public static boolean isEmpty(Object objeto) {
    if (objeto == null) {
      return true;
    } else if (objeto instanceof String) {
      if (objeto.toString().trim().isEmpty() || objeto.equals(STRING_VAZIA)) return true;
    } else if (objeto instanceof Collection) {
      Collection vr = (Collection) objeto;
      if (vr.isEmpty()) return true;
    }
    return false;
  }

  public static boolean isNumberNullOrZero(Object objeto) {
    if (objeto instanceof Number) {
      if (((Number) objeto).intValue() == 0) {
        return true;
      }
    } else if (objeto == null) {
      return true;
    }
    return false;
  }

  public static String parseJsonToString(Object objeto) {
    ObjectMapper mapper = new ObjectMapper();

    String jsonArray;

    try {

      jsonArray = objectToJsonString(objeto, mapper);
      Object obj = jsonStringToObject(objeto, mapper, jsonArray);
      if (obj != null) {
        return jsonArray;
      }
      return STRING_VAZIA;

    } catch (JsonProcessingException e) {
      throw new GenericServiceException("Não foi possivel Fazer parse. ", e);

    } catch (IOException e) {
      throw new GenericServiceException("Não foi possivel Fazer parse. ", e);
    }
  }

  /**
   * metodo compara e diz se o valo1 e maior que o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja maior que valor2
   */
  public static Boolean isMaiorQue(BigDecimal valor1, BigDecimal valor2) {
    return valor1.compareTo(valor2) == 1;
  }

  /**
   * metodo compara e diz se o valo1 e menor que o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja menor que valor2
   */
  public static Boolean isMenorQue(BigDecimal valor1, BigDecimal valor2) {
    return valor1.compareTo(valor2) == -1;
  }

  /**
   * @param valor1
   * @param valor2
   * @return
   */
  public static Boolean isMenorQue(Number valor1, Number valor2) {
    return valor1.doubleValue() < valor2.doubleValue();
  }

  /**
   * metodo compara e diz se o valo1 igual o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja igual valor2
   */
  public static Boolean isEqual(BigDecimal valor1, BigDecimal valor2) {
    return valor1.compareTo(valor2) == 0;
  }

  /**
   * metodo compara e diz se o valo1 e maior que o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja maior que valor2
   */
  public static Boolean isMaiorQue(Integer valor1, Integer valor2) {
    return valor1.compareTo(valor2) == 1;
  }

  public static Boolean isMaiorQue(Number valor1, Number valor2) {
    return valor1.doubleValue() > valor2.doubleValue();
  }

  /**
   * metodo compara e diz se o valo1 e menor que o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja menor que valor2
   */
  public static Boolean isMenorQue(Integer valor1, Integer valor2) {
    return valor1.compareTo(valor2) == -1;
  }

  /**
   * metodo compara e diz se o valo1 igual o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja igual valor2
   */
  public static Boolean isEqual(Integer valor1, Integer valor2) {
    return valor1.compareTo(valor2) == 0;
  }

  /**
   * metodo compara e diz se o valo1 e maior que o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja maior que valor2
   */
  public static Boolean isMaiorQue(Double valor1, Double valor2) {
    return valor1.compareTo(valor2) == 1;
  }

  /**
   * metodo compara e diz se o valo1 e menor que o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja menor que valor2
   */
  public static Boolean isMenorQue(Double valor1, Double valor2) {
    return valor1.compareTo(valor2) == -1;
  }

  /**
   * metodo compara e diz se o valo1 igual o valor2
   *
   * @param valor1
   * @param valor2
   * @return true caso valor1 seja igual valor2
   */
  public static Boolean isEqual(Double valor1, Double valor2) {
    return valor1.compareTo(valor2) == 0;
  }

  public static Object jsonStringToObject(Object objeto, ObjectMapper mapper, String jsonArray)
      throws IOException, JsonParseException, JsonMappingException {
    return mapper.readValue(jsonArray, objeto.getClass());
  }

  private static String objectToJsonString(Object objeto, ObjectMapper mapper)
      throws JsonProcessingException {
    String jsonStr;
    jsonStr = mapper.writeValueAsString(objeto);
    return jsonStr;
  }

  public static final String[] hexStrings;

  static {
    hexStrings = new String[256];
    for (int i = 0; i < 256; i++) {
      StringBuilder d = new StringBuilder(2);
      char ch = Character.forDigit((byte) i >> 4 & 0x0F, 16);
      d.append(Character.toUpperCase(ch));
      ch = Character.forDigit((byte) i & 0x0F, 16);
      d.append(Character.toUpperCase(ch));
      hexStrings[i] = d.toString();
    }
  }

  /**
   * converts a byte array to hex string (suitable for dumps and ASCII packaging of Binary fields
   *
   * @param b - byte array
   * @return String representation
   */
  public static String hexString(byte[] b) {
    StringBuilder d = new StringBuilder(b.length * 2);
    for (byte aB : b) {
      d.append(hexStrings[(int) aB & 0xFF]);
    }
    return d.toString();
  }

  /**
   * @param senha
   * @return
   */
  public static String encodeSenhaSHA(String senha) {
    LegacyPasswordEncoder shaPasswordEncoder = new LegacyPasswordEncoder("SHA-256");
    return shaPasswordEncoder.encodePassword(senha, null);
  }

  public static String[] getNullPropertyNames(Object source) {
    final BeanWrapper src = new BeanWrapperImpl(source);
    java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();

    Set<String> emptyNames = new HashSet<String>();
    for (java.beans.PropertyDescriptor pd : pds) {
      Object srcValue = src.getPropertyValue(pd.getName());
      if (srcValue == null) emptyNames.add(pd.getName());
    }
    String[] result = new String[emptyNames.size()];
    return emptyNames.toArray(result);
  }

  public static String generateRandomLetrasUpperCase(int tamanho) {
    return RandomStringUtils.randomAlphabetic(tamanho).toUpperCase();
  }

  public static String generateRandomNumeros(int tamanho) {
    return RandomStringUtils.randomNumeric(tamanho);
  }

  public static String generateRandomLetrasENumerosUpperCase(int tamanho) {
    return RandomStringUtils.randomAlphanumeric(tamanho).toUpperCase();
  }

  public static String generateRandomLetrasENumerosECaracteresEspeciais(int tamanho) {
    return RandomStringUtils.random(tamanho, LETRAS_NUMEROS_CARACTERERS_ESPECIAIS);
  }

  public static boolean isNotNull(LocalDateTime data) {
    return data != null;
  }

  public static boolean isNotNull(Integer id) {
    return id != null;
  }

  public static boolean isNotNull(String text) {
    return text != null && !isEmpty(text);
  }

  public static boolean isNotNull(List<?> lista) {
    return lista != null && !lista.isEmpty();
  }

  public static boolean isNotNull(Object object) {
    return object != null;
  }

  public static Boolean paraMeterNotEmpty(String param) {
    return param != null && !param.isEmpty();
  }

  /*
      public static void main(String[] args) {

          String ln = generateRandomLetrasENumeros(10);
          System.out.println(ln);
  //		System.out.println(ln.length());

      }
  */
  /*
  Calcula digito verificador para contas bancárias do Banco do Brasil
  Retorna digitos de '0' até '10'. Sendo que o '10' deve ser alterado conforme a necessidade, para '0' ou 'x'
   */
  public static int modulo11DV(Integer idAgencia) {

    int base = 9;
    int r = 0;
    int soma = 0;
    int fator = 2;

    String[] numeros, parcial;
    String ag = idAgencia.toString();
    numeros = new String[ag.length() + 1];
    parcial = new String[ag.length() + 1];
    /* Separacao dos numeros */
    for (int i = ag.length(); i > 0; i--) {
      // pega cada numero isoladamente
      numeros[i] = ag.substring(i - 1, i);
      // Efetua multiplicacao do numero pelo fator
      parcial[i] = String.valueOf(Integer.parseInt(numeros[i]) * fator);
      // Soma dos digitos
      soma += Integer.parseInt(parcial[i]);
      if (fator == base) {
        // restaura fator de multiplicacao para 2
        fator = 1;
      }
      fator++;
    }
    /* Calculo do modulo 11 */
    if (r == 0) {
      soma *= 10;
      int dv = soma % 11;
      //	        if (dv == 10) {
      //	        	dv = 0;
      //	        }
      return dv;
    } else {
      int resto = soma % 11;
      return resto;
    }
  }

  public static boolean isInteger(String str) {
    if (str == null) {
      return false;
    }
    int length = str.length();
    if (length == 0) {
      return false;
    }
    int i = 0;
    if (str.charAt(0) == '-') {
      if (length == 1) {
        return false;
      }
      i = 1;
    }
    for (; i < length; i++) {
      char c = str.charAt(i);
      if (c < '0' || c > '9') {
        return false;
      }
    }
    return true;
  }

  public static boolean isBlankString(String... str) {
    return true;
  }

  // Converte uma List<String> em uma única String
  // Recebe a lista de String e o que será usado como separador
  public static String listStringToString(List<String> list, String separator) {

    StringBuilder sb = new StringBuilder();
    int count = 0;
    int size = list.size();

    for (String item : list) {
      sb.append(item);

      // enquanto nao for a última iteração, incluir o separador
      if (count++ != size - 1) sb.append(separator);
    }

    return sb.toString();
  }

  public static BigDecimal calcularJuroDiario(BigDecimal valorFinan, BigDecimal taxaMensal) {
    return valorFinan
        .multiply(converterTxMensalParaTxDiaria(taxaMensal))
        .setScale(6, BigDecimal.ROUND_HALF_UP);
  }

  private static BigDecimal converterTxMensalParaTxDiaria(BigDecimal taxaMensal) {
    BigDecimal perDia = BigDecimal.ONE.divide(new BigDecimal(30), 6, BigDecimal.ROUND_HALF_UP);
    return new BigDecimal(Math.pow((1 + taxaMensal.doubleValue()), (perDia.doubleValue())) - 1)
        .setScale(6, BigDecimal.ROUND_HALF_UP);
  }

  public static String formataContaCorrente(Long contaCorrente) {
    String ccFormatada = contaCorrente.toString();
    String ultimoCharacter = ccFormatada.substring(ccFormatada.length() - 1);
    return ccFormatada.substring(0, ccFormatada.length() - 1) + "-" + ultimoCharacter;
  }

  public static String currencyFormat(BigDecimal n) {
    Locale ptBR = new Locale("pt", "BR");
    return NumberFormat.getCurrencyInstance(ptBR).format(n);
  }

  public static void inputStreamToFile(InputStream pdfStream, String arquivo) {
    File targetFile = new File(arquivo);
    try {
      FileUtils.copyInputStreamToFile(pdfStream, targetFile);
    } catch (IOException e) {
      e.printStackTrace();
      throw new GenericServiceException(
          "Ocorreu um erro ao tentar salvar o stream para arquivo", e.getMessage());
    }
  }

  public static String removerAcentos(String str) {
    return Normalizer.normalize(str, Normalizer.Form.NFD).replaceAll("[^\\p{ASCII}]", "");
  }

  public static ByteArrayInputStream convertObjectToInputStream(byte item[]) {

    if (item == null && item.length <= 0) {
      throw new GenericServiceException("Não foi possível encontrar bytes");
    }

    return new ByteArrayInputStream(item);
  }

  public static String formatarData(String data) {
    if (data == null || data.isEmpty()) {
      return "";
    }

    if (data.length() >= 10) {
      data = data.substring(0, 10);
    } else {
      return data;
    }

    String[] partes = data.split("-");
    if (partes.length == 3) {
      return partes[2] + "/" + partes[1] + "/" + partes[0];
    }

    return data;
  }

  public static String formatarDataHora(String data) {
    if (data == null || data.isEmpty()) {
      return "";
    }

    if (data.length() >= 16) {
      data = data.substring(0, 16);
    } else {
      return data;
    }

    String[] partes = data.split(" ");
    if (partes.length < 2) {
      return data;
    }

    String dataPart = partes[0];
    String horaMinutoPart = partes[1];

    String[] dataDividida = dataPart.split("-");
    if (dataDividida.length == 3) {
      return dataDividida[2] + "/" + dataDividida[1] + "/" + dataDividida[0] + " " + horaMinutoPart;
    }

    return data;
  }

  public static String formatarNumeroCartao(String numeroCartao) {
    if (numeroCartao == null || numeroCartao.isEmpty()) {
      return "";
    }

    String[] partes = numeroCartao.split("-");

    if (partes.length != 2) {
      return numeroCartao;
    }

    String primeirosDigitos = partes[0];
    String ultimosDigitos = partes[1];

    while (ultimosDigitos.length() < 4) {
      ultimosDigitos = "0" + ultimosDigitos;
    }

    return primeirosDigitos + " - XXXXXX - " + ultimosDigitos;
  }

  public static InputStream base64ToInputStream(String base64) {
    byte[] decodedBytes = Base64.getDecoder().decode(base64);

    return new ByteArrayInputStream(decodedBytes);
  }
}
