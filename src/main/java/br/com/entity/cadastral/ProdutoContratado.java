package br.com.entity.cadastral;

import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import org.hibernate.annotations.DynamicUpdate;

@Entity
@Table(name = "produto_contratado", schema = "cadastral")
@SequenceGenerator(
    name = "seq_id_produto_contratado",
    sequenceName = "cadastral.seq_id_produto_contratado",
    initialValue = 1,
    allocationSize = 1)
@ApiModel(value = "Produto Contratado")
@DynamicUpdate(true)
public class ProdutoContratado implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @Column(name = "id_contrato", nullable = false)
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_id_produto_contratado")
  private Long idContrato;

  @Column(name = "dt_hr_inclusao", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date dtHrInclusao;

  @Column(name = "dt_contrato", nullable = false)
  @Temporal(TemporalType.DATE)
  private Date dtContrato;

  @Column(name = "id_prod_instituicao")
  private Integer idProdInstituicao;

  @Column(name = "dt_hr_cancelamento", nullable = false)
  @Temporal(TemporalType.TIMESTAMP)
  private Date dtHrCancelamento;

  @Column(name = "id_usuario_inclusao")
  private Integer idUsuarioInclusao;

  @Column(name = "id_usuario_cancelamento")
  private Integer idUsuarioCancelamento;

  @Column(name = "taxa_carga", nullable = false)
  private BigDecimal taxaCarga;

  @Column(name = "tarifa_carga", nullable = false)
  private BigDecimal tarifaCarga;

  @Column(name = "tarifa_emissao", nullable = false)
  private BigDecimal tarifaEmissao;

  @Column(name = "tarifa_reposicao", nullable = false)
  private BigDecimal tarifaReposicao;

  @Column(name = "tarifa_emissao_descartavel", nullable = false)
  private BigDecimal tarifaEmissaoDescartavel;

  @Column(name = "tarifa_valor_fatura", nullable = false)
  private BigDecimal tarifaValorFatura;

  @Column(name = "valor_fatura_min", nullable = false)
  private BigDecimal valorFaturaMin;

  @Column(name = "prazo_pagamento")
  private Integer prazoPagamento;

  @Column(name = "id_processadora")
  private Integer idProcessadora;

  @Column(name = "id_instituicao")
  private Integer idInstituicao;

  @Column(name = "id_regional", nullable = false)
  private Integer idRegional;

  @Column(name = "id_filial", nullable = false)
  private Integer idFilial;

  @Column(name = "id_ponto_de_relacionamento", nullable = false)
  private Integer idPontoRelacionamento;

  @Transient private String descProdutoInstituicao;

  @Column(name = "reestabelecimento_limite")
  private Integer reestabelecimentoLimite;

  @Column(name = "dias_corte")
  private String diasCorte;

  @Column(name = "permitir_nome_impresso")
  private Integer permitirNomeImpresso;

  @Column(name = "tarifa_carga_emergencial")
  private BigDecimal tarifaCargaEmergencial;

  @Column(name = "tarifa_doc_ted")
  private BigDecimal tarifaDocTed;

  @Column(name = "tarifa_op")
  private BigDecimal tarifaOp;

  @Column(name = "tarifa_postagem")
  private BigDecimal tarifaPostagem;

  @Column(name = "tarifa_renovacao")
  private BigDecimal tarifaRenovacao;

  // 0 - Não Aplicável / 1 - Instituição Emissora / 2 - Regional / 3 - Filial / 4 - Ponto de
  // Relacionamento / 5 - Endereço do Portador / 6 - Setor/Filial
  @Column(name = "tipo_postagem")
  private Integer tipoPostagemProduto;

  @Column(name = "identificador_externo")
  private String identificadorExterno;

  @Column(name = "permite_carga")
  private Integer permiteCarga;

  @Column(name = "nome_cartao_impresso", length = 19)
  private String nomeCartaoImpresso;

  @Column(name = "tipo_cobranca_reposicao")
  private Integer tipoCobrancaReposicao;

  @Column(name = "identificador_jcard")
  private Long identificadorJCard;

  @Column(name = "id_contrato_corresp")
  private Long idContratoCorrespondente;

  @Column(name = "dt_hr_replicacao")
  private Date dataHoraReplicacao;

  @Column(name = "tarifa_convenio")
  private BigDecimal tarifaConvenio;

  @Column(name = "taxa_convenio")
  private BigDecimal taxaConvenio;

  @Column(name = "tx_juro_cheque_especial")
  private BigDecimal txJuroChequeEspecial;

  @Column(name = "percentual_cheque_especial")
  private BigDecimal percentualChequeEspecial;

  @Column(name = "id_contract")
  private Long idContract;

  @Column(name = "primeiro_cartao_virtual")
  private Integer primeiroCartaoVirtual;

  @Column(name = "bl_permite_fisico")
  private Boolean permiteCartaoFisico;

  @Column(name = "filial_faturamento")
  private Integer filialFaturamento;

  @Column(name = "bl_permite_compra_online")
  private Boolean permiteCompraOnline = Boolean.TRUE;

  @JsonIgnore
  @ManyToOne
  @JoinColumns({
    @JoinColumn(
        name = "id_processadora",
        referencedColumnName = "id_processadora",
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_instituicao",
        referencedColumnName = "id_instituicao",
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_regional",
        referencedColumnName = "id_regional",
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_filial",
        referencedColumnName = "id_filial",
        insertable = false,
        updatable = false),
    @JoinColumn(
        name = "id_ponto_de_relacionamento",
        referencedColumnName = "id_ponto_de_relacionamento",
        insertable = false,
        updatable = false)
  })
  private HierarquiaPontoDeRelacionamento pontoRelacionamento;

  @JsonIgnore
  @ManyToOne
  @JoinColumn(
      name = "id_prod_instituicao",
      referencedColumnName = "id_prod_instituicao",
      insertable = false,
      updatable = false,
      nullable = false)
  private ProdutoInstituicao produtoInstituicao;

  @Transient private List<Integer> diasCorteList;

  @Transient private Integer permiteRestricaoMcc;

  @Transient private Integer tipoRestricaoMcc;

  @Transient private TipoProdutoEnum tipoProduto;

  @Transient private Long idGrupoProduto;

  @Column(name = "permite_carga_b2b")
  private Boolean permiteCargaB2B;

  public BigDecimal getTxJuroChequeEspecial() {
    return txJuroChequeEspecial;
  }

  public void setTxJuroChequeEspecial(BigDecimal txJuroChequeEspecial) {
    this.txJuroChequeEspecial = txJuroChequeEspecial;
  }

  public BigDecimal getPercentualChequeEspecial() {
    return percentualChequeEspecial;
  }

  public void setPercentualChequeEspecial(BigDecimal percentualChequeEspecial) {
    this.percentualChequeEspecial = percentualChequeEspecial;
  }

  public String getDescProdutoInstituicao() {
    if (descProdutoInstituicao == null && produtoInstituicao != null) {
      setDescProdutoInstituicao(produtoInstituicao.getDescProdInstituicao());
    }
    return descProdutoInstituicao;
  }

  public void setDescProdutoInstituicao(String descProdutoInstituicao) {
    this.descProdutoInstituicao = descProdutoInstituicao;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result =
        prime * result + ((descProdutoInstituicao == null) ? 0 : descProdutoInstituicao.hashCode());
    result = prime * result + ((diasCorte == null) ? 0 : diasCorte.hashCode());
    result = prime * result + ((dtContrato == null) ? 0 : dtContrato.hashCode());
    result = prime * result + ((dtHrCancelamento == null) ? 0 : dtHrCancelamento.hashCode());
    result = prime * result + ((dtHrInclusao == null) ? 0 : dtHrInclusao.hashCode());
    result = prime * result + ((idContrato == null) ? 0 : idContrato.hashCode());
    result = prime * result + ((idFilial == null) ? 0 : idFilial.hashCode());
    result = prime * result + ((idInstituicao == null) ? 0 : idInstituicao.hashCode());
    result =
        prime * result + ((idPontoRelacionamento == null) ? 0 : idPontoRelacionamento.hashCode());
    result = prime * result + ((idProcessadora == null) ? 0 : idProcessadora.hashCode());
    result = prime * result + ((idProdInstituicao == null) ? 0 : idProdInstituicao.hashCode());
    result = prime * result + ((idRegional == null) ? 0 : idRegional.hashCode());
    result =
        prime * result + ((idUsuarioCancelamento == null) ? 0 : idUsuarioCancelamento.hashCode());
    result = prime * result + ((idUsuarioInclusao == null) ? 0 : idUsuarioInclusao.hashCode());
    result =
        prime * result + ((permitirNomeImpresso == null) ? 0 : permitirNomeImpresso.hashCode());
    result = prime * result + ((pontoRelacionamento == null) ? 0 : pontoRelacionamento.hashCode());
    result = prime * result + ((prazoPagamento == null) ? 0 : prazoPagamento.hashCode());
    result = prime * result + ((produtoInstituicao == null) ? 0 : produtoInstituicao.hashCode());
    result =
        prime * result
            + ((reestabelecimentoLimite == null) ? 0 : reestabelecimentoLimite.hashCode());
    result = prime * result + ((tarifaCarga == null) ? 0 : tarifaCarga.hashCode());
    result =
        prime * result + ((tarifaCargaEmergencial == null) ? 0 : tarifaCargaEmergencial.hashCode());
    result = prime * result + ((tarifaDocTed == null) ? 0 : tarifaDocTed.hashCode());
    result = prime * result + ((tarifaEmissao == null) ? 0 : tarifaEmissao.hashCode());
    result =
        prime * result
            + ((tarifaEmissaoDescartavel == null) ? 0 : tarifaEmissaoDescartavel.hashCode());
    result = prime * result + ((tarifaOp == null) ? 0 : tarifaOp.hashCode());
    result = prime * result + ((tarifaReposicao == null) ? 0 : tarifaReposicao.hashCode());
    result = prime * result + ((tarifaValorFatura == null) ? 0 : tarifaValorFatura.hashCode());
    result = prime * result + ((taxaCarga == null) ? 0 : taxaCarga.hashCode());
    result = prime * result + ((valorFaturaMin == null) ? 0 : valorFaturaMin.hashCode());
    return result;
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) return true;
    if (obj == null) return false;
    if (getClass() != obj.getClass()) return false;
    ProdutoContratado other = (ProdutoContratado) obj;
    if (descProdutoInstituicao == null) {
      if (other.descProdutoInstituicao != null) return false;
    } else if (!descProdutoInstituicao.equals(other.descProdutoInstituicao)) return false;
    if (diasCorte == null) {
      if (other.diasCorte != null) return false;
    } else if (!diasCorte.equals(other.diasCorte)) return false;
    if (dtContrato == null) {
      if (other.dtContrato != null) return false;
    } else if (!dtContrato.equals(other.dtContrato)) return false;
    if (dtHrCancelamento == null) {
      if (other.dtHrCancelamento != null) return false;
    } else if (!dtHrCancelamento.equals(other.dtHrCancelamento)) return false;
    if (dtHrInclusao == null) {
      if (other.dtHrInclusao != null) return false;
    } else if (!dtHrInclusao.equals(other.dtHrInclusao)) return false;
    if (idContrato == null) {
      if (other.idContrato != null) return false;
    } else if (!idContrato.equals(other.idContrato)) return false;
    if (idFilial == null) {
      if (other.idFilial != null) return false;
    } else if (!idFilial.equals(other.idFilial)) return false;
    if (idInstituicao == null) {
      if (other.idInstituicao != null) return false;
    } else if (!idInstituicao.equals(other.idInstituicao)) return false;
    if (idPontoRelacionamento == null) {
      if (other.idPontoRelacionamento != null) return false;
    } else if (!idPontoRelacionamento.equals(other.idPontoRelacionamento)) return false;
    if (idProcessadora == null) {
      if (other.idProcessadora != null) return false;
    } else if (!idProcessadora.equals(other.idProcessadora)) return false;
    if (idProdInstituicao == null) {
      if (other.idProdInstituicao != null) return false;
    } else if (!idProdInstituicao.equals(other.idProdInstituicao)) return false;
    if (idRegional == null) {
      if (other.idRegional != null) return false;
    } else if (!idRegional.equals(other.idRegional)) return false;
    if (idUsuarioCancelamento == null) {
      if (other.idUsuarioCancelamento != null) return false;
    } else if (!idUsuarioCancelamento.equals(other.idUsuarioCancelamento)) return false;
    if (idUsuarioInclusao == null) {
      if (other.idUsuarioInclusao != null) return false;
    } else if (!idUsuarioInclusao.equals(other.idUsuarioInclusao)) return false;
    if (permitirNomeImpresso == null) {
      if (other.permitirNomeImpresso != null) return false;
    } else if (!permitirNomeImpresso.equals(other.permitirNomeImpresso)) return false;
    if (pontoRelacionamento == null) {
      if (other.pontoRelacionamento != null) return false;
    } else if (!pontoRelacionamento.equals(other.pontoRelacionamento)) return false;
    if (prazoPagamento == null) {
      if (other.prazoPagamento != null) return false;
    } else if (!prazoPagamento.equals(other.prazoPagamento)) return false;
    if (produtoInstituicao == null) {
      if (other.produtoInstituicao != null) return false;
    } else if (!produtoInstituicao.equals(other.produtoInstituicao)) return false;
    if (reestabelecimentoLimite == null) {
      if (other.reestabelecimentoLimite != null) return false;
    } else if (!reestabelecimentoLimite.equals(other.reestabelecimentoLimite)) return false;
    if (tarifaCarga == null) {
      if (other.tarifaCarga != null) return false;
    } else if (!tarifaCarga.equals(other.tarifaCarga)) return false;
    if (tarifaCargaEmergencial == null) {
      if (other.tarifaCargaEmergencial != null) return false;
    } else if (!tarifaCargaEmergencial.equals(other.tarifaCargaEmergencial)) return false;
    if (tarifaDocTed == null) {
      if (other.tarifaDocTed != null) return false;
    } else if (!tarifaDocTed.equals(other.tarifaDocTed)) return false;
    if (tarifaEmissao == null) {
      if (other.tarifaEmissao != null) return false;
    } else if (!tarifaEmissao.equals(other.tarifaEmissao)) return false;
    if (tarifaEmissaoDescartavel == null) {
      if (other.tarifaEmissaoDescartavel != null) return false;
    } else if (!tarifaEmissaoDescartavel.equals(other.tarifaEmissaoDescartavel)) return false;
    if (tarifaOp == null) {
      if (other.tarifaOp != null) return false;
    } else if (!tarifaOp.equals(other.tarifaOp)) return false;
    if (tarifaReposicao == null) {
      if (other.tarifaReposicao != null) return false;
    } else if (!tarifaReposicao.equals(other.tarifaReposicao)) return false;
    if (tarifaValorFatura == null) {
      if (other.tarifaValorFatura != null) return false;
    } else if (!tarifaValorFatura.equals(other.tarifaValorFatura)) return false;
    if (taxaCarga == null) {
      if (other.taxaCarga != null) return false;
    } else if (!taxaCarga.equals(other.taxaCarga)) return false;
    if (valorFaturaMin == null) {
      if (other.valorFaturaMin != null) return false;
    } else if (!valorFaturaMin.equals(other.valorFaturaMin)) return false;
    return true;
  }

  public String getNomeCartaoImpresso() {
    return nomeCartaoImpresso;
  }

  public void setNomeCartaoImpresso(String nomeCartaoImpresso) {
    this.nomeCartaoImpresso = nomeCartaoImpresso;
  }

  public Integer getPermiteCarga() {
    return permiteCarga;
  }

  public void setPermiteCarga(Integer permiteCarga) {
    this.permiteCarga = permiteCarga;
  }

  public String getIdentificadorExterno() {
    return identificadorExterno;
  }

  public void setIdentificadorExterno(String identificadorExterno) {
    this.identificadorExterno = identificadorExterno;
  }

  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public HierarquiaPontoDeRelacionamento getPontoRelacionamento() {
    return pontoRelacionamento;
  }

  public void setPontoRelacionamento(HierarquiaPontoDeRelacionamento pontoRelacionamento) {
    this.pontoRelacionamento = pontoRelacionamento;
  }

  public Long getIdContrato() {
    return idContrato;
  }

  public void setIdContrato(Long idContrato) {
    this.idContrato = idContrato;
  }

  public Date getDtHrInclusao() {
    return dtHrInclusao;
  }

  public void setDtHrInclusao(Date dtHrInclusao) {
    this.dtHrInclusao = dtHrInclusao;
  }

  public Date getDtContrato() {
    return dtContrato;
  }

  public void setDtContrato(Date dtContrato) {
    this.dtContrato = dtContrato;
  }

  public Integer getIdProdInstituicao() {
    return idProdInstituicao;
  }

  public void setIdProdInstituicao(Integer idProdInstituicao) {
    this.idProdInstituicao = idProdInstituicao;
  }

  public Date getDtHrCancelamento() {
    return dtHrCancelamento;
  }

  public void setDtHrCancelamento(Date dtHrCancelamento) {
    this.dtHrCancelamento = dtHrCancelamento;
  }

  public Integer getIdUsuarioCancelamento() {
    return idUsuarioCancelamento;
  }

  public void setIdUsuarioCancelamento(Integer idUsuarioCancelamento) {
    this.idUsuarioCancelamento = idUsuarioCancelamento;
  }

  public BigDecimal getTaxaCarga() {
    return taxaCarga;
  }

  public void setTaxaCarga(BigDecimal taxaCarga) {
    this.taxaCarga = taxaCarga;
  }

  public BigDecimal getTarifaCarga() {
    return tarifaCarga;
  }

  public void setTarifaCarga(BigDecimal tarifaCarga) {
    this.tarifaCarga = tarifaCarga;
  }

  public BigDecimal getTarifaEmissao() {
    return tarifaEmissao;
  }

  public void setTarifaEmissao(BigDecimal tarifaEmissao) {
    this.tarifaEmissao = tarifaEmissao;
  }

  public BigDecimal getTarifaReposicao() {
    return tarifaReposicao;
  }

  public void setTarifaReposicao(BigDecimal tarifaReposicao) {
    this.tarifaReposicao = tarifaReposicao;
  }

  public BigDecimal getTarifaEmissaoDescartavel() {
    return tarifaEmissaoDescartavel;
  }

  public void setTarifaEmissaoDescartavel(BigDecimal tarifaEmissaoDescartavel) {
    this.tarifaEmissaoDescartavel = tarifaEmissaoDescartavel;
  }

  public Integer getPrazoPagamento() {
    return prazoPagamento;
  }

  public void setPrazoPagamento(Integer prazoPagamento) {
    this.prazoPagamento = prazoPagamento;
  }

  public Integer getIdProcessadora() {
    return idProcessadora;
  }

  public void setIdProcessadora(Integer idProcessadora) {
    this.idProcessadora = idProcessadora;
  }

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public Integer getIdRegional() {
    return idRegional;
  }

  public void setIdRegional(Integer idRegional) {
    this.idRegional = idRegional;
  }

  public Integer getIdFilial() {
    return idFilial;
  }

  public void setIdFilial(Integer idFilial) {
    this.idFilial = idFilial;
  }

  public Integer getIdPontoRelacionamento() {
    return idPontoRelacionamento;
  }

  public void setIdPontoRelacionamento(Integer idPontoRelacionamento) {
    this.idPontoRelacionamento = idPontoRelacionamento;
  }

  public ProdutoInstituicao getProdutoInstituicao() {
    return produtoInstituicao;
  }

  public void setProdutoInstituicao(ProdutoInstituicao produtoInstituicao) {
    this.produtoInstituicao = produtoInstituicao;
  }

  public BigDecimal getTarifaValorFatura() {
    return tarifaValorFatura;
  }

  public void setTarifaValorFatura(BigDecimal tarifaValorFatura) {
    this.tarifaValorFatura = tarifaValorFatura;
  }

  public BigDecimal getValorFaturaMin() {
    return valorFaturaMin;
  }

  public void setValorFaturaMin(BigDecimal valorFaturaMin) {
    this.valorFaturaMin = valorFaturaMin;
  }

  public Integer getReestabelecimentoLimite() {
    return reestabelecimentoLimite;
  }

  public void setReestabelecimentoLimite(Integer reestabelecimentoLimite) {
    this.reestabelecimentoLimite = reestabelecimentoLimite;
  }

  public String getDiasCorte() {
    return diasCorte;
  }

  public void setDiasCorte(String diasCorte) {
    this.diasCorte = diasCorte;
  }

  public Integer getPermitirNomeImpresso() {
    return permitirNomeImpresso;
  }

  public void setPermitirNomeImpresso(Integer permitirNomeImpresso) {
    this.permitirNomeImpresso = permitirNomeImpresso;
  }

  public BigDecimal getTarifaCargaEmergencial() {
    return tarifaCargaEmergencial;
  }

  public void setTarifaCargaEmergencial(BigDecimal tarifaCargaEmergencial) {
    this.tarifaCargaEmergencial = tarifaCargaEmergencial;
  }

  public BigDecimal getTarifaDocTed() {
    return tarifaDocTed;
  }

  public void setTarifaDocTed(BigDecimal tarifaDocTed) {
    this.tarifaDocTed = tarifaDocTed;
  }

  public BigDecimal getTarifaOp() {
    return tarifaOp;
  }

  public void setTarifaOp(BigDecimal tarifaOp) {
    this.tarifaOp = tarifaOp;
  }

  public BigDecimal getTarifaPostagem() {
    return tarifaPostagem;
  }

  public void setTarifaPostagem(BigDecimal tarifaPostagem) {
    this.tarifaPostagem = tarifaPostagem;
  }

  public Integer getTipoPostagemProduto() {
    return tipoPostagemProduto;
  }

  public void setTipoPostagemProduto(Integer tipoPostagemProduto) {
    this.tipoPostagemProduto = tipoPostagemProduto;
  }

  public BigDecimal getTarifaRenovacao() {
    return tarifaRenovacao;
  }

  public void setTarifaRenovacao(BigDecimal tarifaRenovacao) {
    this.tarifaRenovacao = tarifaRenovacao;
  }

  public List<Integer> getDiasCorteList() {
    if (getDiasCorte() != null && diasCorteList == null) {
      List<Integer> dias = new ArrayList<>();
      setDiasCorte(getDiasCorte().trim());
      Arrays.asList(getDiasCorte().split(";")).forEach(it -> dias.add(new Integer(it)));
      setDiasCorteList(dias);
    }
    return diasCorteList;
  }

  public void setDiasCorteList(List<Integer> diasCorteList) {
    this.diasCorteList = diasCorteList;
  }

  public Long getIdentificadorJCard() {
    return identificadorJCard;
  }

  public void setIdentificadorJCard(Long identificadorJCard) {
    this.identificadorJCard = identificadorJCard;
  }

  public Integer getPermiteRestricaoMcc() {
    return permiteRestricaoMcc;
  }

  public void setPermiteRestricaoMcc(Integer permiteRestricaoMcc) {
    this.permiteRestricaoMcc = permiteRestricaoMcc;
  }

  public Long getIdContratoCorrespondente() {
    return idContratoCorrespondente;
  }

  public void setIdContratoCorrespondente(Long idContratoCorrespondente) {
    this.idContratoCorrespondente = idContratoCorrespondente;
  }

  public Date getDataHoraReplicacao() {
    return dataHoraReplicacao;
  }

  public void setDataHoraReplicacao(Date dataHoraReplicacao) {
    this.dataHoraReplicacao = dataHoraReplicacao;
  }

  public Integer getTipoCobrancaReposicao() {
    return tipoCobrancaReposicao;
  }

  public void setTipoCobrancaReposicao(Integer tipoCobrancaReposicao) {
    this.tipoCobrancaReposicao = tipoCobrancaReposicao;
  }

  public Integer getTipoRestricaoMcc() {
    return tipoRestricaoMcc;
  }

  public void setTipoRestricaoMcc(Integer tipoRestricaoMcc) {
    this.tipoRestricaoMcc = tipoRestricaoMcc;
  }

  public BigDecimal getTarifaConvenio() {
    return tarifaConvenio;
  }

  public void setTarifaConvenio(BigDecimal tarifaConvenio) {
    this.tarifaConvenio = tarifaConvenio;
  }

  public BigDecimal getTaxaConvenio() {
    return taxaConvenio;
  }

  public void setTaxaConvenio(BigDecimal taxaConvenio) {
    this.taxaConvenio = taxaConvenio;
  }

  public Long getIdContract() {
    return idContract;
  }

  public void setIdContract(Long idContract) {
    this.idContract = idContract;
  }

  public Integer getPrimeiroCartaoVirtual() {
    return primeiroCartaoVirtual;
  }

  public void setPrimeiroCartaoVirtual(Integer primeiroCartaoVirtual) {
    this.primeiroCartaoVirtual = primeiroCartaoVirtual;
  }

  public Boolean getPermiteCartaoFisico() {
    return permiteCartaoFisico;
  }

  public void setPermiteCartaoFisico(Boolean permiteCartaoFisico) {
    this.permiteCartaoFisico = permiteCartaoFisico;
  }

  public TipoProdutoEnum getTipoProduto() {
    return tipoProduto;
  }

  public void setTipoProduto(TipoProdutoEnum tipoProduto) {
    this.tipoProduto = tipoProduto;
  }

  public Long getIdGrupoProduto() {
    return idGrupoProduto;
  }

  public void setIdGrupoProduto(Long idGrupoProduto) {
    this.idGrupoProduto = idGrupoProduto;
  }

  public Integer getFilialFaturamento() {
    return filialFaturamento;
  }

  public void setFilialFaturamento(Integer filialFaturamento) {
    this.filialFaturamento = filialFaturamento;
  }

  public Boolean getPermiteCargaB2b() {
    return permiteCargaB2B;
  }

  public void setPermiteCargaB2b(Boolean permiteCargaB2B) {
    this.permiteCargaB2B = permiteCargaB2B;
  }

  public Boolean getPermiteCompraOnline() {
    return permiteCompraOnline;
  }

  public void setPermiteCompraOnline(Boolean permiteCompraOnline) {
    this.permiteCompraOnline = permiteCompraOnline;
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }
}
