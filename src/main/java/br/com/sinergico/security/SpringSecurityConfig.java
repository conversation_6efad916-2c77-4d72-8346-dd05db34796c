package br.com.sinergico.security;

import br.com.sinergico.service.suporte.LogChamadaApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpMethod;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

/** Created by gustavo on 31/10/15. */
@Configuration
@Order(2)
@EnableWebSecurity
@Component
@EnableGlobalMethodSecurity(securedEnabled = true)
public class SpringSecurityConfig extends WebSecurityConfigurerAdapter {

  @Autowired private UserService userService;

  @Autowired RequestMappingHandlerMapping requestMappingHandlerMapping;

  @Autowired private TokenAuthenticationService tokenAuthenticationService;

  @Autowired private TokenAuthenticationPortadorService tokenAuthenticationPortadorService;

  @Autowired private TokenAuthenticationCorporativoService tokenAuthenticationCorporativoService;

  @Autowired private TokenAuthenticationSwaggerService tokenAuthenticationSwaggerService;

  @Autowired
  private TokenAuthenticationEstabelecimentoService tokenAuthenticationEstabelecimentoService;

  @Autowired private LogChamadaApiService logChamadaApiService;

  @Autowired private TokenAuthenticationUsuarioUnicoService tokenAuthenticationUsuarioUnicoService;

  public SpringSecurityConfig() {
    super(true);
  }

  @Override
  protected void configure(HttpSecurity http) throws Exception {

    http.exceptionHandling()
        .and()
        .anonymous()
        .and()
        .servletApi()
        .and()
        // .headers().cacheControl().and().
        .authorizeRequests()

        // Allow anonymous logins
        .antMatchers("/api/auth/login")
        .permitAll()
        .antMatchers("/api/auth/b2b/login")
        .permitAll()
        .antMatchers("/api/auth/estabelecimento/login")
        .permitAll()
        .antMatchers("/api/auth/estabelecimento/login/app")
        .permitAll()
        .antMatchers("/api/login-unico/auth")
        .permitAll()
        .antMatchers("/api/login-unico/token")
        .permitAll()
        .antMatchers("/api/login-unico/trocar-senha")
        .permitAll()
        .antMatchers("/api/portador/login/")
        .permitAll()
        .antMatchers("/api/portador/login/verifica-abertura-conta/**")
        .permitAll()
        .antMatchers("/api/portador/login/criar-login")
        .permitAll()
        .antMatchers("/api/portador/login/pre-cadastro")
        .permitAll()
        .antMatchers("/api/portador/login/auth")
        .permitAll()
        .antMatchers("/api/portador/login/v2/auth")
        .permitAll()
        .antMatchers("/api/portador/login/auth-parametrizado")
        .permitAll()
        .antMatchers("/api/portador/login/auth-motiva")
        .permitAll()
        .antMatchers("/api/portador/login/logout")
        .permitAll()
        .antMatchers("/api/portador/login/recuperar-senha")
        .permitAll()
        .antMatchers("/api/portador/login/recuperar-senha/email")
        .permitAll()
        .antMatchers("/api/portador/login/recadastrar-senha")
        .permitAll()
        .antMatchers("/api/portador/login/redefinir-senha")
        .permitAll()
        .antMatchers("/api/portador/login/sincronizar-portador")
        .permitAll()
        .antMatchers("/api/portador/login/v2/redefinir-senha")
        .permitAll()
        .antMatchers("/api/mktplace/portador/open/**")
        .permitAll()
        .antMatchers("/api/mktplace2/**")
        .permitAll()
        .antMatchers("/api/shopping/**")
        .permitAll()
        .antMatchers("/api/usuario/estabelecimento/trocar-senha")
        .permitAll()
        .antMatchers("/api/usuario/esqueci-senha/**")
        .permitAll()
        .antMatchers("/api/usuario/estabelecimento/trocar-propria-senha")
        .permitAll()
        .antMatchers("/api/estabelecimento/pre-cadastrar")
        .permitAll()
        .antMatchers("/api/estabelecimento/listar-todos-pre-cadastros")
        .permitAll()
        .antMatchers("/api/estabelecimento/editar-pre-cadastro")
        .permitAll()
        .antMatchers("/api/portador/login/get-pre-cadastro/**")
        .permitAll()
        .antMatchers("/api/portador/login/get-conta/infinancas/pontos-inmais/**")
        .permitAll()
        .antMatchers("/api/endereco/criar-pre-cadastro")
        .permitAll()
        .antMatchers("/api/portador/login/criar-login/pre-cadastro")
        .permitAll()
        .antMatchers("/api/portador/login/get-login/**")
        .permitAll()
        .antMatchers("/api/pessoa/verifica-tipo-conta/**")
        .permitAll()
        .antMatchers("/api/pessoa/verifica-tipo-credencial/")
        .permitAll()
        .antMatchers("/api/representante-legal/valido/credencial")
        .permitAll()
        .antMatchers("/api/representante-legal/valido/conta")
        .permitAll()
        .antMatchers("/api/representante-legal/valido/documento")
        .permitAll()
        .antMatchers("/api/parametro-valor/aplicativo-ativo")
        .permitAll()
        .antMatchers("/api/estabelecimento/completar-pre-cadastro")
        .permitAll()
        .antMatchers("/api/sistema/auth/login")
        .permitAll()
        .antMatchers("/api/usuario/trocar-propria-senha")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/api/loyalty/dados-adicionais/**")
        .permitAll()
        .antMatchers("/api/loyalty/dados-principais/status/**")
        .permitAll()
        .antMatchers("/api/log-cadastro-portador-web-mobile/**")
        .permitAll()
        .antMatchers("/api/loyalty/instituicao/**")
        .permitAll()
        .antMatchers("/api/loyalty/voucher/**")
        .permitAll()
        .antMatchers("/api/token-acesso/validar/**")
        .permitAll()
        .antMatchers("/api/token-acesso/enviar/token-cadastro-login")
        .permitAll()
        .antMatchers("/api/token-acesso/enviar/token-login-acesso-por-usuario")
        .permitAll()
        .antMatchers("/api/token-acesso/enviar/token-login-acesso-por-usuario-b2b")
        .permitAll()
        .antMatchers("/api/token-acesso/usar/**")
        .permitAll()
        .antMatchers("/api/token-redefinicao-senha/enviar/token-redefinir-senha")
        .permitAll()
        .antMatchers("/api/token-redefinicao-senha/v2/enviar/token-redefinir-senha")
        .permitAll()
        .antMatchers("/api/token-redefinicao-senha/usar")
        .permitAll()
        .antMatchers("/api/loyalty/rotator/**")
        .permitAll()
        .antMatchers("/api/portador/brbcard/**")
        .permitAll()
        .antMatchers("/api/portador/credencial/corporativo")
        .permitAll()
        .antMatchers("/api/portador/conta/criar-conta-completa")
        .permitAll()
        .antMatchers("/api/portador/conta/buscar-portador/**")
        .permitAll()
        .antMatchers("/api/antifraude/verificar/**")
        .permitAll()
        .antMatchers("/api/antifraude/validar-ocr")
        .permitAll()
        .antMatchers("/api/antifraude/validar-ocr/caf/**")
        .permitAll()
        .antMatchers("/api/antifraude/validar-ocr-pj/caf/**")
        .permitAll()
        .antMatchers("/api/antifraude/get-status/caf/**")
        .permitAll()
        .antMatchers("/api/antifraude/webhook-status/caf")
        .permitAll()
        .antMatchers("/api/antifraude/standby/**")
        .permitAll()
        .antMatchers("/api/antifraude/detail/**")
        .permitAll()
        .antMatchers("/api/antifraude/caf/registrar-validacao-facial/**")
        .permitAll()
        .antMatchers("/api/contato/buscar-fale-conosco/**")
        .permitAll()
        .antMatchers("/api/produtoinstituicao/buscar/pessoa-fisica/**")
        .permitAll()
        .antMatchers("/api/produtoinstituicao/buscar/pessoa-juridica/**")
        .permitAll()
        .antMatchers("/api/pontorelacionamento/instituicao/**")
        .permitAll()
        .antMatchers("/api/versao-app/verifica-versao")
        .permitAll()
        // Webhook's pix
        .antMatchers("/api/gi/**")
        .permitAll()
        .antMatchers("/api/bank10/gi/**")
        .permitAll()
        // Webhook's Zenvia
        .antMatchers("/api/zenvia/webhook/**")
        .permitAll()
        // cadastro de portadores via cardholder ou aplicativo
        .antMatchers("/api/logradouro/card-holder/**")
        .permitAll()
        .antMatchers("/api/CredencialPreEmitida/card-holder/**")
        .permitAll()
        .antMatchers("/api/proposta/create/proposta/pj")
        .permitAll()
        .antMatchers("/api/proposta/v2/create/proposta/pj")
        .permitAll()
        // Onboard
        .antMatchers("/api/portador/login/validar-cadastro-onboard")
        .permitAll()
        .antMatchers("/api/portador/login/validar-cadastro-onboard-sem-caf")
        .permitAll()
        // Validar cadastro portador login corporativo
        .antMatchers("/api/portador/login/validar-cadastro-corporativo")
        .permitAll()
        .antMatchers("/api/corporativo/validar-cadastro-onboard")
        .permitAll()
        .antMatchers("/api/corporativo/validar-cadastro-onboard-sem-caf")
        .permitAll()
        .antMatchers("/api/corporativo/cadastrar-portador-login")
        .permitAll()
        .antMatchers("/api/corporativo/redefinir-senha-login")
        .permitAll()
        // Configuração de Segurança da Instituição
        .antMatchers("/api/portador/login/encontra-caf-necessario/instituicao/**")
        .permitAll()

        // Diretrizes
        .antMatchers("/api/diretrizes/**")
        .permitAll()
        // Termos de uso
        .antMatchers("/api/termo-uso/**")
        .permitAll()
        // Paths necessários para o Swagger
        .antMatchers("/v2/api-docs")
        .permitAll()
        .antMatchers("/swagger-resources/**")
        .permitAll()
        .antMatchers("/webjars/**")
        .permitAll()
        .antMatchers("/swagger-ui.html")
        .permitAll()
        .antMatchers("/login")
        .permitAll()
        .antMatchers("/swagger")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/js/login.js")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/js/signup.js")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/js/swagger.js")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/img/swagger.png")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/css/swagger-ui.css")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/css/swagger-ui.css.map")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/img/logo.svg")
        .permitAll()
        .antMatchers("api/echo/**")
        .permitAll()
        .antMatchers(HttpMethod.POST, "api/email/callback/event")
        .permitAll()
        .antMatchers(HttpMethod.POST, "api/email/enviar-email")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/api/health")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/actuator/health")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/actuator/health/**")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/actuator/loggers/")
        .permitAll()
        .antMatchers(HttpMethod.POST, "/actuator/loggers/**")
        .permitAll()
        .antMatchers(HttpMethod.GET, "/actuator/prometheus")
        .permitAll()
        .antMatchers("/api/corporativo/auth")
        .permitAll()
        .antMatchers(HttpMethod.OPTIONS, "/**")
        .permitAll()

        // All other request need to be authenticated
        .anyRequest()
        .authenticated()
        .and()

        // Custom Token based authentication based on the header
        // previously given to the client
        .addFilterBefore(
            new StatelessAuthenticationFilter(
                tokenAuthenticationService,
                tokenAuthenticationPortadorService,
                tokenAuthenticationCorporativoService,
                tokenAuthenticationEstabelecimentoService,
                tokenAuthenticationSwaggerService,
                requestMappingHandlerMapping,
                logChamadaApiService,
                tokenAuthenticationUsuarioUnicoService),
            UsernamePasswordAuthenticationFilter.class);
  }

  @Bean
  public CorsConfigurationSource corsConfigurationSource() {
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.addAllowedOriginPattern("*");
    configuration.addAllowedHeader("*");
    configuration.addAllowedMethod("*");
    configuration.setAllowCredentials(true);
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }

  @Override
  public void configure(WebSecurity web) throws Exception {
    web.ignoring()
        .antMatchers(HttpMethod.OPTIONS, "/**")
        .antMatchers(HttpMethod.POST, "/api/auth/login")
        .antMatchers(HttpMethod.POST, "/api/auth/b2b/login")
        .antMatchers(HttpMethod.POST, "/api/auth/estabelecimento/login")
        .antMatchers(HttpMethod.POST, "/api/auth/estabelecimento/login/app")
        .antMatchers(HttpMethod.POST, "/api/login-unico/auth")
        .antMatchers(HttpMethod.POST, "/api/login-unico/token")
        .antMatchers(HttpMethod.POST, "/api/login-unico/trocar-senha")
        .antMatchers(HttpMethod.GET, "/api/portador/login/verifica-abertura-conta/**")
        .antMatchers(HttpMethod.POST, "/api/portador/login")
        .antMatchers(HttpMethod.POST, "/api/portador/login/criar-login")
        .antMatchers(HttpMethod.POST, "/api/portador/login/pre-cadastro")
        .antMatchers(HttpMethod.POST, "/api/portador/login/auth")
        .antMatchers(HttpMethod.POST, "/api/portador/login/v2/auth")
        .antMatchers(HttpMethod.POST, "/api/portador/login/auth-motiva")
        .antMatchers(HttpMethod.POST, "/api/portador/login/auth-parametrizado")
        .antMatchers(HttpMethod.POST, "/api/portador/login/recuperar-senha")
        .antMatchers(HttpMethod.POST, "/api/portador/login/recuperar-senha/email")
        .antMatchers(HttpMethod.POST, "/api/portador/login/recadastrar-senha")
        .antMatchers(HttpMethod.GET, "/api/portador/login/get-login/**")
        .antMatchers(HttpMethod.GET, "/api/parametro-valor/aplicativo-ativo")
        .antMatchers(HttpMethod.GET, "/api/portador/login/get-conta/infinancas/pontos-inmais/**")
        .antMatchers(HttpMethod.POST, "/api/usuario/esqueci-senha")
        .antMatchers(HttpMethod.GET, "/api/portador/login/get-pre-cadastro/**")
        .antMatchers(HttpMethod.GET, "/api/portador/login/get-login/**")
        .antMatchers(HttpMethod.GET, "/api/pessoa/verifica-tipo-conta/**")
        .antMatchers(HttpMethod.POST, "/api/pessoa/verifica-tipo-credencial/")
        .antMatchers(HttpMethod.POST, "/api/representante-legal/valido/conta")
        .antMatchers(HttpMethod.POST, "/api/representante-legal/valido/credencial")
        .antMatchers(HttpMethod.POST, "/api/representante-legal/valido/documento")
        .antMatchers(HttpMethod.POST, "/api/endereco/criar-pre-cadastro")
        .antMatchers(HttpMethod.POST, "/api/portador/login/criar-login/pre-cadastro")
        .antMatchers(HttpMethod.POST, "/api/usuario/trocar-senha")
        .antMatchers(HttpMethod.POST, "/api/usuario/estabelecimento/trocar-senha")
        .antMatchers(HttpMethod.POST, "/api/usuario/estabelecimento/trocar-propria-senha")
        .antMatchers(HttpMethod.POST, "/api/usuario/trocar-propria-senha")
        .antMatchers(HttpMethod.POST, "/api/sistema/auth/login")
        .antMatchers(HttpMethod.GET, "/api/mktplace/portador/open/**")
        .antMatchers(HttpMethod.GET, "/api/shopping/**")
        .antMatchers(HttpMethod.POST, "/api/shopping/**")
        .antMatchers(HttpMethod.GET, "/api/echo/**")
        .antMatchers(HttpMethod.POST, "/api/email/callback/event")
        .antMatchers(HttpMethod.POST, "/api/email/enviar-email")
        .antMatchers(HttpMethod.POST, "/api/estabelecimento/pre-cadastrar")
        .antMatchers(HttpMethod.GET, "/api/estabelecimento/listar-todos-pre-cadastros")
        .antMatchers(HttpMethod.GET, "/api/contato/buscar-fale-conosco/**")
        .antMatchers(HttpMethod.POST, "/api/estabelecimento/completar-pre-cadastro")
        .antMatchers(HttpMethod.POST, "/api/estabelecimento//editar-pre-cadastro")
        .antMatchers(HttpMethod.POST, "/api/versao-app/verifica-versao")

        // Acesso callback QrCode ELO
        .antMatchers(HttpMethod.POST, "/api/elo/qrcode/callbacks/transactions/**")

        // Acesso callback Banco Rendimento
        .antMatchers(HttpMethod.POST, "/api/gi/**")
        .antMatchers(HttpMethod.GET, "/api/gi/**")
        .antMatchers(HttpMethod.POST, "/api/bank10/gi/**")
        .antMatchers(HttpMethod.GET, "/api/bank10/gi/**")

        // Acesso callback Zenvia
        .antMatchers(HttpMethod.POST, "/api/zenvia/webhook/**")
        .antMatchers(HttpMethod.GET, "/api/zenvia/webhook/**")

        // cadastro de portadores via cardholder ou aplicativo
        .antMatchers(HttpMethod.GET, "/api/logradouro/card-holder/**")
        .antMatchers(HttpMethod.POST, "/api/CredencialPreEmitida/card-holder/**")
        // nao precisar estar logado em loyalty
        .antMatchers(HttpMethod.GET, "/api/loyalty/dados-adicionais/**")
        .antMatchers(HttpMethod.GET, "/api/loyalty/dados-principais/status/**")
        .antMatchers(HttpMethod.GET, "/api/loyalty/voucher/**")
        .antMatchers(HttpMethod.POST, "/api/loyalty/voucher/**")
        .antMatchers(HttpMethod.GET, "/api/loyalty/instituicao/**")
        .antMatchers(HttpMethod.GET, "/api/loyalty/rotator/**")
        .antMatchers(HttpMethod.POST, "/api/portador/brbcard/**")
        .antMatchers(HttpMethod.GET, "/api/portador/brbcard/**")
        .antMatchers(HttpMethod.POST, "/api/portador/credencial/corporativo")
        .antMatchers(HttpMethod.POST, "/api/portador/conta/criar-conta-completa")
        .antMatchers(HttpMethod.GET, "/api/portador/conta/buscar-portador/**")
        .antMatchers(HttpMethod.GET, "/api/antifraude/verificar/**")
        .antMatchers(HttpMethod.POST, "/api/antifraude/validar-ocr")
        .antMatchers(HttpMethod.PUT, "/api/antifraude/standby/**")
        .antMatchers(HttpMethod.GET, "/api/antifraude/detail/**")
        .antMatchers(HttpMethod.POST, "/api/antifraude/validar-ocr/caf/**")
        .antMatchers(HttpMethod.POST, "/api/antifraude/validar-ocr-pj/caf/**")
        .antMatchers(HttpMethod.POST, "/api/antifraude/get-status/caf/**")
        .antMatchers(HttpMethod.POST, "/api/antifraude/webhook-status/caf")
        .antMatchers(HttpMethod.POST, "/api/antifraude/caf/registrar-validacao-facial/**")
        .antMatchers(HttpMethod.POST, "/api/log-cadastro-portador-web-mobile/**")
        .antMatchers(HttpMethod.GET, "/api/produtoinstituicao/buscar/pessoa-fisica/**")
        .antMatchers(HttpMethod.GET, "/api/produtoinstituicao/buscar/pessoa-juridica/**")
        .antMatchers(HttpMethod.GET, "/api/pontorelacionamento/instituicao/**")
        .antMatchers(HttpMethod.POST, "/api/proposta/create/proposta/pj")
        .antMatchers(HttpMethod.POST, "/api/proposta/v2/create/proposta/pj")
        .antMatchers(HttpMethod.POST, "/api/token-acesso/validar/**")
        .antMatchers(HttpMethod.POST, "/api/token-acesso/enviar/token-cadastro-login")
        .antMatchers(HttpMethod.POST, "/api/token-acesso/enviar/token-login-acesso-por-usuario")
        .antMatchers(HttpMethod.POST, "/api/token-acesso/enviar/token-login-acesso-por-usuario-b2b")
        .antMatchers(HttpMethod.PUT, "/api/token-acesso/usar/**")
        .antMatchers(HttpMethod.POST, "/api/token-redefinicao-senha/enviar/token-redefinir-senha")
        .antMatchers(
            HttpMethod.POST, "/api/token-redefinicao-senha/v2/enviar/token-redefinir-senha")
        .antMatchers(HttpMethod.PUT, "/api/token-redefinicao-senha/usar")
        .antMatchers(HttpMethod.GET, "/api/mktplace2/listaProdutosCampanha")
        .antMatchers(HttpMethod.GET, "/api/mktplace2/listaCategoriasCampanha")
        .antMatchers(HttpMethod.PUT, "/api/portador/login/redefinir-senha")
        .antMatchers(HttpMethod.PUT, "/api/portador/login/v2/redefinir-senha")
        .antMatchers(HttpMethod.PUT, "/api/portador/login/recuperar-senha")
        .antMatchers(HttpMethod.POST, "/api/portador/login/validar-cadastro-onboard")
        .antMatchers(HttpMethod.POST, "/api/portador/login/validar-cadastro-onboard-sem-caf")
        .antMatchers(HttpMethod.POST, "/api/portador/login/validar-cadastro-corporativo")
        .antMatchers(HttpMethod.GET, "/api/portador/login/encontra-caf-necessario/instituicao/**")
        .antMatchers(HttpMethod.GET, "/api/portador/login/descobre-tipo-login/**")
        .antMatchers(HttpMethod.GET, "/api/diretrizes/**")
        .antMatchers(HttpMethod.GET, "/api/termo-uso/**")
        .antMatchers(HttpMethod.GET, "/v2/api-docs")
        .antMatchers(HttpMethod.GET, "/swagger-resources/**")
        .antMatchers(HttpMethod.GET, "/webjars/**")
        .antMatchers(HttpMethod.GET, "/swagger-ui.html")
        .antMatchers(HttpMethod.GET, "/login")
        .antMatchers(HttpMethod.POST, "/login")
        .antMatchers(HttpMethod.POST, "/signup")
        .antMatchers(HttpMethod.GET, "/signup")
        .antMatchers(HttpMethod.GET, "/swagger")
        .antMatchers(HttpMethod.GET, "/js/login.js")
        .antMatchers(HttpMethod.GET, "/js/jwt-decode.min.js")
        .antMatchers(HttpMethod.GET, "/js/signup.js")
        .antMatchers(HttpMethod.GET, "/js/swagger.js")
        .antMatchers(HttpMethod.GET, "/css/swagger-ui.css")
        .antMatchers(HttpMethod.GET, "/css/swagger-ui.css.map")
        .antMatchers(HttpMethod.GET, "/img/swagger.png")
        .antMatchers(HttpMethod.GET, "/img/logo.svg")
        .antMatchers(HttpMethod.GET, "/api/health")
        .antMatchers(HttpMethod.GET, "/actuator/health")
        .antMatchers(HttpMethod.GET, "/actuator/loggers")
        .antMatchers(HttpMethod.GET, "/actuator/health/**")
        .antMatchers(HttpMethod.POST, "/actuator/loggers/**")
        .antMatchers(HttpMethod.GET, "/actuator/prometheus")
        .antMatchers(HttpMethod.POST, "/api/corporativo/auth")
        .antMatchers(HttpMethod.OPTIONS, "/**")
        .antMatchers(HttpMethod.POST, "/api/corporativo/validar-cadastro-onboard")
        .antMatchers(HttpMethod.POST, "/api/corporativo/cadastrar-portador-login")
        .antMatchers(HttpMethod.PUT, "/api/corporativo/redefinir-senha-login");
  }

  @Override
  protected void configure(AuthenticationManagerBuilder auth) throws Exception {
    auth.userDetailsService(userService).passwordEncoder(new BCryptPasswordEncoder());
  }

  @Bean
  @Override
  public AuthenticationManager authenticationManagerBean() throws Exception {
    return super.authenticationManagerBean();
  }

  @Bean
  @Override
  public UserService userDetailsService() {
    return userService;
  }
}
