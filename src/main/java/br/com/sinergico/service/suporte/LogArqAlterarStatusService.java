package br.com.sinergico.service.suporte;

import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.suporte.LogArqAlterarStatus;
import br.com.entity.suporte.LogRegistroAlterarStatus;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.suporte.AlterarStatusCredencialTO;
import br.com.json.bean.suporte.AlterarStatusTO;
import br.com.json.bean.suporte.VerificacaoArqAlterarStatusCredencialTO;
import br.com.json.bean.suporte.VerificacaoArqAlterarStatusTO;
import br.com.sinergico.enums.StatusArquivoAlterarEnum;
import br.com.sinergico.enums.StatusRegistroAlterarEnum;
import br.com.sinergico.enums.VerificacaoArquivoAlterarStatusEnum;
import br.com.sinergico.repository.suporte.LogArqAlterarStatusRepository;
import br.com.sinergico.repository.suporte.LogRegistroAlterarStatusRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.util.FileUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.Normalizer;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class LogArqAlterarStatusService extends GenericService<LogArqAlterarStatus, Long> {
  private static final String VAZIO = "<<NÃO INFORMADO>>";
  private Boolean erroRegistro = Boolean.FALSE;
  @Autowired private AlterarStatusXls alterarStatusXls;

  private ContaPagamentoService contaPagamentoService;

  private LogArqAlterarStatusRepository repository;

  @Autowired private LogRegistroAlterarStatusRepository registroRepository;

  @Value("${issuer.dir.alterarstatus}")
  private String issuerDirAlterarStatus;

  @Autowired
  public LogArqAlterarStatusService(LogArqAlterarStatusRepository repo) {
    super(repo);
    repository = repo;
  }

  public Long contaTodos() {
    return this.repository.count();
  }

  public Long contaTodosPorInstituicao(Integer idInstituicao) {
    return this.repository.countByIdInstituicao(idInstituicao);
  }

  public List<LogArqAlterarStatus> listarProcessamentoArquivoAlterarStatusPorInstituicao(
      Integer idInstituicao, Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max, Sort.Direction.DESC, "idLogArqAlterarStatus");
    return repository.findByIdInstituicao(idInstituicao, top);
  }

  public List<LogArqAlterarStatus> listarProcessamentoArquivoAlterarStatusSemInstituicao(
      Integer first, Integer max) {
    Pageable top = PageRequest.of(first, max, Sort.Direction.DESC, "idLogArqAlterarStatus");
    return repository.findAll(top).getContent();
  }

  public Boolean cancelarArquivo(Long idArq) {
    LogArqAlterarStatus arq = repository.findById(idArq).orElse(null);
    if (arq != null && arq.getStatusArq() == StatusArquivoAlterarEnum.PENDENTE) {
      arq.setStatusArq(StatusArquivoAlterarEnum.CANCELADO);
      arq.setDataHoraProcessamento(LocalDateTime.now());
      save(arq);
    } else {
      throw new GenericServiceException(
          "Não foi possível cancelar o arquivo, pois já foi processado");
    }

    return true;
  }

  public List<LogRegistroAlterarStatus> listarRegistrosRejeitados(Long idArq) {
    return this.registroRepository.findByLogArqAlterarStatus_IdLogArqAlterarStatusAndStatusRegistro(
        idArq, StatusRegistroAlterarEnum.REJEITADO);
  }

  public ResponseEntity<Map<String, Object>> uploadArquivoAlterarStatus(
      MultipartFile file,
      SecurityUser usuarioInclusao,
      LogArqAlterarStatus.TipoArquivoAlterarEnum tipoArq,
      Integer idInstituicao,
      Integer qtdRegistros)
      throws IOException, NoSuchAlgorithmException {
    Map<String, Object> map = new HashMap<>();

    int posicao = file.getOriginalFilename().lastIndexOf('.');
    String extensao = file.getOriginalFilename().substring(posicao);

    if (extensao.equals(".xls") || extensao.equals(".xlsx")) {

      LogArqAlterarStatus log = new LogArqAlterarStatus();

      log.setStatusArq(StatusArquivoAlterarEnum.PENDENTE);
      log.setIdUsuarioInclusao(usuarioInclusao.getIdUsuario());
      log.setDataHoraInclusao(LocalDateTime.now());
      log.setIdInstituicao(idInstituicao);
      log.setTipoArq(tipoArq);
      log.setQtdAprovados(0);
      log.setQtdRejeitados(0);
      log.setQtdTotal(qtdRegistros);
      log.setHashArq(gerarHashArquivo(convertToFile(file)));
      String nomeOriginalSemExtensao =
          file.getOriginalFilename().substring(0, file.getOriginalFilename().lastIndexOf('.'));
      String nomeArquivoServidor = nomeOriginalSemExtensao.replaceAll("\\s", "_");
      nomeArquivoServidor = Normalizer.normalize(nomeArquivoServidor, Normalizer.Form.NFD);
      nomeArquivoServidor = nomeArquivoServidor.replaceAll("\\P{InBasic_Latin}", "");
      nomeArquivoServidor =
          nomeArquivoServidor.replaceAll("[^A-Za-z0-9._-]", "") + LocalDateTime.now();
      nomeArquivoServidor = nomeArquivoServidor + extensao;
      log.setNomeArq(nomeArquivoServidor);

      // definir diretorio
      String pasta = issuerDirAlterarStatus;

      if (pasta == null) {
        throw new GenericServiceException("O diretório para salvar o arquivo não foi encontrado!");
      }

      try {
        InputStream is = file.getInputStream();
        FileOutputStream fout = new FileOutputStream(pasta + nomeArquivoServidor);
        while (is.available() != 0) {
          fout.write(is.read());
        }
        is.close();
        fout.close();
      } catch (Exception e) {
        throw new GenericServiceException(
            "Não foi possível gravar o arquivo no caminho indicado: " + pasta + nomeArquivoServidor,
            e);
      }

      // Salvar log
      repository.save(log);

      map.put("msg", "O arquivo foi incluído com sucesso.");
      return new ResponseEntity<>(map, HttpStatus.OK);

    } else {
      throw new GenericServiceException(
          "O arquivo com o formato "
              + extensao
              + " não é suportado. Faça upload apenas de arquivos com formato .xls ou .xlsx");
    }
  }

  public File convertToFile(MultipartFile file) throws IOException {
    Path path = Paths.get(System.getProperty("java.io.tmpdir"), file.getOriginalFilename());
    File convFile = path.toFile();
    convFile.createNewFile();
    FileOutputStream fos = new FileOutputStream(convFile);
    fos.write(file.getBytes());
    fos.close();
    return convFile;
  }

  private String gerarHashArquivo(File file)
      throws FileNotFoundException, NoSuchAlgorithmException {

    MessageDigest digest = MessageDigest.getInstance("SHA-512");
    InputStream is = new FileInputStream(file);
    byte[] buffer = new byte[8192];
    int read = 0;

    try {
      while ((read = is.read(buffer)) > 0) {
        digest.update(buffer, 0, read);
      }
      byte[] md5sum = digest.digest();
      BigInteger bigInt = new BigInteger(1, md5sum);
      String output = bigInt.toString(16);

      return output;
    } catch (IOException e) {
      throw new RuntimeException("Unable to process file for SHA-512", e);
    } finally {
      try {
        is.close();
      } catch (IOException e) {
        throw new RuntimeException("Unable to close input stream for SHA-512 calculation", e);
      }
    }
  }

  public VerificacaoArqAlterarStatusTO verificaAlterarStatusViaArquivo(
      InputStream is, String nomeArquivo, SecurityUser user) throws FileNotFoundException {
    List<AlterarStatusTO> to = null;
    Map<String, VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO> verificacoes = null;
    File copy = FileUtil.inputStreamToFile(is);
    to = alterarStatusXls.getImportacaoStatus(new FileInputStream(copy));
    VerificacaoArqAlterarStatusTO verificaoTO = new VerificacaoArqAlterarStatusTO(nomeArquivo, to);
    verificaArquivoValoresRepetidosAlterarStatus(verificaoTO, to);
    verificacoes = getVerificacaoAlterarStatus(to, user);
    verificaoTO.loadVerificacoesArqAlterarStatus(verificacoes);
    return sortVerificacoes(verificaoTO);
  }

  public VerificacaoArqAlterarStatusCredencialTO verificaAlterarStatusViaArquivoCredencial(
      InputStream is, String nomeArquivo, SecurityUser user) throws FileNotFoundException {
    List<AlterarStatusCredencialTO> to = null;
    Map<String, VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO> verificacoes = null;
    File copy = FileUtil.inputStreamToFile(is);
    to = alterarStatusXls.getImportacaoStatusCredencial(new FileInputStream(copy));
    VerificacaoArqAlterarStatusCredencialTO verificaoTO =
        new VerificacaoArqAlterarStatusCredencialTO(nomeArquivo, to);
    verificaArquivoValoresRepetidosAlterarStatusCredencial(verificaoTO, to);
    verificacoes = getVerificacaoAlterarStatusCredencial(to, user);
    verificaoTO.loadVerificacoesArqAlterarStatusCredencial(verificacoes);
    return sortVerificacoesCredencial(verificaoTO);
  }

  private VerificacaoArqAlterarStatusTO sortVerificacoes(
      VerificacaoArqAlterarStatusTO verificaoTO) {
    Collections.sort(
        verificaoTO.getVerificacoesArq(),
        new Comparator<String>() {
          /** Comparação dos primeiros caracteres entre aspa simples dentro de uma string */
          @Override
          public int compare(String a, String b) {
            String firstA = a.substring(a.indexOf("'") + 1, a.length() - 1);
            Integer indexA = firstA.indexOf("'") + 1;
            String firstB = b.substring(b.indexOf("'") + 1, b.length() - 1);
            Integer indexB = firstB.indexOf("'") + 1;
            return Integer.valueOf(a.substring(a.indexOf("'") + 1, a.indexOf("'") + indexA))
                    < Integer.valueOf(b.substring(b.indexOf("'") + 1, b.indexOf("'") + indexB))
                ? -1
                : 1;
          }
        });
    return verificaoTO;
  }

  private VerificacaoArqAlterarStatusCredencialTO sortVerificacoesCredencial(
      VerificacaoArqAlterarStatusCredencialTO verificaoTO) {
    Collections.sort(
        verificaoTO.getVerificacoesArq(),
        new Comparator<String>() {
          /** Comparação dos primeiros caracteres entre aspa simples dentro de uma string */
          @Override
          public int compare(String a, String b) {
            String firstA = a.substring(a.indexOf("'") + 1, a.length() - 1);
            Integer indexA = firstA.indexOf("'") + 1;
            String firstB = b.substring(b.indexOf("'") + 1, b.length() - 1);
            Integer indexB = firstB.indexOf("'") + 1;
            return Integer.valueOf(a.substring(a.indexOf("'") + 1, a.indexOf("'") + indexA))
                    < Integer.valueOf(b.substring(b.indexOf("'") + 1, b.indexOf("'") + indexB))
                ? -1
                : 1;
          }
        });
    return verificaoTO;
  }

  public Map<String, VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO> getVerificacaoAlterarStatus(
      List<AlterarStatusTO> tos, SecurityUser user) {

    Map<String, VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO> verificacoes = new HashMap<>();
    Map<String, VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO> tempVerificacoes = null;
    AlterarStatusTO to = null;

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      tempVerificacoes = getVerificacaoArqAlterarStatus(to, user, i);

      if (tempVerificacoes != null && !tempVerificacoes.isEmpty()) {
        verificacoes.putAll(tempVerificacoes);
      }
    }
    return verificacoes;
  }

  public Map<String, VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO>
      getVerificacaoAlterarStatusCredencial(
          List<AlterarStatusCredencialTO> tos, SecurityUser user) {

    Map<String, VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO> verificacoes =
        new HashMap<>();
    Map<String, VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO> tempVerificacoes = null;
    AlterarStatusCredencialTO to = null;

    for (int i = 0; i < tos.size(); i++) {
      to = tos.get(i);

      tempVerificacoes = getVerificacaoArqAlterarStatusCredencial(to, user, i);

      if (tempVerificacoes != null && !tempVerificacoes.isEmpty()) {
        verificacoes.putAll(tempVerificacoes);
      }
    }
    return verificacoes;
  }

  public List<String> verificaArquivoValoresRepetidosAlterarStatus(
      VerificacaoArqAlterarStatusTO verificaoTO, List<AlterarStatusTO> to) {

    AlterarStatusTO it = null;
    for (int j = 0; j < to.size(); j++) {
      it = to.get(j);
      it.setIdControl(j);
    }
    List<String> lista1;
    lista1 = verificaArquivoIdContaRepetido(to);
    verificaoTO.setListaValorRepetido(lista1);
    return verificaoTO.getListaValorRepetido();
  }

  public List<String> verificaArquivoValoresRepetidosAlterarStatusCredencial(
      VerificacaoArqAlterarStatusCredencialTO verificaoTO, List<AlterarStatusCredencialTO> to) {

    AlterarStatusCredencialTO it = null;
    for (int j = 0; j < to.size(); j++) {
      it = to.get(j);
      it.setIdControl(j);
    }
    List<String> lista1;
    lista1 = verificaArquivoCredencialRepetido(to);
    verificaoTO.setListaValorRepetido(lista1);
    return verificaoTO.getListaValorRepetido();
  }

  public List<String> verificaArquivoCredencialRepetido(List<AlterarStatusCredencialTO> to) {
    List<String> novoArray = new ArrayList<String>();
    for (AlterarStatusCredencialTO tmp : to) {
      for (AlterarStatusCredencialTO chk : to) {
        if (!tmp.getCredencial().toString().isEmpty() && tmp.getCredencial() != null) {
          if (!tmp.getIdControl().equals(chk.getIdControl())
              && tmp.getCredencial().equals(chk.getCredencial())
              && tmp.getIsDuplicatedRegister().equals(false)
              && chk.getIsDuplicatedRegister().equals(false)) {
            novoArray.add("O Credencial " + chk.getCredencial() + ", está duplicado .");
            chk.setIsCredencialDuplicated(true);
          }
        }
      }
    }
    return novoArray;
  }

  public List<String> verificaArquivoIdContaRepetido(List<AlterarStatusTO> to) {
    List<String> novoArray = new ArrayList<String>();
    for (AlterarStatusTO tmp : to) {
      for (AlterarStatusTO chk : to) {
        if (!tmp.getIdConta().toString().isEmpty() && tmp.getIdConta() != null) {
          if (!tmp.getIdControl().equals(chk.getIdControl())
              && tmp.getIdConta().equals(chk.getIdConta())
              && tmp.getIsDuplicatedRegister().equals(false)
              && chk.getIsDuplicatedRegister().equals(false)) {
            novoArray.add("O IdConta " + chk.getIdConta() + ", está duplicado .");
            chk.setIsIdContaDuplicated(true);
          }
        }
      }
    }
    return novoArray;
  }

  public Map<String, VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO>
      getVerificacaoArqAlterarStatus(AlterarStatusTO to, SecurityUser user, Integer linha) {

    Map<String, VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO> verificacoes = new HashMap<>();

    erroRegistro = false;

    // Verificando se o IdConta foi informado
    if (to.getIdConta() == null || to.getIdConta().toString().isEmpty()) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.IDCONTA.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO(0, linha, VAZIO));
      erroRegistro = true;
    }
    if (to.getStatus() == null || to.getStatus().toString().isEmpty()) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.STATUS.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO(0, linha, VAZIO));
      erroRegistro = true;
    }

    ContaPagamento conta = new ContaPagamento();
    if (to.getIdConta() == null) {
      conta = contaPagamentoService.findByIdConta(to.getIdConta());
    }

    // Verificando a existência de uma conta
    if (conta == null && conta.getIdConta() == null) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.IDCONTA.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO(
              0, linha, to.getIdConta().toString()));
      erroRegistro = true;
    }

    if (to.getIsIdContaDuplicated()) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.ERRO.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO(
              1, linha, to.getIdConta().toString()));
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.IDCONTA.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO(
              0, linha, "<<ARQUIVO COM IDCONTA DUPLICADO: " + to.getIdConta() + ">>"));
    }

    if (erroRegistro) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.ERRO.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusTO.VerificacaoLinhaTO(
              1, linha, to.getIdConta().toString()));
    }
    return verificacoes;
  }

  public Map<String, VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO>
      getVerificacaoArqAlterarStatusCredencial(
          AlterarStatusCredencialTO to, SecurityUser user, Integer linha) {

    Map<String, VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO> verificacoes =
        new HashMap<>();

    erroRegistro = false;

    // Verificando se o IdCredencial foi informado
    if (to.getCredencial() == null || to.getCredencial().toString().isEmpty()) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.IDCONTA.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO(0, linha, VAZIO));
      erroRegistro = true;
    }
    if (to.getStatus() == null || to.getStatus().toString().isEmpty()) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.STATUS.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO(0, linha, VAZIO));
      erroRegistro = true;
    }

    if (to.getIsCredencialDuplicated()) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.ERRO.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO(
              1, linha, to.getCredencial().toString()));
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.IDCRENDENCIAL.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO(
              0, linha, "<<ARQUIVO COM IDCONTA DUPLICADO: " + to.getCredencial() + ">>"));
    }

    if (erroRegistro) {
      verificacoes.put(
          VerificacaoArquivoAlterarStatusEnum.ERRO.toString() + ":" + linha,
          new VerificacaoArqAlterarStatusCredencialTO.VerificacaoLinhaTO(
              1, linha, to.getCredencial().toString()));
    }
    return verificacoes;
  }
}
