package br.com.sinergico;

import br.com.sinergico.controller.AuditInterceptor;
import br.com.sinergico.security.CorporativoLoginLogAcessoAuditInterceptor;
import br.com.sinergico.security.PortadorLogAcessoAuditInterceptor;
import br.com.sinergico.util.Constantes;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.util.List;
import javax.net.ssl.SSLContext;
import javax.servlet.MultipartConfigElement;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.servlet.MultipartConfigFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.unit.DataSize;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {

  @Override
  public void addCorsMappings(CorsRegistry registry) {
    registry
        .addMapping("/api/*")
        .allowedOriginPatterns("*")
        .allowedMethods("GET", "HEAD", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "TRACE")
        .allowedHeaders(
            "Content-Type, Accept, X-Requested-With, X-PINGOTHER, content-type, Authorization, authorization, AuthorizationPortador, authorizationportador, authorizationcorporativo, AuthorizationCorporativo, AuthorizationEstabelecimento, device-id, Device-Id")
        .allowCredentials(true);
  }

  @Override
  public void extendMessageConverters(List<HttpMessageConverter<?>> converters) {
    final MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter =
        new MappingJackson2HttpMessageConverter(objectMapper());
    converters.replaceAll(
        httpMessageConverter ->
            (httpMessageConverter instanceof MappingJackson2HttpMessageConverter)
                ? mappingJackson2HttpMessageConverter
                : httpMessageConverter);
  }

  @Bean("restTemplate")
  public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {

    RequestConfig requestConfig = getRequestConfigForOtherRestIntegration();
    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
    HttpComponentsClientHttpRequestFactory factory =
        getHttpComponentsClientHttpRequestFactory(requestConfig, connectionManager);
    return restTemplateBuilder
        .requestFactory(() -> factory) // Definindo o HttpClient customizado
        .setConnectTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO))
        .setReadTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO))
        .build();
  }

  @Bean("restTemplateWithMappingConverter")
  public RestTemplate restTemplateWithMappingConverter(RestTemplateBuilder restTemplateBuilder) {

    RequestConfig requestConfig = getRequestConfigForOtherRestIntegration();
    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();
    HttpComponentsClientHttpRequestFactory factory =
        getHttpComponentsClientHttpRequestFactory(requestConfig, connectionManager);

    return restTemplateBuilder
        .requestFactory(() -> factory) // Definindo o HttpClient customizado
        .setConnectTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO))
        .setReadTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO))
        .additionalMessageConverters(new MappingJackson2HttpMessageConverter())
        .additionalMessageConverters(new StringHttpMessageConverter(StandardCharsets.UTF_8))
        .build();
  }

  @Bean("restTemplateUntrustedCerts")
  public RestTemplate restTemplateUntrustedCerts(RestTemplateBuilder restTemplateBuilder)
      throws NoSuchAlgorithmException, KeyStoreException, KeyManagementException {
    TrustStrategy acceptingTrustStrategy = (cert, authType) -> true;
    SSLContext sslContext =
        new SSLContextBuilder().loadTrustMaterial(null, acceptingTrustStrategy).build();

    RequestConfig requestConfig = getRequestConfigForOtherRestIntegration();

    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

    HttpComponentsClientHttpRequestFactory factory =
        getHttpComponentsClientHttpRequestFactoryWithSslContext(
            requestConfig, connectionManager, sslContext);

    return restTemplateBuilder
        .requestFactory(() -> factory) // Definindo o HttpClient customizado
        .setConnectTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO))
        .setReadTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO))
        .build();
  }

  @Bean("restTemplateVoxage")
  public RestTemplate restTemplateVoxage(RestTemplateBuilder restTemplateBuilder) {
    RequestConfig requestConfig = getRequestConfigForOtherRestIntegrationVoxage();

    PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager();

    HttpComponentsClientHttpRequestFactory factory =
        getHttpComponentsClientHttpRequestFactory(requestConfig, connectionManager);

    return restTemplateBuilder
        .requestFactory(() -> factory) // Definindo o HttpClient customizado
        .setConnectTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_INTEGRACAO_VOXAGE))
        .setReadTimeout(
            Duration.ofMillis(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_INTEGRACAO_VOXAGE))
        .build();
  }

  private HttpComponentsClientHttpRequestFactory getHttpComponentsClientHttpRequestFactory(
      RequestConfig requestConfig, PoolingHttpClientConnectionManager connectionManager) {
    HttpClient httpClient =
        HttpClients.custom()
            .setDefaultRequestConfig(requestConfig)
            .setConnectionManager(connectionManager)
            .build();
    return new HttpComponentsClientHttpRequestFactory(httpClient);
  }

  private HttpComponentsClientHttpRequestFactory
      getHttpComponentsClientHttpRequestFactoryWithSslContext(
          RequestConfig requestConfig,
          PoolingHttpClientConnectionManager connectionManager,
          SSLContext sslContext) {
    HttpClient httpClient =
        HttpClients.custom()
            .setDefaultRequestConfig(requestConfig)
            .setSSLContext(sslContext)
            .setConnectionManager(connectionManager)
            .build();
    return new HttpComponentsClientHttpRequestFactory(httpClient);
  }

  private static RequestConfig getRequestConfigForOtherRestIntegrationVoxage() {
    return RequestConfig.custom()
        .setConnectTimeout(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_INTEGRACAO_VOXAGE)
        .setSocketTimeout(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_INTEGRACAO_VOXAGE)
        .setConnectionRequestTimeout(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_INTEGRACAO_VOXAGE)
        .build();
  }

  private static RequestConfig getRequestConfigForOtherRestIntegration() {
    return RequestConfig.custom()
        .setConnectTimeout(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO)
        .setSocketTimeout(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO)
        .setConnectionRequestTimeout(Constantes.CONNECTION_TIMEOUT_REST_TEMPLATE_DEMAIS_INTEGRACAO)
        .build();
  }

  @Bean
  public ObjectMapper objectMapper() {

    Jackson2ObjectMapperBuilder builder = new Jackson2ObjectMapperBuilder();

    builder.serializationInclusion(Include.NON_NULL);
    builder.featuresToDisable(
        SerializationFeature.FAIL_ON_EMPTY_BEANS,
        DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES,
        DeserializationFeature.FAIL_ON_INVALID_SUBTYPE,
        SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
        DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

    builder.featuresToEnable(
        DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY,
        DeserializationFeature.USE_BIG_INTEGER_FOR_INTS,
        JsonParser.Feature.ALLOW_SINGLE_QUOTES,
        JsonParser.Feature.ALLOW_NUMERIC_LEADING_ZEROS,
        JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES);
    builder.modulesToInstall(new JavaTimeModule());
    return builder.build();
  }

  @Bean
  public AuditInterceptor auditInterceptor() {
    return new AuditInterceptor(); // let Spring go nuts injecting stuff
  }

  @Bean
  public PortadorLogAcessoAuditInterceptor portadorLogAcessoAuditInterceptor() {
    return new PortadorLogAcessoAuditInterceptor();
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {

    registry.addInterceptor(auditInterceptor());
    registry
        .addInterceptor(portadorLogAcessoAuditInterceptor())
        .addPathPatterns(
            "/api/portador/login/auth",
            "/api/portador/login/v2/auth",
            "/api/portador/login/auth-parametrizado",
            "/api/portador/login/auth-motiva");
    registry
        .addInterceptor(corporativoLoginLogAcessoAuditInterceptor())
        .addPathPatterns("/api/corporativo/auth");
  }

  @Bean
  public MultipartConfigElement multipartConfigElement() {

    MultipartConfigFactory factory = new MultipartConfigFactory();

    factory.setMaxFileSize(DataSize.ofMegabytes(10));

    factory.setMaxRequestSize(DataSize.ofMegabytes(10));

    return factory.createMultipartConfig();
  }

  @Bean
  public CorporativoLoginLogAcessoAuditInterceptor corporativoLoginLogAcessoAuditInterceptor() {
    return new CorporativoLoginLogAcessoAuditInterceptor();
  }
}
