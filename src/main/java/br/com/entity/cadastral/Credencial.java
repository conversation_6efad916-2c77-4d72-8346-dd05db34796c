package br.com.entity.cadastral;

import br.com.entity.suporte.Plastico;
import br.com.entity.suporte.TipoStatus;
import br.com.sinergico.annotation.NotUpperCase;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.Type;
import org.slf4j.LoggerFactory;

@Entity
@DynamicUpdate
@Table(name = "credencial", schema = "cadastral")
@ApiModel(value = "Credencial")
@SequenceGenerator(
    name = "seq_id_credencial_generator",
    sequenceName = "cadastral.seq_id_credencial",
    initialValue = 1,
    allocationSize = 1)
public class Credencial implements Serializable {

  private static final long serialVersionUID = 1L;

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "seq_id_credencial_generator")
  @Column(name = "id_credencial", nullable = false)
  private Long idCredencial;

  @Column(name = "token_interno", nullable = false)
  private String tokenInterno;

  @Column(name = "dt_hr_inclusao", nullable = false)
  private LocalDateTime dataHoraInclusao;

  @Column(name = "dt_validade", nullable = false)
  private LocalDateTime dataValidade;

  private Integer status;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(
      name = "status",
      referencedColumnName = "id_status",
      insertable = false,
      updatable = false)
  private TipoStatus tipoStatus;

  @Column(name = "id_status_v2")
  private Integer idStatusV2;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(
      name = "id_status_v2",
      referencedColumnName = "id_status",
      insertable = false,
      updatable = false)
  private TipoStatus tipoStatusV2;

  @Column(name = "dt_hr_status", nullable = false)
  private LocalDateTime dataHoraStatus;

  @Column(name = "id_usuario_inclusao", nullable = false)
  private Integer idUsuarioInclusao;

  @Column(name = "id_usuario_manutencao", nullable = false)
  private Integer idUsuarioManutencao;

  @Column(name = "id_conta", nullable = false)
  private Long idConta;

  @Column(name = "id_conta_pagamento", nullable = false)
  private String idContaPagamento;

  @Column(name = "id_pessoa", nullable = false)
  private Long idPessoa;

  @JsonIgnore
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(
      name = "id_pessoa",
      referencedColumnName = "id_pessoa",
      insertable = false,
      updatable = false)
  private Pessoa pessoa;

  @Column(name = "dt_hr_emitido")
  private LocalDateTime dataHoraEmitido;

  @Column(name = "nome_impresso", nullable = false)
  private String nomeImpresso;

  @Column(name = "id_lote_emissao", nullable = false)
  private Integer idLoteEmissao;

  @Column(name = "ultimos_4_dig", nullable = false)
  private Integer ultimos4Digitos;

  @Column(name = "bin_6", nullable = false)
  private Integer bin6;

  @Column(name = "bin_length", nullable = false)
  private Integer binLength;

  @Column(name = "bin_estendido", nullable = false, precision = 10)
  private Long binEstendido;

  @Column(nullable = false)
  @Type(type = "numeric_boolean")
  private Boolean chip;

  @Column(name = "id_plastico", nullable = false)
  private Integer idPlastico;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(
      name = "id_plastico",
      updatable = false,
      insertable = false,
      referencedColumnName = "id_plastico")
  @JsonIgnore
  private Plastico plastico;

  @Column(nullable = false)
  @Type(type = "numeric_boolean")
  private Boolean virtual;

  @JsonIgnore @NotUpperCase private String pin;

  @Column(name = "apelido_virtual")
  private String apelidoVirtual;

  @Column(name = "habilita_exterior")
  @Type(type = "numeric_boolean")
  private Boolean habilitaExterior;

  @Column(name = "habilita_ecommerce")
  @Type(type = "numeric_boolean")
  private Boolean habilitaEcommerce;

  @Column(name = "habilita_saque")
  @Type(type = "numeric_boolean")
  private Boolean habilitaSaque;

  @Column(name = "habilita_uso_pessoa")
  @Type(type = "numeric_boolean")
  private Boolean habilitaUsoPessoa;

  @Column(name = "habilita_notificacao_transacao")
  @Type(type = "numeric_boolean")
  private Boolean habilitaNotificacaoTransacao;

  private Integer csn;
  private Integer titularidade;

  @Column(name = "motivo_emissao")
  private Integer motivoEmissao;

  @Column(name = "id_credencial_externa")
  private String idCredencialExterna;

  @Column(name = "credencial_externa_reduzida")
  private String credencialExternaReduzida;

  @Column(name = "valor_transacao_maximo")
  private BigDecimal valorTransacaoMaximo;

  @Column(name = "dt_hr_ultima_utilizacao")
  private LocalDateTime dataHoraUltimaUtilizacao;

  @Column(name = "dt_bloqueio_resgate_pontos")
  private LocalDateTime dtBloqueioResgatePontos;

  @Column(name = "dt_hr_liberaca_emissao")
  private LocalDateTime dtHrLiberacaoEmissao;

  @Column(name = "qtd_tentativas_resgate_senha")
  private Integer qtdTentativasResgateSenha = 0;

  @Column(name = "dt_hr_alteracao_senha_resgate")
  private LocalDateTime dtHrAlteracaoSenhaResgate;

  @Column(name = "multi_conta")
  @Type(type = "numeric_boolean")
  private Boolean multiConta;

  @Column(name = "status_nfc")
  private Integer statusNfc;

  @Column(name = "dt_hr_status_nfc")
  private LocalDateTime dtHrStatusNfc;

  @Column(name = "id_bandeira", updatable = false, nullable = false)
  private Integer idBandeira;

  @ManyToOne
  @JoinColumn(
      name = "id_bandeira",
      referencedColumnName = "id",
      insertable = false,
      updatable = false)
  private Bandeira bandeira;

  @Column(name = "service_code", updatable = false, nullable = false)
  private String serviceCode;

  @Column(name = "id_arranjo")
  private Integer idArranjo;

  @ManyToOne
  @JoinColumn(name = "id_arranjo", insertable = false, updatable = false)
  private ArranjoPagamento arranjoPagamento;

  public Credencial() {
    super();
  }

  public Credencial(Credencial credencial) {
    super();
    this.idCredencial = credencial.getIdCredencial();
    this.tokenInterno = credencial.getTokenInterno();
    this.dataHoraInclusao = credencial.getDataHoraInclusao();
    this.dataValidade = credencial.getDataValidade();
    this.status = credencial.getStatus();
    this.tipoStatus = credencial.getTipoStatus();
    this.idStatusV2 = credencial.getIdStatusV2();
    this.tipoStatusV2 = credencial.getTipoStatusV2();
    this.dataHoraStatus = credencial.getDataHoraStatus();
    this.idUsuarioInclusao = credencial.getIdUsuarioInclusao();
    this.idUsuarioManutencao = credencial.getIdUsuarioManutencao();
    this.idConta = credencial.getIdConta();
    this.idContaPagamento = credencial.getIdContaPagamento();
    this.idPessoa = credencial.getIdPessoa();
    this.dataHoraEmitido = credencial.getDataHoraEmitido();
    this.nomeImpresso = credencial.getNomeImpresso();
    this.idLoteEmissao = credencial.getIdLoteEmissao();
    this.ultimos4Digitos = credencial.getUltimos4Digitos();
    this.bin6 = credencial.getBin6();
    this.binLength = credencial.getBinLength();
    this.binEstendido = credencial.getBinEstendido();
    this.chip = credencial.getChip();
    this.idPlastico = credencial.getIdPlastico();
    this.plastico = credencial.getPlastico();
    this.virtual = credencial.getVirtual();
    this.pin = credencial.getPin();
    this.apelidoVirtual = credencial.getApelidoVirtual();
    this.habilitaExterior = credencial.getHabilitaExterior();
    this.habilitaEcommerce = credencial.getHabilitaEcommerce();
    this.habilitaSaque = credencial.getHabilitaSaque();
    this.habilitaUsoPessoa = credencial.getHabilitaUsoPessoa();
    this.habilitaNotificacaoTransacao = credencial.getHabilitaNotificacaoTransacao();
    this.csn = credencial.getCsn();
    this.titularidade = credencial.getTitularidade();
    this.motivoEmissao = credencial.getMotivoEmissao();
    this.idCredencialExterna = credencial.getIdCredencialExterna();
    this.credencialExternaReduzida = credencial.getCredencialExternaReduzida();
    this.valorTransacaoMaximo = credencial.getValorTransacaoMaximo();
    this.dataHoraUltimaUtilizacao = credencial.getDataHoraUltimaUtilizacao();
  }

  public Integer getIdStatusV2() {
    if (idStatusV2 != null) {
      return idStatusV2;
    } else {
      return status;
    }
  }

  public void setIdStatusV2(Integer idStatusV2) {
    this.idStatusV2 = idStatusV2;
    this.status = idStatusV2;
  }

  public TipoStatus getTipoStatusV2() {
    if (tipoStatusV2 != null) {
      return tipoStatusV2;
    } else {
      return tipoStatus;
    }
  }

  public void setTipoStatusV2(TipoStatus tipoStatusV2) {
    this.tipoStatusV2 = tipoStatusV2;
    this.tipoStatus = tipoStatusV2;
  }

  public LocalDateTime getDtHrLiberacaoEmissao() {
    return dtHrLiberacaoEmissao;
  }

  public void setDtHrLiberacaoEmissao(LocalDateTime dtHrLiberacaoEmissao) {
    this.dtHrLiberacaoEmissao = dtHrLiberacaoEmissao;
  }

  public LocalDateTime getDataHoraUltimaUtilizacao() {
    return dataHoraUltimaUtilizacao;
  }

  public void setDataHoraUltimaUtilizacao(LocalDateTime dataHoraUltimaUtilizacao) {
    this.dataHoraUltimaUtilizacao = dataHoraUltimaUtilizacao;
  }

  public BigDecimal getValorTransacaoMaximo() {
    return valorTransacaoMaximo;
  }

  public void setValorTransacaoMaximo(BigDecimal valorTransacaoMaximo) {
    this.valorTransacaoMaximo = valorTransacaoMaximo;
  }

  public String getCredencialExternaReduzida() {
    return credencialExternaReduzida;
  }

  public void setCredencialExternaReduzida(String credencialExternaReduzida) {
    this.credencialExternaReduzida = credencialExternaReduzida;
  }

  public String getIdCredencialExterna() {
    return idCredencialExterna;
  }

  public void setIdCredencialExterna(String idCredencialExterna) {
    this.idCredencialExterna = idCredencialExterna;
  }

  public Long getBinEstendido() {
    return binEstendido;
  }

  public void setBinEstendido(Long binEstendido) {
    this.binEstendido = binEstendido;
  }

  public Integer getCsn() {
    return csn;
  }

  public void setCsn(Integer csn) {
    this.csn = csn;
  }

  public Integer getTitularidade() {
    return titularidade;
  }

  public void setTitularidade(Integer titularidade) {
    this.titularidade = titularidade;
  }

  public TipoStatus getTipoStatus() {
    return tipoStatus;
  }

  public void setTipoStatus(TipoStatus tipoStatus) {
    this.tipoStatus = tipoStatus;
  }

  public Boolean getHabilitaUsoPessoa() {
    return habilitaUsoPessoa;
  }

  public void setHabilitaUsoPessoa(Boolean habilitaUsoPessoa) {
    this.habilitaUsoPessoa = habilitaUsoPessoa;
  }

  public Boolean getHabilitaNotificacaoTransacao() {
    return habilitaNotificacaoTransacao;
  }

  public void setHabilitaNotificacaoTransacao(Boolean habilitaNotificacaoTransacao) {
    this.habilitaNotificacaoTransacao = habilitaNotificacaoTransacao;
  }

  public Boolean getHabilitaExterior() {
    return habilitaExterior;
  }

  public void setHabilitaExterior(Boolean habilitaExterior) {
    this.habilitaExterior = habilitaExterior;
  }

  public Boolean getHabilitaEcommerce() {
    return habilitaEcommerce;
  }

  public void setHabilitaEcommerce(Boolean habilitaEcommerce) {
    this.habilitaEcommerce = habilitaEcommerce;
  }

  public Boolean getHabilitaSaque() {
    return habilitaSaque;
  }

  public void setHabilitaSaque(Boolean habilitaSaque) {
    this.habilitaSaque = habilitaSaque;
  }

  public Plastico getPlastico() {
    return plastico;
  }

  public void setPlastico(Plastico plastico) {
    this.plastico = plastico;
  }

  public String getApelidoVirtual() {
    return apelidoVirtual;
  }

  public void setApelidoVirtual(String apelidoVirtual) {
    this.apelidoVirtual = apelidoVirtual;
  }

  public String getPin() {
    return pin;
  }

  public void setPin(String pin) {
    this.pin = pin;
  }

  public Long getIdCredencial() {
    return idCredencial;
  }

  public void setIdCredencial(Long idCredencial) {
    this.idCredencial = idCredencial;
  }

  public String getTokenInterno() {
    return tokenInterno;
  }

  public void setTokenInterno(String tokenInterno) {
    this.tokenInterno = tokenInterno;
  }

  public LocalDateTime getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(LocalDateTime dataHoraInclusao) {
    this.dataHoraInclusao = dataHoraInclusao;
  }

  public LocalDateTime getDataValidade() {
    return dataValidade;
  }

  public void setDataValidade(LocalDateTime dataValidade) {
    this.dataValidade = dataValidade;
  }

  public Integer getStatus() {
    return status;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public LocalDateTime getDataHoraStatus() {
    return dataHoraStatus;
  }

  public void setDataHoraStatus(LocalDateTime dataHoraStatus) {
    this.dataHoraStatus = dataHoraStatus;
  }

  public Integer getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(Integer idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Integer getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(Integer idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  public Long getIdConta() {
    return idConta;
  }

  public void setIdConta(Long idConta) {
    this.idConta = idConta;
  }

  public String getIdContaPagamento() {
    return idContaPagamento;
  }

  public void setIdContaPagamento(String idContaPagamento) {
    this.idContaPagamento = idContaPagamento;
  }

  public Long getIdPessoa() {
    return idPessoa;
  }

  public void setIdPessoa(Long idPessoa) {
    this.idPessoa = idPessoa;
  }

  public Pessoa getPessoa() {
    return pessoa;
  }

  public void setPessoa(Pessoa pessoa) {
    this.pessoa = pessoa;
  }

  public LocalDateTime getDataHoraEmitido() {
    return dataHoraEmitido;
  }

  public void setDataHoraEmitido(LocalDateTime dataHoraEmitido) {
    this.dataHoraEmitido = dataHoraEmitido;
  }

  public String getNomeImpresso() {
    return nomeImpresso;
  }

  public void setNomeImpresso(String nomeImpresso) {
    this.nomeImpresso = nomeImpresso;
  }

  public Integer getIdLoteEmissao() {
    return idLoteEmissao;
  }

  public void setIdLoteEmissao(Integer idLoteEmissao) {
    this.idLoteEmissao = idLoteEmissao;
  }

  public Integer getUltimos4Digitos() {
    return ultimos4Digitos;
  }

  public void setUltimos4Digitos(Integer ultimos4Digitos) {
    this.ultimos4Digitos = ultimos4Digitos;
  }

  public Integer getBin6() {
    return bin6;
  }

  public void setBin6(Integer bin6) {
    this.bin6 = bin6;
  }

  public Integer getBinLength() {
    return binLength;
  }

  public void setBinLength(Integer binLength) {
    this.binLength = binLength;
  }

  public Boolean getChip() {
    return chip;
  }

  public void setChip(Boolean chip) {
    this.chip = chip;
  }

  public Integer getIdPlastico() {
    return idPlastico;
  }

  public void setIdPlastico(Integer idPlastico) {
    this.idPlastico = idPlastico;
  }

  public Boolean getVirtual() {
    return virtual;
  }

  public void setVirtual(Boolean virtual) {
    this.virtual = virtual;
  }

  public Integer getMotivoEmissao() {
    return motivoEmissao;
  }

  public void setMotivoEmissao(Integer motivoEmissao) {
    this.motivoEmissao = motivoEmissao;
  }

  public LocalDateTime getDtBloqueioResgatePontos() {
    return dtBloqueioResgatePontos;
  }

  public void setDtBloqueioResgatePontos(LocalDateTime dtBloqueioResgatePontos) {
    this.dtBloqueioResgatePontos = dtBloqueioResgatePontos;
  }

  public Integer getQtdTentativasResgateSenha() {
    return qtdTentativasResgateSenha;
  }

  public void setQtdTentativasResgateSenha(Integer qtdTentativasResgateSenha) {
    this.qtdTentativasResgateSenha = qtdTentativasResgateSenha;
  }

  public LocalDateTime getDtHrAlteracaoSenhaResgate() {
    return dtHrAlteracaoSenhaResgate;
  }

  public void setDtHrAlteracaoSenhaResgate(LocalDateTime dtHrAlteracaoSenhaResgate) {
    this.dtHrAlteracaoSenhaResgate = dtHrAlteracaoSenhaResgate;
  }

  public Boolean getMultiConta() {
    return multiConta;
  }

  public void setMultiConta(Boolean multiConta) {
    this.multiConta = multiConta;
  }

  public Integer getStatusNfc() {
    return statusNfc;
  }

  public void setStatusNfc(Integer statusNfc) {
    this.statusNfc = statusNfc;
  }

  public LocalDateTime getDtHrStatusNfc() {
    return dtHrStatusNfc;
  }

  public void setDtHrStatusNfc(LocalDateTime dtHrStatusNfc) {
    this.dtHrStatusNfc = dtHrStatusNfc;
  }

  public Integer getIdBandeira() {
    return this.idBandeira;
  }

  public void setIdBandeira(Integer idBandeira) {
    this.idBandeira = idBandeira;
  }

  public Bandeira getBandeira() {
    return bandeira;
  }

  public void setBandeira(Bandeira bandeira) {
    this.bandeira = bandeira;
  }

  public String getServiceCode() {
    return serviceCode;
  }

  public void setServiceCode(String serviceCode) {
    this.serviceCode = serviceCode;
  }

  public Integer getIdArranjo() {
    return idArranjo;
  }

  public void setIdArranjo(Integer idArranjo) {
    this.idArranjo = idArranjo;
  }

  public ArranjoPagamento getArranjoPagamento() {
    return arranjoPagamento;
  }

  public void setArranjoPagamento(ArranjoPagamento arranjoPagamento) {
    this.arranjoPagamento = arranjoPagamento;
  }

  @Override
  public int hashCode() {
    return Objects.hash(
        idConta, idContaPagamento, idCredencial, idLoteEmissao, idPessoa, idPlastico);
  }

  @Override
  public boolean equals(Object obj) {
    if (this == obj) {
      return true;
    }
    if (obj == null) {
      return false;
    }
    if (getClass() != obj.getClass()) {
      return false;
    }
    Credencial other = (Credencial) obj;
    return Objects.equals(idConta, other.idConta)
        && Objects.equals(idContaPagamento, other.idContaPagamento)
        && Objects.equals(idCredencial, other.idCredencial)
        && Objects.equals(idLoteEmissao, other.idLoteEmissao)
        && Objects.equals(idPessoa, other.idPessoa)
        && Objects.equals(idPlastico, other.idPlastico);
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {
    try {
      Util.toUpperCase(this);
    } catch (Exception e) {
      LoggerFactory.getLogger(getClass()).error(e.getMessage());
    }
  }

  @Override
  public String toString() {
    return "Credencial{"
        + "idCredencial="
        + idCredencial
        + ", tokenInterno='"
        + tokenInterno
        + '\''
        + ", dataHoraInclusao="
        + dataHoraInclusao
        + ", dataValidade="
        + dataValidade
        + ", status="
        + status
        + ", idStatusV2="
        + idStatusV2
        + ", dataHoraStatus="
        + dataHoraStatus
        + ", idUsuarioInclusao="
        + idUsuarioInclusao
        + ", idUsuarioManutencao="
        + idUsuarioManutencao
        + ", idConta="
        + idConta
        + ", idContaPagamento='"
        + idContaPagamento
        + '\''
        + ", idPessoa="
        + idPessoa
        + ", pessoa="
        + pessoa
        + ", dataHoraEmitido="
        + dataHoraEmitido
        + ", nomeImpresso='"
        + nomeImpresso
        + '\''
        + ", idLoteEmissao="
        + idLoteEmissao
        + ", ultimos4Digitos="
        + ultimos4Digitos
        + ", bin6="
        + bin6
        + ", binLength="
        + binLength
        + ", binEstendido="
        + binEstendido
        + ", chip="
        + chip
        + ", idPlastico="
        + idPlastico
        + ", virtual="
        + virtual
        + ", pin='"
        + pin
        + '\''
        + ", apelidoVirtual='"
        + apelidoVirtual
        + '\''
        + ", habilitaExterior="
        + habilitaExterior
        + ", habilitaEcommerce="
        + habilitaEcommerce
        + ", habilitaSaque="
        + habilitaSaque
        + ", habilitaUsoPessoa="
        + habilitaUsoPessoa
        + ", habilitaNotificacaoTransacao="
        + habilitaNotificacaoTransacao
        + ", csn="
        + csn
        + ", titularidade="
        + titularidade
        + ", motivoEmissao="
        + motivoEmissao
        + ", idCredencialExterna='"
        + idCredencialExterna
        + '\''
        + ", credencialExternaReduzida='"
        + credencialExternaReduzida
        + '\''
        + ", dataHoraUltimaUtilizacao="
        + dataHoraUltimaUtilizacao
        + ", dtBloqueioResgatePontos="
        + dtBloqueioResgatePontos
        + ", dtHrLiberacaoEmissao="
        + dtHrLiberacaoEmissao
        + ", qtdTentativasResgateSenha="
        + qtdTentativasResgateSenha
        + ", dtHrAlteracaoSenhaResgate="
        + dtHrAlteracaoSenhaResgate
        + ", multiConta="
        + multiConta
        + ", statusNfc="
        + statusNfc
        + ", dtHrStatusNfc="
        + dtHrStatusNfc
        + ", idBandeira="
        + idBandeira
        + ", serviceCode="
        + serviceCode
        + ", idArranjo="
        + idArranjo
        + '}';
  }
}
