package br.com.sinergico.repository.suporte;

import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicaoCorrespondente;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.json.bean.cadastral.BuscaProdutosEmpresasReplicaveis;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface HierarquiaPontoRelacionamentoRepository
    extends JpaRepository<HierarquiaPontoDeRelacionamento, HierarquiaPontoDeRelacionamentoId>,
        HierarquiaPontoRelacionamentoRepositoryCustom {
  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilial(
          Integer idProcessadora, Integer idInstituicao, Integer idRegional, Integer idFilial);

  List<HierarquiaPontoDeRelacionamento>
      findByIdInstituicaoAndIdProcessadoraAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoIsNotAndB2bFalseOrderByDescricao(
          Integer idInstituicao,
          Integer idProcessadora,
          Integer idRegional,
          Integer idFilial,
          Integer idPontoDeRelacionamento);

  Integer countByDocumentoAndIdProcessadoraAndIdInstituicao(
      String documento, Integer idProcessadora, Integer idInstituicao);

  @Query(value = "select nextval('suporte.seq_id_ponto_de_relacionamento')", nativeQuery = true)
  Integer nextValSeqIdPontoRelacionamento();

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndDescricaoStartingWithIgnoreCaseAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          String descricao,
          boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndDescricaoStartingWithIgnoreCaseAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          String descricao,
          boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndDescricaoStartingWithIgnoreCaseAndB2b(
          Integer idProcessadora, Integer idInstituicao, String descricao, boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndDescricaoStartingWithIgnoreCaseAndB2b(
          Integer idProcessadora, String descricao, boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndDocumentoAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          String documento,
          boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndDocumentoAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          String documento,
          boolean b2b);

  List<HierarquiaPontoDeRelacionamento> findByIdProcessadoraAndIdInstituicaoAndDocumentoAndB2b(
      Integer idProcessadora, Integer idInstituicao, String documento, boolean b2b);

  List<HierarquiaPontoDeRelacionamento> findByIdProcessadoraAndDocumentoAndB2b(
      Integer idProcessadora, String documento, boolean b2b);

  HierarquiaPontoDeRelacionamento
      findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer id,
          boolean b2b);

  HierarquiaPontoDeRelacionamento
      findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdPontoDeRelacionamentoAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer id,
          boolean b2b);

  HierarquiaPontoDeRelacionamento
      findOneByIdProcessadoraAndIdInstituicaoAndIdPontoDeRelacionamentoAndB2b(
          Integer idProcessadora, Integer idInstituicao, Integer id, boolean b2b);

  HierarquiaPontoDeRelacionamento findOneByIdProcessadoraAndIdPontoDeRelacionamentoAndB2b(
      Integer idProcessadora, Integer id, boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndB2bFalse(
          Integer idProc, Integer idInst, Integer idReg, Integer idFilial);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer id,
          boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdPontoDeRelacionamentoAndB2b(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer id,
          boolean b2b);

  List<HierarquiaPontoDeRelacionamento>
      findByIdProcessadoraAndIdInstituicaoAndIdPontoDeRelacionamentoAndB2b(
          Integer idProcessadora, Integer idInstituicao, Integer id, boolean b2b);

  List<HierarquiaPontoDeRelacionamento> findByIdProcessadoraAndIdPontoDeRelacionamentoAndB2b(
      Integer idProcessadora, Integer id, boolean b2b);

  List<BuscaProdutosEmpresasReplicaveis> buscaEmpresasReplicaveis();

  List<HierarquiaPontoDeRelacionamento> findHierarquiaCorrespondenteOrigem(
      ProdutoInstituicaoCorrespondente prodCorresp, ProdutoContratado contratado);

  HierarquiaPontoDeRelacionamento
      findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamento(
          Integer idProcessadora,
          Integer idInstituicao,
          Integer idRegional,
          Integer idFilial,
          Integer id);

  @Query(
      value =
          " select (hpdr.status in (1,8) or hpdr2.status in (1,8)) as statusHabilitado \n "
              + " from suporte.hierarquia_ponto_de_relacionamento hpdr \n "
              + " \t inner join suporte.hierarquia_ponto_de_relacionamento hpdr2 on \n "
              + " \t \t (hpdr.id_processadora_corresp_2, hpdr.id_instituicao_corresp_2, hpdr.id_regional_corresp_2, hpdr.id_filial_corresp_2, hpdr.id_ponto_de_relacionamento_corresp_2) \n "
              + " \t \t = (hpdr2.id_processadora, hpdr2.id_instituicao, hpdr2.id_regional, hpdr2.id_filial, hpdr2.id_ponto_de_relacionamento) \n "
              + " where true \n "
              + " \t and hpdr.id_processadora = :idProcessadora \n "
              + " \t and hpdr.id_instituicao = :idInstituicao \n "
              + " \t and hpdr.id_regional = :idRegional \n "
              + " \t and hpdr.id_filial = :idFilial \n "
              + " \t and hpdr.id_ponto_de_relacionamento = :idPontoDeRelacionamento ",
      nativeQuery = true)
  Boolean checaStatusEmpresasPermiteReplicacao(
      @Param("idProcessadora") Integer idProcessadora,
      @Param("idInstituicao") Integer idInstituicao,
      @Param("idRegional") Integer idRegional,
      @Param("idFilial") Integer idFilial,
      @Param("idPontoDeRelacionamento") Integer idPontoDeRelacionamento);

  @Query(
      value =
          "select * from suporte.hierarquia_ponto_de_relacionamento hpdr where hpdr.id_grupo_empresarial = :idGrupoEmpresarial "
              + "and hpdr.id_instituicao = :idInstituicao order by hpdr.id_ponto_de_relacionamento desc",
      nativeQuery = true)
  List<HierarquiaPontoDeRelacionamento>
      findHierarquiaPontoDeRelacionamentoByIdGrupoEmpresarialAndStatus(
          @Param("idGrupoEmpresarial") Long idGrupoEmpresarial,
          @Param("idInstituicao") Integer idInstituicao);
}
