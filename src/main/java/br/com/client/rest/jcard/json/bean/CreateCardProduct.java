package br.com.client.rest.jcard.json.bean;

import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.util.Util;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;

/**
 * *
 *
 * <p>bean de entrada para criacao de produto no jcard.
 *
 * <p><b>Corpo
 *
 * <p>{ "institutionId": "100101", "name": "Vale Presente", "active" : true, "pos" : true, "atm" :
 * true, "moto": true, "ecommerce" : true, "tips" : true, "anonymous" : true, "startDate" :
 * "2016-09-17", "endDate" : "2099-07-01", "externalAccount" : "0000001", "bin" : "400650",
 * "binLength": 6, "binExtended" : "400650", "binVirtual" : "400650", "binExtendedVirtual" :
 * "400650", "cardNumberLength" :16, "smart" : true, "randomCardNumber" : true, "pinLength" : 4,
 * "serviceCode" : "226", "paymentScheme" : "VISA" }
 *
 * <AUTHOR>
 */
@JsonInclude(Include.NON_NULL)
public class CreateCardProduct implements Serializable {

  private static final long serialVersionUID = 258427775023507945L;

  private String institutionId;
  private String name;
  private Boolean active;
  private Boolean pos;
  private Boolean atm;
  private Boolean moto;
  private Boolean ecommerce;
  private Boolean tips;
  private Boolean anonymous;
  private String startDate;
  private String endDate;
  private String externalAccount;
  private String bin;
  private Integer binLength;
  private String binExtended;
  private String binVirtual;
  private String binExtendedVirtual;
  private Integer cardNumberLength;
  private Boolean smart;
  private Boolean randomCardNumber;
  private Integer pinLength;
  private String serviceCode;
  private String paymentScheme;
  private Boolean additionalCardLimited;
  private Double additionalCardLimitPercentage;
  private TipoProdutoEnum productType;

  public String getInstitutionId() {
    return institutionId;
  }

  public void setInstitutionId(String institutionId) {
    this.institutionId = institutionId;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  public Boolean getActive() {
    return active;
  }

  public void setActive(Boolean active) {
    this.active = active;
  }

  public Boolean getPos() {
    return pos;
  }

  public void setPos(Boolean pos) {
    this.pos = pos;
  }

  public Boolean getAtm() {
    return atm;
  }

  public void setAtm(Boolean atm) {
    this.atm = atm;
  }

  public Boolean getMoto() {
    return moto;
  }

  public void setMoto(Boolean moto) {
    this.moto = moto;
  }

  public Boolean getEcommerce() {
    return ecommerce;
  }

  public void setEcommerce(Boolean ecommerce) {
    this.ecommerce = ecommerce;
  }

  public Boolean getTips() {
    return tips;
  }

  public void setTips(Boolean tips) {
    this.tips = tips;
  }

  public Boolean getAnonymous() {
    return anonymous;
  }

  public void setAnonymous(Boolean anonymous) {
    this.anonymous = anonymous;
  }

  public String getStartDate() {
    return startDate;
  }

  public void setStartDate(String startDate) {
    this.startDate = startDate;
  }

  public String getEndDate() {
    return endDate;
  }

  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }

  public String getExternalAccount() {
    return externalAccount;
  }

  public void setExternalAccount(String externalAccount) {
    this.externalAccount = externalAccount;
  }

  public String getBin() {
    return bin;
  }

  public void setBin(String bin) {
    this.bin = bin;
  }

  public Integer getBinLength() {
    return binLength;
  }

  public void setBinLength(Integer binLength) {
    this.binLength = binLength;
  }

  public String getBinExtended() {
    return binExtended;
  }

  public void setBinExtended(String binExtended) {
    this.binExtended = binExtended;
  }

  public String getBinVirtual() {
    return binVirtual;
  }

  public void setBinVirtual(String binVirtual) {
    this.binVirtual = binVirtual;
  }

  public String getBinExtendedVirtual() {
    return binExtendedVirtual;
  }

  public void setBinExtendedVirtual(String binExtendedVirtual) {
    this.binExtendedVirtual = binExtendedVirtual;
  }

  public Integer getCardNumberLength() {
    return cardNumberLength;
  }

  public void setCardNumberLength(Integer cardNumberLength) {
    this.cardNumberLength = cardNumberLength;
  }

  public Boolean getSmart() {
    return smart;
  }

  public void setSmart(Boolean smart) {
    this.smart = smart;
  }

  public Boolean getRandomCardNumber() {
    return randomCardNumber;
  }

  public void setRandomCardNumber(Boolean randomCardNumber) {
    this.randomCardNumber = randomCardNumber;
  }

  public Integer getPinLength() {
    return pinLength;
  }

  public void setPinLength(Integer pinLength) {
    this.pinLength = pinLength;
  }

  public String getServiceCode() {
    return serviceCode;
  }

  public void setServiceCode(String serviceCode) {
    this.serviceCode = serviceCode;
  }

  public String getPaymentScheme() {
    return paymentScheme;
  }

  public void setPaymentScheme(String paymentScheme) {
    this.paymentScheme = paymentScheme;
  }

  public Boolean getAdditionalCardLimited() {
    return additionalCardLimited;
  }

  public void setAdditionalCardLimited(Boolean additionalCardLimited) {
    this.additionalCardLimited = additionalCardLimited;
  }

  public Double getAdditionalCardLimitPercentage() {
    return additionalCardLimitPercentage;
  }

  public void setAdditionalCardLimitPercentage(Double additionalCardLimitPercentage) {
    this.additionalCardLimitPercentage = additionalCardLimitPercentage;
  }

  public TipoProdutoEnum getProductType() {
    return productType;
  }

  public void setProductType(TipoProdutoEnum productType) {
    this.productType = productType;
  }

  @Override
  public String toString() {
    return "CreateCardProduct [institutionId="
        + institutionId
        + ", name="
        + name
        + ", active="
        + active
        + ", pos="
        + pos
        + ", atm="
        + atm
        + ", moto="
        + moto
        + ", ecommerce="
        + ecommerce
        + ", tips="
        + tips
        + ", anonymous="
        + anonymous
        + ", startDate="
        + startDate
        + ", endDate="
        + endDate
        + ", externalAccount="
        + externalAccount
        + ", bin="
        + bin
        + ", binLength="
        + binLength
        + ", binExtended="
        + binExtended
        + ", binVirtual="
        + binVirtual
        + ", binExtendedVirtual="
        + binExtendedVirtual
        + ", cardNumberLength="
        + cardNumberLength
        + ", smart="
        + smart
        + ", randomCardNumber="
        + randomCardNumber
        + ", pinLength="
        + pinLength
        + ", serviceCode="
        + serviceCode
        + ", paymentScheme="
        + paymentScheme
        + ", productType="
        + productType
        + "]";
  }

  @PrePersist
  @PreUpdate
  public void beforePersist() {

    try {

      Util.toUpperCase(this);

    } catch (Exception e) {

    }
  }
}
