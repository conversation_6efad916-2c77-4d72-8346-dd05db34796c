package br.com.json.bean.cadastral;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class DetalhePreLancamentoLote implements Serializable {
  private static final long serialVersionUID = -2443685902437797214L;

  private String nomeCompleto;
  private BigDecimal idSetorFilial;
  private Integer idLote;
  private BigDecimal idConta;
  private BigDecimal valorLancamento;
  private Date dataHoraInclusao;
  private BigDecimal idUsuarioInclusao;
  private Date dataHoraManutencao;
  private BigDecimal idUsuarioManutencao;
  private BigDecimal idProdInstituicao;
  private Date dataAgendamento;
  private BigDecimal qtdLancamento;
  private BigDecimal valorTotalLancamento;
  private BigDecimal status;
  private String descProdInstituicao;
  private String documento;
  private String matricula;
  private String descSetorFilial;
  private Integer idTipoCarga;
  private Integer tipoEmissaoPedido;
  private Integer tipoPedido;
  private BigDecimal idInstituicao;
  private Boolean cargaIntegracao;
  private String chavePix;
  private Integer idBanco;
  private Integer idAgencia;
  private String contaBancariaX;
  private Integer tipoContaBancaria;
  private Integer statuslancamento;
  private Integer consultaRestrita;

  public BigDecimal getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(BigDecimal idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public DetalhePreLancamentoLote() {
    super();
  }

  public DetalhePreLancamentoLote(Integer idLote, BigDecimal valorLancamento) {
    this.idLote = idLote;
    this.valorLancamento = valorLancamento;
  }

  public Integer getTipoPedido() {
    return tipoPedido;
  }

  public void setTipoPedido(Integer tipoPedido) {
    this.tipoPedido = tipoPedido;
  }

  public Integer getTipoEmissaoPedido() {
    return tipoEmissaoPedido;
  }

  public void setTipoEmissaoPedido(Integer tipoEmissaoPedido) {
    this.tipoEmissaoPedido = tipoEmissaoPedido;
  }

  public String getNomeCompleto() {
    return nomeCompleto;
  }

  public void setNomeCompleto(String nomeCompleto) {
    this.nomeCompleto = nomeCompleto;
  }

  public BigDecimal getIdSetorFilial() {
    return idSetorFilial;
  }

  public void setIdSetorFilial(BigDecimal idSetorFilial) {
    this.idSetorFilial = idSetorFilial;
  }

  public Integer getIdLote() {
    return idLote;
  }

  public void setIdLote(Integer idLote) {
    this.idLote = idLote;
  }

  public BigDecimal getIdConta() {
    return idConta;
  }

  public void setIdConta(BigDecimal idConta) {
    this.idConta = idConta;
  }

  public BigDecimal getValorLancamento() {
    return valorLancamento;
  }

  public void setValorLancamento(BigDecimal valorLancamento) {
    this.valorLancamento = valorLancamento;
  }

  public Date getDataHoraInclusao() {
    return dataHoraInclusao;
  }

  public void setDataHoraInclusao(Date dataHoraInclusao) {
    this.dataHoraInclusao = dataHoraInclusao;
  }

  public BigDecimal getIdUsuarioInclusao() {
    return idUsuarioInclusao;
  }

  public void setIdUsuarioInclusao(BigDecimal idUsuarioInclusao) {
    this.idUsuarioInclusao = idUsuarioInclusao;
  }

  public Date getDataHoraManutencao() {
    return dataHoraManutencao;
  }

  public void setDataHoraManutencao(Date dataHoraManutencao) {
    this.dataHoraManutencao = dataHoraManutencao;
  }

  public BigDecimal getIdUsuarioManutencao() {
    return idUsuarioManutencao;
  }

  public void setIdUsuarioManutencao(BigDecimal idUsuarioManutencao) {
    this.idUsuarioManutencao = idUsuarioManutencao;
  }

  public BigDecimal getIdProdInstituicao() {
    return idProdInstituicao;
  }

  public void setIdProdInstituicao(BigDecimal idProdInstituicao) {
    this.idProdInstituicao = idProdInstituicao;
  }

  public Date getDataAgendamento() {
    return dataAgendamento;
  }

  public void setDataAgendamento(Date dataAgendamento) {
    this.dataAgendamento = dataAgendamento;
  }

  public BigDecimal getQtdLancamento() {
    return qtdLancamento;
  }

  public void setQtdLancamento(BigDecimal qtdLancamento) {
    this.qtdLancamento = qtdLancamento;
  }

  public BigDecimal getValorTotalLancamento() {
    return valorTotalLancamento;
  }

  public void setValorTotalLancamento(BigDecimal valorTotalLancamento) {
    this.valorTotalLancamento = valorTotalLancamento;
  }

  public BigDecimal getStatus() {
    return status;
  }

  public void setStatus(BigDecimal status) {
    this.status = status;
  }

  public String getDescProdInstituicao() {
    return descProdInstituicao;
  }

  public void setDescProdInstituicao(String descProdInstituicao) {
    this.descProdInstituicao = descProdInstituicao;
  }

  public String getDocumento() {
    return documento;
  }

  public void setDocumento(String documento) {
    this.documento = documento;
  }

  public String getMatricula() {
    return matricula;
  }

  public void setMatricula(String matricula) {
    this.matricula = matricula;
  }

  public String getDescSetorFilial() {
    return descSetorFilial;
  }

  public void setDescSetorFilial(String descSetorFilial) {
    this.descSetorFilial = descSetorFilial;
  }

  public Integer getIdTipoCarga() {
    return idTipoCarga;
  }

  public void setIdTipoCarga(Integer idTipoCarga) {
    this.idTipoCarga = idTipoCarga;
  }

  public Boolean getCargaIntegracao() {
    return cargaIntegracao;
  }

  public void setCargaIntegracao(Boolean cargaIntegracao) {
    this.cargaIntegracao = cargaIntegracao;
  }

  public String getChavePix() {
    return chavePix;
  }

  public void setChavePix(String chavePix) {
    this.chavePix = chavePix;
  }

  public Integer getIdBanco() {
    return idBanco;
  }

  public void setIdBanco(Integer idBanco) {
    this.idBanco = idBanco;
  }

  public Integer getIdAgencia() {
    return idAgencia;
  }

  public void setIdAgencia(Integer idAgencia) {
    this.idAgencia = idAgencia;
  }

  public String getContaBancariaX() {
    return contaBancariaX;
  }

  public void setContaBancariaX(String contaBancariaX) {
    this.contaBancariaX = contaBancariaX;
  }

  public Integer getTipoContaBancaria() {
    return tipoContaBancaria;
  }

  public void setTipoContaBancaria(Integer tipoContaBancaria) {
    this.tipoContaBancaria = tipoContaBancaria;
  }

  public Integer getStatuslancamento() {
    return statuslancamento;
  }

  public void setStatuslancamento(Integer statuslancamento) {
    this.statuslancamento = statuslancamento;
  }

  public Integer getConsultaRestrita() {
    return consultaRestrita;
  }

  public void setConsultaRestrita(Integer consultaRestrita) {
    this.consultaRestrita = consultaRestrita;
  }
}
