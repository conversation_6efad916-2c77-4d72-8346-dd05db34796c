package br.com.sinergico.facade.cadastral;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import br.com.BaseControllerTest;
import br.com.entity.cadastral.ArranjoRelacionamento;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.ContaPessoa;
import br.com.entity.cadastral.GrupoEmpresarial;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicao;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituidor;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.suporte.HierarquiaFilial;
import br.com.entity.suporte.HierarquiaInstituicao;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaRegional;
import br.com.entity.suporte.MoedaConta;
import br.com.entity.suporte.TipoStatus;
import br.com.json.bean.cadastral.DadosConta;
import br.com.json.bean.cadastral.DadosPortador;
import br.com.json.bean.cadastral.DetalhesConta;
import br.com.sinergico.repository.cadastral.GrupoEmpresarialRepository;
import br.com.sinergico.repository.suporte.impl.AntifraudeCafPortadorRepositoryImpl;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.ContaPessoaService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.ProdutoContratadoService;
import br.com.sinergico.vo.PessoaPoliticamenteExpostaVO;
import br.com.util.MockUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ContaPagamentoFacadeTest extends BaseControllerTest {

  @Mock private ContaPagamentoService contaPagamentoService;

  @Mock private ContaPessoaService contaPessoaService;

  @Mock private PessoaService pessoaService;

  @Mock private ProdutoContratadoService produtoContratadoService;

  @Mock private AntifraudeCafPortadorRepositoryImpl antifraudeCafPortadorRepository;

  @Mock private GrupoEmpresarialRepository grupoEmpresarialRepository;

  @InjectMocks @Spy private ContaPagamentoFacade facade;

  @Override
  public void setup() {
    super.setup();
  }

  @Test
  void verificaPessoaPoliticamenteExpostaSemCafRealizado() {

    List<PessoaPoliticamenteExpostaVO> pep = new ArrayList<>();
    pep.add(
        PessoaPoliticamenteExpostaVO.builder()
            .documento("12345678900")
            .blPoliticamenteExposta(true)
            .build());

    DetalhesConta detalhesConta = new DetalhesConta();
    DadosPortador dadosPortador = criaDadosPortadorMock();
    DadosConta dadosConta = criaDadosContaMock();
    detalhesConta.setDadosPortador(dadosPortador);
    detalhesConta.setDadosConta(dadosConta);

    when(antifraudeCafPortadorRepository.getPessoaPoliticamenteExpostaRepresentanteLegal(
            detalhesConta.getDadosConta().getIdConta(),
            detalhesConta.getDadosPortador().getIdInstituicao()))
        .thenReturn(pep);

    facade.verificaRepresentantesLegaisPoliticamenteExpostos(dadosConta, 123);
    assertEquals(true, dadosConta.getRepresentantes().get(0).getBlPepCaf());
    verify(antifraudeCafPortadorRepository, times(1))
        .getPessoaPoliticamenteExpostaRepresentanteLegal(anyLong(), anyInt());
  }

  @Test
  void verificaPessoaPoliticamenteExpostaSemCafRealizadoListaVazia() {

    List<PessoaPoliticamenteExpostaVO> pep = new ArrayList<>();
    DetalhesConta detalhesConta = new DetalhesConta();
    DadosPortador dadosPortador = criaDadosPortadorMock();
    DadosConta dadosConta = criaDadosContaMock();
    detalhesConta.setDadosPortador(dadosPortador);
    detalhesConta.setDadosConta(dadosConta);

    when(antifraudeCafPortadorRepository.getPessoaPoliticamenteExpostaRepresentanteLegal(
            detalhesConta.getDadosConta().getIdConta(),
            detalhesConta.getDadosPortador().getIdInstituicao()))
        .thenReturn(pep);

    facade.verificaRepresentantesLegaisPoliticamenteExpostos(dadosConta, 123);
    assertEquals(false, dadosConta.getRepresentantes().get(0).getBlPepCaf());
    verify(antifraudeCafPortadorRepository, times(1))
        .getPessoaPoliticamenteExpostaRepresentanteLegal(anyLong(), anyInt());
  }

  @Test
  void recebeErroTamanhoListaCaf() {

    List<PessoaPoliticamenteExpostaVO> pep = new ArrayList<>();
    pep.add(
        PessoaPoliticamenteExpostaVO.builder()
            .documento("12345678900")
            .blPoliticamenteExposta(true)
            .build());

    DetalhesConta detalhesConta = new DetalhesConta();
    DadosPortador dadosPortador = criaDadosPortadorMock();
    DadosConta dadosConta = criaDadosContaMock();
    detalhesConta.setDadosPortador(dadosPortador);
    detalhesConta.setDadosConta(dadosConta);

    when(antifraudeCafPortadorRepository.getPessoaPoliticamenteExpostaRepresentanteLegal(
            detalhesConta.getDadosConta().getIdConta(),
            detalhesConta.getDadosPortador().getIdInstituicao()))
        .thenReturn(pep);

    adicionaRepresentante(detalhesConta.getDadosConta());

    facade.verificaRepresentantesLegaisPoliticamenteExpostos(detalhesConta.getDadosConta(), 123);
    assertEquals(false, dadosConta.getRepresentantes().get(1).getBlPepCaf());
    verify(antifraudeCafPortadorRepository, times(1))
        .getPessoaPoliticamenteExpostaRepresentanteLegal(anyLong(), anyInt());
  }

  private DadosPortador criaDadosPortadorMock() {
    DadosPortador dadosPortador = new DadosPortador();
    dadosPortador.setDocumento("12345678900");
    dadosPortador.setIdInstituicao(123);
    dadosPortador.setIdTipoPessoa(2);
    return dadosPortador;
  }

  private DadosConta criaDadosContaMock() {
    DadosConta dadosConta = new DadosConta();
    dadosConta.setRepresentantes(new ArrayList<>());
    dadosConta.getRepresentantes().add(new RepresentanteLegal());
    dadosConta.getRepresentantes().get(0).setBlPoliticamenteExposto(false);
    dadosConta.setIdConta(
        MockUtil.getLongAleatorioEntre(MockUtil.getIdContaMin(), MockUtil.getIdContaMax()));
    dadosConta.getRepresentantes().get(0).setBlPepCaf(false);
    dadosConta.getRepresentantes().get(0).setCpf("12345678900");
    dadosConta.setIdInstituicao(123);
    return dadosConta;
  }

  private void adicionaRepresentante(DadosConta dadosConta) {
    RepresentanteLegal rep = new RepresentanteLegal();
    rep.setBlPepCaf(true);
    rep.setCpf("12345678901");
    rep.setStatus(1);
    dadosConta.getRepresentantes().add(rep);
  }

  @Test
  void buscaDadosComGrupoEmpresarial() {
    ContaPagamento contaPagamento = criaDadosContaPagamentoMock();
    GrupoEmpresarial grupoEmpresarialMock = criaDadosGrupoEmpresarial();
    List<ContaPessoa> contasPessoa = criaDadosContasPessoa();
    Pessoa pessoa = criaDadosPessoa();

    when(contaPagamentoService.findByIdNotNull(123L)).thenReturn(contaPagamento);
    when(grupoEmpresarialRepository.findGrupoEmpresarialByIdGrupoEmpresarial(
            contaPagamento.getHierarquiaPontoDeRelacionamento().getIdGrupoEmpresarial()))
        .thenReturn(grupoEmpresarialMock);
    when(contaPessoaService.findByIdConta(123L)).thenReturn(contasPessoa);
    when(produtoContratadoService.findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            contaPagamento.getIdProcessadora(),
            contaPagamento.getIdInstituicao(),
            contaPagamento.getIdRegional(),
            contaPagamento.getIdFilial(),
            contaPagamento.getIdPontoDeRelacionamento(),
            contaPagamento.getIdProdutoInstituicao()))
        .thenReturn(new ProdutoContratado());
    when(pessoaService.findById(contasPessoa.get(0).getIdPessoa())).thenReturn(pessoa);

    DadosConta dadosConta = facade.getDadosConta(123L);

    assertNotEquals(null, dadosConta.getIdGrupoEmpresarial());
    assertNotEquals(null, dadosConta.getNoGrupoEmpresarial());
    assertNotEquals(null, dadosConta.getNuCnpjGrupoEmpresarial());
  }

  private ContaPagamento criaDadosContaPagamentoMock() {
    ContaPagamento contaPagamento = new ContaPagamento();
    HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento =
        criaDadosHierarquiaPontoDeRelacionamento();
    contaPagamento.setIdConta(123L);
    contaPagamento.setIdProcessadora(10);
    contaPagamento.setIdInstituicao(1801);
    contaPagamento.setIdRegional(1);
    contaPagamento.setIdFilial(1);
    contaPagamento.setIdPontoDeRelacionamento(1);
    contaPagamento.setIdProdutoInstituicao(1);
    contaPagamento.setArranjoRelacionamento(new ArranjoRelacionamento());
    contaPagamento.setProdutoInstituicao(criaDadosProdutoInstituicao());
    contaPagamento.setProdutoInstituidor(new ProdutoInstituidor());
    contaPagamento.setDataHoraInclusao(LocalDateTime.now());
    contaPagamento.setHierarquiaPontoDeRelacionamento(hierarquiaPontoDeRelacionamento);
    contaPagamento.setTipoStatusV2(criaDadosTipoStatus());
    contaPagamento.setIdStatusV2(1);
    return contaPagamento;
  }

  private GrupoEmpresarial criaDadosGrupoEmpresarial() {
    GrupoEmpresarial grupoEmpresarial = new GrupoEmpresarial();
    grupoEmpresarial.setIdGrupoEmpresarial(
        MockUtil.getLongAleatorioEntre(MockUtil.getIdContaMin(), MockUtil.getIdContaMax()));
    grupoEmpresarial.setNoGrupoEmpresarial("TESTE");
    grupoEmpresarial.setNuCnpjGrupoEmpresarial("12345678901234");
    return grupoEmpresarial;
  }

  private ProdutoInstituicao criaDadosProdutoInstituicao() {
    ProdutoInstituicao produtoInstituicao = new ProdutoInstituicao();
    List<ProdutoInstituicaoConfiguracao> listaProdutoInstituicaoConfiguracao = new ArrayList<>();
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        new ProdutoInstituicaoConfiguracao();
    MoedaConta moedaConta = new MoedaConta();
    moedaConta.setIdMoeda(123);
    moedaConta.setSimbolo("TESTE");
    produtoInstituicaoConfiguracao.setIdadeMinimaPortador(18);
    produtoInstituicaoConfiguracao.setMoeda(moedaConta);
    listaProdutoInstituicaoConfiguracao.add(produtoInstituicaoConfiguracao);
    produtoInstituicao.setProdutoInstituicaoConfiguracao(listaProdutoInstituicaoConfiguracao);
    return produtoInstituicao;
  }

  private HierarquiaPontoDeRelacionamento criaDadosHierarquiaPontoDeRelacionamento() {
    HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento =
        new HierarquiaPontoDeRelacionamento();
    hierarquiaPontoDeRelacionamento.setIdGrupoEmpresarial(
        MockUtil.getLongAleatorioEntre(MockUtil.getIdContaMin(), MockUtil.getIdContaMax()));
    HierarquiaInstituicao hierarquiaInstituicao = new HierarquiaInstituicao();
    hierarquiaPontoDeRelacionamento.setHierarquiaInstituicao(hierarquiaInstituicao);
    HierarquiaRegional hierarquiaRegional = new HierarquiaRegional();
    hierarquiaPontoDeRelacionamento.setHierarquiaRegional(hierarquiaRegional);
    HierarquiaFilial hierarquiaFilial = new HierarquiaFilial();
    hierarquiaPontoDeRelacionamento.setHierarquiaFilial(hierarquiaFilial);
    return hierarquiaPontoDeRelacionamento;
  }

  private List<ContaPessoa> criaDadosContasPessoa() {
    List<ContaPessoa> contasPessoa = new ArrayList<>();
    ContaPessoa contaPessoa = new ContaPessoa();
    contaPessoa.setNomeCartaoImpresso("TESTE");
    contasPessoa.add(contaPessoa);
    return contasPessoa;
  }

  private Pessoa criaDadosPessoa() {
    Pessoa pessoa = new Pessoa();
    pessoa.setIdTipoPessoa(1);
    return pessoa;
  }

  private TipoStatus criaDadosTipoStatus() {
    TipoStatus tipoStatus = new TipoStatus();
    tipoStatus.setDescStatus("TESTE");
    return tipoStatus;
  }
}
