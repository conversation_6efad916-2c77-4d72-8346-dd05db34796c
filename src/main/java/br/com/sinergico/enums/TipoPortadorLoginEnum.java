package br.com.sinergico.enums;

import lombok.Getter;

@Getter
public enum TipoPortadorLoginEnum {
  LEGADO_SIMPLES(RegraTipoPortadorEnum.SIMPLES),
  LEGADO_DOCUMENTO_ACESSO_PESSOA_ADICIONAL(RegraTipoPortadorEnum.PESSOA_ADICIONAL_PF_CONTA_PJ),
  LEGADO_DOCUMENTO_ACESSO_REPRESENTANTE_LEGAL(RegraTipoPortadorEnum.REPRESENTANTE_LEGAL),
  LEGADO_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  RESPONSAVEL(RegraTipoPortadorEnum.RESPONSAVEL),
  DEPENDENTE(RegraTipoPortadorEnum.DEPENDENTE),
  LOJISTA(RegraTipoPortadorEnum.REPRESENTANTE_LEGAL),
  FINANCIAL_WEB_BANKING(RegraTipoPortadorEnum.SIMPLES),
  FINANCIAL_WEB_BANKING_PJ(RegraTipoPortadorEnum.SIMPLES),
  RP3_WEB_BANKING(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  RP3_WEB_BANKING_PJ(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL),
  MULTICONTAS_VALLOO_PJ(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL),
  BRB_PDAF(RegraTipoPortadorEnum.PESSOA_ADICIONAL_PF_CONTA_PJ),
  BRB_SOCIAL(RegraTipoPortadorEnum.SIMPLES),
  BRB_BENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  BRB_PRE_PAGO(RegraTipoPortadorEnum.SIMPLES),
  BRB_NACAO_FLAMENGO(RegraTipoPortadorEnum.SIMPLES),
  BANESE_CORPORATIVO(RegraTipoPortadorEnum.CORPORATIVO),
  ENTREPAY_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  ENTREPAY_MULTIBENEFICIOS_PJ(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL),
  DISNUVII_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  DISNUVII_MULTIBENEFICIOS_PJ(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL),
  CVS_CESTAS_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  CVS_CESTAS_MULTIBENEFICIOS_PJ(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL),
  PROMOTIVA_CLUB_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  QISTA_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  TIRA_CHAPEU_MULTIBENEFICIOS(RegraTipoPortadorEnum.MULTIBENEFICIOS),
  TIRA_CHAPEU_MULTIBENEFICIOS_PJ(RegraTipoPortadorEnum.MULTIBENEFICIOS_REPRESENTANTE_LEGAL);

  final RegraTipoPortadorEnum regraTipoPortadorLoginEnum;

  TipoPortadorLoginEnum(RegraTipoPortadorEnum regraTipoPortadorLoginEnum) {
    this.regraTipoPortadorLoginEnum = regraTipoPortadorLoginEnum;
  }

  public static TipoPortadorLoginEnum getTipoLoginByName(String tipoLogin) {
    for (TipoPortadorLoginEnum tipo : TipoPortadorLoginEnum.values()) {
      if (tipo.name().equals(tipoLogin)) {
        return tipo;
      }
    }
    return null;
  }
}
