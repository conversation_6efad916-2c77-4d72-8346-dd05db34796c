package br.com.sinergico.vo;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class InfoContaTransacionalPixIssuerVO implements Serializable {

  private Long id;

  private Integer idInstituicaoPix;

  private String dvConta;

  private Integer agencia;

  private String dvAgencia;

  private BigDecimal limiteDiurnoMaximo;

  private BigDecimal limiteDiurnoPorTransacao;

  private BigDecimal limiteNoturnoMaximo;

  private BigDecimal limiteNoturnoPorTransacao;
}
