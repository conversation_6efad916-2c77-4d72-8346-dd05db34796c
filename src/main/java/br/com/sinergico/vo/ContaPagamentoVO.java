package br.com.sinergico.vo;

import br.com.entity.cadastral.ContaPagamento;
import br.com.json.bean.suporte.GetSaldoConta;
import br.com.sinergico.enums.TipoProdutoEnum;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContaPagamentoVO implements Serializable {

  private Long idConta;
  private Integer idProcessadora;
  private Integer idInstituicao;
  private Integer idRegional;
  private Integer idFilial;
  private Integer idPontoDeRelacionamento;
  private Integer idProdutoInstituicao;
  private String descProdutoInstituicao;
  private Integer hierarquiaGrupo;
  private TipoProdutoEnum tipoProduto;
  private GetSaldoConta saldoConta;
  private BigDecimal tarifaPrimeiraVia;
  private BigDecimal tarifaSegundaVia;

  public ContaPagamentoVO(
      Long idConta,
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento,
      Integer idProdutoInstituicao) {
    this.idConta = idConta;
    this.idProcessadora = idProcessadora;
    this.idInstituicao = idInstituicao;
    this.idRegional = idRegional;
    this.idFilial = idFilial;
    this.idPontoDeRelacionamento = idPontoDeRelacionamento;
    this.idProdutoInstituicao = idProdutoInstituicao;
  }

  public ContaPagamentoVO(ContaPagamento contaPagamento) {
    this.idConta = contaPagamento.getIdConta();
    this.idProcessadora = contaPagamento.getIdProcessadora();
    this.idInstituicao = contaPagamento.getIdInstituicao();
    this.idRegional = contaPagamento.getIdRegional();
    this.idFilial = contaPagamento.getIdFilial();
    this.idPontoDeRelacionamento = contaPagamento.getIdPontoDeRelacionamento();
    this.idProdutoInstituicao = contaPagamento.getIdProdutoInstituicao();
    this.descProdutoInstituicao = contaPagamento.getProdutoInstituicao().getDescProdInstituicao();
    this.hierarquiaGrupo =
        contaPagamento.getProdutoInstituicao().getProdutosInstituicaoGrupoProdutos() != null
                && !contaPagamento
                    .getProdutoInstituicao()
                    .getProdutosInstituicaoGrupoProdutos()
                    .isEmpty()
            ? contaPagamento
                .getProdutoInstituicao()
                .getProdutosInstituicaoGrupoProdutos()
                .get(0)
                .getPosicaoHierarquia()
            : null;
    this.tipoProduto =
        contaPagamento.getProdutoInstituicao().getProdutoInstituicaoConfiguracao() != null
                && !contaPagamento
                    .getProdutoInstituicao()
                    .getProdutoInstituicaoConfiguracao()
                    .isEmpty()
            ? contaPagamento
                .getProdutoInstituicao()
                .getProdutoInstituicaoConfiguracao()
                .get(0)
                .getTipoProduto()
            : null;
  }
}
