package br.com.sinergico.service.suporte;

import static br.com.sinergico.util.MyHibernateUtils.listAndCast;

import br.com.client.rest.jcard.json.bean.PontoRelacionamentoResponse;
import br.com.client.rest.totvs.json.bean.response.PreRegistroEmpresaContaResponse;
import br.com.entity.cadastral.ParceiroAcumulo;
import br.com.entity.cadastral.ProdutoCondicoes;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.cadastral.ProdutoInstituicaoCorrespondente;
import br.com.entity.cadastral.SetorFilial;
import br.com.entity.suporte.AcessoGrupo;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.CadastroPontoRelacionamento;
import br.com.entity.suporte.Contato;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.LogHabilitaPedido;
import br.com.entity.suporte.LogUltimasEmpresas;
import br.com.entity.suporte.ParametroDefinicao;
import br.com.entity.suporte.ParametroValor;
import br.com.entity.transacional.B2bTarifa;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.MissingFieldsFilledException;
import br.com.json.bean.cadastral.BuscaB2B;
import br.com.json.bean.cadastral.BuscaProdutosEmpresasReplicaveis;
import br.com.json.bean.suporte.AlterarStatusEmpresa;
import br.com.json.bean.suporte.CadastrarGrupo;
import br.com.json.bean.suporte.CadastrarPontoRelacionamento;
import br.com.json.bean.suporte.CreateLogUltimaEmpresa;
import br.com.json.bean.suporte.HabilitaPontoRelacionamentoRequest;
import br.com.json.bean.suporte.HierarquiaPontoDeRelacionamentoTO;
import br.com.json.bean.suporte.ProdutoContradoCliente;
import br.com.json.bean.suporte.ReplicarEmpresasB2B;
import br.com.json.bean.suporte.StatusB2B;
import br.com.sinergico.criteria.UsuarioCriteria;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.repository.suporte.HierarquiaPontoRelacionamentoRepository;
import br.com.sinergico.repository.transacional.B2bTarifasRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.ParceiroAcumuloService;
import br.com.sinergico.service.cadastral.ProdutoCondicoesService;
import br.com.sinergico.service.cadastral.ProdutoContratadoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoCorrespondenteService;
import br.com.sinergico.service.cadastral.SetorFilialService;
import br.com.sinergico.service.jcard.PontoRelacionamentoService;
import br.com.sinergico.service.loyalty.NotaDebitoService;
import br.com.sinergico.service.totvs.api.PreRegistroEmpresaTotvsService;
import br.com.sinergico.util.BoletoUtil;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Hierarquia;
import br.com.sinergico.util.MyHibernateUtils;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.PontoRelacionamentoVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.persistence.NoResultException;
import org.hibernate.Criteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class HierarquiaPontoRelacionamentoService
    extends GenericService<HierarquiaPontoDeRelacionamento, HierarquiaPontoDeRelacionamentoId> {

  private static final Logger log =
      LoggerFactory.getLogger(HierarquiaPontoRelacionamentoService.class);

  private HierarquiaPontoDeRelacionamento pr;
  private static final Integer ATIVO = 1;
  private static final int NIVEL_HIERARQUIA_PR = 5;
  private static final int GRUPO_PAI_MODELO_PR = 8;
  private static final int NIVEL_PONTO_RELACIONAMENTO = 5;
  private static final Integer PENDENTE_DOCUMENTOS = 8;
  private static final String PARAM_EMAIL_EMPRESA_VALIDACAO = "email.val.docs";
  private static final Integer SEM_GRUPO_PAI = 0;
  public static final long ID_GRUPO_PRODUTO_HOM = 39L;
  public static final long ID_GRUPO_PRODUTO_PROD = 5L;

  @Autowired private UsuarioCriteria usuarioCriteria;

  private HierarquiaPontoRelacionamentoRepository repo;

  @Autowired private ProdutoContratadoService produtoContratadoService;

  @Autowired private ProdutoCondicoesService produtoCondicoesService;

  @Autowired private ContatoService contatoService;

  @Autowired private SetorFilialService setorFilialService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private GrupoService grupoService;

  @Autowired private LogUltimasEmpresasService ultimasEmpresasService;

  @Autowired private HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private LogHabilitaPedidoService logHabilitaPedidoService;

  @Lazy @Autowired private ProdutoInstituicaoCorrespondenteService prodInstCorrespService;

  @Autowired private B2bTarifasRepository b2bTarifasRepository;

  @Autowired private ParametroValorService paramValorService;

  @Autowired private ParceiroAcumuloService parceiroAcumuloService;

  @Autowired private PontoRelacionamentoService pontoRelacionamentoService;

  @Autowired private PreRegistroEmpresaTotvsService preRegistroEmpresaTotvsService;

  @Autowired private NotaDebitoService notaDebitoService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private UtilService utilService;

  @Autowired private EmailService emailService;

  @Autowired
  public HierarquiaPontoRelacionamentoService(HierarquiaPontoRelacionamentoRepository repository) {
    super(repository);
    this.repo = repository;
  }

  @Transactional
  public HierarquiaPontoDeRelacionamento habilitaPontoDeRelacionamento(
      HabilitaPontoRelacionamentoRequest model, SecurityUser user) {
    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getIdRegional(),
            model.getIdFilial(),
            model.getIdPontoDeRelacionamento());
    HierarquiaPontoDeRelacionamento pontoDeRelacionamento = validaPontoDeRelacionamento(id);

    pontoDeRelacionamento.setHabilitaFaturar(model.getStatus());
    pontoDeRelacionamento = save(pontoDeRelacionamento);

    LogHabilitaPedido log = new LogHabilitaPedido();
    log.setIdUsuario(user.getIdUsuario().longValue());
    log.setDataAlteracao(Calendar.getInstance().getTime());
    log.setStatus(pontoDeRelacionamento.getHabilitaFaturar());
    log.setIdProcessadora(model.getIdProcessadora());
    log.setIdInstituicao(model.getIdInstituicao());
    log.setIdRegional(model.getIdRegional());
    log.setIdFilial(model.getIdFilial());
    log.setIdPontoRelacionamento(model.getIdPontoDeRelacionamento());
    logHabilitaPedidoService.save(log);

    return pontoDeRelacionamento;
  }

  private HierarquiaPontoDeRelacionamento validaPontoDeRelacionamento(
      HierarquiaPontoDeRelacionamentoId id) {
    HierarquiaPontoDeRelacionamento pontoDeRelacionamento = repo.findById(id).orElse(null);
    if (!Util.isNotNull(pontoDeRelacionamento)) {
      throw new GenericServiceException("Ponto de Relacionamento não encontrado");
    }
    return pontoDeRelacionamento;
  }

  @Transactional
  public CadastroPontoRelacionamento cadastrarPontoRelacionamento(
      CadastrarPontoRelacionamento model, SecurityUser user) {

    CadastroPontoRelacionamento cpr = new CadastroPontoRelacionamento();
    HierarquiaPontoDeRelacionamento pr = new HierarquiaPontoDeRelacionamento(model);

    setCadastroPontoRelacionamento(pr);

    switch (user.getHierarquiaType()) {
      case PROCESSADORA:
        if (isProcessadoraFieldsFilled()) {
          pr.getId().setIdProcessadora(user.getIdProcessadora());
        } else {
          throw new MissingFieldsFilledException();
        }
        break;

      case INSTITUICAO:
        if (isInstituicaoFieldsFillled()) {
          pr.getId().setIdProcessadora(user.getIdProcessadora());
          pr.getId().setIdInstituicao(user.getIdInstituicao());
        } else {
          throw new MissingFieldsFilledException();
        }
        break;

      case REGIONAL:
        if (isRegionalFieldsFiled()) {
          pr.getId().setIdProcessadora(user.getIdProcessadora());
          pr.getId().setIdInstituicao(user.getIdInstituicao());
          pr.getId().setIdRegional(user.getIdRegional());
        } else {
          throw new MissingFieldsFilledException();
        }
        break;
      case FILIAL:
        pr.getId().setIdProcessadora(user.getIdProcessadora());
        pr.getId().setIdInstituicao(user.getIdInstituicao());
        pr.getId().setIdRegional(user.getIdRegional());
        pr.getId().setIdFilial(user.getIdFilial());
        break;

      case PONTO_RELACIONAMENTO:
        throw new AccessDeniedException(
            "Você não tem permissão para cadastrar um ponto de relacionamento.");
      default:
        throw new AccessDeniedException(null);
    }

    if (findById(pr.getId()) != null) {
      throw new GenericServiceException(
          "Já existe um Ponto de Relacionamento cadastrado com estes dados.");
    }

    if (model.getDescricao() == null || model.getDescricao().isEmpty()) {
      throw new GenericServiceException("O campo descrição deve ser preenchido.");
    }

    String documento = model.getDocumento();

    pr.setIdUsuarioInclusao(user.getIdUsuario());
    pr.setDataHoraInclusao(LocalDateTime.now());

    // verifica se eh B2B
    if (documento != null && !documento.isEmpty() && model.getB2b()) {

      // INCLUIR ID_LAYOUT PADRAO
      pr.setIdLayout(
          hierarquiaInstituicaoService.getIdLayoutPadrao(
              model.getIdProcessadora(), model.getIdInstituicao()));

      pr.setDataHoraInclusao(LocalDateTime.now());
      BeanUtils.copyProperties(model, pr);
      pr.setIdUsuarioInclusao(model.getIdUsuario());

      if (model.getGrauDeRisco() != null) {
        pr.setGrauDeRisco(model.getGrauDeRisco());
      }

      if (model.getDataFundacao() != null) {
        pr.setDataFundacao(DateUtil.dateToLocalDateTime(model.getDataFundacao()));
      }

      pr.getId().setIdPontoDeRelacionamento(nextValSeqIdPontoRelacionamento(pr.getIdInstituicao()));
      pr.setIdPontoDeRelacionamento(pr.getId().getIdPontoDeRelacionamento());
      preparePontoDeRelacionamento(pr, user);
      pr = save(pr);

      List<ProdutoContradoCliente> produtosSelecionados = model.getProdutosSelecionados();

      if (produtosSelecionados == null || produtosSelecionados.isEmpty()) {
        throw new GenericServiceException("Cliente B2B precisa selecionar pelo menos um produto.");
      }

      List<Contato> contatos = model.getContatos();

      if (contatos == null || contatos.isEmpty()) {
        throw new GenericServiceException("Cliente B2B precisa preencher pelo menos um contato.");
      }

      for (Contato contato : contatos) {
        BeanUtils.copyProperties(pr, contato, getNullPropertyNames(pr));
        contato.setIdNivelHierarquia(NIVEL_HIERARQUIA_PR);
        contato.setIdUsuarioInclusao(model.getIdUsuario());
        contato.setStatus(ATIVO);
        contato.setDataHoraStatus(LocalDateTime.now());

        contatoService.save(contato);
      }

      // Salvar Setor Filial Padrão
      SetorFilial setorFilial = new SetorFilial();
      setorFilial.setDescSetorFilial("MATRIZ");
      setorFilial.setIdProcessadora(model.getIdProcessadora());
      setorFilial.setIdInstituicao(model.getIdInstituicao());
      setorFilial.setIdRegional(model.getIdRegional());
      setorFilial.setIdFilial(model.getIdFilial());
      setorFilial.setIdPontoRelacionamento(pr.getIdPontoDeRelacionamento());
      setorFilial.setCep(model.getCepSede());
      setorFilial.setLogradouro(model.getLogradouroSede());
      setorFilial.setNumero(model.getNumeroSede());
      setorFilial.setBairro(model.getBairroSede());
      setorFilial.setCidade(model.getCidadeSede());
      setorFilial.setComplemento(model.getComplementoSede());
      setorFilial.setUf(model.getUfSede());
      setorFilial.setContato(model.getContatos().get(0).getNomeContato());
      setorFilialService.save(setorFilial);

      if (setorFilial.getIdSetorFilial() != null) {
        cpr.setSetorFilial(setorFilial.getIdSetorFilial());
      }

      // Salvar Grupo Padrão
      AcessoGrupo acessoGrupo = new AcessoGrupo();
      CadastrarGrupo cadastroGrupo = new CadastrarGrupo();
      cadastroGrupo.setIdNivelHierarquia(NIVEL_HIERARQUIA_PR);
      cadastroGrupo.setIdProcessadora(model.getIdProcessadora());
      cadastroGrupo.setIdInstituicao(model.getIdInstituicao());
      cadastroGrupo.setIdRegional(model.getIdRegional());
      cadastroGrupo.setIdFilial(model.getIdFilial());
      cadastroGrupo.setIdPontoDeRelacionamento(pr.getIdPontoDeRelacionamento());
      cadastroGrupo.setDescGrupo("ADMINISTRADOR B2B - " + pr.getIdPontoDeRelacionamento());

      AcessoGrupo grupoPai;
      if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL.equals(model.getIdInstituicao())) {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B_INMAIS);
      } else if (Constantes.ID_PRODUCAO_INSTITUICAO_FANBANK.equals(model.getIdInstituicao())) {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B_FANBANK);
      } else if (Constantes.ID_PRODUCAO_INSTITUICAO_PAXPAY.equals(model.getIdInstituicao())) {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B_PAXPAY);
      } else if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
          model.getIdInstituicao())) {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B_INFINANCAS);
      } else if (Constantes.ID_PRODUCAO_INSTITUICAO_MULVI.equals(model.getIdInstituicao())) {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B_MULVI);
      } else if (Constantes.ID_PRODUCAO_INSTITUICAO_BRBCARD.equals(model.getIdInstituicao())) {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B_BRBARD);
      } else {
        grupoPai = grupoService.findByIdGrupo(ConstantesB2B.GRUPO_MODELO_B2B);
      }

      cadastroGrupo.setIdGrupoPai(grupoPai.getIdGrupo());

      grupoService.prepareGrupo(cadastroGrupo, acessoGrupo, user);

      PontoRelacionamentoResponse pontoRelacionamentoResponse =
          pontoRelacionamentoService.createPontoRelacionamento(pr);
      if (!pontoRelacionamentoResponse.getSuccess()) {
        throw new GenericServiceException("Erro ao salvar a configuração.");
      }
      pr.setIdCompany(pontoRelacionamentoResponse.getId());
      this.save(pr);

      Boolean replicarEmpresaBank10 = false;
      List<Long> idContratosReplicaveis = new ArrayList<>();
      for (ProdutoContradoCliente produto : produtosSelecionados) {
        boolean replicaEste = false;
        if (prodInstCorrespService.findIfExistsCorresp(produto.getIdProdInstituicao())) {
          replicarEmpresaBank10 = Boolean.TRUE;
          replicaEste = true;
        }
        ProdutoContratado produtoContratado =
            produtoContratadoService.contratarProdutoPontoRelacionamento(model, pr, produto);
        if (replicaEste) {
          idContratosReplicaveis.add(produtoContratado.getIdContrato());
        }
      }

      if (hierarquiaInstituicaoService.instituicaoEnviaDadosTotvs(
          pr.getIdProcessadora(), pr.getIdInstituicao())) {
        PreRegistroEmpresaContaResponse preRegistroTotvsResponse =
            preRegistroEmpresaTotvsService.preRegistrarEmpresaTotvs(pr);
        if (!preRegistroTotvsResponse.getSucesso()) {
          log.info(
              ConstantesErro.TOT_FALHA_PRE_REGISTRO.format(
                  "EMPRESA", preRegistroTotvsResponse.getErros()));
        }
      }

      // Salvar parceiro Acumulo Padrão
      // salvarParceiroAcumulo(model);

      if (replicarEmpresaBank10) {
        List<BuscaProdutosEmpresasReplicaveis> list = buscarEmpresasReplicaveis();
        List<Long> idContratos =
            list.stream()
                .map(BuscaProdutosEmpresasReplicaveis::getIdContrato)
                .collect(Collectors.toList());
        for (Long idContratoReplicavel : idContratosReplicaveis) {
          if (!idContratos.contains(idContratoReplicavel)) {
            idContratos.add(idContratoReplicavel);
          }
        }
        ReplicarEmpresasB2B replicarEmpresas = new ReplicarEmpresasB2B();
        replicarEmpresas.setIdContratos(idContratos);
        AcessoUsuario usuario =
            acessoUsuarioService.findByIdUsuario(Constantes.ID_USUARIO_REPLICADOR);
        SecurityUser userReplicador = new SecurityUser(usuario);
        replicarEmpresasB2B(replicarEmpresas, userReplicador);
      }

    } else {
      pr.setB2b(false);
      criarGrupoPontoRelacionamento(pr);
      pr = save(pr);
    }

    cpr.setId(pr.getId());

    return cpr;
  }

  private void criarGrupoPontoRelacionamento(HierarquiaPontoDeRelacionamento pr) {

    AcessoGrupo acessoGrupo = new AcessoGrupo();
    acessoGrupo.setIdProcessadora(pr.getId().getIdProcessadora());
    acessoGrupo.setIdInstituicao(pr.getId().getIdInstituicao());
    acessoGrupo.setIdRegional(pr.getId().getIdRegional());
    acessoGrupo.setIdFilial(pr.getId().getIdFilial());
    acessoGrupo.setIdPontoDeRelacionamento(pr.getId().getIdPontoDeRelacionamento());
    acessoGrupo.setIdNivelHierarquia(NIVEL_PONTO_RELACIONAMENTO);
    acessoGrupo.setDescGrupo(
        "GRUPO ADM PR - " + pr.getId().getIdPontoDeRelacionamento().toString());

    acessoGrupo.setIdGrupoPai(
        grupoService.findGrupoAdmHierarquiaAnterior(
            acessoGrupo.getIdNivelHierarquia(),
            acessoGrupo.getIdProcessadora(),
            acessoGrupo.getIdInstituicao(),
            acessoGrupo.getIdRegional(),
            acessoGrupo.getIdFilial(),
            acessoGrupo.getIdPontoDeRelacionamento()));

    acessoGrupo = grupoService.save(acessoGrupo);
  }

  /**
   * Metodo responsavel por verificar os produtos disponiveis para contratacao
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  public List<ProdutoCondicoes> getProdutosPassiveisDeContratacao(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {

    boolean produtoContratadoContaLivre = false;
    List<ProdutoCondicoes> produtosDisponiveis = new ArrayList<>();

    List<ProdutoCondicoes> produtosCondicoes =
        produtoCondicoesService.findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    List<ProdutoContratado> produtosContratadosAtivos =
        produtoContratadoService.getProdutosContratadosAtivos(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);

    if (produtosCondicoes == null || produtosCondicoes.isEmpty()) {
      throw new GenericServiceException(
          "Instituição não possui produtos com condições de contratação");
    }

    // se existe algum produto ja contratatado entao
    if (produtosContratadosAtivos != null && !produtosContratadosAtivos.isEmpty()) {

      // listas temporarias de inteiros
      List<Integer> produtosContratadosAtivosInt = new ArrayList<>();
      List<Integer> produtosCondicoesInt = new ArrayList<>();
      List<Integer> produtosDisponiveisInt = new ArrayList<>();

      /*
       * faco uma copia dos produtos condicoes para
       * uma lista temporaria de inteiros
       * para manipula-la mais a frente
       *
       */

      produtosCondicoes.forEach(
          pc -> {
            produtosCondicoesInt.add(pc.getIdProdInstituicao());
          });

      /*
       * faco uma copia dos produtos contratados ativos para uma
       * lista de inteiros temporaria para facilitar manipulacao
       * e usar o contains
       *
       */
      produtosContratadosAtivos.forEach(
          pca -> {
            produtosContratadosAtivosInt.add(pca.getIdProdInstituicao());
          });

      /*
       * percorro a lista de inteiros de produtos condicoes
       * verificando se o produto condicao inteiro ainda nao foi contratado
       *
       */
      produtosCondicoesInt.forEach(
          pci -> {

            // se o produto condicao ainda nao foi contratado
            if (!produtosContratadosAtivosInt.contains(pci)) {

              // entao ele esta passivel de ser contratado
              produtosDisponiveisInt.add(pci);
            }
          });

      /*
       * Agora percorro todos os produtos condicoes reais
       */
      produtosCondicoes.forEach(
          pcon -> {
            /*e verifico se o idProdInstituicao esta dentro da lista de produtos disponiveis
             * porque se tiver entao esse é um produto passivel de contratacao e
             *
             */
            if (produtosDisponiveisInt.contains(pcon.getIdProdInstituicao())) {

              //  posso adiciona-lo na lista de produtos disponiveis que irei retornar

              produtosDisponiveis.add(pcon);
            }
          });

      // se nao existe produto contratado
    } else {
      // todos os produtos sao passiveis de contratacao
      produtosDisponiveis.addAll(produtosCondicoes);
    }

    List<ProdutoCondicoes> response = new ArrayList<>();

    // verifica se tem produto contratado do tipo conta livre
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(idInstituicao)) {
      for (ProdutoCondicoes produto : produtosCondicoes) {
        ProdutoContratado produtoContratado =
            produtoContratadoService.findProdutoContratadoPorIdGrupoETipoProduto(
                idProcessadora,
                idInstituicao,
                idRegional,
                idFilial,
                idPontoRelacionamento,
                produto.getIdProdInstituicao(),
                utilService.isAmbienteHomologacao() ? ID_GRUPO_PRODUTO_HOM : ID_GRUPO_PRODUTO_PROD,
                TipoProdutoEnum.CONTA_LIVRE);
        if (produtoContratado != null) {
          produtoContratadoContaLivre = true;
        }
      }
    }

    // verifica se o produto é b2b
    for (ProdutoCondicoes tmp : produtosDisponiveis) {
      ProdutoInstituicaoConfiguracao prodInstConf =
          produtoInstituicaoConfiguracaoService
              .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                  tmp.getIdProcessadora(), tmp.getIdProdInstituicao(), tmp.getIdInstituicao());

      tmp.setBlCorporativo(
          !Objects.isNull(prodInstConf) ? prodInstConf.getBlCorporativo() : Boolean.FALSE);

      if (tmp.getIsProdutoB2b()) {
        response.add(tmp);
      }

      if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(tmp.getIdInstituicao())) {
        tmp.setTipoProduto(prodInstConf.getTipoProduto());
        tmp.setIdGrupoProduto(prodInstConf.getIdGrupoProduto());
        if (!produtoContratadoContaLivre) {
          if (tmp.getIdGrupoProduto() != null
              && tmp.getIdGrupoProduto()
                  == (utilService.isAmbienteHomologacao()
                      ? ID_GRUPO_PRODUTO_HOM
                      : ID_GRUPO_PRODUTO_PROD)
              && tmp.getTipoProduto() != TipoProdutoEnum.CONTA_LIVRE) {
            response.remove(tmp);
          }
        }
      }
    }

    return response;
  }

  /**
   * Metodo que busca produtos contratados por um cliente b2b
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  public List<ProdutoContratado> getProdutosContratados(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    return produtoContratadoService.getProdutosContratadosAtivosEInativos(
        idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
  }

  private Integer countByDocumentoAndIdProcessadoraAndIdInstituicao(
      String documento, Integer idProcessadora, Integer idInstituicao) {
    return repo.countByDocumentoAndIdProcessadoraAndIdInstituicao(
        documento, idProcessadora, idInstituicao);
  }

  public List<Contato> getContatosHierarquia(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return contatoService.getContatosHierarquiaOrderByIdContato(
        idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  public HierarquiaPontoDeRelacionamento preparePontoDeRelacionamento(
      HierarquiaPontoDeRelacionamento pr, SecurityUser user) {

    new Hierarquia.Builder()
        .usuario(user)
        .nivel(user.getIdHierarquiaNivel())
        .processadora(pr.getId().getIdProcessadora())
        .instituicao(pr.getId().getIdInstituicao())
        .regional(pr.getId().getIdRegional())
        .filial(pr.getId().getIdFilial())
        .checkHierarquiaPermission()
        .verificaUnidadesHierarquia();

    preparePontoRelacionamentoWithoutCheckHierarq(pr);
    return pr;
  }

  private void preparePontoRelacionamentoWithoutCheckHierarq(HierarquiaPontoDeRelacionamento pr) {
    LocalDateTime now = LocalDateTime.now();
    pr.setDataHoraUltimaAtualizacao(now);
    if (pr.getStatus() == null) {
      pr.setStatus(PENDENTE_DOCUMENTOS);
      pr.setDataStatus(now);
    }
  }

  public void setCadastroPontoRelacionamento(HierarquiaPontoDeRelacionamento pr) {
    this.pr = pr;
  }

  private boolean isFilialFilled() {
    return pr.getId().getIdFilial() != null;
  }

  private boolean isRegionalFilled() {
    return pr.getId().getIdRegional() != null;
  }

  private boolean isInstituicaoFilled() {
    return pr.getId().getIdInstituicao() != null;
  }

  public boolean isProcessadoraFieldsFilled() {
    return isInstituicaoFilled() && isRegionalFilled() && isFilialFilled();
  }

  public boolean isInstituicaoFieldsFillled() {
    return isRegionalFilled() && isFilialFilled();
  }

  public boolean isRegionalFieldsFiled() {
    return isFilialFilled();
  }

  public Integer nextValSeqIdPontoRelacionamento(Integer idInstituicao) {
    // instituicao(4 posicoes) + sequence( 4 posicoes fixas-completar com
    // pad esquerda))
    // StringBuilder seq = new StringBuilder();
    // seq.append(idInstituicao);
    Integer nextValSeqIdPontoRelacionamento = repo.nextValSeqIdPontoRelacionamento();
    // seq.append(Strings.padStart(nextValSeqIdPontoRelacionamento.toString(),
    // MIN_TAM_ID_PONTO_RELACIONAMENTO,
    // CHAR_ZERO));

    return nextValSeqIdPontoRelacionamento;
  }

  @Override
  public List<HierarquiaPontoDeRelacionamento> findAll() {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaPontoDeRelacionamento.class, null, null);
    return listAndCast(criteria);
  }

  public List<HierarquiaPontoDeRelacionamento> getByInstPortadorAndPontoRelacionamento(
      Integer idInst,
      Integer idProc,
      Integer idReg,
      Integer idFilial,
      Integer idPontoRelacionamento) {
    List<HierarquiaPontoDeRelacionamento> prs =
        repo
            .findByIdInstituicaoAndIdProcessadoraAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoIsNotAndB2bFalseOrderByDescricao(
                idInst, idProc, idReg, idFilial, idPontoRelacionamento);
    return prs;
  }

  public List<HierarquiaPontoDeRelacionamento> getByFilial(
      Integer idProc, Integer idInst, Integer idReg, Integer idFilial) {
    List<HierarquiaPontoDeRelacionamento> prs =
        repo.findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilial(
            idProc, idInst, idReg, idFilial);
    Collections.sort(prs);
    return prs;
  }

  public List<HierarquiaPontoDeRelacionamento> getByFilialNotB2b(
      Integer idProc, Integer idInst, Integer idReg, Integer idFilial) {
    List<HierarquiaPontoDeRelacionamento> prs =
        repo.findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndB2bFalse(
            idProc, idInst, idReg, idFilial);
    Collections.sort(prs);
    return prs;
  }

  public List<BuscaB2B> buscarPontoRelacionamentoParametro(String termo, SecurityUser user) {
    List<BuscaB2B> retorno = null;

    try {
      Long.parseLong(termo);

      if (termo.length() == 11 || termo.length() == 14) {
        retorno = buscarEmpresaB2BPorDocumento(termo, user);

      } else {
        retorno = buscarEmpresaB2BPorId(Integer.parseInt(termo), user);
      }

      return retorno;

    } catch (NumberFormatException e) {
      retorno = buscarEmpresaB2BPorNome(termo, user);
      return retorno;
    }
  }

  public List<BuscaProdutosEmpresasReplicaveis> buscarEmpresasReplicaveis() {
    return repo.buscaEmpresasReplicaveis();
  }

  private List<BuscaB2B> buscarEmpresaB2BPorNome(String termo, SecurityUser user) {

    List<HierarquiaPontoDeRelacionamento> empresas = new ArrayList<>();

    if (user.getIdFilial() != null) {
      empresas =
          repo
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndDescricaoStartingWithIgnoreCaseAndB2b(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  termo,
                  true);
    } else if (user.getIdRegional() != null) {
      empresas =
          repo
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndDescricaoStartingWithIgnoreCaseAndB2b(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  termo,
                  true);
    } else if (user.getIdInstituicao() != null) {
      empresas =
          repo.findByIdProcessadoraAndIdInstituicaoAndDescricaoStartingWithIgnoreCaseAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), termo, true);
    } else {
      empresas =
          repo.findByIdProcessadoraAndDescricaoStartingWithIgnoreCaseAndB2b(
              user.getIdProcessadora(), termo, true);
    }

    List<BuscaB2B> lista = new ArrayList<>();

    if (empresas != null) {
      lista.addAll(prepareRetorno(empresas));
    }

    return lista;
  }

  private List<BuscaB2B> buscarEmpresaB2BPorId(Integer termo, SecurityUser user) {

    List<HierarquiaPontoDeRelacionamento> empresas = getAllB2BPorId(termo, user);

    List<BuscaB2B> lista = new ArrayList<>();

    if (empresas != null && !empresas.isEmpty()) {
      lista = prepareRetorno(empresas);
    }

    return lista;
  }

  private List<BuscaB2B> buscarEmpresaB2BPorDocumento(String termo, SecurityUser user) {
    List<HierarquiaPontoDeRelacionamento> empresas = null;

    if (user.getIdFilial() != null) {
      empresas =
          repo.findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndDocumentoAndB2b(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              termo,
              true);
    } else if (user.getIdRegional() != null) {
      empresas =
          repo.findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndDocumentoAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), user.getIdRegional(), termo, true);
    } else if (user.getIdInstituicao() != null) {
      empresas =
          repo.findByIdProcessadoraAndIdInstituicaoAndDocumentoAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), termo, true);
    } else {
      empresas = repo.findByIdProcessadoraAndDocumentoAndB2b(user.getIdProcessadora(), termo, true);
    }

    List<BuscaB2B> lista = new ArrayList<>();

    if (empresas != null) {
      lista.addAll(prepareRetorno(empresas));
    }
    return lista;
  }

  private List<BuscaB2B> prepareRetorno(List<HierarquiaPontoDeRelacionamento> empresas) {

    List<BuscaB2B> busca = new ArrayList<BuscaB2B>();

    for (HierarquiaPontoDeRelacionamento tmp : empresas) {
      BuscaB2B target = new BuscaB2B();
      target.setAtividadePrincipal(tmp.getAtividadePrincipal());
      target.setDataFundacao(tmp.getDataFundacao());
      target.setDataFundacaoStr(
          target.getDataFundacao() != null
              ? DateUtil.dateFormat(
                  "dd/MM/yyyy", DateUtil.localDateTimeToDate(tmp.getDataFundacao()))
              : null);
      target.setDescricao(tmp.getDescricao());
      target.setDocumento(tmp.getDocumento());
      target.setId(tmp.getId().getIdPontoDeRelacionamento());

      target.setIdProcessadora(tmp.getIdProcessadora());
      target.setIdInstituicao(tmp.getIdInstituicao());
      target.setIdRegional(tmp.getIdRegional());
      target.setIdFilial(tmp.getIdFilial());

      busca.add(target);
    }
    return busca;
  }

  public HierarquiaPontoDeRelacionamento buscarB2bPorChavesEstrangeiras(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return repo
        .findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento, true);
  }

  public HierarquiaPontoDeRelacionamento getB2BPorId(Integer id, SecurityUser user) {

    HierarquiaPontoDeRelacionamento empresa = null;

    HierarquiaPontoDeRelacionamentoTO to = null;

    if (user.getIdFilial() != null) {
      empresa =
          repo
              .findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  id,
                  true);
      to =
          repo.findSaldoB2BConvenio(
              user.getIdProcessadora(),
              user.getIdInstituicao(),
              user.getIdRegional(),
              user.getIdFilial(),
              id);

    } else if (user.getIdRegional() != null) {
      empresa =
          repo.findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdPontoDeRelacionamentoAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), user.getIdRegional(), id, true);

      to =
          repo.findSaldoB2BConvenio(
              user.getIdProcessadora(), user.getIdInstituicao(), user.getIdRegional(), null, id);

    } else if (user.getIdInstituicao() != null) {
      empresa =
          repo.findOneByIdProcessadoraAndIdInstituicaoAndIdPontoDeRelacionamentoAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), id, true);

      to =
          repo.findSaldoB2BConvenio(
              user.getIdProcessadora(), user.getIdInstituicao(), null, null, id);

    } else {
      empresa =
          repo.findOneByIdProcessadoraAndIdPontoDeRelacionamentoAndB2b(
              user.getIdProcessadora(), id, true);

      to = repo.findSaldoB2BConvenio(user.getIdProcessadora(), null, null, null, id);
    }

    if (empresa == null) {
      throw new GenericServiceException("Empresa não encontrada!");
    }

    if (to != null
        && empresa.getIdPontoDeRelacionamento().equals(to.getIdPontoDeRelacionamento())) {
      empresa.setLimiteConcedidoCredito(to.getLimiteConcedidoCredito());
      empresa.setDisponivelCredito(to.getDisponivelCredito());
      empresa.setTemConvenio(Boolean.TRUE);
    }

    return empresa;
  }

  public List<HierarquiaPontoDeRelacionamento> getAllB2BPorId(Integer id, SecurityUser user) {

    List<HierarquiaPontoDeRelacionamento> empresas = null;

    if (user.getIdFilial() != null) {
      empresas =
          repo
              .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
                  user.getIdProcessadora(),
                  user.getIdInstituicao(),
                  user.getIdRegional(),
                  user.getIdFilial(),
                  id,
                  true);

    } else if (user.getIdRegional() != null) {
      empresas =
          repo.findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdPontoDeRelacionamentoAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), user.getIdRegional(), id, true);

    } else if (user.getIdInstituicao() != null) {
      empresas =
          repo.findByIdProcessadoraAndIdInstituicaoAndIdPontoDeRelacionamentoAndB2b(
              user.getIdProcessadora(), user.getIdInstituicao(), id, true);

    } else {
      empresas =
          repo.findByIdProcessadoraAndIdPontoDeRelacionamentoAndB2b(
              user.getIdProcessadora(), id, true);
    }

    if (empresas == null) {
      throw new GenericServiceException("Empresa não encontrada!");
    }

    return empresas;
  }

  public List<HierarquiaPontoDeRelacionamento> findAll(String termo, Integer first, Integer max) {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaPontoDeRelacionamento.class, max, first);
    criteria.add(Restrictions.eq("b2b", false));
    criteria.addOrder(Order.asc("idInstituicao"));
    if (termo != null) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        criteria.add(Restrictions.in("idPontoDeRelacionamento", termoListInteiros));
      } else {
        criteria.add(Restrictions.ilike("descricao", "%" + termo + "%"));
      }
    }
    List<HierarquiaPontoDeRelacionamento> result = listAndCast(criteria);
    return result;
  }

  public Integer countBy(String termo) {
    Criteria criteria =
        usuarioCriteria.getHierarquiaCriteria(HierarquiaPontoDeRelacionamento.class, null, null);
    if (termo != null) {
      if (termo.matches("^\\s*\\d+(\\s*,\\s*\\d+)*\\s*$")) {
        String[] termoArray = termo.trim().split("\\s*,\\s*");
        if (termoArray.length > 0) {
          String ultimoId = termoArray[termoArray.length - 1];
          if (ultimoId.isEmpty()) {
            termo = termo.substring(0, termo.lastIndexOf(','));
            termoArray = termo.trim().split("\\s*,\\s*");
          }
        }
        List<String> termoList = Arrays.asList(termoArray);
        List<Integer> termoListInteiros =
            termoList.stream().map(Integer::valueOf).collect(Collectors.toList());
        criteria.add(Restrictions.in("idPontoDeRelacionamento", termoListInteiros));
      } else {
        criteria.add(Restrictions.ilike("descricao", "%" + termo + "%"));
      }
    }
    criteria.setProjection(Projections.rowCount());
    criteria.add(Restrictions.eq("b2b", false));
    return MyHibernateUtils.countRegisters(criteria);
  }

  public void saveLogUltimasEmpresas(CreateLogUltimaEmpresa model, SecurityUser user) {

    LogUltimasEmpresas log = new LogUltimasEmpresas();

    log =
        ultimasEmpresasService.findByIdUsuarioAndHierarquiaPr(
            user.getIdUsuario(),
            model.getIdProcessadora(),
            model.getIdInstituicao(),
            model.getIdRegional(),
            model.getIdFilial(),
            model.getIdPontoDeRelacionamento());

    if (log == null) {
      log = new LogUltimasEmpresas();
      log.setIdUsuario(user.getIdUsuario());
      log.setIdProcessadora(model.getIdProcessadora());
      log.setIdInstituicao(model.getIdInstituicao());
      log.setIdRegional(model.getIdRegional());
      log.setIdFilial(model.getIdFilial());
      log.setIdPontoDeRelacionamento(model.getIdPontoDeRelacionamento());
    }

    log.setDtHrUltimaPesquisa(new Date());
    log.setDadosB2b(
        DateUtil.dateFormat("dd/MM/yyyy HH:mm:ss", log.getDtHrUltimaPesquisa())
            + " - "
            + model.getDescricao()
            + " - "
            + model.getIdPontoDeRelacionamento());

    try {
      log = ultimasEmpresasService.save(log);
    } catch (Exception e) {
      throw new GenericServiceException("Impossível de salvar o log da última empresa acessada!");
    }
  }

  public List<LogUltimasEmpresas> getUltimasEmpresasAcessadas(SecurityUser user) {
    return ultimasEmpresasService.findTop30ByIdUsuarioOrderByDtHrUltimaPesquisaDesc(
        user.getIdUsuario());
  }

  public Boolean existeB2bByDocumento(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return countByDocumentoAndIdProcessadoraAndIdInstituicao(
            documento, idProcessadora, idInstituicao)
        > 0;
  }

  public List<B2bTarifa> findMovimentosNaoFaturadosByPontoDeRelacionamentoId(
      Integer idPontRel, SecurityUser user) {
    HierarquiaPontoDeRelacionamento b2b = getB2BPorId(idPontRel, user);
    return b2bTarifasRepository.buscarTarifasNaoFaturadas(
        b2b.getIdProcessadora(),
        b2b.getIdInstituicao(),
        b2b.getIdRegional(),
        b2b.getIdPontoDeRelacionamento());
  }

  public ResponseEntity<HashMap<String, Object>> getB2bByHierarquia(
      BuscaB2B buscaB2B, SecurityUser user) {

    HashMap<String, Object> map = new HashMap<>();

    if (validarHierarquiaEmpresaUsuario(buscaB2B, user)) {

      HierarquiaPontoDeRelacionamento hpr =
          repo
              .findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamentoAndB2b(
                  buscaB2B.getIdProcessadora(),
                  buscaB2B.getIdInstituicao(),
                  buscaB2B.getIdRegional(),
                  buscaB2B.getIdFilial(),
                  buscaB2B.getId(),
                  true);

      List<ProdutoContratado> produtoContratadoList = new ArrayList<>();
      for (ProdutoContratado pc : hpr.getProdutosContratados()) {
        ProdutoInstituicaoConfiguracao prodInstConf =
            produtoInstituicaoConfiguracaoService
                .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                    hpr.getIdProcessadora(), pc.getIdProdInstituicao(), hpr.getIdInstituicao());

        pc.setTipoProduto(prodInstConf.getTipoProduto());
        pc.setIdGrupoProduto(prodInstConf.getIdGrupoProduto());

        produtoContratadoList.add(pc);
      }
      produtoContratadoList.sort(Comparator.comparing(ProdutoContratado::getDtContrato).reversed());
      hpr.setProdutosContratados(produtoContratadoList);

      map.put("pontoDeRelacionamento", hpr);
      return new ResponseEntity<>(map, HttpStatus.OK);
    }

    map.put("msg", "Ponto de Relacionamento não encontrado!");
    return new ResponseEntity<>(map, HttpStatus.BAD_REQUEST);
  }

  private boolean validarHierarquiaEmpresaUsuario(BuscaB2B buscaB2B, SecurityUser user) {

    switch (user.getHierarquiaType()) {
      case PROCESSADORA:
        if (buscaB2B.getIdProcessadora().intValue() == user.getIdProcessadora().intValue()) {
          return true;
        }
        break;
      case INSTITUICAO:
        if (buscaB2B.getIdProcessadora().intValue() == user.getIdProcessadora().intValue()
            && buscaB2B.getIdInstituicao().intValue() == user.getIdInstituicao().intValue()) {
          return true;
        }
        break;
      case REGIONAL:
        if (buscaB2B.getIdProcessadora().intValue() == user.getIdProcessadora().intValue()
            && buscaB2B.getIdInstituicao().intValue() == user.getIdInstituicao().intValue()
            && buscaB2B.getIdRegional().intValue() == user.getIdRegional().intValue()) {
          return true;
        }
        break;
      case FILIAL:
        if (buscaB2B.getIdProcessadora().intValue() == user.getIdProcessadora().intValue()
            && buscaB2B.getIdInstituicao().intValue() == user.getIdInstituicao().intValue()
            && buscaB2B.getIdRegional().intValue() == user.getIdRegional().intValue()
            && buscaB2B.getIdFilial().intValue() == user.getIdFilial().intValue()) {
          return true;
        }
        break;
      case PONTO_RELACIONAMENTO:
        if (buscaB2B.getIdProcessadora().intValue() == user.getIdProcessadora().intValue()
            && buscaB2B.getIdInstituicao().intValue() == user.getIdInstituicao().intValue()
            && buscaB2B.getIdRegional().intValue() == user.getIdRegional().intValue()
            && buscaB2B.getIdFilial().intValue() == user.getIdFilial().intValue()
            && buscaB2B.getId().intValue() == user.getIdPontoDeRelacionamento().intValue()) {
          return true;
        }
        break;
    }
    return false;
  }

  @Transactional
  public Boolean replicarEmpresasB2BMigracao(ReplicarEmpresasB2B input, SecurityUser user) {
    validarQtyContrato(input);

    for (Long idContrato : input.getIdContratos()) {
      ProdutoContratado contrato = getProdutoContratadoNotNull(idContrato);
      ProdutoInstituicaoCorrespondente prodCorresp = getProdCorrespondenteNotNull(contrato);
      HierarquiaPontoDeRelacionamento empresaCriada = null;
      empresaCriada = cadastrarEmpresaReplicacaoMigracao(prodCorresp, contrato, user);
    }

    return true;
  }

  @Transactional
  public Boolean replicarEmpresasB2B(ReplicarEmpresasB2B input, SecurityUser user) {
    validarQtyContrato(input);

    for (Long idContrato : input.getIdContratos()) {
      ProdutoContratado contrato = getProdutoContratadoNotNull(idContrato);
      ProdutoInstituicaoCorrespondente prodCorresp = getProdCorrespondenteNotNull(contrato);

      HierarquiaPontoDeRelacionamento empresaBuscada = null;

      // o destino sera o bank10?
      if (prodCorresp.getIdInstituicaoDest() != null
          && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
              prodCorresp.getIdInstituicaoDest())) {
        empresaBuscada = findEmpresaIfExistsBank10(prodCorresp, contrato);

      } else {
        empresaBuscada = findEmpresaIfExistsInfinancas(prodCorresp, contrato);
      }
      HierarquiaPontoDeRelacionamento empresaCriada = null;

      if (empresaBuscada == null) {
        // se a empresa existir o sistema ja replica a empresa e contrata o produto
        empresaCriada = cadastrarEmpresaReplicacao(prodCorresp, contrato, user);
      } else {
        empresaCriada = empresaBuscada;
        replicarProdutoContratado(contrato, empresaCriada, prodCorresp);
      }
    }

    return true;
  }

  private HierarquiaPontoDeRelacionamento findEmpresaIfExistsBank10(
      ProdutoInstituicaoCorrespondente prodCorresp, ProdutoContratado contrato) {

    HierarquiaPontoDeRelacionamentoId idOrigem = new HierarquiaPontoDeRelacionamentoId();
    idOrigem.setIdProcessadora(contrato.getIdProcessadora());
    idOrigem.setIdInstituicao(contrato.getIdInstituicao());
    idOrigem.setIdRegional(contrato.getIdRegional());
    idOrigem.setIdFilial(contrato.getIdFilial());
    idOrigem.setIdPontoDeRelacionamento(contrato.getIdPontoRelacionamento());

    HierarquiaPontoDeRelacionamento origem = findById(idOrigem);

    if (origem != null
        && origem.getIdProcessadoraCorresp2() != null
        && origem.getIdInstituicaoCorresp2() != null
        && origem.getIdRegionalCorresp2() != null
        && origem.getIdFilialCorresp2() != null
        && origem.getIdPontoDeRelacionamentoCorresp2() != null
        && origem.getDataHoraReplicacao2() != null) {

      HierarquiaPontoDeRelacionamentoId idDestino = new HierarquiaPontoDeRelacionamentoId();
      idDestino.setIdProcessadora(origem.getIdProcessadoraCorresp2());
      idDestino.setIdInstituicao(origem.getIdInstituicaoCorresp2());
      idDestino.setIdRegional(origem.getIdRegionalCorresp2());
      idDestino.setIdFilial(origem.getIdFilialCorresp2());
      idDestino.setIdPontoDeRelacionamento(origem.getIdPontoDeRelacionamentoCorresp2());

      return findById(idDestino);
    }
    return null;
  }

  private ProdutoContratado replicarProdutoContratado(
      ProdutoContratado contrato,
      HierarquiaPontoDeRelacionamento empresaCriada,
      ProdutoInstituicaoCorrespondente prodCorresp) {
    ProdutoContratado prodContratado = new ProdutoContratado();
    BeanUtils.copyProperties(contrato, prodContratado, getNullPropertyNames(contrato));
    BeanUtils.copyProperties(empresaCriada, prodContratado, getNullPropertyNames(empresaCriada));
    prodContratado.setIdContrato(null);
    prodContratado.setIdUsuarioInclusao(contrato.getIdUsuarioInclusao());
    prodContratado.setIdProdInstituicao(prodCorresp.getIdProdInstituicaoDest());
    prodContratado.setIdPontoRelacionamento(empresaCriada.getIdPontoDeRelacionamento());

    prodContratado =
        produtoContratadoService.contratarProdutoReplicado(prodContratado, empresaCriada);
    atualizarProdContratadoOrigem(contrato, prodContratado);
    return prodContratado;
  }

  private void atualizarProdContratadoOrigem(
      ProdutoContratado contrato, ProdutoContratado prodContratado) {
    contrato.setIdContratoCorrespondente(prodContratado.getIdContrato());
    contrato.setDataHoraReplicacao(new Date());
    produtoContratadoService.save(contrato);
  }

  private HierarquiaPontoDeRelacionamento cadastrarEmpresaReplicacaoMigracao(
      ProdutoInstituicaoCorrespondente prodCorresp, ProdutoContratado contrato, SecurityUser user) {

    HierarquiaPontoDeRelacionamentoId idOrigem = new HierarquiaPontoDeRelacionamentoId();
    BeanUtils.copyProperties(contrato, idOrigem, getNullPropertyNames(contrato));
    idOrigem.setIdPontoDeRelacionamento(contrato.getIdPontoRelacionamento());
    HierarquiaPontoDeRelacionamento empresaOrigem = repo.findById(idOrigem).orElse(null);

    if (empresaOrigem == null) {
      throw new GenericServiceException("Empresa origem não encontrada!");
    }

    HierarquiaPontoDeRelacionamento empresaDestino = new HierarquiaPontoDeRelacionamento();

    BeanUtils.copyProperties(empresaOrigem, empresaDestino, getNullPropertyNames(empresaOrigem));

    HierarquiaPontoDeRelacionamentoId id = new HierarquiaPontoDeRelacionamentoId();
    id.setIdProcessadora(prodCorresp.getIdProcessadoraDest());
    id.setIdInstituicao(prodCorresp.getIdInstituicaoDest());
    id.setIdRegional(prodCorresp.getIdRegionalDest());
    id.setIdFilial(prodCorresp.getIdFilialDest());
    empresaDestino.setId(id);
    empresaDestino.setIdUsuarioInclusao(user.getIdUsuario());

    BeanUtils.copyProperties(id, empresaDestino, getNullPropertyNames(id));

    // INCLUIR ID_LAYOUT PADRAO
    empresaDestino.setIdLayout(
        hierarquiaInstituicaoService.getIdLayoutPadrao(
            prodCorresp.getIdProcessadoraDest(), prodCorresp.getIdInstituicaoDest()));
    empresaDestino.setDataHoraInclusao(LocalDateTime.now());

    empresaDestino
        .getId()
        .setIdPontoDeRelacionamento(
            nextValSeqIdPontoRelacionamento(empresaDestino.getId().getIdInstituicao()));
    empresaDestino.setIdPontoDeRelacionamento(empresaDestino.getId().getIdPontoDeRelacionamento());
    empresaDestino.setStatus(PENDENTE_DOCUMENTOS);

    preparePontoRelacionamentoWithoutCheckHierarq(empresaDestino);

    empresaDestino = save(empresaDestino);

    replicarProdutoContratado(contrato, empresaDestino, prodCorresp);

    List<Contato> contatos =
        contatoService.getContatosHierarquia(
            empresaOrigem.getIdProcessadora(),
            empresaOrigem.getIdInstituicao(),
            empresaOrigem.getIdRegional(),
            empresaOrigem.getIdFilial(),
            empresaOrigem.getIdPontoDeRelacionamento());

    if (contatos == null || contatos.isEmpty()) {
      throw new GenericServiceException("Empresa precisa preencher ter pelo menos um contato.");
    }

    for (Contato cont : contatos) {
      Contato contDest = new Contato();

      BeanUtils.copyProperties(cont, contDest, getNullPropertyNames(cont));
      BeanUtils.copyProperties(empresaDestino, contDest, getNullPropertyNames(empresaDestino));

      contDest.setIdContato(null);
      contDest.setIdUsuarioInclusao(user.getIdUsuario());
      contatoService.save(contDest);
    }

    // Salvar Setor Filial Padrão
    SetorFilial setorFilial = new SetorFilial();
    setorFilial.setDescSetorFilial("MATRIZ");
    setorFilial.setIdProcessadora(empresaDestino.getIdProcessadora());
    setorFilial.setIdInstituicao(empresaDestino.getIdInstituicao());
    setorFilial.setIdRegional(empresaDestino.getIdRegional());
    setorFilial.setIdFilial(empresaDestino.getIdFilial());
    setorFilial.setIdPontoRelacionamento(empresaDestino.getIdPontoDeRelacionamento());
    setorFilial.setCep(empresaDestino.getCepSede());
    setorFilial.setLogradouro(empresaDestino.getLogradouroSede());
    setorFilial.setNumero(empresaDestino.getNumeroSede());
    setorFilial.setBairro(empresaDestino.getBairroSede());
    setorFilial.setCidade(empresaDestino.getCidadeSede());
    setorFilial.setComplemento(empresaDestino.getComplementoSede());
    setorFilial.setUf(empresaDestino.getUfSede());
    setorFilial.setContato(contatos.get(0).getNomeContato());
    setorFilialService.save(setorFilial);

    // Salvar Grupo Padrão
    AcessoGrupo acessoGrupo = new AcessoGrupo();
    CadastrarGrupo cadastroGrupo = new CadastrarGrupo();
    cadastroGrupo.setIdNivelHierarquia(NIVEL_HIERARQUIA_PR);
    //			cadastroGrupo.setIdGrupoPai(GRUPO_PAI_MODELO_PR);
    cadastroGrupo.setIdProcessadora(empresaDestino.getIdProcessadora());
    cadastroGrupo.setIdInstituicao(empresaDestino.getIdInstituicao());
    cadastroGrupo.setIdRegional(empresaDestino.getIdRegional());
    cadastroGrupo.setIdFilial(empresaDestino.getIdFilial());
    cadastroGrupo.setIdPontoDeRelacionamento(empresaDestino.getIdPontoDeRelacionamento());
    cadastroGrupo.setDescGrupo(
        "ADMINISTRADOR B2B - " + empresaDestino.getIdPontoDeRelacionamento());

    AcessoGrupo grupoPai =
        grupoService.buscaGrupoPaiB2B(
            NIVEL_HIERARQUIA_PR, SEM_GRUPO_PAI, empresaDestino.getIdInstituicao());

    cadastroGrupo.setIdGrupoPai(
        grupoPai.getIdGrupo() == null ? GRUPO_PAI_MODELO_PR : grupoPai.getIdGrupo());

    grupoService.prepareGrupoWithoutCheckHierarquia(cadastroGrupo, acessoGrupo);

    atualizarEmpresaOrigem(empresaOrigem, empresaDestino);
    enviarEmailAvisoDocumentacaoPendente(empresaDestino);

    return empresaDestino;
  }

  private HierarquiaPontoDeRelacionamento cadastrarEmpresaReplicacao(
      ProdutoInstituicaoCorrespondente prodCorresp, ProdutoContratado contrato, SecurityUser user) {

    HierarquiaPontoDeRelacionamentoId idOrigem = new HierarquiaPontoDeRelacionamentoId();
    BeanUtils.copyProperties(contrato, idOrigem, getNullPropertyNames(contrato));
    idOrigem.setIdPontoDeRelacionamento(contrato.getIdPontoRelacionamento());
    HierarquiaPontoDeRelacionamento empresaOrigem = repo.findById(idOrigem).orElse(null);

    if (empresaOrigem == null) {
      throw new GenericServiceException("Empresa origem não encontrada!");
    }

    HierarquiaPontoDeRelacionamento empresaDestino = new HierarquiaPontoDeRelacionamento();

    BeanUtils.copyProperties(empresaOrigem, empresaDestino, getNullPropertyNames(empresaOrigem));

    HierarquiaPontoDeRelacionamentoId id = new HierarquiaPontoDeRelacionamentoId();
    id.setIdProcessadora(prodCorresp.getIdProcessadoraDest());
    id.setIdInstituicao(prodCorresp.getIdInstituicaoDest());
    id.setIdRegional(prodCorresp.getIdRegionalDest());
    id.setIdFilial(prodCorresp.getIdFilialDest());
    empresaDestino.setId(id);
    empresaDestino.setIdUsuarioInclusao(user.getIdUsuario());

    BeanUtils.copyProperties(id, empresaDestino, getNullPropertyNames(id));

    // INCLUIR ID_LAYOUT PADRAO
    empresaDestino.setIdLayout(
        hierarquiaInstituicaoService.getIdLayoutPadrao(
            prodCorresp.getIdProcessadoraDest(), prodCorresp.getIdInstituicaoDest()));
    empresaDestino.setDataHoraInclusao(LocalDateTime.now());

    empresaDestino
        .getId()
        .setIdPontoDeRelacionamento(
            nextValSeqIdPontoRelacionamento(empresaDestino.getId().getIdInstituicao()));
    empresaDestino.setIdPontoDeRelacionamento(empresaDestino.getId().getIdPontoDeRelacionamento());
    empresaDestino.setStatus(PENDENTE_DOCUMENTOS);

    preparePontoRelacionamentoWithoutCheckHierarq(empresaDestino);

    PontoRelacionamentoResponse pontoRelacionamentoResponse =
        pontoRelacionamentoService.createPontoRelacionamento(empresaDestino);
    if (!pontoRelacionamentoResponse.getSuccess()) {
      throw new GenericServiceException("Erro ao salvar a configuração.");
    }
    empresaDestino.setIdCompany(pontoRelacionamentoResponse.getId());
    empresaDestino = save(empresaDestino);

    replicarProdutoContratado(contrato, empresaDestino, prodCorresp);

    List<Contato> contatos =
        contatoService.getContatosHierarquia(
            empresaOrigem.getIdProcessadora(),
            empresaOrigem.getIdInstituicao(),
            empresaOrigem.getIdRegional(),
            empresaOrigem.getIdFilial(),
            empresaOrigem.getIdPontoDeRelacionamento());

    if (contatos != null && !contatos.isEmpty()) {
      for (Contato cont : contatos) {
        Contato contDest = new Contato();

        BeanUtils.copyProperties(cont, contDest, getNullPropertyNames(cont));
        BeanUtils.copyProperties(empresaDestino, contDest, getNullPropertyNames(empresaDestino));

        contDest.setIdContato(null);
        contDest.setIdUsuarioInclusao(user.getIdUsuario());
        contatoService.save(contDest);
      }
    }

    if (hierarquiaInstituicaoService.instituicaoEnviaDadosTotvs(
        empresaDestino.getIdProcessadora(), empresaDestino.getIdInstituicao())) {
      PreRegistroEmpresaContaResponse preRegistroTotvsResponse =
          preRegistroEmpresaTotvsService.preRegistrarEmpresaTotvs(empresaDestino);
      if (!preRegistroTotvsResponse.getSucesso()) {
        log.info(
            ConstantesErro.TOT_FALHA_PRE_REGISTRO.format(
                "EMPRESA", preRegistroTotvsResponse.getErros()));
      }
    }

    // Salvar Setor Filial Padrão
    SetorFilial setorFilial = new SetorFilial();
    setorFilial.setDescSetorFilial("MATRIZ");
    setorFilial.setIdProcessadora(empresaDestino.getIdProcessadora());
    setorFilial.setIdInstituicao(empresaDestino.getIdInstituicao());
    setorFilial.setIdRegional(empresaDestino.getIdRegional());
    setorFilial.setIdFilial(empresaDestino.getIdFilial());
    setorFilial.setIdPontoRelacionamento(empresaDestino.getIdPontoDeRelacionamento());
    setorFilial.setCep(empresaDestino.getCepSede());
    setorFilial.setLogradouro(empresaDestino.getLogradouroSede());
    setorFilial.setNumero(empresaDestino.getNumeroSede());
    setorFilial.setBairro(empresaDestino.getBairroSede());
    setorFilial.setCidade(empresaDestino.getCidadeSede());
    setorFilial.setComplemento(empresaDestino.getComplementoSede());
    setorFilial.setUf(empresaDestino.getUfSede());
    setorFilial.setContato(
        (contatos != null && (!contatos.isEmpty() && contatos.get(0).getNomeContato() != null))
            ? contatos.get(0).getNomeContato()
            : (empresaOrigem.getContatos() != null
                    && (!empresaOrigem.getContatos().isEmpty()
                        && empresaOrigem.getContatos().get(0).getNomeContato() != null))
                ? empresaOrigem.getContatos().get(0).getNomeContato()
                : "VALLOO BENEFICIOS");
    setorFilialService.save(setorFilial);

    // Salvar Grupo Padrão
    AcessoGrupo acessoGrupo = new AcessoGrupo();
    CadastrarGrupo cadastroGrupo = new CadastrarGrupo();
    cadastroGrupo.setIdNivelHierarquia(NIVEL_HIERARQUIA_PR);
    //			cadastroGrupo.setIdGrupoPai(GRUPO_PAI_MODELO_PR);
    cadastroGrupo.setIdProcessadora(empresaDestino.getIdProcessadora());
    cadastroGrupo.setIdInstituicao(empresaDestino.getIdInstituicao());
    cadastroGrupo.setIdRegional(empresaDestino.getIdRegional());
    cadastroGrupo.setIdFilial(empresaDestino.getIdFilial());
    cadastroGrupo.setIdPontoDeRelacionamento(empresaDestino.getIdPontoDeRelacionamento());
    cadastroGrupo.setDescGrupo(
        "ADMINISTRADOR B2B - " + empresaDestino.getIdPontoDeRelacionamento());

    AcessoGrupo grupoPai =
        grupoService.buscaGrupoPaiB2B(
            NIVEL_HIERARQUIA_PR, SEM_GRUPO_PAI, empresaDestino.getIdInstituicao());

    cadastroGrupo.setIdGrupoPai(
        grupoPai.getIdGrupo() == null ? GRUPO_PAI_MODELO_PR : grupoPai.getIdGrupo());

    grupoService.prepareGrupoWithoutCheckHierarquia(cadastroGrupo, acessoGrupo);

    atualizarEmpresaOrigem(empresaOrigem, empresaDestino);
    enviarEmailAvisoDocumentacaoPendente(empresaDestino);

    return empresaDestino;
  }

  @Async
  public void enviarEmailAvisoDocumentacaoPendente(HierarquiaPontoDeRelacionamento empresaDestino) {
    String email =
        getEmailToValidarDocs(
            empresaDestino.getIdProcessadora(), empresaDestino.getIdInstituicao());
    emailService.enviarEmailAvisoDocumentacaoPendenteEmpresa(empresaDestino, email);
  }

  private String getEmailToValidarDocs(Integer idProc, Integer idInstituicao) {

    ParametroDefinicao definicao = new ParametroDefinicao();
    definicao.setDescChaveParametro(PARAM_EMAIL_EMPRESA_VALIDACAO);

    ParametroValor valor = new ParametroValor();
    valor.setIdProcessadora(idProc);
    valor.setIdInstituicao(idInstituicao);

    List<ParametroValor> params = paramValorService.findParametros(definicao, valor);

    validaParams(params);
    return params.get(BoletoUtil.POSICAO_ZERO).getValorParametro();
  }

  private void validaParams(List<ParametroValor> params) {
    if (params == null || params.isEmpty()) {
      throw new NoResultException(
          " ParametroDefinicao ou ParametroValor não configurado corretamente: "
              + PARAM_EMAIL_EMPRESA_VALIDACAO);
    }
  }

  private void atualizarEmpresaOrigem(
      HierarquiaPontoDeRelacionamento empresaOrigem, HierarquiaPontoDeRelacionamento pr) {

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(pr.getIdInstituicao())) {
      empresaOrigem.setIdProcessadoraCorresp2(pr.getIdProcessadora());
      empresaOrigem.setIdInstituicaoCorresp2(pr.getIdInstituicao());
      empresaOrigem.setIdRegionalCorresp2(pr.getIdRegional());
      empresaOrigem.setIdFilialCorresp2(pr.getIdFilial());
      empresaOrigem.setIdPontoDeRelacionamentoCorresp2(pr.getIdPontoDeRelacionamento());
      empresaOrigem.setDataHoraReplicacao2(new Date());

    } else {
      empresaOrigem.setIdProcessadoraCorresp(pr.getIdProcessadora());
      empresaOrigem.setIdInstituicaoCorresp(pr.getIdInstituicao());
      empresaOrigem.setIdRegionalCorresp(pr.getIdRegional());
      empresaOrigem.setIdFilialCorresp(pr.getIdFilial());
      empresaOrigem.setIdPontoDeRelacionamentoCorresp(pr.getIdPontoDeRelacionamento());
      empresaOrigem.setDataHoraReplicacao(new Date());
    }
    save(empresaOrigem);
  }

  private HierarquiaPontoDeRelacionamento findEmpresaIfExistsInfinancas(
      ProdutoInstituicaoCorrespondente prodCorresp, ProdutoContratado contrato) {

    HierarquiaPontoDeRelacionamentoId idOrigem = new HierarquiaPontoDeRelacionamentoId();
    idOrigem.setIdProcessadora(contrato.getIdProcessadora());
    idOrigem.setIdInstituicao(contrato.getIdInstituicao());
    idOrigem.setIdRegional(contrato.getIdRegional());
    idOrigem.setIdFilial(contrato.getIdFilial());
    idOrigem.setIdPontoDeRelacionamento(contrato.getIdPontoRelacionamento());

    HierarquiaPontoDeRelacionamento origem = findById(idOrigem);

    if (origem != null
        && origem.getIdProcessadoraCorresp() != null
        && origem.getIdInstituicaoCorresp() != null
        && origem.getIdRegionalCorresp() != null
        && origem.getIdFilialCorresp() != null
        && origem.getIdPontoDeRelacionamentoCorresp() != null
        && origem.getDataHoraReplicacao() != null) {

      HierarquiaPontoDeRelacionamentoId idDestino = new HierarquiaPontoDeRelacionamentoId();
      idDestino.setIdProcessadora(origem.getIdProcessadoraCorresp());
      idDestino.setIdInstituicao(origem.getIdInstituicaoCorresp());
      idDestino.setIdRegional(origem.getIdRegionalCorresp());
      idDestino.setIdFilial(origem.getIdFilialCorresp());
      idDestino.setIdPontoDeRelacionamento(origem.getIdPontoDeRelacionamentoCorresp());

      return findById(idDestino);
    }
    return null;
  }

  private ProdutoInstituicaoCorrespondente getProdCorrespondenteNotNull(
      ProdutoContratado contrato) {
    List<ProdutoInstituicaoCorrespondente> produtosInstCorresp =
        prodInstCorrespService.findProdutoCorrespByIdInstituicaoAndIdProdInstOrig(
            contrato.getIdInstituicao(), contrato.getIdProdInstituicao());

    if (produtosInstCorresp == null || produtosInstCorresp.isEmpty()) {
      throw new GenericServiceException(
          "Nenhum produto correspondente encontrado para os dados informados. idInstituicao: "
              + contrato.getIdInstituicao()
              + " idProdInst: "
              + contrato.getIdProdInstituicao());
    }

    return produtosInstCorresp.get(0);
  }

  private ProdutoContratado getProdutoContratadoNotNull(Long idContrato) {
    ProdutoContratado contrato = produtoContratadoService.findById(idContrato);

    if (contrato == null) {
      throw new GenericServiceException(
          "ProdutoContratado não encontrado: idContrato: " + idContrato);
    }
    return contrato;
  }

  private void validarQtyContrato(ReplicarEmpresasB2B input) {
    if (input == null || input.getIdContratos() == null || input.getIdContratos().isEmpty()) {
      throw new GenericServiceException("Pelo menos um contrato deve ser enviado!");
    }
  }

  public List<StatusB2B> getPossiveisStatusEmpresaB2B(
      Integer idProc,
      Integer idInst,
      Integer idReg,
      Integer idFil,
      Integer idPontRel,
      SecurityUser user) {

    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(idProc, idInst, idReg, idFil, idPontRel);
    HierarquiaPontoDeRelacionamento hpr = getPontoRelacionamentoByIdNotNull(id);

    Map<Integer, List<StatusB2B>> opcoes = getOpcoesStatusDestino();

    return opcoes.get(hpr.getStatus());
  }

  private HierarquiaPontoDeRelacionamento getPontoRelacionamentoByIdNotNull(
      HierarquiaPontoDeRelacionamentoId id) {

    HierarquiaPontoDeRelacionamento hpr = repo.findById(id).orElse(null);

    if (hpr == null) {
      throw new GenericServiceException("Empresa não encontrada.");
    }
    return hpr;
  }

  private Map<Integer, List<StatusB2B>> getOpcoesStatusDestino() {
    Map<Integer, List<StatusB2B>> opcoes = new HashMap<>();
    opcoes.put(0, getDestinoBloqueioOrigem());
    opcoes.put(8, getDestinoPendenteDocumentacao());
    opcoes.put(1, getDestinoStatusAtivo());
    opcoes.put(3, getDestinoStatusBloqueio());
    opcoes.put(null, new ArrayList<>());
    return opcoes;
  }

  private List<StatusB2B> getDestinoStatusBloqueio() {
    List<StatusB2B> destino = new ArrayList<>();
    destino.add(new StatusB2B(1));
    destino.add(new StatusB2B(8));
    return destino;
  }

  private List<StatusB2B> getDestinoStatusAtivo() {
    List<StatusB2B> destino = new ArrayList<>();
    destino.add(new StatusB2B(3));
    destino.add(new StatusB2B(8));
    return destino;
  }

  private List<StatusB2B> getDestinoPendenteDocumentacao() {
    List<StatusB2B> destino = new ArrayList<>();
    destino.add(new StatusB2B(1));
    destino.add(new StatusB2B(3));
    return destino;
  }

  private List<StatusB2B> getDestinoBloqueioOrigem() {
    List<StatusB2B> destino = new ArrayList<>();
    destino.add(new StatusB2B(1));
    destino.add(new StatusB2B(3));
    return destino;
  }

  public Boolean alterarStatusB2B(AlterarStatusEmpresa input, SecurityUser user) {

    HierarquiaPontoDeRelacionamentoId id =
        new HierarquiaPontoDeRelacionamentoId(
            input.getIdProcessadora(),
            input.getIdInstituicao(),
            input.getIdRegional(),
            input.getIdFilial(),
            input.getIdPontoDeRelacionamento());
    HierarquiaPontoDeRelacionamento hpr = getPontoRelacionamentoByIdNotNull(id);
    Map<Integer, List<StatusB2B>> opcoes = getOpcoesStatusDestino();

    List<StatusB2B> opcoesDestino = opcoes.get(hpr.getStatus());

    if (!opcoesDestino.contains(new StatusB2B(input.getStatusDestino()))) {
      throw new GenericServiceException(
          "Não é possível alterar o status da empresa para o desejado!");
    }

    hpr.setStatus(input.getStatusDestino());
    hpr.setDataStatus(LocalDateTime.now());

    hpr = save(hpr);
    boolean sucesso = hpr != null;
    // salvar no jcard
    PontoRelacionamentoResponse pontoRelacionamentoResponse =
        pontoRelacionamentoService.updatePontoRelacionamento(hpr);
    if (!pontoRelacionamentoResponse.getSuccess()) {
      throw new GenericServiceException("Erro ao salvar a configuração.");
    }
    checkCorrespAlterarStatusB2B(hpr, sucesso);
    return true;
  }

  private void checkCorrespAlterarStatusB2B(HierarquiaPontoDeRelacionamento hpr, boolean sucesso) {
    if (sucesso
        && hpr.getIdProcessadoraCorresp2() != null
        && hpr.getIdInstituicaoCorresp2() != null
        && hpr.getIdRegionalCorresp2() != null
        && hpr.getIdFilialCorresp2() != null
        && hpr.getIdPontoDeRelacionamentoCorresp2() != null) {
      HierarquiaPontoDeRelacionamentoId idCorresp =
          new HierarquiaPontoDeRelacionamentoId(
              hpr.getIdProcessadoraCorresp2(),
              hpr.getIdInstituicaoCorresp2(),
              hpr.getIdRegionalCorresp2(),
              hpr.getIdFilialCorresp2(),
              hpr.getIdPontoDeRelacionamentoCorresp2());
      HierarquiaPontoDeRelacionamento hprCorresp = getPontoRelacionamentoByIdNotNull(idCorresp);
      hprCorresp.setStatus(hpr.getStatus());
      hprCorresp.setDataStatus(LocalDateTime.now());
      save(hprCorresp);
    }
  }

  public HierarquiaPontoDeRelacionamento getPontoRelacPorChave(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoDeRelacionamento) {
    return repo
        .findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoDeRelacionamento(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoDeRelacionamento);
  }

  public Boolean checaStatusEmpresasPermiteReplicacao(SecurityUser user) {
    return repo.checaStatusEmpresasPermiteReplicacao(
        user.getIdProcessadora(),
        user.getIdInstituicao(),
        user.getIdRegional(),
        user.getIdFilial(),
        user.getIdPontoDeRelacionamento());
  }

  private void salvarParceiroAcumulo(CadastrarPontoRelacionamento model) {
    ParceiroAcumulo parceiroAcumuloPadrao = new ParceiroAcumulo();
    String idParceiroAcumulo =
        model.getIdFilial().toString() + pr.getIdPontoDeRelacionamento().toString();
    parceiroAcumuloPadrao.setIdParceiroAcumulo(Integer.parseInt(idParceiroAcumulo));
    parceiroAcumuloPadrao.setDataHoraInclusao(LocalDateTime.now());
    parceiroAcumuloPadrao.setIdUsuarioInclusao(model.getIdUsuario());
    parceiroAcumuloPadrao.setIdProcessadora(model.getIdProcessadora());
    parceiroAcumuloPadrao.setIdInstituicao(model.getIdInstituicao());
    parceiroAcumuloPadrao.setIdRegional(model.getIdRegional());
    parceiroAcumuloPadrao.setIdFilial(model.getIdFilial());
    parceiroAcumuloPadrao.setIdPontoDeRelacionamento(pr.getIdPontoDeRelacionamento());
    parceiroAcumuloPadrao.setStatus(1); // ativo
    parceiroAcumuloPadrao.setDataHoraStatus(LocalDateTime.now());
    parceiroAcumuloPadrao.setTipoPagamento(1);
    parceiroAcumuloPadrao.setTipoAcumulo(0);
    parceiroAcumuloPadrao.setFatorRealParaPontos(new BigDecimal(1));
    parceiroAcumuloPadrao.setExpiracaoEmMeses(9999);
    parceiroAcumuloPadrao.setValorVendaPontos(new BigDecimal(0.01));
    parceiroAcumuloPadrao.setIdUsuarioManutencao(model.getIdUsuario());
    parceiroAcumuloPadrao.setCnpj(model.getDocumento());
    parceiroAcumuloPadrao.setCepParceiro(model.getCepSede());
    parceiroAcumuloPadrao.setLogradouroParceiro(model.getLogradouroSede());
    parceiroAcumuloPadrao.setNumeroEndParceiro(model.getNumeroSede());
    parceiroAcumuloPadrao.setComplementoEndParceiro(model.getComplementoSede());
    parceiroAcumuloPadrao.setBairroParceiro(model.getBairroSede());
    parceiroAcumuloPadrao.setCidadeParceiro(model.getCidadeSede());
    parceiroAcumuloPadrao.setUfParceiro(model.getUfSede());
    parceiroAcumuloPadrao.setCepFatura(model.getCepFaturamento());
    parceiroAcumuloPadrao.setLogradouroFatura(model.getLogradouroFaturamento());
    parceiroAcumuloPadrao.setNumeroEndFatura(model.getNumeroFaturamento());
    parceiroAcumuloPadrao.setComplementoEndFatura(model.getComplementoFaturamento());
    parceiroAcumuloPadrao.setBairroFatura(model.getBairroFaturamento());
    parceiroAcumuloPadrao.setCidadeFatura(model.getCidadeFaturamento());
    parceiroAcumuloPadrao.setUfFatura(model.getUfFaturamento());
    parceiroAcumuloPadrao.setDiasCorte(
        "01;02;03;04;05;06;07;08;09;10;11;12;13;14;15;16;17;18;19;20;21;22;23;24;25;26;27;28;29;30;31");
    parceiroAcumuloPadrao.setPrazoPagamento(1);
    parceiroAcumuloPadrao.setIdFormaRecebimento(2);
    parceiroAcumuloPadrao.setTipoPagamento(-1);
    parceiroAcumuloService.save(parceiroAcumuloPadrao);
    notaDebitoService.agendarNotaDebito(parceiroAcumuloPadrao, false);
  }

  public String checaNotificacaoEmpresa(Integer idEmpresa, Integer idInstituicao) {
    String resultado = null;
    List<ParametroValor> parametroValorList =
        paramValorService.getParametrosByDescricaoChaveParametro(
            Constantes.PARAMETRO_EMPRESA_NOTIFICACAO);
    ParametroValor parametroValorInstituicao = null;
    for (ParametroValor parametroValor : parametroValorList) {
      if (parametroValor.getIdInstituicao().equals(idInstituicao)) {
        parametroValorInstituicao = parametroValor;
      }
    }
    if (parametroValorInstituicao != null) {
      List<Integer> idsEmpresas = getIdsEmpresasFromParametro(parametroValorInstituicao);
      for (Integer empresa : idsEmpresas) {
        if (empresa.equals(idEmpresa)) {
          return paramValorService.getParametroByDescricaoChaveParametro(
              Constantes.PARAMETRO_EMPRESA_MENSAGEM);
        } else {
          resultado = "false";
        }
      }
    }
    return resultado;
  }

  private List<Integer> getIdsEmpresasFromParametro(ParametroValor parametroValor) {
    List<Integer> listToReturn = new ArrayList<>();
    for (String item : parametroValor.getValorParametro().split(",")) {
      listToReturn.add(Integer.valueOf(item));
    }
    return listToReturn;
  }

  public List<PontoRelacionamentoVO> getPontoRelacionamentoFromGrupoEmpresarial(
      Long idGrupoEmpresarial, Integer idInstituicao) {
    List<HierarquiaPontoDeRelacionamento> lista =
        repo.findHierarquiaPontoDeRelacionamentoByIdGrupoEmpresarialAndStatus(
            idGrupoEmpresarial, idInstituicao);
    List<PontoRelacionamentoVO> pontosRelacionamento = new ArrayList<>();
    for (HierarquiaPontoDeRelacionamento item : lista) {
      PontoRelacionamentoVO pontoRelacionamentoVO = new PontoRelacionamentoVO();
      pontoRelacionamentoVO.setId(item.getId().getIdPontoDeRelacionamento());
      pontoRelacionamentoVO.setNome(item.getDescricao());
      pontoRelacionamentoVO.setCnpj(item.getDocumento());
      pontosRelacionamento.add(pontoRelacionamentoVO);
    }
    return pontosRelacionamento;
  }
}
