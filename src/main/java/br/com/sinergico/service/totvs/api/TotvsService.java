package br.com.sinergico.service.totvs.api;

import static java.lang.Boolean.FALSE;

import br.com.client.rest.totvs.json.bean.TotvsResponse;
import br.com.client.rest.totvs.json.bean.response.NfsePdfTotvsApiResponse;
import br.com.client.rest.totvs.json.bean.response.NfseTotvsApiResponse;
import br.com.entity.cadastral.PreLancamentoLote;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.totvs.PreRegistro;
import br.com.entity.totvs.PreRegistroContato;
import br.com.entity.totvs.PreRegistroPedidosCarga;
import br.com.entity.totvs.PreRegistroProduto;
import br.com.sinergico.service.suporte.HierarquiaInstituicaoService;
import br.com.sinergico.service.totvs.PreRegistroContatoService;
import br.com.sinergico.service.totvs.PreRegistroPedidosCargaService;
import br.com.sinergico.service.totvs.PreRegistroProdutoService;
import br.com.sinergico.service.totvs.PreRegistroService;
import br.com.sinergico.util.SpringContextUtils;
import java.util.Date;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

@Service
public class TotvsService {

  @Autowired PreRegistroService preRegistroService;

  @Autowired PreRegistroContatoService preRegistroContatoService;

  @Autowired PreRegistroProdutoService preRegistroProdutoService;

  @Autowired PreRegistroPedidosCargaService preRegistroPedidosCargaService;

  @Autowired HierarquiaInstituicaoService hierarquiaInstituicaoService;

  @Autowired private RestTemplate restTemplateWithMappingConverter;

  public static final long ID_STATUS_NOVO_REGISTRO = 1L;

  private static final String UTF_8 = "UTF-8";
  private static final String TOTVS_TOKEN = "totvs.api.header.token";

  private static final String URL_NFSE = "/api/nota-fiscal/solicitar/";

  private static final String URL_NFSE_PDF = "/api/nota-fiscal/solicitar-pdf/";

  @Value("${totvs.api.url}")
  private String totvsUrl;

  protected final Logger LOG = LoggerFactory.getLogger(getClass());

  protected <Q, R extends TotvsResponse> R doPost(Q requestObj, R response, String uri) {
    return doAny(requestObj, response, uri, HttpMethod.POST);
  }

  protected <Q> HttpEntity<Q> getDefaultHttpEntityRequest(
      Q requestObj, MultiValueMap<String, String> headers) {
    return new HttpEntity(requestObj, headers);
  }

  protected <R extends TotvsResponse> R doGet(R response, String uri) {
    return doAny("", response, uri, HttpMethod.GET);
  }

  protected <Q, R extends TotvsResponse> R doPut(Q requestObj, R response, String uri) {
    return doAny(requestObj, response, uri, HttpMethod.PUT);
  }

  protected <Q, R extends TotvsResponse> R doDelete(Q requestObj, R response, String uri) {
    return doAny(requestObj, response, uri, HttpMethod.DELETE);
  }

  protected <Q, R extends TotvsResponse> R doAny(
      Q requestObj, R response, String uri, HttpMethod method) {
    try {

      MultiValueMap<String, String> headers = getDefaultHeaders();
      HttpEntity<Q> request = getDefaultHttpEntityRequest(requestObj, headers);
      ResponseEntity<? extends TotvsResponse> exchange =
          restTemplateWithMappingConverter.exchange(uri, method, request, response.getClass());
      response = (R) exchange.getBody();

    } catch (HttpClientErrorException | HttpServerErrorException e) {
      response.setSucesso(FALSE);
      response.setErros(e.getResponseBodyAsString());
      LOG.error(e.getMessage(), e);

    } catch (Exception e) {
      e.printStackTrace();
      response.setSucesso(FALSE);
      response.setErros(e.getMessage());
      LOG.error(e.getMessage(), e);
    }

    return response;
  }

  protected HttpEntity<String> getHttpEntityString() {
    return new HttpEntity(getDefaultHeaders());
  }

  protected MultiValueMap<String, String> getDefaultHeaders() {

    MultiValueMap<String, String> headers = new LinkedMultiValueMap();
    Environment env = SpringContextUtils.getBean(Environment.class);

    String totvsToken = env.getProperty(TOTVS_TOKEN);

    headers.add("X-API-Key", totvsToken);
    headers.add("Content-Type", "application/json");
    headers.add("charset", UTF_8);
    return headers;
  }

  protected String getUrlBaseTotvs() {
    if (totvsUrl == null) {
      Environment env = SpringContextUtils.getBean(Environment.class);
      return env.getProperty("totvs.api.url");
    }
    return totvsUrl;
  }

  protected Boolean enviarDadosParaTotvs(Integer idProcessadora, Integer idInstituicao) {
    return hierarquiaInstituicaoService.instituicaoEnviaDadosTotvs(idProcessadora, idInstituicao);
  }

  protected Boolean contatoPodeSerAtualizado(Long idPessoa, Long idContato) {
    PreRegistroContato preRegistroContato = null;
    if (idPessoa != null) {
      preRegistroContato = preRegistroContatoService.procuraPorIdPessoa(idPessoa);
    }
    if (idContato != null) {
      preRegistroContato = preRegistroContatoService.procuraPorIdContato(idContato);
    }
    return preRegistroContato != null
        && preRegistroContato.getIdTotvs() != null
        && !preRegistroContato.getIdTotvs().isEmpty();
  }

  protected Boolean produtoPodeSerCancelado(ProdutoContratado produtoContratado) {
    PreRegistro preRegistro =
        preRegistroService.buscarEmpresaPreRegistrada(produtoContratado.getIdPontoRelacionamento());
    PreRegistroProduto preRegistroProduto = null;
    if (preRegistro != null) {
      preRegistroProduto =
          preRegistroProdutoService.procurarPorIdPreRegistroEIdProdutoInstituicao(
              preRegistro.getId(), produtoContratado.getIdProdInstituicao());
    }
    return preRegistroProduto != null && preRegistroProduto.getDtHrSucesso() != null;
  }

  @Transactional
  public void incluirPreRegistroLoteTotvs(PreLancamentoLote lote) {
    Boolean enviaDadosTotvs =
        enviarDadosParaTotvs(lote.getIdProcessadora(), lote.getIdInstituicao());

    if (Boolean.TRUE.equals(enviaDadosTotvs)) {
      Boolean existePreRegistro =
          preRegistroPedidosCargaService.existePreRegistroPorLote(lote.getIdLote().longValue());

      if (!existePreRegistro) {
        PreRegistroPedidosCarga preRegistro = new PreRegistroPedidosCarga();
        preRegistro.setIdPedido(lote.getIdLote().longValue());
        preRegistro.setIdStatus(ID_STATUS_NOVO_REGISTRO);
        preRegistro.setDataHoraInclusao(new Date());

        preRegistroPedidosCargaService.salvarPreRegistroPedido(preRegistro);

        LOG.info("Pré-registro TOTVS criado com sucesso para o lote: {}", lote.getIdLote());
      } else {
        LOG.warn("Pré-registro TOTVS já existe para o lote: {}", lote.getIdLote());
      }
    }
  }

  public NfseTotvsApiResponse buscarNotaFiscalXML(Long idMovimento) {
    return doPost(null, new NfseTotvsApiResponse(), getUrlBaseTotvs() + URL_NFSE + idMovimento);
  }

  public NfsePdfTotvsApiResponse buscarNotaFiscalPdf(Long idMovimento) {
    return doPost(
        null, new NfsePdfTotvsApiResponse(), getUrlBaseTotvs() + URL_NFSE_PDF + idMovimento);
  }
}
