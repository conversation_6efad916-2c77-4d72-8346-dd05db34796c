package br.com.json.bean.suporte;

import br.com.entity.suporte.Contato;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.NotEmpty;

@ApiModel
public class CadastrarInstituicao {

  @NotNull
  @ApiModelProperty(required = true)
  private Integer idInstituicao;

  @NotNull @NotEmpty private String descInstituicao;

  private String cnpj;
  private String cep;
  private String bairro;
  private String cidade;
  private String numero;
  private String uf;
  private String logradouro;
  private String urlLogo;
  private String razaoSocial;
  // usuario tem ou não permissao para acessar a propria conta
  private Boolean acessoPropriaConta;
  private Boolean emiteNotaFiscal;

  private Integer optanteSimplesNacional;
  private Integer incentivoFiscal;
  private Integer codigoMunicipio;
  private String codigoTributacao;
  private BigDecimal aliquotaIss;
  private Integer issRetido;
  private BigDecimal multaBeneficio;
  private BigDecimal jurosBeneficio;
  private List<Contato> contatos;
  private String textoInfCentralAtendimento;
  private String emailReply;

  private String canalEmail;
  private Boolean habilitacaoGrupo;
  private Boolean redefinirSenhaCaf;
  private Integer metodoSegurancaTransacao;

  public CadastrarInstituicao() {}

  public CadastrarInstituicao(Integer idInstituicao, String descInstituicao) {
    this.descInstituicao = descInstituicao;
    //		this.idNivelHierarquia = idNivelHierarquia;
    this.idInstituicao = idInstituicao;
  }

  public CadastrarInstituicao setCanalEmail(String canalEmail) {
    this.canalEmail = canalEmail;
    return this;
  }

  public String getCanalEmail() {
    return canalEmail;
  }

  public String getEmailReply() {
    return emailReply;
  }

  public void setEmailReply(String emailReply) {
    this.emailReply = emailReply;
  }

  public String getDescInstituicao() {
    return descInstituicao;
  }

  public void setDescInstituicao(String descInstituicao) {
    this.descInstituicao = descInstituicao;
  }

  //	public Integer getIdNivelHierarquia() {
  //		return idNivelHierarquia;
  //	}
  //
  //	public void setIdNivelHierarquia(Integer idNivelHierarquia) {
  //		this.idNivelHierarquia = idNivelHierarquia;
  //	}

  public Integer getIdInstituicao() {
    return idInstituicao;
  }

  public void setIdInstituicao(Integer idInstituicao) {
    this.idInstituicao = idInstituicao;
  }

  public String getCnpj() {
    return cnpj;
  }

  public void setCnpj(String cnpj) {
    this.cnpj = cnpj;
  }

  public String getCep() {
    return cep;
  }

  public void setCep(String cep) {
    this.cep = cep;
  }

  public String getBairro() {
    return bairro;
  }

  public void setBairro(String bairro) {
    this.bairro = bairro;
  }

  public String getCidade() {
    return cidade;
  }

  public void setCidade(String cidade) {
    this.cidade = cidade;
  }

  public String getUf() {
    return uf;
  }

  public void setUf(String uf) {
    this.uf = uf;
  }

  public String getLogradouro() {
    return logradouro;
  }

  public void setLogradouro(String logradouro) {
    this.logradouro = logradouro;
  }

  public String getUrlLogo() {
    return urlLogo;
  }

  public void setUrlLogo(String urlLogo) {
    this.urlLogo = urlLogo;
  }

  public String getRazaoSocial() {
    return razaoSocial;
  }

  public void setRazaoSocial(String razaoSocial) {
    this.razaoSocial = razaoSocial;
  }

  public Boolean getAcessoPropriaConta() {
    return acessoPropriaConta;
  }

  public void setAcessoPropriaConta(Boolean acessoPropriaConta) {
    this.acessoPropriaConta = acessoPropriaConta;
  }

  public List<Contato> getContatos() {
    return contatos;
  }

  public void setContatos(List<Contato> contatos) {
    this.contatos = contatos;
  }

  public Boolean getEmiteNotaFiscal() {
    return emiteNotaFiscal;
  }

  public void setEmiteNotaFiscal(Boolean emiteNotaFiscal) {
    this.emiteNotaFiscal = emiteNotaFiscal;
  }

  public Integer getOptanteSimplesNacional() {
    return optanteSimplesNacional;
  }

  public void setOptanteSimplesNacional(Integer optanteSimplesNacional) {
    this.optanteSimplesNacional = optanteSimplesNacional;
  }

  public Integer getIncentivoFiscal() {
    return incentivoFiscal;
  }

  public void setIncentivoFiscal(Integer incentivoFiscal) {
    this.incentivoFiscal = incentivoFiscal;
  }

  public Integer getCodigoMunicipio() {
    return codigoMunicipio;
  }

  public void setCodigoMunicipio(Integer codigoMunicipio) {
    this.codigoMunicipio = codigoMunicipio;
  }

  public String getCodigoTributacao() {
    return codigoTributacao;
  }

  public void setCodigoTributacao(String codigoTributacao) {
    this.codigoTributacao = codigoTributacao;
  }

  public Integer getIssRetido() {
    return issRetido;
  }

  public void setIssRetido(Integer issRetido) {
    this.issRetido = issRetido;
  }

  public BigDecimal getAliquotaIss() {
    return aliquotaIss;
  }

  public void setAliquotaIss(BigDecimal aliquotaIss) {
    this.aliquotaIss = aliquotaIss;
  }

  public String getNumero() {
    return numero;
  }

  public void setNumero(String numero) {
    this.numero = numero;
  }

  public BigDecimal getMultaBeneficio() {
    return multaBeneficio;
  }

  public void setMultaBeneficio(BigDecimal multaBeneficio) {
    this.multaBeneficio = multaBeneficio;
  }

  public BigDecimal getJurosBeneficio() {
    return jurosBeneficio;
  }

  public void setJurosBeneficio(BigDecimal jurosBeneficio) {
    this.jurosBeneficio = jurosBeneficio;
  }

  public String getTextoInfCentralAtendimento() {
    return textoInfCentralAtendimento;
  }

  public void setTextoInfCentralAtendimento(String textoInfCentralAtendimento) {
    this.textoInfCentralAtendimento = textoInfCentralAtendimento;
  }

  public Boolean getHabilitacaoGrupo() {
    return habilitacaoGrupo;
  }

  public void setHabilitacaoGrupo(Boolean habilitacaoGrupo) {
    this.habilitacaoGrupo = habilitacaoGrupo;
  }

  public Boolean getRedefinirSenhaCaf() {
    return redefinirSenhaCaf;
  }

  public void setRedefinirSenhaCaf(Boolean redefinirSenhaCaf) {
    this.redefinirSenhaCaf = redefinirSenhaCaf;
  }

  public Integer getMetodoSegurancaTransacao() {
    return metodoSegurancaTransacao;
  }

  public void setMetodoSegurancaTransacao(Integer metodoSegurancaTransacao) {
    this.metodoSegurancaTransacao = metodoSegurancaTransacao;
  }
}
