package br.com.sinergico.service.transacional;

import br.com.client.rest.jcard.json.bean.JcardResponse;
import br.com.client.rest.jcard.json.bean.JcardResponseRrn;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.transacional.CargaBRBApi;
import br.com.json.bean.transacional.CadastroCargaBRBApi;
import br.com.sinergico.controller.UtilController;
import br.com.sinergico.events.EventoService;
import br.com.sinergico.repository.transacional.CargaBRBApiRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.cadastral.ContaPagamentoService;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.ProdutoInstituicaoConfiguracaoService;
import br.com.sinergico.service.jcard.LogTransacoesService;
import br.com.sinergico.service.suporte.TravaContasService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.vo.CargaBRBApiResponse;
import java.math.BigDecimal;
import java.time.DateTimeException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class CargaBRBApiService extends GenericService<CargaBRBApi, Long> {

  public static final int ID_USUARIO_APP_BRB_HOMOL = 1596;
  public static final int ID_USUARIO_APP_BRB_PROD = 4891;
  private static final int CODIGO_TRANSACAO_CARGA = 834;
  private static final int CODIGO_TRANSACAO_ESTORNO = 835;

  private static final int ARRANJO_VISA_PRE_NACIONAL = 107;
  private static final int ARRANJO_VISA_PRE_INTERNACIONAL = 108;
  private static final int ARRANJO_MASTER_PRE_NACIONAL = 113;
  private static final int ARRANJO_MASTER_PRE_INTERNACIONAL = 114;
  private static final List<Integer> ARRANJOS_PERMITIDOS =
      Arrays.asList(
          ARRANJO_VISA_PRE_NACIONAL,
          ARRANJO_VISA_PRE_INTERNACIONAL,
          ARRANJO_MASTER_PRE_NACIONAL,
          ARRANJO_MASTER_PRE_INTERNACIONAL);

  private static final String TEXTO_EXTRATO_CARGA = "CARGA DE VALORES";
  private static final String TEXTO_EXTRATO_ESTORNO = "ESTORNO DE CARGA DE VALORES";

  public static final Pattern IP_BRB_CARGA =
      Pattern.compile(
          ".*("
              + "172\\.16\\.88\\.254|"
              + "200\\.146\\.227\\.57|"
              + "187\\.72\\.42\\.225|"
              + "8\\.242\\.30\\.146|"
              + "8\\.242\\.29\\.237|"
              + "127\\.0\\.0\\.1"
              + ").*");
  // Mudar para regex de IP(s) de produção do BRB permitido(s)
  private static final List<Integer> ID_USUARIO_BRB_CARGA_HOMOLOGACAO =
      Collections.singletonList(ID_USUARIO_APP_BRB_HOMOL);
  private static final List<Integer> ID_USUARIO_BRB_CARGA_PRODUCAO =
      Collections.singletonList(ID_USUARIO_APP_BRB_PROD);
  // Mudar para ID(s) de usuário(s) BRB permitido(s)

  private static final String MENSAGEM_SUCESSO_CARGA = "Crédito realizado com sucesso.";
  private static final String MENSAGEM_UID_UTILIZADO =
      "Crédito já efetuado anteriormente."; // MENSAGEM PADRÃO DE REUSO DO UUID DE TRANSAÇÃO -
  // ALTERAR APENAS MEDIANTE CONTATO COM BRB
  private static final String MENSAGEM_CONTA_NAO_ENCONTRADA =
      "Crédito não realizado. Conta não encontrada.";
  private static final String MENSAGEM_VALOR_INVALIDO =
      "Crédito não realizado. Valor não permitido.";
  private static final String MENSAGEM_CONTA_BLOQUEADA =
      "Crédito não realizado. Conta com status diferente de desbloqueado.";
  private static final String MENSAGEM_PRODUTO_NAO_CONFERE =
      "Crédito não realizado. ID Produto informado não confere.";
  private static final String MENSAGEM_CARTAO_INVALIDO =
      "Crédito não realizado. Cartão não encontrado para a conta informada.";
  private static final String MENSAGEM_ARRANJO_INVALIDO =
      "Crédito não realizado. Arranjo de pagamento não permitido.";

  private static final String MENSAGEM_SUCESSO_ESTORNO = "Estorno realizado com sucesso.";
  private static final String MENSAGEM_ORIGINAL_JA_ESTORNADA =
      "Transação original já estornada anteriormente.";
  private static final String MENSAGEM_ORIGINAL_NAO_REPLICADA =
      "Transação original ainda não computada. Tente novamente em alguns minutos.";
  private static final String MENSAGEM_ORIGINAL_NAO_ENCONTRADA =
      "Estorno não realizado. Transação original não encontrada.";
  private static final String MENSAGEM_CONTA_BLOQUEADA_ESTORNO =
      "Estorno não realizado. Conta com status diferente de desbloqueado.";
  private static final String MENSAGEM_CARTAO_INVALIDO_ESTORNO =
      "Estorno não realizado. Cartão não encontrado para a conta informada.";
  private static final String MENSAGEM_ARRANJO_INVALIDO_ESTORNO =
      "Estorno não realizado. Arranjo de pagamento não permitido.";

  private static final String MENSAGEM_IP_INVALIDO =
      "IP de acesso não permitido para realizar a transação.";
  private static final String MENSAGEM_USUARIO_INVALIDO =
      "Usuário não tem permissão para realizar a transação.";
  private static final String MENSAGEM_UID_INVALIDO = "UID de Transação informado é inválido.";

  private static final String MENSAGEM_FALHA =
      "Falha ao executar API. Parâmetros vazios ou inconsistentes.";

  private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yDDD");

  private static final int CREDITO = 1;

  public static final int DESBLOQUEADA = 1;
  public static final int STATUS_SUCESSO = 1;
  public static final int STATUS_FALHA = 0;
  public static final String PREFIXO_RRN_BRB = "B";

  private CargaBRBApiRepository repository;

  @Autowired private LancamentoService lancamentoService;

  @Autowired private ContaPagamentoService contaPagamentoService;

  @Autowired private CredencialService credencialService;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private LogTransacoesService logTransacoesService;

  @Autowired private TravaContasService travaContasService;

  @Autowired private EventoService eventoService;

  @Autowired private UtilService utilService;

  @Autowired
  public CargaBRBApiService(CargaBRBApiRepository repository) {
    super(repository);
    this.repository = repository;
  }

  @Transactional
  public ResponseEntity<CargaBRBApiResponse> cargaBRBApi(
      CadastroCargaBRBApi model, SecurityUser user, String ip) {

    ResponseEntity<CargaBRBApiResponse> MENSAGEM_USUARIO_OU_IP_INVALIDO =
        validaUsuarioEIP(user, ip);
    if (MENSAGEM_USUARIO_OU_IP_INVALIDO != null) return MENSAGEM_USUARIO_OU_IP_INVALIDO;

    if (model.getValor().compareTo(BigDecimal.ZERO) <= 0) {
      return responseError(MENSAGEM_VALOR_INVALIDO, HttpStatus.BAD_REQUEST);
    }

    String uid = model.getTransactionUid().toString();
    ResponseEntity<CargaBRBApiResponse> MENSAGEM_UID_INVALIDA = validaDataUidTransacao(uid);
    if (MENSAGEM_UID_INVALIDA != null) return MENSAGEM_UID_INVALIDA;

    ContaPagamento contaPagamento = null;
    try {
      contaPagamento = contaPagamentoService.findByIdNotNull(model.getIdConta());
    } catch (Exception e) {
      return responseError(MENSAGEM_CONTA_NAO_ENCONTRADA, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    if (!contaPagamento.getIdStatusConta().equals(DESBLOQUEADA)) {
      return responseError(MENSAGEM_CONTA_BLOQUEADA, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    if (!contaPagamento.getIdProdutoInstituicao().equals(model.getIdProduto())) {
      return responseError(MENSAGEM_PRODUTO_NAO_CONFERE, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    ProdutoInstituicaoConfiguracao pic =
        produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(
            contaPagamento.getIdProdutoInstituicao());
    if (pic == null || !ARRANJOS_PERMITIDOS.contains(pic.getIdArranjo())) {
      return responseError(MENSAGEM_ARRANJO_INVALIDO, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    Credencial credencial =
        credencialService.buscarCredencialParaLancamentoManual(contaPagamento.getIdConta());
    if (credencial == null) {
      return responseError(MENSAGEM_CARTAO_INVALIDO, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    try {
      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, contaPagamento);
    } catch (Exception e) {
      return responseError(MENSAGEM_USUARIO_INVALIDO, HttpStatus.FORBIDDEN);
    }

    String rrn = PREFIXO_RRN_BRB + uid;

    CargaBRBApi cargaExistente = repository.findByRrnAndStatus(rrn, STATUS_SUCESSO);
    if (cargaExistente != null) {
      return responseError(MENSAGEM_UID_UTILIZADO, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    String freedata =
        model.getTextoExtrato() == null ? TEXTO_EXTRATO_CARGA : model.getTextoExtrato();
    JcardResponse response =
        lancamentoService.doLancamentoManual(
            contaPagamento.getIdAccountCode(),
            CODIGO_TRANSACAO_CARGA,
            model.getValor(),
            Constantes.CODIGO_MOEDA_PADRAO,
            rrn,
            CREDITO,
            credencial.getTokenInterno(),
            contaPagamento.getIdConta(),
            freedata,
            null,
            ip,
            null,
            lancamentoService.getStan(),
            null);

    return getCargaBRBApiResponseResponseEntity(
        rrn,
        contaPagamento,
        credencial,
        model.getValor(),
        freedata,
        MENSAGEM_SUCESSO_CARGA,
        response.getSuccess(),
        response.getErrors(),
        true,
        null);
  }

  public ResponseEntity<CargaBRBApiResponse> estornaCargaBRBApi(
      Long transactionUid, SecurityUser user, String ip) {

    if (transactionUid == null) {
      return responseError(MENSAGEM_FALHA, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    String uid = transactionUid.toString();
    ResponseEntity<CargaBRBApiResponse> MENSAGEM_UID_INVALIDA = validaDataUidTransacao(uid);
    if (MENSAGEM_UID_INVALIDA != null) return MENSAGEM_UID_INVALIDA;

    ResponseEntity<CargaBRBApiResponse> MENSAGEM_USUARIO_OU_IP_INVALIDO =
        validaUsuarioEIP(user, ip);
    if (MENSAGEM_USUARIO_OU_IP_INVALIDO != null) return MENSAGEM_USUARIO_OU_IP_INVALIDO;

    String rrn = PREFIXO_RRN_BRB + uid;
    CargaBRBApi cargaExistente = repository.findByRrnAndStatus(rrn, STATUS_SUCESSO);
    if (cargaExistente == null) {
      return responseError(MENSAGEM_ORIGINAL_NAO_ENCONTRADA, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    CargaBRBApi cargaEstornada = repository.findByRrnEstornadoAndStatus(rrn, STATUS_SUCESSO);
    if (cargaEstornada != null) {
      return responseError(MENSAGEM_ORIGINAL_JA_ESTORNADA, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    ContaPagamento contaPagamento = null;
    try {
      contaPagamento = contaPagamentoService.findByIdNotNull(cargaExistente.getIdConta());
    } catch (Exception e) {
      return responseError(MENSAGEM_CONTA_NAO_ENCONTRADA, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    if (!contaPagamento.getIdStatusConta().equals(DESBLOQUEADA)) {
      return responseError(MENSAGEM_CONTA_BLOQUEADA_ESTORNO, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    travaContasService.travaContas(contaPagamento.getIdConta(), Servicos.LANCAMENTO);

    ProdutoInstituicaoConfiguracao pic =
        produtoInstituicaoConfiguracaoService.findByIdProdInstituicao(
            contaPagamento.getIdProdutoInstituicao());
    if (pic == null || !ARRANJOS_PERMITIDOS.contains(pic.getIdArranjo())) {
      return responseError(MENSAGEM_ARRANJO_INVALIDO_ESTORNO, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    Credencial credencial =
        credencialService.buscarCredencialParaLancamentoManual(contaPagamento.getIdConta());
    if (credencial == null) {
      return responseError(MENSAGEM_CARTAO_INVALIDO_ESTORNO, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    try {
      UtilController.checkHierarquiaUsuarioLogadoEmParidadeOuSuperior(user, contaPagamento);
    } catch (Exception e) {
      return responseError(MENSAGEM_USUARIO_INVALIDO, HttpStatus.FORBIDDEN);
    }

    Long idTransacao = null;
    try {
      idTransacao = logTransacoesService.findIdLogTransacoesByRrn(rrn);
    } catch (Exception e) {
      return responseError(MENSAGEM_ORIGINAL_NAO_REPLICADA, HttpStatus.FAILED_DEPENDENCY);
    }

    JcardResponseRrn response =
        lancamentoService.doLancamentoEstornoById(
            contaPagamento.getIdConta(),
            idTransacao,
            CODIGO_TRANSACAO_ESTORNO,
            Constantes.CODIGO_MOEDA_PADRAO,
            cargaExistente.getValor());

    if (response.getSuccess()) {
      eventoService.publicarMovimentacaoFinanceiraEvent(
          "Estorno_" + Servicos.LANCAMENTO.getDescricao(),
          contaPagamento.getIdInstituicao(),
          null,
          contaPagamento.getIdConta(),
          null,
          CODIGO_TRANSACAO_ESTORNO,
          null,
          cargaExistente.getValor(),
          "0");
    }

    return getCargaBRBApiResponseResponseEntity(
        response.getRrn(),
        contaPagamento,
        credencial,
        cargaExistente.getValor(),
        TEXTO_EXTRATO_ESTORNO,
        MENSAGEM_SUCESSO_ESTORNO,
        response.getSuccess(),
        response.getErrors(),
        false,
        cargaExistente.getRrn());
  }

  private ResponseEntity<CargaBRBApiResponse> getCargaBRBApiResponseResponseEntity(
      String rrn,
      ContaPagamento contaPagamento,
      Credencial credencial,
      BigDecimal valor,
      String textoExtrato,
      String mensagemSucesso,
      Boolean success,
      String errors,
      Boolean carga,
      String rrnOriginal) {

    CargaBRBApi cargaBRBApi =
        criaCargaBRBApiParaSalvar(
            rrn,
            contaPagamento.getIdConta(),
            credencial.getTokenInterno(),
            valor,
            textoExtrato,
            carga);

    if (!carga) {
      cargaBRBApi.setRrnEstornado(rrnOriginal);
    }
    if (success.equals(true)) {
      cargaBRBApi.setStatus(STATUS_SUCESSO);
      repository.save(cargaBRBApi);
      return new ResponseEntity<>(new CargaBRBApiResponse(true, mensagemSucesso), HttpStatus.OK);
    } else {
      cargaBRBApi.setStatus(STATUS_FALHA);
      repository.save(cargaBRBApi);
      return responseError(errors, HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }

  private CargaBRBApi criaCargaBRBApiParaSalvar(
      String rrn,
      Long idConta,
      String tokenInterno,
      BigDecimal valor,
      String freedata,
      Boolean carga) {
    CargaBRBApi cargaBRBApi = new CargaBRBApi();
    cargaBRBApi.setRrn(rrn);
    cargaBRBApi.setIdConta(idConta);
    cargaBRBApi.setTokenInterno(tokenInterno);
    cargaBRBApi.setValor(valor);
    cargaBRBApi.setTextoExtrato(freedata);
    cargaBRBApi.setCodTransacao(carga ? CODIGO_TRANSACAO_CARGA : CODIGO_TRANSACAO_ESTORNO);
    cargaBRBApi.setDataHoraLancamento(LocalDateTime.now());
    return cargaBRBApi;
  }

  private ResponseEntity<CargaBRBApiResponse> validaDataUidTransacao(String uid) {

    try {
      LocalDate.parse(uid.substring(0, 4), DTF);
    } catch (DateTimeException | StringIndexOutOfBoundsException e) {
      return responseError(MENSAGEM_UID_INVALIDO, HttpStatus.UNPROCESSABLE_ENTITY);
    }

    return null;
  }

  private ResponseEntity<CargaBRBApiResponse> validaUsuarioEIP(SecurityUser user, String ip) {
    if (utilService.isAmbienteHomologacao()) {
      if (!ID_USUARIO_BRB_CARGA_HOMOLOGACAO.contains(user.getIdUsuario())) {
        return responseError(MENSAGEM_USUARIO_INVALIDO, HttpStatus.FORBIDDEN);
      }
    } else {
      if (utilService.isAmbienteProducao()) {
        if (!ID_USUARIO_BRB_CARGA_PRODUCAO.contains(user.getIdUsuario())) {
          return responseError(MENSAGEM_USUARIO_INVALIDO, HttpStatus.FORBIDDEN);
        }
      }
    }

    if (!IP_BRB_CARGA.matcher(ip).find()) {
      return responseError(MENSAGEM_IP_INVALIDO, HttpStatus.FORBIDDEN);
    }

    return null;
  }

  private ResponseEntity<CargaBRBApiResponse> responseError(
      String mensagem, HttpStatus httpStatus) {
    return new ResponseEntity<>(new CargaBRBApiResponse(false, mensagem), httpStatus);
  }
}
