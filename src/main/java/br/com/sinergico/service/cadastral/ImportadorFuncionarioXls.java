package br.com.sinergico.service.cadastral;

import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.CadastrarPessoaPDAFRequest;
import br.com.json.bean.cadastral.FuncionarioCadastroCompletoTO;
import br.com.json.bean.cadastral.FuncionarioCargaTO;
import br.com.json.bean.cadastral.FuncionarioDependenteTO;
import br.com.json.bean.cadastral.FuncionarioEmissaoSegundaViaTO;
import br.com.json.bean.cadastral.FuncionarioPJTO;
import br.com.json.bean.cadastral.FuncionarioProdutosSociaisTO;
import br.com.json.bean.cadastral.FuncionarioResponsavelTO;
import br.com.sinergico.enums.RelacaoDependenciaEnum;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.ConstantesB2B;
import br.com.sinergico.util.ConstantesErro;
import br.com.sinergico.util.DateUtil;
import com.google.common.base.Strings;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.bytecode.spi.NotInstrumentedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class ImportadorFuncionarioXls extends ImportadorArquivo {
  private static final Integer INDEX_CPF = 0;
  private static final Integer INDEX_VALOR_CARGA = 1;
  private static final Integer INDEX_NOME_COMPLETO = 2;

  private static final long serialVersionUID = 1L;

  @Autowired private DateUtil dateUtil;

  private static final String DATA_PATTERN = "[0-9]{2}(/|-)[0-9]{2}(/|-)[0-9]{4}";
  private static final Pattern paternData = Pattern.compile(DATA_PATTERN);

  private static Logger logger = LoggerFactory.getLogger(ContaPagamentoService.class);

  public List<FuncionarioCargaTO> getImportacao(File is, Integer arquivoCabecalho) {
    try {
      return getImportacao(new FileInputStream(is), arquivoCabecalho);
    } catch (FileNotFoundException e) {
      throw new GenericServiceException(ImportadorFuncionarioTxt.EXCEPTION, e);
    }
  }

  public List<FuncionarioProdutosSociaisTO> getImportacaoProdutosSociais(File is) {
    try {
      return getImportacaoCadastroProdutosSociais(new FileInputStream(is));
    } catch (FileNotFoundException e) {
      throw new GenericServiceException(ImportadorFuncionarioTxt.EXCEPTION, e);
    }
  }

  public List<FuncionarioEmissaoSegundaViaTO> getImportacaoFuncionarioSegundaVia(
      File is, Integer arquivoCabecalho) {
    try {
      return getImportacaoFuncionarioSegundaVia(new FileInputStream(is), arquivoCabecalho);
    } catch (FileNotFoundException e) {
      throw new GenericServiceException(ImportadorFuncionarioTxt.EXCEPTION, e);
    }
  }

  @Override
  public List<FuncionarioCargaTO> getImportacao(InputStream is, Integer arquivoCabecalho) {
    List<FuncionarioCargaTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        // xssfWorbook = new XSSFWorkbook(is);
        Sheet sheet = w.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();

        while (iterator.hasNext()) {
          Row row = iterator.next();
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioCargaTO to = getFuncionarioCargaTOByRow(row);
            tos.add(to);
          }
        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
          // throw new GenericServiceException("Erro ao fechar o
          // arquivo informado", e);

        }
      }
    }

    return tos;
  }

  public List<FuncionarioEmissaoSegundaViaTO> getImportacaoFuncionarioSegundaVia(
      InputStream is, Integer arquivoCabecalho) {
    List<FuncionarioEmissaoSegundaViaTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        // xssfWorbook = new XSSFWorkbook(is);
        Sheet sheet = w.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();

        while (iterator.hasNext()) {
          Row row = iterator.next();
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioEmissaoSegundaViaTO to = getFuncionarioEmissaoSegundaViaTOByRow(row);
            tos.add(to);
          }
        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
          // throw new GenericServiceException("Erro ao fechar o
          // arquivo informado", e);

        }
      }
    }

    return tos;
  }

  public List<FuncionarioCargaTO> getImportacaoCadPortadores(
      InputStream is, Integer cabalAntigo, Integer arquivoCabecalho) {
    List<FuncionarioCargaTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        // xssfWorbook = new XSSFWorkbook(is);
        Sheet sheet = w.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();

        while (iterator.hasNext()) {
          if (arquivoCabecalho.equals(1)) {
            arquivoCabecalho = 2;
            Row rowCab = iterator.next();
          }
          Row row = iterator.next();
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioCargaTO to = getCadastroPortadoresTOByRow(row);
            tos.add(to);
          }
        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
          // throw new GenericServiceException("Erro ao fechar o
          // arquivo informado", e);

        }
      }
    }

    return tos;
  }

  public List<FuncionarioCadastroCompletoTO> getImportacaoCadastroCompleto(
      InputStream is, Integer cabalAntigo, Integer arquivoCabecalho) {
    List<FuncionarioCadastroCompletoTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        Sheet sheet = w.getSheetAt(0);

        Iterator<Row> iterator = sheet.iterator();

        while (iterator.hasNext()) {
          if (arquivoCabecalho.equals(1)) {
            arquivoCabecalho = 2;
            Row rowCab = iterator.next();
          }
          Row row = iterator.next();
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioCadastroCompletoTO to = getCadastroCompletoTOByRow(row);
            tos.add(to);
          }
          // to.setSetorFilial(idSetorFilial);

        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
          // throw new GenericServiceException("Erro ao fechar o
          // arquivo informado", e);

        }
      }
    }

    return tos;
  }

  public List<FuncionarioProdutosSociaisTO> getImportacaoCadastroProdutosSociais(InputStream is) {
    List<FuncionarioProdutosSociaisTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        Sheet sheet = w.getSheetAt(0);

        for (Row row : sheet) {
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioProdutosSociaisTO to = getCadastroProdutosSociaisTOByRow(row);
            tos.add(to);
          }
        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
        }
      }
    }

    return tos;
  }

  public List<FuncionarioPJTO> getImportacaoCadastroPJ(InputStream is) {
    List<FuncionarioPJTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        Sheet sheet = w.getSheetAt(0);

        for (Row row : sheet) {
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioPJTO to = getCadastroPJTOByRow(row);
            tos.add(to);
          }
        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
        }
      }
    }

    return tos;
  }

  private FuncionarioPJTO getCadastroPJTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioPJTO to = new FuncionarioPJTO();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpf = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setCpf(Strings.padStart(cpf.trim(), 11, '0'));
          break;
        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll(" {2}", " ");
          to.setNomeCompleto(nome);
          break;
        case 2:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeFantasia = cell.getStringCellValue().trim();
          nomeFantasia = nomeFantasia.replaceAll(" {2}", " ");
          to.setNomeFantasia(nomeFantasia);
          break;
        case 3:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDataFundacao(cell.getDateCellValue());
            } else {
              to.setDataFundacao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataFundacao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setDataFundacao(myDate);
            }
          }
          break;
        case 4:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setEmail(cell.getStringCellValue().trim());
          break;
        case 5:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setDDD(cell.getStringCellValue());
          break;
        case 6:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cel = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
          to.setCelular(cel.replace("-", ""));
          break;
        case 7:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpfRep = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setCpfRepresentanteLegal(Strings.padStart(cpfRep.trim(), 11, '0'));
          break;
        case 8:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeRep = cell.getStringCellValue().trim();
          nomeRep = nomeRep.replaceAll(" {2}", " ");
          to.setNomeRepresentanteLegal(nomeRep);
          break;
        case 9:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setEmailRepresentanteLegal(cell.getStringCellValue().trim());
          break;
        case 10:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setDDDRepresentanteLegal(cell.getStringCellValue());
          break;
        case 11:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String celRep = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
          to.setCelularRepresentanteLegal(celRep.replace("-", ""));
          break;
        case 12:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDataNascimentoRepresentanteLegal(cell.getDateCellValue());
            } else {
              to.setDataFundacao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataNascimentoRepresentanteLegal(
                  dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setDataNascimentoRepresentanteLegal(myDate);
            }
          }
          break;
      }
    }
    return to;
  }

  public List<FuncionarioResponsavelTO> getImportacaoCadastroRespDep(InputStream is) {
    List<FuncionarioResponsavelTO> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      XSSFWorkbook xssfWorbook = null;

      // colocando w (resource) como argumento do try para que automaticamente seja fechado, ja que
      // implementa closeable

      try (Workbook w = WorkbookFactory.create(is)) {

        Sheet sheet = w.getSheetAt(0);

        for (Row row : sheet) {
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            FuncionarioResponsavelTO to = getCadastroRespDepTOByRow(row);
            tos.add(to);
          }
        }
      } catch (IOException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (EncryptedDocumentException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS", e);
      } catch (InvalidFormatException e) {
        throw new GenericServiceException("Erro ao ler o arquivo XLS.Formato inválido.", e);
      } finally {

        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
        }
      }
    }

    return tos;
  }

  private FuncionarioResponsavelTO getCadastroRespDepTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioResponsavelTO to = new FuncionarioResponsavelTO();

    ArrayList<FuncionarioDependenteTO> listaDependenteTO = new ArrayList<>();
    FuncionarioDependenteTO depTO1 = new FuncionarioDependenteTO();
    FuncionarioDependenteTO depTO2 = new FuncionarioDependenteTO();
    FuncionarioDependenteTO depTO3 = new FuncionarioDependenteTO();
    to.setDependentes(listaDependenteTO);

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpf = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setCpf(Strings.padStart(cpf.trim(), 11, '0'));
          break;
        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll(" {2}", " ");
          to.setNomeCompleto(nome);
          break;
        case 2:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDataNascimento(cell.getDateCellValue());
            } else {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setDataNascimento(myDate);
            }
          }
          break;
        case 3:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setEmail(cell.getStringCellValue().trim());
          break;
        case 4:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setDDD(cell.getStringCellValue());
          break;
        case 5:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cel = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
          to.setCelular(cel.replace("-", ""));
          break;
        case 6:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (!cell.getStringCellValue().isEmpty()) {
            int cellValue = (int) Double.parseDouble(cell.getStringCellValue());
            int valorAjustado = cellValue - 1;
            if (valorAjustado > RelacaoDependenciaEnum.values().length || valorAjustado < 0) {
              to.setVinculoDependente(null);
            } else {
              to.setVinculoDependente(RelacaoDependenciaEnum.values()[valorAjustado]);
            }
          }
          break;
        case 7:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpfDep1 = cell.getStringCellValue().replaceAll("[^0-9]", "");
          if (!cpfDep1.isEmpty()) {
            depTO1.setCpf(Strings.padStart(cpfDep1.trim(), 11, '0'));
            listaDependenteTO.add(depTO1);
          } else {
            return to;
          }
          break;
        case 8:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeDep1 = cell.getStringCellValue().trim();
          nomeDep1 = nomeDep1.replaceAll(" {2}", " ");
          depTO1.setNomeCompleto(nomeDep1);
          break;
        case 9:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              depTO1.setDataNascimento(cell.getDateCellValue());
            } else {
              depTO1.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              depTO1.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              depTO1.setDataNascimento(myDate);
            }
          }
          break;
        case 10:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          depTO1.setEmail(cell.getStringCellValue().trim());
          break;
        case 11:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          depTO1.setDDD(cell.getStringCellValue());
          break;
        case 12:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String celDep1 = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
          depTO1.setCelular(celDep1.replace("-", ""));
          break;
        case 13:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpfDep2 = cell.getStringCellValue().replaceAll("[^0-9]", "");
          if (!cpfDep2.isEmpty()) {
            depTO2.setCpf(Strings.padStart(cpfDep2.trim(), 11, '0'));
            listaDependenteTO.add(depTO2);
          } else {
            return to;
          }
          break;
        case 14:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeDep2 = cell.getStringCellValue().trim();
          nomeDep2 = nomeDep2.replaceAll(" {2}", " ");
          depTO2.setNomeCompleto(nomeDep2);
          break;
        case 15:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              depTO2.setDataNascimento(cell.getDateCellValue());
            } else {
              depTO2.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              depTO2.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              depTO2.setDataNascimento(myDate);
            }
          }
          break;
        case 16:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          depTO2.setEmail(cell.getStringCellValue().trim());
          break;
        case 17:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          depTO2.setDDD(cell.getStringCellValue());
          break;
        case 18:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String celDep2 = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
          depTO2.setCelular(celDep2.replace("-", ""));
          break;
        case 19:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpfDep3 = cell.getStringCellValue().replaceAll("[^0-9]", "");
          if (!cpfDep3.isEmpty()) {
            depTO3.setCpf(Strings.padStart(cpfDep3.trim(), 11, '0'));
            listaDependenteTO.add(depTO3);
          } else {
            return to;
          }
          break;
        case 20:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeDep3 = cell.getStringCellValue().trim();
          nomeDep3 = nomeDep3.replaceAll(" {2}", " ");
          depTO3.setNomeCompleto(nomeDep3);
          break;
        case 21:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              depTO3.setDataNascimento(cell.getDateCellValue());
            } else {
              depTO3.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              depTO3.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              depTO3.setDataNascimento(myDate);
            }
          }
          break;
        case 22:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          depTO3.setEmail(cell.getStringCellValue().trim());
          break;
        case 23:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          depTO3.setDDD(cell.getStringCellValue());
          break;
        case 24:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String celDep3 = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
          depTO3.setCelular(celDep3.replace("-", ""));
          break;
      }
    }
    return to;
  }

  public List<CadastrarPessoaPDAFRequest> getImportacaoCadastroPDAF(InputStream is) {
    List<CadastrarPessoaPDAFRequest> tos = null;

    if (is != null) {
      tos = new ArrayList<>();

      // utilizando o try with resources
      try (Workbook w = WorkbookFactory.create(is)) {

        Sheet sheet = w.getSheetAt(0);

        for (Row row : sheet) {
          if (row.getCell(0) != null && !row.getCell(0).toString().isEmpty()) {
            CadastrarPessoaPDAFRequest to = getCadastroPDAFLoteTORow(row);
            tos.add(to);
          }
        }
      } catch (IOException | InvalidFormatException | EncryptedDocumentException e) {
        throw new GenericServiceException(
            ConstantesErro.ERRO_ARQUIVO_CADASTRO_PROC5000_PDAF_CRIAR_ARQUIVO_EXCEL.getMensagem(),
            e);
      } finally {
        try {
          is.close();
        } catch (IOException e) {
          logger.error(e.getMessage(), e);
        }
      }
    }

    return tos;
  }

  private CadastrarPessoaPDAFRequest getCadastroPDAFLoteTORow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    CadastrarPessoaPDAFRequest to = new CadastrarPessoaPDAFRequest();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cnpj = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setDocumento(Strings.padStart(cnpj.trim(), 14, '0'));
          break;
        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String razaoSocial = cell.getStringCellValue().trim();
          razaoSocial = razaoSocial.replaceAll(" {2}", " ");
          to.setRazaoSocial(razaoSocial);
          break;
        case 2:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDtFundacao(cell.getDateCellValue());
            } else {
              to.setDtFundacao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDtFundacao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setDtFundacao(myDate);
            }
          }
          break;
        case 3:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            String nomeFantasia = cell.getStringCellValue().trim();
            nomeFantasia = nomeFantasia.replaceAll(" {2}", " ");
            to.setNomeFantasia(nomeFantasia);
          }
          break;
        case 4:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setInscricaoEstadual(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 5:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setInscricaoMunicipal(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 6:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAtividadePrincipal(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 7:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setFormaConstituicao(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 8:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setQtdFuncionarios(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? Integer.valueOf(cell.getStringCellValue())
                  : null);
          break;
        case 9:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setFaturamentoMedioMensal(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? BigDecimal.valueOf(Long.parseLong(cell.getStringCellValue()))
                  : null);
          break;
        case 10:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setCepEmpresa(cell.getStringCellValue());
          break;
        case 11:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setLogradouroEmpresa(cell.getStringCellValue());
          break;
        case 12:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNumeroEmpresa(cell.getStringCellValue());
          break;
        case 13:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setComplementoEmpresa(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 14:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setBairroEmpresa(cell.getStringCellValue());
          break;
        case 15:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setCidadeEmpresa(cell.getStringCellValue());
          break;
        case 16:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setUfEmpresa(cell.getStringCellValue().trim());
          break;
        case 17:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setDddTelefoneFixoEmpresa(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 18:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            String nroTelefoneComercial =
                Strings.padStart(cell.getStringCellValue().trim(), 8, '8');
            to.setNroTelefoneFixoEmpresa(nroTelefoneComercial.replace("-", ""));
          }
          break;
        case 19:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setDddCelularEmpresa(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 20:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            String celularEmpresa = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
            to.setNroCelularEmpresa(celularEmpresa.replace("-", ""));
          }
          break;
        case 21:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setEmail(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue().trim()
                  : null);
          break;
        case 41:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setCodBanco(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 42:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setCodAgencia(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 43:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setContaCorrente(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 44:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setChequeEspecial(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? cell.getStringCellValue()
                  : null);
          break;
        case 45:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            to.setAdditionalProperty("nome_adc_1", cell.getStringCellValue());
          }
          break;
        case 46:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setAdditionalProperty("dt_nascimento_adc_1", cell.getDateCellValue());
            } else {
              to.setAdditionalProperty(
                  "dt_nascimento_adc_1",
                  DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());

            if (matcherData.matches()) {
              try {
                to.setAdditionalProperty(
                    "dt_nascimento_adc_1",
                    DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } catch (NotInstrumentedException pe) {
                to.setAdditionalProperty("dt_nascimento_adc_1", null);
              }
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setAdditionalProperty("dt_nascimento_adc_1", myDate);
            }
          } catch (NotInstrumentedException e) {
            to.setAdditionalProperty("dt_nascimento_adc_1", null);
          }
          break;
        case 47:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            to.setAdditionalProperty("cpf_adc_1", cell.getStringCellValue());
          }
          break;
        case 48:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cargo_adc_1", cell.getStringCellValue());
          break;
        case 49:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("telefone_adc_1", cell.getStringCellValue());
          break;
        case 50:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("email_adc_1", cell.getStringCellValue());
          break;
        case 51:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("nome_mae_adc_1", cell.getStringCellValue());
          break;
        case 52:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cep_adc_1", cell.getStringCellValue());
          break;
        case 53:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("logradouro_adc_1", cell.getStringCellValue());
          break;
        case 54:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("numero_adc_1", cell.getStringCellValue());
          break;
        case 55:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("complemento_adc_1", cell.getStringCellValue());
          break;
        case 56:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("bairro_adc_1", cell.getStringCellValue());
          break;
        case 57:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cidade_adc_1", cell.getStringCellValue());
          break;
        case 58:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("uf_adc_1", cell.getStringCellValue());
          break;
        case 59:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            to.setAdditionalProperty("nome_adc_2", cell.getStringCellValue());
          }
          break;
        case 60:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setAdditionalProperty("dt_nascimento_adc_2", cell.getDateCellValue());
            } else {
              to.setAdditionalProperty(
                  "dt_nascimento_adc_2",
                  DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());

            if (matcherData.matches()) {
              try {
                to.setAdditionalProperty(
                    "dt_nascimento_adc_2",
                    DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } catch (NotInstrumentedException pe) {
                to.setAdditionalProperty("dt_nascimento_adc_2", null);
              }
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setAdditionalProperty("dt_nascimento_adc_2", myDate);
            }
          } catch (NotInstrumentedException e) {
            to.setAdditionalProperty("dt_nascimento_adc_2", null);
          }
          break;
        case 61:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            to.setAdditionalProperty("cpf_adc_2", cell.getStringCellValue());
          }
          break;
        case 62:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cargo_adc_2", cell.getStringCellValue());
          break;
        case 63:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("telefone_adc_2", cell.getStringCellValue());
          break;
        case 64:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("email_adc_2", cell.getStringCellValue());
          break;
        case 65:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("nome_mae_adc_2", cell.getStringCellValue());
          break;
        case 66:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cep_adc_2", cell.getStringCellValue());
          break;
        case 67:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("logradouro_adc_2", cell.getStringCellValue());
          break;
        case 68:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("numero_adc_2", cell.getStringCellValue());
          break;
        case 69:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("complemento_adc_2", cell.getStringCellValue());
          break;
        case 70:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("bairro_adc_2", cell.getStringCellValue());
          break;
        case 71:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cidade_adc_2", cell.getStringCellValue());
          break;
        case 72:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("uf_adc_2", cell.getStringCellValue());
          break;
        case 73:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            to.setAdditionalProperty("nome_adc_3", cell.getStringCellValue());
          }
          break;
        case 74:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setAdditionalProperty("dt_nascimento_adc_3", cell.getDateCellValue());
            } else {
              to.setAdditionalProperty(
                  "dt_nascimento_adc_3",
                  DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());

            if (matcherData.matches()) {
              try {
                to.setAdditionalProperty(
                    "dt_nascimento_adc_3",
                    DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } catch (NotInstrumentedException pe) {
                to.setAdditionalProperty("dt_nascimento_adc_3", null);
              }
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setAdditionalProperty("dt_nascimento_adc_3", myDate);
            }
          } catch (NotInstrumentedException e) {
            to.setAdditionalProperty("dt_nascimento_adc_3", null);
          }
          break;
        case 75:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
            to.setAdditionalProperty("cpf_adc_3", cell.getStringCellValue());
          }
          break;
        case 76:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cargo_adc_3", cell.getStringCellValue());
          break;
        case 77:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("telefone_adc_3", cell.getStringCellValue());
          break;
        case 78:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("email_adc_3", cell.getStringCellValue());
          break;
        case 79:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("nome_mae_adc_3", cell.getStringCellValue());
          break;
        case 80:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cep_adc_3", cell.getStringCellValue());
          break;
        case 81:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("logradouro_adc_3", cell.getStringCellValue());
          break;
        case 82:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("numero_adc_3", cell.getStringCellValue());
          break;
        case 83:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("complemento_adc_3", cell.getStringCellValue());
          break;
        case 84:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("bairro_adc_3", cell.getStringCellValue());
          break;
        case 85:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("cidade_adc_3", cell.getStringCellValue());
          break;
        case 86:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setAdditionalProperty("uf_adc_3", cell.getStringCellValue());
          break;
        case 87:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setDiaVencFatura(
              cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()
                  ? LocalDateTime.parse(cell.getStringCellValue())
                  : null);
          break;
        case 88:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setSmsControle(cell.getStringCellValue());
          break;
        case 89:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setSmsNotificacao(cell.getStringCellValue());
          break;
      }
    }
    return to;
  }

  private FuncionarioCargaTO getFuncionarioCargaTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioCargaTO to = new FuncionarioCargaTO();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpf = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setCpf(Strings.padStart(cpf.trim(), 11, '0'));
          break;
        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setMatricula(cell.getStringCellValue().trim());
          break;
        case 2:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll("  ", " ");
          to.setNomeCompleto(nome);
          break;
        case 3:
          try {
            BigDecimal bd1 = BigDecimal.valueOf(cell.getNumericCellValue());
            BigDecimal bd2 =
                new BigDecimal(String.valueOf(bd1)).setScale(2, BigDecimal.ROUND_FLOOR);
            to.setValorCarga(bd2);
          } catch (IllegalStateException e) {
            BigDecimal bd1 = BigDecimal.valueOf(0);
            to.setValorCarga(bd1);
            // throw new GenericServiceException("Dado incorreto inserido na coluna que recebe Valor
            // Carga Padrão.", e);
          }
          break;
        case 4:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setSetorFilial(cell.getStringCellValue().trim());
          break;
        case 5:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDataNascimento(cell.getDateCellValue());
            } else {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setDataNascimento(myDate);
            }
          }
          break;
        case 6:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setEmail(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setEmail("");
          }
          break;
        case 7:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setDddCelular(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setDddCelular("");
          }
          break;
        case 8:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cel = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
            to.setTelefoneCelular(cel.replace("-", ""));
          } catch (IllegalStateException e) {
            to.setTelefoneCelular("");
          }
          break;
        case 9:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setParcelamento(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setParcelamento("");
          }
      }
    }

    return to;
  }

  private FuncionarioEmissaoSegundaViaTO getFuncionarioEmissaoSegundaViaTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioEmissaoSegundaViaTO to = new FuncionarioEmissaoSegundaViaTO();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpf = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setCpf(Strings.padStart(cpf.trim(), 11, '0'));
          break;
        case 1:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDataNascimento(cell.getDateCellValue());
            } else {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              to.setDataNascimento(myDate);
            }
          }
          break;
        case 2:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll("  ", " ");
          to.setNomeCompleto(nome);
          break;
        case 3:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setSetorFilial(cell.getStringCellValue().trim());
          break;
      }
    }
    return to;
  }

  private FuncionarioCadastroCompletoTO getCadastroCompletoTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioCadastroCompletoTO to = new FuncionarioCadastroCompletoTO();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll("  ", " ");
          to.setNomeCompleto(nome);
          break;

        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setTipoDocumento(cell.getStringCellValue());
          break;

        case 2:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String doc = cell.getStringCellValue().replaceAll("[^0-9]", "");
          if (to.getTipoDocumento() != null
              && Constantes.COD_DOC_CPF_1.equals(to.getTipoDocumento())) {
            to.setCpf(Strings.padStart(doc.trim(), 11, '0'));
          } else if (to.getTipoDocumento() != null
              && Constantes.COD_DOC_CNPJ_2.equals(to.getTipoDocumento())) {
            to.setCpf(Strings.padStart(doc.trim(), 14, '0'));
          } else {
            throw new GenericServiceException(
                "Verifique se foi incluído o tipo de documento para todos os registros do arquivo");
          }
          to.setMatricula(to.getCpf());
          break;

        case 3:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeMae = cell.getStringCellValue().trim();
          nomeMae = nomeMae.replaceAll("  ", " ");
          to.setNomeMae(nomeMae);
          break;

        case 4:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomePai = cell.getStringCellValue().trim();
          nomePai = nomePai.replaceAll("  ", " ");
          to.setNomePai(nomePai);
          break;

        case 5:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNacionalidade(cell.getStringCellValue().trim());
          break;

        case 6:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNaturalidade(cell.getStringCellValue().trim());
          break;

        case 7:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String rg = cell.getStringCellValue().trim();
          rg = rg.replace(".", "").replace("-", "").replace("/", "");
          to.setNumeroRg(rg);
          break;

        case 8:
          Date dataRg = new Date();
          Calendar calRg = Calendar.getInstance();
          calRg.set(Calendar.MONTH, 1);
          calRg.set(Calendar.DATE, 1);
          calRg.set(Calendar.YEAR, 1900);
          calRg.set(Calendar.HOUR, 00);
          calRg.set(Calendar.MINUTE, 00);
          calRg.set(Calendar.SECOND, 00);
          dataRg = calRg.getTime();
          try {

            if (cell.getDateCellValue() instanceof Date) {
              to.setRgDataEmissao(cell.getDateCellValue());
            } else {
              if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
                to.setRgDataEmissao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } else {
                to.setRgDataEmissao(dataRg);
              }
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setRgDataEmissao(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              to.setRgDataEmissao(dataRg);
            }
          }
          break;

        case 9:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setRgOrgaoEmissor(cell.getStringCellValue().trim());
          break;

        case 10:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setSexo(cell.getStringCellValue().trim());
          } catch (Exception e) {
            to.setSexo("N");
          }
          break;
        case 11:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            Integer idEstCiv = Integer.parseInt(cell.getStringCellValue());
            to.setIdEstadoCivil(idEstCiv);
          } catch (Exception e) {
            to.setIdEstadoCivil(ConstantesB2B.ZERO_DOUBLE.intValue());
          }
          break;

        case 12:
          Date data = new Date();
          Calendar cal = Calendar.getInstance();
          cal.set(Calendar.MONTH, 1);
          cal.set(Calendar.DATE, 1);
          cal.set(Calendar.YEAR, 1900);
          cal.set(Calendar.HOUR, 00);
          cal.set(Calendar.MINUTE, 00);
          cal.set(Calendar.SECOND, 00);
          data = cal.getTime();
          try {
            if (cell.getDateCellValue() instanceof Date) {
              to.setDataNascimento(cell.getDateCellValue());
            } else {
              if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
                to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } else {
                to.setDataNascimento(data);
              }
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataNascimento(dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              to.setDataNascimento(data);
            }
          }
          break;

        case 13:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setLogradouro(cell.getStringCellValue().trim());
          break;

        case 14:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setBairro(cell.getStringCellValue().trim());
          break;

        case 15:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNumero(cell.getStringCellValue().trim());
          break;

        case 16:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setCidade(cell.getStringCellValue().trim());
          break;

        case 17:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setUf(cell.getStringCellValue().trim());
          break;

        case 18:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cep = cell.getStringCellValue().trim();
          cep = cep.replace(".", "").replace("-", "");
          cep = Strings.padStart(cep.trim(), 8, '0');
          to.setCep(cep);
          break;

        case 19:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setComplemento(cell.getStringCellValue().trim());
          break;

        case 20:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setEmail(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setEmail("");
          }
          break;

        case 21:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setEmailProfissional(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setEmailProfissional("");
          }
          break;

        case 22:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setDddTelefoneResidencial(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setDddTelefoneResidencial("");
          }
          break;

        case 23:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String tel = cell.getStringCellValue().trim().replace("-", "");
            to.setTelefoneResidencial(tel);
          } catch (IllegalStateException e) {
            to.setTelefoneResidencial("");
          }
          break;

        case 24:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setDddCelular(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setDddCelular("");
          }
          break;

        case 25:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cel = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
            to.setTelefoneCelular(cel.replace("-", ""));
          } catch (IllegalStateException e) {
            to.setTelefoneCelular("");
          }
          break;

        case 26:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setIdBanco(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setIdBanco("");
          }
          break;

        case 27:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String ag = cell.getStringCellValue().trim().replace(".", "").replace("-", "");
            to.setIdAgencia(ag);
          } catch (IllegalStateException e) {
            to.setIdAgencia("");
          }
          break;

        case 28:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String conta =
                cell.getStringCellValue().trim().replace(".", "").replace("-", "").toUpperCase();
            to.setContaBancaria(conta);
          } catch (IllegalStateException e) {
            to.setContaBancaria("");
          }
          break;

        case 29:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setTipoContaBancaria(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setTipoContaBancaria("");
          }
          break;

        case 30:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setSetorFilial(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setSetorFilial("MATRIZ");
          }
          break;
        case 31:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNomeCartaoImpresso(cell.getStringCellValue().trim());
          break;
        case 32:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setRgUFOrgaoEmissor(cell.getStringCellValue().trim());
          break;
        case 33:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setTipoChavePix(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setTipoChavePix("");
          }
          break;
        case 34:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String documento = cell.getStringCellValue().replaceAll("[^0-9]", "");
            if (to.getTipoChavePix() != null) {
              String chavePix;
              switch (to.getTipoChavePix()) {
                case Constantes.COD_PIX_CPF:
                  chavePix = Strings.padStart(documento.trim(), 11, '0');
                  break;
                case Constantes.COD_PIX_CNPJ:
                  chavePix = Strings.padStart(documento.trim(), 14, '0');
                  break;
                case Constantes.COD_PIX_TELEFONE:
                  chavePix = Constantes.COD_PIX_TELEFONE_PREFIX + cell.getStringCellValue().trim();
                  break;
                case Constantes.COD_PIX_EMAIL:
                case Constantes.COD_PIX_ALEATORIA:
                  chavePix = cell.getStringCellValue().trim();
                  break;
                default:
                  throw new GenericServiceException("Tipo chave pix inválido!");
              }
              to.setChavePix(chavePix);
            }
          } catch (IllegalStateException e) {
            to.setChavePix("");
          }
          break;
      }
    }
    if (StringUtils.isBlank(to.getSetorFilial())) {
      to.setSetorFilial("MATRIZ");
    }
    return to;
  }

  private FuncionarioProdutosSociaisTO getCadastroProdutosSociaisTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioProdutosSociaisTO to = new FuncionarioProdutosSociaisTO();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String doc = cell.getStringCellValue().replaceAll("[^0-9]", "");
          to.setCpf(Strings.padStart(doc.trim(), 11, '0'));
          break;

        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll("  ", " ");
          to.setNomeCompleto(nome);
          to.setValorCarga(BigDecimal.ZERO);
          break;

        case 2:
          Date data = new Date();
          Calendar cal = Calendar.getInstance();
          cal.set(Calendar.MONTH, 1);
          cal.set(Calendar.DATE, 1);
          cal.set(Calendar.YEAR, 1900);
          cal.set(Calendar.HOUR, 00);
          cal.set(Calendar.MINUTE, 00);
          cal.set(Calendar.SECOND, 00);
          data = cal.getTime();
          try {
            if (cell.getDateCellValue() != null) {
              to.setDataNascimento(cell.getDateCellValue());
            } else {
              if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
                to.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } else {
                to.setDataNascimento(data);
              }
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setDataNascimento(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              to.setDataNascimento(data);
            }
          }
          break;

        case 3:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String rg = cell.getStringCellValue().trim();
          rg = rg.replace(".", "").replace("-", "").replace("/", "");
          to.setNumeroRg(rg);
          break;

        case 4:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setRgOrgaoEmissor(cell.getStringCellValue().trim());
          break;

        case 5:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setRgUFOrgaoEmissor(cell.getStringCellValue().trim());
          break;

        case 6:
          try {
            if (cell.getDateCellValue() != null) {
              to.setRgDataEmissao(cell.getDateCellValue());
            } else {
              if (cell.getStringCellValue() != null && !cell.getStringCellValue().isEmpty()) {
                to.setRgDataEmissao(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
              } else {
                to.setRgDataEmissao(null);
              }
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              to.setRgDataEmissao(DateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              to.setRgDataEmissao(null);
            }
          }
          break;

        case 7:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNaturalidade(cell.getStringCellValue().trim());
          break;

        case 8:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNacionalidade(cell.getStringCellValue().trim());
          break;

        case 9:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomeMae = cell.getStringCellValue().trim();
          nomeMae = nomeMae.replaceAll("  ", " ");
          to.setNomeMae(nomeMae);
          break;

        case 10:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nomePai = cell.getStringCellValue().trim();
          nomePai = nomePai.replaceAll("  ", " ");
          to.setNomePai(nomePai);
          break;

        case 11:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cep = cell.getStringCellValue().trim();
          cep = cep.replace(".", "").replace("-", "");
          cep = Strings.padStart(cep.trim(), 8, '0');
          if (cep.equals("00000000")) {
            to.setCep("");
          } else {
            to.setCep(cep);
          }
          break;

        case 12:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setLogradouro(cell.getStringCellValue().trim());
          break;

        case 13:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNumero(cell.getStringCellValue().trim());
          break;

        case 14:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setComplemento(cell.getStringCellValue().trim());
          break;

        case 15:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setBairro(cell.getStringCellValue().trim());
          break;

        case 16:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setCidade(cell.getStringCellValue().trim());
          break;

        case 17:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setUf(cell.getStringCellValue().trim());
          break;

        case 18:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setDddTelefoneResidencial(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setDddTelefoneResidencial("");
          }
          break;

        case 19:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String tel = cell.getStringCellValue().trim().replace("-", "");
            to.setTelefoneResidencial(tel);
          } catch (IllegalStateException e) {
            to.setTelefoneResidencial("");
          }
          break;

        case 20:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setDddCelular(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setDddCelular("");
          }
          break;

        case 21:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cel =
                Strings.padStart(cell.getStringCellValue().trim().replace("-", ""), 9, '9');
            to.setTelefoneCelular(cel);
          } catch (IllegalStateException e) {
            to.setTelefoneCelular("");
          }
          break;

        case 22:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            to.setEmail(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            to.setEmail("");
          }
          break;

        case 23:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNumeroAgencia(cell.getStringCellValue().trim());
          break;

        case 24:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          to.setNomeAgencia(cell.getStringCellValue().trim());
          break;

        case 25:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String docResponsavel = cell.getStringCellValue().replaceAll("[^0-9]", "");
            if (docResponsavel.isEmpty()) {
              to.setCpfResponsavel("");
            } else {
              to.setCpfResponsavel(Strings.padStart(docResponsavel.trim(), 11, '0'));
            }
          } catch (IllegalStateException e) {
            to.setCpfResponsavel("");
          }
          break;

        case 26:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String nomeResponsavel = cell.getStringCellValue().trim();
            if (nomeResponsavel.isEmpty()) {
              to.setNomeResponsavel("");
            } else {
              nomeResponsavel = nomeResponsavel.replaceAll("  ", " ");
              to.setNomeResponsavel(nomeResponsavel);
            }
          } catch (IllegalStateException e) {
            to.setNomeResponsavel("");
          }
          break;

        case 27:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String nomeEscola = cell.getStringCellValue().trim();
            if (nomeEscola.isEmpty()) {
              to.setNomeEscola("");
            } else {
              nomeEscola = nomeEscola.replaceAll("  ", " ");
              to.setNomeEscola(nomeEscola);
            }
          } catch (IllegalStateException e) {
            to.setNomeEscola("");
          }
          break;

        case 28:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cidadeEmbossing = cell.getStringCellValue().trim();
            if (cidadeEmbossing.isEmpty()) {
              to.setCidadeEmbossing("");
            } else {
              cidadeEmbossing = cidadeEmbossing.replaceAll("\\s+", " ");
              to.setCidadeEmbossing(cidadeEmbossing);
            }
          } catch (IllegalStateException e) {
            to.setCidadeEmbossing("");
          }
          break;

        case 29:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String ufEmbossing = cell.getStringCellValue().trim();
            if (ufEmbossing.isEmpty()) {
              to.setUfEmbossing("");
            } else {
              ufEmbossing = ufEmbossing.replaceAll("\\s+", " ");
              to.setUfEmbossing(ufEmbossing);
            }
          } catch (IllegalStateException e) {
            to.setUfEmbossing("");
          }
          break;
      }
    }
    if (StringUtils.isBlank(to.getSetorFilial())) {
      to.setSetorFilial("MATRIZ");
    }
    return to;
  }

  private FuncionarioCargaTO getCadastroPortadoresTOByRow(Row row) {
    Iterator<Cell> iteratorCell = row.cellIterator();

    FuncionarioCargaTO funcionarioCargaTO = new FuncionarioCargaTO();

    while (iteratorCell.hasNext()) {
      Cell cell = iteratorCell.next();

      switch (cell.getColumnIndex()) {
        case 0:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String cpf = cell.getStringCellValue().replaceAll("[^0-9]", "");
          funcionarioCargaTO.setCpf(Strings.padStart(cpf.trim(), 11, '0'));
          funcionarioCargaTO.setMatricula(funcionarioCargaTO.getCpf());
          break;

        case 1:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          String nome = cell.getStringCellValue().trim();
          nome = nome.replaceAll("  ", " ");
          funcionarioCargaTO.setNomeCompleto(nome);
          funcionarioCargaTO.setValorCarga(BigDecimal.ZERO);
          break;

        case 2:
          cell.setCellType(Cell.CELL_TYPE_STRING);
          funcionarioCargaTO.setSetorFilial(cell.getStringCellValue().trim());
          break;
        case 3:
          try {
            if (cell.getDateCellValue() instanceof Date) {
              funcionarioCargaTO.setDataNascimento(cell.getDateCellValue());
            } else {
              funcionarioCargaTO.setDataNascimento(
                  dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            }
          } catch (IllegalStateException e) {
            Matcher matcherData = paternData.matcher(cell.getStringCellValue());
            if (matcherData.matches()) {
              funcionarioCargaTO.setDataNascimento(
                  dateUtil.parseDate("dd/MM/yyyy", cell.getStringCellValue()));
            } else {
              Date myDate;
              Calendar cal = Calendar.getInstance();
              cal.set(Calendar.MONTH, 1);
              cal.set(Calendar.DATE, 1);
              cal.set(Calendar.YEAR, 1900);
              cal.set(Calendar.HOUR, 00);
              cal.set(Calendar.MINUTE, 00);
              cal.set(Calendar.SECOND, 00);
              myDate = cal.getTime();
              funcionarioCargaTO.setDataNascimento(myDate);
            }
          }
          break;
        case 4:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            funcionarioCargaTO.setEmail(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            funcionarioCargaTO.setEmail("");
          }
          break;
        case 5:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            funcionarioCargaTO.setDddCelular(cell.getStringCellValue().trim());
          } catch (IllegalStateException e) {
            funcionarioCargaTO.setDddCelular("");
          }
          break;
        case 6:
          try {
            cell.setCellType(Cell.CELL_TYPE_STRING);
            String cel = cell.getStringCellValue().trim();
            if (StringUtils.isNotBlank(cel) && cel.length() == 8) {
              cel = Strings.padStart(cell.getStringCellValue().trim(), 9, '9');
            }
            cel = cel.replace("-", "");
            funcionarioCargaTO.setTelefoneCelular(cel);
          } catch (IllegalStateException e) {
            funcionarioCargaTO.setTelefoneCelular("");
          }
          break;
      }
    }

    return funcionarioCargaTO;
  }
}
