package br.com.json.bean.transacional;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class TedRendimentoResponse {
  private Integer statusCode;
  private String value;
  private Boolean isSuccess;
  private Boolean isFailure;
  private ErroMessage erroMessage;

  @Getter
  @Setter
  public static class ErroMessage {
    private Integer statusCode;
    private String message;
    private Error[] errors;

    @Getter
    @Setter
    public static class Error {
      private String domain;
      private String reason;
      private String message;
    }
  }
}
