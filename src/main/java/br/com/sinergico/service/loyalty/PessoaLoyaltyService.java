package br.com.sinergico.service.loyalty;

import static br.com.sinergico.facade.cadastral.ContaPagamentoFacade.REPRESENTANTE_LEGAL_ATIVO;
import static br.com.sinergico.util.Util.getNullPropertyNames;

import br.com.entity.cadastral.CorporativoResponsavel;
import br.com.entity.cadastral.CorporativoResponsavelCredencial;
import br.com.entity.cadastral.Credencial;
import br.com.entity.cadastral.EnderecoPessoa;
import br.com.entity.cadastral.Pessoa;
import br.com.entity.cadastral.RepresentanteLegal;
import br.com.entity.loyalty.ParceiroProgramaFidelidade;
import br.com.entity.loyalty.ProgramaFidelidade;
import br.com.entity.suporte.EstadoCivil;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.loyalty.DadosPreCadastroResponse;
import br.com.json.bean.loyalty.DadosPrincipaisDTO;
import br.com.json.bean.loyalty.DadosPrincipaisPessoaResponse;
import br.com.json.bean.loyalty.EditarEnderecoPessoaLoyalty;
import br.com.json.bean.loyalty.EditarPessoaLoyaltyRequest;
import br.com.json.bean.loyalty.EnderecoPessoaLoyaltyRequest;
import br.com.sinergico.enums.TipoPortadorLoginEnum;
import br.com.sinergico.repository.loyalty.CampanhaRepository;
import br.com.sinergico.repository.loyalty.ParceiroProgramaFidelidadeRepository;
import br.com.sinergico.repository.loyalty.ParticipanteCampanhaRepository;
import br.com.sinergico.repository.loyalty.ProgramaFidelidadeRepository;
import br.com.sinergico.security.SecurityUserCorporativo;
import br.com.sinergico.security.SecurityUserPortador;
import br.com.sinergico.service.cadastral.CredencialService;
import br.com.sinergico.service.cadastral.EnderecoPessoaService;
import br.com.sinergico.service.cadastral.PessoaService;
import br.com.sinergico.service.cadastral.PortadorLoginService;
import br.com.sinergico.service.cadastral.RepresentanteLegalService;
import br.com.sinergico.service.corporativo.CorporativoService;
import br.com.sinergico.service.suporte.MigracaoMulticontasVallooService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.util.Util;
import br.com.sinergico.vo.MigracaoMulticontasVallooVO;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class PessoaLoyaltyService {

  private static final boolean NAO_CRIAR_CARTAO_VIRTUAL = false;
  private static final int TITULAR = 1;
  private static final Integer BLOQUEIO_CRIACAO = 0;
  public static final Integer USUARIO_FIDELIDADE = 999999;
  private static final Integer PESSOA_FISICA = 1;
  private static final Integer PESSOA_JURIDICA = 2;
  private static final Integer STATUS_ATIVO = 1;
  //	private static final Integer PROD_FIDELIDADE = 2;
  private static final Integer PROD_FIDELIDADE = 30001;
  private static final String URL_ACESSO_LOYALTY_PORTADOR = "url.loyalty.portador";
  private static final Integer TIPO_END_RESIDENCIAL = 1;
  private static final Integer USUARIO_PORTADOR = 999999;
  private static final Integer ID_REGIONAL_AND_FILIAL = 1;
  private static final Integer ID_PONTO_RELACIONAMENTO = 1;

  @Autowired private ProgramaFidelidadeRepository programaRepository;

  @Autowired private ParceiroProgramaFidelidadeRepository parceiroRepository;

  @Autowired private ParticipanteCampanhaRepository participanteCampanhaRepository;

  @Autowired private PessoaService pessoaService;

  @Autowired private RepresentanteLegalService representanteLegalService;

  @Autowired private PortadorLoginService portadorLoginService;

  @Autowired private EnderecoPessoaService enderecoService;

  @Autowired private CampanhaRepository campanhaRepository;

  @Autowired private MigracaoMulticontasVallooService migracaoMulticontasVallooService;

  @Autowired private CorporativoService corporativoService;

  @Autowired private CredencialService credencialService;

  private Logger logger = LoggerFactory.getLogger(PessoaLoyaltyService.class);

  /**
   * Busca Pessoa e verifica cadastroMinimo
   *
   * @param idConta
   * @return
   */
  public boolean possuiCadastroMinimoByIdConta(Long idConta) {
    Pessoa pessoa = pessoaService.findPessoaByIdConta(idConta);

    if (pessoa == null) {
      throw new GenericServiceException("Pessoa não existente nessa conta '" + idConta + "' .");
    }

    return possueCadastroMinimo(pessoa);
  }

  /**
   * Busca Pessoa e verifica cadastroMinimo
   *
   * @param idPessoa
   * @return
   */
  public boolean possueCadastroMinimo(Long idPessoa) {

    Pessoa pessoa = pessoaService.findById(idPessoa);
    if (pessoa == null) {
      throw new GenericServiceException("Pessoa de acesso não existente.");
    }

    portadorLoginService.buscarLoginEGarantirExistencia(
        pessoa.getIdProcessadora(),
        pessoa.getIdInstituicao(),
        pessoa.getDocumento(),
        null,
        null,
        TipoPortadorLoginEnum.LEGADO_SIMPLES,
        "Login de acesso não existente.");

    return possueCadastroMinimo(pessoa);
  }

  /**
   * Requer que a pessoa tenha (para ser considerado cadastro minimo): CPF Nome completo
   * Nacionalidade Data de Nascimento Sexo Estado civil 1 dos emails 1 dos telefone 1 Endereço
   *
   * @return
   */
  public boolean possueCadastroMinimo(Pessoa pessoa) {
    return Util.isNotNull(pessoa.getDocumento())
        && Util.isNotNull(pessoa.getNacionalidade())
        && Util.isNotNull(pessoa.getDataNascimento())
        && Util.isNotNull(pessoa.getSexo())
        && Util.isNotNull(pessoa.getSexo().getId())
        && Util.isNotNull(pessoa.getEstadoCivil())
        && Util.isNotNull(pessoa.getEstadoCivil().getId())
        && (Util.isNotNull(pessoa.getEmail()) || Util.isNotNull(pessoa.getEmailProfissional()))
        && (Util.isNotNull(pessoa.getTelefoneCelular())
            || Util.isNotNull(pessoa.getTelefoneComercial()))
        && Util.isNotNull(pessoa.getEnderecosPessoa());
  }

  public ParceiroProgramaFidelidade getParceiroProgramaFidelidadeNotNull(Integer id) {
    ParceiroProgramaFidelidade parceiro = parceiroRepository.findById(id).orElse(null);

    if (parceiro == null) {
      throw new GenericServiceException(
          "Não foi possível realizar cadastro."
              + "Parceiro do Programa de Fidelidade não encontrado. "
              + id);
    }
    return parceiro;
  }

  private ProgramaFidelidade getProgramaFidelidadeNotNull(Integer idProgFidelidade) {
    ProgramaFidelidade programa = programaRepository.findById(idProgFidelidade).orElse(null);

    if (programa == null) {
      throw new GenericServiceException(
          "Não foi possível realizar cadastro.",
          "Programa de fidelidade não encontrado. " + idProgFidelidade);
    }
    return programa;
  }

  public DadosPrincipaisPessoaResponse buscarDadosPrincipaisPessoa(
      DadosPrincipaisDTO dadosPrincipaisDTO, SecurityUserPortador userPortador) {

    if (userPortador == null) {
      throw new GenericServiceException(
          "Não foi possível carregar dados.", "Portador não encontrado.");
    }

    DadosPrincipaisPessoaResponse dadosPrincipais = new DadosPrincipaisPessoaResponse();

    Integer idInstituicaoParaConsultarPessoa =
        Constantes.ID_PRODUCAO_INSTITUICAO_BRBPAY.equals(userPortador.getIdInstituicao())
            ? 2401
            : userPortador.getIdInstituicao();
    userPortador.setIdInstituicao(idInstituicaoParaConsultarPessoa);

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            userPortador.getIdProcessadora(),
            userPortador.getIdInstituicao(),
            userPortador.getCpf(),
            userPortador.getIdTipoPessoa());

    if (pessoa == null) {
      throw new GenericServiceException(
          "Não foi possível carregar dados.", "Pessoa não encontrada.");
    }

    BeanUtils.copyProperties(pessoa, dadosPrincipais);

    if (Constantes.PESSOA_JURIDICA.equals(userPortador.getIdTipoPessoa())) {
      dadosPrincipais.setNomeCompleto(pessoa.getNomeFantasia());
      dadosPrincipais.setDocumentoRepresentante(userPortador.getDocumentoAcesso());
      for (Long idConta : userPortador.getContasPortador()) {
        RepresentanteLegal representanteLegal =
            representanteLegalService.findOneByIdContaAndCpfAndStatus(
                idConta, userPortador.getDocumentoAcesso(), REPRESENTANTE_LEGAL_ATIVO);
        if (representanteLegal != null) {
          dadosPrincipais.setEmailRepresentante(representanteLegal.getEmail());
          dadosPrincipais.setDddTelefoneResidencialRepresentante(representanteLegal.getDddFixo());
          dadosPrincipais.setTelefoneResidencialRepresentante(representanteLegal.getTelefoneFixo());
          dadosPrincipais.setDddTelefoneCelularRepresentante(representanteLegal.getDddCelular());
          dadosPrincipais.setTelefoneCelularRepresentante(representanteLegal.getTelefoneCelular());
          break;
        }
      }
    }

    // ver como fazer com o parceiro
    //		dadosPrincipais.setIdParceiroPrograma(idParceiroPrograma);
    dadosPrincipais.setAtividadePrincipal(pessoa.getAtividadePrincipal());
    dadosPrincipais.setFormaDeConstituicao(pessoa.getFormaDeConstituicao());
    dadosPrincipais.setDataFundacao(DateUtil.localDateTimeToDate(pessoa.getDataFundacao()));
    dadosPrincipais.setRgDataEmissao(DateUtil.localDateTimeToDate(pessoa.getRgDataEmissao()));
    dadosPrincipais.setDataNascimento(DateUtil.localDateTimeToDate(pessoa.getDataNascimento()));
    dadosPrincipais.setEstadoCivil(pessoa.getIdEstadoCivil());
    dadosPrincipais.setNaturalidade(pessoa.getNaturalidade());
    dadosPrincipais.setPais(pessoa.getNacionalidade());
    dadosPrincipais.setOrgaoExpedidor(pessoa.getRgOrgaoEmissor());
    EstadoCivil estadoCivil = pessoa.getEstadoCivil();
    String descEstado = "";

    if (estadoCivil != null) {
      descEstado =
          estadoCivil.getDescricaoEstadoCivil() == null
              ? ""
              : estadoCivil.getDescricaoEstadoCivil();
    }

    dadosPrincipais.setDescEstadoCivil(descEstado);

    List<EnderecoPessoa> enderecos = pessoa.getEnderecosPessoa();

    if (enderecos != null && !enderecos.isEmpty()) {

      EnderecoPessoa end = enderecos.get(0);
      EnderecoPessoaLoyaltyRequest enderecoRes = new EnderecoPessoaLoyaltyRequest();
      BeanUtils.copyProperties(end, enderecoRes);

      dadosPrincipais.setEnderecoResidencial(enderecoRes);
    }

    List<MigracaoMulticontasVallooVO> buscarContasMigradasPorDocumento =
        migracaoMulticontasVallooService.buscarContasMigradasPorDocumento(pessoa.getDocumento());
    if (!buscarContasMigradasPorDocumento.isEmpty()
        && Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(pessoa.getIdInstituicao())) {
      dadosPrincipais.setMigrado(true);
    }

    return dadosPrincipais;
  }

  @Transactional
  public void editarPessoaCompleta(
      Integer idProgFidelidade,
      EditarPessoaLoyaltyRequest model,
      SecurityUserPortador userPortador) {
    ProgramaFidelidade programa = getProgramaFidelidadeNotNull(idProgFidelidade);

    Pessoa pessoa = getPessoaLogada(userPortador);

    // nao é mais permitido alterar o email
    if (model.getEmail() != null
        && pessoa.getEmail() != null
        && !Objects.equals(
            pessoa.getIdInstituicao(), Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_DIGITAL)) {
      if (!pessoa.getEmail().toUpperCase().equals(model.getEmail().toUpperCase()))
        throw new GenericServiceException("Não é permitido alterar o E-mail.");
    }

    BeanUtils.copyProperties(model, pessoa, getNullPropertyNames(model));

    if (Util.isNotNull(model.getFormaDeConstituicao())) {
      pessoa.setFormaDeConstituicao(model.getFormaDeConstituicao());
    }
    if (Util.isNotNull(model.getAtividadePrincipal())) {
      pessoa.setAtividadePrincipal(model.getAtividadePrincipal());
    }
    if (model.getDataNascimento() != null) {
      pessoa.setDataNascimento(DateUtil.dateToLocalDateTime(model.getDataNascimento()));
    }
    if (model.getRgDataEmissao() != null) {
      pessoa.setRgDataEmissao(DateUtil.dateToLocalDateTime(model.getRgDataEmissao()));
    }
    if (model.getEstadoCivil() != null) {
      pessoa.setIdEstadoCivil(model.getEstadoCivil());
    }
    if (Util.isNotNull(model.getPais())) {
      pessoa.setNacionalidade(model.getPais());
    }
    if (Util.isNotNull(model.getOrgaoExpedidor())) {
      pessoa.setRgOrgaoEmissor(model.getOrgaoExpedidor());
    }

    EditarEnderecoPessoaLoyalty enderecoResidencial = model.getEnderecoResidencial();

    if (enderecoResidencial != null) {

      BeanUtils.copyProperties(model, pessoa, getNullPropertyNames(model));

      List<EnderecoPessoa> enderecosPessoa = pessoa.getEnderecosPessoa();
      EnderecoPessoa enderecoPessoa = new EnderecoPessoa();
      enderecoPessoa.setIdPessoa(pessoa.getIdPessoa());

      if (enderecosPessoa != null && !enderecosPessoa.isEmpty()) {
        enderecoPessoa = enderecosPessoa.get(0);

      } else {
        enderecoPessoa.setIdTipoEndereco(TIPO_END_RESIDENCIAL);
        enderecoPessoa.setStatus(STATUS_ATIVO);
        enderecoPessoa.setIdUsuarioInclusao(USUARIO_FIDELIDADE);
      }

      if (model.getOrigemConfirmacao() != null && !model.getOrigemConfirmacao().isEmpty()) {
        enderecoPessoa.setDtHrConfirmacao(new Date());
        enderecoPessoa.setOrigemConfirmacao(model.getOrigemConfirmacao());
      }

      BeanUtils.copyProperties(
          enderecoResidencial, enderecoPessoa, getNullPropertyNames(enderecoResidencial));
      enderecoPessoa.setDtHrInclusao(new Date());
      enderecoService.save(enderecoPessoa);
    }

    pessoaService.save(pessoa);

    // Email loyalty nao pode mais ser alterado
    // pessoaService.updateEmailPessoaByModel(model,pessoa.getIdInstituicao(),pessoa.getIdProcessadora());
  }

  private Pessoa getPessoaLogada(SecurityUserPortador userPortador) {
    Pessoa pessoa = new Pessoa();
    if (userPortador.getCpf().length() == 14) {
      pessoa =
          pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
              userPortador.getIdProcessadora(),
              userPortador.getIdInstituicao(),
              userPortador.getCpf(),
              PESSOA_JURIDICA);
    } else {
      pessoa =
          pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
              userPortador.getIdProcessadora(),
              userPortador.getIdInstituicao(),
              userPortador.getCpf(),
              PESSOA_FISICA);
    }

    if (pessoa == null) {
      throw new GenericServiceException("Não foi possível editar dados.", "Pessoa não encontrada.");
    }
    return pessoa;
  }

  public DadosPreCadastroResponse buscarNomeEmailPorCPF(Integer idInstituicao, String cpf) {
    return pessoaService.buscarNomeEmailPorCPF(idInstituicao, cpf);
  }

  public Boolean buscarPessoaNaInstituicao(
      Integer idProcessadora, Integer idInstituicao, String documento) {
    return pessoaService.findByIdInstituicaoAndDocumento(idProcessadora, idInstituicao, documento);
  }

  public Boolean buscarPessoaInMaisPorDocumento(String documento) {
    return pessoaService.findPessoaInMaisPorDocumento(documento);
  }

  public DadosPrincipaisPessoaResponse buscarDadosPrincipaisPessoaCampanha(
      Integer idProgFidelidade, SecurityUserPortador userPortador, Integer idCampanha) {

    if (userPortador == null) {
      throw new GenericServiceException(
          "Não foi possível carregar dados.", "Portador não encontrado.");
    }

    DadosPrincipaisPessoaResponse dadosPrincipais = new DadosPrincipaisPessoaResponse();

    Pessoa pessoa =
        pessoaService.findOneByIdProcessadoraAndIdInstituicaoAndDocumentoAndTipoPessoa(
            userPortador.getIdProcessadora(),
            userPortador.getIdInstituicao(),
            userPortador.getCpf(),
            PESSOA_FISICA);

    if (pessoa == null) {
      throw new GenericServiceException(
          "Não foi possível carregar dados.", "Pessoa não encontrada.");
    }

    BeanUtils.copyProperties(pessoa, dadosPrincipais);

    // ver como fazer com o parceiro
    //		dadosPrincipais.setIdParceiroPrograma(idParceiroPrograma);
    dadosPrincipais.setDataNascimento(DateUtil.localDateTimeToDate(pessoa.getDataNascimento()));
    dadosPrincipais.setEstadoCivil(pessoa.getIdEstadoCivil());
    dadosPrincipais.setNaturalidade(pessoa.getNaturalidade());
    dadosPrincipais.setPais(pessoa.getNacionalidade());
    dadosPrincipais.setOrgaoExpedidor(pessoa.getRgOrgaoEmissor());
    EstadoCivil estadoCivil = pessoa.getEstadoCivil();
    String descEstado = "";

    if (estadoCivil != null) {
      descEstado =
          estadoCivil.getDescricaoEstadoCivil() == null
              ? ""
              : estadoCivil.getDescricaoEstadoCivil();
    }

    dadosPrincipais.setDescEstadoCivil(descEstado);

    List<EnderecoPessoa> enderecos = pessoa.getEnderecosPessoa();

    if (enderecos != null && !enderecos.isEmpty()) {

      EnderecoPessoa end = enderecos.get(0);
      EnderecoPessoaLoyaltyRequest enderecoRes = new EnderecoPessoaLoyaltyRequest();
      BeanUtils.copyProperties(end, enderecoRes);

      dadosPrincipais.setEnderecoResidencial(enderecoRes);
    }

    dadosPrincipais.setParticipanteCampanha(
        participanteCampanhaRepository.findByDocumentoAndIdCampanha(
            userPortador.getCpf(), idCampanha.longValue()));
    dadosPrincipais.setCampanha(
        campanhaRepository.findByIdCampanhaAndIdInstituicao(
            idCampanha.longValue(), userPortador.getIdInstituicao().longValue()));
    return dadosPrincipais;
  }

  public DadosPrincipaisPessoaResponse buscarCorporativoResponsavel(
      DadosPrincipaisDTO dadosPrincipaisDTO, SecurityUserCorporativo securityUserCorporativo) {

    DadosPrincipaisPessoaResponse dadosPrincipaisPessoaResponse =
        new DadosPrincipaisPessoaResponse();
    CorporativoResponsavel corporativoResponsavel =
        corporativoService.findResponsavelAtivoByDocumento(securityUserCorporativo.getUsername());
    CorporativoResponsavelCredencial responsavelCredencial =
        this.corporativoService.findResponsavelCredencial(corporativoResponsavel.getId());
    if (responsavelCredencial != null) {
      Credencial credencial =
          this.credencialService.findByIdCredencial(responsavelCredencial.getIdCredencial());
      Pessoa pessoa = this.pessoaService.findOneByIdPessoa(credencial.getIdPessoa());
      dadosPrincipaisPessoaResponse.setRazaoSocial(pessoa.getRazaoSocial());
      dadosPrincipaisPessoaResponse.setNomeFantasia(pessoa.getNomeFantasia());
      dadosPrincipaisPessoaResponse.setDocumentoRepresentante(pessoa.getDocumento());
    }
    dadosPrincipaisPessoaResponse.setNomeCompleto(corporativoResponsavel.getNome());
    dadosPrincipaisPessoaResponse.setDddTelefoneCelular(corporativoResponsavel.getDdd());
    dadosPrincipaisPessoaResponse.setTelefoneCelular(corporativoResponsavel.getCelular());
    dadosPrincipaisPessoaResponse.setEmail(corporativoResponsavel.getEmail());
    dadosPrincipaisPessoaResponse.setDocumento(corporativoResponsavel.getDocumento());
    return dadosPrincipaisPessoaResponse;
  }
}
