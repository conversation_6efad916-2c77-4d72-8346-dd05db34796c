package br.com.sinergico.controller;

import static br.com.sinergico.service.suporte.AcessoUsuarioService.SESSAO_USUARIO_INEXISTENTE;
import static br.com.sinergico.service.suporte.AcessoUsuarioService.USUARIO_NAO_CADASTRO_SENHA_INVALIDA;
import static br.com.sinergico.util.Constantes.QUANTIDADE_HISTORICO_SENHA;

import br.com.entity.suporte.AcessoHistoricoSenha;
import br.com.entity.suporte.AcessoHistoricoSenhaUsuarioUnico;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AcessoUsuarioUnico;
import br.com.entity.suporte.AcessoUsuarioUnicoView;
import br.com.entity.suporte.TokenFuncionalidade;
import br.com.enumVO.TipoTokenFuncionalidadeEnum;
import br.com.exceptions.GenericServiceException;
import br.com.exceptions.InvalidRequestException;
import br.com.json.bean.cadastral.LoginUnicoTokenVallo;
import br.com.json.bean.suporte.InfoLoginUnicoCache;
import br.com.json.bean.suporte.LoginSuperUsuario;
import br.com.json.bean.suporte.TrocarSenha;
import br.com.sinergico.repository.suporte.impl.InfoLoginUnicoCacheRepository;
import br.com.sinergico.security.PasswordValidatorService;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.EmailService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.AcessoUsuarioUnicoService;
import br.com.sinergico.service.suporte.TokenAcessoService;
import br.com.sinergico.service.suporte.TokenFuncionalidadeService;
import br.com.sinergico.service.suporte.TravaServicosService;
import br.com.sinergico.service.suporte.enums.Servicos;
import br.com.sinergico.swagger.SecuredSwagger;
import br.com.sinergico.util.Constantes;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = "/api/login-unico")
public class LoginUnicoController extends UtilController {

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private TokenAcessoService tokenAcessoService;

  @Autowired private AcessoUsuarioUnicoService acessoUsuarioUnicoService;

  @Autowired private TokenFuncionalidadeService tokenFuncionalidadeService;

  @Autowired private PasswordValidatorService passwordValidatorService;

  @Autowired private TravaServicosService travaServicosService;

  @Autowired private HttpServletRequest request;

  @Autowired private EmailService emailService;

  @Autowired private InfoLoginUnicoCacheRepository infoLoginUnicoCacheRepository;

  @Autowired EntityManager em;

  @RequestMapping(
      value = "/auth",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(
      notes = "Guarde o token gerado para futuras requisições",
      value = "Login do sistema Issuer",
      response = Token.class)
  @SecuredSwagger({"ROLE_PUBLIC_LOGIN"})
  public ResponseEntity<?> doLoginUnico(
      HttpServletRequest request,
      HttpServletResponse response,
      @RequestBody LoginSuperUsuario login)
      throws IOException {

    if (login.getToken() == null || login.getToken().isEmpty()) {
      return tokenAcessoService.gerarTokenAcessoLoginUnico(
          login.getUsuario(), login.getSenha(), request);
    } else {
      return acessoUsuarioUnicoService.doLoginUsuarioUnico(
          request, response, login.getUsuario(), login.getSenha(), login.getToken());
    }
  }

  @RequestMapping(
      value = "/token",
      method = RequestMethod.POST,
      consumes = MediaType.APPLICATION_JSON_UTF8_VALUE,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Login para usuários com token único vindo do Login Unico")
  @SecuredSwagger({"ROLE_PUBLIC_LOGIN"})
  public ResponseEntity<?> doLoginUnicoByToken(
      @RequestBody @Valid LoginUnicoTokenVallo loginUnicoTokenVallo,
      HttpServletRequest request,
      BindingResult result,
      HttpServletResponse response) {
    if (result.hasErrors()) {
      throw new InvalidRequestException("Erro na Validação de login do portador.", result);
    }

    TokenFuncionalidade tokenFuncionalidade =
        tokenFuncionalidadeService.utilizarTokenByFuncionalidade(
            loginUnicoTokenVallo.getToken(), TipoTokenFuncionalidadeEnum.TOKEN_LOGIN_UNICO);
    if (tokenFuncionalidade == null) {
      throw new GenericServiceException("Token inválido.", HttpStatus.UNAUTHORIZED);
    }

    if (!tokenFuncionalidade.getDocumento().equalsIgnoreCase(loginUnicoTokenVallo.getCpf())) {
      throw new GenericServiceException(
          "Token informado não pertence ao CPF informado.", HttpStatus.UNAUTHORIZED);
    }
    HashMap<String, Object> map = new HashMap<>();
    AcessoUsuario acessoUsuario = null;
    try {
      acessoUsuario =
          acessoUsuarioService.findByLoginSimples(loginUnicoTokenVallo.getLogin().toUpperCase());
    } catch (Exception e) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      acessoUsuarioService.saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA
              + " | Informação interna - Login: "
              + loginUnicoTokenVallo.getLogin());
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    if (acessoUsuario == null) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      acessoUsuarioService.saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA
              + " | Informação interna - Login: "
              + loginUnicoTokenVallo.getLogin());
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    AcessoUsuarioUnico acessoUsuarioUnico;
    try {
      acessoUsuarioUnico =
          acessoUsuarioUnicoService.findByLogin(tokenFuncionalidade.getChaveExterna());
    } catch (Exception e) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      acessoUsuarioService.saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA
              + " | Informação interna - Login: "
              + tokenFuncionalidade.getChaveExterna());
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    if (acessoUsuarioUnico == null) {
      map.put("msg", USUARIO_NAO_CADASTRO_SENHA_INVALIDA);
      acessoUsuarioService.saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.UNAUTHORIZED,
          USUARIO_NAO_CADASTRO_SENHA_INVALIDA
              + " | Informação interna - Login: "
              + tokenFuncionalidade.getChaveExterna());
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    }
    String senhaUsuario;
    if (loginUnicoTokenVallo.getIdentificador() == null) {
      map.put("msg", SESSAO_USUARIO_INEXISTENTE);
      acessoUsuarioService.saveAcessoLog(
          acessoUsuario,
          request,
          HttpStatus.UNAUTHORIZED,
          SESSAO_USUARIO_INEXISTENTE
              + " | Informação interna - Login: "
              + tokenFuncionalidade.getChaveExterna());
      return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
    } else {
      InfoLoginUnicoCache infoLoginUnicoCache =
          infoLoginUnicoCacheRepository
              .findById(loginUnicoTokenVallo.getIdentificador())
              .orElse(null);
      if (infoLoginUnicoCache == null
          || !loginUnicoTokenVallo
              .getIdentificador()
              .equalsIgnoreCase(infoLoginUnicoCache.getIdUnico())) {
        map.put("msg", SESSAO_USUARIO_INEXISTENTE);
        acessoUsuarioService.saveAcessoLog(
            acessoUsuario,
            request,
            HttpStatus.UNAUTHORIZED,
            SESSAO_USUARIO_INEXISTENTE
                + " | Informação interna - Login: "
                + tokenFuncionalidade.getChaveExterna());
        return new ResponseEntity<>(map, HttpStatus.UNAUTHORIZED);
      } else {
        senhaUsuario = infoLoginUnicoCache.getSenha();
      }
    }
    if (acessoUsuario.getTipoB2B() == null) {
      return acessoUsuarioService.doLogin(
          request,
          response,
          loginUnicoTokenVallo.getLogin(),
          senhaUsuario,
          null,
          loginUnicoTokenVallo.getIdentificador());
    } else {
      return acessoUsuarioService.doLoginB2b(
          request,
          response,
          loginUnicoTokenVallo.getLogin(),
          senhaUsuario,
          acessoUsuario.getTipoB2B(),
          null,
          loginUnicoTokenVallo.getIdentificador());
    }
  }

  @ApiOperation(
      value = "Serviço responsável pela solicitação de Recuperação de Senha do Login Unico")
  @ApiImplicitParam(paramType = "header", name = AUTH_HEADER_NAME, value = "API Key")
  @RequestMapping(value = "/recuperar-senha/{id}", method = RequestMethod.GET)
  @Secured({"ROLE_ALTERAR_SENHA_LOGIN_UNICO"})
  public ResponseEntity<HashMap<String, String>> recuperarSenhaLoginUnico(@PathVariable Long id) {

    AcessoUsuarioUnico acessoUsuarioUnico = acessoUsuarioUnicoService.findById(id);
    SecurityUser user = getAuthenticatedUser(request, em);
    travaServicosService.travaServicos(null, Servicos.TROCAR_SENHA_ISSUER);

    if (acessoUsuarioUnico == null) {
      throw new GenericServiceException("O Usuário solicitado não existe na base");
    }

    String senhaRandom = acessoUsuarioService.generatePassword();
    String senhaEncoded =
        acessoUsuarioService.encodePassword(senhaRandom + acessoUsuarioUnico.getCpf());
    acessoUsuarioUnico.setIdUsuarioManutencao(user.getIdUsuario().longValue());
    acessoUsuarioUnico.setSenha(senhaEncoded);
    acessoUsuarioUnico.setSenhaExpirada(true);
    acessoUsuarioUnico.setStatus(Constantes.USUARIO_ATIVO);
    acessoUsuarioUnicoService.save(acessoUsuarioUnico);

    try {
      String url = acessoUsuarioUnicoService.getUrlTrocaSenhaLoginUnico();
      emailService.sendPasswordEmailSuperUsuario(acessoUsuarioUnico, senhaRandom, url);
    } catch (Exception e) {
      throw new GenericServiceException(
          "Não foi possível enviar Senha por E-mail para o Usuário", e);
    }

    HashMap<String, String> map = new HashMap<>();
    map.put(
        "msg",
        "Solicitação de Alteração de Senha do Usuário "
            + acessoUsuarioUnico.getNome()
            + " realizada com sucesso");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }

  @RequestMapping(
      value = "/trocar-senha",
      method = RequestMethod.POST,
      produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
  @ApiOperation(value = "Serviço responsável por trocar a senha do login unico")
  public ResponseEntity<HashMap<String, String>> trocarSenhaLoginUnico(
      @RequestBody TrocarSenha trocarSenha) {

    AcessoUsuarioUnico acessoUsuarioUnico =
        acessoUsuarioUnicoService.findByLoginAndSenha(
            trocarSenha.getLogin(), trocarSenha.getSenha());

    HashMap<String, String> map = new HashMap<>();
    if (acessoUsuarioUnico == null) {
      throw new BadCredentialsException("Login ou senha incorretos");
    }

    if (!passwordValidatorService.validate(
        trocarSenha.getNovaSenha(),
        Arrays.asList(
            acessoUsuarioUnico.getCpf(),
            acessoUsuarioUnico.getNome(),
            acessoUsuarioUnico.getLogin()))) {
      throw new GenericServiceException(
          "Para a sua segurança sua nova senha deverá conter no mínimo 8 caracteres,"
              + " contendo pelo menos 1 letra maiúscula, 1 letra minúscula, 1 caracter especial e números."
              + " Sua senha deve ser distinta de suas informações pessoais, como documentos e nome. Busque criar uma senha que não seja facilmente deduzível ou intuitiva.");
    }
    travaServicosService.travaServicos(null, Servicos.TROCAR_SENHA_ISSUER);

    List<AcessoHistoricoSenhaUsuarioUnico> senhasSuperUsuario =
        new ArrayList<>(acessoUsuarioUnico.getAcessoHistoricoSenhaUsuarioUnicos());

    AcessoHistoricoSenhaUsuarioUnico senhaSuperUsuario = new AcessoHistoricoSenhaUsuarioUnico();
    senhaSuperUsuario.setDataHoraTroca(LocalDateTime.now());
    for (AcessoHistoricoSenhaUsuarioUnico a : senhasSuperUsuario) {
      if (acessoUsuarioService.isPassMatch(trocarSenha.getNovaSenha(), a.getHsSenhaAnterior())) {
        throw new GenericServiceException(
            "Você não pode usar uma senha que já utilizou recentemente.");
      }
      if (senhasSuperUsuario.size() == QUANTIDADE_HISTORICO_SENHA
          && a.getDataHoraTroca().isBefore(senhaSuperUsuario.getDataHoraTroca())) {
        senhaSuperUsuario = a;
      }
    }
    String senhaEncodedAtual =
        acessoUsuarioService.encodePassword(
            trocarSenha.getNovaSenha() + acessoUsuarioUnico.getCpf());
    String senhaEncodedAnterior =
        acessoUsuarioService.encodePassword(trocarSenha.getSenha() + acessoUsuarioUnico.getCpf());

    senhaSuperUsuario.setAcessoUsuarioUnico(acessoUsuarioUnico);
    senhasSuperUsuario.remove(senhaSuperUsuario);
    senhaSuperUsuario.setDataHoraTroca(LocalDateTime.now());
    senhaSuperUsuario.setHsSenhaAnterior(senhaEncodedAnterior);
    senhasSuperUsuario.add(senhaSuperUsuario);

    acessoUsuarioUnico.setSenhaExpirada(false);
    acessoUsuarioUnico.setSenha(senhaEncodedAtual);
    acessoUsuarioUnico.setDtHrUltimaTrocaSenha(LocalDateTime.now());
    acessoUsuarioUnico.getAcessoHistoricoSenhaUsuarioUnicos().addAll(senhasSuperUsuario);
    acessoUsuarioUnicoService.save(acessoUsuarioUnico);

    List<AcessoUsuarioUnicoView> acessoUsuarioUnicoViews;
    acessoUsuarioUnicoViews =
        acessoUsuarioUnicoService.getAllAcessoUsuarioUnicoViewByLoginAcessoUsuarioUnico(
            acessoUsuarioUnico.getLogin());
    acessoUsuarioUnicoViews.forEach(
        acessoUsuarioUnicoView -> {
          AcessoUsuario acessoUsuario =
              acessoUsuarioService.findByIdUsuario(
                  acessoUsuarioUnicoView.getIdAcessoUsuario().intValue());
          List<AcessoHistoricoSenha> senhasAcessoUsuario = new ArrayList<>();
          senhasAcessoUsuario.addAll(acessoUsuario.getAcessoHistoricoSenhas());
          AcessoHistoricoSenha senhaAcessoUsuario = new AcessoHistoricoSenha();
          senhaAcessoUsuario.setDataHoraTroca(new Date());
          for (AcessoHistoricoSenha a : senhasAcessoUsuario) {
            if (a.getDataHoraTroca().before(senhaAcessoUsuario.getDataHoraTroca())) {
              senhaAcessoUsuario = a;
            }
          }
          senhaAcessoUsuario.setAcessoUsuario(acessoUsuario);
          senhasAcessoUsuario.remove(senhaAcessoUsuario);
          senhaAcessoUsuario.setDataHoraTroca(new Date());
          senhaAcessoUsuario.setSenhaAnterior(senhaEncodedAnterior);

          senhasAcessoUsuario.add(senhaAcessoUsuario);

          acessoUsuario.setSenhaExpirada(false);
          acessoUsuario.setSenha(senhaEncodedAtual);
          acessoUsuario.setDtHrUltimaTrocaSenha(new Date());
          acessoUsuario.setStatus(Constantes.USUARIO_ATIVO);
          acessoUsuario.setDtHrStatus(new Date());
          acessoUsuario.getAcessoHistoricoSenhas().addAll(senhasAcessoUsuario);
          acessoUsuarioService.save(acessoUsuario);
        });

    map.put("msg", "Senha alterada com sucesso");
    return new ResponseEntity<>(map, HttpStatus.OK);
  }
}
