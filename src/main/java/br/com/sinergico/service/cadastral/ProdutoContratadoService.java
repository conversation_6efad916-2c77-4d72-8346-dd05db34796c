package br.com.sinergico.service.cadastral;

import br.com.client.rest.jcard.json.bean.CardContractConfiguration;
import br.com.client.rest.jcard.json.bean.CardContractConfigurationResponse;
import br.com.entity.cadastral.ContaPagamento;
import br.com.entity.cadastral.DebFolhaConvEmpresa;
import br.com.entity.cadastral.ParceriaComercial;
import br.com.entity.cadastral.ParceriaComercialContrato;
import br.com.entity.cadastral.ParceriaComercialContratoId;
import br.com.entity.cadastral.ProdutoCondicoes;
import br.com.entity.cadastral.ProdutoContratado;
import br.com.entity.cadastral.ProdutoInstituicaoConfiguracao;
import br.com.entity.suporte.AcessoUsuario;
import br.com.entity.suporte.AplicativoServicoProdutoContratado;
import br.com.entity.suporte.AplicativoServicoProdutoContratadoId;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamento;
import br.com.entity.suporte.HierarquiaPontoDeRelacionamentoId;
import br.com.entity.suporte.TipoParceriaComercial;
import br.com.exceptions.GenericServiceException;
import br.com.json.bean.cadastral.BuscaProdutosEmpresasReplicaveis;
import br.com.json.bean.cadastral.DadosProdutosContratados;
import br.com.json.bean.suporte.CadastrarPontoRelacionamento;
import br.com.json.bean.suporte.ProdutoContradoCliente;
import br.com.json.bean.suporte.ReplicarEmpresasB2B;
import br.com.sinergico.enums.TipoProdutoEnum;
import br.com.sinergico.repository.cadastral.ProdutoContratadoRepository;
import br.com.sinergico.repository.suporte.AplicativoServiceProdutoContratadoRepository;
import br.com.sinergico.security.SecurityUser;
import br.com.sinergico.service.GenericService;
import br.com.sinergico.service.UtilService;
import br.com.sinergico.service.corporativo.CorporativoService;
import br.com.sinergico.service.jcard.CardContractConfigurationService;
import br.com.sinergico.service.suporte.AcessoUsuarioService;
import br.com.sinergico.service.suporte.HierarquiaPontoRelacionamentoService;
import br.com.sinergico.service.suporte.TipoParceriaComercialService;
import br.com.sinergico.service.totvs.api.PreRegistroProdutoTotvsService;
import br.com.sinergico.util.Constantes;
import br.com.sinergico.util.DateUtil;
import br.com.sinergico.vo.AplicativoServicoVO;
import br.com.sinergico.vo.ParceriaComercialContratoVO;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Service
public class ProdutoContratadoService extends GenericService<ProdutoContratado, Long> {

  private static final int PERMITE_NOME_IMP_INF = 1;

  private static final int ZERO = 0;

  private static final int ULTIMO_DIA_MES = 32;

  private static final int CONVENIO = 4;

  private static final int _31 = 31;

  private static final int _30 = 30;

  private static final int _29 = 29;

  public static final long ID_GRUPO_MULTICONTAS_HOM = 39L;

  public static final long ID_GRUPO_MULTICONTAS_PROD = 5L;
  public static final Integer MENOS_UM = -1;

  private ProdutoContratadoRepository repository;

  @Autowired private CardContractConfigurationService cardContractConfigurationService;

  @Lazy @Autowired
  private HierarquiaPontoRelacionamentoService hierarquiaPontoRelacionamentoService;

  @Autowired private ProdutoCondicoesService produtoCondicoesService;

  @Autowired private ProdutoInstituicaoConfiguracaoService prodConfigService;

  @Autowired private DebFolhaConvEmpresaService debFolhaConvEmpresaService;

  @Autowired private ProdutoInstituicaoCorrespondenteService prodInstCorrespService;

  @Autowired private AcessoUsuarioService acessoUsuarioService;

  @Autowired private DateUtil dateUtil;

  @Autowired private ProdutoInstituicaoConfiguracaoService produtoInstituicaoConfiguracaoService;

  @Autowired private UtilService utilService;

  @Autowired private ProdutoContratadoMccService produtoContratadoMccService;

  @Autowired private TipoParceriaComercialService tipoParceriaComercialService;
  @Autowired private ParceriaComercialService parceriaComercialService;
  @Autowired private ParceriaComercialContratoService parceriaComercialContratoService;
  @Autowired private PreRegistroProdutoTotvsService preRegistroProdutoTotvsService;
  @Autowired private CorporativoService corporativoService;

  @Autowired
  private AplicativoServiceProdutoContratadoRepository aplicativoServiceProdutoContratadoRepository;

  @Autowired
  public ProdutoContratadoService(ProdutoContratadoRepository repository) {
    super(repository);
    this.repository = repository;
  }

  public ProdutoContratado getProdutoIdentificadorJcardNotNull(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento,
      Integer idProdInstituicao) {
    return repository.getProdutoIdentificadorNotNull(
        idProcessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoRelacionamento,
        idProdInstituicao);
  }

  public ProdutoContratado findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento,
      Integer idProdInstituicao) {

    return repository
        .findOneByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamentoAndIdProdInstituicaoAndDtHrCancelamentoIsNull(
            idProcessadora,
            idInstituicao,
            idRegional,
            idFilial,
            idPontoRelacionamento,
            idProdInstituicao);
  }

  public ProdutoContratado findProdutoContratadoCorresp(ProdutoContratado produtoContratado) {
    ProdutoContratado produtoContratadoCorresp = null;
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
            produtoContratado.getIdInstituicao())
        && produtoContratado.getIdContratoCorrespondente() != null) {
      produtoContratadoCorresp =
          repository.findProdutoContratadoCorresp(produtoContratado.getIdContratoCorrespondente());
    } else if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS.equals(
        produtoContratado.getIdInstituicao())) {
      produtoContratadoCorresp =
          repository.findProdutoContratadoCorrespOrigem(produtoContratado.getIdContrato());
    }
    return produtoContratadoCorresp;
  }

  /**
   * Metodo responsavel por buscar os produtos contratados ativos de uma empresa cliente b2b
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return
   */
  public List<ProdutoContratado> getProdutosContratadosAtivos(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {

    List<ProdutoContratado> lista =
        repository
            .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamentoAndDtHrCancelamentoIsNull(
                idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);

    for (ProdutoContratado produtoContratado : lista) {
      try {
        ProdutoInstituicaoConfiguracao prodInstConf =
            produtoInstituicaoConfiguracaoService
                .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                    produtoContratado.getIdProcessadora(),
                    produtoContratado.getIdProdInstituicao(),
                    produtoContratado.getIdInstituicao());

        produtoContratado.setTipoProduto(prodInstConf.getTipoProduto());
        produtoContratado.setIdGrupoProduto(prodInstConf.getIdGrupoProduto());
      } catch (Exception e) {
      }
    }

    return lista;
  }

  /**
   * Metodo rseponsavel por buscar todos os produtos contratados ativos e inativos por uma empresa
   * b2b
   *
   * @param idProcessadora
   * @param idInstituicao
   * @param idRegional
   * @param idFilial
   * @param idPontoRelacionamento
   * @return lista de produtos contratados
   */
  public List<ProdutoContratado> getProdutosContratadosAtivosEInativos(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {

    return repository
        .findByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamento(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
  }

  /**
   * Metodo responavel por editar as taxas de um produto contratado
   *
   * @param prodContratado
   * @return ProdutoContratado
   */
  @Transactional
  public ProdutoContratado editarProduto(ProdutoContratado prodContratado) {

    ProdutoCondicoes produtoCondicoes =
        produtoCondicoesService.findOneByIdProdInsituicao(prodContratado.getIdProdInstituicao());

    if (produtoCondicoes == null) {
      throw new GenericServiceException(
          "Condições não configuradas para o produto: " + prodContratado.getIdProdInstituicao());
    }

    ProdutoContratado produtoContratadoBuscado =
        findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            prodContratado.getIdProcessadora(),
            prodContratado.getIdInstituicao(),
            prodContratado.getIdRegional(),
            prodContratado.getIdFilial(),
            prodContratado.getIdPontoRelacionamento(),
            prodContratado.getIdProdInstituicao());

    if (produtoContratadoBuscado == null) {
      throw new GenericServiceException(
          "Produto contratado não encontrado: " + prodContratado.getIdProdInstituicao());
    }

    produtoContratadoBuscado.setTxJuroChequeEspecial(prodContratado.getTxJuroChequeEspecial());
    produtoContratadoBuscado.setPercentualChequeEspecial(
        prodContratado.getPercentualChequeEspecial());

    //		save(produtoContratadoBuscado);

    return atualizarCardContrato(produtoContratadoBuscado);

    //		return produtoContratadoBuscado;
  }

  /**
   * Metodo responavel por contratar um produto(criar um contrato de um produto para uma empresa b2b
   *
   * @param model
   * @return ProdutoContratado
   */
  @Transactional
  public ProdutoContratado contratarProduto(ProdutoContradoCliente model) {

    ProdutoContratado produtoContratado = prepareContratatarProduto(model);
    ProdutoCondicoes produtoCondicoes =
        produtoCondicoesService.findOneByIdProdInsituicao(produtoContratado.getIdProdInstituicao());

    if (produtoCondicoes == null) {
      throw new GenericServiceException(
          "Condições não configuradas para o produto: " + produtoContratado.getIdProdInstituicao());
    }

    ProdutoContratado produtoContratadoBuscado =
        findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            produtoContratado.getIdProcessadora(),
            produtoContratado.getIdInstituicao(),
            produtoContratado.getIdRegional(),
            produtoContratado.getIdFilial(),
            produtoContratado.getIdPontoRelacionamento(),
            produtoContratado.getIdProdInstituicao());

    if (produtoContratadoBuscado != null) {
      throw new GenericServiceException(
          "Produto já contratado não pode ser contratado: "
              + produtoContratado.getIdProdInstituicao());
    }

    Boolean replicar = false;
    if (prodInstCorrespService.findIfExistsCorresp(produtoContratado.getIdProdInstituicao())) {
      replicar = Boolean.TRUE;
    }

    CardContractConfiguration cardContractConfiguration = new CardContractConfiguration();

    HierarquiaPontoDeRelacionamento hpr =
        hierarquiaPontoRelacionamentoService.findById(
            new HierarquiaPontoDeRelacionamentoId(
                produtoContratado.getIdProcessadora(),
                produtoContratado.getIdInstituicao(),
                produtoContratado.getIdRegional(),
                produtoContratado.getIdFilial(),
                produtoContratado.getIdPontoRelacionamento()));

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodConfigService.findByIdProdInstituicao(produtoContratado.getIdProdInstituicao());
    cardContractConfiguration.setIdCompany(hpr.getIdCompany());
    cardContractConfiguration.setIdCardProduct(produtoInstituicaoConfiguracao.getIdCardProduct());
    cardContractConfiguration.setOnlineAvailable(produtoContratado.getPermiteCompraOnline());

    atualizarCamposCasoForProdutoTipoSaldoLivre(
        hpr, produtoInstituicaoConfiguracao, produtoContratado);

    // BUSCAR IDENFICADOR_JCARD EM ALGUM PRODUTO ANTERIOR
    ProdutoContratado prcon =
        getProdutoIdentificadorJcardNotNull(
            produtoContratado.getIdProcessadora(),
            produtoContratado.getIdInstituicao(),
            produtoContratado.getIdRegional(),
            produtoContratado.getIdFilial(),
            produtoContratado.getIdPontoRelacionamento(),
            produtoContratado.getIdProdInstituicao());

    if (prcon != null) {
      produtoContratado.setIdentificadorJCard(prcon.getIdentificadorJCard());
    }

    produtoContratado =
        cadastrarCardContrato(produtoContratado, produtoCondicoes, cardContractConfiguration);

    verificarGravarParceirasComerciais(model, produtoContratado);
    produtoContratado = save(produtoContratado);

    if (replicar) {
      List<BuscaProdutosEmpresasReplicaveis> list =
          hierarquiaPontoRelacionamentoService.buscarEmpresasReplicaveis();
      List<Long> idContrato =
          list.stream()
              .map(BuscaProdutosEmpresasReplicaveis::getIdContrato)
              .collect(Collectors.toList());
      if (!idContrato.contains(produtoContratado.getIdContrato())) {
        idContrato.add(produtoContratado.getIdContrato());
      }
      ReplicarEmpresasB2B replicarEmpresas = new ReplicarEmpresasB2B();
      replicarEmpresas.setIdContratos(idContrato);
      AcessoUsuario usuario =
          acessoUsuarioService.findByIdUsuario(Constantes.ID_USUARIO_REPLICADOR);
      SecurityUser user = new SecurityUser(usuario);
      hierarquiaPontoRelacionamentoService.replicarEmpresasB2B(replicarEmpresas, user);
    }

    if (produtoInstituicaoConfiguracao.getBlCorporativo()
        && !model.getServicosAplicativo().isEmpty()) {
      List<AplicativoServicoVO> pixServicos =
          model.getServicosAplicativo().stream()
              .filter(e -> e.getDescServico().toLowerCase().startsWith("pix"))
              .collect(Collectors.toList());
      if (!pixServicos.isEmpty()) {
        List<AplicativoServicoVO> servicos =
            this.corporativoService.listarServicos(model.getIdInstituicao(), false);
        model.getServicosAplicativo().removeIf(e -> e.getNome().toLowerCase().startsWith("pix"));
        pixServicos =
            servicos.stream()
                .filter(e -> e.getNome().toLowerCase().startsWith("pix"))
                .collect(Collectors.toList());
        model.getServicosAplicativo().addAll(pixServicos);
      }
      for (AplicativoServicoVO servico : model.getServicosAplicativo()) {
        AplicativoServicoProdutoContratado aplicativoServicoProdutoContratado =
            getAplicativoServicoProdutoContratado(servico, produtoContratado);
        aplicativoServiceProdutoContratadoRepository.save(aplicativoServicoProdutoContratado);
      }
    }

    preRegistroProdutoTotvsService.contratarProdutoTotvs(produtoContratado);

    return produtoContratado;
  }

  private static AplicativoServicoProdutoContratado getAplicativoServicoProdutoContratado(
      AplicativoServicoVO servico, ProdutoContratado produtoContratado) {
    AplicativoServicoProdutoContratado aplicativoServicoProdutoContratado =
        new AplicativoServicoProdutoContratado();
    AplicativoServicoProdutoContratadoId aplicativoServicoProdutoContratadoId =
        new AplicativoServicoProdutoContratadoId();
    aplicativoServicoProdutoContratadoId.setIdAplicativoFrontend(servico.getIdFrontEnd());
    aplicativoServicoProdutoContratadoId.setIdProdutoContratado(produtoContratado.getIdContrato());
    aplicativoServicoProdutoContratadoId.setIdAplicativoServico(servico.getId());
    aplicativoServicoProdutoContratado.setAplicativoServicoProdutoContratadoId(
        aplicativoServicoProdutoContratadoId);
    return aplicativoServicoProdutoContratado;
  }

  private List<ParceriaComercialContrato> verificarGravarParceirasComerciais(
      ProdutoContradoCliente model, ProdutoContratado produtoContratado) {

    List<ParceriaComercialContrato> parceriaComercialContratoList = new ArrayList<>();
    if (model.getParcerias() != null && !model.getParcerias().isEmpty()) {

      // Repassa a produto contratado VO, para o modal
      for (ParceriaComercialContratoVO contratoVO : model.getParcerias()) {
        ParceriaComercialContrato parceriaComercialContrato = new ParceriaComercialContrato();

        ParceriaComercial parceriaComercial =
            parceriaComercialService.findById(contratoVO.getIdParceria());
        if (parceriaComercial == null) {
          throw new GenericServiceException(
              "Parceiro Comercial Selecionado não Encontrado! ID: " + contratoVO.getIdParceria());
        }
        parceriaComercialContrato.setParceria(parceriaComercial);
        TipoParceriaComercial tipoParceriaComercial =
            this.tipoParceriaComercialService.findById(contratoVO.getIdTipo());
        if (tipoParceriaComercial == null) {
          throw new GenericServiceException(
              "Tipo de Parceiro Comercial Selecionado não Encontrado! ID: "
                  + contratoVO.getIdTipo());
        }
        parceriaComercialContrato.setTipoParceria(tipoParceriaComercial);

        parceriaComercialContrato.setParceriaComercialContratoId(
            new ParceriaComercialContratoId(
                produtoContratado.getIdContrato(),
                parceriaComercial.getId(),
                tipoParceriaComercial.getId()));
        ParceriaComercialContrato novoContratoComercial =
            parceriaComercialContratoService.criarContratoComercial(parceriaComercialContrato);
        parceriaComercialContratoList.add(novoContratoComercial);
      }
    }
    return parceriaComercialContratoList;
  }

  @Transactional
  public ProdutoContratado contratarProdutoPontoRelacionamento(
      CadastrarPontoRelacionamento model,
      HierarquiaPontoDeRelacionamento pr,
      ProdutoContradoCliente produto) {
    ProdutoContratado produtoContratado = prepareProdutoContratado(model, pr, produto);
    ProdutoCondicoes produtoCondicoes =
        produtoCondicoesService.findOneByIdProdInsituicao(produtoContratado.getIdProdInstituicao());

    if (produtoCondicoes == null) {
      throw new GenericServiceException(
          "Condições não configuradas para o produto: " + produtoContratado.getIdProdInstituicao());
    }

    ProdutoContratado produtoContratadoBuscado =
        findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            produtoContratado.getIdProcessadora(),
            produtoContratado.getIdInstituicao(),
            produtoContratado.getIdRegional(),
            produtoContratado.getIdFilial(),
            produtoContratado.getIdPontoRelacionamento(),
            produtoContratado.getIdProdInstituicao());

    if (produtoContratadoBuscado != null) {
      throw new GenericServiceException(
          "Produto já contratado não pode ser contratado: "
              + produtoContratado.getIdProdInstituicao());
    }

    CardContractConfiguration cardContractConfiguration = new CardContractConfiguration();

    HierarquiaPontoDeRelacionamento hpr =
        hierarquiaPontoRelacionamentoService.findById(
            new HierarquiaPontoDeRelacionamentoId(
                produtoContratado.getIdProcessadora(),
                produtoContratado.getIdInstituicao(),
                produtoContratado.getIdRegional(),
                produtoContratado.getIdFilial(),
                produtoContratado.getIdPontoRelacionamento()));

    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodConfigService.findByIdProdInstituicao(produtoContratado.getIdProdInstituicao());
    cardContractConfiguration.setIdCompany(hpr.getIdCompany());
    cardContractConfiguration.setIdCardProduct(produtoInstituicaoConfiguracao.getIdCardProduct());
    cardContractConfiguration.setOnlineAvailable(produtoContratado.getPermiteCompraOnline());
    atualizarCamposCasoForProdutoTipoSaldoLivre(
        pr, produtoInstituicaoConfiguracao, produtoContratado);

    produtoContratado =
        cadastrarCardContrato(produtoContratado, produtoCondicoes, cardContractConfiguration);

    verificarGravarParceirasComerciais(produto, produtoContratado);
    produtoContratado = save(produtoContratado);

    return produtoContratado;
  }

  public void atualizarCamposCasoForProdutoTipoSaldoLivre(
      HierarquiaPontoDeRelacionamento pr,
      ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao,
      ProdutoContratado produtoContratado) {
    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(pr.getIdInstituicao())
        && TipoProdutoEnum.CONTA_LIVRE.equals(produtoInstituicaoConfiguracao.getTipoProduto())) {
      if (Objects.equals(produtoContratado.getPermiteCargaB2b(), Boolean.FALSE)) {
        produtoContratado.setPrazoPagamento(Constantes.ZERO_INTEGER);
        produtoContratado.setTaxaCarga(BigDecimal.ZERO);
        produtoContratado.setTarifaCarga(BigDecimal.ZERO);
        produtoContratado.setTarifaCargaEmergencial(BigDecimal.ZERO);
        produtoContratado.setValorFaturaMin(BigDecimal.ZERO);
        produtoContratado.setTarifaValorFatura(BigDecimal.ZERO);
      }
    }
  }

  public ProdutoContratado prepareProdutoContratado(
      CadastrarPontoRelacionamento model,
      HierarquiaPontoDeRelacionamento pr,
      ProdutoContradoCliente produto) {
    ProdutoContratado prodContratado = new ProdutoContratado();
    if (produto.getPrazoPagamento() == null) {
      prodContratado.setPrazoPagamento(Constantes.ZERO_INTEGER);
      prodContratado.setTaxaCarga(BigDecimal.ZERO);
      prodContratado.setTarifaCarga(BigDecimal.ZERO);
      prodContratado.setTarifaCargaEmergencial(BigDecimal.ZERO);
      prodContratado.setValorFaturaMin(BigDecimal.ZERO);
      prodContratado.setTarifaValorFatura(BigDecimal.ZERO);
    }
    BeanUtils.copyProperties(produto, prodContratado, getNullPropertyNames(produto));
    BeanUtils.copyProperties(pr, prodContratado, getNullPropertyNames(pr));

    prodContratado.setIdUsuarioInclusao(model.getIdUsuario());
    prodContratado.setIdProcessadora(model.getIdProcessadora());
    prodContratado.setIdPontoRelacionamento(pr.getIdPontoDeRelacionamento());
    prodContratado.setDtHrInclusao(new Date());
    prodContratado.setPermiteCargaB2b(produto.getPermiteCargaB2B());

    if (produto.getDiasCorteList() != null) {
      StringBuilder dias = new StringBuilder();
      for (Integer dia : produto.getDiasCorteList()) {
        dias.append(dia + ";");
      }

      prodContratado.setDiasCorte(dias.toString());
    }

    return prodContratado;
  }

  public ProdutoContratado prepareContratatarProduto(ProdutoContradoCliente model) {
    ProdutoContratado produtoContratado = new ProdutoContratado();
    if (model.getPrazoPagamento() == null) {
      produtoContratado.setPrazoPagamento(Constantes.ZERO_INTEGER);
      produtoContratado.setTaxaCarga(BigDecimal.ZERO);
      produtoContratado.setTarifaCarga(BigDecimal.ZERO);
      produtoContratado.setTarifaCargaEmergencial(BigDecimal.ZERO);
      produtoContratado.setValorFaturaMin(BigDecimal.ZERO);
      produtoContratado.setTarifaValorFatura(BigDecimal.ZERO);
    }
    BeanUtils.copyProperties(model, produtoContratado);
    if (produtoContratado.getTipoPostagemProduto() == null) {
      produtoContratado.setTipoPostagemProduto(Constantes.TIPO_POSTAGEM_NAO_APLICAVEL);
    }
    produtoContratado.setIdUsuarioInclusao(model.getIdUsuario());
    produtoContratado.setPermiteCargaB2b(model.getPermiteCargaB2B());

    if (model.getDiasCorteList() != null) {
      StringBuilder dias = new StringBuilder();
      for (Integer dia : model.getDiasCorteList()) {
        dias.append(dia + ";");
      }
      produtoContratado.setDiasCorte(dias.toString());
    }

    return produtoContratado;
  }

  @Transactional
  public ProdutoContratado cadastrarCardContrato(
      ProdutoContratado prodContratado,
      ProdutoCondicoes produtoCondicoes,
      CardContractConfiguration cardContractConfiguration) {

    if (produtoCondicoes.isProdutoInCondicoes(prodContratado)) {
      prodContratado.setDtHrInclusao(new Date());
      prodContratado = save(prodContratado);

      gerarDebFolhaConvenio(prodContratado);
    }

    cardContractConfiguration.setExternalId(prodContratado.getIdContrato());

    CardContractConfigurationResponse resJcard =
        cardContractConfigurationService.createCardContract(cardContractConfiguration);
    if (!resJcard.isSuccess()) {
      throw new GenericServiceException("Erro ao criar o contrato no jcard.", resJcard.getErrors());
    }

    if (MENOS_UM.equals(prodContratado.getPrimeiroCartaoVirtual())) {
      prodContratado.setPrimeiroCartaoVirtual(null);
    }

    prodContratado.setIdContract(resJcard.getId());

    prodContratado = save(prodContratado);

    return prodContratado;
  }

  private ProdutoContratado atualizarCardContrato(ProdutoContratado prodContratado) {
    return save(prodContratado);
  }

  /**
   * Metodo responavel por contratar um produto(criar um contrato de um produto para uma empresa b2b
   *
   * @param prodContratado
   * @param hierarquiaPontoDeRelacionamento
   * @return ProdutoContratado
   */
  public ProdutoContratado contratarProdutoReplicado(
      ProdutoContratado prodContratado,
      HierarquiaPontoDeRelacionamento hierarquiaPontoDeRelacionamento) {

    ProdutoContratado produtoContratadoBuscado =
        findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            prodContratado.getIdProcessadora(),
            prodContratado.getIdInstituicao(),
            prodContratado.getIdRegional(),
            prodContratado.getIdFilial(),
            prodContratado.getIdPontoRelacionamento(),
            prodContratado.getIdProdInstituicao());

    if (produtoContratadoBuscado != null) {
      throw new GenericServiceException(
          "Produto já contratado não pode ser contratado: "
              + prodContratado.getIdProdInstituicao());
    }
    prodContratado.setPermitirNomeImpresso(PERMITE_NOME_IMP_INF);
    zerarTaxasProduto(prodContratado);
    prodContratado.setDtHrInclusao(new Date());
    prodContratado = save(prodContratado);

    CardContractConfiguration cardContractConfiguration = new CardContractConfiguration();
    cardContractConfiguration.setExternalId(prodContratado.getIdContrato());
    cardContractConfiguration.setIdCompany(hierarquiaPontoDeRelacionamento.getIdCompany());
    ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao =
        prodConfigService.findByIdProdInstituicao(prodContratado.getIdProdInstituicao());
    cardContractConfiguration.setIdCardProduct(produtoInstituicaoConfiguracao.getIdCardProduct());
    cardContractConfiguration.setOnlineAvailable(prodContratado.getPermiteCompraOnline());
    CardContractConfigurationResponse contractConfigurationResponse =
        cardContractConfigurationService.createCardContract(cardContractConfiguration);
    if (!contractConfigurationResponse.isSuccess()) {
      throw new GenericServiceException(
          "Erro ao criar o contrato no jcard.", contractConfigurationResponse.getErrors());
    }

    // BUSCAR IDENFICADOR_JCARD EM ALGUM PRODUTO ANTERIOR
    ProdutoContratado prcon =
        getProdutoIdentificadorJcardNotNull(
            prodContratado.getIdProcessadora(),
            prodContratado.getIdInstituicao(),
            prodContratado.getIdRegional(),
            prodContratado.getIdFilial(),
            prodContratado.getIdPontoRelacionamento(),
            prodContratado.getIdProdInstituicao());

    if (prcon != null) {
      prodContratado.setIdentificadorJCard(prcon.getIdentificadorJCard());
    }

    prodContratado.setIdContract(contractConfigurationResponse.getId());

    preRegistroProdutoTotvsService.contratarProdutoTotvs(prodContratado);

    return prodContratado;
  }

  private void zerarTaxasProduto(ProdutoContratado prodContratado) {
    prodContratado.setTarifaCarga(new BigDecimal(0));
    prodContratado.setTarifaCargaEmergencial(new BigDecimal(0));
    prodContratado.setTarifaDocTed(new BigDecimal(0));
    prodContratado.setTarifaEmissao(new BigDecimal(0));
    prodContratado.setTarifaEmissaoDescartavel(new BigDecimal(0));
    prodContratado.setTarifaOp(new BigDecimal(0));
    prodContratado.setTarifaPostagem(new BigDecimal(0));
    prodContratado.setTarifaRenovacao(new BigDecimal(0));
    prodContratado.setTarifaReposicao(new BigDecimal(0));
    prodContratado.setTarifaValorFatura(new BigDecimal(0));
    prodContratado.setTaxaCarga(new BigDecimal(0));
    prodContratado.setPrazoPagamento(0);
  }

  private void gerarDebFolhaConvenio(ProdutoContratado prodContratado) {
    ProdutoInstituicaoConfiguracao prod =
        prodConfigService.findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
            prodContratado.getIdProcessadora(),
            prodContratado.getIdProdInstituicao(),
            prodContratado.getIdInstituicao());

    if (prod == null) {
      throw new GenericServiceException(
          "Não foi possível encontrar a configuração do produto contratado.");
    }

    if (prod.getIdProdutoPlataforma() == CONVENIO) {

      Boolean existeDebFolhaConvEmpresa =
          debFolhaConvEmpresaService
              .countByIdProcessadoraAndIdInstituicaoAndIdRegionalAndIdFilialAndIdPontoRelacionamentoAndIdProdInstituicaoAndDataFaturamentoIsNull(
                  prodContratado.getIdProcessadora(),
                  prodContratado.getIdInstituicao(),
                  prodContratado.getIdRegional(),
                  prodContratado.getIdFilial(),
                  prodContratado.getIdPontoRelacionamento(),
                  prodContratado.getIdProdInstituicao());

      if (!existeDebFolhaConvEmpresa) {
        DebFolhaConvEmpresa dfce = new DebFolhaConvEmpresa();
        BeanUtils.copyProperties(prodContratado, dfce);

        Integer diaAtual = LocalDateTime.now().getDayOfMonth();
        Integer aux = ZERO;

        for (Integer item : prodContratado.getDiasCorteList()) {
          if (item > diaAtual) {
            if (aux == ZERO) {
              aux = item;
            } else {
              if (item < aux) {
                aux = item;
              }
            }
          }
        }

        if (aux == ZERO || aux == ULTIMO_DIA_MES) {
          for (Integer item : prodContratado.getDiasCorteList()) {
            if (item < diaAtual) {
              if (aux == ZERO) {
                aux = item;
              } else {
                if (item < aux) {
                  aux = item;
                }
              }
            }
          }
        }

        if (aux == ZERO) {
          aux = diaAtual;
        }

        Calendar c = Calendar.getInstance();

        if (aux == ULTIMO_DIA_MES) {
          // Se o proximo agendamento é no mês seguinte
          if (aux <= diaAtual) {
            c.add(Calendar.MONTH, +1);
          }
          c.set(Calendar.DATE, c.getActualMaximum(Calendar.DAY_OF_MONTH));

        } else if (aux == _29 || aux == _30 || aux == _31) {
          Calendar cControle = Calendar.getInstance();
          cControle.setTime(c.getTime());

          // Se o proximo agendamento é no mês seguinte
          if (aux <= diaAtual) {
            // aumenta 1 mês nos calendarios
            cControle.add(Calendar.MONTH, +1);
            c.add(Calendar.MONTH, +1);
          }

          // seta dia do proximo corte no calendario de controle
          cControle.set(Calendar.DAY_OF_MONTH, aux);

          // verifica se o calendario de controle avançou um mês ou ano
          // se sim, quer dizer que o mês não possui aquele dia, então ele seta no calendario de
          // resposta o ultimo dia do mês.
          if (cControle.get(Calendar.MONTH) > c.get(Calendar.MONTH)
              || cControle.get(Calendar.YEAR) > c.get(Calendar.YEAR)) {
            c.set(Calendar.DATE, c.getActualMaximum(Calendar.DAY_OF_MONTH));
            // se o dia existir no mês, ele so seta o dia.
          } else {
            c.set(Calendar.DAY_OF_MONTH, aux);
          }
        } else {
          // Se o proximo agendamento é no mês seguinte
          if (aux <= diaAtual) {
            c.add(Calendar.MONTH, +1);
          }
          c.set(Calendar.DAY_OF_MONTH, aux);
        }

        Date agendamentoFinal = null;

        // verifica se a data é um dia util, se não, procura o dia util anterior
        Calendar diaUtilAgendamento = Calendar.getInstance();
        diaUtilAgendamento.setTime(dateUtil.getDiaUtilAnterior(c.getTime()));
        agendamentoFinal = diaUtilAgendamento.getTime();

        // se o agendamento futuro for igual a data atual, adicionar mais um dia e procurar proximo
        // dia util.
        // so permite setar um dia util do proximo mês se o dia util anterior ao agendamento futuro
        // for a data atual, pois o processo já foi rodado.
        if (agendamentoFinal.equals(new Date())) {
          diaUtilAgendamento.add(Calendar.DATE, +1);
          agendamentoFinal = dateUtil.getProximoDiaUtil(diaUtilAgendamento.getTime());
        }

        dfce.setDataAgendamento(agendamentoFinal);
        dfce.setDiaCorteProduto(aux);
        dfce.setIdContrato(prodContratado.getIdContrato());
        dfce.setReestabelecimentoLimite(prodContratado.getReestabelecimentoLimite());

        debFolhaConvEmpresaService.save(dfce);
      }
    }
  }

  /**
   * Médoto responsavel por cancelar um produto contratado
   *
   * @param prodContratado
   * @return
   */
  @Transactional
  public void cancelarProdutoContratado(ProdutoContratado prodContratado, SecurityUser user) {

    ProdutoContratado produtoContratadoBuscado =
        findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            prodContratado.getIdProcessadora(),
            prodContratado.getIdInstituicao(),
            prodContratado.getIdRegional(),
            prodContratado.getIdFilial(),
            prodContratado.getIdPontoRelacionamento(),
            prodContratado.getIdProdInstituicao());

    ProdutoContratado produtoContratadoCorresp = null;
    if (prodContratado
            .getIdInstituicao()
            .equals(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_PAGAMENTOS)
        || prodContratado
            .getIdInstituicao()
            .equals(Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS)) {
      produtoContratadoCorresp = findProdutoContratadoCorresp(produtoContratadoBuscado);
    } // se o produto nao foi encontrado e porque ele ja esta cancelado ou porque nunca foi
    // contratado
    if (produtoContratadoBuscado == null) {
      throw new GenericServiceException(
          "Produto não pode ser cancelado: " + prodContratado.getIdProdInstituicao());
    }

    ProdutoInstituicaoConfiguracao prodInstConf =
        produtoInstituicaoConfiguracaoService
            .findByIdProcessadoraAndIdProdInstituicaoAndIdInstituicao(
                prodContratado.getIdProcessadora(),
                prodContratado.getIdProdInstituicao(),
                prodContratado.getIdInstituicao());
    produtoContratadoBuscado.setTipoProduto(prodInstConf.getTipoProduto());
    produtoContratadoBuscado.setIdGrupoProduto(prodInstConf.getIdGrupoProduto());
    Long grupoMulticontas =
        utilService.isAmbienteHomologacao() ? ID_GRUPO_MULTICONTAS_HOM : ID_GRUPO_MULTICONTAS_PROD;

    if (Constantes.ID_PRODUCAO_INSTITUICAO_VALLOO_BENEFICIOS.equals(
            produtoContratadoBuscado.getIdInstituicao())
        && produtoContratadoBuscado.getIdGrupoProduto() != null
        && grupoMulticontas.equals(produtoContratadoBuscado.getIdGrupoProduto())
        && TipoProdutoEnum.CONTA_LIVRE.equals(produtoContratadoBuscado.getTipoProduto())) {

      List<ProdutoContratado> produtoContratados =
          getProdutosContratadosAtivos(
              prodContratado.getIdProcessadora(),
              prodContratado.getIdInstituicao(),
              prodContratado.getIdRegional(),
              prodContratado.getIdFilial(),
              prodContratado.getIdPontoRelacionamento());

      for (ProdutoContratado produto : produtoContratados) {
        if (produto.getIdGrupoProduto() != null
            && grupoMulticontas.equals(produto.getIdGrupoProduto())
            && !TipoProdutoEnum.CONTA_LIVRE.equals(produto.getTipoProduto())) {
          throw new GenericServiceException(
              "Para cancelar este contrato é necessário cancelar os contratos dos outros produtos do Multiconta primeiro.");
        }
      }
    }

    CardContractConfigurationResponse resJcard =
        cardContractConfigurationService.cancelCardContract(
            produtoContratadoBuscado.getIdContract());

    if (!resJcard.isSuccess()) {
      throw new GenericServiceException("Não foi possível atualizar a configuração!!");
    }

    if (produtoContratadoCorresp != null
        && produtoContratadoCorresp.getDtHrCancelamento() == null) {
      CardContractConfigurationResponse resJcardCorresp =
          cardContractConfigurationService.cancelCardContract(
              produtoContratadoCorresp.getIdContract());

      if (!resJcardCorresp.isSuccess()) {
        throw new GenericServiceException("Não foi possível atualizar a configuração!!");
      }

      produtoContratadoCorresp.setIdUsuarioCancelamento(user.getIdUsuario());
      produtoContratadoCorresp.setDtHrCancelamento(new Date());
      preRegistroProdutoTotvsService.cancelarContratoProdutoTotvs(produtoContratadoCorresp);
      save(produtoContratadoCorresp);
    }

    produtoContratadoBuscado.setIdUsuarioCancelamento(user.getIdUsuario());
    produtoContratadoBuscado.setDtHrCancelamento(new Date());
    preRegistroProdutoTotvsService.cancelarContratoProdutoTotvs(produtoContratadoBuscado);
    save(produtoContratadoBuscado);
    removerTodosProdutoContratadoMcc(produtoContratadoBuscado, produtoContratadoCorresp, user);
  }

  public void removerTodosProdutoContratadoMcc(
      ProdutoContratado produtoContratadoBuscado,
      ProdutoContratado produtoContratadoCorresp,
      SecurityUser user) {
    try {
      produtoContratadoMccService.removerTodosProdutoContratadoMcc(produtoContratadoBuscado, user);
      if (produtoContratadoCorresp != null
          && produtoContratadoCorresp.getDtHrCancelamento() == null) {
        produtoContratadoMccService.removerTodosProdutoContratadoMcc(
            produtoContratadoCorresp, user);
      }
    } catch (Exception e) {
      e.printStackTrace();
    }
  }

  /**
   * Médoto responsavel por cancelar um produto contratado por id
   *
   * @param idContrato, idUsuario
   * @return
   */
  @Transactional
  public Boolean cancelarProdutoContratadoPorId(Long idContrato, Integer idUsuario) {

    ProdutoContratado produtoContratadoBuscado = findById(idContrato);

    // se o produto nao foi encontrado e porque ele ja esta cancelado ou porque nunca foi contratado
    if (produtoContratadoBuscado == null) {
      throw new GenericServiceException("Produto não pode ser cancelado: Contrato " + idContrato);
    }

    CardContractConfigurationResponse resJcard =
        cardContractConfigurationService.cancelCardContract(
            produtoContratadoBuscado.getIdContract());

    if (!resJcard.isSuccess()) {
      throw new GenericServiceException("Não foi possível atualizar a configuração!!");
    }

    produtoContratadoBuscado.setIdUsuarioCancelamento(idUsuario);
    produtoContratadoBuscado.setDtHrCancelamento(new Date());

    return save(produtoContratadoBuscado) != null;
  }

  public List<ProdutoContratado> buscarProdutosConvenioByEmpresa(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {

    List<ProdutoContratado> prods =
        repository.recuperarProdContratadoConvByEmpresa(
            idProcessadora, idInstituicao, idRegional, idFilial, idPontoRelacionamento);
    if (prods == null) {
      throw new GenericServiceException("Esta empresa não possui produtos de plataforma convênio");
    }
    return prods;
  }

  public List<ProdutoContratado> buscarPorIdExterno(String idExterno) {
    List<ProdutoContratado> prods =
        repository.findByIdentificadorExternoAndDtHrCancelamentoIsNull(idExterno);
    if (prods.isEmpty() || prods.size() == 0) {
      throw new GenericServiceException(
          "Não foi localizado produto contratado com o identificador externo: " + idExterno);
    }
    return prods;
  }

  public boolean isPrimeiroCartaoVirtual(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {
    Integer primeiroCartaoVirtualContrato =
        getPrimeiroCartaoVirtualContrato(conta, produtoInstituicaoConfiguracao);

    if (primeiroCartaoVirtualContrato != null) {
      return !primeiroCartaoVirtualContrato.equals(0);
    } else {
      return isPrimeiroCartaoVirtualConfiguradoNoProduto(produtoInstituicaoConfiguracao);
    }
  }

  public boolean isPrimeiroCartaoVirtualEFisico(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {
    Integer primeiroCartaoVirtualContrato =
        getPrimeiroCartaoVirtualContrato(conta, produtoInstituicaoConfiguracao);

    if (primeiroCartaoVirtualContrato != null) {
      return primeiroCartaoVirtualContrato.equals(2);
    } else {
      return isPrimeiroCartaoVirtualEFisicoConfiguradoNoProduto(produtoInstituicaoConfiguracao);
    }
  }

  private Integer getPrimeiroCartaoVirtualContrato(
      ContaPagamento conta, ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {
    ProdutoContratado produtoContratado =
        findProdutoContratadoAtivoByHierarquiaAndProdutoInstituicao(
            conta.getIdProcessadora(),
            conta.getIdInstituicao(),
            conta.getIdRegional(),
            conta.getIdFilial(),
            conta.getIdPontoDeRelacionamento(),
            produtoInstituicaoConfiguracao.getIdProdInstituicao());

    return produtoContratado != null ? produtoContratado.getPrimeiroCartaoVirtual() : null;
  }

  private boolean isPrimeiroCartaoVirtualConfiguradoNoProduto(
      ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {
    return (produtoInstituicaoConfiguracao.getVirtual() == null
            || !produtoInstituicaoConfiguracao.getVirtual())
        && (produtoInstituicaoConfiguracao.getPrimeiroCartaoVirtual() != null
            && !produtoInstituicaoConfiguracao.getPrimeiroCartaoVirtual().equals(0));
  }

  private boolean isPrimeiroCartaoVirtualEFisicoConfiguradoNoProduto(
      ProdutoInstituicaoConfiguracao produtoInstituicaoConfiguracao) {
    return (produtoInstituicaoConfiguracao.getVirtual() == null
            || !produtoInstituicaoConfiguracao.getVirtual())
        && produtoInstituicaoConfiguracao.getPrimeiroCartaoVirtual() != null
        && produtoInstituicaoConfiguracao.getPrimeiroCartaoVirtual().equals(2);
  }

  /**
   * Metodo responsavel por buscar o proximo numero Identificador Externo de um produto
   *
   * @return identificador externo seguinte
   */
  public Integer getProximoIdExternoProdInstituicao() {
    return repository.getProximoIdExternoProdInstituicao();
  }

  public List<Long> buscarIdsPorProdInstituicao(Integer idProdInstituicao) {
    return repository.buscarIdsPorProdInstituicao(idProdInstituicao);
  }

  public ProdutoContratado findProdutoContratadoPorIdGrupoETipoProduto(
      Integer idPeocessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento,
      Integer idProdInstituicao,
      Long idGrupoProduto,
      TipoProdutoEnum tipoProduto) {
    return repository.findProdutoContratadoPorIdGrupoETipoProduto(
        idPeocessadora,
        idInstituicao,
        idRegional,
        idFilial,
        idPontoRelacionamento,
        idProdInstituicao,
        idGrupoProduto,
        tipoProduto);
  }

  public DadosProdutosContratados buscarDadosProdutoContratadoPeloTipoProduto(
      Integer idProcessadora,
      Integer idInstituicao,
      Integer idRegional,
      Integer idFilial,
      Integer idPontoRelacionamento) {

    DadosProdutosContratados dados = new DadosProdutosContratados();
    List<ProdutoCondicoes> produtosCondicoes =
        produtoCondicoesService.findByIdProcessadoraAndIdInstituicao(idProcessadora, idInstituicao);

    for (ProdutoCondicoes produto : produtosCondicoes) {
      ProdutoContratado produtoContratado =
          findProdutoContratadoPorIdGrupoETipoProduto(
              idProcessadora,
              idInstituicao,
              idRegional,
              idFilial,
              idPontoRelacionamento,
              produto.getIdProdInstituicao(),
              utilService.isAmbienteHomologacao()
                  ? ID_GRUPO_MULTICONTAS_HOM
                  : ID_GRUPO_MULTICONTAS_PROD,
              TipoProdutoEnum.CONTA_LIVRE);
      if (produtoContratado != null) {
        dados.setNomeCartaoImpresso(produtoContratado.getNomeCartaoImpresso());
        dados.setTipoPostagemProduto(produtoContratado.getTipoPostagemProduto());
        dados.setPermitirNomeImpresso(produtoContratado.getPermitirNomeImpresso());
        dados.setTipoCobrancaReposicao(produtoContratado.getTipoCobrancaReposicao());
        dados.setPrimeiroCartaoVirtual(produtoContratado.getPrimeiroCartaoVirtual());
        dados.setPermiteCartaoFisico(produtoContratado.getPermiteCartaoFisico());
        dados.setTipoProduto(TipoProdutoEnum.CONTA_LIVRE);
        dados.setIdGrupoProduto(
            utilService.isAmbienteHomologacao()
                ? ID_GRUPO_MULTICONTAS_HOM
                : ID_GRUPO_MULTICONTAS_PROD);
        dados.setTarifaEmissao(produtoContratado.getTarifaEmissao());
        dados.setTarifaReposicao(produtoContratado.getTarifaReposicao());
        dados.setTarifaPostagem(produtoContratado.getTarifaPostagem());
      }
    }

    return dados;
  }

  @Transactional
  public ProdutoContratado alterarFilialFaturamento(
      Long idContrato, Integer novaFilialFaturamento) {
    ProdutoContratado produtoContratado = findById(idContrato);

    if (produtoContratado == null) {
      throw new GenericServiceException("Produto contratado não encontrado: " + idContrato);
    }

    produtoContratado.setFilialFaturamento(novaFilialFaturamento);
    return save(produtoContratado);
  }
}
